package com.yunhesoft.performanceAnalysis.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.performanceAnalysis.entity.dto.PAParam;
import com.yunhesoft.performanceAnalysis.entity.po.PaWordInfo;
import com.yunhesoft.performanceAnalysis.entity.vo.PAMonthDataVo;
import com.yunhesoft.performanceAnalysis.service.PAService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.tmsf.form.entity.po.SFForm;
import com.yunhesoft.tmsf.form.service.IFormManageService;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class PAServiceImpl implements PAService {
	
	@Autowired
    private EntityService srv;
	
	@Autowired
	private IFormManageService fmsrv;
	
	@Autowired
	private ICostService costService;
	
//	@Autowired
//	private IDataSourceService tdsSrv;	//数据源
//	
//	@Autowired
//	private FormTdsHandlerFactory handlerFactory;	//工厂类
	
	/**
	 * @category 按年度查询表单各月数据
	 */
	@Override
	public List<PAMonthDataVo> loadYearData(PAParam param) {
		//参数获取
		String formId = param.getFormId();
		String year = param.getYear();
		String orgCode = param.getOrgCode();
		if(StringUtils.isEmpty(year)) {
			year = DateTimeUtils.getNowYear();
		}else {
			year = year.substring(0, 4);
		}
		
		Map<String, Map<String, String>> dataMap = new HashMap<String, Map<String,String>>();
		
		Where where = Where.create();
		where.eq(PaWordInfo::getTmused, 1);
		where.eq(PaWordInfo::getNf, year);
		where.eq(PaWordInfo::getFormId, formId);
		if(StringUtils.isNotEmpty(orgCode)) {
			where.eq(PaWordInfo::getOrgCode, orgCode);
		}
		List<PaWordInfo> list = srv.queryData(PaWordInfo.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			for (PaWordInfo obj : list) {
				Map<String, String> tmap = new HashMap<String, String>();
				tmap.put("id", obj.getId());
				tmap.put("dataId", obj.getDataId());
				tmap.put("orgCode", obj.getOrgCode());
				tmap.put("createTime", obj.getCreateTimeStr());
				tmap.put("updateTime", obj.getUpdateTimeStr());
				tmap.put("createName", obj.getCreateUserName());
				tmap.put("updateName", obj.getUpdateUserName());
				tmap.put("formName", obj.getFormName());
				dataMap.put(obj.getYf(), tmap);
			}
		}
		
		SFForm sfForm = fmsrv.queryFormInfoById(formId);
		if (sfForm == null) {
			return null;
		}
		//获取已有数据
//		String nk = param.getYearKey();
//		String mk = param.getMonthKey();
//		if(StringUtils.isEmpty(nk)) {
//			nk = "nf";
//		}
//		if(StringUtils.isEmpty(mk)) {
//			mk = "yf";
//		}
//		String tdsAlias = sfForm.getTableId();
//		TdataSource tds = StringUtils.isEmpty(tdsAlias) ? null : tdsSrv.getTDataSource(tdsAlias);
//		if (tds == null) {
//			return null;
//		}
//		Map<String, Object> valueMap = new HashMap<>();
//		valueMap.put(nk, year);
//		
//		FormTdsHandlerService handler = handlerFactory.getInstance(tds.getTdsclassName());
//		if (handler == null) {
//			return null;
//		}
//		
//		Map<String, Map<String, String>> dataMap = new HashMap<String, Map<String,String>>();
//		List<TdsinPara> tdsInPara = tdsSrv.getTDSInPara(tdsAlias);
//		List data = handler.getTmsfFormTableData(sfForm, tds, tdsInPara, valueMap, null, null, null);
//		if(StringUtils.isNotEmpty(data)) {
//			for (Object obj : data) {
//				Map<String, Object> map = (Map<String, Object>) obj;
//				Set<String> strings = map.keySet();
//				
//				Map<String, String> tmap = new HashMap<String, String>();
//				for (String key : strings) {
//					if (key.equalsIgnoreCase("createByName")) {
//						tmap.put("createName", map.get(key)==null?"":map.get(key).toString());
//					} else if (key.equalsIgnoreCase("updateByName")) {
//						tmap.put("updateName", map.get(key)==null?"":map.get(key).toString());
//					} else if (key.equalsIgnoreCase("_id") || key.equalsIgnoreCase("id")) {
//						tmap.put("id", map.get(key)==null?"":map.get(key).toString());
//					} else if (key.equalsIgnoreCase("createTime") || key.equalsIgnoreCase("updateTime")) {
//						String rv = "";
//						Object o = map.get(key);
//						if(o!=null) {
//							if(o instanceof java.util.Date) {
//								rv = DateTimeUtils.formatDateTime((Date)o);
//							}else if(o instanceof String) {
//								rv = String.valueOf(o);
//							}
//						}
//						String ks = "createTime";
//						if(!key.equalsIgnoreCase("updateTime")) {
//							ks = "updateTime";
//						}
//						tmap.put(ks, rv);
//					}
//				}
//			}
//		}
		
		List<PAMonthDataVo> rlist = new ArrayList<PAMonthDataVo>();
		
		//整理12个月数据
		for (int i = 1; i < 13; i++) {
			String mon = year + "-" + (i<10?"0":"") + i;
			Map<String, String> map = dataMap.get(mon);
			
			PAMonthDataVo vo = new PAMonthDataVo();
			vo.setTmsort(i);
			vo.setId(null);
			vo.setYear(year);
			vo.setMon(mon);
			vo.setFormId(formId);
			vo.setName(sfForm.getName());
			vo.setWordFileId(sfForm.getWordFileId());
			if(map!=null) {
				String id = map.get("id");
				String dataId = map.get("dataId");
				String org = map.get("orgCode");
//				String formName = map.get("formName");
				String ct = map.get("createTime");
				String ut = map.get("updateTime");
				String createName = map.get("createName");
				String updateName = map.get("updateName");
				String userName = StringUtils.isEmpty(updateName) ? createName : updateName;
				
				vo.setId(id);
				vo.setDataId(dataId);
				vo.setTdataId(dataId);
				vo.setOrgCode(org);
				vo.setCreateTime(ct);
				vo.setUpdateTime(ut);
				vo.setUpdateUserName(userName);
				vo.setCreateUserName(createName);
				
			}else {
				String id = TMUID.getUID();
				vo.setDataId(null);
				vo.setTdataId(id);
				vo.setOrgCode(orgCode);
			}
			
			rlist.add(vo);
		}
		
		return rlist;
	}

	/**
	 * @category 保存分析相关word数据
	 * @param param
	 * @return
	 */
	@Override
	public Boolean saveData(PAParam param) {
		Boolean flag = true;
		
		String id = param.getId();
		String formId = param.getFormId();
		String yf = param.getMon();
		String nf = param.getYear();
		String dataId = param.getTdataId();
		String orgCode = param.getOrgCode();
		String unitCode = param.getUnitCode();
		
		SysUser user = SysUserHolder.getCurrentUser();
		String userName = user.getRealName();
		String timeStr = DateTimeUtils.getNowDateTimeStr();
		
		if(StringUtils.isEmpty(id)) {//无数据进行保存
			String formName = null;
			SFForm sfForm = fmsrv.queryFormInfoById(formId);
			if (sfForm != null) {
				formName = sfForm.getName();
			}
			
			PaWordInfo obj = new PaWordInfo();
			obj.setId(TMUID.getUID());
			obj.setNf(nf);
			obj.setYf(yf);
			obj.setFormId(formId);
			obj.setFormName(formName);
			obj.setDataId(dataId);
			obj.setOrgCode(orgCode);
			obj.setUnitCode(unitCode);
			obj.setCreateUserName(userName);
			obj.setUpdateUserName(userName);
			obj.setCreateTimeStr(timeStr);
			obj.setUpdateTimeStr(timeStr);
			obj.setTmused(1);
			
			flag = flag && 1 == srv.insert(obj);
		}else {//有数据进行更新
			PaWordInfo obj = srv.queryObjectById(PaWordInfo.class, id);
			if(obj!=null) {
				obj.setUpdateUserName(userName);
				obj.setUpdateTimeStr(timeStr);
				flag = flag && 1 == srv.updateById(obj);
			}
		}
		
		return flag;
	}

	@Override
	public List<PAMonthDataVo> loadMonthData(PAParam param) {
		
		List<PAMonthDataVo> rlist = new ArrayList<PAMonthDataVo>();
		
		//参数获取
		String formId = param.getFormId();
		String mon = param.getMon();
		String orgCode = param.getOrgCode();
		String year = null;
		if(StringUtils.isEmpty(mon)) {
			return rlist;
		}else {
			year = mon.substring(0, 4);
		}
		
		Map<String, Map<String, String>> dataMap = new HashMap<String, Map<String,String>>();
		
		Where where = Where.create();
		where.eq(PaWordInfo::getTmused, 1);
		where.eq(PaWordInfo::getYf, mon);
		where.eq(PaWordInfo::getFormId, formId);
		if(StringUtils.isNotEmpty(orgCode)) {
			where.eq(PaWordInfo::getOrgCode, orgCode);
		}
		List<PaWordInfo> list = srv.queryData(PaWordInfo.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			for (PaWordInfo obj : list) {
				Map<String, String> tmap = new HashMap<String, String>();
				tmap.put("id", obj.getId());
				tmap.put("dataId", obj.getDataId());
				tmap.put("orgCode", obj.getOrgCode());
				tmap.put("unitCode", obj.getUnitCode());
				tmap.put("createTime", obj.getCreateTimeStr());
				tmap.put("updateTime", obj.getUpdateTimeStr());
				tmap.put("createName", obj.getCreateUserName());
				tmap.put("updateName", obj.getUpdateUserName());
				tmap.put("formName", obj.getFormName());
				dataMap.put(obj.getUnitCode(), tmap);
			}
		}
		
		SFForm sfForm = fmsrv.queryFormInfoById(formId);
		if (sfForm == null) {
			return null;
		}
		
		//获取核算对象列表，循环列表，输出信息
		List<Costuint>  listCostuint = costService.getCostuintListOrgId(orgCode);
		if(StringUtils.isNotEmpty(listCostuint)) {
			SysUser user = SysUserHolder.getCurrentUser();
			int sort = 1;
			for (Costuint unit : listCostuint) {
				Map<String, String> map = dataMap.get(unit.getId());
				
				
				boolean isAdmin = SysUserUtil.isAdmin(user.getId());
				String qx = "";
				if(isAdmin) {
					qx = "manage";
				}else {
					String userOrg = user.getOrgId();
					qx = costService.userOrgIsManageOrg(unit.getId(), userOrg, user.getId(), user.getPostId());
				}
				
				PAMonthDataVo vo = new PAMonthDataVo();
				vo.setId(null);
				vo.setTmsort(sort++);
				vo.setYear(year);
				vo.setMon(mon);
				vo.setFormId(formId);
				vo.setName(sfForm.getName());
				vo.setWordFileId(sfForm.getWordFileId());
				vo.setUnitCode(unit.getId());
				vo.setUnitName(unit.getName());
				vo.setQx(qx);
				if(map!=null) {
					String id = map.get("id");
					String dataId = map.get("dataId");
					String org = map.get("orgCode");
					String ct = map.get("createTime");
					String ut = map.get("updateTime");
					String createName = map.get("createName");
					String updateName = map.get("updateName");
					String userName = StringUtils.isEmpty(updateName) ? createName : updateName;
					
					vo.setId(id);
					vo.setDataId(dataId);
					vo.setTdataId(dataId);
					vo.setOrgCode(org);
					vo.setCreateTime(ct);
					vo.setUpdateTime(ut);
					vo.setUpdateUserName(userName);
					vo.setCreateUserName(createName);
					
				}else {
					String id = TMUID.getUID();
					vo.setDataId(null);
					vo.setTdataId(id);
					vo.setOrgCode(orgCode);
				}
				
				rlist.add(vo);
			}
		}
		
		return rlist;
	}

	@Override
	public String getMonth(PAParam param) {
		String diff = param.getMonDiff();
		int idiff = -1;
		if(Coms.judgeLong(diff)) {
			idiff = Integer.parseInt(diff);
		}
		
		String month = null;
		if(idiff!=0) {
			month = DateTimeUtils.formatDateTime(DateTimeUtils.doMonth(DateTimeUtils.getNowDate(), idiff)).substring(0, 7);
		}else {
			month = DateTimeUtils.getNowDateStr().substring(0, 7);
		}
		
		return month;
	}
	
	@Override
	public String getUserAuth(PAParam param) {
		String unitCode = param.getUnitCode();
		SysUser user = SysUserHolder.getCurrentUser();
		
		boolean isAdmin = SysUserUtil.isAdmin(user.getId());
		if(isAdmin) {
			return "manage";
		}
		
		String orgCode = user.getOrgId();
		String qx = costService.userOrgIsManageOrg(unitCode, orgCode, user.getId(), user.getPostId());
		return qx;
	}

    
}
