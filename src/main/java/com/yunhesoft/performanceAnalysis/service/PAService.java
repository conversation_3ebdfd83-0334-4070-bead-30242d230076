package com.yunhesoft.performanceAnalysis.service;

import java.util.List;

import com.yunhesoft.performanceAnalysis.entity.dto.PAParam;
import com.yunhesoft.performanceAnalysis.entity.vo.PAMonthDataVo;

/**
 * 
 * <AUTHOR>
 *
 */
public interface PAService {
	/** 
	 * @category 按年度查询表单各月数据
	 * @param param
	 * @return
	 */
	public List<PAMonthDataVo> loadYearData(PAParam param);
	
	/**
	 * @category 保存分析相关word数据
	 * @param param
	 * @return
	 */
	public Boolean saveData(PAParam param);
	
	/** 
	 * @category 按年度查询表单各月数据
	 * @param param
	 * @return
	 */
	public List<PAMonthDataVo> loadMonthData(PAParam param);
	
	/**
	 * @category 根据参数获取显示月份
	 * @param param
	 * @return
	 */
	public String getMonth(PAParam param);
	/**
	 * @category 获取当前用户权限等级
	 * @param param
	 * @return manage operate other
	 */
	public String getUserAuth(PAParam param);
}
