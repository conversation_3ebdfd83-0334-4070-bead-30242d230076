package com.yunhesoft.performanceAnalysis.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 绩效分析word数据信息
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "PA_WORD_INFO")
public class PaWordInfo extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    /** 年度 */
    @Column(name="NF", length=20)
    private String nf;
    
    /** 月份 */
    @Column(name="YF", length=20)
    private String yf;
    
    /** 核算对象编码 */
    @Column(name="UNIT_CODE", length=50)
    private String unitCode;
    
    /** 表单ID */
    @Column(name="FORM_ID", length=50)
    private String formId;
    
    /** 表单名称 */
    @Column(name="FORM_NAME", length=100)
    private String formName;
    
    /** 数据ID */
    @Column(name="DATA_ID", length=50)
    private String dataId;
    
    /** 机构代码备用 */
    @Column(name="ORG_CODE", length=50)
    private String orgCode;
    
    /** 机构名称备用 */
    @Column(name="ORG_NAME", length=100)
    private String orgName;
    
    /** 创建人姓名 */
    @Column(name="CREATE_USER_NAME", length=50)
    private String createUserName;
    
    /** 更新人姓名 */
    @Column(name="UPDATE_USER_NAME", length=50)
    private String updateUserName;
    
    /** 创建时间 */
    @Column(name="CREATE_TIME_STR", length=50)
    private String createTimeStr;
    
    /** 更新时间 */
    @Column(name="UPDATE_TIME_STR", length=50)
    private String updateTimeStr;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}
