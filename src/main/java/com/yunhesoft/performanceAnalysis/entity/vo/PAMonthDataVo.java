package com.yunhesoft.performanceAnalysis.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @category 月度数据模板
 * <AUTHOR>
 *
 */
@Data
public class PAMonthDataVo {
	
	@ApiModelProperty(value = "年")
	private String year;
	
	@ApiModelProperty(value = "月")
	private String mon;
	
	@ApiModelProperty(value = "表单名称")
	private String name;
	
	@ApiModelProperty(value = "数据ID")
	private String dataId;
	
	@ApiModelProperty(value = "临时数据ID")
	private String tdataId;
	
	@ApiModelProperty(value = "表单ID")
	private String formId;
	
	@ApiModelProperty(value = "机构代码")
	private String orgCode;
	
	@ApiModelProperty(value = "word文件id")
	private String wordFileId;
	
	@ApiModelProperty(value = "数据源别名")
	private String tdsAlias;
	
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	
	@ApiModelProperty(value = "最后更新时间")
	private String updateTime;
	
	@ApiModelProperty(value = "最后更新人")
	private String updateUserName;
	
	@ApiModelProperty(value = "创建人")
	private String createUserName;
	
	@ApiModelProperty(value = "核算对象名称")
	private String unitName;
	
	@ApiModelProperty(value = "核算对象代码")
	private String unitCode;
	
	
	private String id;
	private Integer tmsort;
	private String qx;//manage operate other
	
	
	
}
