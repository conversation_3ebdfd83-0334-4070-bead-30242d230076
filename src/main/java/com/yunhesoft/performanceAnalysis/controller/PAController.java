package com.yunhesoft.performanceAnalysis.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.performanceAnalysis.entity.dto.PAParam;
import com.yunhesoft.performanceAnalysis.entity.vo.PAMonthDataVo;
import com.yunhesoft.performanceAnalysis.service.PAService;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 考试操作
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/performanceAnalysisWord")
@Api(tags = "绩效分析word接口")
@CrossOrigin
public class PAController extends BaseRestController {
	@Autowired
	private PAService srv;

	/**
	 * @category 按年度查询表单各月数据
	 * @param param
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "按年度查询表单各月数据", notes = "按年度查询表单各月数据")
	@RequestMapping(value = "/loadYearData", method = { RequestMethod.POST })
	public Res<List<PAMonthDataVo>> loadYearData(@RequestBody PAParam param) {
		Res<List<PAMonthDataVo>> res = new Res<List<PAMonthDataVo>>();
		res.setResult(srv.loadYearData(param));
		return res;
	}

	/**
	 * @category 保存指定月份数据记录
	 * @param param
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "保存指定月份数据记录")
	@RequestMapping(value = "/saveData", method = { RequestMethod.POST })
	public Res<Boolean> saveData(@RequestBody PAParam param) {
		Res<Boolean> res = new Res<Boolean>();
		res.setResult(srv.saveData(param));
		return Res.OK(res);
	}
	
	/**
	 * @category 按月查询表单机构核算对象数据
	 * @param param
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "按月查询表单机构核算对象数据", notes = "按月查询表单机构核算对象数据")
	@RequestMapping(value = "/loadMonthData", method = { RequestMethod.POST })
	public Res<List<PAMonthDataVo>> loadMonthData(@RequestBody PAParam param) {
		Res<List<PAMonthDataVo>> res = new Res<List<PAMonthDataVo>>();
		res.setResult(srv.loadMonthData(param));
		return res;
	}
	
	/**
	 * @category 根据参数获取显示月份
	 * @param param
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "根据参数获取显示月份", notes = "根据参数获取显示月份，默认取上月，monDiff=-1，可通过参数调整")
	@RequestMapping(value = "/getMonth", method = { RequestMethod.POST })
	public Res<String> getMonth(@RequestBody PAParam param) {
		Res<String> res = new Res<String>();
		res.setResult(srv.getMonth(param));
		return res;
	}
	
	/**
	 * @category 获取当前用户权限等级
	 * @param param
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "获取当前用户权限等级", notes = "获取当前用户权限等级")
	@RequestMapping(value = "/getUserAuth", method = { RequestMethod.POST })
	public Res<String> getUserAuth(@RequestBody PAParam param) {
		Res<String> res = new Res<String>();
		res.setResult(srv.getUserAuth(param));
		return res;
	}
}
