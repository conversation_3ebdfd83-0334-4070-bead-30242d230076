package com.yunhesoft.syslog.utils;

import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;

import javax.sql.DataSource;

import com.alibaba.druid.pool.DruidDataSource;
import com.itextpdf.io.exceptions.IOException;
import com.yunhesoft.core.common.utils.PassUtils;

/**
 * log4j日志存入数据库 数据库连接工厂类
 * 
 * <AUTHOR>
 * @since 2024-04-23
 */
public class LogConnectionFactory {

	private DataSource dataSource;

	private LogConnectionFactory() {
		try {
			Properties prop = new Properties();
			try {
				InputStreamReader inputStreamReader = new InputStreamReader(
						this.getClass().getClassLoader().getResourceAsStream("application.properties"), "utf-8");
				prop.load(inputStreamReader);
			} catch (IOException e) {
				throw new RuntimeException(e);
			}

			String enable = prop.getProperty("app.savelog2db"); // 是否保存到数据库
			if ("true".equals(enable)) {
				String active = prop.getProperty("spring.profiles.active"); // spring.profiles.active=version,config,oracle
				String url = prop.getProperty("spring.datasource.url");
				String username = prop.getProperty("spring.datasource.username");
				String password = PassUtils.ConfigFileDecrypt(prop.getProperty("spring.datasource.password"));
				String driverClassName = "";
				active = active.toLowerCase();
				if (active.contains("oracle")) {
					driverClassName = "oracle.jdbc.OracleDriver";
				} else if (active.contains("sqlserver")) {
					driverClassName = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
				} else if (active.contains("kingbase")) {
					driverClassName = "com.kingbase8.Driver";
				} else if (active.contains("postgresql")) {
					driverClassName = "org.postgresql.Driver";
				} else {
					driverClassName = "com.mysql.cj.jdbc.Driver";
				}
				Properties props = new Properties();
				props.setProperty("druid.url", url);
				props.setProperty("druid.username", username);
				props.setProperty("druid.password", password);
				props.setProperty("druid.driverClassName", driverClassName);
				this.dataSource = new DruidDataSource();
				((DruidDataSource) this.dataSource).configFromPropety(props);
				// Connection conn= this.dataSource.getConnection();
				// conn.isClosed();
				System.out.println("**********log4j2日志数据库连接成功！*************");
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * get connection
	 *
	 * @return
	 * @throws SQLException
	 */
	public static Connection getConnection() throws SQLException {
		// System.out.println("******log4j获取数据库连接");
		return Singleton.INSTANCE.dataSource.getConnection();
	}

	/**
	 * only used inner
	 */
	private interface Singleton {
		LogConnectionFactory INSTANCE = new LogConnectionFactory();
	}
}
