package com.yunhesoft.compositionPlatform.entity.dto;

import com.yunhesoft.compositionPlatform.entity.po.ComPlatAuth;
import com.yunhesoft.compositionPlatform.entity.po.ComPlatMenu;
import com.yunhesoft.compositionPlatform.entity.po.ComPlatUserMenuSort;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("保存数据对象")
@Data
public class ComPlatSaveDataDto {
    @ApiModelProperty("菜单配置列表")
    List<ComPlatMenu> menuList;
    @ApiModelProperty("菜单权限列表")
    List<ComPlatAuth> authList;
    @ApiModelProperty("用户自定义菜单排序")
    List<ComPlatUserMenuSort> userMenuSortList;
}
