package com.yunhesoft.compositionPlatform.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@ApiModel("岗位工作聚合平台个人菜单排序")
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(
        name = "COM_PLAT_USER_MENU_SORT",
        indexes = {@Index(name = "IDX_COMPLAT_MENUSORT_USERID", columnList = "USER_ID")
                , @Index(name = "IDX_COMPLAT_MENUSORT_MENUID", columnList = "COM_PLAT_MENU_ID")}
)
public class ComPlatUserMenuSort extends BaseEntity {
    @ApiModelProperty(name = "用户ID")
    @Column(name = "USER_ID", length = 50)
    private String userId;

    @ApiModelProperty(name = "岗位工作聚合平台菜单ID")
    @Column(name = "COM_PLAT_MENU_ID", length = 50)
    private String comPlatMenuId;

    @ApiModelProperty(name = "序号")
    @Column(name = "SN")
    private Integer sn;
}
