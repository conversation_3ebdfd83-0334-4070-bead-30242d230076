package com.yunhesoft.compositionPlatform.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

@ApiModel("岗位工作聚合平台菜单权限")
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(
        name = "COM_PLAT_AUTH",
        indexes = @Index(name = "IDX_COMPLAT_MENUID", columnList = "COM_PLAT_MENU_ID")
)
public class ComPlatAuth extends BaseEntity {
    @ApiModelProperty(name = "岗位工作聚合平台菜单ID")
    @Column(name = "COM_PLAT_MENU_ID", length = 50)
    private String comPlatMenuId;

    @ApiModelProperty(name = "权限机构编码（多个英文逗号分隔）")
    @Column(name = "ORG_CODES", length = 2000)
    private String permOrgCodes;

    @ApiModelProperty(name = "权限角色ID（多个英文逗号分隔）")
    @Column(name = "PERM_ROLE_IDS", length = 2000)
    private String permRoleIds;

    @ApiModelProperty(name = "权限岗位ID（多个英文逗号分隔）")
    @Column(name = "PERM_POST_IDS", length = 2000)
    private String permPostIds;

    @ApiModelProperty(name = "权限人员ID（多个英文逗号分隔）")
    @Column(name = "PERM_USER_IDS", length = 2000)
    private String permUserIds;

    @ApiModelProperty(name = "权限key（多个英文逗号分隔）")
    @Column(name = "PERM_KEYS", length = 2000)
    private String permKeys;
}
