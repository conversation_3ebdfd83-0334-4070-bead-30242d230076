package com.yunhesoft.compositionPlatform.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

@ApiModel("岗位工作聚合平台菜单")
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "COM_PLAT_MENU")
public class ComPlatMenu extends BaseEntity {
    @ApiModelProperty(name = "菜单库数据ID")
    @Column(name = "MENU_LIB_ID", length = 50)
    private String menuLibId;

    @ApiModelProperty(name = "菜单名称")
    @Column(name = "MENU_NAME", length = 200)
    private String menuName;

    @ApiModelProperty(name = "菜单图标")
    @Column(name = "ICON", length = 200)
    private String icon;

    @ApiModelProperty(name = "菜单类型", example = "1=内部系统组件，2=外部系统页面")
    @Column(name = "MENU_TYPE")
    private Integer menuType;

    @ApiModelProperty(name = "PC地址")
    @Column(name = "PC_URL", length = 500)
    private String pcUrl;

    @ApiModelProperty(name = "APP地址")
    @Column(name = "APP_URL", length = 500)
    private String appUrl;

    @ApiModelProperty(name = "应用范围", example = "1=PC，2=APP，3=全部")
    @Column(name = "APPLY_RANGE")
    private Integer applyRange;

    @ApiModelProperty(name = "显示状态", example = "1=显示，2=隐藏")
    @Column(name = "VISIBLE")
    private Integer visible;

    @ApiModelProperty(name = "序号")
    @Column(name = "SN")
    private Integer sn;

    @ApiModelProperty(name = "有效标识", example = "1=有效，0=无效")
    @Column(name = "TMUSED")
    private Integer tmused;

    @Transient
    @ApiModelProperty(name = "行记录标识：1=增、-1=删、0=改")
    private Integer flag;
}
