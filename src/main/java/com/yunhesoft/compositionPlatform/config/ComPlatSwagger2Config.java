package com.yunhesoft.compositionPlatform.config;

import com.yunhesoft.core.common.component.Swagger2Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

@Configuration
@EnableSwagger2WebMvc
public class ComPlatSwagger2Config {
    @Autowired
    private Swagger2Utils utils;

    @Bean(value = "compositionPlatform-api")
    public Docket bzjsDocket() {
        ApiInfo apiInfo = utils.apiInfo("compositionPlatform-api", "", "0.0.1");
        return utils.newDocket(apiInfo, "tm4-岗位工作聚合平台", "com.yunhesoft.compositionPlatform");
    }
}
