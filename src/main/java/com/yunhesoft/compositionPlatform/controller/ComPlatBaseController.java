package com.yunhesoft.compositionPlatform.controller;

import com.yunhesoft.compositionPlatform.entity.dto.ComPlatSaveDataDto;
import com.yunhesoft.compositionPlatform.entity.dto.ComPlatSyncDataDto;
import com.yunhesoft.compositionPlatform.entity.vo.ComPlatConfVo;
import com.yunhesoft.compositionPlatform.service.IComPlatService;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/compositionPlatform/conf")
@Api(tags = "功能聚合平台 - 基础数据接口")
@Log4j2
public class ComPlatBaseController extends BaseRestController {
    @Autowired
    private IComPlatService comPlatService;

    @ApiOperation("获取功能菜单配置列表数据")
    @RequestMapping(value = "/getConfList", method = {RequestMethod.POST})
    public Res getConfList() {
        try {
            List<ComPlatConfVo> confList = comPlatService.getConfList();
            return Res.OK(confList);
        } catch (Exception e) {
            log.error("获取功能菜单配置列表数据失败", e);
            return Res.FAIL("获取功能菜单配置列表数据失败（错误信息：" + e.getMessage() + "）");
        }
    }

    @ApiOperation(value = "保存功能菜单配置列表数据", notes = "操作：CRUD、上移/下移；记录标识：1=增、-1=删、0=改")
    @RequestMapping(value = "/saveConfList", method = {RequestMethod.POST})
    public Res saveConfList(@RequestBody ComPlatSaveDataDto saveDataDto) {
        try {
            String error = comPlatService.saveConfList(saveDataDto);
            if (StringUtils.isEmpty(error)) {
                return Res.OK("保存功能菜单配置列表数据成功");
            } else {
                return Res.FAIL("保存功能菜单配置列表数据失败（错误信息：" + error + "）");
            }
        } catch (Exception e) {
            log.error("保存功能菜单配置列表数据失败", e);
            return Res.FAIL("保存功能菜单配置列表数据失败（错误信息：" + e.getMessage() + "）");
        }
    }

    @ApiOperation("同步菜单资源库")
    @RequestMapping(value = "/syncMenuResourceLibrary", method = {RequestMethod.POST})
    public Res syncMenuResourceLibrary(@RequestBody ComPlatSyncDataDto syncDataDto) {
        try {
            String error = comPlatService.syncMenuResourceLibrary(syncDataDto);
            if (StringUtils.isEmpty(error)) {
                return Res.OK("同步菜单资源库成功");
            } else {
                return Res.FAIL("同步菜单资源库失败（错误信息：" + error + "）");
            }
        } catch (Exception e) {
            log.error("同步菜单资源库失败", e);
            return Res.FAIL("同步菜单资源库失败（错误信息：" + e.getMessage() + "）");
        }
    }

    @ApiOperation("保存外部系统页面的使用权限")
    @RequestMapping(value = "/saveMenuAuth", method = {RequestMethod.POST})
    public Res saveMenuAuth(@RequestBody ComPlatSaveDataDto saveDataDto) {
        try {
            String error = comPlatService.saveMenuAuth(saveDataDto);
            if (StringUtils.isEmpty(error)) {
                return Res.OK("保存外部系统页面的使用权限成功");
            } else {
                return Res.FAIL("保存外部系统页面的使用权限失败（错误信息：" + error + "）");
            }
        } catch (Exception e) {
            log.error("保存外部系统页面的使用权限失败", e);
            return Res.FAIL("保存外部系统页面的使用权限失败（错误信息：" + e.getMessage() + "）");
        }
    }

    @ApiOperation("保存用户自定义菜单排序")
    @RequestMapping(value = "/saveUserMenuSort", method = {RequestMethod.POST})
    public Res saveUserMenuSort(@RequestBody ComPlatSaveDataDto saveDataDto) {
        try {
            String error = comPlatService.saveUserMenuSort(saveDataDto);
            if (StringUtils.isEmpty(error)) {
                return Res.OK("保存用户自定义菜单排序成功");
            } else {
                return Res.FAIL("保存用户自定义菜单排序失败（错误信息：" + error + "）");
            }
        } catch (Exception e) {
            log.error("保存用户自定义菜单排序失败", e);
            return Res.FAIL("保存用户自定义菜单排序失败（错误信息：" + e.getMessage() + "）");
        }
    }

    @ApiOperation(value = "获取当前用户可使用的菜单列表", notes = "此接口应用于聚合平台菜单列表与个人菜单自定义排序")
    @RequestMapping(value = "/getMenuOfMine", method = {RequestMethod.POST})
    public Res getMenuOfMine() {
        try {
            List<ComPlatConfVo> confList = comPlatService.getMenuOfMine();
            return Res.OK(confList);
        } catch (Exception e) {
            log.error("获取当前用户可使用的菜单列表数据失败", e);
            return Res.FAIL("获取当前用户可使用的菜单列表数据失败（错误信息：" + e.getMessage() + "）");
        }
    }
}
