package com.yunhesoft.compositionPlatform.service.impl;

import com.yunhesoft.compositionPlatform.entity.dto.ComPlatSaveDataDto;
import com.yunhesoft.compositionPlatform.entity.dto.ComPlatSyncDataDto;
import com.yunhesoft.compositionPlatform.entity.po.ComPlatAuth;
import com.yunhesoft.compositionPlatform.entity.po.ComPlatMenu;
import com.yunhesoft.compositionPlatform.entity.po.ComPlatUserMenuSort;
import com.yunhesoft.compositionPlatform.entity.vo.ComPlatConfVo;
import com.yunhesoft.compositionPlatform.service.IComPlatService;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.menu.entity.po.SysMenuLib;
import com.yunhesoft.system.menu.service.ISysMenuLibService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ComPlatServiceImpl implements IComPlatService {
    @Autowired
    private EntityService entityService;
    @Autowired
    private ISysMenuLibService sysMenuLibService;

    /**
     * 获取功能聚合平台配置数据
     * @return 配置数据
     */
    @Override
    public List<ComPlatConfVo> getConfList() {
        List<ComPlatConfVo> comPlatConfVos = new ArrayList<>();

        Order order = new Order();
        order.orderByAsc(ComPlatMenu::getSn);

        List<ComPlatMenu> comPlatMenus = entityService.queryData(ComPlatMenu.class, null, null, null);
        if (!ObjectUtils.isEmpty(comPlatMenus)) {
            comPlatConfVos = ObjUtils.convertToList(ComPlatConfVo.class, comPlatMenus);
        }

        return comPlatConfVos;
    }

    /**
     * 保存功能菜单配置列表数据
     * @param saveDataDto 保存数据对象
     * @return 错误信息
     */
    @Override
    @Transactional
    public String saveConfList(ComPlatSaveDataDto saveDataDto) {
        if (ObjectUtils.isEmpty(saveDataDto)) {
            return "保存数据对象不能为空";
        }
        List<ComPlatMenu> menuList = saveDataDto.getMenuList();
        if (ObjectUtils.isEmpty(menuList)) {
            return "保存数据列表不能为空";
        }

        List<ComPlatMenu> newMenuList = new ArrayList<>(); //新增
        List<ComPlatMenu> modMenuList = new ArrayList<>(); //修改
        List<ComPlatMenu> delMenuList = new ArrayList<>(); //删除

        //解析数据
        for (ComPlatMenu menu : menuList) {
            Integer flag = Optional.ofNullable(menu.getFlag()).orElse(0);
            if (flag == 1) { // 新增
                menu.setId(TMUID.getUID());
                menu.setTmused(1);
                newMenuList.add(menu);
            } else if (flag == -1) { // 删除
                menu.setTmused(0);
                delMenuList.add(menu);
            } else if (flag == 0) { // 修改
                modMenuList.add(menu);
            }
        }

        //保存数据
        if (!ObjectUtils.isEmpty(newMenuList)) {
            entityService.rawInsertBatch(newMenuList); //批量新增
        }
        if (!ObjectUtils.isEmpty(modMenuList)) {
            entityService.updateBatch(modMenuList); //批量修改
        }
        if (!ObjectUtils.isEmpty(delMenuList)) {
            entityService.updateBatch(delMenuList); //批量假删除
        }

        return "";
    }

    /**
     * 保存功能菜单配置列表数据
     * @param saveDataDto
     * @return 错误信息
     */
    @Override
    @Transactional
    public String saveMenuAuth(ComPlatSaveDataDto saveDataDto) {
        if (ObjectUtils.isEmpty(saveDataDto)) {
            return "保存数据对象不能为空";
        }
        List<ComPlatAuth> authList = saveDataDto.getAuthList();
        if (ObjectUtils.isEmpty(authList)) {
            return "保存数据列表不能为空";
        }

        List<ComPlatAuth> newMenuAuthList = new ArrayList<>(); //新增
        List<ComPlatAuth> modMenuAuthList = new ArrayList<>(); //修改

        //解析
        for (ComPlatAuth comPlatAuth : authList) {
            String id = comPlatAuth.getId();
            if (ObjectUtils.isEmpty(id)) { //添加
                comPlatAuth.setId(TMUID.getUID());
                newMenuAuthList.add(comPlatAuth);
            } else { //修改（不删除，清空操作也为更新，每个菜单至多一条权限数据）
                modMenuAuthList.add(comPlatAuth);
            }
        }

        //保存数据
        if (!ObjectUtils.isEmpty(newMenuAuthList)) {
            entityService.rawInsertBatch(newMenuAuthList); //批量新增
        }
        if (!ObjectUtils.isEmpty(modMenuAuthList)) {
            entityService.updateBatch(modMenuAuthList); //批量修改
        }

        return "";
    }

    /**
     * 同步菜单资源库
     * @param syncDataDto
     * @return 错误信息
     */
    @Transactional
    @Override
    public String syncMenuResourceLibrary(ComPlatSyncDataDto syncDataDto) {
        if (ObjectUtils.isEmpty(syncDataDto)) {
            return "同步数据对象不能为空";
        }
        String[] menuLibIds = syncDataDto.getMenuLibIds();
        if (ObjectUtils.isEmpty(menuLibIds)) {
            return "同步数据ID列表不能为空";
        }
        //从菜单资源库获取菜单资源
        List<String> list = Arrays.asList(menuLibIds);
        List<SysMenuLib> listLib = sysMenuLibService.getMenuLibList(list);

        if (ObjectUtils.isEmpty(listLib)) {
            return "未获取到有效的菜单库数据";
        }

        //获取数据库中的聚合平台菜单配置数据
        List<ComPlatConfVo> dbConfList = getConfList();
        Map<String, ComPlatConfVo> dbConfMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(dbConfList)) {
            dbConfMap = dbConfList.stream().filter(v -> !StringUtils.isEmpty(v.getMenuLibId())).collect(Collectors.toMap(ComPlatConfVo::getMenuLibId, v -> v));
        }

        //解析菜单库数据
        List<ComPlatMenu> newMenuList = getComPlatMenus(listLib, dbConfMap);

        //保存数据
        if (!ObjectUtils.isEmpty(newMenuList)) {
            entityService.rawInsertBatch(newMenuList); //批量新增
        }

        return "";
    }

    /**
     * 解析菜单库数据
     * @param listLib
     * @param dbConfMap
     * @return
     */
    private List<ComPlatMenu> getComPlatMenus(List<SysMenuLib> listLib, Map<String, ComPlatConfVo> dbConfMap) {
        List<ComPlatMenu> newMenuList = new ArrayList<>(); //新增聚合平台菜单数据

        for (SysMenuLib e : listLib) {
            // 第一层数据是菜单，children层数据是按钮权限
            String id = e.getId();
            String menuName = e.getMenuName();

            if (dbConfMap.containsKey(id)) {
                continue; // 已存在则跳过
            }

            //菜单库数据 → 聚合平台菜单数据
            ComPlatMenu comPlatMenu = new ComPlatMenu();
            comPlatMenu.setId(TMUID.getUID());
            comPlatMenu.setTmused(1);
            comPlatMenu.setMenuName(menuName);
            comPlatMenu.setMenuLibId(id);
            comPlatMenu.setMenuType(1);
            comPlatMenu.setVisible(1);
            comPlatMenu.setIcon(e.getIcon());
            comPlatMenu.setPcUrl(e.getPath());
            comPlatMenu.setAppUrl(e.getApp_path());
            comPlatMenu.setSn(e.getTmsort());

            Integer applyRange = 1;
            try {
                String applyRangeStr = e.getApplyRange();
                applyRange = Integer.parseInt(applyRangeStr);
            } catch (Exception ex) {
            }
            comPlatMenu.setApplyRange(applyRange);

            newMenuList.add(comPlatMenu);
        }

        return newMenuList;
    }

    /**
     * 保存用户自定义菜单排序
     * @param saveDataDto
     * @return
     */
    @Override
    public String saveUserMenuSort(ComPlatSaveDataDto saveDataDto) {
        if (ObjectUtils.isEmpty(saveDataDto)) {
            return "保存数据对象不能为空";
        }
        List<ComPlatUserMenuSort> userMenuSortList = saveDataDto.getUserMenuSortList();
        if (ObjectUtils.isEmpty(userMenuSortList)) {
            return "保存数据列表不能为空";
        }

        List<ComPlatUserMenuSort> newMenuSortList = new ArrayList<>(); //新增
        SysUser user = SysUserHolder.getCurrentUser();

        //解析
        for (ComPlatUserMenuSort userMenuSort : userMenuSortList) {
            userMenuSort.setId(TMUID.getUID());
            userMenuSort.setUserId(user.getId());
            newMenuSortList.add(userMenuSort);
        }

        //删除历史数据
        entityService.rawExcute("delete from COM_PLAT_USER_MENU_SORT where USER_ID = '" + user.getId() + "'");

        //保存新数据
        if (!ObjectUtils.isEmpty(newMenuSortList)) {
            entityService.rawInsertBatch(newMenuSortList); //批量新增
        }

        return "";
    }

    /**
     * 获取当前用户可使用的菜单列表
     * @return 菜单列表
     */
    @Override
    public List<ComPlatConfVo> getMenuOfMine() {
        //TODO 获取所有菜单数据列表
        List<ComPlatConfVo> confList = getConfList();

        //TODO 获取个人自定义功能菜单排序配置数据

        //TODO 获取TM4系统级菜单权限配置数据

        //TODO 根据聚合平台权限、TM4系统级菜单权限配置数据，过滤当前用户可用的功能菜单

        //TODO 根据个人自定义功能菜单排序配置数据进行排序，如未自定义，则按全局菜单默认配置排序

        return Collections.emptyList();
    }

    /**
     * 获取功能菜单配置列表与权限数据
     * @return
     */
    private String getConfMenuAndAuth(){
        String sqlStr ="select * from com_plat_menu t1 "
                +" left join com_plat_auth t2 on t2.com_plat_menu_id=t1.id ";

        //TODO 获取功能菜单配置列表与权限数据

        //entityService.

        return "";
    }
}
