package com.yunhesoft.compositionPlatform.service;

import com.yunhesoft.compositionPlatform.entity.dto.ComPlatSaveDataDto;
import com.yunhesoft.compositionPlatform.entity.dto.ComPlatSyncDataDto;
import com.yunhesoft.compositionPlatform.entity.vo.ComPlatConfVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface IComPlatService {
    /**
     * 获取功能聚合平台配置数据
     * @return
     */
    List<ComPlatConfVo> getConfList();

    /**
     * 保存功能菜单配置列表数据
     * @param saveDataDto 保存数据对象
     * @return 错误信息
     */
    String saveConfList(ComPlatSaveDataDto saveDataDto);

    /**
     * 保存功能菜单权限列表数据
     * @param saveDataDto
     * @return
     */
    String saveMenuAuth(ComPlatSaveDataDto saveDataDto);

    /**
     * 保存用户自定义菜单排序
     * @param saveDataDto
     * @return
     */
    String saveUserMenuSort(ComPlatSaveDataDto saveDataDto);

    /**
     * 同步菜单资源库
     * @param syncDataDto
     * @return
     */
    String syncMenuResourceLibrary(ComPlatSyncDataDto syncDataDto);

    /**
     * 获取当前用户可使用的菜单列表
     * @return 菜单列表
     */
    List<ComPlatConfVo> getMenuOfMine();
}
