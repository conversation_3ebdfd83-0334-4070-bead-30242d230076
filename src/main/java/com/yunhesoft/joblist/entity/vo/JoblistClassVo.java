package com.yunhesoft.joblist.entity.vo;


import java.util.List;

import com.yunhesoft.joblist.entity.po.JoblistClass;

import lombok.Getter;
import lombok.Setter;

/**
 * 	作业分类Vo类
 * <AUTHOR>
 * @date 2024/06/12
 */
@Setter
@Getter
public class JoblistClassVo extends JoblistClass {
	
	private static final long serialVersionUID = 1L;

	/** 节点id */
    private String nodeId;
    
    /** 节点名称 */
    private String nodeName;
    
    /** 子记录 */
    private List<JoblistClassVo> children;
    
	/** 专业id列表 */
    private List<String> specialityIdList;
    
    /** 节点显示名称 */
    private String nodeShowName;
    
    /** 路径 */
    private String path;
    
}
