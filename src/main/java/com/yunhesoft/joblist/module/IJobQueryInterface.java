package com.yunhesoft.joblist.module;


import com.yunhesoft.joblist.entity.dto.JobFinishDto;
import com.yunhesoft.joblist.entity.dto.JoblistInputDto;
import com.yunhesoft.joblist.entity.vo.JoblistActivityExampleVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 工作清单多模块查询接口
 * @date 2025/3/6
 */
public interface IJobQueryInterface {
    /**
     * 查询工作完成情况
     *
     * @return
     * <AUTHOR>
     * @date 2025/3/6
     * @params
     */
    JobFinishDto getFinishCase(JoblistInputDto param);

    /**
     * 查询工作列表
     *
     * @return
     * <AUTHOR>
     * @date 2025/3/6
     * @params
     */
    List<JoblistActivityExampleVo> getJobList(JoblistInputDto param);
}
