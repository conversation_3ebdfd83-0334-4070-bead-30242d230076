package com.yunhesoft.joblist.operCard.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 操作卡执行_提示卡阅读记录
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "OPERCARD_EXEC_STEPREAD", indexes = {@Index(name = "opercard_exec_stepread_i", columnList = "EXECID")})
public class OpercardExecStepread extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 实例ID */
    @Column(name="EXECID", length=50)
    private String execId;
    
    /** 步骤ID */
    @Column(name="STEPID", length=50)
    private String stepId;
    
    /** 执行实例步骤ID */
    @Column(name="EXECSTEPID", length=50)
    private String execStepId;
    
    /** 段落ID */
    @Column(name="GROUPID", length=50)
    private String groupId;
    
    /** 阅读标识 */
    @Column(name="READSIGN")//0：未阅读 1：已阅读
    private Integer readSign;
    
    /** 阅读岗位 */
    @Column(name="POSTID", length=50)
    private String postId;
    
    /** 岗位名称 */
    @Column(name="POSTNAME", length=255)
    private String postName;
    
    /** 阅读人 */
    @Column(name="READPERSON", length=200)
    private String readPerson;
    
    /** 阅读人id */
    @Column(name="READPERSONID", length=50)
    private String readPersonId;
    
    /** 阅读时间 */
    @Column(name="READDT")
    private Date readDt;
    

}