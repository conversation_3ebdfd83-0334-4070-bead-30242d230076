package com.yunhesoft.joblist.operCard.entity.dto;

import java.util.List;

import com.yunhesoft.joblist.operCard.entity.vo.OperCardExecInstrumentVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardExecStepVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="操作卡执行DTO类",description="操作卡执行DTO类")
public class OperCardExecDto {
	
	@ApiModelProperty(value = "执行命令") 
    private String execCommand;//执行当前岗位全部记录:execPostAll,执行步骤:execStep,采集仪表数据:collectInstrument,阅读提示卡:readCard,重复步骤:repeatStep,跳过步骤:skipStep,撤销跳过步骤:cancelSkipStep,审核跳过步骤:auditSkipStep
    
	@ApiModelProperty(value = "执行实例ID")
    private String execId;
  
    @ApiModelProperty(value = "执行参数,由执行页面传入，操作卡执行完毕后，传给对应的功能消息时携带这个参数值")
    private String execParam;
    
    @ApiModelProperty(value = "要处理的步骤数据列表")
 	private List<OperCardExecStepVo> dataList;
       
    @ApiModelProperty(value = "仪表信息列表（采集仪表数据时使用）")
    private List<OperCardExecInstrumentVo> instrumentList;
}