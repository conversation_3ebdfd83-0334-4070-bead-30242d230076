package com.yunhesoft.joblist.operCard.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 操作卡执行_步骤执行信息
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "OPERCARD_EXEC_STEPDETAILS", indexes = {@Index(name = "opercard_exec_stepdetails_i", columnList = "EXECID")})
public class OpercardExecStepdetails extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 实例ID */
    @Column(name="EXECID", length=50)
    private String execId;
    
    /** 步骤ID */
    @Column(name="STEPID", length=50)
    private String stepId;
    
    /** 执行实例步骤ID */
    @Column(name="EXECSTEPID", length=50)
    private String execStepId;
    
    /** 操作标识岗位 */
    @Column(name="OPSIGNPOST", length=50)//I:内操 P:外操 M班长
    private String opSignPost;
    
    /** 执行状态 */
    @Column(name="EXECSTATUS")//0：未确认 1：已确认； 2：跳过步骤
    private Integer execStatus;
    
    /** 录入时间 */
    @Column(name="INPUTDT")
    private Date inputDt;
    
    /** 开始时间 */
    @Column(name="STARTDT")
    private Date startDt;
    
    /** 结束时间 */
    @Column(name="ENDDT")
    private Date endDt;
    
    /** 执行持续时间 */
    @Column(name="EXECUTIVETIME")
    private Double executiveTime;
    
    /** 执行岗位机构代码 */
    @Column(name="POSTORGCODE", length=50)
    private String postOrgCode;
    
    /** 执行岗位 */
    @Column(name="POSTID", length=50)
    private String postId;
    
    /** 岗位名称 */
    @Column(name="POSTNAME", length=255)
    private String postName;

    /** 执行人 */
    @Column(name="EXECPERSON", length=200)
    private String execPerson;
    
    /** 执行人id */
    @Column(name="EXECPERSONID", length=50)
    private String execPersonId;

}