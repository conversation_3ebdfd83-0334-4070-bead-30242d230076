package com.yunhesoft.joblist.operCard.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.itextpdf.styledxmlparser.jsoup.Jsoup;
import com.itextpdf.styledxmlparser.jsoup.nodes.Document;
import com.itextpdf.styledxmlparser.jsoup.nodes.Element;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.operCard.entity.dto.OperCardOperstepDto;
import com.yunhesoft.joblist.operCard.entity.po.OpercardCatalog;
import com.yunhesoft.joblist.operCard.entity.po.OpercardInfo;
import com.yunhesoft.joblist.operCard.entity.po.OpercardOperstep;
import com.yunhesoft.joblist.operCard.entity.po.OpercardSteppost;
import com.yunhesoft.joblist.operCard.entity.vo.OpercardOperstepVo;
import com.yunhesoft.joblist.operCard.service.IParseWordInfoService;
import com.yunhesoft.joblist.operCard.service.IParseWordOperstepService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.impl.EntityServiceImpl;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class ParseWordOperstepServiceImpl implements IParseWordOperstepService {
    @Autowired
    private EntityService entityService;
    @Autowired
    private EntityServiceImpl entservice;
    @Autowired
    private IParseWordInfoService infoServ;

    @Override
    public List<OpercardOperstep> queryOperstep(OpercardCatalog opercardCatalog) {
        Where where = new Where();
        where.eq(OpercardOperstep::getCatalogAlias, opercardCatalog.getId());
        where.eq(OpercardOperstep::getTmUsed, 1);
        where.isNull(OpercardOperstep::getCardVerId);
        List<OpercardOperstep> lists = entityService.queryList(OpercardOperstep.class, where,
                Order.create().orderByAsc(OpercardOperstep::getTmSort), null);

        List<OpercardOperstep> operstepLists = new ArrayList<>();
        for (OpercardOperstep operstep : lists) {
//            operstep.setOpContent(getRowTitle(operstep.getOpContent()));
            operstepLists.add(operstep);
        }
        return operstepLists;
    }

    @Override
    public List<OpercardOperstep> queryOperstepType(OpercardCatalog opercardCatalog) {
        List<OpercardOperstep> operstepLists = new ArrayList<>();
        if(opercardCatalog!=null) {
	        Where where = Where.create();
	        where.eq(OpercardOperstep::getCatalogAlias, opercardCatalog.getCatalogAlias());
	//        where.eq(OpercardOperstep::getOpType, 3);
//	        where.eq(OpercardOperstep::getTmUsed, 1);
	        where.isNull(OpercardOperstep::getCardVerId);
	        Order order = Order.create();
	        order.orderByAsc(OpercardCatalog::getTmSort);
	        List<OpercardOperstep> lists = entityService.queryList(OpercardOperstep.class, where, order);
	    	if(StringUtils.isNotEmpty(lists)) {
	    		Map<String,List<OpercardSteppost>> postMap = null;
	    		Where where1 = Where.create();
	    		where1.eq(OpercardSteppost::getCatalogAlias, opercardCatalog.getCatalogAlias());
	    		where1.eq(OpercardSteppost::getTmUsed, 1);
	    		where1.isNull(OpercardSteppost::getCardVerId);
	    		Order order1 = Order.create();
	   	        order1.orderByAsc(OpercardSteppost::getId);
	   	        List<OpercardSteppost> listPost = entityService.queryList(OpercardSteppost.class, where1, order1);
	   	        if(StringUtils.isNotEmpty(listPost)) {
	   	        	postMap =  listPost.stream().collect(Collectors.groupingBy(obj->obj.getStepId()==null?"":obj.getStepId()));//按步骤id分组
	   	        }
				if(postMap==null) {
					postMap = new HashMap<String,List<OpercardSteppost>>();
				}
				String operateRegex = "^(&lt;|[\\[\\(])([IPM]|[IP][\\|][IP])(&gt;|[\\]\\)])";//操作标识解析(用断言(?!\\<[Pp]\\>)取操作符)	
				Pattern pOperate = Pattern.compile(operateRegex);
	   	        for (OpercardOperstep operstep : lists) {
		            if (!ObjUtils.isEmpty(operstep.getOpContent())) {
		                if(operstep.getOpType()!=null && operstep.getOpType().intValue()==3) {
		                	if(StringUtils.isNotEmpty(operstep.getOpContent())) {
		                		StringBuffer sb = new StringBuffer();
		                		String content = getRowTitle(operstep.getOpContent());
		          				Matcher m = pOperate.matcher(content);
		            			if(m.find()){//查找到了,代表输入数据正确有效		            				
		            				m.appendReplacement(sb,"");//替换已经查找到的数据为""
		            				m.appendTail(sb);//将剩下未匹配的尾部字符添加进StringBuffer
		            			}
	            				operstep.setOpContent(sb.toString());//操作内容文本
		                	}
		                }
		            }
		            List<OpercardSteppost> step = postMap.get(operstep.getId());
		            if(StringUtils.isNotEmpty(step)) {
		            	StringBuffer postId = new StringBuffer();
		            	StringBuffer postName = new StringBuffer();
		            	for(OpercardSteppost temp:step) {
		            		postId.append(","+temp.getPostId());
		            		postName.append(","+temp.getPostName());
		            	}
		            	operstep.setPostId(postId.substring(1));
		            	operstep.setPostName(postName.substring(1));
		            }
		            operstepLists.add(operstep);
		        }
	    	}
        }
        return operstepLists;
    }

    @Override
    public int updateOperstep(OpercardOperstep opercardOperstep) {
        //根据节点id，删除该节点下所有操作步骤并更新，修改后的操作步骤
        entityService.update(opercardOperstep);
        if (!ObjUtils.isEmpty(opercardOperstep.getCatalogAlias())) {
        	updateCardContent(opercardOperstep.getCatalogAlias());
//            StringBuffer sb = new StringBuffer();
//            Where where1 = Where.create();
//            where1.eq(OpercardOperstep::getCatalogAlias, opercardOperstep.getCatalogAlias());
//            where1.eq(OpercardOperstep::getTmUsed, 1);
//            List<OpercardOperstep> opersteplistNew = entityService.queryList(OpercardOperstep.class, where1,
//                    Order.create().orderByAsc(OpercardOperstep::getTmSort), null);
//            for (OpercardOperstep operstep:opersteplistNew) {
//                sb.append(operstep.getOpContent());
//            }
//            OpercardCatalog catlog = entityService.queryObjectById(OpercardCatalog.class,opercardOperstep.getGroupId());
//            catlog.setCardContent(sb.toString());
//            entityService.update(catlog);
        }
        return 0;
    }

    @Override
    public int insertOperstepType(OperCardOperstepDto operCardOperstepDto) {
//        try {
//            entservice.begin();// 事务开始
            List<OpercardOperstepVo> lists = operCardOperstepDto.getTabledata();

//            Where where3 = Where.create();
//            where3.eq(OpercardOperstep::getCatalogAlias, lists.get(0).getCatalogAlias());
//            where3.eq(OpercardOperstep::getOpType,3);
//            where3.eq(OpercardOperstep::getTmUsed, 1);
//            List<OpercardOperstep> opersteplists = entityService.queryList(OpercardOperstep.class, where3,
//                    Order.create().orderByAsc(OpercardOperstep::getTmSort), null);
            Where where = Where.create();
            where.eq(OpercardOperstep::getCatalogAlias,lists.get(0).getCatalogAlias());
            where.eq(OpercardOperstep::getTmUsed,1);
//           where.eq(OpercardOperstep::getOpType,3);
            Integer sort = entityService.findMaxValue(OpercardOperstep.class,OpercardOperstep::getTmSort,Integer.class,where);
            if(sort==null) {
            	sort=1;
            }
        	String operateRegex = "(&lt;|[\\[\\(])([IPM]|[IP][\\|][IP])(&gt;|[\\]\\)])";//操作标识解析			
			Pattern pOperate = Pattern.compile(operateRegex);
            for (OpercardOperstepVo opercardOperstepVo : lists) {
                OpercardOperstep opercardOperstep = ObjUtils.copyTo(opercardOperstepVo, OpercardOperstep.class);
                if(StringUtils.isNotEmpty(opercardOperstep.getOpSign()) && (opercardOperstep.getOpType()!=null && opercardOperstep.getOpType().intValue()==3)) {
                	Matcher m = pOperate.matcher(opercardOperstep.getOpSign());
                	if(m.find()){//查找到了,代表输入数据正确有效
                		String leftSign = m.group(1).replace("&lt;","<");//左操作符
      					String postSign = m.group(2);//操作岗位
      					String rightSign = m.group(3).replace("&gt;",">");;//右操作符
      					opercardOperstep.setOpSignType(leftSign+rightSign);
      					opercardOperstep.setOpSignPost(postSign);
      				}
            	}else {
            		opercardOperstep.setOpSign("");
            		opercardOperstep.setOpSignType("");
            		opercardOperstep.setOpSignPost("");
            	}
                boolean isNew = true;
                String operstepId = opercardOperstep.getId();
            	if(StringUtils.isNotEmpty(operstepId)) {
            		operstepId = opercardOperstep.getId();
            		isNew=false;
            	}else {
            		operstepId = TMUID.getUID();
            	}
                String[] postIdArr = {};
                String[] postNameArr = {}; 
                if(opercardOperstepVo.getOpType()!=null && opercardOperstepVo.getOpType().intValue()==3 && StringUtils.isNotEmpty(opercardOperstepVo.getPostId())) {
                	postIdArr=opercardOperstepVo.getPostId().split(",");
                	postNameArr=opercardOperstepVo.getPostName().split(",");
                }
                List<OpercardSteppost> postList = new ArrayList<OpercardSteppost>();
                for(int i=0,j=postIdArr.length;i<j;i++) {
            	   OpercardSteppost opercardSteppost = new OpercardSteppost();
            	   opercardSteppost.setId(TMUID.getUID());
                   opercardSteppost.setCardId(opercardOperstep.getCardId());
                   /** 步骤ID */
                   opercardSteppost.setStepId(operstepId);
                   opercardSteppost.setCatalogAlias(opercardOperstep.getCatalogAlias());
                   opercardSteppost.setPostName(postNameArr[i]);
                   opercardSteppost.setPostId(postIdArr[i]);
                   opercardSteppost.setTmUsed(1);
                   postList.add(opercardSteppost);
                }
                //查询当前步骤标签的格式
                String opContent = opercardOperstep.getOpContent();
                if(opercardOperstep.getOpType()!=null && opercardOperstep.getOpType().intValue()==3) {
                	opContent ="<p>"+opercardOperstep.getOpSign() + opContent+"</p>";
                }
//                opContent = matchFormat(opersteplists,opContent);
                //如果id是空则表示该数据为新增
                if (isNew) {
                    sort++;
                    if(StringUtils.isNotEmpty(postList)) {
                    	entityService.insertBatch(postList,1000);
                    }

                    //    groupId    段落id     节点id         操作卡cardId      从节点对象获取
                    opercardOperstep.setOpContent(opContent);
                    opercardOperstep.setTmSort(sort);
                    opercardOperstep.setId(operstepId);
                    opercardOperstep.setOpType(opercardOperstepVo.getOpType());
                    opercardOperstep.setTmUsed(1);
                    entityService.insert(opercardOperstep);
                } else {
                    //存储岗位信息
                    Where where2 = Where.create();
                    where2.eq(OpercardSteppost::getStepId, opercardOperstep.getId());
                    List<OpercardSteppost> opercardStepposts = entityService.queryList(OpercardSteppost.class, where2, null, null);
                    if(StringUtils.isNotEmpty(opercardStepposts)) {
                    	entityService.deleteByIdBatch(opercardStepposts,1000);
                    }
                    if(StringUtils.isNotEmpty(postList)) {
                    	entityService.insertBatch(postList,1000);
                    }                   
                    //查询当前步骤标签的格式
//                    String opContent = opercardOperstep.getOpSign() + opercardOperstep.getOpContent();
//                    opContent = matchFormat(opersteplists,opContent);
                    opercardOperstep.setOpContent(opContent);
                    entityService.update(opercardOperstep);
                }
            }

            //查询当前操作卡修改操作卡发布状态为0
            //updateCardStatus(lists.get(0).getCardId());
            infoServ.updateCardStatus(lists.get(0).getCardId());
            //查询当前节点下所有内容及步骤，拼接
            if (!ObjUtils.isEmpty(lists)) {
            	updateCardContent(lists.get(0).getCatalogAlias());
//                StringBuffer sb = new StringBuffer();
//                Where where1 = Where.create();
//                where1.eq(OpercardOperstep::getCatalogAlias, lists.get(0).getCatalogAlias());
//                where1.eq(OpercardOperstep::getTmUsed, 1);
//                List<OpercardOperstep> opersteplistNew = entityService.queryList(OpercardOperstep.class, where1,
//                        Order.create().orderByAsc(OpercardOperstep::getTmSort), null);
//                for (OpercardOperstep opercardOperstep:opersteplistNew) {
//                    sb.append(opercardOperstep.getOpContent());
//                }
//                OpercardCatalog opercardCatalog = new OpercardCatalog();
//                opercardCatalog.setId(lists.get(0).getGroupId());
//                OpercardCatalog catlog = entityService.rawQueryById(opercardCatalog);
//                catlog.setCardContent(sb.toString());
//                System.out.println(catlog.getCardContent());
//                entityService.update(catlog);
            }

//            entservice.commit();// 事务提交
//        } catch (Exception e) {
//            log.error("新增仪表数据失败", e);
//            entservice.rollback();// 事务回滚
//        }
        return 0;
    }
    /**
     * 更新操作卡预览内容
     * @category 
     * <AUTHOR> 
     * @param catalogAlias
     */
    private void updateCardContent(String catalogAlias) {
    	   Where where = Where.create();
    	   where.eq(OpercardCatalog::getCatalogAlias, catalogAlias);
           where.isNull(OpercardCatalog::getCardVerId);
           List<OpercardCatalog> cataLogList = entityService.queryList(OpercardCatalog.class, where);
           if(StringUtils.isNotEmpty(cataLogList)) {
        	   StringBuffer sb = new StringBuffer();
        	   OpercardCatalog data = cataLogList.get(0);
        	   OpercardCatalog  query = new OpercardCatalog();
        	   query.setId(data.getCatalogAlias());
        	   List<OpercardOperstep> stepList = queryOperstep(query);
        	   if(StringUtils.isNotEmpty(stepList)) {
                   for (OpercardOperstep temp:stepList) {
                       sb.append(temp.getOpContent());
                   }
        	   }
        	   data.setCardContent(sb.toString());
        	   entityService.update(data);
           }
    }
    
    private String matchFormat (List<OpercardOperstep> opersteplists,String opContent){
        String returnContent = "";
        if(ObjUtils.notEmpty(opersteplists)){
            String opContentDoc = opersteplists.get(opersteplists.size()-1).getOpContent();
            //处理页面新增的数据，并创建格式
            // 解析 HTML
            if(!opContentDoc.contains("<P>")){
                return "<p>"+opContent+"</p>";
            }

            Document doc = Jsoup.parse(opContentDoc);
            Element p = doc.selectFirst("p");

            if (p != null) {
                // 保留<p>标签原有属性
                String pClass = p.attr("class");
                String pStyle = p.attr("style");
                // 获取第一个span的属性（如果存在）
                Element firstSpan = p.selectFirst("span");
                String spanClass = firstSpan != null ? firstSpan.attr("class") : "";
                String spanStyle = firstSpan != null ? firstSpan.attr("style") : "";

                // 重建标签结构
                p.empty();
                        p.attr("class", pClass);
                        p.attr("style", pStyle);
                        p.appendElement("span");
                        p.attr("class", spanClass);
                        p.attr("style", spanStyle);
                        p.text("你好");
            }

            returnContent = doc.body().html();
        }else{
            returnContent = "<p>"+opContent+"</p>";
        }
        return returnContent;
    }

    private void updateCardStatus (String cardId){
        OpercardInfo opercardInfo = entityService.queryObjectById(OpercardInfo.class,cardId);
        opercardInfo.setCardStatus(0);
        entityService.update(opercardInfo);
    }

    @Override
    public int deleteOperstep(List<OpercardOperstep> lists) {
    	int result = 0;
        if(StringUtils.isNotEmpty(lists)) {
//    	  List<OpercardOperstep> operstepList = new ArrayList<>();
//          for (OpercardOperstep opercardOperstep : lists) {
//          	//opercardOperstep.setTmUsed(0);
//              operstepList.add(opercardOperstep);
//          }
//          entityService.updateBatch(operstepList);
          result = entityService.deleteByIdBatch(lists, 1000);
          if(result>0) {
        	updateCardContent(lists.get(0).getCatalogAlias());
        	infoServ.updateCardStatus(lists.get(0).getCardId());
          }
        }
    	
//        try {
//            entservice.begin();// 事务开始
//            List<OpercardOperstep> operstepList = new ArrayList<>();
//            for (OpercardOperstep opercardOperstep : lists) {
//            	//opercardOperstep.setTmUsed(0);
//                operstepList.add(opercardOperstep);
//            }
////            entityService.updateBatch(operstepList);
//            entityService.deleteByIdBatch(operstepList, 1000);
//            //查询当前节点下所有内容及步骤，拼接
//            if (!ObjUtils.isEmpty(lists)) {
//            	updateCardContent(lists.get(0).getCatalogAlias());
////                StringBuffer sb = new StringBuffer();
////                Where where = Where.create();
////                where.eq(OpercardOperstep::getCatalogAlias, lists.get(0).getCatalogAlias());
////                where.eq(OpercardOperstep::getTmUsed, 1);
////                List<OpercardOperstep> opersteplists = entityService.queryList(OpercardOperstep.class, where,
////                        Order.create().orderByAsc(OpercardOperstep::getTmSort), null);
////                for (OpercardOperstep opercardOperstep:opersteplists) {
////                    sb.append(opercardOperstep.getName());
////                }
////                OpercardCatalog opercardCatalog = new OpercardCatalog();
////                opercardCatalog.setId(lists.get(0).getGroupId());
////                OpercardCatalog catlog = entityService.rawQueryById(opercardCatalog);
////                catlog.setCardContent(sb.toString());
////                entityService.update(catlog);
//            }
//
//            entservice.commit();// 事务提交
//        } catch (Exception e) {
//            log.error("新增仪表数据失败", e);
//            entservice.rollback();// 事务回滚
//        }
        
        return result;
    }








    private String getRowTitle(String rowstr) {
        //去除格式，获取文字内容，并提取
        String title = rowstr.replace("\r", "").replace("\n", "");
        title = title.replaceAll("</?p[^>]*>", "").replaceAll("<br[^>]*>", "").replaceAll("<BR[^>]*>", "");
        //表格特殊处理
        if (title.startsWith("<table")) {//取第一行内容
            List<String> trlist = Coms.getBl(title, "<tr[^>]*>.*?</tr>");
            if (trlist.size() > 0) {
                title = trlist.get(0).replaceAll("</?[^>]*>", "");
            } else {
                title = "";
            }
        } else {
            title = title.replaceAll("</?[^>]*>", "");
        }
        if (StringUtils.isEmpty(title)) {
            title = "节点";
        }
        return title;
    }
}
