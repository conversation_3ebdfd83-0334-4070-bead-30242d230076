package com.yunhesoft.joblist.operCard.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExec;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecFixed;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecInsdetails;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecInstrument;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecJump;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecOperstep;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecStepdetails;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecSteppost;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecStepread;
import com.yunhesoft.joblist.operCard.service.IOpercardExecDbService;
import com.yunhesoft.joblist.operCard.service.IOpercardExecService;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.impl.EntityServiceImpl;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
/**
 * 操作卡执行服务类
  * @Description:
  * <AUTHOR>
  * @date 2025年3月11日
 */
@Service
public class OpercardExecDbServiceImpl implements IOpercardExecDbService {
	
	@Autowired
	private EntityService entityService;
	@Autowired
	private EntityServiceImpl entservice;
	@Autowired
	private IOpercardExecService operServ;
    @Autowired
    private RedisUtil redis;
	private String readisKey_base="OPERCARD:EXEC:";//缓存key
	private String readisKey_main="MAIN:";//主信息
	private String readisKey_step="STEP:";//步骤信息
	private String readisKey_stepdetails="STEPDETAILS:";//步骤确认信息
	private String readisKey_post="POST:";//岗位信息
	private String readisKey_instrument="INSTRUMENT:";//仪表信息
	private String readisKey_insdetails="INSDETAILS:";//仪表值
	private String readisKey_stepread="STEPREAD:";//提示卡阅读记录
	private String readisKey_jump="JUMP:";//跳步审核信息
	private long timeout=86400l;//缓存过期时间（秒）60*60*24=1天
	/**
	 * 获取缓存key
	 * @category 
	 * <AUTHOR> 
	 * @param key 缓存key
	 * @param execId 实例ID
	 * @return
	 */
    private String getReadisKey(String key,String execId) {
        if (MultiTenantUtils.enalbe()) {// 多租户模式
            return readisKey_base + key + MultiTenantUtils.getTenantId()+":"+execId;
        } else {
            return readisKey_base + key+execId;
        }
    }
	/**
	 * 获取操作卡固定化状态
	 * @category 
	 * <AUTHOR> 
	 * @param cardId 操作卡ID
	 * @param catalogAlias 目标别名
	 * @param cardVerId 操作卡版本
	 * @return OpercardExecFixed
	 */
	@Override
	public List<OpercardExecFixed> getOpercardExecFixed(String cardId, String catalogAlias, String cardVerId) {
		// TODO Auto-generated method stub
		List<OpercardExecFixed> result = new ArrayList<OpercardExecFixed>();
		if(StringUtils.isNotEmpty(cardId) && StringUtils.isNotEmpty(catalogAlias) && StringUtils.isNotEmpty(cardVerId)) {//参数有效
			Where where = Where.create();
			where.eq(OpercardExecFixed::getCardId, cardId);
			where.eq(OpercardExecFixed::getCatalogAlias, catalogAlias);
			where.eq(OpercardExecFixed::getCardVerId, cardVerId);
			Order order = Order.create();
			order.orderByAsc(OpercardExecFixed::getCreateTime);
			List<OpercardExecFixed> queryList = entityService.queryList(OpercardExecFixed.class, where,order);
			if (StringUtils.isNotEmpty(queryList)) {// 不为空
				result = queryList;
			}
		}	
		return result;
	}
	/**
	 * 操作卡实例化
	 * @category 操作卡实例化
	 * <AUTHOR> 
	 * @param exec 主信息
	 * @param stepList 步骤
	 * @param postList 岗位
	 * @param instrumentList 仪表
	 * @param updateStepList 要更新的步骤信息
	 * @param clearReadis 是否需要清理缓存（仅步骤重复时需要清理）
	 * @return boolean true成功 false失败
	 */
	@Override
	public boolean opercardInstance(OpercardExec exec, List<OpercardExecOperstep> stepList,
			List<OpercardExecSteppost> postList, List<OpercardExecInstrument> instrumentList,List<OpercardExecOperstep> updateStepList,Boolean clearReadis) {
		// TODO Auto-generated method stub
		boolean result = false;
		if(StringUtils.isNotEmpty(stepList)) {//有主信息和步骤
			boolean hasMain = (exec==null?false:true);//有主信息
			boolean hasPost = StringUtils.isNotEmpty(postList);//有岗位添加
			boolean hasInstrument = StringUtils.isNotEmpty(instrumentList);//有仪表
			try {
				entservice.begin();// 事务开始
				// 插入信息
				if(hasMain) {
					entservice.insert(exec);
				}
				entservice.insertBatch(stepList,1000);
				if(StringUtils.isNotEmpty(updateStepList)) {
					entservice.updateBatch(updateStepList, 1000);
				}
				if (hasPost) {
					entservice.insertBatch(postList,1000);
				}
				if (hasInstrument) {
					entservice.insertBatch(instrumentList,1000);
				}
				entservice.commit();// 事务提交
				result = true;
			} catch (Exception e) {
				entservice.rollback();// 事务回滚
			}finally {	
				//事务没有专门的关闭函数
			}
			if(result && clearReadis!=null && clearReadis.booleanValue()) {//需要清理缓存
				String execId=stepList.get(0).getExecId();
				if(StringUtils.isNotEmpty(execId)) {
					if(hasMain) {
						redis.delete(getReadisKey(this.readisKey_main,execId));
					}
					redis.delete(getReadisKey(this.readisKey_step,execId));
					if(hasPost) {
						redis.delete(getReadisKey(this.readisKey_post,execId));
					}
					if(hasInstrument) {
						redis.delete(getReadisKey(this.readisKey_instrument,execId));
					}
				}
			}
		}
		return result;
	}
	/**
	 * 获取操作卡实例主信息
	 * @category 获取操作卡实例主信息
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @return
	 */
	@Override
	public OpercardExec getOpercardExec(String execId,boolean readReadis) {
		// TODO Auto-generated method stub
		OpercardExec result = null;
		if(StringUtils.isNotEmpty(execId)) {//参数有效
			String readisKey = getReadisKey(this.readisKey_main,execId);
			if (readReadis && redis.hasKey(readisKey)) {
				result = redis.getClassObject(OpercardExec.class, readisKey);
			}else {			
				result = entityService.queryObjectById(OpercardExec.class, execId);
				if(readReadis && result!=null) {//不从缓存中取时，也不添加到缓存
					redis.setObject(readisKey, result, timeout);//添加到readis
				}
			}
		}
		return result;
	}
	/**
	 * 获取操作卡实例步骤信息
	 * @category 获取操作卡实例步骤信息
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @return
	 */
	@Override
	public List<OpercardExecOperstep> getStepList(String execId,boolean readReadis) {
		// TODO Auto-generated method stub
		List<OpercardExecOperstep> result = new ArrayList<OpercardExecOperstep>();
		if(StringUtils.isNotEmpty(execId)) {//参数有效
			String readisKey = getReadisKey(this.readisKey_step,execId);
			if (readReadis && redis.hasKey(readisKey)) {
				result = redis.getClassList(OpercardExecOperstep.class, readisKey);
			}else {
				Where where = Where.create();
				where.eq(OpercardExecOperstep::getExecId, execId);
				Order order = Order.create();
				order.orderByAsc(OpercardExecOperstep::getTmSort);
//				order.orderByAsc(OpercardExecOperstep::getRepeatSign);
//				order.orderByAsc(OpercardExecOperstep::getRepeatTime);
//				order.orderByAsc(OpercardExecOperstep::getRepeatDt);
//				order.orderByAsc(OpercardExecOperstep::getRepeatSort);
				List<OpercardExecOperstep> queryList = entityService.queryList(OpercardExecOperstep.class, where,order);
				if (StringUtils.isNotEmpty(queryList)) {// 不为空
					for(OpercardExecOperstep temp:queryList) {
						if(temp.getOpType()!=null) {
							int opType=temp.getOpType().intValue();
							if(opType==1 || opType==2) {//操作步骤和警示
								temp.setOpContent(operServ.changeImageUrl(temp.getOpContent()));
							}
							result.add(temp);
						}
					}
//					result = queryList;
				}
				if(readReadis) {//不从缓存中取时，也不添加到缓存
					redis.setList(readisKey, result);//添加到readis
					redis.expire(readisKey, timeout);//设置超时时间
				}
			}
		}	
		return result;
	}
	/**
	 * 获取操作卡实例岗位信息
	 * @category 获取操作卡实例岗位信息
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @return
	 */
	@Override
	public List<OpercardExecSteppost> getPostList(String execId) {
		// TODO Auto-generated method stub
		List<OpercardExecSteppost> result = new ArrayList<OpercardExecSteppost>();
		if(StringUtils.isNotEmpty(execId)) {//参数有效
			String readisKey = getReadisKey(this.readisKey_post,execId);
			if (redis.hasKey(readisKey)) {
				result = redis.getClassList(OpercardExecSteppost.class, readisKey);
			}else {	
				Where where = Where.create();
				where.eq(OpercardExecSteppost::getExecId, execId);
				List<OpercardExecSteppost> queryList = entityService.queryList(OpercardExecSteppost.class, where);
				if (StringUtils.isNotEmpty(queryList)) {// 不为空
					result = queryList;
				}
				redis.setList(readisKey, result);//添加到readis
				redis.expire(readisKey, timeout);//设置超时时间
			}
		}	
		return result;
	}
	/**
	 * 获取操作卡实例仪表信息
	 * @category 获取操作卡实例仪表信息
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @return
	 */
	@Override
	public List<OpercardExecInstrument> getInstrumentList(String execId) {
		// TODO Auto-generated method stub
		List<OpercardExecInstrument> result = new ArrayList<OpercardExecInstrument>();
		if(StringUtils.isNotEmpty(execId)) {//参数有效
			String readisKey = getReadisKey(this.readisKey_instrument,execId);
			if (redis.hasKey(readisKey)) {
				result = redis.getClassList(OpercardExecInstrument.class, readisKey);
			}else {	
				Where where = Where.create();
				where.eq(OpercardExecInstrument::getExecId, execId);
				Order order = Order.create();
				order.orderByAsc(OpercardExecInstrument::getTmSort);
				List<OpercardExecInstrument> queryList = entityService.queryList(OpercardExecInstrument.class, where,order);
				if (StringUtils.isNotEmpty(queryList)) {// 不为空
					for(OpercardExecInstrument temp:queryList) {
						if("tip".equals(temp.getComType())) {
							temp.setPropertyCode(operServ.changeImageUrl(temp.getPropertyCode()));
						}
						result.add(temp);
					}
//					result = queryList;
				}
				redis.setList(readisKey, result);//添加到readis
				redis.expire(readisKey, timeout);//设置超时时间
			}
		}	
		return result;
	}
	/**
	 * 获取操作卡实例确认信息
	 * @category 获取操作卡实例确认信息
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @param readReadis 是否从缓存中读取
	 * @return
	 */
	@Override
	public List<OpercardExecStepdetails> getStepdetailsList(String execId,boolean readReadis) {
		// TODO Auto-generated method stub
		List<OpercardExecStepdetails> result = new ArrayList<OpercardExecStepdetails>();
		if(StringUtils.isNotEmpty(execId)) {//参数有效
			String readisKey = getReadisKey(this.readisKey_stepdetails,execId);
			if (readReadis && redis.hasKey(readisKey)) {
				result = redis.getClassList(OpercardExecStepdetails.class, readisKey);
			}else {
				Where where = Where.create();
				where.eq(OpercardExecStepdetails::getExecId, execId);
				Order order = Order.create();
				order.orderByAsc(OpercardExecStepdetails::getCreateTime);//这个排序为了防止并发，一旦并发录入，则排序可以保证使用最后一次录入的数据
				List<OpercardExecStepdetails> queryList = entityService.queryList(OpercardExecStepdetails.class, where, order);
				if (StringUtils.isNotEmpty(queryList)) {// 不为空
					result = queryList;
				}
				if(readReadis) {//不从缓存中取时，也不添加到缓存
					redis.setList(readisKey, result);//添加到readis
					redis.expire(readisKey, timeout);//设置超时时间
				}
			}
		}	
		return result;
	}
	/**
	 * 获取操作卡实例确认信息
	 * @category 获取操作卡实例确认信息
	 * <AUTHOR> 
	 * @param execIdList 操作卡实例ID列表
	 * @return
	 */
	@Override
	public List<OpercardExecStepdetails> getStepdetailsListByIds(List<String> execIdList) {
		// TODO Auto-generated method stub
		List<OpercardExecStepdetails> result = new ArrayList<OpercardExecStepdetails>();
		if(StringUtils.isNotEmpty(execIdList)) {//参数有效
			Where where = Where.create();
			where.in(OpercardExecStepdetails::getExecId, execIdList.toArray());
//			Order order = Order.create();
//			order.orderByAsc(OpercardExecStepdetails::getCreateTime);//这个排序为了防止并发，一旦并发录入，则排序可以保证使用最后一次录入的数据
			List<OpercardExecStepdetails> queryList = entityService.queryList(OpercardExecStepdetails.class, where);
			if (StringUtils.isNotEmpty(queryList)) {// 不为空
				result = queryList;
			}
		}	
		return result;
	}
	/**
	 * 获取操作卡实例仪表值信息
	 * @category 获取操作卡实例仪表值信息
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @param readReadis 是否从缓存中读取
	 * @return
	 */
	@Override
	public List<OpercardExecInsdetails> getInstrumentdetailsList(String execId,boolean readReadis) {
		// TODO Auto-generated method stub
		List<OpercardExecInsdetails> result = new ArrayList<OpercardExecInsdetails>();
		if(StringUtils.isNotEmpty(execId)) {//参数有效
			String readisKey = getReadisKey(this.readisKey_insdetails,execId);
			if (readReadis && redis.hasKey(readisKey)) {
				result = redis.getClassList(OpercardExecInsdetails.class, readisKey);
			}else {	
				Where where = Where.create();
				where.eq(OpercardExecInsdetails::getExecId, execId);
				Order order = Order.create();
				order.orderByAsc(OpercardExecInsdetails::getCreateTime);//这个排序为了防止并发，一旦并发录入，则排序可以保证使用最后一次录入的数据
				List<OpercardExecInsdetails> queryList = entityService.queryList(OpercardExecInsdetails.class, where, order);
				if (StringUtils.isNotEmpty(queryList)) {// 不为空
					result = queryList;
				}
				if(readReadis) {//不从缓存中取时，也不添加到缓存
					redis.setList(readisKey, result);//添加到readis
					redis.expire(readisKey, timeout);//设置超时时间
				}
			}
		}	
		return result;
	}
	/**
	 * 获取操作卡实例步骤阅读信息
	 * @category 获取操作卡实例步骤阅读信息
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @return
	 */
	@Override
	public List<OpercardExecStepread> getStepreadList(String execId) {
		// TODO Auto-generated method stub
		List<OpercardExecStepread> result = new ArrayList<OpercardExecStepread>();
		if(StringUtils.isNotEmpty(execId)) {//参数有效
			String readisKey = getReadisKey(this.readisKey_stepread,execId);
			if (redis.hasKey(readisKey)) {
				result = redis.getClassList(OpercardExecStepread.class, readisKey);
			}else {	
				Where where = Where.create();
				where.eq(OpercardExecStepread::getExecId, execId);
				List<OpercardExecStepread> queryList = entityService.queryList(OpercardExecStepread.class, where);
				if (StringUtils.isNotEmpty(queryList)) {// 不为空
					result = queryList;
				}
				redis.setList(readisKey, result);//添加到readis
				redis.expire(readisKey, timeout);//设置超时时间
			}
		}	
		return result;
	}
	/**
	 * 获取操作卡实例跳步信息
	 * @category 获取操作卡实例跳步信息
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @return
	 */
	@Override
	public List<OpercardExecJump> getJumpList(String execId) {
		// TODO Auto-generated method stub
		List<OpercardExecJump> result = new ArrayList<OpercardExecJump>();
		if(StringUtils.isNotEmpty(execId)) {//参数有效
			String readisKey = getReadisKey(this.readisKey_jump,execId);
			if (redis.hasKey(readisKey)) {
				result = redis.getClassList(OpercardExecJump.class, readisKey);
			}else {	
				Where where = Where.create();
				where.eq(OpercardExecJump::getExecId, execId);
				where.eq(OpercardExecJump::getTmUsed, 1);
				List<OpercardExecJump> queryList = entityService.queryList(OpercardExecJump.class, where);
				if (StringUtils.isNotEmpty(queryList)) {// 不为空
					result = queryList;
				}
				redis.setList(readisKey, result);//添加到readis
				redis.expire(readisKey, timeout);//设置超时时间
			}
		}	
		return result;
	}

	/**
	 * 保存步骤确认数据
	 * @category 保持步骤确认数据
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @param addList 添加记录列表
	 * @param updateList 更新记录列表
	 * @return
	 */
	@Override
	public boolean saveStepdetails(String execId, List<OpercardExecStepdetails> addList,
			List<OpercardExecStepdetails> updateList) {
		// TODO Auto-generated method stub
		boolean result = false;
		boolean hasAddData = StringUtils.isNotEmpty(addList);
		boolean hasUpdateData = StringUtils.isNotEmpty(updateList);
		if(hasAddData || hasUpdateData) {//有要保存的数据
			try {
				entservice.begin();// 事务开始
				// 插入信息
				if (hasAddData) {
					entservice.insertBatch(addList,1000);
				}
				if (hasUpdateData) {
					entservice.updateBatch(updateList, 1000);
				}
				entservice.commit();// 事务提交
				result = true;
			} catch (Exception e) {
				entservice.rollback();// 事务回滚
			}finally {	
				//事务没有专门的关闭函数
			}
			if(result && StringUtils.isNotEmpty(execId)) {//保存成功，清理缓存
				redis.delete(getReadisKey(this.readisKey_stepdetails,execId));
			}
		}
		return result;
	}
	/**
	 * 保存步骤仪表数据
	 * @category 保存步骤仪表数据
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @param addList 添加记录列表
	 * @param updateList 更新记录列表
	 * @return
	 */
	@Override
	public boolean saveInsdetails(String execId, List<OpercardExecInsdetails> addList,
			List<OpercardExecInsdetails> updateList) {
		// TODO Auto-generated method stub
		boolean result = false;
		boolean hasAddData = StringUtils.isNotEmpty(addList);
		boolean hasUpdateData = StringUtils.isNotEmpty(updateList);
		if(hasAddData || hasUpdateData) {//有要保存的数据
			try {
				entservice.begin();// 事务开始
				// 插入信息
				if (hasAddData) {
					entservice.insertBatch(addList,1000);
				}
				if (hasUpdateData) {
					entservice.updateBatch(updateList, 1000);
				}
				entservice.commit();// 事务提交
				result = true;
			} catch (Exception e) {
				entservice.rollback();// 事务回滚
			}finally {	
				//事务没有专门的关闭函数
			}
			if(result && StringUtils.isNotEmpty(execId)) {//保存成功，清理缓存
				redis.delete(getReadisKey(this.readisKey_insdetails,execId));
			}
		}
		return result;
	}
	/**
	 * 保存提示卡阅读信息
	 * @category 保存提示卡阅读信息
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @param addList 添加记录列表
	 * @return
	 */
	@Override
	public boolean saveStepread(String execId, List<OpercardExecStepread> addList) {
		// TODO Auto-generated method stub
		boolean result = false;
		if(StringUtils.isNotEmpty(addList)) {
			int saveResult = entityService.insertBatch(addList, 1000);
			if (saveResult > 0) {
				result = true;
				if(StringUtils.isNotEmpty(execId)) {//保存成功，清理缓存
					redis.delete(getReadisKey(this.readisKey_stepread,execId));
				}
			}
		}
		return result;
	}
	/**
	 * 保存跳步申请信息
	 * @category 
	 * <AUTHOR> 
	 * @param execId 操作卡实例ID
	 * @param dataList 数据列表
	 * @param isUpdate true更新 false新建
	 * @return
	 */
	@Override
	public boolean saveJump(String execId, List<OpercardExecJump> dataList, boolean isUpdate) {
		// TODO Auto-generated method stub
		boolean result = false;
		if(StringUtils.isNotEmpty(dataList)) {
			int saveResult = 0;
			if(isUpdate) {//更新
				saveResult = entityService.updateBatch(dataList, 1000);
			}else {//插入
				saveResult = entityService.insertBatch(dataList, 1000);
			}
			if (saveResult > 0) {
				result = true;
				if(StringUtils.isNotEmpty(execId)) {//保存成功，清理缓存
					redis.delete(getReadisKey(this.readisKey_jump,execId));
				}
			}
		}
		return result;
	}
	/**
	 * 获取操作卡实例主信息
	 * @category 获取操作卡实例主信息
	 * <AUTHOR> 
	 * @param OpercardExec 操作卡实例
	 * @return
	 */
	@Override
	public boolean saveOpercardExec(OpercardExec data) {
		// TODO Auto-generated method stub
		boolean result = false;
		if(data!=null) {
			int saveResult = entityService.update(data);//更新
			if (saveResult > 0) {
				result = true;
				redis.delete(getReadisKey(this.readisKey_main,data.getId()));
			}
		}
		return result;
	}
	/**
	 * 查询操作卡执行信息
	 * @category 查询操作卡执行信息
	 * <AUTHOR> 
	 * @param orgCode 机构代码
	 * @param cardId 操作卡ID
	 * @param catalogAlias 操作卡目录别名
	 * @param startDt 开始时间
	 * @param endDt 截止时间
	 * @return List<OpercardExec>
	 */
	@Override
	public List<OpercardExec> queryOperCardExecData(String orgCode, String cardId, String catalogAlias, String startDt,
			String endDt, Pagination<?> page) {
		// TODO Auto-generated method stub
		List<OpercardExec> result = new ArrayList<OpercardExec>();
		if(StringUtils.isNotEmpty(orgCode) && startDt!=null && startDt.length()>=10 && endDt!=null && endDt.length()>=10) {//参数有效
			Where where = Where.create();
			where.eq(OpercardExec::getOrgCode, orgCode);
			if(StringUtils.isNotEmpty(cardId) && !"root".equals(cardId)){
				where.eq(OpercardExec::getCardId, cardId);
			}
			if(StringUtils.isNotEmpty(catalogAlias)){
				where.eq(OpercardExec::getCatalogAlias, catalogAlias);
			}
			Date sdt = DateTimeUtils.parseDateTime(startDt.substring(0,10)+" 00:00:00");
			Date edt = DateTimeUtils.parseDateTime(endDt.substring(0,10)+" 23:59:59");
			where.and();
			where.lb();
				where.between(OpercardExec::getStartDt, sdt, edt);
				where.or();
				where.between(OpercardExec::getEndDt, sdt, edt);
				where.or();
				where.lb();
					where.lt(OpercardExec::getStartDt, sdt);
					where.gt(OpercardExec::getEndDt, edt);
				where.rb();
			where.rb();
			Order order = Order.create();
			order.orderByDesc(OpercardExec::getStartDt);
			order.orderByAsc(OpercardExec::getCardId);
			order.orderByAsc(OpercardExec::getCatalogAlias);
			List<OpercardExec> queryList = entityService.queryData(OpercardExec.class, where,order,page);
			if (StringUtils.isNotEmpty(queryList)) {// 不为空
				result = queryList;
			}
		}	
		return result;
	}
	/**
	 * 获取操作卡人员待办跳步信息
	 * @category 
	 * <AUTHOR> 
	 * @param userId 人员ID
	 * @return
	 */
	@Override
	public List<OpercardExecJump> getAuditJumpList(String userId) {
		// TODO Auto-generated method stub
		List<OpercardExecJump> result = new ArrayList<OpercardExecJump>();
		if(StringUtils.isNotEmpty(userId)){
			Where where = Where.create();
			where.eq(OpercardExecJump::getAuditPersonId, userId);//人员ID
			where.eq(OpercardExecJump::getAuditStatus, 2);//待审核
			where.eq(OpercardExecJump::getTmUsed, 1);//使用中
			Order order = Order.create();
			order.orderByDesc(OpercardExecJump::getAppDt);//申请时间
			List<OpercardExecJump> queryList = entityService.queryList(OpercardExecJump.class, where,order);
			if (StringUtils.isNotEmpty(queryList)) {// 不为空
				result = queryList;
			}
		}
		return result;
	}
	/**
	 * 获取操作卡人员待办信息
	 * @category 
	 * <AUTHOR> 
	 * @param userId 人员ID
	 * @return
	 */
	@Override
	public List<OpercardExec> getExecAuditToDoList(List<String> execIdList) {
		// TODO Auto-generated method stub
		List<OpercardExec> result = new ArrayList<OpercardExec>();
		if(StringUtils.isNotEmpty(execIdList)){
			Where where = Where.create();
			where.in(OpercardExec::getId, execIdList.toArray());//人员ID
			where.eq(OpercardExec::getExecStatus, 1);//执行中
			List<OpercardExec> queryList = entityService.queryList(OpercardExec.class, where);
			if (StringUtils.isNotEmpty(queryList)) {// 不为空
				result = queryList;
			}
		}
		return result;
	}
	/**
	 * 获取操作卡固定化状态
	 * @category 获取操作卡固定化状态
	 * <AUTHOR> 
	 * @param cardId 操作卡ID
	 * @param catalogAlias 目标别名
	 * @param cardVerId 操作卡版本
	 * @return OpercardExecFixed
	 */
	@Override
	public boolean saveOpercardExecFixed(OpercardExecFixed fixed) {
		// TODO Auto-generated method stub
		boolean result = false;
		if(fixed!=null) {
			int res = 0;
			if(StringUtils.isNotEmpty(fixed.getId())){
				res = entityService.update(fixed);
			}else {
				fixed.setId(TMUID.getUID());
				res = entityService.insert(fixed);
			}
			if(res>0) {
				result = true;
			}
		}
		return result;
	}

}
