package com.yunhesoft.joblist.operCard.service.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Random;
import java.util.stream.Collectors;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.ElementList;
import com.itextpdf.tool.xml.XMLWorker;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import com.itextpdf.tool.xml.css.CssFile;
import com.itextpdf.tool.xml.css.StyleAttrCSSResolver;
import com.itextpdf.tool.xml.html.CssAppliers;
import com.itextpdf.tool.xml.html.CssAppliersImpl;
import com.itextpdf.tool.xml.html.Tags;
import com.itextpdf.tool.xml.parser.XMLParser;
import com.itextpdf.tool.xml.pipeline.css.CSSResolver;
import com.itextpdf.tool.xml.pipeline.css.CssResolverPipeline;
import com.itextpdf.tool.xml.pipeline.end.ElementHandlerPipeline;
import com.itextpdf.tool.xml.pipeline.html.HtmlPipeline;
import com.itextpdf.tool.xml.pipeline.html.HtmlPipelineContext;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.PropertyUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.entity.vo.JoblistActivityExampleVo;
import com.yunhesoft.joblist.operCard.entity.dto.OperCardExecDto;
import com.yunhesoft.joblist.operCard.entity.dto.OperCardExecInitDto;
import com.yunhesoft.joblist.operCard.entity.dto.OperCardExecQueryDto;
import com.yunhesoft.joblist.operCard.entity.po.OpercardClassify;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExec;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecFixed;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecInsdetails;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecInstrument;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecJump;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecOperstep;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecStepdetails;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecSteppost;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecStepread;
import com.yunhesoft.joblist.operCard.entity.po.OpercardInfo;
import com.yunhesoft.joblist.operCard.entity.po.OpercardInstrument;
import com.yunhesoft.joblist.operCard.entity.po.OpercardOperstep;
import com.yunhesoft.joblist.operCard.entity.po.OpercardSteppost;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardExecDataVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardExecInstrumentVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardExecStepVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardExecVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardInitResultVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardInstanceVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardModelVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardUserVo;
import com.yunhesoft.joblist.operCard.service.IOpercardExecDbService;
import com.yunhesoft.joblist.operCard.service.IOpercardExecService;
import com.yunhesoft.joblist.operCard.service.IParseWordClassifyService;
import com.yunhesoft.joblist.operCard.service.IParseWordInfoService;
import com.yunhesoft.joblist.operCard.service.IParseWordInstrumentService;
import com.yunhesoft.joblist.operCard.utils.MFontProvider;
import com.yunhesoft.joblist.operCard.utils.OperCardExportModel;
import com.yunhesoft.joblist.operCard.utils.OperCardExportModelFactory;
import com.yunhesoft.joblist.service.IJoblistInputService;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataInputCallbackDto;
import com.yunhesoft.joblist.workInstruction.service.impl.WIDataInputCallbackService;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgPostService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.post.service.ISysPostService;
import com.yunhesoft.system.role.utils.IPermUtils;
import com.yunhesoft.system.tds.service.IRtdbService;
import com.yunhesoft.system.tools.sysConfig.service.impl.SysConfigServiceImpl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import lombok.extern.log4j.Log4j2;
@Service
@Log4j2
public class OpercardExecServiceImpl implements IOpercardExecService {
	
	@Autowired
	private IOpercardExecDbService dbService;
	@Autowired
	private SysConfigServiceImpl configService;// 系统配置信息
	@Autowired
	private IPermUtils perUtils;	// 权限
    @Autowired
    private IRtdbService rtdbSrv; //实时数据
    @Autowired
    private WIDataInputCallbackService widService;//操作指令
    @Autowired
    private ISysPostService postService;//岗位
    @Autowired
    private ISysEmployeeOrgPostService userPostService;//人员岗位
	@Autowired
	private ISysEmployeeInfoService employeeService;// 人员服务
    @Autowired
    private IJoblistInputService joblistInputService;
    @Autowired
    private IParseWordInstrumentService parseWord;
    @Autowired
    private ISysOrgService orgServ;
    @Autowired
    private ISysEmployeeInfoService empServ;
    @Autowired
    private IParseWordClassifyService classServ;
    @Autowired
    private IParseWordInfoService infoServ;

    private int rdbDefaultScale = 6;// 实时数据默认小数位
	
    private boolean testMode=false;//测试模式，true：建操作卡时建立测试卡
    
    private String TM4BaseUrl = null;//前台能访问的链接地址，用于替换图片链接
    
    private String cardIdSuffix = "ONLY";//唯一卡的id后缀
    
    public static Map<String, Font> pdfFontMap = new HashMap<>();
    
    public BaseFont pdfBaseFont = null;
    
    private final ResourceLoader resourceLoader;
    
    private MFontProvider mp = null;//PDF字体管理

	@Value("${opercard.export.mode:default}")
	private String opercardExportMode;//操作卡的导出模式
	
    public OpercardExecServiceImpl(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }
	/**
	 * 初始化操作卡实例
	 * @category 初始化操作卡实例
	 * <AUTHOR> 
	 * @param parm OperCardExecDto
	 * @return OperCardInitResultVo 操作卡实例创建结果 OperCardInitResultVo.execId操作卡实例ID
	 */
	@Override
	public OperCardInitResultVo initOpercard(OperCardExecInitDto parm) {
		// TODO Auto-generated method stub
		OperCardInitResultVo reuslt = new OperCardInitResultVo();
		boolean resResult = false; //创建结果 true成功 false失败
		String resExecId ="";//操作卡实例ID
		String errorInfo ="";//失败原因
		int fixedStatus=0;//固化状态
		if(parm!=null && StringUtils.isNotEmpty(parm.getCardId())) {
			boolean hasCard = false;
			String execId = null;
			if(parm.getOnlyOneCardByWorkId()!=null && parm.getOnlyOneCardByWorkId().intValue()==1 && StringUtils.isNotEmpty(parm.getWorkId())) {//一个wordId只能创建一个实例
				execId=parm.getWorkId()+this.cardIdSuffix;
				OpercardExec exec = dbService.getOpercardExec(execId, true);
				if(exec!=null) {
					hasCard = true;
					fixedStatus=exec.getFixedStatus()==null?0:exec.getFixedStatus().intValue();
				}
			}
			if(hasCard) {//有实例，直接返回实例
				resResult=true;//实例化成功
				resExecId=execId;//实例ID
			}else {//无实例，创建实例
				OperCardModelVo cardVo = getCardModel(parm.getCardId(),parm.getCatalogAlias());//获取操作卡发布数据
				if(cardVo!=null && StringUtils.isNotEmpty(cardVo.getCardVerId())){//获取到了发布版本			
					if(StringUtils.isNotEmpty(cardVo.getOpStep())) {//操作卡有步骤
						OpercardInfo info = infoServ.getOpercardInfo(parm.getCardId());
						if(info!=null) {
							Date nowDt  = DateTimeUtils.getNowDate();//当前时间
							fixedStatus=this.isFixed(cardVo.getCardId(), cardVo.getCatalogAlias(), cardVo.getCardVerId(),info);
							int execType = 2;//执行方式 1串行执行 2并行执行 默认为并行
							int inputType = 2;//录入方式 1逐步确认 2关键点确认
							int pCount = 0;//外操执行次数 
							int iCount = 0;//内操执行次数
							int mCount = 0;//班长执行次数
//							不再获取传入的参数
//							if(parm.getExecType()!=null) {
//								if(parm.getExecType().intValue()>1) {
//									execType=parm.getExecType().intValue();
//								}
//							}
							//直接走卡上的设置
							if(info.getExecType()!=null) {
								execType=info.getExecType().intValue();
							}
							if(info.getInputType()!=null) {
								inputType=info.getInputType().intValue();
							}
							boolean notOp = true;//是否有可操作步骤
							if(execId==null) {//如果没有指定的ID，创建一个新ID
								execId =TMUID.getUID();//实例ID
							}
							OpercardExec exec = new OpercardExec();
							exec.setId(execId);
							exec.setCardId(cardVo.getCardId());// 操作卡id
							exec.setCardName(cardVo.getCardName());//操作卡名称
							exec.setCatalogAlias(cardVo.getCatalogAlias());//操作卡目录别名
							exec.setCatalogName(cardVo.getCatalogName());//目录名称
							exec.setCardVerId(cardVo.getCardVerId());//操作卡版本ID
							exec.setOrgCode(cardVo.getOrgCode());//机构代码
							exec.setWorkId(parm.getWorkId());//指令/活动id
							exec.setWorkType(parm.getWorkType());//工作类型 1=指令，2=活动
							exec.setWorkContent(this.subStringByte(parm.getWorkContent(), 4000));//工作内容
							exec.setExecType(execType);//执行方式 1串行执行 2并行执行
							exec.setInputType(inputType);//录入方式 1逐步确认 2关键点确认
							exec.setFixedStatus(fixedStatus);//固化状态 //0未固化 1已固化	
							exec.setExecStatus(1);//执行状态 -1废止 0未开始 1执行中 2已完成	 这里给1，加快查询速度否则执行时要查0或1 (如果已固化，则直接打上完成标识)
							exec.setStartDt(null);//开始时间
							exec.setEndDt(null);//截止时间
							exec.setPCount(0);//初始化统计值
							exec.setICount(0);//初始化统计值
							exec.setMCount(0);//初始化统计值
							List<OpercardExecOperstep> stepList = new ArrayList<OpercardExecOperstep>();//步骤
							List<OpercardExecSteppost> postList = new ArrayList<OpercardExecSteppost>();//岗位
							List<OpercardExecInstrument> instrumentList = new ArrayList<OpercardExecInstrument>();//仪表	
							HashMap<String,String> execStepIdMap = new HashMap<String,String>();//步骤实例ID对照表
							for(OpercardOperstep temp:cardVo.getOpStep()) {
								OpercardExecOperstep bean = new OpercardExecOperstep();
								bean.setId(TMUID.getUID());//ID
								bean.setExecId(execId);//实例ID
								bean.setStepId(temp.getId());//步骤ID
								bean.setName(temp.getName());//步骤名称
								bean.setTmSort(temp.getTmSort());//排序
								bean.setMemo(temp.getMemo());//注释
								bean.setOpType(temp.getOpType());//步骤类型 //1：文本；2：警示；3：操作步骤
								bean.setOpContent(temp.getOpContent());//操作内容
								bean.setOpSign(temp.getOpSign());//操作标识 完整的标识 [P] [I] (M)
								bean.setOpSignType(temp.getOpSignType());//操作标识类型 []:操作 ():确认 <>:安全
							    bean.setOpSignPost(temp.getOpSignPost());//操作标识岗位 I:内操 P:外操 M班长
							    bean.setRequiredStep(temp.getRequiredStep());//必填步骤 1：必填；0：非必填 默认1
							    bean.setGroupId(temp.getGroupId());//段落ID
							    bean.setConfirmType(temp.getConfirmType());//确认方式 1手动确认 2身份证 3人脸识别
							    bean.setRepeatSign(0);//重复记录标识  0：不是 1：是
							    bean.setRepeatTime(0);//重复记录的次数，每次重复加1，多次重复累次加1
							    bean.setRepeatSort(0);//存储被复制记录的排序值
							    stepList.add(bean);
							    execStepIdMap.put(bean.getStepId(), bean.getId());//记录步骤实例ID
							    //if(notOp) {//没有操作步骤，需要对步骤进行判断
						    	if(bean.getOpType()!=null && bean.getOpType().intValue()==3) {//步骤类型 //1：文本；2：警示；3：操作步骤
						    		notOp=false;//有操作步骤
						    		if(bean.getOpSignPost()!=null) {//统计操作步骤的岗位数据
										if(bean.getOpSignPost().indexOf("P")>=0) {
											pCount++;
										}
										if(bean.getOpSignPost().indexOf("I")>=0) {
											iCount++;
										}
										if(bean.getOpSignPost().indexOf("M")>=0) {
											mCount++;
										}
						    		}
						    	}
							    //}    
							}
							if(notOp) {//没有步骤
								errorInfo ="操作卡无可操作项目";//失败原因
							}else {

								if(fixedStatus==1) {//已固化，需要更新部分操作信息
									exec.setExecStatus(2);//执行状态 -1废止 0未开始 1执行中 2已完成	 这里给1，加快查询速度否则执行时要查0或1 (如果已固化，则直接打上完成标识)
									exec.setStartDt(nowDt);//开始时间
									exec.setEndDt(nowDt);//截止时间///还没写完，需要加统计数据
									exec.setPCount(pCount);//初始化统计值
									exec.setICount(iCount);//初始化统计值
									exec.setMCount(mCount);//初始化统计值
								}
								
								if(StringUtils.isNotEmpty(cardVo.getOpPost())) {//操作卡有岗位
									for(OpercardSteppost temp:cardVo.getOpPost()) {
										String execStepId = execStepIdMap.get(temp.getStepId());
										if(StringUtils.isNotEmpty(execStepId)) {//能对照上步骤ID
											OpercardExecSteppost bean = new OpercardExecSteppost();
											bean.setId(TMUID.getUID());//ID
											bean.setExecId(execId);//实例ID
											bean.setStepId(temp.getStepId());//步骤ID
											bean.setExecStepId(execStepId);//执行实例步骤ID
											bean.setPostId(temp.getPostId());//执行岗位
											bean.setPostName(temp.getPostName());
											bean.setMemo(temp.getMemo());// 注释
											postList.add(bean);
										}
									}
								}
								if(StringUtils.isNotEmpty(cardVo.getOpInstrument())) {//操作卡有仪表
									for(OpercardInstrument temp:cardVo.getOpInstrument()) {
										String execStepId = execStepIdMap.get(temp.getStepId());
										if(StringUtils.isNotEmpty(execStepId)) {//能对照上步骤ID
											OpercardExecInstrument bean = new OpercardExecInstrument();
											bean.setId(TMUID.getUID());//ID
											bean.setExecId(execId);//实例ID
											bean.setStepId(temp.getStepId());//步骤ID
											bean.setExecStepId(execStepId);//执行实例步骤ID
											bean.setMemo(temp.getMemo());// 注释
											bean.setTagName(temp.getTagName());//仪表名称
											bean.setTagCode(temp.getTagCode());//仪表位号
											bean.setUpLimit(temp.getUpLimit());//上限值
											bean.setLowLimit(temp.getLowLimit());//下限值
											bean.setComType(temp.getComType());//控件类型 raw：实时仪表；number：数字框；combo:下拉框；img:图片；tip：风险提示  textfield：文本框
											bean.setPropertyCode(temp.getPropertyCode());//扩展内容
											instrumentList.add(bean);
										}
									}
								}
								boolean isSave = dbService.opercardInstance(exec, stepList, postList, instrumentList,null,null);//创建实例，由于是新创建，所以不需要清缓存
								if(isSave) {
									resResult=true;//实例化成功
									resExecId=execId;//实例ID
								}
							}
						}else {
							errorInfo ="操作卡信息未找到";//失败原因
						}
					}else {
						errorInfo ="操作卡无内容";//失败原因
					}
				}else {
					errorInfo ="操作卡尚未发布";//失败原因
				}
			}
		}else {
			errorInfo ="未传入操作卡ID";//失败原因
		}
		reuslt.setResult(resResult);
		reuslt.setExecId(resExecId);
		reuslt.setFixedStatus(fixedStatus);
		reuslt.setErrorInfo(errorInfo);
		return reuslt;
	}
	/**
	 * 验证身份证
	 * @category 
	 * <AUTHOR> 
	 * @param cardId 身份证号
	 * @return String true：验证成功 其他字符：失败原因
	 */
	@Override
	public String checkIdentityCard(String cardId) {
		// TODO Auto-generated method stub
		String result = "未传入身份证号！";
		if(StringUtils.isNotEmpty(cardId)) {//有身份证
			SysUser user = SysUserHolder.getCurrentUser();
			if(user!=null) {
				if(perUtils.isAdmin(user.getId()) || perUtils.isTenantAdmin(user)) {// 超级管理员
					result = "true";
				}else {
					if(StringUtils.isNotEmpty(user.getCardno())) {
						if(cardId.trim().equals(user.getCardno().trim())){//身份证号相同，则认证通过
							result = "true";
						}else {
							result = "认证失败，身份证号不正确！";
						}
					}else {
						result = "当前人员未设置身份证信息！";
					}
				}
			}else {
				result = "未获取到登录信息！";
			}
		}
		return result;
	}
	/**
	 * 人脸识别验证
	 * @category 人脸识别验证
	 * <AUTHOR> 
	 * @return String true：验证成功 其他字符：失败原因
	 */
	@Override
	public String checkFace() {
		// TODO Auto-generated method stub
		String result = "识别失败，请重新识别！";
//		Random rand = new Random();
//		int i = rand.nextInt(10);
//		if(i<3) {//百分之30概率会返回成功
//			result = "true";
//		}
		return result;
	}
	/**
	 * 获取某张操作卡的执行岗位列表
	 * @category 获取某张操作卡的执行岗位列表
	 * <AUTHOR> 
	 * @param cardId
	 * @param catalogAlias
	 * @return List<OperCardUserVo>
	 */
	@Override
	public List<String> getOperCardPost(String cardId, String catalogAlias) {
		// TODO Auto-generated method stub
		List<String> resut = new ArrayList<String>();
		if(StringUtils.isNotEmpty(cardId) && StringUtils.isNotEmpty(catalogAlias)) {//有要判断的人员
			OperCardModelVo cardVo = getCardModel(cardId,catalogAlias);//获取操作卡数据
			if(cardVo!=null && StringUtils.isNotEmpty(cardVo.getCardVerId())){//获取到了发布版本			
				LinkedHashMap<String,OpercardSteppost> operPostMap = new LinkedHashMap<String,OpercardSteppost>();//可操作岗位
				if(StringUtils.isNotEmpty(cardVo.getOpPost())) {
					for(OpercardSteppost temp:cardVo.getOpPost()){
						if(StringUtils.isNotEmpty(temp.getPostId())) {
							if(!operPostMap.containsKey((temp.getPostId()))){
								operPostMap.put(temp.getPostId(), temp);
							}
						}
					}
					if(operPostMap.size()>0) {
						resut.addAll(operPostMap.keySet());
					}
				}
			}
		}
		return resut;
	}
	/**
	 * 执行操作卡实例
	 * @category 执行操作卡实例
	 * <AUTHOR> 
	 * @param parm OperCardExecDto
	 * @return  List<OperCardExecStepVo> 执行后操作卡各个步骤的状态
	 */
	@Override
	public OperCardExecVo execOpercard(OperCardExecDto parm) {
		// TODO Auto-generated method stub
		OperCardExecVo result = null;
		boolean execResult = false;//执行结果
		boolean returnNewData = false;//成功时返回最新操作卡数据
		boolean cardStart = false; //需要计算操作卡是否开始
		boolean cardComplete = false; //需要计算操作卡是否完成
		boolean returnSearchAll = false;//返回值是否返回全部（false为只返回操作步骤）
		String errorInfo = null;//错误信息
		if(parm!=null  && StringUtils.isNotEmpty(parm.getExecCommand()) && StringUtils.isNotEmpty(parm.getExecId())) {//参数有效
			Date nowDt  = DateTimeUtils.getNowDate();//当前时间
			//操作人员信息
			String postOrgCode = null;
			String postId = null;
			String postName = null;
			String userId = null;
			String userName = null;
			SysUser user = SysUserHolder.getCurrentUser();
			if(user!=null) {
				postOrgCode = user.getOrgId();
				postId=user.getPostId();
				postName=user.getPostName();
				userId =user.getId();
				userName =user.getRealName();
			}
			if("execPostAll".equals(parm.getExecCommand())) {//执行当前岗位全部记录
				List<OpercardExecOperstep> stepList = dbService.getStepList(parm.getExecId(),true);//获取步骤
				if(StringUtils.isNotEmpty(stepList)) {
					List<OpercardExecSteppost> postList = dbService.getPostList(parm.getExecId());//操作岗位
					HashSet<String> canEditStep = new HashSet<String>();//可编辑步骤
					boolean editAll = getEditStep(postList,canEditStep);//获取可编辑步骤
					if(editAll || canEditStep.size()>0) {//有可编辑记录
						List<OpercardExecStepdetails> stepdetailsList = dbService.getStepdetailsList(parm.getExecId(),false);//确认信息,不从缓存取，防止缓存和数据库不一致
						HashMap<String,OpercardExecStepdetails> stepdetailsMap = getStepdetailsMap(stepdetailsList);
						List<OpercardExecJump> jumpList = dbService.getJumpList(parm.getExecId());//跳步申请信息
						HashSet<String> auditSet =getAuditSet(jumpList);
						boolean canSave = true;//是否可以保存
						List<OpercardExecStepdetails> addList = new ArrayList<OpercardExecStepdetails>();
						List<OpercardExecStepdetails> updateList = new ArrayList<OpercardExecStepdetails>();
						for(OpercardExecOperstep temp:stepList) {
							if(temp.getOpType()!=null && temp.getOpType().intValue()==3) {//是操作步骤
								if(editAll || canEditStep.contains(temp.getId())) {//可执行步骤
									if(auditSet.contains(temp.getId())) {//有跳步申请未通过
										canSave =false;
										errorInfo="步骤："+temp.getOpContent()+" 正在进行跳步申请，无法确认操作卡";		
										break;
									}else {
										OpercardExecStepdetails bean = stepdetailsMap.get(temp.getId());//获取之前的确认信息
										if(bean==null) {//未操作过
											bean = new OpercardExecStepdetails();
											bean.setId(TMUID.getUID());
											bean.setExecId(temp.getExecId());//实例ID
											bean.setStepId(temp.getStepId());//步骤ID
											bean.setExecStepId(temp.getId());//执行实例步骤ID
											bean.setOpSignPost(temp.getOpSignPost());//执行岗位 I P M
											addList.add(bean);
										}else {
											if(bean.getExecStatus()==null || bean.getExecStatus().intValue()==0) {//未确认的记录
												updateList.add(bean);
											}else {
												continue;//此记录不需要再确认了
											}
										}
										bean.setExecStatus(1);//已确认
										bean.setInputDt(nowDt);//录入时间
										bean.setPostOrgCode(postOrgCode);
										bean.setPostId(postId);
										bean.setPostName(postName);
										bean.setExecPersonId(userId);
										bean.setExecPerson(userName);
									}
								}
							}
						}
						if(canSave) {
							if(addList.size()>0 || updateList.size()>0) {//有要更新的数据
								if(dbService.saveStepdetails(parm.getExecId(),addList, updateList)) {
									execResult = true;//执行结果
									returnNewData = true;//需要阅读操作卡数据
									cardStart = true; //需要计算操作卡是否开始
									cardComplete = true; //需要计算操作卡是否完成
								}else {
									errorInfo="数据保存失败！";	
								}
								
							}else {
								errorInfo="所有步骤已确认，无需重复确认！";	
							}
						}
					}else {
						errorInfo="无可确认步骤！";						
					}
				}else {
					errorInfo="未获取到操作卡步骤信息！";						
				}		
			}else if("execStep".equals(parm.getExecCommand())) {//执行步骤
				if(StringUtils.isNotEmpty(parm.getDataList())) {//有可执行数据
					List<OpercardExecStepdetails> stepdetailsList = dbService.getStepdetailsList(parm.getExecId(),false);//确认信息,不从缓存取，防止缓存和数据库不一致
					HashMap<String,OpercardExecStepdetails> stepdetailsMap = getStepdetailsMap(stepdetailsList);//步骤确认信息
					List<OpercardExecInsdetails> getInstrumentdetailsList = dbService.getInstrumentdetailsList(parm.getExecId(),false);//仪表数据,不从缓存取，防止缓存和数据库不一致
					HashMap<String,OpercardExecInsdetails> instrumentdetailsMap = getInstrumentdetailsMap(getInstrumentdetailsList);//仪表值信息
					List<OpercardExecJump> jumpList = dbService.getJumpList(parm.getExecId());//跳步申请信息
					HashSet<String> auditSet =getAuditSet(jumpList);
					boolean canSave = true;//是否可以保存
					List<OpercardExecStepdetails> addStepdetailsList = new ArrayList<OpercardExecStepdetails>();
					List<OpercardExecStepdetails> updateStepdetailsList = new ArrayList<OpercardExecStepdetails>();
					List<OpercardExecInsdetails> addInsdetailsList = new ArrayList<OpercardExecInsdetails>();
					List<OpercardExecInsdetails> updateInsdetailsList = new ArrayList<OpercardExecInsdetails>();
					for(OperCardExecStepVo temp:parm.getDataList()) {
						if(auditSet.contains(temp.getExecStepId())) {//有审核信息
							errorInfo="步骤："+temp.getOpContent()+" 正在进行跳步申请，无法确认";		
							canSave =false;
							break;
						}
						OpercardExecStepdetails bean = stepdetailsMap.get(temp.getExecStepId());//获取之前的确认信息
						if(bean==null) {//未操作过
							bean = new OpercardExecStepdetails();
							bean.setId(TMUID.getUID());
							bean.setExecId(temp.getExecId());//实例ID
							bean.setStepId(temp.getStepId());//步骤ID
							bean.setExecStepId(temp.getExecStepId());//执行实例步骤ID
							bean.setOpSignPost(temp.getOpSignPost());//执行岗位 I P M
							addStepdetailsList.add(bean);
						}else {
							updateStepdetailsList.add(bean);
						}
						bean.setExecStatus(temp.getExecStatus());//已确认
						bean.setInputDt(nowDt);//录入时间
						bean.setPostOrgCode(postOrgCode);
						bean.setPostId(postId);
						bean.setPostName(postName);
						bean.setExecPersonId(userId);
						bean.setExecPerson(userName);
						if(StringUtils.isNotEmpty(temp.getInstrumentList())){//有仪表
							for(OperCardExecInstrumentVo insTemp:temp.getInstrumentList()){//记录仪表信息
								if(insTemp.getInputType()==null || insTemp.getInputType().intValue()==2) {//是手动录入的,才进行保存
									OpercardExecInsdetails insBean = instrumentdetailsMap.get(insTemp.getInstrumentId());//获取之前的确认信息
									if(insBean==null) {//未操作过
										insBean = new OpercardExecInsdetails();
										insBean.setId(TMUID.getUID());
										insBean.setExecId(insTemp.getExecId());//实例ID
										insBean.setStepId(insTemp.getStepId());//步骤ID
										insBean.setExecStepId(insTemp.getExecStepId());//执行实例步骤ID
										insBean.setInstrumentId(insTemp.getInstrumentId());//仪表ID
										addInsdetailsList.add(insBean);
									}else {
										updateInsdetailsList.add(insBean);
									}
									insBean.setTagValue(insTemp.getTagValue());//仪表值
									insBean.setTagStatus(getTagStatus(insBean.getTagValue(),insTemp.getUpLimit(),insTemp.getLowLimit()));//仪表状态 1：超限；0:正常
									insBean.setInputType(2);//手动录入
									insBean.setInputDt(nowDt);//录入时间
									insBean.setInputPersonId(userId);//录入人id
									insBean.setInputPerson(userName);//录入人
								}
							}
						}
					}
					if(canSave) {
						if(addStepdetailsList.size()>0 || updateStepdetailsList.size()>0 || addInsdetailsList.size()>0 || updateInsdetailsList.size()>0) {//有要更新的数据
							errorInfo="";
							boolean saveStepdetails = true;//保存步骤确认信息
							if(addStepdetailsList.size()>0 || updateStepdetailsList.size()>0) {
								saveStepdetails = dbService.saveStepdetails(parm.getExecId(),addStepdetailsList, updateStepdetailsList);
								if(!saveStepdetails) {
									errorInfo+="步骤确认数据保存失败！";	
								}
							}
							boolean saveInsdetails = true;//保存仪表数据信息
							if(addInsdetailsList.size()>0 || updateInsdetailsList.size()>0) {
								saveInsdetails = dbService.saveInsdetails(parm.getExecId(),addInsdetailsList, updateInsdetailsList);
								if(!saveInsdetails) {
									errorInfo+="仪表数据保存失败！";	
								}
							}
							if(saveStepdetails && saveInsdetails) {//全保存成功了
								execResult = true;//执行结果
								returnNewData=true;//需要阅读操作卡数据
								cardStart = true; //需要计算操作卡是否开始
								cardComplete = true; //需要计算操作卡是否完成
								errorInfo = null;
							}
						}else {
							errorInfo="无可保存数据！";	
						}
					}
				}
			}else if("collectInstrument".equals(parm.getExecCommand())) {//采集仪表数据
				if(StringUtils.isNotEmpty(parm.getInstrumentList())) {//有仪表要采集数据
					List<OpercardExecInsdetails> getInstrumentdetailsList = dbService.getInstrumentdetailsList(parm.getExecId(),false);//仪表数据,不从缓存取，防止缓存和数据库不一致
					HashMap<String,OpercardExecInsdetails> instrumentdetailsMap = getInstrumentdetailsMap(getInstrumentdetailsList);//仪表值信息
					List<OpercardExecInsdetails> addInsdetailsList = new ArrayList<OpercardExecInsdetails>();
					List<OpercardExecInsdetails> updateInsdetailsList = new ArrayList<OpercardExecInsdetails>();
					List<OpercardExecInsdetails> allInsdetailsList = new ArrayList<OpercardExecInsdetails>();
					HashSet<String> tagSet = new HashSet<String>();
					for(OperCardExecInstrumentVo insTemp:parm.getInstrumentList()){//记录仪表信息
						if("raw".equals(insTemp.getComType())) {//是实时仪表
							OpercardExecInsdetails insBean = instrumentdetailsMap.get(insTemp.getInstrumentId());//获取之前的确认信息
							if(insBean==null) {//未操作过
								insBean = new OpercardExecInsdetails();
								insBean.setId(TMUID.getUID());
								insBean.setExecId(insTemp.getExecId());//实例ID
								insBean.setStepId(insTemp.getStepId());//步骤ID
								insBean.setExecStepId(insTemp.getExecStepId());//执行实例步骤ID
								insBean.setInstrumentId(insTemp.getInstrumentId());//仪表ID
								insBean.setTagName(insTemp.getTagName());//仪表名称
								insBean.setTagCode(insTemp.getTagCode());//仪表位号
								insBean.setUpLimit(insTemp.getUpLimit());//上限
								insBean.setLowLimit(insTemp.getLowLimit());//下限
								addInsdetailsList.add(insBean);
							}else {
								updateInsdetailsList.add(insBean);
							}
							allInsdetailsList.add(insBean);
							tagSet.add(insBean.getTagCode());//记录仪表，等待数采
							//insBean.setTagValue(getTagValue(insTemp.getTagCode()));//获取实时数据，给仪表值字段赋值
							//insBean.setTagStatus(getTagStatus(insBean.getTagValue(),insTemp.getUpLimit(),insTemp.getLowLimit()));//仪表状态 1：超限；0:正常
							insBean.setInputType(1);//自动采集
							insBean.setInputDt(nowDt);//录入时间
							insBean.setInputPersonId(userId);//录入人id
							insBean.setInputPerson(userName);//录入人	
						}
					}
					if(addInsdetailsList.size()>0 || updateInsdetailsList.size()>0) {//有要更新的数据
						HashMap<String,OpercardExecInsdetails> insdetailsMap = new HashMap<String,OpercardExecInsdetails>();
						List<String> tagCodes = new ArrayList<String>();
						tagCodes.addAll(tagSet);
						HashMap<String,TagData> tagValueMap = getTagValues(tagCodes);//数据采集
						for(OpercardExecInsdetails temp:allInsdetailsList){
							if(StringUtils.isNotEmpty(temp.getTagCode())) {
								TagData val = tagValueMap.get(temp.getTagCode().toUpperCase());//仪表位号全大写
								if(val!=null) {
									if(judgeDouble(val.getValue())) {//取到了返回值
										try {
											String tagValue = String.valueOf(val.getValue());
											tagValue = new BigDecimal(tagValue).setScale(this.rdbDefaultScale, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();//格式化数据：保留6位小数->去掉尾部的0->不使用科学计数法
											temp.setTagValue(tagValue);
											temp.setTagStatus(getTagStatus(temp.getTagValue(),temp.getUpLimit(),temp.getLowLimit()));//仪表状态 1：超限；0:正常
										}catch(Exception e) {
											log.error("", e);
										}
									}
									Date tagDate = val.dateTime();
									if(tagDate!=null) {
										temp.setInputDt(tagDate);//使用数采时间作为录入时间
									}
								}else {
									//下面是测试数据，后续真正测试时需要禁用掉
									//temp.setTagValue(getTagValue(temp.getTagCode()));//获取实时数据，给仪表值字段赋值
									//temp.setTagStatus(getTagStatus(temp.getTagValue(),temp.getUpLimit(),temp.getLowLimit()));//仪表状态 1：超限；0:正常
								}
								insdetailsMap.put(temp.getInstrumentId(), temp);//仪表数据map
							}
						}
						if(dbService.saveInsdetails(parm.getExecId(),addInsdetailsList, updateInsdetailsList)) {
							execResult = true;//执行结果
//							returnNewData=true;//需要阅读操作卡数据
							for(OperCardExecInstrumentVo temp:parm.getInstrumentList()){//记录仪表信息
								OpercardExecInsdetails dataTemp = insdetailsMap.get(temp.getInstrumentId());
								if(dataTemp!=null) {
									instrumentdetailToVoCopy(dataTemp,temp);//复制仪表值信息
								}
							}
							List<OperCardExecStepVo> stepList = new ArrayList<OperCardExecStepVo>();
							OperCardExecStepVo step= new OperCardExecStepVo();
							step.setInstrumentList(parm.getInstrumentList());
							stepList.add(step);
							result = new OperCardExecVo();
							result.setStepList(stepList);
							cardStart = true; //需要计算操作卡是否开始
						}else {
							errorInfo="数据保存失败！";
						}
					}else {
						errorInfo="无可采集数据！";	
					}
				}
			}else if("readCard".equals(parm.getExecCommand())) {//阅读提示卡
				if(StringUtils.isNotEmpty(userId) && StringUtils.isNotEmpty(parm.getDataList())) {//有可执行数据
					List<OpercardExecStepread> readList = dbService.getStepreadList(parm.getExecId());
					HashMap<String,HashSet<String>> stepreadMap = getStepreadMap(readList);					
					//boolean canSave = true;//是否需要保存
					List<OpercardExecStepread> addList = new ArrayList<OpercardExecStepread>();
					for(OperCardExecStepVo temp:parm.getDataList()) {
						String key = temp.getExecStepId()+"_"+temp.getGroupId();
						HashSet<String> groupSet = stepreadMap.get(key);
						if(groupSet!=null && groupSet.contains(userId)) {
							//已经阅读过，就不用再存一次了
						}else {
							OpercardExecStepread bean = new OpercardExecStepread();
							bean.setId(TMUID.getUID());
							bean.setExecId(temp.getExecId());//实例ID
							bean.setExecStepId(temp.getExecStepId());//执行实例步骤ID
							bean.setStepId(temp.getStepId());//操作卡模型步骤ID
							bean.setGroupId(temp.getGroupId());//段落ID
							bean.setReadSign(1);//阅读标识 0：未阅读 1：已阅读
							bean.setPostId(postId);//岗位
							bean.setPostName(postName);
							bean.setReadPersonId(userId);//人员
							bean.setReadPerson(userName);
							bean.setReadDt(nowDt);//阅读时间	
							addList.add(bean);
						}
					}
					if(addList.size()>0) {
						if(dbService.saveStepread(parm.getExecId(), addList)) {//保存
							execResult = true;//执行结果
//							returnNewData=true;//需要阅读操作卡数据
							cardStart = true; //需要计算操作卡是否开始
						}else {
							errorInfo="数据保存失败！";
						}
					}else {
						execResult = true;//不用保存，直接返回true	
					}
				}
			}else if("repeatStep".equals(parm.getExecCommand())) {//重复步骤
				if(StringUtils.isNotEmpty(parm.getDataList())) {//有可执行数据	
					String execId =parm.getDataList().get(0).getExecId();
					OpercardExec execData = dbService.getOpercardExec(execId,false);
					if(execData!=null && execData.getExecStatus()!=null && execData.getExecStatus().intValue()<2) {//只处理未执行完毕的
						Map<String,OperCardExecStepVo> opStepMap =  parm.getDataList().stream()
								.collect(Collectors.toMap(OperCardExecStepVo::getExecStepId, operCardExecStepVo -> operCardExecStepVo, (key1, key2) -> key2));// 将list转换为map// 重复键值时，第一个key被第二个key覆盖;					
						List<OpercardExecOperstep> opStep = dbService.getStepList(parm.getExecId(),false);//步骤
						if(StringUtils.isNotEmpty(opStep)) {
							int local = 0;//复制数据的插入位置
							List<OpercardExecSteppost> opPost = dbService.getPostList(parm.getExecId());//岗位
							Map<String,List<OpercardExecSteppost>> opPostMap =  opPost.stream().collect(Collectors.groupingBy(post->post.getExecStepId()));
							if(opPostMap==null) {
								opPostMap = new HashMap<String,List<OpercardExecSteppost>>();
							}
							List<OpercardExecInstrument> opInstrument = dbService.getInstrumentList(parm.getExecId());//仪表 
							Map<String,List<OpercardExecInstrument>> opInstrumentMap =  opInstrument.stream().collect(Collectors.groupingBy(instrument->instrument.getExecStepId()));
							if(opInstrumentMap==null) {
								opInstrumentMap = new HashMap<String,List<OpercardExecInstrument>>();
							}
							List<OpercardExecOperstep> addOperstepList = new ArrayList<OpercardExecOperstep>();
							List<OpercardExecOperstep> updateOperstepList = new ArrayList<OpercardExecOperstep>();
							List<OpercardExecSteppost> addSteppostList = new ArrayList<OpercardExecSteppost>();
							List<OpercardExecInstrument> addInstrumentList = new ArrayList<OpercardExecInstrument>();
							for(int i=0,j=opStep.size();i<j;i++) {
								OpercardExecOperstep temp = opStep.get(i);
								if(opStepMap.size()>0) {//还有要复制的步骤
									OperCardExecStepVo setpVo = opStepMap.get(temp.getId());
									if(setpVo!=null) {	
										if(temp.getTmSort()!=null) {//有排序，取排序值
											local=temp.getTmSort().intValue();//记录排序值
										}else {//无排序按位置计算
											local=i+1;//记录位置（排序默认为顺序值加一）
										}
										opStepMap.remove(temp.getId());//移除复制数据
										temp.setId(TMUID.getUID());//重新赋予ID
										int repeatTime = 0;//重复记录的排序值，默认为0，每次重复加1，多次重复累次加1
										if(temp.getRepeatTime()!=null) {
											repeatTime=temp.getRepeatTime().intValue();
										}
										temp.setRepeatSign(1);//是否为重复记录 0：不是 1：是
										temp.setRepeatTime(repeatTime+1);//重复次数加1
										temp.setRepeatSort(temp.getTmSort());//记录原排序
										temp.setTmSort(null);//排序后面计算
										temp.setRepeatPersonId(userId);//人员
										temp.setRepeatPerson(userName);
										temp.setRepeatDt(nowDt);//时间	
										temp.setRequiredStep(1);//复制的步骤都是必填项
										addOperstepList.add(temp);
										List<OpercardExecSteppost> steppostList = opPostMap.get(setpVo.getExecStepId());//获取岗位
										if(StringUtils.isNotEmpty(steppostList)) {//有岗位
											for(OpercardExecSteppost tempPost:steppostList) {
												tempPost.setId(TMUID.getUID());//重新赋予ID
												tempPost.setExecStepId(temp.getId());//执行ID
												addSteppostList.add(tempPost);
											}
										}
										List<OpercardExecInstrument> instrumentList = opInstrumentMap.get(setpVo.getExecStepId());//获取仪表
										if(StringUtils.isNotEmpty(instrumentList)) {//有仪表
											for(OpercardExecInstrument tempInstrument:instrumentList) {
												tempInstrument.setId(TMUID.getUID());//重新赋予ID
												tempInstrument.setExecStepId(temp.getId());//执行ID
												addInstrumentList.add(tempInstrument);
											}
										}
									}
								}else {
									updateOperstepList.add(temp);//后续要更新排序
								}
							}
							//计算排序值
							for(OpercardExecOperstep temp:addOperstepList) {
								temp.setTmSort(++local);//设置插入记录的排序值
							}
							for(OpercardExecOperstep temp:updateOperstepList) {
								temp.setTmSort(++local);//设置插入记录后面记录的排序值
							}
							if(addOperstepList.size()>0) {
								if(dbService.opercardInstance(null,addOperstepList, addSteppostList, addInstrumentList,updateOperstepList,true)) {//保存(需要清缓存，否则查询不到)
									execResult = true;//执行结果
									returnNewData=true;//需要阅读操作卡数据
									cardStart = true; //需要计算操作卡是否开始
									returnSearchAll=true;//重复步骤时需要返回全部信息，用于前台刷新
								}else {
									errorInfo="数据保存失败！";
								}
							}else {
								errorInfo="未找到可以复制的数据！";
							}					
						}
					}else {
						if(execData==null) {
							errorInfo="未找到操作卡实例！";
						}else {
							errorInfo="操作卡已执行完毕，无法重复步骤！";
						}					
					}
				}
			}else if("skipStep".equals(parm.getExecCommand())) {//跳过步骤
				if(StringUtils.isNotEmpty(parm.getDataList())) {//有可执行数据
					List<OpercardExecJump> jumpList = dbService.getJumpList(parm.getExecId());
					HashSet<String> auditSet = getAuditSet(jumpList);//查找审核中的记录
					List<OpercardExecJump> addJumpList = new ArrayList<OpercardExecJump>();				
					boolean canSave = true;//是否可以保存
					StringBuffer info = new StringBuffer();
					for(OperCardExecStepVo temp:parm.getDataList()) {
						if(auditSet.contains(temp.getExecStepId())) {
							info.append("步骤【"+temp.getOpContent()+"】已提交审核，无法重复提交；");
							canSave=false;
						}else {
							OpercardExecJump bean = new OpercardExecJump();
							bean.setId(TMUID.getUID());//ID
							bean.setExecId(temp.getExecId());//操作卡实例ID
							bean.setStepId(temp.getStepId());//步骤ID
							bean.setExecStepId(temp.getExecStepId());//执行ID
							bean.setAppPerson(userName);//申请人
							bean.setAppPersonId(userId);//申请人id
							bean.setAppReason(temp.getAppReason());//申请原因
							bean.setAppDt(nowDt);//申请时间
							bean.setAuditPerson(temp.getAuditPerson());//审核人
							bean.setAuditPersonId(temp.getAuditPersonId());//审核人ID
							bean.setAuditStatus(2);//审核状态 1：审核通过；0:未提交；2：审核中；-1：否决

							bean.setTmUsed(1);//是否使用 1：使用；0：不使用  
							addJumpList.add(bean);
						}
					}
					if(canSave) {
						if(dbService.saveJump(parm.getExecId(), addJumpList, false)){//保存
							execResult = true;//执行结果
//							returnNewData=true;//需要阅读操作卡数据
							cardStart = true; //需要计算操作卡是否开始
						}else {
							errorInfo="数据保存失败！";
						}
					}else {
						if(info.length()>0) {
							errorInfo = info.substring(1);
						}
					}
				}
			}else if("cancelSkipStep".equals(parm.getExecCommand())) {//撤销跳过步骤
				if(StringUtils.isNotEmpty(parm.getDataList())) {//有可执行数据
					List<OpercardExecJump> jumpList = dbService.getJumpList(parm.getExecId());//获取审核数据
					Map<String,List<OpercardExecJump>> jumpMap =  jumpList.stream().collect(Collectors.groupingBy(jump->jump.getExecStepId()));//这里防止有2个审核记录并发存在，正常就1个审核记录
					if(jumpMap==null) {
						jumpMap = new HashMap<String,List<OpercardExecJump>>();
					}
					boolean canSave = true;//是否可以保存
					List<OpercardExecJump> updateJumpList = new ArrayList<OpercardExecJump>();	
					StringBuffer info = new StringBuffer();
					for(OperCardExecStepVo temp:parm.getDataList()) {
						List<OpercardExecJump> tempList = jumpMap.get(temp.getExecStepId());
						if(StringUtils.isNotEmpty(tempList)) {//有数据
							for(OpercardExecJump tempJump:tempList) {
								tempJump.setAuditStatus(0);//撤销 1：审核通过；0:未提交；2：审核中；-1：否决
								tempJump.setTmUsed(0);//1：使用；0：不使用
								tempJump.setAuditDt(nowDt);//撤销时间
								updateJumpList.add(tempJump);
							}
						}else {
							canSave = false;//是否可以保存
							info.append("步骤【"+temp.getOpContent()+"】未找到审核信息，无法撤销；");
						}
					}
					if(canSave) {
						if(dbService.saveJump(parm.getExecId(), updateJumpList, true)){//保存
							execResult = true;//执行结果
							returnNewData=true;//需要阅读操作卡数据
						}else {
							errorInfo="数据保存失败！";
						}
					}else {
						if(info.length()>0) {
							errorInfo = info.substring(1);
						}
					}
				}
			}else if("auditSkipStep".equals(parm.getExecCommand())) {//审核跳过步骤
				if(StringUtils.isNotEmpty(parm.getDataList())) {//有可执行数据
					List<OpercardExecJump> jumpList = dbService.getJumpList(parm.getExecId());//获取审核数据
					Map<String,List<OpercardExecJump>> jumpMap =  jumpList.stream().collect(Collectors.groupingBy(jump->jump.getExecStepId()));//这里防止有2个审核记录并发存在，正常就1个审核记录
					if(jumpMap==null) {
						jumpMap = new HashMap<String,List<OpercardExecJump>>();
					}
					List<OpercardExecStepdetails> stepdetailsList = dbService.getStepdetailsList(parm.getExecId(),false);//确认信息,不从缓存取，防止缓存和数据库不一致
					HashMap<String,OpercardExecStepdetails> stepdetailsMap = getStepdetailsMap(stepdetailsList);//步骤确认信息
					boolean canSave = true;//是否可以保存
					List<OpercardExecStepdetails> addStepdetailsList = new ArrayList<OpercardExecStepdetails>();
					List<OpercardExecStepdetails> updateStepdetailsList = new ArrayList<OpercardExecStepdetails>();
					List<OpercardExecJump> updateJumpList = new ArrayList<OpercardExecJump>();	
					StringBuffer info = new StringBuffer();
					for(OperCardExecStepVo temp:parm.getDataList()) {
						List<OpercardExecJump> tempList = jumpMap.get(temp.getExecStepId());
						if(StringUtils.isNotEmpty(tempList)) {//有数据
							int auditStatus = 1;//审核状态
							if(temp.getAuditStatus()!=null && temp.getAuditStatus().intValue()==-1) {//审核否决
								auditStatus=-1;
							}
							String appPostId=postId;
							String appPostName=postName;
							String appUserId=userId;
							String appUserName=userName;
							for(OpercardExecJump tempJump:tempList) {
								tempJump.setAuditStatus(auditStatus);// 1：审核通过；0:未提交；2：审核中；-1：否决
								tempJump.setAuditPerson(userName);//审核人
								tempJump.setAuditPersonId(userId);//审核人id
								tempJump.setAuditDesc(temp.getAuditDesc());//审核描述
								tempJump.setAuditDt(nowDt);//审核时间
								appPostId=tempJump.getAppPostId();//申请人岗位
								appPostName=tempJump.getAppPostName();//申请人岗位名称
								appUserId=tempJump.getAppPersonId();//申请人ID
								appUserName=tempJump.getAppPerson();//申请人
								updateJumpList.add(tempJump);
							}
							if(auditStatus==1) {//审核通过
								OpercardExecStepdetails bean = stepdetailsMap.get(temp.getExecStepId());//获取之前的确认信息
								if(bean==null) {//未操作过
									bean = new OpercardExecStepdetails();
									bean.setId(TMUID.getUID());
									bean.setExecId(temp.getExecId());//实例ID
									bean.setStepId(temp.getStepId());//步骤ID
									bean.setExecStepId(temp.getExecStepId());//执行实例步骤ID
									bean.setOpSignPost(temp.getOpSignPost());//执行岗位 I P M
									addStepdetailsList.add(bean);
								}else {
									updateStepdetailsList.add(bean);
								}
								bean.setExecStatus(2);//跳步审核通过
								bean.setInputDt(nowDt);//录入时间
								bean.setPostOrgCode(postOrgCode);
								bean.setPostId(appPostId);
								bean.setPostName(appPostName);
								bean.setExecPersonId(appUserId);
								bean.setExecPerson(appUserName);
							}		
						}else {
							canSave = false;//是否可以保存
							info.append("步骤【"+temp.getOpContent()+"】未找到审核信息，无法审核；");
						}
					}
					if(canSave) {
						errorInfo="";
						boolean saveStepdetails = true;//保存步骤确认信息
						if(addStepdetailsList.size()>0 || updateStepdetailsList.size()>0) {
							saveStepdetails = dbService.saveStepdetails(parm.getExecId(),addStepdetailsList, updateStepdetailsList);
							if(!saveStepdetails) {
								errorInfo+="步骤确认数据保存失败！";	
							}
						}
						boolean savejump = true;//保存仪表数据信息
						if(updateJumpList.size()>0) {
							savejump = dbService.saveJump(parm.getExecId(), updateJumpList, true);
							if(!savejump) {
								errorInfo+="跳步审核数据保存失败！";	
							}
						}
						if(saveStepdetails && savejump) {//全保存成功了
							execResult = true;//执行结果
							returnNewData=true;//需要阅读操作卡数据
							cardComplete = true; //需要计算操作卡是否完成
							errorInfo = null;
						}
					}else {
						if(info.length()>0) {
							errorInfo = info.substring(1);
						}
					}
				}
			}
			if(execResult) {//执行成功
				if(returnNewData) {//需要返回最新操作卡数据（默认返回最新，如需不返回则在对应命令执行代码中加上returnNewData=false）
					OperCardInstanceVo instanceVo = getOpercardInstance(parm.getExecId(),true);//重新获取实例，返回最新实例
					if(instanceVo!=null) {
						result = createExecOpercardBean(instanceVo,true,returnSearchAll);//只返回操作步骤和和提示卡	
						if(cardComplete){//需要计算操作卡是否完成
							cardComplete(result,parm.getExecParam(),nowDt,userId,userName);//计算操作卡执行 
						}else {
							if(cardStart) {//需要开始卡
								saveExecStart(instanceVo.getInfo(),nowDt);
							}
						}
					}
				}else {
					if(cardStart) {//需要开始卡
						OpercardExec exec = dbService.getOpercardExec(parm.getExecId(),true);//主信息,缓存读
						if(exec!=null) {
							saveExecStart(exec,nowDt);
						}
					}
				}
			}
			if(result==null) {
				result = new OperCardExecVo();
			}
			result.setOperateResult(execResult);//操作结果
			result.setErrorInfo(errorInfo);//错误信息	
		}
		return result;
	}
	/**
	 * 操作卡完成操作
	 * @category 操作卡完成操作
	 * <AUTHOR> 
	 * @param dataVo 操作卡结果类
	 * @param cardStart 是否计算操作卡开始
	 * @param execParam 执行参数,由执行页面传入，操作卡执行完毕后，传给对应的功能消息时携带这个参数值
	 */
	private void cardComplete(OperCardExecVo dataVo,String execParam,Date nowDt,String userId,String userName) {
		if(dataVo!=null) {
			if(dataVo.getExecStatus()!=null && dataVo.getExecStatus().intValue()==1) {//执行中的操作卡
				if(StringUtils.isNotEmpty(dataVo.getStepList())) {
					boolean isComplete = true;
					LinkedHashMap<String,OperCardExecStepVo> userMap = new LinkedHashMap<String,OperCardExecStepVo>();
					int pCount = 0;//外操执行次数 
					int iCount = 0;//内操执行次数
					int mCount = 0;//班长执行次数
					for(OperCardExecStepVo temp:dataVo.getStepList()) {
						if(temp.getOpType()!=null && temp.getOpType().intValue()==3) {//只判断操作列 步骤类型 1：文本；2：警示；3：操作步骤
							int execStatus = 0;//执行状态 0：未确认 1：已确认； 2：跳过步骤
							if(temp.getExecStatus()!=null ) {
								execStatus = temp.getExecStatus().intValue();
							}
							if(execStatus==1 || execStatus==2) {
								//1和2都等于完成了（跳步等于不做，就是完成了）	
								//统计执行数据
								if(temp.getOpSignPost().indexOf("P")>=0) {
									pCount++;
								}
								if(temp.getOpSignPost().indexOf("I")>=0) {
									iCount++;
								}
								if(temp.getOpSignPost().indexOf("M")>=0) {
									mCount++;
								}
								if(StringUtils.isNotEmpty(temp.getExecPerson())) {//执行请人
									if(!userMap.containsKey(temp.getExecPersonId())) {
										userMap.put(temp.getExecPersonId(),temp);
									}
								}
//								if(StringUtils.isNotEmpty(temp.getAppPerson())) {//跳步申请人
//									if(!userSet.contains(temp.getAppPerson())) {
//										userSet.add(temp.getAppPerson());
//									}
//								}
							}else {
								isComplete=false;//有未完成的
								break;
							}
						}
					}
					if(isComplete) {//全部的卡操作步骤都完成了
						OperCardExecDataVo countVo = new OperCardExecDataVo();
						countVo.setPCount(pCount);//外操
						countVo.setICount(iCount);//内操
						countVo.setMCount(mCount);//班长	
						if(userMap.size()>0) {
							StringBuffer userIds = new StringBuffer();
							StringBuffer userNames = new StringBuffer();
							for(Entry<String,OperCardExecStepVo> tempUser:userMap.entrySet()) {
								userIds.append(","+tempUser.getValue().getExecPersonId());
								userNames.append(","+tempUser.getValue().getExecPerson());
							}
							countVo.setInputUserId(userIds.substring(1));//操作人ID
							countVo.setInputUserName(userNames.substring(1));//操作人							
						}
						int cardExecStatus = 2;
						if(saveExecStatus(dataVo.getExecId(),cardExecStatus,countVo,execParam,userId,userName)) {//执行完毕
							dataVo.setExecStatus(cardExecStatus);//更新完成状态
						}
					}else {
						if(StringUtils.isEmpty(dataVo.getStartDt())) {//没有开始						
							saveExecStart(dataVo.getExecId(),nowDt);
						}
					}
				}
			}
		}
	}
	/**
	 * 保存操作卡状态
	 * @category 保存操作卡状态
	 * <AUTHOR> 
	 * @param execId 实例ID
	 * @param execStatus 执行结果
	 * @param countMap 统计结果
	 * @param execParam 执行参数
	 * @return
	 */
	private boolean saveExecStatus(String execId,int execStatus,OperCardExecDataVo countVo,String execParam,String userId,String userName) {
		boolean result = false;
		if(StringUtils.isNotEmpty(execId)) {
			OpercardExec execData = dbService.getOpercardExec(execId,false);
			if(execData!=null) {
				Date nowDt = DateTimeUtils.getNowDate();
				execData.setExecStatus(execStatus);//-1废止 0未开始 1执行中 2已完成
				if(execData.getStartDt()==null) {//未开始的操作卡，立即开始
					execData.setStartDt(nowDt);//开始时间
				}
				execData.setEndDt(nowDt);//结束时间
				if(countVo!=null) {//记录统计数据
					execData.setPCount(countVo.getPCount());//外操
					execData.setICount(countVo.getICount());//内操
					execData.setMCount(countVo.getMCount());//班长
					execData.setInputUserName(subStringByte(countVo.getInputUserName(),4000));//操作人
					execData.setInputUserId(subStringByte(countVo.getInputUserId(),4000));//操作人ID
				}
				if(execData.getStartDt()!=null) {
					execData.setExecutiveTime((double)DateTimeUtils.diffDate(execData.getEndDt(), execData.getStartDt(), DateTimeUtils.MINITE));//执行持续时间
				}
				if(dbService.saveOpercardExec(execData)) {//保存成功
					result = true;
					if(execStatus==2) {//执行完毕
						//通知其他模块
						System.out.println("操作卡已完成："+execData.getId());
						int workType=0;
						if(execData.getWorkType()!=null) {//1=指令，2=活动
							workType=execData.getWorkType().intValue();
						}
						switch(workType) {
							case 1: { //指令
								WIDataInputCallbackDto dto = new WIDataInputCallbackDto();
								dto.setCardId(execData.getCardId());//操作卡id
								dto.setCatalogAlias(execData.getCatalogAlias());//操作卡目录别名
								dto.setWorkId(execData.getWorkId());//指令/活动id
								dto.setWorkType(execData.getWorkType());//工作类型 1指令，2活动
								dto.setExecType(execData.getExecType());//执行方式 1顺序执行 2同步执行
								dto.setExecId(execData.getId());//操作卡实例ID
								dto.setExecUserId(execData.getInputUserId());//操作人ID
								dto.setExecUserName(execData.getInputUserName());//操作人
								dto.setExtParams(execParam);//额外扩展参数
								dto.setCurrentUserId(userId);//操作人Id
								dto.setCurrentUserName(userName);//操作人姓名
								widService.callback(dto);//回调操作指令
								break;
							}
							case 2: { //活动
						        //更新子活动反馈、确认状态
//						        JSONArray activityArray = new JSONArray();
//						        JSONObject activityObj = new JSONObject();
//						        activityObj.put("taskId", execData.getWorkId());
//						        activityArray.add(activityObj);
						        JoblistActivityExampleVo dto = new JoblistActivityExampleVo();
						        dto.setActivityExampleId(execData.getWorkId());
						        dto.setStatusValue(2);
						        dto.setStatusValueName("已完成");
						        joblistInputService.updateJobExampleStatus(dto);
								break;
							}
						}
					}else if(execStatus==-1){//废止
						//暂无此操作
					}
				}	
			}
		}
		return result;
	}
	/**
	 * 操作卡开始
	 * @category 
	 * <AUTHOR> 
	 * @param execId
	 * @return
	 */
	private boolean saveExecStart(String execId,Date startDt) {
		boolean result = false;
		if(StringUtils.isNotEmpty(execId) && startDt!=null) {
			OpercardExec execData = dbService.getOpercardExec(execId,false);//读数据库数据，确保数据准确
			if(execData!=null) {
				if(execData.getStartDt()==null) {//未开始的操作卡，立即开始
					execData.setStartDt(startDt);//开始时间
				}
			}
			if(dbService.saveOpercardExec(execData)) {//保存成功
				result = true;
			}
		}
		return result;
	}
	/**
	 * 操作卡开始
	 * @category 
	 * <AUTHOR> 
	 * @param execId
	 * @return
	 */
	private boolean saveExecStart(OpercardExec info,Date startDt) {
		boolean result = false;
		if(info!=null) {
			if(info.getStartDt()==null && startDt!=null) {//未开始的操作卡
				OpercardExec execData = dbService.getOpercardExec(info.getId(),false);//读数据库数据，确保数据准确
				if(execData!=null) {
					if(execData.getStartDt()==null) {//未开始的操作卡，立即开始
						execData.setStartDt(startDt);//开始时间
					}
				}
				if(dbService.saveOpercardExec(execData)) {//保存成功
					result = true;
				}
			}else {
				result = true;
			}
		}
		return result;
	}
	/**
	 * 获取全部可编辑步骤
	 * @category 
	 * <AUTHOR> 
	 * @param postList 操作卡岗位
	 * @param canEditStep 可操作步骤
	 * @return boolean true时可操作全部岗位数据 false走canEditStep中的数据
	 */
	private boolean getEditStep(List<OpercardExecSteppost> postList,HashSet<String> canEditStep) {
		boolean editAll = false;//编辑全部
		SysUser user = SysUserHolder.getCurrentUser();
		if(user!=null) {
			if(perUtils.isAdmin(user.getId()) || perUtils.isTenantAdmin(user)) {// 超级管理员
				editAll=true;
			}else {
				if(StringUtils.isNotEmpty(user.getPostId()) && StringUtils.isNotEmpty(postList) && canEditStep!=null) {
					for(OpercardExecSteppost temp:postList) {
						if(StringUtils.isNotEmpty(temp.getPostId())) {
							if(user.getPostId().equals(temp.getPostId())) {//是可操作岗
								canEditStep.add(temp.getExecStepId());
							}
						}
					}
				}
			}
		}
		return editAll;
	}
	private HashMap<String,OpercardExecStepdetails> getStepdetailsMap(List<OpercardExecStepdetails> stepdetailsList){
		HashMap<String,OpercardExecStepdetails> stepdetailsMap = new HashMap<String,OpercardExecStepdetails>();//步骤确认信息
		if(StringUtils.isNotEmpty(stepdetailsList)) {
			for(OpercardExecStepdetails temp:stepdetailsList){
				stepdetailsMap.put(temp.getExecStepId(), temp);
			}
		}
		return stepdetailsMap;
	}
	private HashMap<String,OpercardExecInsdetails> getInstrumentdetailsMap(List<OpercardExecInsdetails> instrumentdetailsList){
		HashMap<String,OpercardExecInsdetails> instrumentdetailsMap = new HashMap<String,OpercardExecInsdetails>();//仪表值map
		if(StringUtils.isNotEmpty(instrumentdetailsList)) {
			for(OpercardExecInsdetails temp:instrumentdetailsList){
				instrumentdetailsMap.put(temp.getInstrumentId(), temp);
			}
		}
		return instrumentdetailsMap;
	}
	private HashSet<String> getAuditSet(List<OpercardExecJump> jumpList){
		HashSet<String> auditSet = new 	HashSet<String>();
		if(StringUtils.isNotEmpty(jumpList)) {
			for(OpercardExecJump temp:jumpList){
				if(temp.getAuditStatus()!=null && temp.getAuditStatus().intValue()==2) {//是审核中的记录	
					auditSet.add(temp.getExecStepId());
				}	
			}
		}
		return auditSet;
	}
	private HashMap<String,HashSet<String>> getStepreadMap(List<OpercardExecStepread> readList){
		HashMap<String,HashSet<String>> stepreadMap = new HashMap<String,HashSet<String>>();//阅读记录map
		if(StringUtils.isNotEmpty(readList)) {
			for(OpercardExecStepread temp:readList){
				String key = temp.getExecStepId()+"_"+temp.getGroupId();
				HashSet<String> groupSet = stepreadMap.get(key);
				if(groupSet==null) {
					groupSet = new HashSet<String>();
					stepreadMap.put(key, groupSet);
				}
				groupSet.add(temp.getReadPersonId());
				
			}
		}
		return stepreadMap;
	}
	
	/**
	 * 判断仪表是否超限
	 * @category 判断仪表是否超限
	 * <AUTHOR> 
	 * @param tagValue 仪表值
	 * @param upLimit 上限
	 * @param lowLimit 下限
	 * @return
	 */
	private int getTagStatus(String tagValueStr,Double upLimit,Double lowLimit) {
		int tagStatus = 0;//仪表状态 1：超限；0:正常		
		if(upLimit!=null || lowLimit!=null) {
			if(judgeDouble(tagValueStr)) {//输入的仪表值是数值
				try {
					double tagValue = Double.parseDouble(tagValueStr);
					if(upLimit!=null) {//有上限
						if(tagValue>upLimit.doubleValue()) {//超上限
							tagStatus=1;//超限
						}
					}
					if(lowLimit!=null) {//有下限
						if(tagValue<lowLimit.doubleValue()) {//超下限
							tagStatus=1;//超限
						}
					}
				}catch(Exception e) {
					log.error("", e);
				}
			}									
		}
		return tagStatus;
	}
	/**
	 * 获取实时数据值(测试用)
	 * @category 获取实时数据值(测试用)
	 * <AUTHOR> 
	 * @param tagCode 实时数据仪表
	 * @return
	 */
	private String getTagValue(String tagCode) {
		String result = null;
		if(StringUtils.isNotEmpty(tagCode)) {
			try {
				result =  Double.toString(Math.round(((double)(new Random().nextInt(1000)))/100d));//后续挂实时数据
			}catch(Exception e) {
				log.error("", e);
			}
		}
		return result;
	}
	/**
	 * 获取实时数据值
	 * @category 获取实时数据值
	 * <AUTHOR> 
	 * @param tagCode 实时数据仪表列表
	 * @return
	 */
	private HashMap<String,TagData> getTagValues(List<String> tagCodes) {
		HashMap<String,TagData> result = new HashMap<String,TagData>();
		if(StringUtils.isNotEmpty(tagCodes)) {
			Date nowDt = DateTimeUtils.getNowDate();
			Date nowDt_start = DateTimeUtils.doMinute(nowDt, -5);//这里往前取5分钟，防止服务器时间与程序时间不一致
			List<Tag> tagList = null;
			try {
				//放置下班时间
				tagList = rtdbSrv.queryRtdbTagData(tagCodes,DateTimeUtils.formatDateTime(nowDt_start), DateTimeUtils.formatDateTime(nowDt), 60);//1分钟1个点
			}catch(Exception e) {
				log.error("", e);
			}
			if(StringUtils.isNotEmpty(tagList)) {
				for(Tag temp:tagList) {
					if(StringUtils.isNotEmpty(temp.getTagCode()) && StringUtils.isNotEmpty(temp.getDatas())) {
						result.put(temp.getTagCode().toUpperCase(), temp.getDatas().get(temp.getDatas().size()-1));//全大写仪表位号,取最后一个数据
					}
				}
			}
		}
		return result;
	}
	/**
	 * 校验人员是否可以操作某张操作卡
	 * @category 校验人员是否可以操作某张操作卡
	 * <AUTHOR> 
	 * @param parm OperCardExecInitDto
	 * @return List<OperCardUserVo>  OperCardUserVo.canEdit=1可操作 perCardUserVo.errorinfo 不可操作的原因
	 */
	@Override
	public List<OperCardUserVo> checkOperateUser(OperCardExecInitDto parm) {
		// TODO Auto-generated method stub
		List<OperCardUserVo> resut = new ArrayList<OperCardUserVo>();
		if(parm!=null && StringUtils.isNotEmpty(parm.getCheckOperateList())) {//有要判断的人员
			String errorInfo = null;
			LinkedHashMap<String,OpercardSteppost> operPostMap = new LinkedHashMap<String,OpercardSteppost>();//可操作岗位
			OperCardModelVo cardVo = getCardModel(parm.getCardId(),parm.getCatalogAlias());//获取操作卡发布数据(测试用)
			if(cardVo!=null && StringUtils.isNotEmpty(cardVo.getCardVerId())){//获取到了发布版本			
				if(StringUtils.isNotEmpty(cardVo.getOpPost())) {
					for(OpercardSteppost temp:cardVo.getOpPost()){
						if(StringUtils.isNotEmpty(temp.getPostId())) {
							if(!operPostMap.containsKey((temp.getPostId()))){
								operPostMap.put(temp.getPostId(), temp);
							}
						}
					}
					if(operPostMap.size()==0) {
						errorInfo="操作卡可操作岗位未设置";
					}
				}else {
					errorInfo="操作卡无可操作岗位";
				}	
			}else {
				errorInfo="操作卡未发布，无法校验操作人员";
			}
			Map<String,SysEmployeeOrgPost> postMap = null;
			if(errorInfo==null) {//无错误信息，读取岗位
				List<String> sql = new ArrayList<String>();
				for(OperCardUserVo temp:parm.getCheckOperateList()) {
					if(StringUtils.isNotEmpty(temp.getUserId())){
						sql.add(temp.getUserId());
					}
				}
				if(sql.size()>0) {
					postMap = userPostService.getEmployeeOrgPostMap(sql);//查找人员岗位				
				}
			}
			if(postMap==null) {
				postMap = new HashMap<String,SysEmployeeOrgPost>();
			}	
			String postName=null;
			for(OperCardUserVo temp:parm.getCheckOperateList()) {
				SysEmployeeOrgPost post = postMap.get(temp.getUserId());//查找人员岗位
				if(post==null) {
					temp.setCanEdit(0);
					if(errorInfo!=null) {
						temp.setErrorInfo(errorInfo);
					}else {
						temp.setErrorInfo("未找到岗位信息");
					}
				}else {
					if(operPostMap.containsKey(post.getPostid())) {
						temp.setCanEdit(1);//可编辑
					}else {
						if(postName==null) {
							StringBuffer postNameBuf = new StringBuffer();
							for(Entry<String,OpercardSteppost> tempPost:operPostMap.entrySet()) {
								SysPost postN = postService.findPostById(tempPost.getKey());
								if(postN==null) {//没找到岗位
									postNameBuf.append(","+tempPost.getValue().getPostName());	//取设置的岗位						
								}else {
									postNameBuf.append(","+postN.getName());
								}
							}
							if(postNameBuf.length()>0) {
								postName=postNameBuf.substring(1);
							}else {
								postName="";//不再读取了，正常走不到这
							}
						}
						temp.setCanEdit(0);
						String userPostName="";
						SysPost postUser = postService.findPostById(post.getPostid());
						if(postUser!=null) {
							userPostName=postUser.getName();
						}
						temp.setErrorInfo("人员岗位为【"+userPostName+"】,不在可操作岗位【"+postName+"】中，不能执行操作卡");
					}
				}
				resut.add(temp);
			}
		}
		return resut;
	}
	/**
	 * 获取操作卡实例
	 * @category 获取操作卡实例
	 * <AUTHOR> 
	 * @param parm OperCardExecQueryDto
	 * @return OperCardExecVo 操作卡步骤
	 */
	@Override
	public OperCardExecVo getExecOpercard(OperCardExecQueryDto parm) {
		// TODO Auto-generated method stub
		OperCardExecVo result = null;
		if(parm!=null) {
			boolean canEdit = true;
			if(parm.getReadOnlyModel()!=null && parm.getReadOnlyModel().intValue()==1) {//只读模式
				canEdit = false;
			}
			OperCardInstanceVo instanceVo = getOpercardInstance(parm.getExecId(),canEdit);//获取实例
			result = createExecOpercardBean(instanceVo,canEdit,true);
			if(parm.getHasWorkContent()!=null && parm.getHasWorkContent().booleanValue()) {
				if(instanceVo!=null && instanceVo.getInfo()!=null) {
					result.setWorkContent(instanceVo.getInfo().getWorkContent());//写入工作内容
				}
			}
		}
		return result;
	}
	/**
	 * 查找操作卡执行记录
	 * @category 查找操作卡执行记录
	 * <AUTHOR> 
	 * @param parm
	 * @return List<OperCardExecDataVo>
	 */
	@Override
	public List<OperCardExecDataVo> queryOperCardExecData(OperCardExecQueryDto parm) {
		// TODO Auto-generated method stub
		List<OperCardExecDataVo> result = new ArrayList<OperCardExecDataVo>();
		if(parm!=null) {
	        Pagination<?> page = null;
	        if (ObjUtils.notEmpty(parm.getPageSize()) && parm.getPageSize() > 0) {// 创建分页信息
	            page = Pagination.create(ObjUtils.isEmpty(parm.getPageNum()) ? 1 : parm.getPageNum(), parm.getPageSize());
	        }
			List<OpercardExec> queryList = dbService.queryOperCardExecData(parm.getOrgCode(), parm.getCardId(), parm.getCatalogAlias(), parm.getStartDt(), parm.getEndDt(),page);
			if(StringUtils.isNotEmpty(queryList)) {
				String orgName = "";
				SysOrg org = orgServ.findOrgById(parm.getOrgCode());
				if(org!=null) {
					orgName = org.getOrgname();
				}
				int type=1;//1汇总数据 2执行记录
				if(parm!=null && parm.getType()!=null) {
					type = parm.getType().intValue();
				}
				List<String> execIdList = new ArrayList<String>();
				HashSet<String> createUserIdSet = new HashSet<String>();
				List<OperCardExecDataVo> queryDataList = new ArrayList<OperCardExecDataVo>();
				for(OpercardExec temp:queryList) {
					OperCardExecDataVo bean = new OperCardExecDataVo();
					bean.setExecId(temp.getId());//执行实例ID
					bean.setCardId(temp.getCardId());//操作卡id
					bean.setCardName(temp.getCardName());//操作卡名称
					bean.setCatalogAlias(temp.getCatalogAlias());//操作卡目录别名
					bean.setCatalogName(temp.getCatalogName());//操作卡目录名称
					bean.setCardVerId(temp.getCardVerId());//操作卡版本ID
					bean.setOrgCode(temp.getOrgCode());//机构代码
					bean.setOrgName(orgName);//机构名称
					if(temp.getStartDt()!=null) {
						bean.setStartDt(DateTimeUtils.formatDateTime(temp.getStartDt()));//开始时间
					}
					if(temp.getEndDt()!=null) {
						bean.setEndDt(DateTimeUtils.formatDateTime(temp.getEndDt()));//结束时间
					}
					bean.setExecStatus(temp.getExecStatus());//执行状态 -1废止 0未开始 1执行中 2已完成
					bean.setWorkContent(temp.getWorkContent());//执行内容
					bean.setCreateUserId(temp.getCreateBy());//创建卡实例的人员ID
					createUserIdSet.add(temp.getCreateBy());
					if(bean.getExecStatus()!=null && bean.getExecStatus().intValue()==2) {//已完成的
						if(temp.getPCount()!=null) {
							bean.setPCount(temp.getPCount());//外操操作次数
						}
						if(temp.getICount()!=null) {
							bean.setICount(temp.getICount());//内操操作次数
						} 
						if(temp.getMCount()!=null) {
							bean.setMCount(temp.getMCount());//班长操作次数
						}
						bean.setInputUserName(temp.getInputUserName());//操作人
					}else {//未完成的，需要去数据里统计
						execIdList.add(temp.getId());
						queryDataList.add(bean);//要查询子数据的实例
					}
					result.add(bean);
				}
				if(type==1) {//汇总数据
					if(queryDataList.size()>0) {
						List<OpercardExecStepdetails> stepdetailsList = dbService.getStepdetailsListByIds(execIdList);
						if(StringUtils.isNotEmpty(stepdetailsList)) {
							Map<String,List<OpercardExecStepdetails>> stepdetailsMap = stepdetailsList.stream().collect(Collectors.groupingBy(step->step.getExecId()));
							if(stepdetailsMap==null) {
								stepdetailsMap = new HashMap<String,List<OpercardExecStepdetails>>();
							}
							for(OperCardExecDataVo temp:queryDataList) {
								int pCount = 0;//外操执行次数 
								int iCount = 0;//内操执行次数
								int mCount = 0;//班长执行次数
								List<OpercardExecStepdetails> tempList =stepdetailsMap.get(temp.getExecId());//查找执行人员
								if(StringUtils.isNotEmpty(tempList)) {
									LinkedHashSet<String> userSet = new LinkedHashSet<String>();
	//								HashSet<String> execStepIdSet = new HashSet<String>();//用于判断步骤执行人员重复问题
									for(OpercardExecStepdetails tempStep:tempList) {
										int execStatus = 0;//执行状态 0：未确认 1：已确认； 2：跳过步骤
										if(tempStep.getExecStatus()!=null ) {
											execStatus = tempStep.getExecStatus().intValue();
										}
										if(execStatus==1 || execStatus==2) {
											//1和2都等于完成了（跳步等于不做，就是完成了）	
											//统计执行数据
	//										if("P".equalsIgnoreCase(tempStep.getOpSignPost())) {
	//											pCount++;
	//										}else if("I".equalsIgnoreCase(tempStep.getOpSignPost())) {
	//											iCount++;
	//										}else if("M".equalsIgnoreCase(tempStep.getOpSignPost())) {
	//											mCount++;
	//										}
											//统计执行数据
											if(tempStep.getOpSignPost().indexOf("P")>=0) {
												pCount++;
											}
											if(tempStep.getOpSignPost().indexOf("I")>=0) {
												iCount++;
											}
											if(tempStep.getOpSignPost().indexOf("M")>=0) {
												mCount++;
											}
										}
										if(StringUtils.isNotEmpty(tempStep.getExecPerson())) {//执行人
											if(!userSet.contains(tempStep.getExecPerson())) {
												userSet.add(tempStep.getExecPerson());
											}
										}
									}
									if(userSet.size()>0) {
										StringBuffer userName = new StringBuffer();
										for(String tempUserName:userSet) {
											userName.append(","+tempUserName);
										}
										 temp.setInputUserName(userName.substring(1));//操作人	
									}
								}	
								temp.setPCount(pCount);//外操
								temp.setICount(iCount);//内操
								temp.setMCount(mCount);//班长	
							}
						}	
					}
				}else {//执行记录
					if(result.size()>0) {
						Map<String,SysEmployeeInfo> userMap =null;
						if(createUserIdSet.size()>0) {
							List<String> userList = new ArrayList<String>();
							userList.addAll(createUserIdSet);
							List<SysEmployeeInfo> userInfoList = empServ.findEmployeeByIds(userList);
							if(StringUtils.isNotEmpty(userInfoList)) {
								userMap =  userInfoList.stream().collect(Collectors.toMap(SysEmployeeInfo::getId, obj -> obj, (key1, key2) -> key2));// 将list转换为map// 重复键值时，第一个key被第二个key覆盖;	
							}
						}
						if(userMap==null) {
							userMap = new HashMap<String,SysEmployeeInfo>();
						}
						HashMap<String,OpercardClassify> classMap = getOperCardClassify(parm.getOrgCode());//分类
						for(OperCardExecDataVo temp:result) {
							SysEmployeeInfo user = userMap.get(temp.getCreateUserId());
							if(user!=null) {
								temp.setCreateUserName(user.getEmpname());//使用人
							}					
							OpercardClassify classify = classMap.get(temp.getCardId());
							if(classify!=null) {
								temp.setClassifyId(classify.getId());
								temp.setClassifyName(classify.getName());//操作卡分类
							}
						}		
					}
				}
				if(page!=null) {
					parm.setTotal(Long.valueOf(page.getTotal()).intValue());//总数
				}
			}		
		}
		return result;
	}
	/**
	 * 获取操作卡分类
	 * @category 获取操作卡分类
	 * <AUTHOR> 
	 * @param orgCode 机构代码
	 * @return HashMap<String,OpercardClassify>
	 */
	private HashMap<String,OpercardClassify> getOperCardClassify(String orgCode){
		HashMap<String,OpercardClassify> result = new HashMap<String,OpercardClassify>();
		if(StringUtils.isNotEmpty(orgCode)) {
			List<OpercardClassify> classList = classServ.queryAllClassify(orgCode);
			if(StringUtils.isNotEmpty(classList)) {
				List<OpercardInfo> infoList = infoServ.queryAllParseWord(orgCode);
				if(StringUtils.isNotEmpty(infoList)) {
					Map<String,OpercardClassify> classMap =classList.stream().collect(Collectors.toMap(OpercardClassify::getId, obj -> obj, (key1, key2) -> key2));// 将list转换为map// 重复键值时，第一个key被第二个key覆盖;	
					if(classMap==null) {
						classMap = new HashMap<String,OpercardClassify>();
					}					
					for(OpercardInfo temp:infoList) {
						OpercardClassify classBean = classMap.get(temp.getClassid());
						if(classBean!=null) {
							result.put(temp.getId(), classBean);
						}
					}
				}
			}
		}
		return result;
	}
	@SuppressWarnings("unchecked")
	@Override
	public void exportOperCardExecData(OperCardExecQueryDto parm, HttpServletResponse response) {
		// TODO Auto-generated method stub
		//Excel输出部分
		// 创建工作薄对象
	
		String sheetName = null;
		int type=1;//1汇总数据 2执行记录
		if(parm!=null && parm.getType()!=null) {
			type = parm.getType().intValue();
		}
		if(type==1) {
			sheetName = "统计数据";
		}else {
			sheetName = "执行记录";
		}
		//生成表头
		ExportParams exportParams = new ExportParams(null, null,sheetName);
		List<ExcelExportEntity> keyList = new ArrayList<ExcelExportEntity>();
		int i=0;
		if(type==1) {//汇总数据
			keyList.add(getCountExpEntity(i++, "操作卡名称","cardName", 300));
			keyList.add(getCountExpEntity(i++, "操作卡项目","catalogName",300));
			keyList.add(getCountExpEntity(i++, "操作人","inputUserName",250));
			keyList.add(getCountExpEntity(i++, "内操操作次数","iCount",200));
			keyList.add(getCountExpEntity(i++, "外操操作次数","pCount",200));
			keyList.add(getCountExpEntity(i++, "班长操作次数","mCount",200));
			keyList.add(getCountExpEntity(i++, "操作开始时间","startDt",200));
			keyList.add(getCountExpEntity(i++, "操作结束时间","endDt",200));	
		}else {//执行记录
			keyList.add(getCountExpEntity(i++, "部门","orgName",200));
			keyList.add(getCountExpEntity(i++, "操作卡类别","classifyName",200));
			keyList.add(getCountExpEntity(i++, "操作卡名称","cardName", 300));
			keyList.add(getCountExpEntity(i++, "操作卡项目","catalogName",300));
			keyList.add(getCountExpEntity(i++, "使用原因","workContent",300));
			keyList.add(getCountExpEntity(i++, "使用人","createUserName",200));
			keyList.add(getCountExpEntity(i++, "开始使用时间","startDt",200));
			keyList.add(getCountExpEntity(i++, "结束使用时间","endDt",120));
		}
		
		List<Map<String, Object>> excelDataList = new ArrayList<Map<String, Object>>();	
		if(parm!=null) {	
			//生成数据
			parm.setPageSize(0);//导出不分页
			List<OperCardExecDataVo> dataList =  queryOperCardExecData(parm);
			if(StringUtils.isNotEmpty(dataList)) {
				for(OperCardExecDataVo temp:dataList) {
					Map<String, Object> dataMap =ObjUtils.copyTo(temp, Map.class);
					excelDataList.add(dataMap);
				}
			}
		}
		Workbook workbook = ExcelExport.getWorkbook(exportParams, keyList, excelDataList, null);
		// 文档输出
		ExcelExport.downLoadExcel("导出数据", response, workbook);
	}
	/**
	 * 转换对象
	 * @Description: 
	 * @param index
	 * @param titleObj
	 * @return
	 */
	private ExcelExportEntity getCountExpEntity(int index, String colName,String colCode,int width) {
		ExcelExportEntity expEntity = new ExcelExportEntity(colName, colCode);//textfield datetimefield combo textareafield
		expEntity.setWidth(getWidth(width));
		expEntity.setOrderNum(index);
//			expEntity.setNeedMerge(true);
		return expEntity;
	}
	/**
	 * 获取excel宽度
	 * @Description:
	 * @param width
	 * @return
	 */
	private static double getWidth(Integer width) {
		double w = 20.00;
		if (width != null) {
			w = width.intValue();
			if (w > 0) {
				w = width / 10.00;
				if (w < 20) {
					w = 20;
				}
			}
		}
		return w;
	}
	/**
	 * 操作卡导出PDF
	 * TODO
	 * @see com.yunhesoft.joblist.operCard.service.IOpercardExecService#exportOperCardExecPDF(com.yunhesoft.joblist.operCard.entity.dto.OperCardExecQueryDto, javax.servlet.http.HttpServletResponse)
	 */
	@Override
	public void exportOperCardExecPDF(OperCardExecQueryDto parm, HttpServletResponse response) {
		// TODO Auto-generated method stub
		if(parm!=null && StringUtils.isNotEmpty(parm.getExecId())) {
			parm.setReadOnlyModel(1);//导出时只用只读模式
			parm.setHasWorkContent(true);//要查工作内容
			OperCardExecVo execVo = getExecOpercard(parm);
			if(execVo!=null) {
	        String fileName = String.valueOf(System.currentTimeMillis())+ ".pdf";//前台会重新命名，后台给一个名称就可以了
//	        fileName =  (StringUtils.isEmpty(fileName) ? "操作卡" : fileName) + ".pdf";
				try {
//					opercardExportMode="gdsh";
					OperCardExportModel oem = OperCardExportModelFactory.getInstance(opercardExportMode);//各地区的导出类
					ByteArrayOutputStream baStream = new ByteArrayOutputStream();
			        Document doc = new Document(PageSize.A4, 0, 0, 50, 0);
			       
			        PdfWriter.getInstance(doc, baStream);
			        response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			        doc.open();
			        doc.newPage();
			        String title = execVo.getCardName();
			        Paragraph titleParagraph = new Paragraph(title, getPdfFont(20, true));
			        titleParagraph.setAlignment(Paragraph.ALIGN_CENTER);
			        titleParagraph.setSpacingAfter(10);
			        doc.add(titleParagraph);
			      
			        String titleSub = execVo.getCatalogName();
			        Paragraph titleSubParagraph = new Paragraph(titleSub, getPdfFont(18, true));
			        titleSubParagraph.setAlignment(Paragraph.ALIGN_CENTER);
			        titleSubParagraph.setSpacingAfter(100);
			        doc.add(titleSubParagraph);
			        HashMap<String,OperCardUserVo> userList = new HashMap<String,OperCardUserVo>();
			        List<OperCardUserVo> operUserList = new ArrayList<OperCardUserVo>();
			        LinkedHashMap<String,OperCardUserVo> Mlist = new LinkedHashMap<String,OperCardUserVo>();
			        LinkedHashMap<String,OperCardUserVo> Ilist = new LinkedHashMap<String,OperCardUserVo>();
			        LinkedHashMap<String,OperCardUserVo> Plist = new LinkedHashMap<String,OperCardUserVo>();
			        for(OperCardExecStepVo tempStep:execVo.getStepList()) {
			        	if(tempStep.getOpType()!=null && tempStep.getOpType().intValue()==3) {//操作步骤
			        		if(StringUtils.isNotEmpty(tempStep.getExecPersonId())) {
			        			OperCardUserVo userVo = null;
								String execPersonId = tempStep.getExecPersonId();
								if(userList.containsKey(execPersonId)) {
			        				userVo=userList.get(execPersonId);
			        			}else {
									String execPerson = tempStep.getExecPerson();
									String cardId = null;//身份证号
									String staffNo = null;//工号
									SysEmployeeInfo userinfo = employeeService.findEmployeeById(execPerson);//缓存中获取人员
									if(userinfo!=null) {
										cardId=userinfo.getCardno();
										staffNo=userinfo.getStaffNo();
									}
									//人员姓名  "薛勇"
//									String execPerson = "薛勇";
//									//"人员身份证号"
//									String cardId = "211003197205154830";
			        				userVo = new OperCardUserVo();
			        				userVo.setUserId(execPersonId);
			        				userVo.setUserName(execPerson);
			        				userVo.setOpSignPostLevel(0);//目前未计算级别
									userVo.setCardId(cardId);//身份证号
									userVo.setStaffNo(staffNo);//工号
			        				userList.put(userVo.getUserId(),userVo);
			        			}
			        			int level = 0;
			        			if(tempStep.getOpSignPost().indexOf("M")>=0) {
			        				level=3;
				        		}else if(tempStep.getOpSignPost().indexOf("I")>=0) {//这里防止i/P这种重复计算，所以用if else 
				        			level=2;
								}else if(tempStep.getOpSignPost().indexOf("P")>=0) {
									level=1;
								}
			        			if(userVo.getOpSignPostLevel()!=null && userVo.getOpSignPostLevel().intValue()<level) {//需要设置到人员列表（这里的逻辑是 如果你确认过班长的步骤就是班长，确认过内操步骤就是内操，都内确认过就是外操，按班长-内操-外操的优先级计算岗位）
			        				switch(userVo.getOpSignPostLevel().intValue()) {//移除掉低级别的操作岗位
			        					case 1:
			        						Plist.remove(userVo.getUserId());
			        						break;
			        					case 2:
			        						Ilist.remove(userVo.getUserId());
			        						break;
			        					case 3:
			        						Mlist.remove(userVo.getUserId());//最大就是3，理论上3不会大于3，走不到这
			        						break;
			        				}
	        						userVo.setOpSignPostLevel(level);
			        				switch(level) {//添加到更高级别的岗位
			        					case 1:
			        						Plist.put(userVo.getUserId(),userVo);
			        						break;
			        					case 2:
			        						Ilist.put(userVo.getUserId(),userVo);
			        						break;
			        					case 3:
			        						Mlist.put(userVo.getUserId(),userVo);
			        						break;
			        				}
			        			}
			        		}
			        	}
			        }
			        PdfPTable table = new PdfPTable(3);
	                // 设置各列的列宽
			        table.setTotalWidth(600f);
			        Font black = this.getPdfFont(14, false);
			        Font blackB = this.getPdfFont(14, true);
			        Font white = this.getPdfFont(14, false, oem.getOperUserColor()==null?BaseColor.BLACK:oem.getOperUserColor());//如果未指定操作人员姓名的颜色，则使用黑色，如果需要进行签章，则使用白色
			        table.addCell(createTableCell("使用原因",blackB,1,null));
			        table.addCell(createTableCell(execVo.getWorkContent()==null?"":execVo.getWorkContent(),black,2,null));
			        table.addCell(createTableCell("操作开始时间",blackB,1,null));
			        table.addCell(createTableCell(execVo.getStartDt()==null?"":execVo.getStartDt(),black,2,null));
			        table.addCell(createTableCell("操作截止时间",blackB,1,null));
			        table.addCell(createTableCell(execVo.getEndDt()==null?"":execVo.getEndDt(),black,2,null));
			        table.addCell(createTableCell("操作人(班长)",blackB,1,null));	        
			        table.addCell(createTableCell(getOperUserNames(Mlist,oem,operUserList),white,2,null));
			        table.addCell(createTableCell("操作人(内操)",blackB,1,null));
			        table.addCell(createTableCell(getOperUserNames(Ilist,oem,operUserList),white,2,null));
			        table.addCell(createTableCell("操作人(外操)",blackB,1,null));
			        table.addCell(createTableCell(getOperUserNames(Plist,oem,operUserList),white,2,null));
                    doc.add(table);
                    
//	                for (List<PrizeNoticeTableDataCell> tableRow : RowT) {
//	                    for (PrizeNoticeTableDataCell tableCell : tableRow) {
//	                        Paragraph paragraph = new Paragraph(tableCell.getValue(), getPdfFont(tableCell.getFontSize(), tableCell.isBolder()));
//	                        paragraph.setLeading(tableCell.getFontSize());
//	                        PdfPCell pdfCell = new PdfPCell(paragraph);
//	                        String align = tableCell.getAlign();
//	                        pdfCell.setHorizontalAlignment("left".equalsIgnoreCase(align) ? Element.ALIGN_LEFT : ("right".equalsIgnoreCase(align) ? Element.ALIGN_RIGHT : Element.ALIGN_CENTER));
//	                        pdfCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
//	                        pdfCell.setMinimumHeight(25);// 设置最小单元格高度
//	                        pdfCell.setColspan(tableCell.getWidth());
//	                        table.addCell(pdfCell);
//	                    }
//	                }
			        
			        doc.newPage();
			        for(OperCardExecStepVo tempStep:execVo.getStepList()) {
				        Paragraph htmlParagraph = new Paragraph();
				        ElementList elementList = parseToElementList(tempStep.getOpContent(), null);
			            // 写入到 段落 Paragraph
			            for (Element element : elementList) {
			            	htmlParagraph.add(element);
			            }
	//		            htmlParagraph.setMultipliedLeading(1.5f);
	//		            htmlParagraph.setSpacingBefore(100f);
	//			        htmlParagraph.setAlignment(Paragraph.ALIGN_CENTER);
//				        List<IElement> el = HtmlConverter.convertToElements(tempStep.getOpContent(), null);
//			            for (IElement element : el) {
//			            	System.out.println(element);
//			            }
			            doc.add(htmlParagraph);
			        }
//			        Paragraph titleParagraph1 = new Paragraph("", getPdfFont(12, false));
//			        List<IElement> el = HtmlConverter.convertToElements(execVo.getStepList().get(0).getOpContent(), null);
//		            for (IElement element : el) {
//		            	System.out.println(element);
//		            }
//			        titleParagraph1.setAlignment(Paragraph.ALIGN_LEFT);
//			        titleParagraph1.setSpacingAfter(10);
//			        doc.add(titleParagraph1);
			        
			        doc.close();
			        response.setCharacterEncoding("UTF-8");
			        response.setContentType("application/pdf");
			        try {
			            response.setHeader("Content-Disposition","attachment;filename=" + new String(fileName.getBytes(), "iso8859-1"));
			        } catch (UnsupportedEncodingException e) {
			            throw new RuntimeException(e);
			        }
					ServletOutputStream streamOut = response.getOutputStream();
			        baStream = oem.signature(baStream, response, operUserList);//签章
			        baStream.writeTo(streamOut);
			        baStream.close();
			        streamOut.flush();
			        streamOut.close();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
	}
	/**
	 * 获取操作人员姓名列表
	 * @category 获取操作人员姓名列表
	 * <AUTHOR> 
	 * @param userMap
	 * @param OperCardExportModel
	 * @param returnOperUserList
	 * @return
	 */
	private String getOperUserNames(LinkedHashMap<String,OperCardUserVo> userMap,OperCardExportModel oem,List<OperCardUserVo> returnOperUserList) {
		String result = null;
        StringBuffer sb = new StringBuffer();
        for(Entry<String,OperCardUserVo> temp:userMap.entrySet()) {
        	sb.append(","+oem.getOperUserName(temp.getValue()));
        	returnOperUserList.add(temp.getValue());
        }
		if(sb.length()>0) {
			result = sb.substring(1);
		}else {
			result = "";
		}
		return result;
	}
	private Font getPdfFont(float size, boolean bolder) {
        if (pdfBaseFont == null) {
        	pdfBaseFont = getMFontProvider().getBaseFont();
        }
        String key = size + "_" + bolder;
        Font font = pdfFontMap.computeIfAbsent(key, k -> new Font(pdfBaseFont, size, bolder ? Font.BOLD : Font.NORMAL));
       
        return font;
    }
	private Font getPdfFont(float size, boolean bolder,BaseColor color) {

		if (pdfBaseFont == null) {
        	pdfBaseFont = getMFontProvider().getBaseFont();
        }
		Font result =new Font(pdfBaseFont, size, bolder ? Font.BOLD : Font.NORMAL);
		if (bolder) {
			result.setStyle(Font.BOLD);
		}
		if (color!=null) {
			result.setColor(color);//BaseColor.WHITE
	    }
        return result;
	}
	private MFontProvider getMFontProvider() {
		if(mp==null) {
			mp = new MFontProvider(this.resourceLoader);
		}
		return mp;
	}
	/**
	 * html生成PDF元素
	 * @category 
	 * <AUTHOR> 
	 * @param html
	 * @param css
	 * @return ElementList
	 * @throws IOException
	 */
    public ElementList parseToElementList(String html, String css) throws IOException {
        // CSS
        CSSResolver cssResolver = new StyleAttrCSSResolver();
        if (css != null) {
            CssFile cssFile = XMLWorkerHelper.getCSS(new ByteArrayInputStream(css.getBytes()));
            cssResolver.addCss(cssFile);
        } 
        // HTML
        CssAppliers cssAppliers = new CssAppliersImpl(getMFontProvider());
        HtmlPipelineContext htmlContext = new HtmlPipelineContext(cssAppliers);
        htmlContext.setTagFactory(Tags.getHtmlTagProcessorFactory());
        htmlContext.autoBookmark(false);

        // Pipelines
        ElementList elements = new ElementList();
        ElementHandlerPipeline end = new ElementHandlerPipeline(elements, null);
        HtmlPipeline htmlPipeline = new HtmlPipeline(htmlContext, end);
        CssResolverPipeline cssPipeline = new CssResolverPipeline(cssResolver, htmlPipeline);

        // XML Worker
        XMLWorker worker = new XMLWorker(cssPipeline, true);
        XMLParser p = new XMLParser(worker);
        html = html.replace("<br>", "<br/>").replace("<hr>", "<hr/>");
        	//	.replace("<img>", "").replace("<param>", "").replace("<link>", "");//不支持单独标签
        p.parse(new ByteArrayInputStream(html.getBytes()));
        return elements;
    } 
    private PdfPCell createTableCell(String content,Font font,int colspan,Integer align) {
    	 Paragraph paragraph = new Paragraph(content, font);
    	 paragraph.setLeading(14);
    	 PdfPCell PdfPCell = new PdfPCell(paragraph);
    	 PdfPCell.setHorizontalAlignment(align==null?Element.ALIGN_CENTER:align.intValue());
    	 PdfPCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
    	 PdfPCell.setMinimumHeight(50);// 设置最小单元格高度
    	 PdfPCell.setColspan(colspan);
//    	 PdfPCell.setNoWrap(true);
    	 return PdfPCell;
    }
	/**
	 * 获取操作卡人员待办信息
	 * @category 
	 * <AUTHOR> 
	 * @param userId 人员ID
	 * @return
	 */
	@Override
	public List<OperCardExecVo> getExecOpercardToDoList(String userId) {
		// TODO Auto-generated method stub
		List<OperCardExecVo> reuslt = new ArrayList<OperCardExecVo>();
		if(StringUtils.isNotEmpty(userId)) {
			List<OpercardExecJump> jumpList = dbService.getAuditJumpList(userId);
			if(StringUtils.isNotEmpty(jumpList)) {//有待办
				List<OpercardExecJump> keepList = new ArrayList<OpercardExecJump>();//去重复
				HashSet<String> execIdSet = new HashSet<String>();
				for(OpercardExecJump temp:jumpList) {
					if(!execIdSet.contains(temp.getExecId())) {
						execIdSet.add(temp.getExecId());
						keepList.add(temp);
					}
				}
				List<String> execIdList = new ArrayList<String>();
				execIdList.addAll(execIdSet);
				List<OpercardExec> execList = dbService.getExecAuditToDoList(execIdList);
				if(StringUtils.isNotEmpty(jumpList)) {//查到了正在执行的主记录
					Map<String,OpercardExec> execMap =  execList.stream()
							.collect(Collectors.toMap(OpercardExec::getId, OpercardExec -> OpercardExec, (key1, key2) -> key2));// 将list转换为map// 重复键值时，第一个key被第二个key覆盖;					
					if(execMap==null) {
						execMap = new HashMap<String,OpercardExec>();
					}
					for(OpercardExecJump temp:keepList) {
						OpercardExec mainData =  execMap.get(temp.getExecId());//查找正在执行的主记录
						if(mainData!=null) {
							OperCardExecVo bean = execToVoCopy(mainData, null);
							bean.setExecStatus(3);//待跳步审核
							reuslt.add(bean);
						}
					}
				}
			}
		}
		return reuslt;
	}
	/**
	 * 创建操作卡实例执行类
	 * @category 创建操作卡实例执行类
	 * <AUTHOR> 
	 * @param instanceVo
	 * @param canEdit 是否可以编辑框
	 * @param searchAll true查全部 false只查找操作项和提示卡
	 * @return
	 */
	private OperCardExecVo createExecOpercardBean(OperCardInstanceVo instanceVo,boolean canEdit,boolean searchAll) {
		// TODO Auto-generated method stub
		OperCardExecVo result = null;
		if(instanceVo!=null) {
			if(instanceVo!=null && instanceVo.getInfo()!=null) {//获取到了实例
				boolean noFixed = true; //是否已固化
				if(instanceVo.getInfo().getFixedStatus()!=null && instanceVo.getInfo().getFixedStatus().intValue()==1) {//已固化
					noFixed = false; 
				}
				List<OperCardExecStepVo> stepList = new ArrayList<OperCardExecStepVo>();
				result = execToVoCopy(instanceVo.getInfo(), null);//复制主信息
				result.setOperateDt(DateTimeUtils.getNowDateStr());//操作日期
				result.setStepList(stepList);
				boolean editAll = false;//编辑全部
				Map<String,List<OpercardExecSteppost>> postMap = null;//步骤岗位信息
				HashSet<String> canEditStep = new HashSet<String>();//可编辑步骤
				if(canEdit) {
					editAll = getEditStep(instanceVo.getOpPost(),canEditStep);//获取可编辑步骤
					if(StringUtils.isNotEmpty(instanceVo.getOpPost())){
						postMap =  instanceVo.getOpPost().stream().collect(Collectors.groupingBy(obj->obj.getExecStepId()));//分组岗位信息
					}
				}
				if(postMap==null) {
					postMap = new HashMap<String,List<OpercardExecSteppost>>();
				}
				HashMap<String,OpercardExecStepdetails> stepdetailsMap = getStepdetailsMap(instanceVo.getOpStepdetails());//步骤确认信息
				HashMap<String,OpercardExecInsdetails> instrumentdetailsMap = getInstrumentdetailsMap(instanceVo.getOpInstrumentdetails());//仪表值map
				HashMap<String,List<OperCardExecInstrumentVo>> instrumentMap = new HashMap<String,List<OperCardExecInstrumentVo>>();//仪表信息
				if(StringUtils.isNotEmpty(instanceVo.getOpInstrument())) {
					for(OpercardExecInstrument temp:instanceVo.getOpInstrument()){
						List<OperCardExecInstrumentVo> list = instrumentMap.get(temp.getExecStepId());
						if(list==null) {
							list = new ArrayList<OperCardExecInstrumentVo>();
							instrumentMap.put(temp.getExecStepId(), list);
						}
						OperCardExecInstrumentVo bean = instrumentToVoCopy(temp, null);
						OpercardExecInsdetails details = instrumentdetailsMap.get(bean.getInstrumentId());//获取仪表数据
						if(details!=null) {
							instrumentdetailToVoCopy(details,bean);//复制仪表值信息
//							bean.setTagValue(details.getTagValue());//仪表值
//							bean.setTagStatus(details.getTagStatus());//仪表状态 1：超限；0:正常
//							bean.setInputType(details.getInputType());//录入方式 1自动采集 2手动录入
//							if(details.getInputDt()!=null){
//								bean.setInputDt(DateTimeUtils.formatDateTime(details.getInputDt()));//录入时间
//							}
//							bean.setInputPerson(details.getInputPerson());//录入人
//							bean.setInputPersonId(details.getInputPersonId());//录入人id
						}
						list.add(bean);
					}
				}
				HashMap<String,List<String>> readMap = new HashMap<String,List<String>>();//提示卡阅读列表
				if(StringUtils.isNotEmpty(instanceVo.getOpRead())){
					for(OpercardExecStepread temp:instanceVo.getOpRead()){
						List<String> list = readMap.get(temp.getExecStepId());
						if(list==null) {
							list = new ArrayList<String>();
							readMap.put(temp.getExecStepId(), list);
						}
						list.add(temp.getReadPersonId());
					}
				}
				HashMap<String,OpercardExecJump> jumpMap = new HashMap<String,OpercardExecJump>();//跳步申请
				if(StringUtils.isNotEmpty(instanceVo.getOpJump())){
					for(OpercardExecJump temp:instanceVo.getOpJump()){
						jumpMap.put(temp.getExecStepId(),temp);
					}
				}
				int execType = 1;//执行方式1顺序执行 2同步执行
				if(result.getExecType()!=null) {
					execType=result.getExecType().intValue();
				}
				boolean findFirstPostStep = true;//是否需要找第一个可操作步骤
				OperCardExecStepVo lastStep = null;//从第一个可操作步骤开始，可连续执行的最后一步
				for(OpercardExecOperstep temp:instanceVo.getOpStep()) {
					OperCardExecStepVo bean = stepToVoCopy(temp, null);
					int opType = 1;//1：文本；2：警示（提示卡）；3：操作步骤
					if(temp.getOpType()!=null) {
						opType = temp.getOpType().intValue();
					}
					if(opType==2) {//提示卡
						bean.setReadUserIdList(readMap.get(bean.getExecStepId()));//提示卡阅读信息
					}
					if(opType==3) {//操作步骤
						int execStatus=0;// 执行状态 0：未确认 1：已确认； 2：跳过步骤   没执行确认信息的统一为未确认（0）状态
						OpercardExecStepdetails stepdetailsInfo = stepdetailsMap.get(bean.getExecStepId());//确认信息
						if(stepdetailsInfo!=null) {
							bean.setExecPerson(stepdetailsInfo.getExecPerson());//执行人
							bean.setExecPersonId(stepdetailsInfo.getExecPersonId());//执行人id
							if(stepdetailsInfo.getInputDt()!=null){
								bean.setInputDt(DateTimeUtils.formatDateTime(stepdetailsInfo.getInputDt()));//跳步审核时间
							}
							if(stepdetailsInfo.getExecStatus()!=null) {
								execStatus=stepdetailsInfo.getExecStatus().intValue();//记录执行状态
							}
						}
						bean.setExecStatus(execStatus);// 执行状态 0：未确认 1：已确认； 2：跳过步骤
						bean.setInstrumentList(instrumentMap.get(bean.getExecStepId()));//仪表信息
						
						OpercardExecJump jumpInfo = jumpMap.get(bean.getExecStepId());//跳步申请
						if(jumpInfo!=null) {
							bean.setAppPerson(jumpInfo.getAppPerson());//跳步申请人
							bean.setAppPersonId(jumpInfo.getAppPersonId());//跳步申请人id
							bean.setAppReason(jumpInfo.getAppReason());//跳步申请原因
							if(jumpInfo.getAppDt()!=null){
								bean.setAppDt(DateTimeUtils.formatDateTime(jumpInfo.getAppDt()));//跳步申请时间
							}
							bean.setAuditPerson(jumpInfo.getAuditPerson());//跳步审核人
							bean.setAuditPersonId(jumpInfo.getAuditPersonId());//跳步审核人id
							bean.setAuditStatus(jumpInfo.getAuditStatus());//跳步审核状态 1：审核通过；0:未提交；2：审核中；-1：否决
							bean.setAuditDesc(jumpInfo.getAuditDesc());//跳步审核说明
							if(jumpInfo.getAuditDt()!=null){
								bean.setAuditDt(DateTimeUtils.formatDateTime(jumpInfo.getAuditDt()));//跳步审核时间
							}
						}
						int canEditStatus = 0;//是否可编辑 0否 1是
						if(canEdit) {//编辑模式，才判断是否当前岗位可编辑某个步骤		
							if(findFirstPostStep) {//尚未找到第一步
								if(execStatus==0) {//未确认记录
									if(editAll || canEditStep.contains(bean.getExecStepId())) {//当前人员岗位可编辑此步骤
										if(bean.getAuditStatus()!=null && bean.getAuditStatus().intValue()==2) {//提交了跳步申请并且尚未申请通过的记录
											canEditStatus=0;//跳步申请后不允许操作
											findFirstPostStep=false;//找到了当前岗位能执行的第一步，但不能执行，后续也就不需要找了
										}else {	
											canEditStatus=1;//可操作
											if(noFixed && bean.getRequiredStep()!=null && bean.getRequiredStep().intValue()==1) {//判断必填项 未固化，找可执行步骤，已固化，所有步骤都可以执行
												findFirstPostStep=false;//找到了当前岗位能执行的第一步(找必填项，非必填项可以一起填写)，后续就不需要找了
												lastStep = null;//找到了必填项，就不用在补足必填项了
											}else {
												lastStep = bean;//记录步骤
											}
										}
									}else {
										if(noFixed && execType!=2) {//顺序执行（未固化），则第一步当前人员不能编辑，也要打上标识（也就是找当前卡的第一步），同步执行则需要找当前人员的第一步，不是整个卡的第一步
											findFirstPostStep=false;//找到了第一步，后续就不需要找了
										}
									}
								}
							}
						}
						bean.setCanEdit(canEditStatus);
						if(canEditStatus==1) {
							bean.setCanOperate(1);//能编辑就可操作
						}else {
							if(editAll || canEditStep.contains(bean.getExecStepId())) {//当前人员岗位可编辑此步骤
								bean.setCanOperate(1);//是操作岗就可操作
							}
						}
						List<OpercardExecSteppost> stepPost =  postMap.get(bean.getExecStepId());//步骤岗位信息
						if(StringUtils.isNotEmpty(stepPost)){
							StringBuffer postIdBuf = new StringBuffer();
							StringBuffer postNameBuf = new StringBuffer();
							for(OpercardExecSteppost tempPost:stepPost) {
								postIdBuf.append(","+tempPost.getPostId());
								postNameBuf.append(","+tempPost.getPostName());
							}
							bean.setOperatePostId(postIdBuf.substring(1));//可操作岗位ID
							bean.setOperatePostName(postNameBuf.substring(1));//可操作岗位名称
						}
					}
					if(searchAll || opType==2 || opType==3) {//查全部或者是提示卡和操作步骤
						stepList.add(bean);
					}
				}
				if(lastStep!=null) {//查到了最后一个可执行步骤，但不是必填项
					lastStep.setRequiredStep(1);//补足成必填项
				}
			}
		}
		return result;
	}
	/**
	 * 获取操作卡实例
	 * @category 获取操作卡实例
	 * <AUTHOR> 
	 * @param execId 实例ID
	 * @param canEdit false只读模式 true编辑模式（编辑模式按照登录人岗位获取可编辑记录，只读模式所有记录不可以编辑） 
	 * @return
	 */
	private OperCardInstanceVo getOpercardInstance(String execId,boolean canEdit) {
		OperCardInstanceVo result = null;
		if(StringUtils.isNotEmpty(execId)) {
			OpercardExec exec = dbService.getOpercardExec(execId,true);//主信息
			if(exec!=null) {
				result = new OperCardInstanceVo();
				//模型
				result.setInfo(exec);
				result.setOpStep(dbService.getStepList(execId,true));//步骤
				if(canEdit) {//只读模式下不需要读取岗位信息
					result.setOpPost(dbService.getPostList(execId));//岗位
				}
				result.setOpInstrument(dbService.getInstrumentList(execId));//仪表 
				//数据
				result.setOpStepdetails(dbService.getStepdetailsList(execId,true));//确认信息
				result.setOpInstrumentdetails(dbService.getInstrumentdetailsList(execId,true));//仪表数据
				result.setOpRead(dbService.getStepreadList(execId));//阅读信息
				result.setOpJump(dbService.getJumpList(execId));//跳步信息
			}
		}
		return result;
	}
	/**
	 * 复制步骤
	 * @category 复制步骤
	 * <AUTHOR> 
	 * @param sourceObj
	 * @param targetObj
	 * @return
	 */
	private OperCardExecStepVo stepToVoCopy(OpercardExecOperstep sourceObj, OperCardExecStepVo targetObj){
		if(targetObj==null) {
			targetObj= new OperCardExecStepVo();
		}
		if(sourceObj.getExecId()!=null) {//实例ID
			targetObj.setExecId(sourceObj.getExecId());
		}
		if(sourceObj.getId()!=null) {//执行实例步骤ID
			targetObj.setExecStepId(sourceObj.getId());
		}
		if(sourceObj.getStepId()!=null) {//操作卡模型步骤ID
			targetObj.setStepId(sourceObj.getStepId());
		}
		if(sourceObj.getName()!=null) {//步骤名称
			targetObj.setName(sourceObj.getName());
		}
		if(sourceObj.getTmSort()!=null) {//排序
			targetObj.setTmSort(sourceObj.getTmSort());
		}
		if(sourceObj.getMemo()!=null) {//注释
			targetObj.setMemo(sourceObj.getMemo());
		}
		if(sourceObj.getOpType()!=null) {//步骤类型 1：文本；2：警示；3：操作步骤
			targetObj.setOpType(sourceObj.getOpType());
		}
		if(sourceObj.getOpContent()!=null) {//操作内容
			targetObj.setOpContent(sourceObj.getOpContent());
		}
		if(sourceObj.getOpSign()!=null) {//操作标识 完整的标识 [P] [I] (M)
			targetObj.setOpSign(sourceObj.getOpSign());
		}
		if(sourceObj.getOpSignType()!=null) {//操作标识类型 []:操作 ():确认 <>:安全
			targetObj.setOpSignType(sourceObj.getOpSignType());
		}
		if(sourceObj.getOpSignPost()!=null) {//操作标识岗位 I:内操 P:外操 M班长
			targetObj.setOpSignPost(sourceObj.getOpSignPost());
		}
		if(sourceObj.getRequiredStep()!=null) {//必填步骤 1：必填；0：非必填 默认1
			targetObj.setRequiredStep(sourceObj.getRequiredStep());
		}
		if(sourceObj.getGroupId()!=null) {//段落ID
			targetObj.setGroupId(sourceObj.getGroupId());
		}
		if(sourceObj.getConfirmType()!=null) {//确认方式 1手动确认 2身份证 3人脸识别
			targetObj.setConfirmType(sourceObj.getConfirmType());
		}
		if(sourceObj.getRepeatSign()!=null) {//重复记录标识  0：不是 1：是
			targetObj.setRepeatSign(sourceObj.getRepeatSign());
		}
		if(sourceObj.getRepeatTime()!=null) {//重复记录排序 默认为0，每次重复加1，多次重复累次加1
			targetObj.setRepeatTime(sourceObj.getRepeatTime());
		}
		if(sourceObj.getRepeatSort()!=null) {//存储被复制记录的排序值
			targetObj.setRepeatSort(sourceObj.getRepeatSort());
		}
		if(sourceObj.getRepeatDt()!=null) {//重复记录时间
			targetObj.setRepeatDt(DateTimeUtils.formatDateTime(sourceObj.getRepeatDt()));
		}
		if(sourceObj.getRepeatPerson()!=null) {//重复记录人员
			targetObj.setRepeatPerson(sourceObj.getRepeatPerson());
		}
		if(sourceObj.getRepeatPersonId()!=null) {//重复记录人员id
			targetObj.setRepeatPersonId(sourceObj.getRepeatPersonId());
		}
		return targetObj;
	} 
	/**
	 * 复制仪表
	 * @category 复制仪表
	 * <AUTHOR> 
	 * @param sourceObj
	 * @param targetObj
	 * @return
	 */
	private OperCardExecInstrumentVo instrumentToVoCopy(OpercardExecInstrument sourceObj, OperCardExecInstrumentVo targetObj){
		if(targetObj==null) {
			targetObj= new OperCardExecInstrumentVo();
		}
		if(sourceObj.getExecId()!=null) {//实例ID
			targetObj.setExecId(sourceObj.getExecId());
		}
		if(sourceObj.getId()!=null) {//执行实例步骤ID
			targetObj.setExecStepId(sourceObj.getId());
		}
		if(sourceObj.getStepId()!=null) {//操作卡模型步骤ID
			targetObj.setStepId(sourceObj.getStepId());
		}
		if(sourceObj.getId()!=null) {//仪表ID
			targetObj.setInstrumentId(sourceObj.getId());
		}
		if(sourceObj.getId()!=null) {//仪表ID
			targetObj.setInstrumentId(sourceObj.getId());
		}
		if(sourceObj.getMemo()!=null) {//注释
			targetObj.setMemo(sourceObj.getMemo());
		}
		if(sourceObj.getTagName()!=null) {//仪表名称
			targetObj.setTagName(sourceObj.getTagName());
		}
		if(sourceObj.getTagCode()!=null) {//仪表位号
			targetObj.setTagCode(sourceObj.getTagCode());
		}
		if(sourceObj.getTagCode()!=null) {//仪表位号
			targetObj.setTagCode(sourceObj.getTagCode());
		}
		if(sourceObj.getUpLimit()!=null) {//上限值
			targetObj.setUpLimit(sourceObj.getUpLimit());
		}    
		if(sourceObj.getLowLimit()!=null) {//下限值
			targetObj.setLowLimit(sourceObj.getLowLimit());
		}     
		if(sourceObj.getComType()!=null) {//控件类型 raw：实时仪表；number：数字框；combo:下拉框；img:图片；tip：风险提示  textfield：文本框
			targetObj.setComType(sourceObj.getComType());
		}     		
		if(sourceObj.getTmSort()!=null) {//排序
			targetObj.setTmSort(sourceObj.getTmSort());
		}
		if(sourceObj.getPropertyCode()!=null) {//扩展内容
			targetObj.setPropertyCode(sourceObj.getPropertyCode());
			if("combo".equals(targetObj.getComType())) {//下拉框，需要特殊处理下拉列表
				targetObj.setPropertyCode(targetObj.getPropertyCode().replaceAll("[，、。\\|\\s]", ",").trim());//兼容各种分隔符
			}
		}
		return targetObj;
	} 	
	/**
	 * 复制仪表数据
	 * @category 复制仪表数据
	 * <AUTHOR> 
	 * @param sourceObj
	 * @param targetObj
	 * @return
	 */
	private OperCardExecInstrumentVo instrumentdetailToVoCopy(OpercardExecInsdetails sourceObj, OperCardExecInstrumentVo targetObj){
		if(targetObj==null) {
			targetObj= new OperCardExecInstrumentVo();
		}
		if(sourceObj.getTagValue()!=null) {//仪表值
			targetObj.setTagValue(sourceObj.getTagValue());
		}
		if(sourceObj.getTagStatus()!=null) {//仪表状态 1：超限；0:正常
			targetObj.setTagStatus(sourceObj.getTagStatus());
		}
		if(sourceObj.getInputType()!=null) {//录入方式 1自动采集 2手动录入
			targetObj.setInputType(sourceObj.getInputType());
		}
		if(sourceObj.getInputDt()!=null) {////录入时间
			targetObj.setInputDt(DateTimeUtils.formatDateTime(sourceObj.getInputDt()));
		}
		if(sourceObj.getInputPerson()!=null) {//录入人
			targetObj.setInputPerson(sourceObj.getInputPerson());
		}
		if(sourceObj.getInputPersonId()!=null) {//录入人id
			targetObj.setInputPersonId(sourceObj.getInputPersonId());
		}  
		return targetObj;
	}
	/**
	 * 复制主实例信息
	 * @category 复制主实例信息
	 * <AUTHOR> 
	 * @param sourceObj
	 * @param targetObj
	 * @return
	 */
	private OperCardExecVo execToVoCopy(OpercardExec sourceObj, OperCardExecVo targetObj){
		if(targetObj==null) {
			targetObj= new OperCardExecVo();
		}
		if(sourceObj.getId()!=null) {//实例ID
			targetObj.setExecId(sourceObj.getId());
		}
		if(sourceObj.getCardId()!=null) {//操作卡id
			targetObj.setCardId(sourceObj.getCardId());
		}
		if(sourceObj.getCardName()!=null) {//操作卡名称
			targetObj.setCardName(sourceObj.getCardName());
		}
		if(sourceObj.getCatalogAlias()!=null) {//操作卡目录别名
			targetObj.setCatalogAlias(sourceObj.getCatalogAlias());
		}
		if(sourceObj.getCatalogName()!=null) {//操作卡目标名称
			targetObj.setCatalogName(sourceObj.getCatalogName());
		}
		if(sourceObj.getCardVerId()!=null) {//操作卡版本ID
			targetObj.setCardVerId(sourceObj.getCardVerId());
		}
		if(sourceObj.getOrgCode()!=null) {//机构代码
			targetObj.setOrgCode(sourceObj.getOrgCode());
		}
		if(sourceObj.getWorkId()!=null) {//指令/活动id
			targetObj.setWorkId(sourceObj.getWorkId());
		}
		if(sourceObj.getWorkType()!=null) {//指工作类型 1=指令，2=活动
			targetObj.setWorkType(sourceObj.getWorkType());
		}
		if(sourceObj.getExecType()!=null) {//执行方式1串行执行 2并行执行
			targetObj.setExecType(sourceObj.getExecType());
		}else {
			targetObj.setExecType(2);//默认同步执行
		}
		if(sourceObj.getInputType()!=null) {//录入方式1逐步录入 2关键点录入
			targetObj.setInputType(sourceObj.getInputType());
		}else {
			targetObj.setInputType(2);//默认关键点录入
		}
		if(sourceObj.getExecStatus()!=null) {//执行状态 -1废止 0未开始 1执行中 2已完成 3待跳步审核（仅读取待办时有该状态）
			targetObj.setExecStatus(sourceObj.getExecStatus());
		}
		if(sourceObj.getFixedStatus()!=null) {//是否已固化 0：否 1：是
			targetObj.setFixedStatus(sourceObj.getFixedStatus());
		}
		if(sourceObj.getStartDt()!=null) {
			targetObj.setStartDt(DateTimeUtils.formatDateTime(sourceObj.getStartDt()));//开始时间
		}else {
			targetObj.setStartDt("");
		}
		if(sourceObj.getStartDt()!=null) {
			targetObj.setEndDt(DateTimeUtils.formatDateTime(sourceObj.getEndDt()));//截止时间
		}else {
			targetObj.setEndDt("");
		}
		return targetObj;
	}
	
	/**
	 * 获取操作卡固定化状态
	 * @category 
	 * <AUTHOR> 
	 * @param cardId 操作卡ID
	 * @param catalogAlias 目标别名
	 * @param cardVerId 操作卡版本
	 * @return boolean 1已固定 0未固定
	 */
	private int isFixed(String cardId,String catalogAlias,String cardVerId,OpercardInfo info ) {
		int result = 0;
		int fixedNumber=0;
		String fixedNumberStr = configService.getSysConfig("opercardFixed");
		if(Coms.judgeInt(fixedNumberStr)) {
			try {fixedNumber=Integer.parseInt(fixedNumberStr);}catch(Exception e) {}
		}
		if(fixedNumber>0) {//设置了固化值
			if(info==null) {
				info = infoServ.getOpercardInfo(cardId);
			}
			if(info!=null && (info.getFixedAble()==null || info.getFixedAble().intValue()==1)) {//操作卡允许固化
				List<OpercardExecFixed> fixList = dbService.getOpercardExecFixed(cardId, catalogAlias, cardVerId);
				if(StringUtils.isNotEmpty(fixList)) {
					OpercardExecFixed fixed = fixList.get(0);
					int execCount = 0;
					if(fixed.getExecCount()!=null) {
						execCount = fixed.getExecCount();
					}
					if(execCount>=fixedNumber) {//达到了固化次数
						result = 1;
					}
					execCount++;//次数加1
					fixed.setExecCount(execCount);//记录次数
					dbService.saveOpercardExecFixed(fixed);
				}else {
					OpercardExecFixed fixed = new OpercardExecFixed();
					fixed.setCardId(cardId);//操作卡id
					fixed.setCatalogAlias(catalogAlias);//操作卡目录别名
					fixed.setCardVerId(cardVerId);//操作卡版本ID
					fixed.setExecCount(1);//记录次数
					dbService.saveOpercardExecFixed(fixed);
				}
			}
		}
		return result;
	}
	/**
	 * 判断是否为数字（处理科学计数法）
	 * @category 
	 * <AUTHOR> 
	 * @param val
	 * @return
	 */
	private static boolean judgeDouble(Object val) {		
//		return Coms.judgeDouble(val);
		if (Coms.judgeDouble(val)) {
			return true;
		} else {
			String so = String.valueOf(val);
			if (Coms.isFind(so, "^-?\\d+\\.?\\d+[E][-]?\\d+$")) {// 是科学计数法
				return true;
			} else {
				return false;
			}
		}
	}
	
	
	////////////////////////////////////////////////////以下为测试用代码///////////////////////
	/**
	 * 获取操作卡模型
	 * @category 
	 * <AUTHOR> 
	 * @param cardId 操作卡id
	 * @param catalogAlias 操作卡目录别名
	 * @return OperCardModelVo
	 */
	private OperCardModelVo getCardModel(String cardId,String catalogAlias){
		OperCardModelVo reuslt = null;
		if(this.testMode) {//测试模式
			reuslt= getCardModelTest(cardId,catalogAlias);//获取测试操作卡数据 
		}else {//正式应用模式
			reuslt= parseWord.queryCardIdInstrument(cardId, catalogAlias);//获取操作卡发布数据  
		}
		return reuslt;
	}
	/**
	 * 获取操作卡模型
	 * @category 
	 * <AUTHOR> 
	 * @param cardId 操作卡id
	 * @param catalogAlias 操作卡目录别名
	 * @param cardVerId 操作卡版本ID
	 * @return OperCardModelVo
	 */
	private OperCardModelVo getCardModelTest(String cardId,String catalogAlias){
		
		String postId_P_1="waicao1";//外操1
		String postId_P_2="waicao2";//外操2
		
		String postId_I_1="neicao1";//外操1
		String postId_I_2="neicao2";//外操2
		
		String postId_M="zhibanzhang1";//值班长
		
		OperCardModelVo reuslt = new OperCardModelVo(); 
		reuslt.setCardId("testCard1");
		reuslt.setCardName("苯乙烯设备操作卡");
		reuslt.setCatalogAlias("testcatalog1");
		reuslt.setCatalogName("换热器投用操作");
		reuslt.setCardVerId("version1.0");
		reuslt.setOrgCode("orgCode1");
		List<OpercardOperstep> opStep = new ArrayList<OpercardOperstep>();//步骤信息
		List<OpercardSteppost> opPost = new ArrayList<OpercardSteppost>();//步骤岗位信息
		List<OpercardInstrument> opInstrument = new ArrayList<OpercardInstrument>();//步骤仪表信息
		reuslt.setOpStep(opStep);
		reuslt.setOpPost(opPost);
		reuslt.setOpInstrument(opInstrument);
		String id = "ID00000";
		int i=1;
		int j=1;
		String stepId=null;
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "文字"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;font-weight:bold;font-size:40px;\">（一）A级 纲要</p>", null, null, null, null, null, null));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "图片"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;\">初始状态S0<br/>换热器处于空气状态，与系统隔离</p>", null, null, null, null, null, null));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "文字"+i, "备注"+i, i++, 1, "<p style=\"text-align:left;\">1.换热器拆盲板</p>", null, null, null, null, null, null));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "图片"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;\">状态S1<br/>换热器盲板拆除</p>", null, null, null, null, null, null));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "文字"+i, "备注"+i, i++, 1, "<p style=\"text-align:left;\">2.换热器置换</p>", null, null, null, null, null, null));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "图片"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;\">状态S2<br/>换热器置换合格</p>", null, null, null, null, null, null));

		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "文字"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;font-weight:bold;font-size:40px;\">（二）B级  操作</p>", null, null, null, null, null, null));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "图片"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;\">初始状态S0<br/>换热器处于空气状态，与系统隔离</p>", null, null, null, null, null, null));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "文字"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;font-weight:bold;font-size:30px;\">初始状态确认：</p>", null, null, null, null, null, null));
		
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">( P ) － 确认换热器验收合格，管箱、封头、管壳程接管法兰螺栓无松动、无缺损</p>", "(P)", "()","P", 0, "group1",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_1, "备注"+stepId));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_2, "备注"+stepId));
		opInstrument.add(this.createInstrument(stepId+"_"+j++, cardId, catalogAlias, stepId, "备注"+stepId, "确认描述", "", null, null, "textfield", ""));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">( P ) － 确认换热器与工艺系统隔离</p>", "(P)", "()","P", 0, "group1",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_1, "备注"+stepId));
		opInstrument.add(this.createInstrument(stepId+"_"+j++, cardId, catalogAlias, stepId, "备注"+stepId, "隔离时间（分钟）", "", null, null, "number", ""));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">( P ) － 确认换热器出入口线、副线、排凝阀完好</p>", "(P)", "()","P", 1, "group1",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_2, "备注"+stepId));
		opInstrument.add(this.createInstrument(stepId+"_"+j++, cardId, catalogAlias, stepId, "备注"+stepId, "提示", "", null, null, "tip", "检查出入口线、副线、排凝阀，不要有遗漏"));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">( I ) － 确认仪表投用正常且指示正确</p>", "(I)", "()","I", 1, "group1",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_1, "备注"+stepId));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_2, "备注"+stepId));
		opInstrument.add(this.createInstrument(stepId+"_"+j++, cardId, catalogAlias, stepId, "备注"+stepId, "仪表投用正常", "", null, null, "combo", "是,否"));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "文字"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;font-weight:bold;font-size:30px;\">1.换热器置换：</p>", null, null, null, null, null, null));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "提示"+i, "备注"+i, i++, 2, "<p style=\"text-align:center;\">提示卡<br/>" + 
				"1、蒸汽过热炉F-3001降温速度要严格按降温曲线控制，蒸汽过热炉F-3001降温期间，降温速度要严格按升温曲线控制，“A”室炉膛温度（TI-30607）、“B”室炉膛温度（TI-30605）温降不能超过50℃/h。<br/>" + 
				"2、第一脱氢反应器R-3001及第二脱氢反应器R-3002的入口温度与出口环带温度之间的温差不超过110℃。<br/></p>",null, null,null, null, "group2",null));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">( I )－确认乙苯/苯乙烯分离塔C-4001塔塔顶压力不允许超过0.125MPa_____</p>", "(I)", "()","I", 1, "group2",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_I_1, "备注"+stepId));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_I_2, "备注"+stepId));
		opInstrument.add(this.createInstrument(stepId+"_"+j++, cardId, catalogAlias, stepId, "备注"+stepId, "压力", "ssyb_tdyl", 0.125d, 0.1d, "raw", null));
		opInstrument.add(this.createInstrument(stepId+"_"+j++, cardId, catalogAlias, stepId, "备注"+stepId, "温度", "ssyb_tdwd", 200d, 0d, "raw", null));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">[ P ] － 吹扫蒸汽排凝</p>", "(P)", "()","P", 1, "group2",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_1, "备注"+stepId));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_2, "备注"+stepId));
		opInstrument.add(this.createInstrument(stepId+"_"+j++, cardId, catalogAlias, stepId, "备注"+stepId, "现场图片", "", null, null, "img", null));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">( P ) － 确认换热器管、壳程至排污线阀打开3～5圈</p>", "(P)", "()","P", 1, "group2",1));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">[ P ] － 关闭蒸汽排凝阀</p>", "[P]", "[]","P", 1, "group2",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_1, "备注"+stepId));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "图片"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;\">状态S1<br/>换热器盲板拆除</p>", null, null, null, null, null, null));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "文字"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;font-weight:bold;font-size:30px;\">2.换热器投用：</p>", null, null, null, null, null, null));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">< M > － 投用有毒有害介质的换热器，佩戴好防护用具</p>", "<M>", "<>","M", 1, "group3",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_M, "备注"+stepId));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "文字"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;font-weight:bold;font-size:30px;\">3.1充冷介质：</p>", null, null, null, null, null, null));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">[ P ] － 稍开换热器冷介质出口阀</p>", "[P]", "[]","P", 0, "group3",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_1, "备注"+stepId));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_2, "备注"+stepId));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">[ P ] － 稍开换热器冷介质入口阀</p>", "[P]", "[]","P", 1, "group3",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_1, "备注"+stepId));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_P_2, "备注"+stepId));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "文字"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;font-weight:bold;font-size:30px;\">3.2投用冷介质：</p>", null, null, null, null, null, null));
		stepId=id+i;
		opStep.add(this.createStep(stepId, reuslt.getCardId(), reuslt.getCatalogAlias(), "操作"+i, "备注"+i, i++, 3, "<p style=\"text-align:center;\">[ M ]  － 关闭换热器放空阀</p>", "[M]", "[]","M", 1, "group3",1));
		opPost.add(this.createPost(stepId+"_"+j++, cardId, catalogAlias, stepId, postId_M, "备注"+stepId));
		opStep.add(this.createStep(id+i, reuslt.getCardId(), reuslt.getCatalogAlias(), "图片"+i, "备注"+i, i++, 1, "<p style=\"text-align:center;\">状态S2<br/>换热器置换合格</p>", null, null, null, null, null, null));
		
		return reuslt;
	}
	//创建步骤
	private OpercardOperstep createStep(String id,String cardId,String catalogAlias,String name,String memo,Integer tmSort,
			Integer opType,String opContent,String opSign,String opSignType,String opSignPost,Integer requiredStep,String groupId,Integer confirmType) {
		OpercardOperstep reuslt = new OpercardOperstep();
		reuslt.setId(id);//步骤卡ID
		reuslt.setCardId(cardId);//操作卡ID
		reuslt.setCatalogAlias(catalogAlias);//操作卡目录别名
		reuslt.setName(name);//步骤名称
		reuslt.setTmUsed(1);//是否使用 1：使用；0：不使用
		reuslt.setTmSort(tmSort);//排序
		reuslt.setMemo(memo);//注释
		reuslt.setOpType(opType);//步骤类型 1：文本；2：警示；3：操作步骤
		reuslt.setOpContent(opContent);//操作内容
		reuslt.setOpSign(opSign);//操作标识 完整的标识 [P] [I] (M)
		reuslt.setOpSignType(opSignType);//操作标识类型 []:操作 ():确认 <>:安全
		reuslt.setOpSignPost(opSignPost);// 操作标识岗位 I:内操 P:外操 M班长
		reuslt.setRequiredStep(requiredStep);//必填步骤  1：必填；0：非必填 默认1 
		reuslt.setGroupId(groupId);//段落ID 提示卡使用
		reuslt.setConfirmType(confirmType);//确认方式 1手动确认 2身份证 3人脸识别
		return reuslt;
	}
	//创建岗位
	private OpercardSteppost createPost(String id,String cardId,String catalogAlias,String stepId,String postId,String memo) {
		OpercardSteppost reuslt = new OpercardSteppost();
		reuslt.setId(id);//步骤卡ID
		reuslt.setCardId(cardId);//操作卡ID
		reuslt.setCatalogAlias(catalogAlias);//操作卡目录别名
		reuslt.setStepId(stepId);//步骤ID
		reuslt.setPostId(postId);//岗位id
		reuslt.setMemo(memo);//注释
		return reuslt;
	}
	//创建仪表
	private OpercardInstrument createInstrument(String id,String cardId,String catalogAlias,String stepId,String memo,String tagName,String tagCode,Double upLimit,Double lowLimit,String comType,String propertyCode) {
		OpercardInstrument reuslt = new OpercardInstrument();
		reuslt.setId(id);//步骤卡ID
		reuslt.setCardId(cardId);//操作卡ID
		reuslt.setCatalogAlias(catalogAlias);//操作卡目录别名
		reuslt.setStepId(stepId);//步骤ID
		reuslt.setMemo(memo);//注释
		reuslt.setTagName(tagName);//仪表名称
		reuslt.setTagCode(tagCode);//仪表位号
		reuslt.setUpLimit(upLimit);//上限值
		reuslt.setLowLimit(lowLimit);//下限值
		reuslt.setComType(comType);//控件类型  raw：实时仪表；number：数字框；combo:下拉框；img:图片；tip：风险提示  textfield：文本框
		reuslt.setTmUsed(1);//是否使用
		reuslt.setPropertyCode(propertyCode);//扩展内容
		
		return reuslt;
	}
	/**
	 * 截断字符串，保证字符串的长度不超出byteLength
	 * 
	 * @param Str        (String) 要截断的字符串
	 * @param byteLength (int) 要保留的字符串byte长度(英文1长度 中文2长度)
	 * @return String 截断后的字符串
	 */
	private String subStringByte(String Str, int byteLength) {

		String result = Str;

		if (Str != null && Str.length() != 0 && byteLength > 0) {// 传入数据有效

			byte[] original = Str.getBytes();// 获得原始字符串的byte数组

			if (original.length > byteLength) {// 字符byte数组长度超出最大长度了,需要进行字符截取

				byte[] copy = new byte[byteLength + 1];// 生成长度加1的byte数组

				for (int i = 0; i < copy.length; i++) {

					copy[i] = original[i];

				}

				String copyStr = new String(copy);// 根据截断的字符byte数组生成字符串

				result = copyStr.substring(0, copyStr.length() - 1);// 去掉最后一个字符以保证截取的长度(保证最后一个字符不是被截断的字符)

			}

		}

		return result;
	}
	/**
	 * 更新图片的链接，确保手机端图片可见
	 * @category 更新图片的链接，确保手机端图片可见
	 * <AUTHOR> 
	 * @param text
	 * @return String
	 */
	@Override
	public String changeImageUrl(String text) {
		String result = text;
		if(StringUtils.isNotEmpty(result) && StringUtils.isNotEmpty(getTM4BaseUrl())) {//有地址
			result = result.replaceAll("(?<=['\"]{1})[.]?/files/", getTM4BaseUrl()+"/files/");
		}
		return result;
	}
	/**
	 * 获取TM4访问地址
	 * @category 
	 * <AUTHOR>
	 */
	private String getTM4BaseUrl() {
		if(this.TM4BaseUrl==null) {
			this.TM4BaseUrl = PropertyUtils.getProperty("app.Tm4Url");
			if(StringUtils.isEmpty(this.TM4BaseUrl)) {
				this.TM4BaseUrl="";//没读到配置文件，就不读了
			}
		}
		return this.TM4BaseUrl;
	}


}
