package com.yunhesoft.joblist.generater;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.joblist.entity.dto.JobFrequencyCalcDto;
import com.yunhesoft.joblist.entity.dto.JobInstanceListDto;
import com.yunhesoft.joblist.entity.dto.StandardJobLibQueryDto;
import com.yunhesoft.joblist.entity.po.*;
import com.yunhesoft.joblist.entity.vo.JobFrequencyCalcVo;
import com.yunhesoft.joblist.entity.vo.JoblistActivityExampleVo;
import com.yunhesoft.joblist.service.IJobFrequencyCalcService;
import com.yunhesoft.joblist.service.IJobGeneraterService;
import com.yunhesoft.joblist.service.IJoblistMethodService;
import com.yunhesoft.joblist.service.IStandardJobLibConfigService;
import com.yunhesoft.joblist.utils.JoblistActivityCacheUtils;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgPostService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动生成抽象类
 *
 * <AUTHOR>
 * @date 2024/6/26
 * @params
 * @return
 */
@Log4j2
@Component
public abstract class AbstractJobGenerater {
    protected IJoblistMethodService methodService;
    protected IStandardJobLibConfigService jobLibConfigService;
    protected ISysEmployeeOrgPostService orgPostService;
    protected EntityService dao;
    protected ISysOrgService orgService;
    protected ISysEmployeeInfoService employeeInfoService;
    protected IJobGeneraterService jobGeneraterService;
    protected ISysEmployeeOrgPostService postService;
    protected IUnitMethodService unitMethodService;
    protected Long timeout;
    @Setter
    protected Boolean insertBeforeDel;
    @Setter
    protected String dataOrigin;

    public Map<String, Integer> statusMap = new HashMap<>();
    //此变量为对象变量  此类是由实现类new的实例对象 在一个对象实例中为了提升效率使用了此列表缓存数据
    public List<JoblistActivityExample> exampleCacheList = new ArrayList<>();
    public AbstractJobGenerater() {
        methodService = SpringUtils.getBean(IJoblistMethodService.class);
        orgPostService = SpringUtils.getBean(ISysEmployeeOrgPostService.class);
        jobLibConfigService = SpringUtils.getBean(IStandardJobLibConfigService.class);
        orgService = SpringUtils.getBean(ISysOrgService.class);
        dao = SpringUtils.getBean(EntityService.class);
        employeeInfoService = SpringUtils.getBean(ISysEmployeeInfoService.class);
        jobGeneraterService = SpringUtils.getBean(IJobGeneraterService.class);
        postService = SpringUtils.getBean(ISysEmployeeOrgPostService.class);
        unitMethodService = SpringUtils.getBean(IUnitMethodService.class);
    }

    /**
     * 获取班次信息
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/1
     * @params
     */
    public List<ShiftForeignVo> getShiftInfo(String orgId, Integer beforeHandDay) {
        //获取子机构
        ISysOrgService orgService = SpringUtils.getBean(ISysOrgService.class);
        IShiftService shiftService = SpringUtils.getBean(IShiftService.class);
        List<SysOrg> orgList = orgService.getOrgList(orgId);
        if (StringUtils.isEmpty(orgList)) {
            return null;
        }
        //过滤掉自己  广东扁平化 不能过滤掉自己
        List<SysOrg> children;
//        children = orgList.stream().filter(i -> !i.getId().equals(orgId)).collect(Collectors.toList());
//        if (StringUtils.isEmpty(children)) {
            children = orgList;
//        }
        Date start = new Date();
        Date end;
        if (beforeHandDay != 0) {
            end = DateTimeUtils.doDate(start, beforeHandDay);
        } else {
            end = start;
        }
        SimpleDateFormat sdfbc = new SimpleDateFormat("yyyy-MM-dd");
        String bc = sdfbc.format(end);
        //获取当班信息
        List<ShiftForeignVo> shilftList = shiftService.getAllShiftDataByrq(bc);
        if (StringUtils.isEmpty(shilftList)) {
            return null;
        }
        List<String> finalChildren = children.stream().map(SysOrg::getId).collect(Collectors.toList());
        shilftList = shilftList.stream().filter(i -> finalChildren.contains(i.getOrgCode())).collect(Collectors.toList());
        if (StringUtils.isEmpty(shilftList)) {
            return null;
        }
        return shilftList;
    }

    /**
     * 获取班次信息
     *
     * @param orgId   机构代码
     * @param dateStr 日期字符串
     * @return
     */
    public List<ShiftForeignVo> getShiftInfo(String orgId, String dateStr) {
        //获取子机构
        ISysOrgService orgService = SpringUtils.getBean(ISysOrgService.class);
        IShiftService shiftService = SpringUtils.getBean(IShiftService.class);
        List<SysOrg> orgList = orgService.getOrgList(orgId);
        if (StringUtils.isEmpty(orgList)) {
            return null;
        }
        //过滤掉自己
        List<SysOrg> children;
//        children = orgList.stream().filter(i -> !i.getId().equals(orgId)).collect(Collectors.toList());
//        if (StringUtils.isEmpty(children)) {
            children = orgList;
//        }
        //获取当班信息
        List<ShiftForeignVo> shilftList = shiftService.getAllShiftDataByrq(dateStr);
        if (StringUtils.isEmpty(shilftList)) {
            return null;
        }
        List<String> finalChildren = children.stream().map(SysOrg::getId).collect(Collectors.toList());
        shilftList = shilftList.stream().filter(i -> finalChildren.contains(i.getOrgCode())).collect(Collectors.toList());
        if (StringUtils.isEmpty(shilftList)) {
            return null;
        }
        return shilftList;
    }

    /**
     * 构建生成配置map 根据频次获取时间范围以及活动次数
     *
     * @param activityProperties
     * @param beforeHandDay
     * @return
     * <AUTHOR>
     * @date 2024/6/26
     * @params
     */
    public JSONObject builderGeneraterConfig(List<JoblistActivityProperties> activityProperties, Integer beforeHandDay) {
        return this.builderGeneraterConfig(activityProperties, null, beforeHandDay);
    }

    public JSONObject builderGeneraterConfig(List<JoblistActivityProperties> activityProperties, String orgId, Integer beforeHandDay) {
        return this.builderGeneraterConfig(activityProperties, orgId, beforeHandDay, null);
    }

    public JSONObject builderGeneraterConfig(List<JoblistActivityProperties> activityProperties, String orgIdOrgin, Integer beforeHandDay, String dateStr) {
        IJobFrequencyCalcService frequencyCalcService = SpringUtils.getBean(IJobFrequencyCalcService.class);
        JSONObject configMap = new JSONObject();
        JobFrequencyCalcDto calcDto = new JobFrequencyCalcDto();
        String orgId;
        for (JoblistActivityProperties activityProperty : activityProperties) {
            //不定期活动不生成
            if (StringUtils.isEmpty(activityProperty.getFrequencyid()) || "none".equals(activityProperty.getFrequencyid())) {
                continue;
            }
            //调用获取频次
            JSONObject activityConfig = new JSONObject();
            if (StringUtils.isEmpty(orgIdOrgin)) {
                //没传 直接使用配置上的
                orgId = activityProperty.getOrgid();
            }else{
                //传了 使用传入的
                orgId = orgIdOrgin;
            }
            List<ShiftForeignVo> shiftInfo;
            if (StringUtils.isNotEmpty(dateStr)) {
                //按照指定日期获取班次
                shiftInfo = getShiftInfo(orgId, dateStr);
            } else {
                //按照当前日期+预生成时间
                shiftInfo = getShiftInfo(orgId, beforeHandDay);
            }
            if (StringUtils.isEmpty(shiftInfo)) {
                continue;
            }
            //根据频次和班次获取整理 本次生成配置信息
            JSONArray shiftList = getConfigByShiftAndFrequency(activityProperty, shiftInfo, calcDto, frequencyCalcService);
            activityConfig.put("shiftList", shiftList);
            activityConfig.put("activityStart", activityProperty.getBeginDate());
            activityConfig.put("activityEnd", activityProperty.getEndDate());
            activityConfig.put("noEnd", activityProperty.getNoEndDate());
            configMap.put(activityProperty.getId(), activityConfig);
        }
        return configMap;
    }

    /**
     * 根据频次和班次获取整理 本次生成配置信息
     * @param activityProperty
     * @param shiftInfo
     * @param calcDto
     * @param frequencyCalcService
     * @return {@link JSONArray }
     */
    private JSONArray getConfigByShiftAndFrequency(JoblistActivityProperties activityProperty, List<ShiftForeignVo> shiftInfo, JobFrequencyCalcDto calcDto, IJobFrequencyCalcService frequencyCalcService) {
        JSONArray shiftList = new JSONArray();
        for (ShiftForeignVo shiftForeignVo : shiftInfo) {
            calcDto.setBeginTime(shiftForeignVo.getSbsj());
            calcDto.setEndTime(shiftForeignVo.getXbsj());
            calcDto.setWorkDate(shiftForeignVo.getSbsj().substring(0, 10));
            calcDto.setCycleSchemeID(activityProperty.getFrequencyid());
            JSONArray frequencyArray = new JSONArray();
            List<JobFrequencyCalcVo> cycleSchemeFrequency = frequencyCalcService.getCycleSchemeFrequency(calcDto);
            if (StringUtils.isNotEmpty(cycleSchemeFrequency)) {
                int index = 1;
                for (JobFrequencyCalcVo jobFrequencyCalcVo : cycleSchemeFrequency) {
                    JSONObject frequencyObj = new JSONObject();
                    frequencyObj.put("beginDate", jobFrequencyCalcVo.getBeginTime());
                    frequencyObj.put("endDate", jobFrequencyCalcVo.getEndTime());
                    frequencyObj.put("frequecyNo", jobFrequencyCalcVo.getFrequencyNo());
                    if (StringUtils.isNotEmpty(jobFrequencyCalcVo.getFrequencyNo())) {
                        if (Coms.judgeInt(jobFrequencyCalcVo.getFrequencyNo())) {
                            // 是数字为 次数频次  次数频次1
                            frequencyObj.put("frequecyType", 1);
                            Integer no = Integer.valueOf(jobFrequencyCalcVo.getFrequencyNo());
                            frequencyObj.put("recordNo", no);
                            String timeByFrequencyNo = getTimeByFrequencyNo(jobFrequencyCalcVo.getBeginTime(), no);
                            frequencyObj.put("beginDate", timeByFrequencyNo);
                        } else {
                            // 时间频次0   次数频次1
                            frequencyObj.put("frequecyType", 0);
                            frequencyObj.put("recordNo", index++);
                        }
                    }
                    frequencyObj.put("orgcode", shiftForeignVo.getOrgCode());
                    frequencyObj.put("shiftClassCode", shiftForeignVo.getShiftClassCode());
                    frequencyObj.put("tbrq", shiftForeignVo.getTbrq());
                    frequencyArray.add(frequencyObj);
                }
            }
            if (frequencyArray.size() > 0) {
                JSONObject config = new JSONObject();
                config.put("frequency", frequencyArray);
                config.put("orgcode", shiftForeignVo.getOrgCode());
                config.put("sbsj", shiftForeignVo.getSbsj());
                config.put("xbsj", shiftForeignVo.getXbsj());
                config.put("tbrq", shiftForeignVo.getTbrq());
                config.put("shiftClassCode", shiftForeignVo.getShiftClassCode());
                shiftList.add(config);
            }
        }
        return shiftList;
    }

    private String getTimeByFrequencyNo(String begin, Integer no) {
        SimpleDateFormat sdf;
        if (begin.length() > 10) {
            sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        } else {
            sdf = new SimpleDateFormat("yyyy-MM-dd");
        }
        try {

            Date parse = sdf.parse(begin);
            Date date = DateTimeUtils.doSecond(parse, no - 1);
            String format = sdf.format(date);
            return format;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 活动实例数据的生成
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    public int getActivityExampleSaveList(JoblistActivityProperties joblistActivityProperties, int max, List<JoblistActivityExample> saveList, JSONArray configList, List<JobListExampleDutyPerson> dutyPersonList, List<JobListExampleDutyPerson> saveDutyPersonList, Map<String, List<JoblistPersonBind>> formBindMapByActivityId, List<JoblistExampleFormBind> bindList) {
        if (configList == null || configList.size() == 0) {
            //无配置
            return -9999;
        }
        //根据配置id获取其机构岗位
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < configList.size(); i++) {
            //按照班次生成
            JSONObject shift = configList.getJSONObject(i);
            String sbsj = shift.getString("sbsj");
            String xbsj = shift.getString("xbsj");
            String shiftClassCode = shift.getString("shiftClassCode");
            String orgcode = shift.getString("orgcode");
            String tbrq = shift.getString("tbrq");
            ++max;
            JoblistActivityExample activityExampleParent = this.getMainActivity(joblistActivityProperties,tbrq
                    ,sbsj,xbsj,shiftClassCode
                ,orgcode,max);
            //生成一个主任务
            if(!activityExampleParent.getSkipInsert()){
                //跳过不需要添加的数据
                saveList.add(activityExampleParent);
            }
            //获取频次
            JSONArray frequecyList = shift.getJSONArray("frequency");
            if("inner".equals(this.dataOrigin)) {
                //根据频次生成子任务实例
                max = productChildrenActivityExampleByFrequency(joblistActivityProperties, max, saveList, dutyPersonList, saveDutyPersonList, formBindMapByActivityId, bindList, frequecyList, tbrq, activityExampleParent, orgcode);
            }else{
                //增加临时任务时需要重新确定主任务状态
                if(activityExampleParent.getActivityStatus() ==2 ){
                    //如果状态为已完成 此主任务一定是已存在数据库的  需要对状态变动，加入状态变动集合 最后保存时统一处理
                    this.statusMap.put(activityExampleParent.getId(),-1);
                    exampleCacheList.add(activityExampleParent);
                }
                //增加的临时任务
                JSONObject frequecy = frequecyList.getJSONObject(0);
                String activityDate = tbrq;
                String beginDate = String.valueOf(frequecy.get("beginDate"));
                Integer frequencyType = Integer.valueOf(String.valueOf(frequecy.get("frequecyType")));
                JoblistActivityExample activityExample = ObjUtils.copyTo(activityExampleParent, JoblistActivityExample.class);
                activityExample.setJobName("不定期");
                activityExample.setDataOrigin("temp");
                Integer jobNo = Integer.valueOf(String.valueOf(frequecy.get("recordNo")));
                activityExample.setId(TMUID.getUID());
                String endDate = String.valueOf(frequecy.get("endDate"));
                //活动时间
                //临时任务应该是当前时间 作为活动时间
                activityExample.setActivityDate(sdf.format(new Date()));
                //频次开始时间
                activityExample.setBeginDate(beginDate);
                activityExample.setSkipInsert(false);
                //频次结束时间
                activityExample.setEndDate(endDate);
                activityExample.setPid(activityExampleParent.getId());
                activityExample.setIsParent(0);
                activityExample.setStandardDuration(joblistActivityProperties.getStandardDuration());
                activityExample.setTmSort(++max);
                activityExample.setRecordid(joblistActivityProperties.getRecordid());
                activityExample.setRecordType(joblistActivityProperties.getRecordType());
                activityExample.setJobNo(jobNo);
                activityExample.setTbrq(tbrq);
                activityExample.setFrequencyType(frequencyType);
                activityExample.setActivityStatus(0);
                //给任务实例分配人员
                allocationPersonToActivityExample(dutyPersonList, saveDutyPersonList, activityExample, orgcode);
                //表单绑定
                jobExampleFormBind(joblistActivityProperties, formBindMapByActivityId, bindList, activityExample);
                saveList.add(activityExample);
            }
        }
        return max;
    }

    /**
     * //根据频次生成子任务实例
     * <AUTHOR>
     * @date 2025/5/15
     * @params
     *
    */
    private int productChildrenActivityExampleByFrequency(JoblistActivityProperties joblistActivityProperties, int max, List<JoblistActivityExample> saveList, List<JobListExampleDutyPerson> dutyPersonList, List<JobListExampleDutyPerson> saveDutyPersonList, Map<String, List<JoblistPersonBind>> formBindMapByActivityId, List<JoblistExampleFormBind> bindList, JSONArray frequecyList, String tbrq, JoblistActivityExample activityExampleParent, String orgcode) {
        for (int j = 0; j < frequecyList.size(); j++) {
            JSONObject frequecy = frequecyList.getJSONObject(j);
            String activityDate;
            String beginDate = String.valueOf(frequecy.get("beginDate"));
            Integer frequencyType = Integer.valueOf(String.valueOf(frequecy.get("frequecyType")));
            if (1 == frequencyType) {
                //次数频次
                //频次开始时间为 开始时间
                activityDate = beginDate;
            } else if (2 == frequencyType) {
                activityDate = tbrq;
            } else {
                //时间频次
                activityDate = String.valueOf(frequecy.get("frequecyNo"));
            }
            Integer jobNo = Integer.valueOf(String.valueOf(frequecy.get("recordNo")));
            String endDate = String.valueOf(frequecy.get("endDate"));
            //生成子活动
            JoblistActivityExample activityExample = ObjUtils.copyTo(activityExampleParent, JoblistActivityExample.class);
            activityExample.setId(TMUID.getUID());
            //活动时间
            activityExample.setActivityDate(activityDate);
            //频次开始时间
            activityExample.setBeginDate(beginDate);
            //频次结束时间
            activityExample.setEndDate(endDate);
            activityExample.setPid(activityExampleParent.getId());
            activityExample.setIsParent(0);
            activityExample.setStandardDuration(joblistActivityProperties.getStandardDuration());
            activityExample.setTmSort(++max);
            activityExample.setRecordid(joblistActivityProperties.getRecordid());
            activityExample.setRecordType(joblistActivityProperties.getRecordType());
            activityExample.setJobNo(jobNo);
            activityExample.setTbrq(tbrq);
            activityExample.setFrequencyType(frequencyType);
            activityExample.setSkipInsert(false);
            //给任务实例分配人员
            allocationPersonToActivityExample(dutyPersonList, saveDutyPersonList, activityExample, orgcode);
            //表单绑定
            jobExampleFormBind(joblistActivityProperties, formBindMapByActivityId, bindList, activityExample);
            if(!activityExample.getSkipInsert()){
                saveList.add(activityExample);
            }
        }
        return max;
    }

    /**
     * 获取针对本次生成 所需要的主活动
     * <AUTHOR>
     * @date 2025/5/15
     * @params
     * @return
     *
    */
    private JoblistActivityExample getMainActivity(JoblistActivityProperties joblistActivityProperties,
                                                          String tbrq, String sbsj, String xbsj,
                                                          String shiftClassCode, String orgcode, int max){
        if(!"inner".equals(this.dataOrigin)){
            //根据参数获取已经生成的主活动
            JobInstanceListDto dto = new JobInstanceListDto();
            dto.setActivityId(joblistActivityProperties.getId());
            dto.setSbsj(sbsj);
            dto.setXbsj(xbsj);
            dto.setShiftId(shiftClassCode);
            dto.setOrgCode(orgcode);
            dto.setJobType(joblistActivityProperties.getResponsibilityType());
            List<JoblistActivityExample> joblistActivityExamplesByQuery = jobGeneraterService.getJoblistActivityExamplesByQuery(dto);
            if(StringUtils.isNotEmpty(joblistActivityExamplesByQuery)){
                JoblistActivityExample joblistActivityExample = joblistActivityExamplesByQuery.get(0);
                joblistActivityExample.setSkipInsert(true);
                return joblistActivityExample;
            }
        }
        //获取一个新任务
        JoblistActivityExample joblistActivityExample = this.productNewMainActivity(joblistActivityProperties, tbrq, sbsj, xbsj, shiftClassCode, orgcode, max);
        joblistActivityExample.setSkipInsert(false);
        return joblistActivityExample;
    }
    /**
     * 生成一个新的主任务
     * <AUTHOR>
     * @date 2025/5/15
     * @params
     * @return
     *
    */
    private JoblistActivityExample productNewMainActivity(JoblistActivityProperties joblistActivityProperties,
                                                          String tbrq, String sbsj, String xbsj,
                                                          String shiftClassCode, String orgcode, int max){
        JoblistActivityExample activityExampleParent = ObjUtils.copyTo(joblistActivityProperties, JoblistActivityExample.class);
        activityExampleParent.setId(TMUID.getUID());
        activityExampleParent.setTbrq(tbrq);
        activityExampleParent.setActivityId(joblistActivityProperties.getId());
        activityExampleParent.setActivityStatus(0);
        activityExampleParent.setActivityDate(sbsj);
        activityExampleParent.setBeginDate(sbsj);
        activityExampleParent.setEndDate(xbsj);
        activityExampleParent.setSbsj(sbsj);
        activityExampleParent.setXbsj(xbsj);
        activityExampleParent.setShiftClassCode(shiftClassCode);
        activityExampleParent.setOrgCode(orgcode);
        activityExampleParent.setPOrgCode(joblistActivityProperties.getOrgid());
        activityExampleParent.setRecordid(joblistActivityProperties.getRecordid());
        activityExampleParent.setIsParent(1);
        activityExampleParent.setStandardDuration(joblistActivityProperties.getStandardDuration());
        activityExampleParent.setTmSort(max);
        activityExampleParent.setResponsibilityType(joblistActivityProperties.getResponsibilityType());
        activityExampleParent.setJobRequirement(joblistActivityProperties.getJobRequirement());
        activityExampleParent.setTmUsed(1);
        activityExampleParent.setDataOrigin(dataOrigin);
        return activityExampleParent;
    }

    private static void jobExampleFormBind(JoblistActivityProperties joblistActivityProperties, Map<String, List<JoblistPersonBind>> formBindMapByActivityId, List<JoblistExampleFormBind> bindList, JoblistActivityExample activityExample) {
        List<JoblistPersonBind> joblistPersonBinds = formBindMapByActivityId.get(joblistActivityProperties.getId());
        if (StringUtils.isNotEmpty(joblistPersonBinds)) {
            for (JoblistPersonBind joblistPersonBind : joblistPersonBinds) {
                JoblistExampleFormBind bean = new JoblistExampleFormBind();
                bean.setId(TMUID.getUID());
                bean.setOrgCode(joblistActivityProperties.getOrgid());
                bean.setActivityExampleId(activityExample.getId());
                bean.setFormId(joblistPersonBind.getBindid());
                bean.setFormName(joblistPersonBind.getBindname());
                bindList.add(bean);
            }
        }
    }

    /**
     * 生成实例
     *
     * @return useRecord是否使用生成记录功能 默认是开启
     * <AUTHOR>
     * @date 2024/8/5
     * @params
     */
    public abstract List<JoblistActivityExampleVo> generateActivityInstance(JSONObject activityIds, Boolean useRecord, List<JoblistActivityProperties> activityProperties);

    /**
     * 给任务实例分配人员
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    public abstract void allocationPersonToActivityExample(List<JobListExampleDutyPerson> dutyPersonList, List<JobListExampleDutyPerson> saveDutyPersonList, JoblistActivityExample activityExample, String orgcode);

    /**
     * 获取可以执行的频次配置
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */

    public JSONArray getCanExecConfig(JoblistActivityProperties joblistActivityProperties, JSONArray preConfigList) {
        //根据频次 计算设定次数
        Map<String, Integer> frequencyMap = computerFrequencyByPerconfig(preConfigList);
        //根据频次获取需要生成的频次列表
        JSONArray configList = new JSONArray();
        for (int i = 0; i < preConfigList.size(); i++) {
            JSONObject shift = preConfigList.getJSONObject(i);
            JSONArray frequencyList = shift.getJSONArray("frequency");
            JSONArray canFrequency = new JSONArray();
            for (int j = 0; j < frequencyList.size(); j++) {
                JSONObject frequencyItem = frequencyList.getJSONObject(j);
                String frequncyTime = String.valueOf(frequencyItem.get("frequecyNo"));
                String shiftClassCode = String.valueOf(frequencyItem.get("shiftClassCode"));
                String beginDate = frequencyItem.getString("beginDate");
                String endDate = frequencyItem.getString("endDate");
                Boolean b = jobGeneraterService.taskCanGenerater(joblistActivityProperties.getId(), beginDate, endDate, frequncyTime, frequencyMap.get(shiftClassCode+"_"+frequncyTime));
                if (b) {
                    //这个频次可以生成数据
                    canFrequency.add(frequencyItem);
                }
            }
            if (canFrequency.size() > 0) {
                JSONObject canShift = JSONObject.parseObject(JSONObject.toJSONString(shift));
                canShift.put("frequency", canFrequency);
                configList.add(canShift);
            }
        }
        if (configList.size() == 0) {
            return null;
        }
        return configList;
    }

    /**
     * 根据频次 计算设定次数
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    public Map<String, Integer> computerFrequencyByPerconfig(JSONArray preConfigList) {
        //根据频次 计算设定次数
        Map<String, Integer> frequencyMap = new HashMap<>();
        for (int i = 0; i < preConfigList.size(); i++) {
            JSONObject shift = preConfigList.getJSONObject(i);
            JSONArray frequency = shift.getJSONArray("frequency");
            for (int j = 0; j < frequency.size(); j++) {
                String frequncyTime = String.valueOf(frequency.getJSONObject(j).get("frequecyNo"));
                String shiftClassCode = String.valueOf(frequency.getJSONObject(j).get("shiftClassCode"));
                frequencyMap.put(shiftClassCode+"_"+frequncyTime, frequencyMap.getOrDefault(frequncyTime, 0) + 1);
            }
        }
        return frequencyMap;
    }

    /**
     * 保存到数据库
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    public void finishSave(JSONObject activityIds, List<JoblistActivityExample> saveList, List<JobListExampleDutyPerson> saveDutyPersonList, List<JoblistActivityExampleVo> activityExampleVos, List<JoblistExampleFormBind> saveBindList, Boolean useRecord) {
        //保存任务实例
        boolean b = false;
        //保存活动实例数据
        b = saveActivityExampleList(saveList, b);
        if(this.statusMap!=null && !this.statusMap.isEmpty()){
            jobGeneraterService.updateActivityStatusByActivityId(this.statusMap,false,()-> this.exampleCacheList);
        }
        //保存责任人数据
        jobGeneraterService.saveDutyPerson(saveDutyPersonList,insertBeforeDel);
        //保存表单绑定数据
        saveFormBindList(saveBindList,insertBeforeDel);
        if (b) {
            //保存成功后
            if (useRecord) jobGeneraterService.taskGeneraterRecorder(activityIds, saveList);
            for (JoblistActivityExample joblistActivityExample : saveList) {
                JoblistActivityExampleVo vo = ObjUtils.copyTo(joblistActivityExample, JoblistActivityExampleVo.class);
                activityExampleVos.add(vo);
            }
        }
    }

    private void saveFormBindList(List<JoblistExampleFormBind> saveBindList,Boolean insertBeforeDel) {
        if (StringUtils.isNotEmpty(saveBindList)) {
            //插入列表去重
            saveBindList = saveBindList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() ->
                                    new TreeSet<>(
                                            Comparator.comparing(
                                                    item -> item.getActivityExampleId() + "_" + item.getFormId()
                                            ))),  ArrayList::new));
            //删除
            if(insertBeforeDel) {
                Where where = null;
                List<String> keys = saveBindList.stream()
                        .map(item ->
                                item.getOrgCode() + "_"
                                        + item.getActivityExampleId()
                        )
                        .collect(Collectors.toList());
                for (int i = 0; i < keys.size(); i++) {
                    if (where == null) {
                        where = Where.create();
                    }
                    String key = keys.get(i);
                    String[] split = key.split("_");
                    String orgCode = split[0];
                    String activityExampleId = split[1];
                    where.lb();
                    where.eq(JobListExampleDutyPerson::getOrgCode, orgCode);
                    where.eq(JobListExampleDutyPerson::getActivityExampleId, activityExampleId);
                    where.rb();
                    if (i != keys.size() - 1) {
                        where.or();
                    }
                }
                dao.rawDeleteByWhere(JobListExampleDutyPerson.class, where);
                dao.insertBatch(saveBindList);
                JoblistActivityCacheUtils.loadActivityFormBindInCache(saveBindList);
            }else{
                dao.insertBatch(saveBindList);
                JoblistActivityCacheUtils.loadActivityFormBindInCache(saveBindList);

            }
        }
    }

    private boolean saveActivityExampleList(List<JoblistActivityExample> saveList, boolean b) {
        if (StringUtils.isNotEmpty(saveList)) {
            List<JoblistActivityExample> insertList = saveList
                    .stream()
                    .filter(item-> item.getSkipInsert()==null || !item.getSkipInsert())
                    .collect(Collectors.toList());
            List<JoblistActivityExample> updateList = saveList
                    .stream()
                    .filter(item-> item.getSkipInsert()!=null && item.getSkipInsert())
                    .collect(Collectors.toList());
            if(StringUtils.isNotEmpty(insertList)){
                if(insertBeforeDel){
                    b = deleteAndInsertActivityExample(insertList);
                }else{
                    //直接插入
                    b = this.insertActitvityListToDbAndRedis(insertList);
                }
            }
            if(StringUtils.isNotEmpty(updateList)){
                b = dao.updateByIdBatch(updateList) > 0;
            }
        }
        return b;
    }

    private boolean deleteAndInsertActivityExample(List<JoblistActivityExample> insertList) {
        //删除并插入  删除相同的 活动 机构 班次 活动时间 提报日期 的活动
        //构建删除条件
        List<Where> whereList = new ArrayList<>();
        //插入列表需要去重
        insertList = insertList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() ->
                                new TreeSet<>(
                                        Comparator.comparing(
                                                item -> item.getActivityId() + "_" + item.getOrgCode() + "_"
                                + item.getShiftClassCode()+"_"+item.getIsParent()
                                + "_" + item.getSbsj() + "_" + item.getActivityDate()
                                + "_" + item.getTbrq()
                                        ))),  ArrayList::new));
        List<String> activityKey = insertList.stream()
                .map(
                        item -> item.getActivityId() + "_" + item.getOrgCode() + "_"
                                + item.getShiftClassCode()+"_"+item.getIsParent()
                                + "_" + item.getSbsj() + "_" + item.getActivityDate()
                                + "_" + item.getTbrq()).distinct().collect(Collectors.toList());

        Where where = null;
        for (int i = 0; i < activityKey.size(); i++) {
            if(where==null){
                where = Where.create();
            }
            String exampleKey = activityKey.get(i);
            String[] key = exampleKey.split("_");
            String activityId = key[0];
            String orgCode = key[1];
            String shiftCode = key[2];
            String isParent = key[3];
            String sbsj = key[4];
            String activityDate = key[5];
            String tbrq = key[6];
            where.lb();
            where.eq(JoblistActivityExample::getActivityId, activityId);
            where.eq(JoblistActivityExample::getOrgCode, orgCode);
            where.eq(JoblistActivityExample::getShiftClassCode, shiftCode);
            where.eq(JoblistActivityExample::getIsParent, isParent);
            where.eq(JoblistActivityExample::getSbsj, sbsj);
            where.eq(JoblistActivityExample::getActivityDate, activityDate);
            where.eq(JoblistActivityExample::getTbrq, tbrq);
            where.rb();
            if(i!=activityKey.size()-1){
                where.and();
            }
        }
        List<JoblistActivityExample> joblistActivityExamples = dao.rawQueryListByWhere(JoblistActivityExample.class, where);
        JoblistActivityCacheUtils.clearActivityExampleCache(joblistActivityExamples, true);
        dao.rawDeleteByWhere(JoblistActivityExample.class,where);
        return this.insertActitvityListToDbAndRedis(insertList);

    }

    private boolean insertActitvityListToDbAndRedis(List<JoblistActivityExample> insertList) {
        //保存到数据库列表
        boolean flag = dao.insertBatch(insertList,200)>0;
        //更新缓存
        JoblistActivityCacheUtils.loadActivityExampleInCache(insertList);
        return flag;

    }


    /**
     * 获取实例最大排序
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    public int getExampleMaxSort() {
        //获取最大排序
        int max = 0;
        Long maxSort = dao.findMaxId(JoblistActivityExample.class, JoblistActivityExample::getTmSort);
        if (maxSort != null) {
            max = maxSort.intValue();
        }
        return max;
    }

    /**
     * 根据活动生成配置获取活动属性
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    public List<JoblistActivityProperties> getActivityPropertiesByConfigs(JSONObject activityIds, List<String> activityIdList) {
        //根据活动生成配置生成活动

        //根据活动配置获取活动属性id列表
        jobGeneraterService.getActivityIdListByGeneraterConfig(activityIds, activityIdList);

        //根据activityIds 查询活动属性表
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setIdList(activityIdList);
        queryDto.setTmUsed(1);
        List<JoblistActivityProperties> activityPropertiesList = methodService.getJoblistActivityPropertiesList(queryDto);
        return activityPropertiesList;
    }

    /**
     * 任务生成
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    public abstract List<JoblistActivityExampleVo> generateActivityInstance(JSONObject activityIds, List<JoblistActivityProperties> activityProperties);


    /**
     * 生成入口
     *
     * @return
     * <AUTHOR>
     * @date 2024/6/26
     * @params
     */
    public List<JoblistActivityExampleVo> generate(List<JoblistActivityProperties> activityProperties, Integer beforeHandDay) throws Exception {
        //根据批次构建生成配置
        JSONObject configMap = this.builderGeneraterConfig(activityProperties, beforeHandDay);
        //生成实例
        return this.generateActivityInstance(configMap, activityProperties);
    }
    public List<JoblistActivityExampleVo> generate(List<JoblistActivityProperties> activityProperties, Integer beforeHandDay,Boolean useRecord) throws Exception {
        //根据批次构建生成配置
        JSONObject configMap = this.builderGeneraterConfig(activityProperties, beforeHandDay);
        //生成实例
        return this.generateActivityInstance(configMap,useRecord, activityProperties);
    }

    /**
     * 不定期工作添加入口
     *
     * @return
     * <AUTHOR>
     * @date 2024/6/26
     * @params
     */
    public List<JoblistActivityExampleVo> generateIrregular(List<String> ids, ShiftForeignVo shift) {
        if (StringUtils.isEmpty(ids)) {
            return null;
        }
        //获取活动配置
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setIdList(ids);
        queryDto.setTmUsed(1);
        List<JoblistActivityProperties> activityProperties = methodService.getJoblistActivityPropertiesList(queryDto);
        //查询活动名称
        MethodQueryDto dto = new MethodQueryDto();
        dto.setIdList(ids);
        List<Costuint> unitList = unitMethodService.getCostuintList(dto);
        if(StringUtils.isNotEmpty(unitList)){
            Map<String, String> nameMap = unitList.stream().collect(Collectors.toMap(Costuint::getId, Costuint::getName));
            activityProperties.stream().forEach(i-> i.setJobName(nameMap.get(i.getId())));
        }
        //根据批次构建生成配置
        if (StringUtils.isEmpty(activityProperties)) {
            return null;
        }
        //获取班次信息
        //根据不定期配置生成活动数据
        JSONObject configMap = new JSONObject();
        for (JoblistActivityProperties activityProperty : activityProperties) {
            JSONObject activityConfig = new JSONObject();
            JSONObject frequencyObj = new JSONObject();
            //构建频次参数 ,不定期实际上没有频次参数，无实际意义，但是为了数据完整性，给定默认值
            frequencyObj.put("frequecyType", 2);
            frequencyObj.put("recordNo", 1);
            frequencyObj.put("orgcode", shift.getOrgCode());
            frequencyObj.put("shiftClassCode", shift.getShiftClassCode());
            frequencyObj.put("beginDate", shift.getSbsj());
            JSONArray frequencyArray = new JSONArray();
            frequencyArray.add(frequencyObj);
            //g
            JSONObject config = new JSONObject();
            config.put("frequency", frequencyArray);
            config.put("orgcode", shift.getOrgCode());
            config.put("sbsj", shift.getSbsj());
            config.put("xbsj", shift.getXbsj());
            config.put("tbrq", shift.getTbrq());
            config.put("shiftClassCode", shift.getShiftClassCode());
            JSONArray shiftList = new JSONArray();
            shiftList.add(config);
            activityConfig.put("shiftList", shiftList);
            activityConfig.put("activityStart", activityProperty.getBeginDate());
            activityConfig.put("activityEnd", activityProperty.getEndDate());
            activityConfig.put("noEnd", activityProperty.getNoEndDate());
            configMap.put(activityProperty.getId(), activityConfig);
        }
        return this.generateActivityInstance(configMap, false,activityProperties);
    }
}
