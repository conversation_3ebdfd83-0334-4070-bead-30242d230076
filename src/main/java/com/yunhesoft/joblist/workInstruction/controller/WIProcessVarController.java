package com.yunhesoft.joblist.workInstruction.controller;


import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIProcessVarDto;
import com.yunhesoft.joblist.workInstruction.service.IWIProcessVarService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import dm.jdbc.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/joblist/wi/processVar")
@Api(tags = "工作指令流程变量")
public class WIProcessVarController extends BaseRestController {

    @Autowired
    private IWIProcessVarService srv;

    @ApiOperation("查询流程变量机构列表")
    @RequestMapping(value = "/queryProcessVarOrgList", method = {RequestMethod.GET})
    public Res<?> queryProcessVarOrgList() {
        return Res.OK(srv.queryProcessVarOrgList());
    }

    @ApiOperation("查询流程变量")
    @RequestMapping(value = "/queryProcessVarList", method = {RequestMethod.GET})
    public Res<?> queryProcessVarList() {
        return Res.OK(srv.queryProcessVarList());
    }

    @ApiOperation("查询变量设置(按机构)")
    @RequestMapping(value = "/queryProcessVarSetting", method = {RequestMethod.POST})
    public Res<?> queryProcessVarSetting(@RequestBody WIProcessVarDto dto) {
        return Res.OK(srv.queryProcessVarSetting(dto));
    }

    @ApiOperation("保存变量设置(按机构)")
    @RequestMapping(value = "/saveProcessVarSetting", method = {RequestMethod.POST})
    public Res<?> saveProcessVarSetting(@RequestBody WIProcessVarDto dto) {
        String msg = srv.saveProcessVarSetting(dto);
        return StringUtil.isEmpty(msg) ? Res.OK() : Res.FAIL(msg);
    }

}
