package com.yunhesoft.joblist.workInstruction.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

@ApiModel(value = "流程变量的具体设置")
@Getter
@Setter
@Entity
@Table(name = "WI_PROCESS_VAR_CONFIG", indexes = {@Index(name = "WI_PROCESS_VAR_CONFIG_INDEX", columnList = "VAR_ID,ORG_CODE") })
public class WIProcessVarConfig extends BaseEntity {

	@ApiModelProperty(value = "变量名称表的id")
	@Column(name = "VAR_ID", length = 50)
	private String varId;

	@ApiModelProperty(value = "变量所属单位")
	@Column(name = "ORG_CODE", length = 50)
	private String orgCode;

	@ApiModelProperty(value = "变量所属单位")
	@Column(name = "ORG_NAME", length = 100)
	private String orgName;

	@ApiModelProperty(value = "类型 1人员 2岗位 3机构")
	@Column(name = "CONFIG_TYPE")
	private int configType;

	@ApiModelProperty(value = "编码")
	@Column(name = "CONFIG_CODE", length = 50)
	private String configCode;

	@ApiModelProperty(value = "名称")
	@Column(name = "CONFIG_LABEL", length = 100)
	private String configLabel;

	@ApiModelProperty(value = "排序")
	@Column(name = "TMSORT")
	private int tmsort;

}
