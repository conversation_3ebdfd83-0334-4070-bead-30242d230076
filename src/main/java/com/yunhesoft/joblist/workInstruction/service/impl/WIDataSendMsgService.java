package com.yunhesoft.joblist.workInstruction.service.impl;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.gmsg.client.topicMsg.GmsgTopicMsgClient;
import com.yunhesoft.gmsg.client.topicMsg.GmsgTopicMsgContent;
import com.yunhesoft.gmsg.client.topicMsg.GmsgTopicMsgParams;
import com.yunhesoft.gmsg.core.model.GActionType;
import com.yunhesoft.gmsg.core.model.GMessage;
import com.yunhesoft.joblist.workInstruction.entity.po.WIData;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcessDataStep;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcessDataStepAcc;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIDataAccEmpVo;
import com.yunhesoft.joblist.workInstruction.service.IWIProcessVarService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.ISysEmployeeChangeInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.service.ISysOrgService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class WIDataSendMsgService {
    @Autowired
    private EntityService entityService;

    @Autowired
    private ISysEmployeeChangeInfoService empChangeInfoSrv;

    @Autowired
    private IWIProcessVarService processVarSrv;

    @Autowired
    private WIDataUtils dataUtils;

    @Autowired
    private IShiftService shiftSrv;

    @Autowired
    private ISysOrgService orgSrv;

    @Autowired
    private GmsgTopicMsgClient msgClient;

//    @Autowired
//    private SysMessageService sysMessageService;

    public static final String MSG_INFO_URL_PC = "/joblist/workInstruction/base/instructionDataInfoDialog.vue";

    public static final String MSG_INFO_URL_APP = "wiDataInfoForMsg";
//    public static final String MSG_INFO_URL_APP = "/pages/workInstruction/orderInfoMsg.vue";

    /**
     * 发送待办消息和抄送消息
     * @param data
     * @param currentStep
     * @param nextStepList
     * @param ccType
     * @param ccCode
     * @return
     */
    public String sendTodoMsgAndCcMsg (WIData data, WIProcessDataStep currentStep, List<WIProcessDataStep> nextStepList, int ccType, String ccCode) {

//        boolean filterByShift = false;


        Map<WIProcessDataStep, List<WIProcessDataStepAcc>> accGroup = new HashMap<>();
        if (StringUtils.isNotEmpty(nextStepList)) {
            for (WIProcessDataStep eachStep : nextStepList) {
//                int stepHandle = eachStep.getStepHandle();
//                String handleText = stepHandle == 1 ? "待审核" : "待接收";
                List<WIProcessDataStepAcc> accList = eachStep.getAccList();
                if (StringUtils.isEmpty(accList)) {
                    continue;
                }
                for (WIProcessDataStepAcc acc : accList) {
                    int objType = acc.getObjType();
                    if (objType != 1) {
                        continue;
                    }
                    accGroup.computeIfAbsent(eachStep, k -> new ArrayList<>()).add(acc);
                }
//                int selectInputUser = eachStep.getSelectInputUser();
//                if (selectInputUser == 1 || stepHandle == 3 || stepHandle == 6) {
//                    //选择执行人、填写、结转填写时，按班次过滤
//                    filterByShift = true;
//                }
            }
        }

        String category = data.getCategory();
        String typeName = null;
        if ("task".equals(category)) {
            typeName = "任务";
        } else if ("pro".equals(data.getDataType())){
            typeName = "生产指令";
        } else if ("oper".equals(data.getDataType())) {
            typeName = "操作指令";
        } else {
            typeName = "工作指令";
        }

        SysUser user = SysUserUtil.getCurrentUser();
        String currentUserId = user == null ? null : user.getId();
        //待办要接收的人
        Set<String> userIdSet = new HashSet<>();
        String nowdt = DateTimeUtils.getNowDateTimeStr();
        for (Map.Entry<WIProcessDataStep, List<WIProcessDataStepAcc>> entry : accGroup.entrySet()) {
            WIProcessDataStep dataStep = entry.getKey();
            List<WIProcessDataStepAcc> accList = entry.getValue();
            if (StringUtils.isEmpty(accList)) {
                continue;
            }

            boolean filterByShift = false;
            int stepHandle = dataStep.getStepHandle();

            //结转
            String expireAssignOrgCode = null;
            int selectInputUser = dataStep.getSelectInputUser();
            if (stepHandle == 5 && StringUtils.isNotEmpty(dataStep.getExpireAssignOrgCode())) {
                //结转指定机构
                expireAssignOrgCode = dataStep.getExpireAssignOrgCode();
            } else if (selectInputUser == 1 || stepHandle == 3 || stepHandle == 6) {
                //选择执行人、填写、结转填写时，按班次过滤
                filterByShift = true;
            }
//            List<ShiftForeignVo> shiftInfoList = filterByShift ? dataUtils.getOrgShiftInfo(accOrgCode, nowdt) : null;
//            ShiftForeignVo shiftInfo = StringUtils.isEmpty(shiftInfoList) ? null : shiftInfoList.get(0);

//            String accCode = accList.stream().map(WIProcessDataStepAcc::getAccCode).collect(Collectors.joining(","));

            Map<String, ShiftForeignVo> empOrgShiftMap = new HashMap<>();
            List<WIDataAccEmpVo> accUserResult = this.getUserByAccList(data, accList.get(0).getAccType(), accList);
            if (StringUtils.isNotEmpty(accUserResult)) {
                String handle = "待接收";
                if (stepHandle == 1) {
                    handle = "待审核";
                } else if (stepHandle == 3 || stepHandle == 6) {
                    handle = "待填报";
                } else if (stepHandle == 4) {
                    handle = "待反馈";
                } else if (stepHandle == 5) {
                    handle = "结转待接收";
                }
                String todoMsgTitle = typeName + handle+"("+data.getDataName()+")";

                String todoMsgText = "您有一条"+typeName + "记录"+handle+"。名称："+data.getDataName()+"，开始时间："+data.getStartDt()+"，结束时间："+(StringUtils.isEmpty(data.getEndDt()) ? "无": data.getEndDt())+"。";
//                + "内容："+data.getDataContent();
                for (WIDataAccEmpVo vo : accUserResult) {
                    WIProcessDataStepAcc acc = vo.getAcc();
                    List<EmployeeVo> empList = vo.getEmpList();
                    if (StringUtils.isEmpty(empList)) {
                        continue;
                    }
                    List<EmployeeVo> todoEmpList = new ArrayList<>();
//                    List<String> todoEmpIdList = new ArrayList<>();
                    for (EmployeeVo emp : empList) {
                        if (userIdSet.contains(emp.getEmpTmuid())) {
                            //已经发送过，不重复
                            continue;
                        }
                        if (StringUtils.isNotEmpty(currentUserId) && currentUserId.equals(emp.getEmpTmuid())) {
                            //不给自己发
                            continue;
                        }

                        boolean todo = false;
                        //是兼岗的用兼岗机构，否则用人员自身机构
                        String empOrgCode = StringUtils.isNotEmpty(acc.getPartTimePostOrgCode()) ? acc.getPartTimePostOrgCode() : emp.getOrgcode();
                        if (StringUtils.isNotEmpty(expireAssignOrgCode)) {
                            //是指定单位的
                            todo = expireAssignOrgCode.equals(empOrgCode);
                        } else if (filterByShift) {
                            //按班次过滤
                            ShiftForeignVo orgNowShiftInfo = empOrgShiftMap.computeIfAbsent(empOrgCode, k -> dataUtils.getOrgNowShiftInfo(empOrgCode));
                            if (orgNowShiftInfo != null && StringUtils.isNotEmpty(orgNowShiftInfo.getOrgCode())) {
                                todo = true;
                            }
//                        String currentShiftOrgCode = shiftInfo == null ? null : shiftInfo.getOrgCode();
//                        if (StringUtils.isNotEmpty(currentShiftOrgCode) && currentShiftOrgCode.equals(emp.getOrgcode())) {
//                            //是当前班组
//                            todo = true;
//                        }
                        } else {
                            todo = true;
                        }
                        if (todo) {
                            todoEmpList.add(emp);
                            userIdSet.add(emp.getEmpTmuid());
//                            todoEmpIdList.add(emp.getEmpTmuid());
                        }

//                        if (StringUtils.isNotEmpty(currentUserId) && !currentUserId.equals(emp.getEmpTmuid())) {
//                            //不给自己发
//                            userIdSet.add(emp.getEmpTmuid());
//                        }
                    }
                    if (StringUtils.isNotEmpty(todoEmpList)) {
                        String sout = "给下级审核人发消息：" + todoEmpList.stream().map(EmployeeVo::getEmpname).collect(Collectors.joining(","));
                        System.out.println(sout);
                        List<String> receivers = todoEmpList.stream().map(EmployeeVo::getEmpTmuid).distinct().collect(Collectors.toList());

                        GmsgTopicMsgContent msgContent = new GmsgTopicMsgContent();

                        msgContent.setContent(todoMsgText);
                        msgContent.setLinkName("点击查看");

                        JSONObject pcDataStepId = null;
                        JSONObject appDataStepId = null;
                        if (dataStep.getStepHandle() == 3 || dataStep.getStepHandle() == 6) {
                            //填写
                            pcDataStepId = JSON.parseObject("{\"inputStepId\": \""+acc.getProcessDataStepId()+"\", \"inputStepAccId\": \""+acc.getId()+"\"}");
                            appDataStepId = JSON.parseObject("{\"inputStepId\": \""+acc.getProcessDataStepId()+"\", \"inputStepAccId\": \""+acc.getId()+"\"}");
                        } else {
                            //接收
                            pcDataStepId = JSON.parseObject("{\"acceptStepId\": \""+acc.getProcessDataStepId()+"\", \"acceptStepAccId\": \""+acc.getId()+"\"}");
                            appDataStepId = JSON.parseObject("{\"acceptStepId\": \""+acc.getProcessDataStepId()+"\", \"acceptStepAccId\": \""+acc.getId()+"\"}");
                        }
                        msgContent.setLinkUrlPc(MSG_INFO_URL_PC);
                        JSONObject paramsPc = new JSONObject();
                        paramsPc.put("id", data.getId());
                        paramsPc.put("dataStepId", pcDataStepId);
                        msgContent.setParamsPc(paramsPc);

                        msgContent.setLinkUrlApp(MSG_INFO_URL_APP);
//                        msgContent.setLinkUrlApp("orderInfoMsg");
                        JSONObject paramsApp = new JSONObject();
                        paramsApp.put("id", data.getId());
                        paramsApp.put("dataStepId", appDataStepId);
                        msgContent.setParamsApp(paramsApp);

                        GmsgTopicMsgParams params = GmsgTopicMsgParams.builder().messageId(TMUID.getUID()).messageTitle(todoMsgTitle).receiveUserId(receivers).linkedMsgList(new ArrayList<>(Collections.singletonList(msgContent))).build();
                        Map<String, Object> messageBody = new HashMap<>();
                        messageBody.put("msgs", params.getLinkedMsgList());
                        GMessage message = GMessage.build().id(params.getMessageId()).topic("msg").publisher(params.getPublishUserId()).publisherName(params.getPublishUserName()).receivers(receivers).messageTitle(params.getMessageTitle()).messageBody(messageBody).action(GActionType.PUB);
                        System.out.println("json--------------------");
                        System.out.println(JSON.toJSONString( message));
                        msgClient.publishMessage(params);

//                    Map<String, Object> msg = new HashMap<>();
//                    msg.put("name", "消息name");
//                    msg.put("title", "消息title");
//                    String msgBody= todoMsgText+"。<a pcurl='/joblist/workInstruction/base/instructionDataInfoDialog.vue' pcparams='{\"id\":\""+data.getId()+"\",\"dataStepId\":\"{\\\"acceptStepId\\\":\\\""+acc.getProcessDataStepId()+"\\\",\\\"acceptStepAccId\\\":\\\""+acc.getId()+"\\\"}\"}' appurl='/app/xxx' appparams=\"\">点击查看</a>";
//                    msg.put("content", msgBody);
//                    gWebSocketClient.publish(
//                            "msg",
//                            TMUID.getUID(),
//                            receivers,
//                            msgTitle,
//                            msg);
                    }
                }
            }

        }

//        if (StringUtils.isNotEmpty(todoEmpList)) {
//            String sout = "给下级审核人发消息：" + todoEmpList.stream().map(EmployeeVo::getEmpname).collect(Collectors.joining(","));
//            System.out.println(sout);
//            List<String> receivers = todoEmpList.stream().map(EmployeeVo::getEmpTmuid).collect(Collectors.toList());
//            Map<String, Object> msg = new HashMap<>();
//            msg.put("name", "消息name");
//            msg.put("title", "消息title");
//            String msgBody="您有一条工作指令记录待接收，<a pcurl='/joblist/workInstruction/base/instructionDataInfoDialog.vue' pcparams='{\"id\":\"ZR7C7Q8WN07HAMEFOL0231\",\"dataStepId\":\"{\\\"acceptStepId\\\":\\\"ZR7C7Q93H07HAME7VW0255\\\",\\\"acceptStepAccId\\\":\\\"ZR7C7Q93H07HAME7VX0263\\\"}\"}' appurl='/app/xxx' appparams=\"\">点击查看</a>";
//            msg.put("content", msgBody);
//            gWebSocketClient.publish(
//                "msg",
//                TMUID.getUID(),
//                receivers,
//                "工作指令待接收",
//                    msg);
//        }

        String ccMsgTitle = typeName + "抄送通知"+"("+data.getDataName()+")";
        String ccMsgText = typeName + ":【"+data.getDataName()+"】已下达，现抄送给您。开始时间："+data.getStartDt()+"，结束时间："+(StringUtils.isEmpty(data.getEndDt()) ? "无": data.getEndDt())+"。";
//                "内容："+data.getDataContent();
        //抄送人
        List<WIProcessDataStepAcc> accList = new ArrayList<>();
        List<EmployeeVo> ccEmpList = new ArrayList<>();
        List<EmployeeVo> userList = StringUtils.isEmpty(ccCode) ? null : empChangeInfoSrv.getUserChangeList(DateTimeUtils.getNowDateStr(), Arrays.asList(ccCode.split(",")), false);
        if (StringUtils.isNotEmpty(userList)) {
            int tmsort = 1;
            for (EmployeeVo emp : userList) {
                if (userIdSet.contains(emp.getEmpTmuid())) {
                    continue;
                }
                if (StringUtils.isNotEmpty(currentUserId) && currentUserId.equals(emp.getEmpTmuid())) {
                    //不给自己发
                    continue;
                }
                ccEmpList.add(emp);
                userIdSet.add(emp.getEmpTmuid());

                WIProcessDataStepAcc acc = new WIProcessDataStepAcc();
                acc.setId(TMUID.getUID());
                acc.setDataId(data.getId());
                acc.setProcessDataId(currentStep.getProcessDataId());
                acc.setProcessDataStepId(currentStep.getId());
                acc.setAccOrgCode(currentStep.getAccOrgCode());
                acc.setAccOrgName(currentStep.getAccOrgName());
                acc.setObjType(3);
                acc.setAccType(1);
                acc.setAccCode(emp.getEmpTmuid());
                acc.setAccText(emp.getEmpname());
                acc.setHandleUserId(emp.getEmpTmuid());
                acc.setHandleUserName(emp.getEmpname());
                acc.setHandleTime(nowdt);
                acc.setHandleResult(1);
                acc.setTmsort(tmsort++);
                accList.add(acc);
            }
        }

        if (StringUtils.isNotEmpty(accList)) {
            entityService.insertBatch(accList, 1000);
        }

        if (StringUtils.isNotEmpty(ccEmpList)) {
            String sout = "给抄送人发消息：" + ccEmpList.stream().map(EmployeeVo::getEmpname).collect(Collectors.joining(","));
            System.out.println(sout);

            List<String> receivers = ccEmpList.stream().map(EmployeeVo::getEmpTmuid).distinct().collect(Collectors.toList());

            GmsgTopicMsgContent msgContent = new GmsgTopicMsgContent();

            msgContent.setContent(ccMsgText);
            msgContent.setLinkName("点击查看");

            msgContent.setLinkUrlPc(MSG_INFO_URL_PC);
            JSONObject paramsPc = new JSONObject();
            paramsPc.put("id", data.getId());
            paramsPc.put("dataStepId", JSON.parseObject("{\"acceptStepId\": \""+currentStep.getId()+"\", \"stepCc\": true}"));
            msgContent.setParamsPc(paramsPc);

            msgContent.setLinkUrlApp(MSG_INFO_URL_APP);
            JSONObject paramsApp = new JSONObject();
            paramsApp.put("id", data.getId());
            paramsApp.put("dataStepId", JSON.parseObject("{\"acceptStepId\": \""+currentStep.getId()+"\", \"stepCc\": true}"));
            msgContent.setParamsApp(paramsApp);

            GmsgTopicMsgParams params = GmsgTopicMsgParams.builder().messageId(TMUID.getUID()).messageTitle(ccMsgTitle).receiveUserId(receivers).linkedMsgList(new ArrayList<>(Collections.singletonList(msgContent))).build();
            msgClient.publishMessage(params);
//            sysMessageService.sendLinkedSysMessage(ccMsgTitle, msgContent, receivers, "0", "系统");

        }

        return null;
    }

    /**
     * 给已处理的人员发消息
     * @param type -1审核否决 -2拒绝接收 -3 撤回消息 -4终止消息
     * @return
     */
    public String sendMsgToAlready (WIData data, WIProcessDataStep currentStep, int type) {
        List<WIProcessDataStep> allStepList = entityService.queryData(WIProcessDataStep.class, Where.create().eq(WIProcessDataStep::getDataId, data.getId()).eq(WIProcessDataStep::getTmused, 1).eq(WIProcessDataStep::getProcessDataId, currentStep.getProcessDataId()), Order.create().orderByAsc(WIProcessDataStep::getStepNo), null);
        if (StringUtils.isEmpty(allStepList)) {
            return null;
        }
        List<WIProcessDataStep> alreadyStepList = new ArrayList<>();
        List<String> stepIdArray = new ArrayList<>();
        for (WIProcessDataStep dataStep : allStepList) {
            if (dataStep.getId().equals(currentStep.getId())) {
                //跳过自身
                continue;
            }
            if (dataStep.getStepNo() >= currentStep.getStepNo()) {
                //后面的步骤跳过
                continue;
            }
            if (dataStep.getHandleResult() != 1) {
                //没通过的跳过
                continue;
            }
            if (currentStep.getInAcceptOrg() == 1) {
                //内部节点
                if (dataStep.getInAcceptOrg() == 0 || (dataStep.getInAcceptOrg() == 1 && dataStep.getAccOrgCode().equals(currentStep.getAccOrgCode()))) {
                    //上级单位，或者当前单位分支内的节点
                    alreadyStepList.add(dataStep);
                    stepIdArray.add(dataStep.getId());
                }
            } else if (dataStep.getInAcceptOrg() == 0) {
                //上级单位节点
                alreadyStepList.add(dataStep);
                stepIdArray.add(dataStep.getId());
            }
        }

        List<WIProcessDataStepAcc> accList = entityService.queryData(WIProcessDataStepAcc.class, Where.create().eq(WIProcessDataStepAcc::getDataId, data.getId()).in(WIProcessDataStepAcc::getProcessDataStepId, stepIdArray.toArray()), null, null);
        if (StringUtils.isEmpty(accList)) {
            return null;
        }
        List<String> sendMsgIdList = new ArrayList<>();
        for (WIProcessDataStepAcc acc : accList) {
            if (acc.getObjType() != 1) {
                continue;
            }
            if (acc.getHandleResult() != 1 || StringUtils.isEmpty(acc.getHandleUserId())) {
                //没处理
                continue;
            }
            sendMsgIdList.add(acc.getHandleUserId());
        }

        return null;
    }


    /**
     * 发送抄送消息
     * @param data
     * @param currentStep
     * @param accType
     * @param accCode
     * @param accOrgCode
     * @return
     */
    public String sendCcMsg (WIData data, WIProcessDataStep currentStep, int accType, String accCode, String accOrgCode) {

        List<EmployeeVo> userList = this.getUserByAccCode(data, accType, accCode, accOrgCode);
        if (StringUtils.isEmpty(userList)) {
            return null;
        }
        String category = data.getCategory();
        String typeName = null;
        if ("task".equals(category)) {
            typeName = "任务";
        } else if ("pro".equals(data.getDataType())){
            typeName = "生产指令";
        } else if ("oper".equals(data.getDataType())) {
            typeName = "操作指令";
        } else {
            typeName = "工作指令";
        }
        String msgTitle = typeName + "消息提醒";
        String msgText = typeName + ":【"+data.getDataName()+"】已下达，现抄送给您。开始时间："+data.getStartDt()+"，结束时间："+(StringUtils.isEmpty(data.getEndDt()) ? "无": data.getEndDt())+"。" +
                "内容："+data.getDataContent();
        List<WIProcessDataStepAcc> accList = new ArrayList<>();
        String nowdt = DateTimeUtils.getNowDateTimeStr();
        for (int i=0; i < userList.size(); i++) {
            EmployeeVo emp = userList.get(i);
            WIProcessDataStepAcc acc = new WIProcessDataStepAcc();
            acc.setId(TMUID.getUID());
            acc.setDataId(data.getId());
            acc.setProcessDataId(currentStep.getProcessDataId());
            acc.setProcessDataStepId(currentStep.getId());
            acc.setAccOrgCode(currentStep.getAccOrgCode());
            acc.setAccOrgName(currentStep.getAccOrgName());
            acc.setObjType(3);
            acc.setAccType(1);
            acc.setAccCode(emp.getEmpTmuid());
            acc.setAccText(emp.getEmpname());
            acc.setHandleUserId(emp.getEmpTmuid());
            acc.setHandleUserName(emp.getEmpname());
            acc.setHandleTime(nowdt);
            acc.setHandleResult(1);
            acc.setTmsort(i+1);
            accList.add(acc);
        }

        entityService.insertBatch(accList, 1000);

        System.out.println(msgTitle);
        System.out.println(msgText);
        return null;
    }

    public List<EmployeeVo> getUserByAccCode (WIData data, int accType, String accCode, String accOrgCode) {
        if (StringUtils.isEmpty(accCode)) {
            return null;
        }
        List<EmployeeVo> result = new ArrayList<>();
        if (accType == 1) {
            //人员
            result = empChangeInfoSrv.getUserChangeList(DateTimeUtils.getNowDateStr(), Arrays.asList(accCode.split(",")), false);
        } else if (accType == 2) {
            //岗位
//            result = StringUtils.isEmpty(accOrgCode) ? null : empSrv.getEmployeeByOrgcode(accOrgCode);
            List<EmployeeVo> empList = StringUtils.isEmpty(accOrgCode) ? null : empChangeInfoSrv.getOrgUserChange(DateTimeUtils.getNowDateStr(), accOrgCode, true, true);
            if (StringUtils.isNotEmpty(empList)) {
                for (EmployeeVo emp : empList) {
                    String postTmuid = emp.getPostTmuid();
                    if (StringUtils.isEmpty(postTmuid)) {
                        continue;
                    }
                    if ((","+accCode+",").indexOf(","+postTmuid+",") >= 0) {
                        result.add(emp);
                    }
                }
            }

        } else if (accType == 3) {
            //机构
            for (String eachOrg : accCode.split(",")) {
                List<EmployeeVo> eachList = empChangeInfoSrv.getOrgUserChange(DateTimeUtils.getNowDateStr(), eachOrg, true, true);
                if (StringUtils.isNotEmpty(eachList)) {
                    result.addAll(eachList);
                }
            }
        } else if (accType == 100 && StringUtils.isNotEmpty(accOrgCode)) {
            //自定义变量
            for (String eachCode : accCode.split(",")) {
                List<EmployeeVo> userList = processVarSrv.getUserByVar(eachCode, accOrgCode);
                if (StringUtils.isNotEmpty(userList)) {
                    result.addAll(userList);
                }
            }
        }
        return result;
    }


    public List<WIDataAccEmpVo> getUserByAccList (WIData data, int accType, List<WIProcessDataStepAcc> accList) {
        if (StringUtils.isEmpty(accList)) {
            return null;
        }
        List<WIDataAccEmpVo> result = new ArrayList<>();
        if (accType == 1) {
            //人员
            Map<String, WIProcessDataStepAcc> accMap = new HashMap<>();
            List<String> accCodeList = new ArrayList<>();
            for (WIProcessDataStepAcc acc : accList) {
                accMap.put(acc.getAccCode(), acc);
                accCodeList.add(acc.getAccCode());
            }
            List<EmployeeVo> empresult = empChangeInfoSrv.getUserChangeList(DateTimeUtils.getNowDateStr(), accCodeList, false);
            if (StringUtils.isNotEmpty(empresult)) {
                for (EmployeeVo emp : empresult) {
                    WIProcessDataStepAcc acc = accMap.get(emp.getEmpTmuid());
                    WIDataAccEmpVo each = new WIDataAccEmpVo();
                    each.setAcc(acc);
                    each.setEmpList(new ArrayList<>(Collections.singletonList( emp)));
                    result.add(each);
                }
            }
        } else if (accType == 2) {
            //岗位
//            result = StringUtils.isEmpty(accOrgCode) ? null : empSrv.getEmployeeByOrgcode(accOrgCode);
            Map<String, List<EmployeeVo>> userTemp = new HashMap<>();
            Map<String, List<WIProcessDataStepAcc>> accGroup = new HashMap<>();
            for (WIProcessDataStepAcc acc : accList) {
                String accOrgCode = acc.getAccOrgCode();
                if (StringUtils.isEmpty(accOrgCode)) {
                    continue;
                }
                List<EmployeeVo> empList = userTemp.containsKey(accOrgCode) ? userTemp.get(accOrgCode) : empChangeInfoSrv.getOrgUserChange(DateTimeUtils.getNowDateStr(), accOrgCode, true, true);
                if (StringUtils.isNotEmpty(empList)) {
                    userTemp.put(accOrgCode, empList);
                    List<EmployeeVo> userList = new ArrayList<>();
                    for (EmployeeVo emp : empList) {
                        String postTmuid = emp.getPostTmuid();
                        if (StringUtils.isEmpty(postTmuid)) {
                            continue;
                        }
                        if (postTmuid.equals(acc.getAccCode())) {
                            userList.add(emp);
                        }
                    }
                    if (StringUtils.isNotEmpty(userList)) {
                        WIDataAccEmpVo each = new WIDataAccEmpVo();
                        each.setAcc(acc);
                        each.setEmpList(userList);
                        result.add(each);
                    }
                }
                accGroup.computeIfAbsent(acc.getAccOrgCode(), k -> new ArrayList<>()).add(acc);
            }
        } else if (accType == 3) {
            //机构

            for (WIProcessDataStepAcc acc : accList) {
                List<EmployeeVo> userList = empChangeInfoSrv.getOrgUserChange(DateTimeUtils.getNowDateStr(), acc.getAccCode(), true, true);
                if (StringUtils.isNotEmpty(userList)) {
                    WIDataAccEmpVo each = new WIDataAccEmpVo();
                    each.setAcc(acc);
                    each.setEmpList(userList);
                    result.add(each);
//                    empresult.addAll(eachList);
                }
            }
        } else if (accType == 100) {
            //自定义变量
            for (WIProcessDataStepAcc acc : accList) {
                if (StringUtils.isEmpty(acc.getAccOrgCode())) {
                    continue;
                }
                List<EmployeeVo> userList = processVarSrv.getUserByVar(acc.getAccCode(), acc.getAccOrgCode());
                if (StringUtils.isNotEmpty(userList)) {
                    WIDataAccEmpVo each = new WIDataAccEmpVo();
                    each.setAcc(acc);
                    each.setEmpList(userList);
                    result.add(each);
//                    empresult.addAll(userList);
                }
            }
        }
        return result;
    }


}
