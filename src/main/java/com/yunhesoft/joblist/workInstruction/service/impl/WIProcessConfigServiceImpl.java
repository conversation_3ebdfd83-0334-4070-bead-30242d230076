package com.yunhesoft.joblist.workInstruction.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.joblist.config.JoblistModuleConfig;
import com.yunhesoft.joblist.entity.dto.JobListModuleConfigDto;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIProcessDto;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcess;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcessStep;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcessStepAcc;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcessVarConfig;
import com.yunhesoft.joblist.workInstruction.service.IWIProcessConfigService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class WIProcessConfigServiceImpl implements IWIProcessConfigService {

    @Autowired
    private EntityService entityService;

    @Autowired
    private Environment env;

    /**
     * 是否使用操作卡
     *
     * @return
     */
    @Override
    public boolean useOpCard() {
        JoblistModuleConfig moduleConfig = SpringUtils.getBean(JoblistModuleConfig.class);
        if(moduleConfig==null){
            return false;
        }
        List<JobListModuleConfigDto> modules = moduleConfig.getModules();
        if (StringUtils.isEmpty(modules)) {
            return false;
        }
        JobListModuleConfigDto config = modules.stream().filter(item -> "workInstruction".equals(item.getAlias())).findFirst().orElse(null);
        String json = Optional.ofNullable(config).map(JobListModuleConfigDto::getConfig).orElse(null);
        if (StringUtils.isEmpty(json)) {
            return false;
        }
        try {
            JSONObject parse = JSON.parseObject(json);
            Boolean use_op_card = parse.getBoolean("use_op_card");
            if (use_op_card != null && use_op_card) {
                return true;
            }
        } catch (Exception e){}

        return false;
    }

    /**
     * 查询流程列表
     *
     * @return
     */
    @Override
    public List<WIProcess> queryProcessList(WIProcessDto dto) {
        String category = Optional.ofNullable(dto).map(WIProcessDto::getCategory).orElse(null);
        String processName = Optional.ofNullable(dto).map(WIProcessDto::getProcessName).orElse(null);
        Where where = Where.create();
        if (StringUtils.isNotEmpty(category)) {
            where.eq(WIProcess::getCategory, category);
        }
        if (StringUtils.isNotEmpty(processName)) {
            where.like(WIProcess::getProcessName, processName);
        }
        return entityService.queryData(WIProcess.class, where, Order.create(WIProcess::getCreateTime, Order.ASC), null);
    }

    /**
     * 保存流程列表
     *
     * @param processList
     * @return
     */
    @Override
    public String saveProcess(List<WIProcess> processList) {
        if (StringUtils.isEmpty(processList)) {
            return "没有要保存的记录";
        }
        List<WIProcess> insertList = new ArrayList<>();
        List<WIProcess> updateList = new ArrayList<>();
        for (WIProcess step : processList) {
            if (StringUtils.isEmpty(step.getId())) {
                //新增
                step.setId(TMUID.getUID());
                insertList.add(step);
            } else {
                //修改
                updateList.add(step);
            }
        }

        if (StringUtils.isNotEmpty(insertList)) {
            entityService.insertBatch(insertList, 1000);
        }
        if (StringUtils.isNotEmpty(updateList)) {
            entityService.updateByIdBatch(updateList);
        }

        return "";
    }


    /**
     * 删除流程
     *
     * @param processList
     * @return
     */
    @Override
    public String deleteProcess(List<WIProcess> processList) {
        if (StringUtils.isEmpty(processList)) {
            return "没有要删除的记录";
        }
        entityService.deleteByIdBatch(processList);

        entityService.rawDeleteByWhere(WIProcessStep.class, Where.create().in(WIProcessStep::getProcessId, processList.stream().map(WIProcess::getId).toArray()));
        return "";
    }

    /**
     * 查询类别列表
     *
     * @return
     */
    @Override
    public JSONArray queryCategoryList() {
        JSONArray result = new JSONArray();
        result.add(JSON.parse("{\"label\": \"指令\", \"value\": \"workInstruction\"}"));
        result.add(JSON.parse("{\"label\": \"任务\", \"value\": \"task\"}"));
        return result;
    }

    /**
     * 查询流程节点列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<WIProcessStep> queryProcessStepList(WIProcessDto dto) {
        String processId = Optional.ofNullable(dto).map(WIProcessDto::getProcessId).orElse(null);
        if (StringUtils.isEmpty(processId)) {
            return null;
        }
        List<WIProcessStep> stepList = entityService.queryData(WIProcessStep.class, Where.create().eq(WIProcessStep::getProcessId, processId), Order.create(WIProcessStep::getStepNo, Order.ASC), null);
        if (StringUtils.isEmpty(stepList)) {
            return null;
        }
//        List<WIProcessStepAcc> accList = entityService.queryData(WIProcessStepAcc.class, Where.create().eq(WIProcessStepAcc::getProcessId, processId), Order.create(), null);
//        Map<String, List<String>> accIdGroup = new HashMap<>(); //接收对象id
//        Map<String, List<String>> accNameGroup = new HashMap<>();   //接收对象名称
//        Map<String, List<String>> ccIdGroup = new HashMap<>();  //抄送对象id
//        Map<String, List<String>> ccNameGroup = new HashMap<>();    //抄送对象名称
//
//        Map<String, Integer> accTypeMap = new HashMap<>();
//        Map<String, Integer> ccTypeMap = new HashMap<>();
//        if (StringUtils.isNotEmpty(accList)) {
//            for (WIProcessStepAcc acc : accList) {
//                int objType = acc.getObjType();
//                String stepId = acc.getStepId();
//                int accType = acc.getAccType();
//                if (objType == 1) {
//                    //接收
//                    accIdGroup.computeIfAbsent(stepId, k -> new ArrayList<>()).add(acc.getAccCode());
//                    accNameGroup.computeIfAbsent(stepId, k -> new ArrayList<>()).add(acc.getAccText());
//
//                    accTypeMap.put(stepId, accType);
//                } else if (objType == 2) {
//                    //抄送
//                    ccIdGroup.computeIfAbsent(stepId, k -> new ArrayList<>()).add(acc.getAccCode());
//                    ccNameGroup.computeIfAbsent(stepId, k -> new ArrayList<>()).add(acc.getAccText());
//
//                    ccTypeMap.put(stepId, accType);
//                }
//            }
//        }
//        for (WIProcessStep step : stepList) {
//            step.setAccType(accTypeMap.getOrDefault(step.getId(), 0));
//            step.setCarbonCopyType(ccTypeMap.getOrDefault(step.getId(), 0));
//            List<String> accIdList = accIdGroup.get(step.getId());
//            List<String> accNameList = accNameGroup.get(step.getId());
//            List<String> ccIdList = ccIdGroup.get(step.getId());
//            List<String> ccNameList = ccNameGroup.get(step.getId());
//            if (StringUtils.isNotEmpty(accIdList)) {
//                step.setAccCode(StringUtils.join(accIdList, ","));
//            }
//            if (StringUtils.isNotEmpty(accNameList)) {
//                step.setAccText(StringUtils.join(accNameList, ","));
//            }
//            if (StringUtils.isNotEmpty(ccIdList)) {
//                step.setCarbonCopyCode(StringUtils.join(ccIdList, ","));
//            }
//            if (StringUtils.isNotEmpty(ccNameList)) {
//                step.setCarbonCopyText(StringUtils.join(ccNameList, ","));
//            }
//        }

        //给步骤补充接收对象和抄送对象信息
        this.setStepListAccData(stepList);
        return stepList;
    }

    /**
     * 保存流程节点列表
     *
     * @param stepList
     * @return
     */
    @Override
    public String saveProcessStep(List<WIProcessStep> stepList) {
        if (StringUtils.isEmpty(stepList)) {
            return "没有要保存的记录";
        }
        List<WIProcessStep> insertList = new ArrayList<>();
        List<WIProcessStep> updateList = new ArrayList<>();

        Set<String> updateStepIdSet = new HashSet<>();
        List<WIProcessStepAcc> accList = new ArrayList<>();
        for (WIProcessStep step : stepList) {
            if (StringUtils.isEmpty(step.getId())) {
                //新增
                step.setId(TMUID.getUID());
                insertList.add(step);
            } else {
                //修改
                updateList.add(step);
                updateStepIdSet.add(step.getId());
            }

            //抄送
            int ccType = step.getCarbonCopyType();
            String ccCode = step.getCarbonCopyCode();
            step.setCarbonCopy(ccType > 0 && StringUtils.isNotEmpty(ccCode) ? 1 : 0);
            String ccText = step.getCarbonCopyText();
            if (StringUtils.isNotEmpty(ccCode)) {
                String[] codeSplit = ccCode.split(",");
                String[] textSplit = ccText.split(",");
                for (int i = 0; i < codeSplit.length; i++) {
                    String code = codeSplit[i];
                    String text = textSplit[i];
                    WIProcessStepAcc acc = new WIProcessStepAcc();
                    acc.setObjType(2);
                    acc.setId(TMUID.getUID());
                    acc.setProcessId(step.getProcessId());
                    acc.setStepId(step.getId());
                    acc.setStepNo(step.getStepNo());
                    acc.setAccType(ccType);
                    acc.setAccCode(code);
                    acc.setAccText(text);
                    acc.setTmsort(i+1);
                    accList.add(acc);
                }
            }

            //接收
            int accType = step.getAccType();
            String accCode = step.getAccCode();
            String accText = step.getAccText();
            if (StringUtils.isNotEmpty(accCode)) {
                String[] codeSplit = accCode.split(",");
                String[] textSplit = accText.split(",");
                for (int i = 0; i < codeSplit.length; i++) {
                    String code = codeSplit[i];
                    String text = textSplit[i];
                    WIProcessStepAcc acc = new WIProcessStepAcc();
                    acc.setObjType(1);
                    acc.setId(TMUID.getUID());
                    acc.setProcessId(step.getProcessId());
                    acc.setStepId(step.getId());
                    acc.setStepNo(step.getStepNo());
                    acc.setAccType(accType);
                    acc.setAccCode(code);
                    acc.setAccText(text);
                    acc.setTmsort(i+1);
                    accList.add(acc);
                }
            }
        }

        if (StringUtils.isNotEmpty(insertList)) {
            entityService.insertBatch(insertList, 1000);
        }
        if (StringUtils.isNotEmpty(updateList)) {
            entityService.updateByIdBatch(updateList);
        }
        this.recalcStep(stepList.get(0).getProcessId());
        if (StringUtils.isNotEmpty(accList) && StringUtils.isNotEmpty(updateStepIdSet)) {
            //修改
            entityService.deleteAndInsert(WIProcessStepAcc.class, Where.create().in(WIProcessStepAcc::getStepId, updateStepIdSet.toArray()), accList);
        } else if (StringUtils.isNotEmpty(accList)) {
            //单新增
            entityService.insertBatch(accList, 1000);
        } else if (StringUtils.isNotEmpty(updateStepIdSet)) {
            //单清空
            entityService.rawDeleteByWhere(WIProcessStepAcc.class, Where.create().in(WIProcessStepAcc::getStepId, updateStepIdSet.toArray()));
        }

        return "";
    }

    /**
     * 删除流程节点列表
     *
     * @param stepList
     * @return
     */
    @Override
    public String deleteProcessStep(List<WIProcessStep> stepList) {
        if (StringUtils.isEmpty(stepList)) {
            return "没有要删除的记录";
        }
        entityService.deleteByIdBatch(stepList);
        this.recalcStep(stepList.get(0).getProcessId());

        entityService.rawDeleteByWhere(WIProcessStepAcc.class, Where.create().in(WIProcessStepAcc::getStepId, stepList.stream().map(WIProcessStep::getId).toArray()));
        return "";
    }

    /**
     * 给流程步骤补充接收对象及抄送对象
     * @param stepList
     */
    @Override
    public void setStepListAccData(List<WIProcessStep> stepList) {
        if (StringUtils.isEmpty(stepList)) {
            return;
        }
        Set<String> stepIdSet = new HashSet<>();
        for (WIProcessStep step : stepList) {
            stepIdSet.add(step.getId());
        }
        List<WIProcessStepAcc> accList = entityService.queryData(WIProcessStepAcc.class, Where.create().in(WIProcessStepAcc::getStepId, stepIdSet.toArray()), null, null);
        if (StringUtils.isEmpty(accList)) {
            return;
        }
        Map<String, List<WIProcessStepAcc>> accGroup = new HashMap<>(); //接收对象
        Map<String, List<WIProcessStepAcc>> ccGroup = new HashMap<>(); //抄送对象
        Map<String, List<String>> accIdGroup = new HashMap<>(); //接收对象id
        Map<String, List<String>> accNameGroup = new HashMap<>();   //接收对象名称
        Map<String, List<String>> ccIdGroup = new HashMap<>();  //抄送对象id
        Map<String, List<String>> ccNameGroup = new HashMap<>();    //抄送对象名称

        Map<String, Integer> accTypeMap = new HashMap<>();
        Map<String, Integer> ccTypeMap = new HashMap<>();
        if (StringUtils.isNotEmpty(accList)) {
            for (WIProcessStepAcc acc : accList) {
                int objType = acc.getObjType();
                String stepId = acc.getStepId();
                int accType = acc.getAccType();
                if (objType == 1) {
                    //接收
                    accGroup.computeIfAbsent(stepId, k -> new ArrayList<>()).add(acc);
                    accIdGroup.computeIfAbsent(stepId, k -> new ArrayList<>()).add(acc.getAccCode());
                    accNameGroup.computeIfAbsent(stepId, k -> new ArrayList<>()).add(acc.getAccText());

                    accTypeMap.put(stepId, accType);
                } else if (objType == 2) {
                    //抄送
                    ccGroup.computeIfAbsent(stepId, k -> new ArrayList<>()).add(acc);
                    ccIdGroup.computeIfAbsent(stepId, k -> new ArrayList<>()).add(acc.getAccCode());
                    ccNameGroup.computeIfAbsent(stepId, k -> new ArrayList<>()).add(acc.getAccText());

                    ccTypeMap.put(stepId, accType);
                }
            }
        }
        for (WIProcessStep step : stepList) {
            step.setAccList(accGroup.get(step.getId()));
            step.setCcList(ccGroup.get(step.getId()));
            step.setAccType(accTypeMap.getOrDefault(step.getId(), 0));
            step.setCarbonCopyType(ccTypeMap.getOrDefault(step.getId(), 0));
            List<String> accIdList = accIdGroup.get(step.getId());
            List<String> accNameList = accNameGroup.get(step.getId());
            List<String> ccIdList = ccIdGroup.get(step.getId());
            List<String> ccNameList = ccNameGroup.get(step.getId());
            if (StringUtils.isNotEmpty(accIdList)) {
                step.setAccCode(StringUtils.join(accIdList, ","));
            }
            if (StringUtils.isNotEmpty(accNameList)) {
                step.setAccText(StringUtils.join(accNameList, ","));
            }
            if (StringUtils.isNotEmpty(ccIdList)) {
                step.setCarbonCopyCode(StringUtils.join(ccIdList, ","));
            }
            if (StringUtils.isNotEmpty(ccNameList)) {
                step.setCarbonCopyText(StringUtils.join(ccNameList, ","));
            }
        }
    }

    /**
     * 重新计算步骤属性
     * @param processId
     * @return
     */
    private String recalcStep(String processId) {
        List<WIProcessStep> stepList = entityService.queryData(WIProcessStep.class, Where.create().eq(WIProcessStep::getProcessId, processId), Order.create(WIProcessStep::getStepNo, Order.ASC).orderByAsc(WIProcessStep::getId), null);
        if (StringUtils.isEmpty(stepList)) {
            return null;
        }

        int selectAcceptOrgIndex = -1;
        for (int i = 0; i < stepList.size(); i++) {
            WIProcessStep step = stepList.get(i);
            int selectAcceptOrg = step.getSelectAcceptOrg();
            if (selectAcceptOrg == 1 && selectAcceptOrgIndex < 0) {
                //选择执行机构的节点
                selectAcceptOrgIndex = i;
            } else if (selectAcceptOrgIndex >= 0) {
                //选择执行机构之后的节点
                step.setInAcceptOrg(1);
            }
            //只有最后一行才可以选择执行人
            step.setSelectInputUser(i == stepList.size()-1 ? 1 : 0);
        }

        for (int i = 0; i < stepList.size(); i++) {
            WIProcessStep step = stepList.get(i);
            //选择执行机构之前的为外部节点
            step.setInAcceptOrg(i > selectAcceptOrgIndex ? 1 : 0);
        }

        entityService.updateByIdBatch(stepList, "selectInputUser", "inAcceptOrg");
        return null;

    }
}
