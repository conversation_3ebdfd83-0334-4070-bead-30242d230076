package com.yunhesoft.joblist.workInstruction.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.joblist.entity.dto.JobFinishDto;
import com.yunhesoft.joblist.entity.dto.JoblistInputDto;
import com.yunhesoft.joblist.entity.vo.JoblistActivityExampleVo;
import com.yunhesoft.joblist.module.IJobQueryInterface;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataDto;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataQueryDto;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIDataSimpleVo;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIDataVo;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public class WIQueryServiceImpl implements IJobQueryInterface {

    @Autowired
    private WIDataServiceImpl dataService;

    /**
     * 查询工作完成情况
     *
     * @param param
     * @return
     * <AUTHOR>
     * @date 2025/3/6
     * @params
     */
    @Override
    public JobFinishDto getFinishCase(JoblistInputDto param) {
        WIDataQueryDto dto = this.getQueryDtoByParam(param);
        dto.setQueryType("accept");
        int total = 0;
        int finished = 0;
        int unfinish = 0;
        List<WIDataSimpleVo> list = SpringUtils.getBean(WIDataServiceImpl.class).queryDataList(dto);
        if (StringUtils.isNotEmpty(list)) {
            total = list.size();
            finished = list.get(0).getFinishedTotal();
            unfinish = total - finished;
        }
        JobFinishDto result = new JobFinishDto();
        result.setTotal(total);
        result.setUnfinish(unfinish);
        result.setFinished(finished);
        return result;
    }

    private WIDataQueryDto getQueryDtoByParam(JoblistInputDto param) {
        WIDataQueryDto dto = new WIDataQueryDto();
        dto.setQueryType(param.getQueryType());
        dto.setQueryDate(param.getWorkDate());
        dto.setCategory(param.getCategory());
        return dto;
    }

    /**
     * 查询工作列表
     *
     * @param param
     * @return
     * <AUTHOR>
     * @date 2025/3/6
     * @params
     */
    @Override
    public List<JoblistActivityExampleVo> getJobList(JoblistInputDto param) {
        WIDataQueryDto dto = this.getQueryDtoByParam(param);
        List<WIDataSimpleVo> list = SpringUtils.getBean(WIDataServiceImpl.class).queryDataList(dto);
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        List<JoblistActivityExampleVo> result = new ArrayList<>();
        for (WIDataSimpleVo vo : list) {
            JoblistActivityExampleVo bean = new JoblistActivityExampleVo();
            bean.setId(vo.getId());
            bean.setActivityName(vo.getDataName());
            bean.setModuleAlias("workInstruction");
            bean.setRowObject(JSONObject.from(vo));
            //bean.setFinished(1);
            //bean.setTotal(2);
            result.add(bean);
        }
        return result;
    }
}
