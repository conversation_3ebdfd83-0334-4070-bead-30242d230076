package com.yunhesoft.joblist.workInstruction.service;


import com.alibaba.fastjson2.JSONArray;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIProcessDto;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcess;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcessStep;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcessStepAcc;

import java.util.List;
import java.util.Set;


public interface IWIProcessConfigService {

	/**
	 * 是否使用操作卡
	 * @return
	 */
	boolean useOpCard();


	/**
	 * 查询流程列表
	 * @return
	 */
	List<WIProcess> queryProcessList(WIProcessDto dto);

	/**
	 * 保存流程列表
	 * @param processList
	 * @return
	 */
	String saveProcess(List<WIProcess> processList);


	/**
	 * 删除流程
	 * @param processList
	 * @return
	 */
	String deleteProcess(List<WIProcess> processList);

	/**
	 * 查询类别列表
	 * @return
	 */
	JSONArray queryCategoryList();


	/**
	 * 查询流程节点列表
	 * @param dto
	 * @return
	 */
	List<WIProcessStep> queryProcessStepList(WIProcessDto dto);

	/**
	 * 保存流程节点列表
	 * @param stepList
	 * @return
	 */
	String saveProcessStep(List<WIProcessStep> stepList);


	/**
	 * 删除流程节点列表
	 * @param stepList
	 * @return
	 */
	String deleteProcessStep(List<WIProcessStep> stepList);


	void setStepListAccData(List<WIProcessStep> stepList);

}
