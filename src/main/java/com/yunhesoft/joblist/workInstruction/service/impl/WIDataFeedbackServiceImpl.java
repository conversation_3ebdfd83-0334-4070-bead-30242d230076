package com.yunhesoft.joblist.workInstruction.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataFeedbackDto;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataFeedbackQueryDto;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataFeedbackSubmitDto;
import com.yunhesoft.joblist.workInstruction.entity.po.*;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIFeedbackItemVo;
import com.yunhesoft.joblist.workInstruction.service.IWIDataFeedbackService;
import com.yunhesoft.joblist.workInstruction.service.IWIDataService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.employee.service.ISysEmployeeChangeInfoService;
import com.yunhesoft.system.employee.service.impl.EmployeeBasicOperationImpl;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Update;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class WIDataFeedbackServiceImpl implements IWIDataFeedbackService {

    @Autowired
    private EntityService entityService;

    @Autowired
    private EmployeeBasicOperationImpl empSrv;

    @Autowired
    private ISysOrgService orgSrv;

    @Autowired
    private ISysEmployeeChangeInfoService empChangeInfoSrv;

    @Autowired
    private IFilesInfoService filesInfoService;

    @Autowired
    private IWIDataService dataSrv;

    @Autowired
    private IShiftService shiftSrv;

    @Autowired
    private WIDataUtils dataUtils;

    @Autowired
    private WIDataSendMsgService msgSrv;

    public String inputFeedback(MultipartFile[] uploadFiles, WIDataFeedbackDto saveData, WIProcessDataStep currentInputStep, WIProcessDataStepAcc currentInputStepAcc) {

        if (currentInputStep == null || currentInputStepAcc == null) {
            return "未找到当前节点";
        }

        String dataId = currentInputStep.getDataId();
        if (StringUtils.isEmpty(dataId)) {
            return "传入参数有误";
        }
        WIData data = entityService.queryObjectById(WIData.class, dataId);
        int handleResult = currentInputStepAcc.getHandleResult();
        int currentStatus = currentInputStepAcc.getCurrentStatus();
        if (currentStatus != 1) {
            return "当前节点不是待处理节点";
        }
        SysUser user = SysUserUtil.getCurrentUser();
        String currentUserId = user.getId();
        String currentUserName = user.getRealName();
        String currentUserOrgCode = user.getOrgId();

        String nowdt = DateTimeUtils.getNowDateTimeStr();
        String endDt = data.getEndDt();
        if (StringUtils.isNotEmpty(endDt) && nowdt.compareTo(endDt) > 0) {
            String expireAssignOrgCode = currentInputStep.getExpireAssignOrgCode();
            //有截止日期，且当前超期了
            if (StringUtils.isNoneEmpty(currentInputStep.getCurrentOrgCode(), expireAssignOrgCode) && !(","+expireAssignOrgCode+",").contains(","+currentInputStep.getCurrentOrgCode()+",")) {
                //与指定班组不匹配，无法反馈
                return "当前记录已超期，只能由最后一个班次机构进行反馈";
            }
        }
//        List<SysOrg> allOrgList = orgSrv.getOrgList(currentInputStep.getAccOrgCode());
//        ShiftForeignVo currentShift = shiftSrv.getShiftByOrgListDateTime(new ArrayList<>(Collections.singletonList(currentUserOrgCode)), nowdt);
        ShiftForeignVo currentShift = null;
        if (StringUtils.isNotEmpty(currentInputStepAcc.getPartTimePostOrgCode())) {
            //兼岗，用兼岗的班组取班次
            currentShift = dataUtils.getOrgNowShiftInfo(currentInputStepAcc.getPartTimePostOrgCode());
        } else {
            currentShift = dataUtils.getCurrentUserShiftInfo();
        }

//        ShiftForeignVo currentShift = shiftSrv.getShiftByOrgListDateTime(allOrgList.stream().map(SysOrg::getOrgcode).collect(Collectors.toList()), nowdt);
        if (currentShift == null || StringUtils.isEmpty(currentShift.getOrgCode())) {
            return "当前不当班，无法反馈";
        }
//        if (StringUtils.isNotEmpty(endDt)) {
//            //有截止时间
//            if (nowdt.compareTo(endDt) > 0) {
//                return "当前时间已超过截止时间";
//            }
//        }

        boolean hasAlreadyAcc = false;
        List<WIProcessDataStepAcc> alreadyAccList = entityService.queryData(WIProcessDataStepAcc.class, Where.create().eq(WIProcessDataStepAcc::getProcessDataStepId, currentInputStep.getId()).eq(WIProcessDataStepAcc::getObjType, 10), null, null);
        if (StringUtils.isNotEmpty(alreadyAccList)) {
            for (WIProcessDataStepAcc acc : alreadyAccList) {
                if (acc.getHandleUserId().equals(currentUserId)) {
                    hasAlreadyAcc = true;
                    break;
                }
            }
        }
        if (!hasAlreadyAcc) {
            //创建一个已办记录
            WIProcessDataStepAcc alreadyAcc = new WIProcessDataStepAcc();
            alreadyAcc.setId(TMUID.getUID());
            alreadyAcc.setDataId(currentInputStep.getDataId());
            alreadyAcc.setProcessDataId(currentInputStep.getProcessDataId());
            alreadyAcc.setProcessDataStepId(currentInputStep.getId());
            alreadyAcc.setAccOrgCode(currentInputStep.getAccOrgCode());
            alreadyAcc.setAccOrgName(currentInputStep.getAccOrgName());
            alreadyAcc.setTmsort(-1);
            alreadyAcc.setObjType(10);
            alreadyAcc.setAccCode("already");
            alreadyAcc.setHandleResult(1);
            alreadyAcc.setHandleUserId(currentUserId);
            alreadyAcc.setHandleUserName(currentUserName);
            alreadyAcc.setHandleTime(nowdt);
            alreadyAcc.setHandleUserOrgCode(currentUserOrgCode);
            entityService.insert(alreadyAcc);
        }

        Where where = Where.create();
        where.eq(WIDataOrgFeedbackInfo::getDataId, currentInputStep.getDataId());
        where.eq(WIDataOrgFeedbackInfo::getOrgCode,  currentShift.getOrgCode());
        where.eq(WIDataOrgFeedbackInfo::getShiftDate, currentShift.getTbrq());
        List<WIDataOrgFeedbackInfo> infoList = entityService.queryData(WIDataOrgFeedbackInfo.class, where, null, null);
        WIDataOrgFeedbackInfo info = StringUtils.isEmpty(infoList) ? null : infoList.get(0);
        boolean insertFlag = false;
        if (info == null) {
            //没有记录需要新增
            insertFlag = true;
            info = new WIDataOrgFeedbackInfo();
            info.setId(TMUID.getUID());
            info.setDataId(currentInputStep.getDataId());
            info.setProcessDataId(currentInputStep.getProcessDataId());
            info.setProcessDataStepId(currentInputStep.getId());
            info.setAccOrgCode(currentInputStep.getAccOrgCode());
            info.setAccOrgName(currentInputStep.getAccOrgName());
            info.setOrgCode(currentShift.getOrgCode());
            String shiftOrgName = currentShift.getOrgName();
            if (StringUtils.isEmpty(shiftOrgName) && StringUtils.isNotEmpty(currentShift.getOrgCode())) {
                SysOrg org = orgSrv.findOrgById(currentShift.getOrgCode());
                shiftOrgName = org == null ? null : org.getOrgname();
            }
            info.setOrgName(shiftOrgName);
            info.setShiftCode(currentShift.getShiftClassCode());
            info.setShiftName(currentShift.getShiftClassName());
            info.setShiftDate(currentShift.getTbrq());
            info.setShiftStartDt(currentShift.getSbsj());
            info.setShiftEndDt(currentShift.getXbsj());
        }

        info.setFeedbackDt(nowdt);
        info.setFeedbackContent(saveData.getFeedbackContent());
        info.setFeedbackStatus(saveData.getFeedbackStatus());
        info.setFeedbackUserId(currentUserId);
        info.setFeedbackUserName(currentUserName);
        info.setFeedbackComplete(saveData.getStepStatus());

        currentInputStep.setHandleResult(1);
        currentInputStep.setHandleTime(nowdt);
        currentInputStep.setHandleUserName(currentUserName);
        currentInputStep.setHandleUserId(currentUserId);
        currentInputStep.setHandleUserOrgCode(currentUserOrgCode);

        currentInputStepAcc.setHandleResult(1);
        currentInputStepAcc.setHandleUserId(currentUserId);
        currentInputStepAcc.setHandleUserName(currentUserName);
        currentInputStepAcc.setHandleTime(nowdt);
        currentInputStepAcc.setHandleUserOrgCode(currentUserOrgCode);

        if (insertFlag) {
            entityService.insert(info);
        } else {
            entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(info)), "feedbackDt", "feedbackContent", "feedbackStatus", "feedbackUserId", "feedbackUserName", "feedbackComplete");
        }

        entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentInputStep)), "handleResult", "handleTime", "handleUserId", "handleUserName", "handleUserOrgCode");
        entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentInputStepAcc)), "handleResult", "handleTime", "handleUserId", "handleUserName", "handleUserOrgCode");

        if (uploadFiles != null && uploadFiles.length > 0) {
            //有新上传的附件
            dataUtils.fileUpload(uploadFiles, data.getId(), info.getAccOrgCode(), 1, info.getId());
        }
        if (StringUtils.isNotEmpty(saveData.getDeleteFileIdArray())) {
            //删除的附件
            dataUtils.deleteUploadFiles(saveData.getDeleteFileIdArray());
        }

        if (saveData.getStepStatus() == 2) {
            //彻底结束

            WIDataOrgInfo orgInfo = dataUtils.getWIDataOrgInfo(dataId, currentInputStep.getAccOrgCode());
            if (orgInfo != null) {
                orgInfo.setCompleteStatus(1);
                orgInfo.setCompleteUserId(currentUserId);
                orgInfo.setCompleteUserName(currentUserName);
                orgInfo.setCompleteDt(nowdt);
                entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(orgInfo)), "completeStatus", "completeUserId", "completeUserName", "completeDt");
            }

            //先清除当前步骤待办
            currentInputStep.setCurrentStatus(0);
            entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentInputStep)), "currentStatus");
            Where currentAccWhere = Where.create();
            currentAccWhere.eq(WIProcessDataStepAcc::getDataId, dataId);
            currentAccWhere.eq(WIProcessDataStepAcc::getProcessDataStepId, currentInputStep.getId());
            entityService.rawUpdate(WIProcessDataStepAcc.class, Update.create(WIProcessDataStepAcc::getCurrentStatus, 0), currentAccWhere);

            if (StringUtils.isNotEmpty(currentInputStep.getInputFromId())) {
                //清除上级接收人的待办
                Where stepWhere = Where.create();
                stepWhere.eq(WIProcessDataStep::getDataId, dataId);
                stepWhere.eq(WIProcessDataStep::getId, currentInputStep.getInputFromId());
                entityService.rawUpdate(WIProcessDataStep.class, Update.create(WIProcessDataStep::getCurrentStatus, 0), stepWhere);

                Where accWhere = Where.create();
                accWhere.eq(WIProcessDataStepAcc::getDataId, dataId);
                accWhere.eq(WIProcessDataStepAcc::getProcessDataStepId, currentInputStep.getInputFromId());
                entityService.rawUpdate(WIProcessDataStepAcc.class, Update.create(WIProcessDataStepAcc::getCurrentStatus, 0), accWhere);
            }

            //检查其他车间是否完成，全部完成则更新总状态
            this.checkAndUpdateDataSuccess(data);
            String inputStartId = currentInputStep.getInputStartId();
            WIProcessDataStep startStep = StringUtils.isEmpty(inputStartId) ? null : entityService.queryObjectById(WIProcessDataStep.class, inputStartId);

            if (startStep != null && startStep.getFeedback() == 1) {
                //需要上级需要再反馈，生成一条待反馈记录
                WIProcessDataStep feedbackStep = new WIProcessDataStep();
                feedbackStep.setAccOrgName(startStep.getAccOrgName());
                feedbackStep.setAccOrgCode(startStep.getAccOrgCode());
                feedbackStep.setProcessId(startStep.getProcessId());
                feedbackStep.setProcessDataId(startStep.getProcessDataId());
                feedbackStep.setInAcceptOrg(startStep.getInAcceptOrg());
                feedbackStep.setDataId(startStep.getDataId());
                feedbackStep.setStepName("反馈");
                feedbackStep.setStepDesc("反馈");
                feedbackStep.setTmused(1);
                feedbackStep.setId(TMUID.getUID());
                feedbackStep.setStepHandle(4);
                feedbackStep.setHandleMessage("");
//                feedbackStep.setFeedbackStatus(0);
//                feedbackStep.setAccType(1);
//                feedbackStep.setAccCode(startStep.getHandleUserId());
//                feedbackStep.setAccText(startStep.getHandleUserName());
                feedbackStep.setStepNo(currentInputStep.getStepNo() + 1);
                feedbackStep.setHandleResult(0);
                feedbackStep.setCurrentStatus(1);

                feedbackStep.setInputFromId(null);
                feedbackStep.setInputStartId(null);
                feedbackStep.setHandleUserId(null);
                feedbackStep.setHandleUserName(null);
                feedbackStep.setHandleTime(null);

                WIProcessDataStepAcc feedbackStepAcc = new WIProcessDataStepAcc();
                feedbackStepAcc.setId(TMUID.getUID());
                feedbackStepAcc.setDataId(feedbackStep.getDataId());
                feedbackStepAcc.setProcessDataId(feedbackStep.getProcessDataId());
                feedbackStepAcc.setProcessDataStepId(feedbackStep.getId());
                feedbackStepAcc.setAccOrgCode(feedbackStep.getAccOrgCode());
                feedbackStepAcc.setAccOrgName(feedbackStep.getAccOrgName());
                feedbackStepAcc.setObjType(1);
                feedbackStepAcc.setAccType(1);
                feedbackStepAcc.setAccCode(startStep.getHandleUserId());
                feedbackStepAcc.setAccText(startStep.getHandleUserName());
                feedbackStepAcc.setCurrentStatus(1);

                entityService.insert(feedbackStep);
                entityService.insert(feedbackStepAcc);

                feedbackStep.setAccList(new ArrayList<>(Collections.singletonList(feedbackStepAcc)));
                //TODO 给上级发消息告知已填写反馈内容
                msgSrv.sendTodoMsgAndCcMsg(data, currentInputStep, new ArrayList<>(Collections.singletonList(feedbackStep)), 0, null);
            } else if (startStep != null) {
                //不需要再反馈，只发消息通知
                //TODO 给上级发消息告知已填写反馈内容
            }

        }



        return "";
    }

    /**
     * 反馈
     *
     * @param uploadFiles
     * @param saveData
     * @return
     */
    @Override
    public String feedback(MultipartFile[] uploadFiles, WIDataFeedbackDto saveData) {

        String stepParams = saveData.getDataStepId();
        if (StringUtils.isEmpty(stepParams)) {
            return "传入参数有误";
        }
        WIProcessDataStep currentAcceptStep = null;
        WIProcessDataStepAcc currentAcceptStepAcc = null;
        WIProcessDataStep currentInputStep = null;
        WIProcessDataStepAcc currentInputStepAcc = null;
        if (StringUtils.isNotEmpty(stepParams)) {
            JSONObject jsonObject = JSON.parseObject(stepParams);
            String inputStepId = jsonObject.containsKey("inputStepId") ? jsonObject.getString("inputStepId") : "";
            String inputStepAccId = jsonObject.containsKey("inputStepAccId") ? jsonObject.getString("inputStepAccId") : "";
            if (StringUtils.isNotEmpty(inputStepId)) {
                //接收指令时，查当前节点信息
                currentInputStep = entityService.queryObjectById(WIProcessDataStep.class, inputStepId);
            }
            if (StringUtils.isNotEmpty(inputStepAccId)) {
                //接收指令时，查当前节点信息
                currentInputStepAcc = entityService.queryObjectById(WIProcessDataStepAcc.class, inputStepAccId);
            }
            String acceptStepId = jsonObject.containsKey("acceptStepId") ? jsonObject.getString("acceptStepId") : "";
            String acceptStepAccId = jsonObject.containsKey("acceptStepAccId") ? jsonObject.getString("acceptStepAccId") : "";
            if (StringUtils.isNotEmpty(acceptStepId)) {
                //接收指令时，查当前节点信息
                currentAcceptStep = entityService.queryObjectById(WIProcessDataStep.class, acceptStepId);
            }
            if (StringUtils.isNotEmpty(acceptStepAccId)) {
                //接收指令时，查当前节点信息
                currentAcceptStepAcc = entityService.queryObjectById(WIProcessDataStepAcc.class, acceptStepAccId);
            }
        }
        if ((currentAcceptStep == null || currentAcceptStepAcc == null) && (currentInputStep == null || currentInputStepAcc == null)) {
            return "未找到当前节点";
        }

        String dataId = currentAcceptStep != null ? currentAcceptStep.getDataId() : currentInputStep.getDataId();
        if (StringUtils.isEmpty(dataId)) {
            return "找不到记录";
        }
        WIData data = entityService.queryObjectById(WIData.class, dataId);

//        int handleResult = currentInputStepAcc.getHandleResult();
//        int currentStatus = currentInputStepAcc.getCurrentStatus();
//        if (currentStatus != 1) {
//            return "当前节点不是待处理节点";
//        }

//        String inputStepId = saveData.getInputStepId();
//        String feedbackId = saveData.getFeedbackId();
        if (currentInputStep != null && currentInputStepAcc != null && currentInputStep.getCurrentStatus() == 1) {
            //填报反馈
            return this.inputFeedback(uploadFiles, saveData, currentInputStep, currentInputStepAcc);
        }

        //不是填报反馈则为向上反馈
        WIProcessDataStep currentStep = currentAcceptStep;
        WIProcessDataStepAcc currentStepAcc = currentAcceptStepAcc;
        if (currentStep == null || currentStepAcc == null) {
            return "未找到当前节点";
        }
        if (currentStepAcc.getCurrentStatus() != 1) {
            return "当前节点不是待处理节点";
        }

        SysUser user = SysUserUtil.getCurrentUser();
        String currentUserId = user.getId();
        String currentUserName = user.getRealName();

        String nowdt = DateTimeUtils.getNowDateTimeStr();
        List<WIDataOrgInfo> orgInfoList = entityService.queryData(WIDataOrgInfo.class, Where.create().eq(WIDataOrgInfo::getDataId, dataId).eq(WIDataOrgInfo::getAccOrgCode, currentStep.getAccOrgCode()), null, null);
        WIDataOrgInfo orgInfo = StringUtils.isNotEmpty(orgInfoList) ? orgInfoList.get(0) : null;
        if (orgInfo != null) {
            orgInfo.setFeedbackStatus(saveData.getFeedbackStatus());
            orgInfo.setFeedbackContent(saveData.getFeedbackContent());
            orgInfo.setFeedbackDt(nowdt);
            orgInfo.setFeedbackComplete(1);
            orgInfo.setFeedbackUserId(currentUserId);
            orgInfo.setFeedbackUserName(currentUserName);
            List<String> updCols = new ArrayList<>();
            updCols.add("feedbackStatus");
            updCols.add("feedbackContent");
            updCols.add("feedbackDt");
            updCols.add("feedbackComplete");
            updCols.add("feedbackUserId");
            updCols.add("feedbackUserName");

            entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(orgInfo)), updCols.toArray(updCols.toArray(new String[0])));
        }

        currentStep.setHandleMessage(null);
        currentStep.setHandleUserId(currentUserId);
        currentStep.setHandleUserName(currentUserName);
        currentStep.setHandleTime(DateTimeUtils.getNowDateTimeStr());
        currentStep.setHandleResult(1);
        currentStep.setCurrentStatus(0);

        currentStepAcc.setHandleMessage(null);
        currentStepAcc.setHandleUserId(currentUserId);
        currentStepAcc.setHandleUserName(currentUserName);
        currentStepAcc.setHandleTime(DateTimeUtils.getNowDateTimeStr());
        currentStepAcc.setHandleResult(1);
        currentStepAcc.setCurrentStatus(0);

        entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentStep)), "handleMessage", "handleUserId", "handleUserName", "handleTime", "handleResult", "currentStatus");
        entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentStepAcc)), "handleMessage", "handleUserId", "handleUserName", "handleTime", "handleResult", "currentStatus");

        if (uploadFiles != null && uploadFiles.length > 0) {
            dataUtils.fileUpload(uploadFiles, data.getId(), currentStep.getAccOrgCode(), 2, orgInfo == null ? null : orgInfo.getId());
        }

        return "";
    }

    public WIDataOrgFeedbackInfo queryCurrentStepFeedbackInfo (WIDataFeedbackDto saveData) {
//        String dataId = saveData.getDataId();
//        String dataStepId = saveData.getDataStepId();
        String stepParams = saveData.getDataStepId();
        if (StringUtils.isEmpty(stepParams)) {
            return null;
        }
        WIProcessDataStep currentInputStep = null;
        WIProcessDataStepAcc currentInputStepAcc = null;
        if (StringUtils.isNotEmpty(stepParams)) {
            JSONObject jsonObject = JSON.parseObject(stepParams);
            String inputStepId = jsonObject.containsKey("inputStepId") ? jsonObject.getString("inputStepId") : "";
            String inputStepAccId = jsonObject.containsKey("inputStepAccId") ? jsonObject.getString("inputStepAccId") : "";
            if (StringUtils.isNotEmpty(inputStepId)) {
                //接收指令时，查当前节点信息
                currentInputStep = entityService.queryObjectById(WIProcessDataStep.class, inputStepId);
            }
            if (StringUtils.isNotEmpty(inputStepAccId)) {
                //接收指令时，查当前节点信息
                currentInputStepAcc = entityService.queryObjectById(WIProcessDataStepAcc.class, inputStepAccId);
            }
        }
        if (currentInputStep == null || currentInputStepAcc == null) {
            return null;
        }

//        String nowdt = DateTimeUtils.getNowDateTimeStr();
//        List<SysOrg> allOrgList = orgSrv.getOrgList(currentInputStep.getAccOrgCode());
//        List<String> childrenOrg = StringUtils.isEmpty(allOrgList) ? null : allOrgList.stream().filter(item -> !"workshop".equals(item.getOrgType())).map(SysOrg::getOrgcode).collect(Collectors.toList());
//        if (StringUtils.isEmpty(childrenOrg)) {
//            return null;
//        }
//        ShiftForeignVo currentShift = shiftSrv.getShiftByOrgListDateTime(childrenOrg, nowdt);
//        if (currentShift == null) {
//            return null;
//        }
        ShiftForeignVo currentShift = null;
        if (StringUtils.isNotEmpty(currentInputStepAcc.getPartTimePostOrgCode())) {
            //兼岗，用兼岗的班组取班次
            currentShift = dataUtils.getOrgNowShiftInfo(currentInputStepAcc.getPartTimePostOrgCode());
        } else {
            currentShift = dataUtils.getCurrentUserShiftInfo();
        }
//        ShiftForeignVo currentShift = dataUtils.getCurrentUserShiftInfo();
        if (currentShift == null || StringUtils.isEmpty(currentShift.getOrgCode())) {
            return null;
        }
        Where where = Where.create();
        where.eq(WIDataOrgFeedbackInfo::getDataId, currentInputStep.getDataId());
        where.eq(WIDataOrgFeedbackInfo::getOrgCode,  currentShift.getOrgCode());
        where.eq(WIDataOrgFeedbackInfo::getShiftDate, currentShift.getTbrq());
        List<WIDataOrgFeedbackInfo> infoList = entityService.queryData(WIDataOrgFeedbackInfo.class, where, null, null);
        WIDataOrgFeedbackInfo info = StringUtils.isEmpty(infoList) ? null : infoList.get(0);

        if (info != null) {
            List<WIDataFile> fileList = entityService.queryData(WIDataFile.class, Where.create().eq(WIDataFile::getInfoId, info.getId()), Order.create().orderByAsc(WIDataFile::getId), null);
            if (StringUtils.isNotEmpty(fileList)) {
                for (WIDataFile file : fileList) {
                    file.setCanDelete(true);
                }
            }
            info.setFileList(fileList);
        }

        return info;
    }

    public WIFeedbackItemVo queryOrgFeedbackInfo (WIFeedbackItemVo dto) {
        String dataId = dto.getDataId();
        String orgCode = dto.getAcceptOrgCode();
        Where where = Where.create();
        where.eq(WIDataOrgInfo::getDataId, dataId);
        where.eq(WIDataOrgInfo::getAccOrgCode, orgCode);
        List<WIDataOrgInfo> wiDataOrgInfos = entityService.queryData(WIDataOrgInfo.class, where, null, null);
        WIDataOrgInfo orgInfo = StringUtils.isEmpty(wiDataOrgInfos) ? null : wiDataOrgInfos.get(0);
        Where fileWhere = Where.create();
        fileWhere.eq(WIDataFile::getDataId, dataId);
        fileWhere.eq(WIDataFile::getOrgCode, orgCode);
        List<WIDataFile> wiDataFiles = entityService.queryData(WIDataFile.class, fileWhere, Order.create().orderByAsc(WIDataFile::getId), null);
        WIFeedbackItemVo result = new WIFeedbackItemVo();
        if (orgInfo != null) {
            result.setDataId(orgInfo.getDataId());
            result.setOrgInfoId(orgInfo.getId());
            result.setSummaryStatus(orgInfo.getSummaryStatus());
            result.setFeedbackStatus(orgInfo.getFeedbackStatus());
            result.setFeedbackContent(orgInfo.getFeedbackContent());
            if (StringUtils.isNotEmpty(wiDataFiles)) {
                result.setFileList(wiDataFiles);
            }
        }
        return result;
    }

    /**
     * 查询反馈列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<WIFeedbackItemVo> queryFeedbackItemList(WIDataFeedbackQueryDto dto) {
        if (dto == null) {
            return null;
        }
        String mode = dto.getMode();
        Where where = Where.create();
        where.eq(WIData::getTmused, 1);
        where.eq(WIData::getCategory, WIDataUtils.WI_DATA_CATEGORY_WI);
        where.eq(WIData::getInAcceptOrg, 0);
        String startDt = dto.getStartDt();
        String endDt = dto.getEndDt();
        String dataName = dto.getDataName();
        String orgCode = dto.getOrgCode();
//        if (StringUtils.isNotEmpty(startDt)) {
//            where.ge(WIData::getStartDt, startDt + " 23:59:59");
//        }
//        if (StringUtils.isNotEmpty(endDt)) {
//            where.le(WIData::getStartDt, endDt + " 23:59:59");
//        }
        if (StringUtils.isNoneEmpty(startDt, endDt)) {
            // start>=st && end<=ed   start<=st && end>=st   start>=st && start<=end   end>=st && end<=ed
            // start betweend st and ed  and end is null
            where.and();
            where.lb();

            where.between(WIData::getStartDt, startDt+" 00:00:00", endDt+" 23:59:59");

            where.or().between(WIData::getEndDt, startDt+" 00:00:00", endDt+" 23:59:59");

            where.or();

            where.lb();

            where.lt(WIData::getStartDt, startDt+" 00:00:00");
            where.and();
            where.lb();
            where.gt(WIData::getEndDt, endDt+" 23:59:59");
            where.or().isEmpty(WIData::getEndDt);
            where.or().isNull(WIData::getEndDt);
            where.rb();

            where.rb();


            where.rb();

        }
//        if (StringUtils.isNotEmpty(endDt)) {
//            where.and();
//            where.lb();
//            where.isEmpty(WIData::getEndDt);
//            where.or().isNull(WIData::getEndDt);
//            where.or().le(WIData::getEndDt, endDt+" 23:59:59");
//            where.rb();
//        }
        Order order = Order.create();
        order.orderByAsc(WIData::getCreateTime);
        List<WIData> list = entityService.queryData(WIData.class, where, order, null);
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        List<WIData> dataResult = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        for (WIData data : list) {
            if (StringUtils.isNotEmpty(dataName)) {
                if (!data.getDataName().contains(dataName)) {
                    continue;
                }
            }
            dataResult.add(data);
            idList.add(data.getId());
        }
//        if (StringUtils.isNotEmpty(dataName)) {
//            dataResult = new ArrayList<>();
//            List<String> idList = new ArrayList<>();
//            for (WIData data : list) {
//                if (data.getDataName().contains(dataName)) {
//                    dataResult.add(data);
//                    idList.add(data.getId());
//                }
//            }
//            idArray = idList.toArray();
//        } else {
//            dataResult = list;
//            idArray = list.stream().map(WIData::getId).toArray();
//        }

        if (StringUtils.isEmpty(dataResult)) {
            return null;
        }
        Where orgWhere = Where.create();
        orgWhere.in(WIDataOrgInfo::getDataId, idList.toArray());
        orgWhere.eq(WIDataOrgInfo::getFeedbackComplete, 1);
        if (StringUtils.isNotEmpty(orgCode)) {
            orgWhere.eq(WIDataOrgInfo::getAccOrgCode, orgCode);
        }
        if (dto.getFeedbackStatus() != 0) {
            orgWhere.eq(WIDataOrgInfo::getFeedbackStatus, dto.getFeedbackStatus());
        }
        List<WIDataOrgInfo> orgList = entityService.queryData(WIDataOrgInfo.class, orgWhere, Order.create(), null);
        if (StringUtils.isEmpty(orgList)) {
            return null;
        }

        Map<String, List<WIDataOrgInfo>> group = orgList.stream().collect(Collectors.groupingBy(WIDataOrgInfo::getDataId));

        //查找指令关联的操作卡生成的实例id
        Where cardWhere = Where.create();
        List<WIDataOrgOpCardInfo> cardInfoList=entityService.queryData(WIDataOrgOpCardInfo.class,cardWhere, Order.create(), null);
        Map<String, List<WIDataOrgOpCardInfo>> cardInfoMap = cardInfoList.stream().collect(Collectors.groupingBy(WIDataOrgOpCardInfo::getDataId));
        List<WIFeedbackItemVo> result = new ArrayList<>();
        for (WIData data : dataResult) {
            List<WIDataOrgInfo> dataOrgInfoList = group.get(data.getId());
            //根据指令id获取绑定该指令上的操作卡
            List<WIDataOrgOpCardInfo> cardList= cardInfoMap.get(data.getId());
            if (StringUtils.isEmpty(dataOrgInfoList)) {
                continue;
            }
            for (WIDataOrgInfo orgInfo : dataOrgInfoList) {
                int summaryStatus = orgInfo.getSummaryStatus();
                if ("submit".equals(mode)) {
                    //汇总模式

                } else if (summaryStatus != 1){
                    //查询模式只查提交过的
                    continue;
                }

                WIFeedbackItemVo vo = new WIFeedbackItemVo();
                vo.setOrgInfoId(orgInfo.getId());
                vo.setDataId(data.getId());
                vo.setDataName(data.getDataName());
                vo.setDataContent(data.getDataContent());
                vo.setAcceptOrgName(orgInfo.getAccOrgName());
                vo.setSubmitDt(data.getSubmitDt());
                vo.setSubmitUser(data.getSubmitUserName());
                vo.setAcceptUser(orgInfo.getAccUserName());
                vo.setAcceptDt(orgInfo.getAccDt());
                int feedbackStatus = orgInfo.getFeedbackStatus();
                String feedbackContent = orgInfo.getFeedbackContent();
                String feedbackText = "状态："+(feedbackStatus==2?"已完成":(feedbackStatus==-1 ? "未完成": "进行中"))+"。反馈内容："+feedbackContent;
                vo.setFeedbackStatus(feedbackStatus);
                vo.setFeedbackContent(feedbackContent);
                vo.setFeedbackText(feedbackText);
                vo.setCompleteDt(orgInfo.getCompleteDt());
                vo.setSummaryStatus(orgInfo.getSummaryStatus());
                vo.setCardInfoList(cardList);

                result.add(vo);
            }
        }

        //result按照submitDt倒叙
        if (StringUtils.isNotEmpty(result)) {
            result.sort(Comparator.comparing(WIFeedbackItemVo::getSubmitDt).reversed());
        }
        return result;
    }

    /**
     * 修改反馈内容
     *
     * @param list
     * @return
     */
    @Override
    public String modifyFeedbackContent(List<WIFeedbackItemVo> list) {
        if (StringUtils.isEmpty(list)) {
            return "没有要保存的记录";
        }
        List<WIDataOrgInfo> updateList = new ArrayList<>();
        for (WIFeedbackItemVo vo : list) {
            WIDataOrgInfo orgInfo = new WIDataOrgInfo();
            orgInfo.setId(vo.getOrgInfoId());
            orgInfo.setFeedbackStatus(vo.getFeedbackStatus());
            orgInfo.setFeedbackContent(vo.getFeedbackContent());
            updateList.add(orgInfo);
        }

        entityService.updateByIdBatch(updateList, "feedbackStatus", "feedbackContent");

        return "";
    }

    /**
     * 提交汇总反馈
     *
     * @param dto
     * @return
     */
    @Override
    public String submitFeedbackItemList(WIDataFeedbackSubmitDto dto) {
        String content = Optional.ofNullable(dto).map(WIDataFeedbackSubmitDto::getSubmitContent).orElse(null);
        List<WIFeedbackItemVo> itemList = Optional.ofNullable(dto).map(WIDataFeedbackSubmitDto::getItemList).orElse(null);

        if (StringUtils.isEmpty(content)) {
            return "反馈内容不能为空";
        }
        if (StringUtils.isEmpty(itemList)) {
            return "没有要汇总的记录";
        }

        List<WIDataOrgInfo> updateList = new ArrayList<>();

        String nowdt = DateTimeUtils.getNowDateTimeStr();
        String currentUserId = SysUserUtil.getCurrentUser().getId();
        String currentUserName = SysUserUtil.getCurrentUser().getRealName();
        for (WIFeedbackItemVo vo : itemList) {
            WIDataOrgInfo orgInfo = new WIDataOrgInfo();
            orgInfo.setId(vo.getOrgInfoId());
            orgInfo.setSummaryStatus(1);
            orgInfo.setSummaryDt(nowdt);
            orgInfo.setSummaryUserId(currentUserId);
            orgInfo.setSummaryUserName(currentUserName);
            updateList.add(orgInfo);
        }

        entityService.updateByIdBatch(updateList, "summaryStatus", "summaryDt", "summaryUserId", "summaryUserName");


        return "";
    }


    /**
     * 检测记录是否通过并修改状态
     *
     * @param data
     * @return
     */
    @Override
    public String checkAndUpdateDataSuccess(WIData dto) {
        if (dto == null) {
            return null;
        }
        List<WIDataOrgInfo> orgInfoList = dataUtils.getOrgInfoList(dto.getId());
        if (StringUtils.isEmpty(orgInfoList)) {
            return null;
        }
        int completeOrgCount = 0;
        boolean allSuccess = true;
        for (WIDataOrgInfo orgInfo : orgInfoList) {
            if (orgInfo.getCompleteStatus() != 1) {
                //未完成
                allSuccess = false;
            } else {
                //完成
                completeOrgCount++;
            }
        }


        if (allSuccess) {
            WIData data = entityService.queryObjectById(WIData.class, dto.getId());
            String flag = dataSrv.changeDataStatus(data, 10, completeOrgCount);
        }
        return "";
    }

}
