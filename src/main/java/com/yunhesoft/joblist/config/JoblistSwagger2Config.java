package com.yunhesoft.joblist.config;

import com.yunhesoft.core.common.component.Swagger2Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

@Configuration
@EnableSwagger2WebMvc
public class JoblistSwagger2Config {
    @Autowired
    private Swagger2Utils utils;

    @Bean(value = "joblist-api")
    public Docket joblistDocket() {
        ApiInfo apiInfo = utils.apiInfo("joblist-api", "", "0.0.1");
        return utils.newDocket(apiInfo, "tm4-智能化岗位责任清单", "com.yunhesoft.joblist");
    }
}
