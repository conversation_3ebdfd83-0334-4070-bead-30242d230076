//package com.yunhesoft.joblist.listener;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.yunhesoft.core.utils.ObjUtils;
//import com.yunhesoft.joblist.entity.dto.AccountSaveDto;
//import com.yunhesoft.joblist.service.IJobGeneraterService;
//import com.yunhesoft.mobile.entity.vo.AcctobjInputVo;
//import com.yunhesoft.mobile.exception.MobileException;
//import com.yunhesoft.mobile.service.IMobLeanCostingService;
//import com.yunhesoft.system.tds.event.AccountSaveEvent;
//import lombok.extern.log4j.Log4j2;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.ApplicationListener;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//@Log4j2
//@Component
//public class AccountSaveListener implements ApplicationListener<AccountSaveEvent> {
//    @Autowired
//    private IMobLeanCostingService mobLeanCostingService;
//    @Autowired
//    private IJobGeneraterService jobGeneraterService;
//
//    @Override
//    public void onApplicationEvent(AccountSaveEvent event) {
//        com.yunhesoft.system.applyConf.entity.vo.AccountSaveDto src = (com.yunhesoft.system.applyConf.entity.vo.AccountSaveDto) event.getSource();
//        AccountSaveDto source = JSONObject.parseObject(JSONObject.toJSONString(src), AccountSaveDto.class);
//        if (ObjUtils.isEmpty(source)) {
//            log.error("台账保存事件源数据无效");
//            return;
//        }
//        String operType = source.getOperType();
//        //业务类型判断
//        String type = source.getType();
//        if (AccountSaveDto.TYPE_COLLECTION_POINT.equals(type)) { //采集点台账
//            //更新 APP 端采集点数据
//            try {
//                //List<AcctobjInputVo> collectionPointInputData = source.getCollectionPointInputData();
//                if (AccountSaveDto.OPERTYPE_CONFIRM.equals(operType)) { //确认操作
//                    // TODO 调用活动状态更新接口，如活动为手动确认则活动状态更新为已反馈，如活动为自动确认则活动状态更新为已完成
//                    //提示：collectionPointInputData 按照台账功能实现目前只给传递一条数据（每行后边有个确认按钮），AcctobjInputVo 中的 taskId 为子活动的 id
//                    JSONArray jsonArray = JSONArray.parseArray(JSONArray.toJSONString(source.getCollectionPointInputData()));
//                    jobGeneraterService.saveActivityStatusHandler(jsonArray);
//                } else { //普通保存
//                    mobLeanCostingService.syncDataWithAccount(source);
//                }
//            } catch (MobileException e) {
//                log.error("台账保存事件源数据处理有误", e);
//            }
//        } else if (AccountSaveDto.TYPE_ROUTING_INSPECTION.equals(type)) { //巡检台账
//            if (AccountSaveDto.OPERTYPE_CONFIRM.equals(operType)) {
//                // TODO 调用活动状态更新接口，如活动为手动确认则活动状态更新为已反馈，如活动为自动确认则活动状态更新为已完成
//                //提示：collectionPointInputData 按照台账功能实现目前只给传递一条数据（每行后边有个确认按钮），AcctobjInputVo 中的 taskId 为子活动的 id
//                JSONArray jsonArray = JSONArray.parseArray(JSONArray.toJSONString(source.getCollectionPointInputData()));
//                jobGeneraterService.saveActivityStatusHandler(jsonArray);
//            }
//        } else {
//            log.error("台账保存事件源数据业务类型无效");
//        }
//    }
//}
