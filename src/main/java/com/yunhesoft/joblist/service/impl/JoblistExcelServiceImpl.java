package com.yunhesoft.joblist.service.impl;


import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.entity.dto.StandardJobLibQueryDto;
import com.yunhesoft.joblist.entity.po.CycleScheme;
import com.yunhesoft.joblist.entity.po.JoblistActivityProperties;
import com.yunhesoft.joblist.entity.po.JoblistClass;
import com.yunhesoft.joblist.entity.po.JoblistExtPropertiesConfig;
import com.yunhesoft.joblist.entity.po.JoblistPersonBind;
import com.yunhesoft.joblist.entity.po.JoblistProgram;
import com.yunhesoft.joblist.entity.vo.JoblistActivityPropertiesVo;
import com.yunhesoft.joblist.entity.vo.JoblistClassVo;
import com.yunhesoft.joblist.entity.vo.JoblistColmConfigVo;
import com.yunhesoft.joblist.service.IJoblistExcelService;
import com.yunhesoft.joblist.service.IJoblistMethodService;
import com.yunhesoft.joblist.service.IStandardJobLibConfigService;
import com.yunhesoft.leanCosting.baseConfig.entity.po.ProjectType;
import com.yunhesoft.leanCosting.baseConfig.service.impl.CostProjectTypeSeriveceIpml;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.kernel.utils.excel.ExcelImport;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.service.ISysDictTypeService;
import com.yunhesoft.tmsf.form.entity.po.SFForm;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;

@Service
public class JoblistExcelServiceImpl implements IJoblistExcelService {

	@Autowired
	private IJoblistMethodService methodService;
	
	@Autowired
	private IStandardJobLibConfigService jobLibConfigService;
	
	@Autowired
	private ISysDictTypeService dictTypeService; //数据字典类型服务
	
	@Autowired 
	private CostProjectTypeSeriveceIpml projectTypeService;
	
	@Autowired
	private IUnitMethodService unitMethodService; // 核算相关方法
	
	@Autowired
	private ICostService costService;
	
	private Integer POINTCOUNT = 2; //保留小数位数
	
	
	//——————————————————————————  导入 ↓  —————————————————————————————————
	
	/**
	 *	导入作业活动Excel
	 * @param file
	 * @param queryDto
	 * @return
	 */
	@Override
	public String importExcelJoblistActivity(MultipartFile file, StandardJobLibQueryDto queryDto) {
		String result = "";
		if(file!=null&&queryDto!=null) {
			String orgid = queryDto.getOrgid();
			String typeid = queryDto.getTypeid();
			String unitid = queryDto.getUnitid();
			if(StringUtils.isNotEmpty(orgid)&&StringUtils.isNotEmpty(unitid)) {
				try {
					Workbook wb = WorkbookFactory.create(file.getInputStream());
					if(wb!=null) {
						Sheet sheet = wb.getSheetAt(0);// 获取sheet页
						if(sheet!=null) {
							result = this.importActivity(file, orgid, typeid, unitid);
						}
					}
				} catch (EncryptedDocumentException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return result;
	}
	
	//导入作业活动
	@SuppressWarnings("unchecked")
	private String importActivity(MultipartFile file, String orgid, String typeid, String unitid) {
		String result = "";
		String errMess = "";
		int dataCount = 0;
		List<JoblistClass> addClassList = new ArrayList<JoblistClass>();
		List<JoblistPersonBind> addPersonBindList = new ArrayList<JoblistPersonBind>();
		List<Costuint> addUnitList = new ArrayList<Costuint>();
		List<JoblistActivityProperties> addList = new ArrayList<JoblistActivityProperties>();
		List<JoblistActivityProperties> updList = new ArrayList<JoblistActivityProperties>();
		List<JoblistPersonBind> addBindList = new ArrayList<JoblistPersonBind>();
		List<JoblistPersonBind> delBindList = new ArrayList<JoblistPersonBind>();
		HashMap<String, String> bindPostIdMap = new HashMap<String, String>(); //绑定岗位对应的机构同步到核算对象的操作机构中
		//获取配置列信息
		List<JoblistColmConfigVo> confList = this.getJoblistColmConfigList();
		//判断参数有效
		if(file!=null && StringUtils.isNotEmpty(orgid) && StringUtils.isNotEmpty(typeid) 
			&& StringUtils.isNotEmpty(unitid) && StringUtils.isNotEmpty(confList)) {
			try {
				ImportParams importParams = new ImportParams();
				importParams.setStartRows(0);
				importParams.setStartSheetIndex(0);
		        ExcelImportResult<?> excelResult = ExcelImport.importExcel(file.getInputStream(), Map.class, importParams);
		        if (excelResult != null) {
		        	List<LinkedHashMap<String, Object>> dataList = (List<LinkedHashMap<String, Object>>) excelResult.getList();
	        		if(StringUtils.isNotEmpty(dataList)) { //Excel中有数据
	        			//获取相关配置信息
	        			
	        			//列名称Map
	        			Map<String, String> colmNameMap = confList.stream().collect(Collectors.toMap(JoblistColmConfigVo::getColmCode, JoblistColmConfigVo::getColmName, (key1, key2) -> key2));
	        			String classid_label = "分类";
	        			if(StringUtils.isNotEmpty(colmNameMap)&&colmNameMap.containsKey("classid")) {
	        				classid_label = colmNameMap.get("classid");
	        			}
	        			String unitName_label = "活动名称";
	        			if(StringUtils.isNotEmpty(colmNameMap)&&colmNameMap.containsKey("unitName")) {
	        				unitName_label = colmNameMap.get("unitName");
	        			}
	        			String positionArea_label = "负责区域";
	        			if(StringUtils.isNotEmpty(colmNameMap)&&colmNameMap.containsKey("positionArea")) {
	        				positionArea_label = colmNameMap.get("positionArea");
	        			}
	        			String involveDevice_label = "涉及设备";
	        			if(StringUtils.isNotEmpty(colmNameMap)&&colmNameMap.containsKey("involveDevice")) {
	        				involveDevice_label = colmNameMap.get("involveDevice");
	        			}
	        			String jobRequirement_label = "工作要求";
	        			if(StringUtils.isNotEmpty(colmNameMap)&&colmNameMap.containsKey("jobRequirement")) {
	        				jobRequirement_label = colmNameMap.get("jobRequirement");
	        			}
	        			String qualityRequirement_label = "质量要求";
	        			if(StringUtils.isNotEmpty(colmNameMap)&&colmNameMap.containsKey("qualityRequirement")) {
	        				qualityRequirement_label = colmNameMap.get("qualityRequirement");
	        			}
	        			String basicScore_label = "基础分值";
	        			if(StringUtils.isNotEmpty(colmNameMap)&&colmNameMap.containsKey("basicScore")) {
	        				basicScore_label = colmNameMap.get("basicScore");
	        			}
	        			String beginDate_label = "开始日期";
	        			if(StringUtils.isNotEmpty(colmNameMap)&&colmNameMap.containsKey("beginDate")) {
	        				beginDate_label = colmNameMap.get("beginDate");
	        			}
	        			String endDate_label = "结束日期";
	        			if(StringUtils.isNotEmpty(colmNameMap)&&colmNameMap.containsKey("endDate")) {
	        				endDate_label = colmNameMap.get("endDate");
	        			}
	        			
	        			//当前日期
	    				String currDate = DateTimeUtils.getNowDateStr();
	    				if(StringUtils.isNotEmpty(currDate)&&currDate.length()>10) {
	    					currDate = currDate.substring(0,10);
	    				}
	        			
	        			// 1、已存在数据
						Map<String, List<JoblistActivityProperties>> activityByClassMap = new HashMap<String, List<JoblistActivityProperties>>(); //数据按分类分组Map
						Map<String, JoblistActivityProperties> activityMap = new HashMap<String, JoblistActivityProperties>(); //数据Map
						StandardJobLibQueryDto activityDto = new StandardJobLibQueryDto();
						activityDto.setUnitid(unitid);
						activityDto.setOrgid(orgid);
						List<JoblistActivityProperties> activityList = methodService.getJoblistActivityPropertiesList(activityDto);
						if (StringUtils.isNotEmpty(activityList)) {
							activityMap = activityList.stream().collect(Collectors.toMap(JoblistActivityProperties::getId, Function.identity()));
							activityByClassMap = activityList.stream().collect(Collectors.groupingBy(JoblistActivityProperties::getClassid));
	    				}
	        			
	        			// 2、分类
						List<JoblistClass> allClassDataList = new ArrayList<JoblistClass>(); //全部分类数据
						List<String> classIdList = new ArrayList<String>();
	        			Map<String, JoblistClassVo> classMapByName = new HashMap<String, JoblistClassVo>();
	        			StandardJobLibQueryDto classDto = new StandardJobLibQueryDto();
	        			classDto.setOrgid(orgid);
	        			classDto.setTypeid(typeid);
	        			classDto.setClassDataList(allClassDataList); //返回查询的分类数据
	        			List<JoblistClassVo> classList = jobLibConfigService.getJoblistClassCombList(classDto, "root");
	        			if(StringUtils.isNotEmpty(classList)) {
	        				classMapByName = classList.stream().collect(Collectors.toMap(JoblistClassVo::getNodeShowName, Function.identity(), (key1, key2) -> key2));
	        				classIdList = classList.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
	        			}
	        			//第一层分类名称（判断生成分类用）
	        			int maxClassPx = 0;
	        			Map<String, List<JoblistClass>> firstClassNameMap = new HashMap<String, List<JoblistClass>>();
	        			if(StringUtils.isNotEmpty(allClassDataList)) {
	        				Map<String, List<JoblistClass>> allClassDataMap  = allClassDataList.stream().collect(Collectors.groupingBy(JoblistClass::getPid));
	        				if(StringUtils.isNotEmpty(allClassDataMap) && allClassDataMap.containsKey("root")) {
	        					List<JoblistClass> firstClassList = allClassDataMap.get("root");
	        					if(StringUtils.isNotEmpty(firstClassList)) {
	        						firstClassNameMap = firstClassList.stream().collect(Collectors.groupingBy(JoblistClass::getCname));
	        						Integer tmsort = firstClassList.get(firstClassList.size()-1).getTmsort();
	        						if(tmsort!=null) {
	        							maxClassPx = tmsort;
	        						}
	        					}
	        				}
	        			}
	        			
	        			// 3、活动类型
	        			Map<String, SysDictData> activityTypeMapByName = new HashMap<String, SysDictData>();
	        			List<SysDictData> activityTypeList = dictTypeService.selectDictDataByType("joblist_activity_activityType");    
	        			if(StringUtils.isNotEmpty(activityTypeList)) {
	        				activityTypeMapByName = activityTypeList.stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity(), (key1, key2) -> key2));
	    				}
	        			
	        			// 4、频次
	        			Map<String, CycleScheme> cycleMapByName = new HashMap<String, CycleScheme>();
	        			StandardJobLibQueryDto cycleDto = new StandardJobLibQueryDto();
	        			cycleDto.setOrgid(orgid);
	        			cycleDto.setQueryType("orgSys");
	    				List<CycleScheme> cycleList = methodService.getCycleSchemeList(cycleDto);
	    				if(StringUtils.isNotEmpty(cycleList)) {
	    					cycleMapByName = cycleList.stream().collect(Collectors.toMap(CycleScheme::getSname, Function.identity(), (key1, key2) -> key2));
	    				}
	    				CycleScheme noneObj = new CycleScheme();
	    				noneObj.setId("none");
	    				noneObj.setSname("不定期");
	    				cycleMapByName.put("不定期", noneObj);
	        			
	    				// 5、工况
	    				Map<String, ProjectType> statusMapByName = new HashMap<String, ProjectType>();
	    				String defaultStatus = "";
	    				List<ProjectType> statusList = projectTypeService.getData();
	    				if(StringUtils.isNotEmpty(statusList)) {
	    					for (int i = 0; i < statusList.size(); i++) {
		    					ProjectType statusObj = statusList.get(i);
		    					String dsname = statusObj.getDsname();
		    					if(StringUtils.isNotEmpty(dsname)) {
		    						statusMapByName.put(dsname, statusObj);
		    					}
		    					int iSDefault = statusObj.getISDefault();
		    					if(iSDefault==1) {
		    						defaultStatus = statusObj.getId();
		    					}
							}
	    					if(StringUtils.isEmpty(defaultStatus)) {
	    						defaultStatus = statusList.get(0).getId();
	    					}
	    				}
	    				
	    				// 6、方案
	    				Map<String, JoblistProgram> programMapByName_score = new HashMap<String, JoblistProgram>();
	    				Map<String, JoblistProgram> programMapByName_weight = new HashMap<String, JoblistProgram>();
	    				StandardJobLibQueryDto programDto = new StandardJobLibQueryDto();
	    				programDto.setOrgid(orgid);
	    				List<JoblistProgram> programList = jobLibConfigService.getJoblistProgramData(programDto);
	    				if(StringUtils.isNotEmpty(programList)) {
	    					for (int i = 0; i < programList.size(); i++) {
	    						JoblistProgram programObj = programList.get(i);
	    						String dataStr = programObj.getProgramName()==null?"":programObj.getProgramName().trim();
	    						if(StringUtils.isNotEmpty(dataStr)) {
	    							int calcType = programObj.getCalcType()==null?0:programObj.getCalcType();
	    							if(calcType==0) { //分值
	    								programMapByName_score.put(dataStr, programObj);
	    							}else if(calcType==1) { //权重
	    								programMapByName_weight.put(dataStr, programObj);
	    							}
	    						}
	    					}
	    				}
	    				
	    				// 7、核算对象中活动数据
	    				HashMap<String, Costuint> unitPidNameMap = new HashMap<String, Costuint>();
	    				HashMap<String, String> unitMaxSortMap = new HashMap<String, String>();
	    				MethodQueryDto unitDto = new MethodQueryDto();
	    				unitDto.setProductive(1);
	    				List<Costuint> unitList = unitMethodService.getCostuintList(unitDto);
	    				this.getUnitDataMap(unitList, unitPidNameMap, unitMaxSortMap);
	    				
	    				// 8、获取优先级
	    				String priorityid_default = "";
	    				Map<String, JoblistExtPropertiesConfig> priorityMap = new HashMap<String, JoblistExtPropertiesConfig>();
	    				StandardJobLibQueryDto priorityDto = new StandardJobLibQueryDto();
	    				priorityDto.setOrgid(orgid);
	    				priorityDto.setPtype(1);
	    				List<JoblistExtPropertiesConfig> priorityList = jobLibConfigService.getExtPropertiesListByInit(priorityDto);
	    				if(StringUtils.isNotEmpty(priorityList)) {
	    					priorityMap = priorityList.stream().collect(Collectors.toMap(JoblistExtPropertiesConfig::getPname, Function.identity(), (key1, key2) -> key2));
	    					if(StringUtils.isNotEmpty(priorityMap)&&priorityMap.containsKey("无")) {
	    						priorityid_default = priorityMap.get("无").getId();
	    					}
	    				}
	    				
	    				// 9、根据分类获取绑定作业
    					Map<String, List<JoblistPersonBind>> secialityMap = new HashMap<String, List<JoblistPersonBind>>();
	    				if(StringUtils.isNotEmpty(classIdList)) {
							StandardJobLibQueryDto dto = new StandardJobLibQueryDto();
							dto.setOrgid(orgid);
    	    				dto.setTypeid(typeid);
    	    				dto.setPidList(classIdList);
    	    				dto.setBindtype(3); //专业
		    				List<JoblistPersonBind> bindList = methodService.getJoblistPersonBindList(dto);
		    				if(StringUtils.isNotEmpty(bindList)) {
		    					secialityMap = bindList.stream().collect(Collectors.groupingBy(JoblistPersonBind::getPid));
		    				}
	    				}
	    				
	    				// 10、根据作业单元获取绑定岗位、工区
	    				List<JoblistPersonBind> bindPostList = new ArrayList<JoblistPersonBind>(); //岗位
	    				Map<String, JoblistPersonBind> bindPostNameMap = new HashMap<String, JoblistPersonBind>(); //岗位名称Map
	    				List<JoblistPersonBind> workAreaList = new ArrayList<JoblistPersonBind>(); //工区
	    				
	    				List<JoblistPersonBind> activityBindFormList = new ArrayList<JoblistPersonBind>(); //活动绑定表单
	    				Map<String, List<JoblistPersonBind>> activityBindFormMap = new HashMap<String, List<JoblistPersonBind>>(); //活动绑定表单Map
	    				List<JoblistPersonBind> activityBindPostList = new ArrayList<JoblistPersonBind>(); //活动绑定岗位
	    				Map<String, List<JoblistPersonBind>> activityBindPostMap = new HashMap<String, List<JoblistPersonBind>>(); //活动绑定岗位Map
	    				
	    				// 获取核算单元绑定的岗位、工区
						StandardJobLibQueryDto bindtypeDto = new StandardJobLibQueryDto();
						bindtypeDto.setPid(unitid);
	    				List<JoblistPersonBind> bindtypeList = methodService.getJoblistPersonBindList(bindtypeDto);
	    				Map<Integer, List<JoblistPersonBind>> bindtypeMap = new HashMap<Integer, List<JoblistPersonBind>>();
	    				if(StringUtils.isNotEmpty(bindtypeList)) {
	    					bindtypeMap = bindtypeList.stream().collect(Collectors.groupingBy(JoblistPersonBind::getBindtype));
	    				}
						if(StringUtils.isNotEmpty(bindtypeMap)) {
							if(bindtypeMap.containsKey(1)) { //岗位
								bindPostList = bindtypeMap.get(1);
    						}
    						if(bindtypeMap.containsKey(2)) { //工区
    							workAreaList = bindtypeMap.get(2);
    						}
						}
						//核算单元绑定岗位Map
						if(StringUtils.isNotEmpty(bindPostList)) {
							for (int i = 0; i < bindPostList.size(); i++) {
								JoblistPersonBind bindPostObj = bindPostList.get(i);
								String postName = bindPostObj.getBindname();
								if(StringUtils.isNotEmpty(postName)&&!bindPostNameMap.containsKey(postName.trim())) {
									bindPostNameMap.put(postName.trim(), bindPostObj);
								}
							}
						}
						
						//获取活动绑定的表单模板
						if(StringUtils.isNotEmpty(activityMap)) {
							List<String> activityIdList = new ArrayList<String>(activityMap.keySet());
							if(StringUtils.isNotEmpty(activityIdList)) {
								//活动绑定岗位
								StandardJobLibQueryDto postDto = new StandardJobLibQueryDto();
								postDto.setPidList(activityIdList);
								postDto.setBindtype(1);
			    				List<JoblistPersonBind> postList = methodService.getJoblistPersonBindList(postDto);
			    				if(StringUtils.isNotEmpty(postList)) {
			    					activityBindPostList = postList;
			    				}
								//活动绑定表单
								StandardJobLibQueryDto formDto = new StandardJobLibQueryDto();
								formDto.setPidList(activityIdList);
								formDto.setBindtype(4);
			    				List<JoblistPersonBind> formList = methodService.getJoblistPersonBindList(formDto);
			    				if(StringUtils.isNotEmpty(formList)) {
			    					activityBindFormList = formList;
			    				}
							}
						}
						//活动绑定岗位
						if(StringUtils.isNotEmpty(activityBindPostList)) {
							activityBindPostMap = activityBindPostList.stream().collect(Collectors.groupingBy(JoblistPersonBind::getPid));
						}
						//活动绑定表单
						if(StringUtils.isNotEmpty(activityBindFormList)) {
							activityBindFormMap = activityBindFormList.stream().collect(Collectors.groupingBy(JoblistPersonBind::getPid));
						}
						
						// 11、记录来源
						Map<String, SysDictData> recordTypeMapByName = new HashMap<String, SysDictData>();
						List<SysDictData> recordTypeList = dictTypeService.selectDictDataByType("joblist_activity_recordType");    
						if(StringUtils.isNotEmpty(recordTypeList)) {
							recordTypeMapByName = recordTypeList.stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity(), (key1, key2) -> key2));
	    				}
						
						// 12、功能项
//						Map<String, SysDictData> recordidMapByName = new HashMap<String, SysDictData>();
//						List<SysDictData> recordidList = dictTypeService.selectDictDataByType("joblist_activity_recordid");    
//						if(StringUtils.isNotEmpty(recordidList)) {
//							recordidMapByName = recordidList.stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity(), (key1, key2) -> key2));
//	    				}
						
						// 13、表单模板
						Map<String, SFForm> formMapByName = new HashMap<String, SFForm>();
						StandardJobLibQueryDto formDto = new StandardJobLibQueryDto();
						formDto.setOrgid(orgid);
						List<SFForm> formList = jobLibConfigService.getFormCombList(formDto);
						if(StringUtils.isNotEmpty(formList)) {
							if(StringUtils.isNotEmpty(formList)) {
								formMapByName = formList.stream().collect(Collectors.toMap(SFForm::getName, Function.identity(), (key1, key2) -> key2));
		    				}
						}
						
						// 14、责任对象
						Map<String, SysDictData> responsibilityTypeMapByName = new HashMap<String, SysDictData>();
						List<SysDictData> responsibilityTypeList = dictTypeService.selectDictDataByType("joblist_activity_responsibilityType");    
						if(StringUtils.isNotEmpty(responsibilityTypeList)) {
							responsibilityTypeMapByName = responsibilityTypeList.stream().collect(Collectors.toMap(SysDictData::getDictLabel, Function.identity(), (key1, key2) -> key2));
	    				}
						
						// 15、获取专业数据（生成分类数据用）
	    				StandardJobLibQueryDto secialityDto = new StandardJobLibQueryDto();
	    				secialityDto.setOrgid(orgid);
	    				secialityDto.setPtype(0);
	    				List<JoblistExtPropertiesConfig> secialityList = jobLibConfigService.getExtPropertiesListByInit(secialityDto);
	    				
						//是否为台账活动
						HashMap<String, Integer> formIsAccountMap = new HashMap<String, Integer>();
						
						//已存在活动名称（判断每个活动只能导入一次）
						List<String> hasUnitNameList = new ArrayList<String>();
						//遍历数据
	        			for (int j = 0; j < dataList.size(); j++) {
	        				LinkedHashMap<String, Object> dataMap = dataList.get(j);
	        				if(StringUtils.isNotEmpty(dataMap)) {
	        					JoblistActivityPropertiesVo excelData = null;
	        					//遍历列信息
	        					for (int i = 0; i < confList.size(); i++) {
	        						JoblistColmConfigVo confObj = confList.get(i);
	        						String colmCode = confObj.getColmCode();
	        						String colmName = confObj.getColmName();
	        						if(StringUtils.isNotEmpty(colmCode)&&StringUtils.isNotEmpty(colmName)) {
	        							if(dataMap.containsKey(colmName)) {
	        								if(excelData==null) {
	        									excelData = new JoblistActivityPropertiesVo();
	        								}
        									String value_excel = "";
        									Object value = dataMap.get(colmName);
        									if(value!=null && !"".equals(value.toString())) {
        										value_excel = value.toString().trim();
        									}
        									if("classid".equals(colmCode)) { //分类
        										if(StringUtils.isNotEmpty(value_excel)) {
        											if(classMapByName.containsKey(value_excel)) { //已存在分类，直接从数据中获取ID
            											String classid = classMapByName.get(value_excel).getId();
            											excelData.setClassid(classid);
            										}else { //不存在分类，生成一个新的分类
            											if(StringUtils.isEmpty(firstClassNameMap)||!firstClassNameMap.containsKey(value_excel)) {
            												//生成新分类
            												String newClassId = TMUID.getUID();
            												JoblistClass addClass = new JoblistClass();
            												addClass.setId(newClassId);
            												addClass.setTypeid(typeid);
            												addClass.setPid("root");
            												addClass.setOrgid(orgid);
            												addClass.setCname(value_excel);
            												addClass.setMemo(value_excel);
            												addClass.setTmused(1);
            												addClass.setTmsort(++maxClassPx);
            												addClassList.add(addClass);
            												//赋值分类id
                											excelData.setClassid(newClassId);
                											//新分类存入Map
                											JoblistClassVo addClassVo = new JoblistClassVo();
                											BeanUtils.copyProperties(addClass, addClassVo); // 赋予返回对象
                											addClassVo.setNodeId(addClass.getId());
                											addClassVo.setNodeName(addClass.getCname());
                											addClassVo.setNodeShowName(addClass.getCname());
                											classMapByName.put(value_excel, addClassVo);
                											//生成新分类绑定专业
                											if(StringUtils.isNotEmpty(secialityList)) {
                												List<JoblistPersonBind> list = new ArrayList<JoblistPersonBind>();
                												for (int k = 0; k < secialityList.size(); k++) {
                													JoblistExtPropertiesConfig secialityObj = secialityList.get(k);
                													//整理分类绑定的专业数据
                													JoblistPersonBind secialityBind = new JoblistPersonBind();
                													secialityBind.setId(TMUID.getUID());
                													secialityBind.setTypeid(typeid);
                													secialityBind.setPid(newClassId);
                													secialityBind.setOrgid(orgid);
                													secialityBind.setBindtype(3);
                													secialityBind.setBindid(secialityObj.getId());
                													secialityBind.setBindname(secialityObj.getPname());
                													secialityBind.setTmused(1);
                													addPersonBindList.add(secialityBind);
                													list.add(secialityBind);
																}
                												//存入Map
                												if(StringUtils.isNotEmpty(list)) {
                													secialityMap.put(newClassId, list);
                												}
                						    				}
            											}
            										}
        										}
    	        							}else if("activityType".equals(colmCode)) { //活动类型
    	        								if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(activityTypeMapByName)&&activityTypeMapByName.containsKey(value_excel)) {
        											String activityType = activityTypeMapByName.get(value_excel).getDictValue();
        											excelData.setActivityType(activityType);
        										}
    	        							}else if("unitName".equals(colmCode)) { //活动名称
    	        								excelData.setUnitName(value_excel);
    	        							}else if("frequencyid".equals(colmCode)) { //周期--频次
    	        								if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(cycleMapByName)&&cycleMapByName.containsKey(value_excel)) {
        											String frequencyid = cycleMapByName.get(value_excel).getId();
        											excelData.setFrequencyid(frequencyid);
        										}
    	        							}else if("standardDuration".equals(colmCode)) { //时长
    	        								if(StringUtils.isNotEmpty(value_excel)) {
    	        									double standardDuration = 0d;
    	        									try {
    	        										String value_str = new BigDecimal(Double.valueOf(value_excel)).setScale(this.POINTCOUNT, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    	        										standardDuration = Double.valueOf(value_str);
    	        									}catch(Exception e) {}
    	        									excelData.setStandardDuration(standardDuration);
    	        								}
    	        							}else if("responsibilityType".equals(colmCode)) { //责任对象
    	        								if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(responsibilityTypeMapByName)&&responsibilityTypeMapByName.containsKey(value_excel)) {
        											String responsibilityTypeStr = responsibilityTypeMapByName.get(value_excel).getDictValue();
        											int responsibilityType = 1;
        											try {
        												responsibilityType = Integer.valueOf(responsibilityTypeStr);
    	        									}catch(Exception e) {}
        											excelData.setResponsibilityType(responsibilityType);
        										}
    	        							}else if("priorityid".equals(colmCode)) { //优先级
    	        								if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(priorityMap)&&priorityMap.containsKey(value_excel)) {
        											String priorityid = priorityMap.get(value_excel).getId();
        											excelData.setPriorityid(priorityid);
        										}
    	        							}else if("statusid".equals(colmCode)) { //工况
    	        								if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(statusMapByName)&&statusMapByName.containsKey(value_excel)) {
        											String statusid = statusMapByName.get(value_excel).getId();
        											excelData.setStatusid(statusid);
        										}
    	        							}else if("calcSchemeid_score".equals(colmCode)) { //分值考核--选择方案
    	        								if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(programMapByName_score)&&programMapByName_score.containsKey(value_excel)) {
        											String calcSchemeid = programMapByName_score.get(value_excel).getId();
        											excelData.setCalcSchemeid(calcSchemeid);
        											excelData.setCalcType(0); //分值
        										}
    	        							}else if("calcSchemeid_weight".equals(colmCode)) { //权重考核--选择方案
    	        								//先按分值考核，再按权重考核
    	        								if(StringUtils.isEmpty(excelData.getCalcSchemeid())) {
    	        									if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(programMapByName_weight)&&programMapByName_weight.containsKey(value_excel)) {
            											String calcSchemeid = programMapByName_weight.get(value_excel).getId();
            											excelData.setCalcSchemeid(calcSchemeid);
            											excelData.setCalcType(1); //权重
            										}
    	        								}
    	        							}else if("positionArea".equals(colmCode)) { //负责区域
    	        								excelData.setPositionArea(value_excel);
    	        							}else if("involveDevice".equals(colmCode)) { //涉及设备
    	        								excelData.setInvolveDevice(value_excel);
    	        							}else if("jobRequirement".equals(colmCode)) { //工作要求
    	        								excelData.setJobRequirement(value_excel);
    	        							}else if("qualityRequirement".equals(colmCode)) { //质量要求
    	        								excelData.setQualityRequirement(value_excel);
    	        							}else if("basicScore".equals(colmCode)) { //基础分值
    	        								if(StringUtils.isNotEmpty(value_excel)) {
    	        									double basicScore = 0d;
    	        									try {
    	        										String value_str = new BigDecimal(Double.valueOf(value_excel)).setScale(this.POINTCOUNT, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    	        										basicScore = Double.valueOf(value_str);
    	        									}catch(Exception e) {}
    	        									excelData.setBasicScore(basicScore);
    	        								}
    	        							}else if("recordType".equals(colmCode)) { //记录来源
    	        								if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(recordTypeMapByName)&&recordTypeMapByName.containsKey(value_excel)) {
        											String recordType = recordTypeMapByName.get(value_excel).getDictValue();
        											excelData.setRecordType(recordType);
        										}
    	        							}
//    	        							else if("recordid".equals(colmCode)) { //功能项
//    	        								if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(recordidMapByName)&&recordidMapByName.containsKey(value_excel)) {
//        											String recordid = recordidMapByName.get(value_excel).getDictValue();
//        											excelData.setRecordid(recordid);
//        										}
//    	        							}
    	        							else if("bindFormId".equals(colmCode)) { //表单模板
    	        								if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(formMapByName)) {
    	        									String formIdStr = "";
    	        									String formNameStr = "";
    	        									String[] value_excel_arr = value_excel.split(",");
    	        									if(value_excel_arr.length>0) {
    	        										for (int k = 0; k < value_excel_arr.length; k++) {
															String value_excel_str = value_excel_arr[k];
															if(StringUtils.isNotEmpty(value_excel_str)&&formMapByName.containsKey(value_excel_str.trim())) {
																String formId = formMapByName.get(value_excel_str.trim()).getId();
																String formName = value_excel_str.trim();
																formIdStr += ","+formId;
																formNameStr += ","+formName;
															}
														}
    	        									}
    	        									if(StringUtils.isNotEmpty(formIdStr)) {
    	        										formIdStr = formIdStr.substring(1);
    	        										formNameStr = formNameStr.substring(1);
    	        									}
        											excelData.setBindFormIdStr(formIdStr);
        											excelData.setBindFormNameStr(formNameStr);
        										}
    	        							}else if("beginDate".equals(colmCode)) { //开始日期
    	        								if(StringUtils.isNotEmpty(value_excel)) {
    	        									excelData.setBeginDate(value_excel);
    	        								}
    	        							}else if("endDate".equals(colmCode)) { //结束日期
    	        								if(StringUtils.isNotEmpty(value_excel)) {
    	        									excelData.setEndDate(value_excel);
    	        								}
    	        							}else if("bindPostId".equals(colmCode)) { //绑定岗位
    	        								if(StringUtils.isNotEmpty(value_excel)&&StringUtils.isNotEmpty(bindPostNameMap)) {
    	        									String postIdStr = "";
    	        									String postNameStr = "";
    	        									String[] value_excel_arr = value_excel.split(",");
    	        									if(value_excel_arr.length>0) {
    	        										for (int k = 0; k < value_excel_arr.length; k++) {
															String value_excel_str = value_excel_arr[k];
															if(StringUtils.isNotEmpty(value_excel_str)&&bindPostNameMap.containsKey(value_excel_str.trim())) {
																String postId = bindPostNameMap.get(value_excel_str.trim()).getBindid();
																String postName = value_excel_str.trim();
																postIdStr += ","+postId;
																postNameStr += ","+postName;
															}
														}
    	        									}
    	        									if(StringUtils.isNotEmpty(postIdStr)) {
    	        										postIdStr = postIdStr.substring(1);
    	        										postNameStr = postNameStr.substring(1);
    	        									}
        											excelData.setBindPostIdStr(postIdStr);
        											excelData.setBindPostNameStr(postNameStr);
        										}
    	        							}
        								}
	        						}
								}
	        					if(excelData!=null) { //excel有数据
	        						String classid = excelData.getClassid();
	        						String unitName = excelData.getUnitName();
	        						String activityType = excelData.getActivityType();
	        						String frequencyid = excelData.getFrequencyid();
	        						Double standardDuration = excelData.getStandardDuration();
	        						Integer responsibilityType = excelData.getResponsibilityType(); //责任对象
	        						String priorityid = excelData.getPriorityid(); //优先级
	        						String statusid = excelData.getStatusid();
	        						String calcSchemeid = excelData.getCalcSchemeid();
	        						Integer calcType = excelData.getCalcType();
	        						String positionArea = excelData.getPositionArea();
	        						String involveDevice = excelData.getInvolveDevice();
	        						String jobRequirement = excelData.getJobRequirement();
	        						String qualityRequirement = excelData.getQualityRequirement();
	        						Double basicScore = excelData.getBasicScore();
	        						String recordType = excelData.getRecordType();
	        						String recordid = excelData.getRecordid();
	        						String bindFormIdStr = excelData.getBindFormIdStr();
	        						String bindFormNameStr = excelData.getBindFormNameStr();
	        						String beginDate = this.getDateStr(excelData.getBeginDate()); //开始日期
	        						String endDate = this.getDateStr(excelData.getEndDate()); //结束日期
	        						String bindPostIdStr = excelData.getBindPostIdStr(); //岗位ID
	        						String bindPostNameStr = excelData.getBindPostNameStr(); //岗位名称
	        						
	        						if(StringUtils.isEmpty(classid)&&StringUtils.isEmpty(unitName)&&StringUtils.isEmpty(activityType)
	        							&&StringUtils.isEmpty(frequencyid)&&standardDuration==null&&responsibilityType==null
	        							&&StringUtils.isEmpty(priorityid)&&StringUtils.isEmpty(statusid)&&StringUtils.isEmpty(calcSchemeid)
	        							&&calcType==null&&StringUtils.isEmpty(positionArea)&&StringUtils.isEmpty(involveDevice)
	        							&&StringUtils.isEmpty(jobRequirement)&&StringUtils.isEmpty(qualityRequirement)&&basicScore==null
	        							&&StringUtils.isEmpty(recordType)&&StringUtils.isEmpty(recordid)&&StringUtils.isEmpty(bindFormIdStr)
	        							&&StringUtils.isEmpty(bindFormNameStr)&&StringUtils.isEmpty(beginDate)&&StringUtils.isEmpty(endDate)
	        							&&StringUtils.isEmpty(bindPostIdStr)&&StringUtils.isEmpty(bindPostNameStr)) {
	        							break; //行记录为空，结束导入
	        						}
	        						
									if(StringUtils.isEmpty(classid)||StringUtils.isEmpty(unitName)) {
										errMess += "【第"+(j+1)+"行记录】未匹配到有效的";
										if(StringUtils.isEmpty(classid)) {
											errMess += "“"+classid_label+"”";
										}
										if(StringUtils.isEmpty(classid)&&StringUtils.isEmpty(unitName)) {
											errMess += "和";
										}
										if(StringUtils.isEmpty(unitName)) {
											errMess += "“"+unitName_label+"”";
										}
										errMess += "！</br>";
										continue;
									}
									//判断每个活动只能导入一次
									if(hasUnitNameList.contains(unitName)) {
										continue;
    								}else {
    									hasUnitNameList.add(unitName);
    								}
									if(unitName.length()>100) {
										errMess += "【第"+(j+1)+"行记录】“"+unitName_label+"”过长（超过100个字符）！</br>";
										continue;
									}
									if(StringUtils.isNotEmpty(positionArea) && positionArea.length()>100) {
										errMess += "【第"+(j+1)+"行记录】“"+positionArea_label+"”过长（超过100个字符）！</br>";
										continue;
									}
									if(StringUtils.isNotEmpty(involveDevice) && involveDevice.length()>100) {
										errMess += "【第"+(j+1)+"行记录】“"+involveDevice_label+"”过长（超过100个字符）！</br>";
										continue;
									}
									if(StringUtils.isNotEmpty(jobRequirement) && jobRequirement.length()>2000) {
										errMess += "【第"+(j+1)+"行记录】“"+jobRequirement_label+"”过长（超过2000个字符）！</br>";
										continue;
									}
									if(StringUtils.isNotEmpty(qualityRequirement) && qualityRequirement.length()>1000) {
										errMess += "【第"+(j+1)+"行记录】“"+qualityRequirement_label+"”过长（超过1000个字符）！</br>";
										continue;
									}
									if(StringUtils.isNotEmpty(beginDate) && "error".equals(beginDate)) {
										errMess += "【第"+(j+1)+"行记录】“"+beginDate_label+"”格式不正确（例：2000-01-01）！</br>";
										continue;
									}
									if(StringUtils.isNotEmpty(endDate) && "error".equals(endDate)) {
										errMess += "【第"+(j+1)+"行记录】“"+endDate_label+"”格式不正确（例：2000-01-01）！</br>";
										continue;
									}
									if(basicScore!=null&&(basicScore<-999999.99||basicScore>999999.99)) {
										errMess += "【第"+(j+1)+"行记录】“"+basicScore_label+"”超出范围（-999999.99~999999.99）！</br>";
										continue;
									}
									
									String activityId = "";
				    				String dataKey = classid + "_" + unitName;
	        						if(StringUtils.isNotEmpty(unitPidNameMap)&&unitPidNameMap.containsKey(dataKey)) { //修改
	        							Costuint costObj = unitPidNameMap.get(dataKey);
	        							activityId = costObj.getId();
	        							if(StringUtils.isNotEmpty(activityId)&&StringUtils.isNotEmpty(activityMap)&&activityMap.containsKey(activityId)) {
	        								JoblistActivityProperties activityObj = activityMap.get(activityId);
	        								boolean isSave = false;
	        								if(activityType!=null) {
	        									String activityType_o = activityObj.getActivityType();
	        									if(!activityType.equals(activityType_o)) {
	        										activityObj.setActivityType(activityType); //目前仅有常规工作、周期作业、突发应急，来源于数据字典
	        										isSave = true;
	        									}
	        								}
	        								if(frequencyid!=null) {
	        									String frequencyid_o = activityObj.getFrequencyid();
	        									if(!frequencyid.equals(frequencyid_o)) {
	        										activityObj.setFrequencyid(frequencyid); //周期--频次
	        										isSave = true;
	        									}
	        								}
	        								if(standardDuration!=null) {
	        									double standardDuration_o = activityObj.getStandardDuration()==null?0d:activityObj.getStandardDuration();
	        									if(standardDuration!=standardDuration_o) {
	        										activityObj.setStandardDuration(standardDuration); //时长
	        										isSave = true;
	        									}
	        								}
	        								if(responsibilityType!=null) {
	        									int responsibilityType_o = activityObj.getResponsibilityType()==null?1:activityObj.getResponsibilityType();
	        									if(responsibilityType!=responsibilityType_o) {
	        										activityObj.setResponsibilityType(responsibilityType); //责任对象
	        										isSave = true;
	        									}
	        								}
	        								if(priorityid!=null) {
	        									String priorityid_o = activityObj.getPriorityid();
	        									if(!priorityid.equals(priorityid_o)) {
	        										activityObj.setPriorityid(priorityid); //优先级
	        										isSave = true;
	        									}
	        								}
	        								if(statusid!=null) {
	        									String statusid_o = activityObj.getStatusid();
	        									if(!statusid.equals(statusid_o)) {
	        										activityObj.setStatusid(statusid); //工况
	        										isSave = true;
	        									}
	        								}
	        								if(calcSchemeid!=null) {
	        									String calcSchemeid_o = activityObj.getCalcSchemeid();
	        									if(!calcSchemeid.equals(calcSchemeid_o)) {
	        										activityObj.setCalcSchemeid(calcSchemeid); //方案
	        										activityObj.setCalcType(calcType);
	        										isSave = true;
	        									}
	        								}
	        								if(positionArea!=null) {
	        									String positionArea_o = activityObj.getPositionArea()==null?"":activityObj.getPositionArea().trim();
	        									if(!positionArea.equals(positionArea_o)) {
	        										activityObj.setPositionArea(positionArea); //负责区域
	        										isSave = true;
	        									}
	        								}
	        								if(involveDevice!=null) {
	        									String involveDevice_o = activityObj.getInvolveDevice()==null?"":activityObj.getInvolveDevice().trim();
	        									if(!involveDevice.equals(involveDevice_o)) {
	        										activityObj.setInvolveDevice(involveDevice); //涉及设备
	        										isSave = true;
	        									}
	        								}
	        								if(jobRequirement!=null) {
	        									String jobRequirement_o = activityObj.getJobRequirement()==null?"":activityObj.getJobRequirement().trim();
	        									if(!jobRequirement.equals(jobRequirement_o)) {
	        										activityObj.setJobRequirement(jobRequirement); //工作要求
	        										isSave = true;
	        									}
	        								}
	        								if(qualityRequirement!=null) {
	        									String qualityRequirement_o = activityObj.getQualityRequirement()==null?"":activityObj.getQualityRequirement().trim();
	        									if(!qualityRequirement.equals(qualityRequirement_o)) {
	        										activityObj.setQualityRequirement(qualityRequirement); //质量要求
	        										isSave = true;
	        									}
	        								}
	        								if(basicScore!=null) {
	        									double basicScore_o = activityObj.getBasicScore()==null?0d:activityObj.getBasicScore();
	        									if(basicScore_o!=basicScore) {
	        										activityObj.setBasicScore(basicScore); //基础分值
	        										isSave = true;
	        									}
	        								}
	        								if(recordType!=null) {
	        									String recordType_o = activityObj.getRecordType()==null?"":activityObj.getRecordType();
	        									if(!recordType.equals(recordType_o)) {
	        										activityObj.setRecordType(recordType); //记录来源
	        										isSave = true;
	        									}
	        								}
	        								if(recordid!=null) {
	        									String recordid_o = activityObj.getRecordid()==null?"":activityObj.getRecordid();
	        									if(!recordid.equals(recordid_o)) {
	        										activityObj.setRecordid(recordid); //功能项
	        										isSave = true;
	        									}
	        								}
	        								if(beginDate!=null) {
	        									String beginDate_o = activityObj.getBeginDate()==null?"":activityObj.getBeginDate();
	        									if(!beginDate.equals(beginDate_o)) {
	        										activityObj.setBeginDate(beginDate); //开始日期
	        										isSave = true;
	        									}
	        								}
	        								if(endDate!=null) {
	        									String endDate_o = activityObj.getEndDate()==null?"":activityObj.getEndDate();
	        									if(!endDate.equals(endDate_o)) {
	        										activityObj.setEndDate(endDate); //结束日期
	        										isSave = true;
	        									}
	        									int noEndDate = activityObj.getNoEndDate()==null?0:activityObj.getNoEndDate(); //0、活动有结束期限；1、活动永久有效；默认是1，此时不能修改结束日期
		        								if(noEndDate==1) {
		        									activityObj.setNoEndDate(0);
		        									isSave = true;
		        								}
	        								}else {
	        									int noEndDate = activityObj.getNoEndDate()==null?0:activityObj.getNoEndDate(); //0、活动有结束期限；1、活动永久有效；默认是1，此时不能修改结束日期
		        								if(noEndDate==0) {
		        									activityObj.setNoEndDate(1);
		        									isSave = true;
		        								}
	        								}
	        								
	        								//校验结束日期与开始日期大小
	        								int noEndDate = activityObj.getNoEndDate()==null?0:activityObj.getNoEndDate(); //0、活动有结束期限；1、活动永久有效；默认是1，此时不能修改结束日期
	        								if(noEndDate==0) {
	        									String beginDate_o = activityObj.getBeginDate();
		        								String endDate_o = activityObj.getEndDate();
		        								if(StringUtils.isNotEmpty(beginDate_o)&&StringUtils.isNotEmpty(endDate_o)&&beginDate_o.compareTo(endDate_o)>0) {
		        									errMess += "【第"+(j+1)+"行记录】“"+beginDate_label+"（"+beginDate_o+"）”不能大于“"+endDate_label+"（"+endDate_o+"）”！</br>";
		    										continue;
		    									}
	        								}
	        								
	        								//整理岗位绑定数据
	        								if(StringUtils.isNotEmpty(bindPostIdStr)&&StringUtils.isNotEmpty(bindPostNameStr)) { //修改时判断有数据才传入（没有数据，不能删除原来的记录）
	        									jobLibConfigService.setBindData(addBindList, delBindList, "upd", activityBindPostMap, activityId, orgid, 1, bindPostIdStr, bindPostNameStr);
			    								bindPostIdMap.put(activityId, bindPostIdStr); //绑定岗位对应的机构同步到核算对象的操作机构中
	        								}
	        								
	        								//整理表单模板数据
	        								if(StringUtils.isNotEmpty(bindFormIdStr)&&StringUtils.isNotEmpty(bindFormNameStr)) { //修改时判断有数据才传入（没有数据，不能删除原来的记录）
	        									jobLibConfigService.setBindData(addBindList, delBindList, "upd", activityBindFormMap, activityId, orgid, 4, bindFormIdStr, bindFormNameStr);
	        									//根据绑定的表单判断是否为台账活动
	        									String[] bindFormIdArr = bindFormIdStr.split(",");
	        									List<String> bindFormIdList = Arrays.asList(bindFormIdArr);
	        									StandardJobLibQueryDto dto = new StandardJobLibQueryDto();
	        									dto.setIdList(bindFormIdList);
	        									int isAccountActivity = jobLibConfigService.getIsAccountActivity(dto, formIsAccountMap);
	        									int isAccountActivity_o = activityObj.getIsAccountActivity()==null?0:activityObj.getIsAccountActivity();
	        									if(isAccountActivity!=isAccountActivity_o) {
	        										activityObj.setIsAccountActivity(isAccountActivity);
	        										isSave = true;
	        									}
	        								}
	        								
	        								if(isSave) {
	        									updList.add(activityObj);
	        								}
	        								
	        								if(isSave || StringUtils.isNotEmpty(addBindList) || StringUtils.isNotEmpty(delBindList)) {
	        									dataCount += 1;
	        								}
		        							
	        							}
	        						}else { //新增
	        							activityId = TMUID.getUID();
	        							//核算对象中新增活动
	        							String unitPx = "";
	        							if(StringUtils.isNotEmpty(unitMaxSortMap)&&unitMaxSortMap.containsKey(classid)) {
	        								unitPx = unitMaxSortMap.get(classid);
	        							}
	        							unitPx = costService.getNewSort(unitPx, 1);
	        							unitMaxSortMap.put(classid, unitPx);
	        							
	        							Costuint unitObj = new Costuint();
	        							unitObj.setId(activityId);
	        							unitObj.setName(unitName);
	        							unitObj.setPid(classid);
	        							unitObj.setProductive(1); //活动
	        							unitObj.setTmused(1);
	        							unitObj.setTmSort(unitPx);
	        							addUnitList.add(unitObj);
	        							
	        							//作业活动数据
	    								int maxPx = 0;
	    								if(StringUtils.isNotEmpty(activityByClassMap)&&activityByClassMap.containsKey(classid)) {
	    									List<JoblistActivityProperties> activityByClassList = activityByClassMap.get(classid);
	    									if(StringUtils.isNotEmpty(activityByClassList)) {
	    										Integer tmsort = activityByClassList.get(activityByClassList.size()-1).getTmsort();
	    										if(tmsort!=null) {
	    											maxPx = tmsort;
	    										}
	    									}
	    								}
	    								maxPx += 1;
	    								
	    								JoblistActivityProperties dataObj = new JoblistActivityProperties();
	    								dataObj.setId(activityId);
	    								dataObj.setUnitid(unitid);
	    								dataObj.setOrgid(orgid);
	    								dataObj.setActivityType(activityType); //活动类型：目前仅有常规工作、周期作业、突发应急，来源于数据字典
	    								dataObj.setClassid(classid);
	    								dataObj.setStatusid(statusid); //工况ID
	    								dataObj.setPriorityid(StringUtils.isEmpty(priorityid)?priorityid_default:priorityid); //优先级ID
	    								dataObj.setResponsibilityType(responsibilityType==null?1:responsibilityType); //责任对象类型：0、班组；1、岗位；默认岗位；
	    								dataObj.setAvailableCount(0); //领取次数：默认是0
	    								dataObj.setFrequencyid(frequencyid); //频次ID
	    								dataObj.setIsFixTime(1); //是否固定时间：0、不是固定时间；1、是固定时间；默认是时间；
	    								dataObj.setFixTime(null); //固定时间点：文本形式的时间，格式是hh:mm:ss
	    								dataObj.setRelativeType(1); //相对时间类型：0、上班之前； 1、上班之后；2、下班之前；3、下班之后；默认是1
	    								dataObj.setRelativeTime(0); //相对时间：单位是分钟，默认是0
	    								dataObj.setPositionArea(positionArea); //负责区域
	    								dataObj.setInvolveDevice(involveDevice); //涉及设备
	    								dataObj.setJobRequirement(jobRequirement); //工作要求
	    								dataObj.setQualityRequirement(qualityRequirement); //质量要求
	    								dataObj.setConfirmType(0); //确认方式：0、自动确认；1、手工确认；默认是自动确认
	    								dataObj.setCalcType(StringUtils.isEmpty(calcSchemeid)?0:calcType); //分值计算方式：0、给定分值；1、给定权重；默认是给定分值
	    								dataObj.setCalcSchemeid(calcSchemeid); //分解方案ID
	    								dataObj.setBasicScore(basicScore); //基本分：先给定2位小数，给定分值时不可填写
	    								dataObj.setStandardDuration(standardDuration); //标准时长：2位小数
	    								dataObj.setTimeUnit(1); //标准时长单位：0、秒；1、分；2、小时；3、天；
	    								dataObj.setRecordType(StringUtils.isEmpty(recordType)?"0":recordType); //记录来源：0、无；
	    								dataObj.setRecordid("2"); //默认表单 //dataObj.setRecordid(recordid); //绑定的记录ID：从数据字典读取来源
	    								dataObj.setStartingConditionid(null); //开始条件ID
	    								dataObj.setEndConditionid(null); //结束条件ID
	    								dataObj.setPreplanid(null); //预案ID
	    								dataObj.setRegulationid(null); //操作规程ID
	    								dataObj.setTmused(1);
	    								dataObj.setTmsort(maxPx);
	    								dataObj.setBeginDate(StringUtils.isEmpty(beginDate)?currDate:beginDate);
	    								dataObj.setEndDate(StringUtils.isEmpty(endDate)?null:endDate);
	    								dataObj.setNoEndDate(StringUtils.isEmpty(endDate)?1:0); //无限期：0、活动有结束期限；1、活动永久有效；默认是1，此时不能修改结束日期
	    								dataObj.setExecType(1); //操作卡执行方式：1、顺序执行； 2、同步执行；
	    								
        								//校验结束日期与开始日期大小
        								if(dataObj.getNoEndDate()==0) { //0、活动有结束期限；1、活动永久有效；默认是1，此时不能修改结束日期
        									String beginDate_o = dataObj.getBeginDate();
	        								String endDate_o = dataObj.getEndDate();
	        								if(StringUtils.isNotEmpty(beginDate_o)&&StringUtils.isNotEmpty(endDate_o)&&beginDate_o.compareTo(endDate_o)>0) {
	        									errMess += "【第"+(j+1)+"行记录】“"+beginDate_label+"（"+(StringUtils.isEmpty(beginDate)?"默认":"")+beginDate_o+"）”不能大于“"+endDate_label+"（"+endDate_o+"）”！</br>";
	    										continue;
	    									}
        								}
        								
	    								addList.add(dataObj);
	    								
	    								//整理绑定岗位数据
//	    								String bindPostIdStr = "";
//	    								String bindPostNameStr = "";
//	    								if(StringUtils.isNotEmpty(bindPostList)) {
//	    									for (int m = 0; m < bindPostList.size(); m++) {
//	    										JoblistPersonBind bindObj = bindPostList.get(m);
//	    										String id = bindObj.getBindid();
//	    										String name =bindObj.getBindname();
//	    										if(StringUtils.isNotEmpty(id) && StringUtils.isNotEmpty(name)) {
//	    											bindPostIdStr += "," + id;
//	    											bindPostNameStr += "," + name;
//	    										}
//	    									}
//	    									if(StringUtils.isNotEmpty(bindPostIdStr)) {
//	    										bindPostIdStr = bindPostIdStr.substring(1);
//	    										bindPostNameStr = bindPostNameStr.substring(1);
//	    									}
//	    								}
	    								
	    								jobLibConfigService.setBindData(addBindList, delBindList, "add", null, activityId, orgid, 1, bindPostIdStr, bindPostNameStr);
	    								bindPostIdMap.put(activityId, bindPostIdStr); //绑定岗位对应的机构同步到核算对象的操作机构中
										
	    								//整理绑定工区数据
	    								String workAreaIdStr = "";
	    								String workAreaNameStr = "";
	    								if(StringUtils.isNotEmpty(workAreaList)) {
	    									for (int m = 0; m < workAreaList.size(); m++) {
	    										JoblistPersonBind bindObj = workAreaList.get(m);
	    										String id = bindObj.getBindid();
	    										String name =bindObj.getBindname();
	    										if(StringUtils.isNotEmpty(id) && StringUtils.isNotEmpty(name)) {
	    											workAreaIdStr += "," + id;
	    											workAreaNameStr += "," + name;
	    										}
	    									}
	    									if(StringUtils.isNotEmpty(workAreaIdStr)) {
	    										workAreaIdStr = workAreaIdStr.substring(1);
	    										workAreaNameStr = workAreaNameStr.substring(1);
	    									}
	    								}
	    								jobLibConfigService.setBindData(addBindList, delBindList, "add", null, activityId, orgid, 2, workAreaIdStr, workAreaNameStr);

	    								//整理绑定专业数据
	    								String secialityIdStr = "";
	    								String secialityNameStr = "";
	    								if(StringUtils.isNotEmpty(secialityMap)&&secialityMap.containsKey(classid)) {
	    									List<JoblistPersonBind> bindList = secialityMap.get(classid);
	    									if(StringUtils.isNotEmpty(bindList)) {
		    									for (int m = 0; m < bindList.size(); m++) {
		    										JoblistPersonBind bindObj = bindList.get(m);
		    										String secialityId = bindObj.getBindid();
		    										String secialityName =bindObj.getBindname();
		    										if(StringUtils.isNotEmpty(secialityId) && StringUtils.isNotEmpty(secialityName)) {
		    											secialityIdStr += "," + secialityId;
		    											secialityNameStr += "," + secialityName;
		    										}
		    									}
		    									if(StringUtils.isNotEmpty(secialityIdStr)) {
		    										secialityIdStr = secialityIdStr.substring(1);
		    										secialityNameStr = secialityNameStr.substring(1);
		    									}
		    								}
	    								}
	    								jobLibConfigService.setBindData(addBindList, delBindList, "add", null, activityId, orgid, 3, secialityIdStr, secialityNameStr);
	    								
	    								//整理表单模板数据
	        							jobLibConfigService.setBindData(addBindList, delBindList, "add", null, activityId, orgid, 4, bindFormIdStr, bindFormNameStr);
	        							//根据绑定的表单判断是否为台账活动
	        							if(StringUtils.isNotEmpty(bindFormIdStr)) {
	        								String[] bindFormIdArr = bindFormIdStr.split(",");
	    									List<String> bindFormIdList = Arrays.asList(bindFormIdArr);
	    									StandardJobLibQueryDto dto = new StandardJobLibQueryDto();
	    									dto.setIdList(bindFormIdList);
	    									int isAccountActivity = jobLibConfigService.getIsAccountActivity(dto, formIsAccountMap);
	    									int isAccountActivity_o = dataObj.getIsAccountActivity()==null?0:dataObj.getIsAccountActivity();
	    									if(isAccountActivity!=isAccountActivity_o) {
	    										dataObj.setIsAccountActivity(isAccountActivity);
	    									}
	        							}
    									
	    								//将新增数据存入Map中，获取排序用
	    								if(StringUtils.isNotEmpty(activityByClassMap)&&activityByClassMap.containsKey(classid)) {
	    									List<JoblistActivityProperties> activityByClassList = activityByClassMap.get(classid);
	    									activityByClassList.add(dataObj);
	    								}else {
	    									List<JoblistActivityProperties> activityByClassList = new ArrayList<JoblistActivityProperties>();
	    									activityByClassList.add(dataObj);
	    									activityByClassMap.put(classid, activityByClassList);
	    								}
	    								
	    								dataCount += 1;
	        							
	        						}
	        					}
	        				}
						}
	        		}
		        }
			}catch(Exception e) {
				e.printStackTrace();
			}
		}
		
		if(StringUtils.isNotEmpty(errMess)) {
			result = errMess;
		}else {
			if(StringUtils.isNotEmpty(addList)||StringUtils.isNotEmpty(updList)||dataCount>0) {
				if(StringUtils.isEmpty(result)&&StringUtils.isNotEmpty(addUnitList)) {
					result = unitMethodService.saveCostuintData(addUnitList, null, null);
				}
				if (StringUtils.isEmpty(result)) {
		            result = methodService.saveDataJoblistClass(addClassList, null, null);
		            if (StringUtils.isEmpty(result)) {
		                result = methodService.saveDataJoblistPersonBind(addPersonBindList, null, null);
		            }
		        }
				if(StringUtils.isEmpty(result)) {
					result = methodService.saveDataJoblistActivityProperties(addList, updList, null);
					if(StringUtils.isEmpty(result)) {
						result = String.valueOf(dataCount);
						methodService.saveDataJoblistPersonBind(addBindList, null, delBindList);
					}
					if(StringUtils.isEmpty(result)) {
						jobLibConfigService.asyCostOrgByActivityBindPost(bindPostIdMap, orgid);
					}
				}
			}else {
				result = "0";
			}
		}
		return result;
	}
	
	//处理日期数据格式
	private String getDateStr(String dateStr) {
		if(StringUtils.isNotEmpty(dateStr)) {
			dateStr = dateStr.replaceAll("年", "-").replaceAll("月", "-").replaceAll("日", "").replaceAll("/", "-");
			String[] dateStrArr = dateStr.split("-");
			if(dateStrArr!=null&&dateStrArr.length==3) {
				try {
					int year = Integer.valueOf(dateStrArr[0]);
					int month = Integer.valueOf(dateStrArr[1]);
					int day = Integer.valueOf(dateStrArr[2]);
					if(month>12) {
						month = 12;
					}
					String yf = year + "-";
					if(month<10) {
						yf += "0";
					}
					yf += month;
					if(yf.length()!=7) {
						dateStr = "error";
					}else {
						String rq = yf + "-";
						if(day<10) {
							rq += "0";
						}
						rq += day;
						if(rq.length()!=10) {
							dateStr = "error";
						}else {
							String monthEndStr = DateTimeUtils.getMonthEnd(yf);
							if(rq.compareTo(monthEndStr)>0) {
								dateStr = monthEndStr;
							}else {
								dateStr = rq;
							}
						}
					}
				}catch(Exception e) {
					dateStr = "error";
				}
			}
		}
		return dateStr;
	}
	
	//获取Excel列信息
	private List<JoblistColmConfigVo> getJoblistColmConfigList() {
		List<JoblistColmConfigVo> result = new ArrayList<JoblistColmConfigVo>();
		result.add(new JoblistColmConfigVo("classid", "分类", 150, "left"));
		result.add(new JoblistColmConfigVo("activityType", "活动类型", 100, "left"));
		result.add(new JoblistColmConfigVo("unitName", "活动名称", 200, "left"));
		result.add(new JoblistColmConfigVo("frequencyid", "周期/频次", 100, "left")); //频次
		result.add(new JoblistColmConfigVo("standardDuration", "时长(分钟)", 100, "left"));
		result.add(new JoblistColmConfigVo("responsibilityType", "责任对象", 100, "left"));
		result.add(new JoblistColmConfigVo("bindPostId", "岗位", 160, "left"));
		result.add(new JoblistColmConfigVo("priorityid", "优先级", 100, "left"));
		result.add(new JoblistColmConfigVo("statusid", "工况", 100, "left"));
		result.add(new JoblistColmConfigVo("positionArea", "负责区域", 100, "left"));
		result.add(new JoblistColmConfigVo("involveDevice", "涉及设备", 100, "left"));
		result.add(new JoblistColmConfigVo("jobRequirement", "工作要求", 200, "left"));
		result.add(new JoblistColmConfigVo("qualityRequirement", "质量要求", 200, "left"));
		result.add(new JoblistColmConfigVo("beginDate", "开始日期", 100, "left"));
		result.add(new JoblistColmConfigVo("endDate", "结束日期", 100, "left"));
		result.add(new JoblistColmConfigVo("calcSchemeid_score", "分值考核", 150, "left")); //选择方案
		result.add(new JoblistColmConfigVo("calcSchemeid_weight", "权重考核", 150, "left")); //选择方案
		result.add(new JoblistColmConfigVo("basicScore", "基础分值", 100, "right"));
		result.add(new JoblistColmConfigVo("recordType", "记录来源", 150, "left"));
//		result.add(new JoblistColmConfigVo("recordid", "功能项", 150, "left"));
		result.add(new JoblistColmConfigVo("bindFormId", "表单模板", 150, "left"));
		return result;
	}
	
	//获取活动名称Map
	private void getUnitDataMap(List<Costuint> unitList, HashMap<String, Costuint> unitPidNameMap, 
		HashMap<String, String> unitMaxSortMap) {
		if(StringUtils.isNotEmpty(unitList)) {
			for (int i = 0; i < unitList.size(); i++) {
				Costuint unitObj = unitList.get(i);
				String pid = unitObj.getPid()==null?"":unitObj.getPid();
				String name = unitObj.getName();
				String tmSort = unitObj.getTmSort();
				String key = pid+"_"+name;
				if(unitPidNameMap!=null) {
					if(!unitPidNameMap.containsKey(key)) {
						unitPidNameMap.put(key, unitObj);
					}
				}
				if(unitMaxSortMap!=null) {
					unitMaxSortMap.put(pid, tmSort);
				}
			}			
		}
	}
	
	//——————————————————————————  导入 ↑  —————————————————————————————————
	
	
	
	
	
	//——————————————————————————  导出模板 ↓  —————————————————————————————————
	
	/**
	 *	作业活动导出Excel模板
	 * @param queryDto
	 * @param response
	 */
	@Override
	public void exportExcelTemplateJoblistActivity(StandardJobLibQueryDto queryDto, HttpServletResponse response) {
	    String fileName = queryDto.getExcelFileName()==null?"作业活动导入模板":queryDto.getExcelFileName();
	    String orgid = queryDto.getOrgid();
	    String typeid = queryDto.getTypeid();
	    
		List<ExcelExportEntity> excelTitleList = new ArrayList<ExcelExportEntity>(); //excel标题
		List<JoblistColmConfigVo> titleList = this.getJoblistColmConfigList(); //获取表头数据
		if(StringUtils.isNotEmpty(titleList)) {
			for (int i = 0; i < titleList.size(); i++) {
				JoblistColmConfigVo bean = titleList.get(i);
				ExcelExportEntity expEntity = getExpEntity(i, bean);
				excelTitleList.add(expEntity);
			}
		}
		ExportParams exportParams = new ExportParams(null, null, fileName);
		exportParams.setAddIndex(false); //是否生成序号
		Workbook workbook = ExcelExport.getWorkbook(exportParams, excelTitleList, null, null);
		Sheet sheet = workbook.getSheetAt(0);
		
		if(StringUtils.isNotEmpty(titleList)) {
			
			// 1、分类
			List<String> class_list = new ArrayList<String>();
			StandardJobLibQueryDto classDto = new StandardJobLibQueryDto();
			classDto.setOrgid(orgid);
			classDto.setTypeid(typeid);
			List<JoblistClassVo> classList = jobLibConfigService.getJoblistClassCombList(classDto, "root");
			if(StringUtils.isNotEmpty(classList)) {
				for (int i = 0; i < classList.size(); i++) {
					String dataStr = classList.get(i).getNodeShowName();
					if(StringUtils.isNotEmpty(dataStr) && !class_list.contains(dataStr)) {
						class_list.add(dataStr);
					}
				}
			}
			
			// 2、活动类型
			List<String> activityType_list = new ArrayList<String>();
			List<SysDictData> activityTypeList = dictTypeService.selectDictDataByType("joblist_activity_activityType");    
			if(StringUtils.isNotEmpty(activityTypeList)) {
				for (int i = 0; i < activityTypeList.size(); i++) {
					String dataStr = activityTypeList.get(i).getDictLabel();
					if(StringUtils.isNotEmpty(dataStr) && !activityType_list.contains(dataStr)) {
						activityType_list.add(dataStr);
					}
				}
			}
			
			// 3、频次
			List<String> cycle_list = new ArrayList<String>();
			if(StringUtils.isNotEmpty(orgid)) {
				StandardJobLibQueryDto cycleDto = new StandardJobLibQueryDto();
				cycleDto.setOrgid(orgid);
				cycleDto.setQueryType("orgSys");
				List<CycleScheme> cycleList = methodService.getCycleSchemeList(cycleDto);
				if(StringUtils.isNotEmpty(cycleList)) {
					for (int i = 0; i < cycleList.size(); i++) {
						String dataStr = cycleList.get(i).getSname();
						if(StringUtils.isNotEmpty(dataStr) && !cycle_list.contains(dataStr)) {
							cycle_list.add(dataStr);
						}
					}
				}
			}
			cycle_list.add("不定期");
			
			// 4、工况
			List<String> status_list = new ArrayList<String>();
			List<ProjectType> statusList = projectTypeService.getData();
			if(StringUtils.isNotEmpty(statusList)) {
				for (int i = 0; i < statusList.size(); i++) {
					String dataStr = statusList.get(i).getDsname();
					if(StringUtils.isNotEmpty(dataStr) && !status_list.contains(dataStr)) {
						status_list.add(dataStr);
					}
				}
			}
			
			// 5、方案
			List<String> program_score_list = new ArrayList<String>();
			List<String> program_weight_list = new ArrayList<String>();
			StandardJobLibQueryDto programDto = new StandardJobLibQueryDto();
			programDto.setOrgid(orgid);
			List<JoblistProgram> programList = jobLibConfigService.getJoblistProgramData(programDto);
			if(StringUtils.isNotEmpty(programList)) {
				for (int i = 0; i < programList.size(); i++) {
					JoblistProgram programObj = programList.get(i);
					String dataStr = programObj.getProgramName()==null?"":programObj.getProgramName().trim();
					if(StringUtils.isNotEmpty(dataStr)) {
						int calcType = programObj.getCalcType()==null?0:programObj.getCalcType();
						if(calcType==0) { //分值
							program_score_list.add(dataStr);
						}else if(calcType==1) { //权重
							program_weight_list.add(dataStr);
						}
					}
				}
			}
			
			// 6、记录来源
			List<String> recordType_list = new ArrayList<String>();
			List<SysDictData> recordTypeList = dictTypeService.selectDictDataByType("joblist_activity_recordType");    
			if(StringUtils.isNotEmpty(recordTypeList)) {
				for (int i = 0; i < recordTypeList.size(); i++) {
					String dataStr = recordTypeList.get(i).getDictLabel();
					if(StringUtils.isNotEmpty(dataStr) && !recordType_list.contains(dataStr)) {
						recordType_list.add(dataStr);
					}
				}
			}
			
			// 7、功能项
			List<String> recordid_list = new ArrayList<String>();
			List<SysDictData> recordidList = dictTypeService.selectDictDataByType("joblist_activity_recordid");    
			if(StringUtils.isNotEmpty(recordidList)) {
				for (int i = 0; i < recordidList.size(); i++) {
					String dataStr = recordidList.get(i).getDictLabel();
					if(StringUtils.isNotEmpty(dataStr) && !recordid_list.contains(dataStr)) {
						recordid_list.add(dataStr);
					}
				}
			}
			
			// 8、表单模板
			List<String> form_list = new ArrayList<String>();
			StandardJobLibQueryDto formDto = new StandardJobLibQueryDto();
			formDto.setOrgid(orgid);
			List<SFForm> formList = jobLibConfigService.getFormCombList(formDto);
			if(StringUtils.isNotEmpty(formList)) {
				for (int i = 0; i < formList.size(); i++) {
					String dataStr = formList.get(i).getName();
					if(StringUtils.isNotEmpty(dataStr) && !form_list.contains(dataStr)) {
						form_list.add(dataStr);
					}
				}
			}
			
			// 9、责任对象
			List<String> responsibilityType_list = new ArrayList<String>();
			List<SysDictData> responsibilityTypeList = dictTypeService.selectDictDataByType("joblist_activity_responsibilityType");    
			if(StringUtils.isNotEmpty(responsibilityTypeList)) {
				for (int i = 0; i < responsibilityTypeList.size(); i++) {
					String dataStr = responsibilityTypeList.get(i).getDictLabel();
					if(StringUtils.isNotEmpty(dataStr) && !responsibilityType_list.contains(dataStr)) {
						responsibilityType_list.add(dataStr);
					}
				}
			}
			
			// 10、获取优先级
			List<String> priorityid_list = new ArrayList<String>();
			StandardJobLibQueryDto priorityDto = new StandardJobLibQueryDto();
			priorityDto.setOrgid(orgid);
			priorityDto.setPtype(1);
			List<JoblistExtPropertiesConfig> priorityList = jobLibConfigService.getExtPropertiesListByInit(priorityDto);
			if(StringUtils.isNotEmpty(priorityList)) {
				for (int i = 0; i < priorityList.size(); i++) {
					String dataStr = priorityList.get(i).getPname();
					if(StringUtils.isNotEmpty(dataStr) && !priorityid_list.contains(dataStr)) {
						priorityid_list.add(dataStr);
					}
				}
			}
			
			//样式
			Map<String,HSSFCellStyle> styleMap = this.styleMapInit((HSSFWorkbook)workbook);
			HSSFCellStyle styleBody_L = styleMap.get("styleBody_L");
			HSSFCellStyle styleBody_R = styleMap.get("styleBody_R");
			
			int firstRow = 1;
			int lastRow = 999;
			
			for (int i = 0; i < titleList.size(); i++) {
				JoblistColmConfigVo bean = titleList.get(i);
				String colmCode = bean.getColmCode();
				if(StringUtils.isNotEmpty(colmCode)) {
					//设置下拉框
					List<String> selectList = new ArrayList<String>();
					if("classid".equals(colmCode)) { //分类
						if (StringUtils.isNotEmpty(class_list)) {
							selectList = class_list;
		                }
					}else if("activityType".equals(colmCode)) { //活动类型
						if (StringUtils.isNotEmpty(activityType_list)) {
							selectList = activityType_list;
		                }
					}else if("frequencyid".equals(colmCode)) { //周期--频次
						if (StringUtils.isNotEmpty(cycle_list)) {
							selectList = cycle_list;
		                }
					}else if("statusid".equals(colmCode)) { //工况
						if (StringUtils.isNotEmpty(status_list)) {
							selectList = status_list;
		                }
					}else if("calcSchemeid_score".equals(colmCode)) { //分值考核--选择方案
						if (StringUtils.isNotEmpty(program_score_list)) {
							selectList = program_score_list;
		                }
					}else if("calcSchemeid_weight".equals(colmCode)) { //权重考核--选择方案
						if (StringUtils.isNotEmpty(program_weight_list)) {
							selectList = program_weight_list;
		                }
					}else if("recordType".equals(colmCode)) { //记录来源
						if (StringUtils.isNotEmpty(recordType_list)) {
							selectList = recordType_list;
		                }
					}else if("recordid".equals(colmCode)) { //功能项
						if (StringUtils.isNotEmpty(recordid_list)) {
							selectList = recordid_list;
		                }
					}else if("bindFormId".equals(colmCode)) { //表单模板
						if (StringUtils.isNotEmpty(form_list)) {
							selectList = form_list;
		                }
					}else if("responsibilityType".equals(colmCode)) { //责任对象
						if (StringUtils.isNotEmpty(responsibilityType_list)) {
							selectList = responsibilityType_list;
		                }
					}else if("priorityid".equals(colmCode)) { //优先级
						if (StringUtils.isNotEmpty(priorityid_list)) {
							selectList = priorityid_list;
		                }
					}
					
					if (StringUtils.isNotEmpty(selectList)) {
	                    ExcelExport.createSelectList(workbook, firstRow, lastRow, i, selectList);
	                }
					//设置样式
					if("standardDuration".equals(colmCode)) {
						sheet.setDefaultColumnStyle(i, styleBody_R);
					}else {
						sheet.setDefaultColumnStyle(i, styleBody_L);
					}
				}
			}
		}
		// 设置默认行高
		sheet.setDefaultRowHeightInPoints(20);
		// 下载记录
		ExcelExport.downLoadExcel(fileName, response, workbook);
	}
		
	/**
	 * @Description: 转换对象
	 * @param index
	 * @param titleObj
	 * @return
	 */
	private static ExcelExportEntity getExpEntity(int index, JoblistColmConfigVo titleObj) {
		String colmName = titleObj.getColmName();
		ExcelExportEntity expEntity = new ExcelExportEntity(colmName, colmName);
		expEntity.setWidth(getWidth(titleObj.getColmWidth()));
		expEntity.setOrderNum(index+1);
		expEntity.setNeedMerge(false);
		return expEntity;
	}
	
	/**
	 * @Description:获取excel宽度
	 * @param width
	 * @return
	 */
	private static double getWidth(Integer width) {
		double w = 10d;
		if (width != null) {
			w = width.intValue();
			if (w > 0) {
				w = width / 7d;
				if (w < 10d) {
					w = 10d;
				}
			}
		}
		return w;
	}
	
	/**
	 *	初始化Excel表格样式
	 * @param wb
	 * @return
	 */
	private Map<String,HSSFCellStyle> styleMapInit(HSSFWorkbook wb){
		Map<String,HSSFCellStyle> map = new HashMap<String, HSSFCellStyle>();

		//无背景颜色(中)
		HSSFCellStyle styleBody_C = wb.createCellStyle();
		styleBody_C.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_C.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
		styleBody_C.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_C.setBorderBottom(BorderStyle.THIN);
		styleBody_C.setBorderLeft(BorderStyle.THIN);
		styleBody_C.setBorderRight(BorderStyle.THIN);
		styleBody_C.setBorderTop(BorderStyle.THIN);
		styleBody_C.setLocked(false); //设置未锁定
		map.put("styleBody_C", styleBody_C);
		
		//无背景颜色(左)
		HSSFCellStyle styleBody_L = wb.createCellStyle();
		styleBody_L.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_L.setAlignment(HorizontalAlignment.LEFT); // 设置水平对齐方式
		styleBody_L.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_L.setBorderBottom(BorderStyle.THIN);
		styleBody_L.setBorderLeft(BorderStyle.THIN);
		styleBody_L.setBorderRight(BorderStyle.THIN);
		styleBody_L.setBorderTop(BorderStyle.THIN);
		styleBody_L.setLocked(false); //设置未锁定
		map.put("styleBody_L", styleBody_L);
				
		//无背景颜色(右)
		HSSFCellStyle styleBody_R = wb.createCellStyle();
		styleBody_R.setDataFormat((short) BuiltinFormats.getBuiltinFormat("TEXT"));
		styleBody_R.setAlignment(HorizontalAlignment.RIGHT); // 设置水平对齐方式
		styleBody_R.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
		// 设置边框线为细实线
		styleBody_R.setBorderBottom(BorderStyle.THIN);
		styleBody_R.setBorderLeft(BorderStyle.THIN);
		styleBody_R.setBorderRight(BorderStyle.THIN);
		styleBody_R.setBorderTop(BorderStyle.THIN);
		styleBody_R.setLocked(false); //设置未锁定
		map.put("styleBody_R", styleBody_R);
		
		return map;
	}
	
	//——————————————————————————  导出模板 ↓  —————————————————————————————————
	
}
