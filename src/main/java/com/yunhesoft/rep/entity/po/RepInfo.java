package com.yunhesoft.rep.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(value = "报表模型")
@Getter
@Setter
@Entity
@Table(name = "REP_INFO")
public class RepInfo extends BaseEntity {
    /**
     * 报表别名
     */
    @ApiModelProperty(value = "报表别名")
    @Column(name = "REPALIAS", length = 50)
    private String repAlias;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "REPNAME", length = 255)
    private String repName;
    /**
     * 注释
     */
    @ApiModelProperty(value = "注释")
    @Column(name = "MEMO", length = 255)
    private String memo;
    /**
     * 所属机构代码
     */
    @ApiModelProperty(value = "所属机构代码")
    @Column(name = "ORGCODE", length = 50)
    private String orgCode;
    /**
     * 所属机构名称
     */
    @ApiModelProperty(value = "所属机构名称")
    @Column(name = "ORGNAME", length = 50)
    private String orgName;
    /**
     * 是否使用 1：使用；0：删除
     */
    @ApiModelProperty(value = "是否使用")
    @Column(name = "TMUSED", length = 20)
    private int tmused;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @Column(name = "TMSORT", length = 20)
    private int tmsort;
}
