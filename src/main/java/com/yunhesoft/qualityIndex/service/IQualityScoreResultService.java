package com.yunhesoft.qualityIndex.service;

import com.yunhesoft.qualityIndex.entity.dto.SaveDto;
import com.yunhesoft.qualityIndex.entity.po.QualityScoreResult;
import com.yunhesoft.system.kernel.service.model.Pagination;

import java.util.List;


/**
 * (QualityScoreResult)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-14 15:18:30
 */
public interface IQualityScoreResultService {

    /**
     * 通过主键查询单条数据
     * <AUTHOR>
     * @Date 2025-03-14
     * @param id 主键
     * @return 单条数据
     */
    QualityScoreResult queryQualityScoreResultById(String id);


    /**
     * 分页查询
     * <AUTHOR>
     * @Date 2025-03-14
     * @param qualityScoreResult 筛选条件
     * @return 查询结果
     */

    List<QualityScoreResult> queryQualityScoreResult(QualityScoreResult qualityScoreResult);

    /**
     * 保存数据
     * <AUTHOR>
     * @Date 2025-03-14
     * @param qualityScoreResult 实体
     * @return 保存结果
     */
     Boolean saveQualityScoreResult(SaveDto saveDto);
}

