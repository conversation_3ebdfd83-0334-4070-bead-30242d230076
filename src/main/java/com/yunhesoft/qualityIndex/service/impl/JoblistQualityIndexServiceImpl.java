package com.yunhesoft.qualityIndex.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.entity.po.JoblistActivityExample;
import com.yunhesoft.qualityIndex.entity.dto.JoblistQualityIndexQueryDto;
import com.yunhesoft.qualityIndex.entity.dto.SaveDto;
import com.yunhesoft.qualityIndex.entity.po.QualityIndex;
import com.yunhesoft.qualityIndex.service.IJoblistQualityIndexService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 岗位工作清单活动质量指标设置(QualityIndex)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-08 20:05:05
 */
@Service
public class JoblistQualityIndexServiceImpl implements IJoblistQualityIndexService {
    @Autowired
    private EntityService dao;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @Override
    public QualityIndex queryJoblistQualityIndexById(String id) {
        Where where = Where.create();
        return dao.queryObjectById(QualityIndex.class, id);
    }


    /**
     * 分页查询
     *
     * @param joblistQualityIndex 筛选条件
     * @param pageRequest         分页对象
     * @return 查询结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @Override
    public List<QualityIndex> queryJoblistQualityIndexByPage(JoblistQualityIndexQueryDto joblistQualityIndex, Pagination<?> page) {
        Where where = Where.create();
        if (StringUtils.isNotEmpty(joblistQualityIndex.getObjectId())) {
            where.eq(QualityIndex::getObjectId, joblistQualityIndex.getObjectId());
        } else if (StringUtils.isNotEmpty(joblistQualityIndex.getObjectIds())) {
            where.in(QualityIndex::getObjectId, joblistQualityIndex.getObjectIds().toArray());
        }else{
            //同时为空可能是非法请求
            return Collections.emptyList();
        }
        where.eq(QualityIndex::getTmused, 1);

        if (StringUtils.isNotEmpty(joblistQualityIndex.getIndexName())) {
            where.like(QualityIndex::getIndexName, joblistQualityIndex.getIndexName());
        }

        Order order = Order.create();
        order.orderByAsc(QualityIndex::getCreateTime);
        return dao.queryData(QualityIndex.class, where, order, page);
    }


    /**
     * 保存数据
     *
     * @param joblistQualityIndex 实体
     * @return 保存结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @Override
    public Boolean saveJoblistQualityIndex(SaveDto saveDto) {
        Boolean flag = false;
        if (saveDto != null) {
            List<QualityIndex> insertList = new ArrayList<>();
            List<QualityIndex> updateList = new ArrayList<>();
            List<QualityIndex> deleteList = new ArrayList<>();
            if (StringUtils.isNotEmpty(saveDto.getData())) {
                JSONArray jsonArray = JSONArray.parseArray(saveDto.getData());
                if (jsonArray != null) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject row = jsonArray.getJSONObject(i);
                        Integer rowFlag = row.getInteger("TDSROW_rowFlag");
                        QualityIndex bean = row.toJavaObject(QualityIndex.class);
                        if (rowFlag == null || rowFlag == 0) {
                            bean.setId(TMUID.getUID());
                            bean.setTmused(1);
                            insertList.add(bean);
                        } else if (rowFlag == 1) {
                            bean.setTmused(1);
                            updateList.add(bean);
                        } else {
                            bean.setTmused(0);
                            deleteList.add(bean);
                        }
                    }
                }
            }
            if (StringUtils.isNotEmpty(insertList)) {
                flag = dao.insertBatch(insertList) > 0;
            }
            if (StringUtils.isNotEmpty(updateList)) {
                flag = dao.updateByIdBatch(updateList) > 0;
            }
            if (StringUtils.isNotEmpty(deleteList)) {
                flag = dao.updateByIdBatch(deleteList) > 0;
            }
        }
        return flag;
    }


    /**
     * 获取质量指标对应活动实例
     * <AUTHOR>
     * @date 2025/6/27
     * @params specialList 专业列表  日期 rq
     * @return
     */
    @Override
    public List<JoblistActivityExample> getActExamplesByQuality(List<String> specialList, String rq){
        //通过专业查询质量指标
        List<QualityIndex> qualityIndices = getQualityIndicesBySpecials(specialList);
        if (qualityIndices == null) return null;
        //通过质量指标查询活动配置id
        List<Object> activityPropertyIds =
                qualityIndices.stream().map(QualityIndex::getObjectId).distinct().collect(Collectors.toList());
        //通过提报时间和活动获取活动列表
        Where where1 = Where.create();
        where1.eq(JoblistActivityExample::getTmUsed,1);
        where1.eq(JoblistActivityExample::getTbrq,rq);
        return dao.queryListIn(JoblistActivityExample.class, JoblistActivityExample::getActivityId, activityPropertyIds, where1
                , null);
    }

    /**
     * 通过专业查询质量指标
     * <AUTHOR>
     * @date 2025/6/27
     * @params
     * @return
     *
    */
    private List<QualityIndex> getQualityIndicesBySpecials(List<String> specialList) {
        Where where = Where.create();
        where.eq(QualityIndex::getTmused,1);
        where.in(QualityIndex::getSpecCode, specialList.toArray());
        List<QualityIndex> qualityIndices = dao.rawQueryListByWhere(QualityIndex.class, where);
        if(StringUtils.isEmpty(qualityIndices)){
            return null;
        }
        return qualityIndices;
    }
}

