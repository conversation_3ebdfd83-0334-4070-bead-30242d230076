package com.yunhesoft.qualityIndex.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.qualityIndex.entity.dto.JoblistQiEvalRuleQueryDto;
import com.yunhesoft.qualityIndex.entity.dto.SaveDto;
import com.yunhesoft.qualityIndex.entity.po.QualityEvalRule;
import com.yunhesoft.qualityIndex.service.IJoblistQiEvalRuleService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 活动质量指标考核细则(QualityEvalRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-08 20:05:04
 */
@Service
public class JoblistQiEvalRuleServiceImpl implements IJoblistQiEvalRuleService {
    @Autowired
    private EntityService dao;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @Override
    public QualityEvalRule queryJoblistQiEvalRuleById(String id) {
        Where where = Where.create();
        return dao.queryObjectById(QualityEvalRule.class, id);
    }


    /**
     * 分页查询
     *
     * @param joblistQiEvalRule 筛选条件
     * @param pageRequest       分页对象
     * @return 查询结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @Override
    public List<QualityEvalRule> queryJoblistQiEvalRuleByPage(JoblistQiEvalRuleQueryDto joblistQiEvalRule, Pagination<?> page) {
        Where where = Where.create();
        if (StringUtils.isNotEmpty(joblistQiEvalRule.getObjectId())) {
            where.eq(QualityEvalRule::getObjectId, joblistQiEvalRule.getObjectId());
        }
        where.eq(QualityEvalRule::getQiId, joblistQiEvalRule.getQiId());
        where.eq(QualityEvalRule::getTmused, 1);
        if(joblistQiEvalRule.getIsEfficient() != null ){
            where.eq(QualityEvalRule::getIsEfficient, 1);
        }
        Order order = Order.create();
        order.orderByAsc(QualityEvalRule::getCreateTime);
        return dao.queryData(QualityEvalRule.class, where, order, page);
    }


    /**
     * 保存数据
     *
     * @param joblistQiEvalRule 实体
     * @return 保存结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @Override
    public Boolean saveJoblistQiEvalRule(SaveDto saveDto) {
        boolean flag = false;
        if (saveDto != null) {
            List<QualityEvalRule> insertList = new ArrayList<>();
            List<QualityEvalRule> updateList = new ArrayList<>();
            List<QualityEvalRule> deleteList = new ArrayList<>();
            if (StringUtils.isNotEmpty(saveDto.getData())) {
                JSONArray jsonArray = JSONArray.parseArray(saveDto.getData());
                if (jsonArray != null) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject row = jsonArray.getJSONObject(i);
                        Integer rowFlag = row.getInteger("TDSROW_rowFlag");
                        QualityEvalRule bean = row.toJavaObject(QualityEvalRule.class);
                        if (rowFlag == null || rowFlag == 0) {
                            bean.setId(TMUID.getUID());
                            bean.setTmused(1);
                            insertList.add(bean);
                        } else if (rowFlag == 1) {
                            bean.setTmused(1);
                            updateList.add(bean);
                        } else {
                            bean.setTmused(0);
                            deleteList.add(bean);
                        }
                    }
                }
            }
            if (StringUtils.isNotEmpty(insertList)) {
                flag = dao.insertBatch(insertList) > 0;
            }
            if (StringUtils.isNotEmpty(updateList)) {
                flag = dao.updateByIdBatch(updateList) > 0;
            }
            if (StringUtils.isNotEmpty(deleteList)) {
                flag = dao.updateByIdBatch(deleteList) > 0;
            }
        }
        return flag;
    }
}

