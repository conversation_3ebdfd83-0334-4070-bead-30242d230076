package com.yunhesoft.qualityIndex.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.qualityIndex.entity.dto.SaveDto;
import com.yunhesoft.qualityIndex.entity.po.QualityScoreResult;
import com.yunhesoft.qualityIndex.service.IQualityScoreResultService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * (QualityScoreResult)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-14 15:18:30
 */
@Service
public class QualityScoreResultServiceImpl implements IQualityScoreResultService {
    @Autowired
    private EntityService dao;

    /**
     * 通过主键查询单条数据
     * <AUTHOR>
     * @Date 2025-03-14
     * @param id 主键
     * @return 单条数据
     */
    @Override
    public QualityScoreResult queryQualityScoreResultById(String id){
        Where where = Where.create();
        return dao.queryObjectById(QualityScoreResult.class,id);
    }


    /**
     * 分页查询
     * <AUTHOR>
     * @Date 2025-03-14
     * @param qualityScoreResult 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    @Override
    public List<QualityScoreResult> queryQualityScoreResult(QualityScoreResult qualityScoreResult){
        Where where = Where.create();
        where.eq(QualityScoreResult::getQualityId, qualityScoreResult.getQualityId());
        where.eq(QualityScoreResult::getActivityPropertyId, qualityScoreResult.getActivityPropertyId());
        where.eq(QualityScoreResult::getActivityExampleId, qualityScoreResult.getActivityExampleId());
        where.in(QualityScoreResult::getPersonId,qualityScoreResult.getPersonIds().toArray());
        return dao.queryData(QualityScoreResult.class,where,null,null);
    }



    /**
     * 保存数据
     * <AUTHOR>
     * @Date 2025-03-14
     * @param qualityScoreResult 实体
     * @return 保存结果
     */
     @Override
    public Boolean saveQualityScoreResult(SaveDto saveDto){
        Boolean flag = false;
        if(saveDto!=null){
            List<QualityScoreResult> insertList = new ArrayList<>();
            List<QualityScoreResult> updateList = new ArrayList<>();
            List<QualityScoreResult> deleteList = new ArrayList<>();
            if(StringUtils.isNotEmpty(saveDto.getData())){
                JSONArray jsonArray = JSONArray.parseArray(saveDto.getData());
                if(jsonArray!=null){
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject row = jsonArray.getJSONObject(i);
                        Integer rowFlag = row.getInteger("TDSROW_rowFlag");
                        QualityScoreResult bean = row.toJavaObject(QualityScoreResult.class);
                        if(rowFlag==null || rowFlag==0){
                            bean.setId(TMUID.getUID());
                            insertList.add(bean);
                        }else if(rowFlag==1){
                            updateList.add(bean);
                        }else {
                            deleteList.add(bean);
                        }
                    }
                }
            }
            if(StringUtils.isNotEmpty(insertList)){
                flag = dao.insertBatch(insertList)>0;
            }
            if(StringUtils.isNotEmpty(updateList)){
                flag = dao.updateByIdBatch(updateList)>0;
            }
            if(StringUtils.isNotEmpty(deleteList)){
                flag = dao.updateByIdBatch(deleteList)>0;
            }
        }
        return flag;
     }
}

