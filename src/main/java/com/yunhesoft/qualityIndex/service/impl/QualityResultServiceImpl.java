package com.yunhesoft.qualityIndex.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.qualityIndex.entity.dto.SaveDto;
import com.yunhesoft.qualityIndex.entity.po.QualityResult;
import com.yunhesoft.qualityIndex.service.IQualityResultService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 质量指标计算结果(QualityResult)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-14 10:24:09
 */
@Service
public class QualityResultServiceImpl implements IQualityResultService {
    @Autowired
    private EntityService dao;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     * <AUTHOR>
     * @Date 2025-03-14
     */
    @Override
    public QualityResult queryQualityResultById(String id) {
        Where where = Where.create();
        return dao.queryObjectById(QualityResult.class, id);
    }


    /**
     * 分页查询
     *
     * @param qualityResult 筛选条件
     * @param pageRequest   分页对象
     * @return 查询结果
     * <AUTHOR>
     * @Date 2025-03-14
     */
    @Override
    public List<QualityResult> queryQualityResultByPage(QualityResult qualityResult, Pagination<?> page) {
        Where where = Where.create();
        if(StringUtils.isNotEmpty(qualityResult.getSourceId())){
            where.eq(QualityResult::getSourceId, qualityResult.getSourceId());
        }
        if(StringUtils.isNotEmpty(qualityResult.getSourceIdList())){
            where.in(QualityResult::getSourceId, qualityResult.getSourceIdList().toArray());
        }
        if(StringUtils.isNotEmpty(qualityResult.getQualityIdList())){
            where.in(QualityResult::getQualityId, qualityResult.getQualityIdList().toArray());
        }
        return dao.queryData(QualityResult.class, where, null, page);
    }


    /**
     * 保存数据
     *
     * @param qualityResult 实体
     * @return 保存结果
     * <AUTHOR>
     * @Date 2025-03-14
     */
    @Override
    public Boolean saveQualityResult(SaveDto saveDto) {
        Boolean flag = false;
        if (saveDto != null) {
            List<QualityResult> insertList = new ArrayList<>();
            List<QualityResult> updateList = new ArrayList<>();
            List<QualityResult> deleteList = new ArrayList<>();
            if (StringUtils.isNotEmpty(saveDto.getData())) {
                JSONArray jsonArray = JSONArray.parseArray(saveDto.getData());
                if (jsonArray != null) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject row = jsonArray.getJSONObject(i);
                        Integer rowFlag = row.getInteger("TDSROW_rowFlag");
                        QualityResult bean = row.toJavaObject(QualityResult.class);
                        if (rowFlag == null || rowFlag == 0) {
                            bean.setId(TMUID.getUID());
                            insertList.add(bean);
                        } else if (rowFlag == 1) {
                            updateList.add(bean);
                        } else {
                            deleteList.add(bean);
                        }
                    }
                }
            }
            if (StringUtils.isNotEmpty(insertList)) {
                flag = dao.insertBatch(insertList) > 0;
            }
            if (StringUtils.isNotEmpty(updateList)) {
                flag = dao.updateByIdBatch(updateList) > 0;
            }
            if (StringUtils.isNotEmpty(deleteList)) {
                flag = dao.updateByIdBatch(deleteList) > 0;
            }
        }
        return flag;
    }
}

