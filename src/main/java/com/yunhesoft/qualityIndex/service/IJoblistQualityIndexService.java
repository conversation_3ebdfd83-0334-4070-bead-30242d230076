package com.yunhesoft.qualityIndex.service;

import com.yunhesoft.joblist.entity.po.JoblistActivityExample;
import com.yunhesoft.qualityIndex.entity.dto.JoblistQualityIndexQueryDto;
import com.yunhesoft.qualityIndex.entity.po.QualityIndex;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.qualityIndex.entity.dto.SaveDto;

import java.util.List;


/**
 * 岗位工作清单活动质量指标设置(QualityIndex)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-08 20:05:05
 */
public interface IJoblistQualityIndexService {

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     * <AUTHOR>
     * @Date 2025-03-08
     */
    QualityIndex queryJoblistQualityIndexById(String id);


    /**
     * 分页查询
     *
     * @param joblistQualityIndex 筛选条件
     * @param pageRequest         分页对象
     * @return 查询结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    List<QualityIndex> queryJoblistQualityIndexByPage(JoblistQualityIndexQueryDto joblistQualityIndex, Pagination<?> page);


    /**
     * 保存数据
     *
     * @param joblistQualityIndex 实体
     * @return 保存结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    Boolean saveJoblistQualityIndex(SaveDto saveDto);

    List<JoblistActivityExample> getActExamplesByQuality(List<String> specialList, String rq);
}

