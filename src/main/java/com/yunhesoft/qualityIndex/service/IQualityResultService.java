package com.yunhesoft.qualityIndex.service;

import com.yunhesoft.qualityIndex.entity.po.QualityResult;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.qualityIndex.entity.dto.SaveDto;

import java.util.List;


/**
 * 质量指标计算结果(QualityResult)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-14 10:24:08
 */
public interface IQualityResultService {

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     * <AUTHOR>
     * @Date 2025-03-14
     */
    QualityResult queryQualityResultById(String id);


    /**
     * 分页查询
     *
     * @param qualityResult 筛选条件
     * @param pageRequest   分页对象
     * @return 查询结果
     * <AUTHOR>
     * @Date 2025-03-14
     */
    List<QualityResult> queryQualityResultByPage(QualityResult qualityResult, Pagination<?> page);


    /**
     * 保存数据
     *
     * @param qualityResult 实体
     * @return 保存结果
     * <AUTHOR>
     * @Date 2025-03-14
     */
    Boolean saveQualityResult(SaveDto saveDto);
}

