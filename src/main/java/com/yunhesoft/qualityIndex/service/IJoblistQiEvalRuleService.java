package com.yunhesoft.qualityIndex.service;

import com.yunhesoft.qualityIndex.entity.dto.JoblistQiEvalRuleQueryDto;
import com.yunhesoft.qualityIndex.entity.po.QualityEvalRule;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.qualityIndex.entity.dto.SaveDto;

import java.util.List;


/**
 * 活动质量指标考核细则(QualityEvalRule)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-08 20:05:04
 */
public interface IJoblistQiEvalRuleService {

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     * <AUTHOR>
     * @Date 2025-03-08
     */
    QualityEvalRule queryJoblistQiEvalRuleById(String id);


    /**
     * 分页查询
     *
     * @param joblistQiEvalRule 筛选条件
     * @param pageRequest       分页对象
     * @return 查询结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    List<QualityEvalRule> queryJoblistQiEvalRuleByPage(JoblistQiEvalRuleQueryDto joblistQiEvalRule, Pagination<?> page);


    /**
     * 保存数据
     *
     * @param joblistQiEvalRule 实体
     * @return 保存结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    Boolean saveJoblistQiEvalRule(SaveDto saveDto);
}

