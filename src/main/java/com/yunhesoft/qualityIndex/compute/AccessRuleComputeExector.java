package com.yunhesoft.qualityIndex.compute;

import com.yunhesoft.accountTools.service.IAccountAppService;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.joblist.entity.po.JobListExampleDutyPerson;
import com.yunhesoft.joblist.entity.po.JoblistActivityExample;
import com.yunhesoft.qualityIndex.entity.dto.JoblistQiEvalRuleQueryDto;
import com.yunhesoft.qualityIndex.entity.dto.QualityIndexComputeParamsDto;
import com.yunhesoft.qualityIndex.entity.po.QualityEvalRule;
import com.yunhesoft.qualityIndex.entity.po.QualityIndex;
import com.yunhesoft.qualityIndex.entity.po.QualityScoreResult;
import com.yunhesoft.qualityIndex.service.IJoblistQiEvalRuleService;
import com.yunhesoft.system.kernel.service.EntityService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class AccessRuleComputeExector extends AbstractComputeExector {
    @Autowired
    private IJoblistQiEvalRuleService ruleService;


    /**
     * 单步计算
     *
     * @return
     * <AUTHOR>
     * @date 2025/3/14
     * @params
     */
    @Autowired
    private IAccountAppService serv; // 服务类

    public QualityScoreResult compute(String sourceType, String sourceId, QualityIndexComputeParamsDto object) {
        QualityScoreResult result = new QualityScoreResult();
        //调用计算解析公式得到结果
        String resultScore = serv.judgeParse(object);
        Boolean isHit = true;
        Double v = null;
        try {
            v = Double.parseDouble(resultScore);
        } catch (Exception e) {
            //转换异常 就是未命中
            isHit = false;
        }
        //判断结果是否命中规则
        if (!isHit) {
            return null;
        }
        result.setScore(v);
        return result;
    }


    /**
     * 保存计算结果
     *
     * @return
     * <AUTHOR>
     * @date 2025/3/14
     * @params
     */
    public void saveResult(List<QualityScoreResult> result) {
        if (StringUtils.isEmpty(result)) {
            return;
        }
        EntityService dao = SpringUtils.getBean(EntityService.class);
        List<QualityScoreResult> insertList = result.stream().filter(item -> StringUtils.isEmpty(item.getId())).collect(Collectors.toList());
        List<QualityScoreResult> updateList = result.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(insertList)) {
            for (QualityScoreResult qualityResult : insertList) {
                qualityResult.setId(TMUID.getUID());
            }
            dao.insertBatch(insertList);
        }
        if (StringUtils.isNotEmpty(updateList)) {
            dao.updateBatch(updateList);
        }
    }

    /**
     * 计算活动得分
     *
     * @return
     * <AUTHOR>
     * @date 2025/3/14
     * @params
     */
    public QualityScoreResult exeCompute(QualityIndex qualityIndex, QualityIndexComputeParamsDto object) {
        QualityScoreResult currentCompute = null;
        //根据质量指标 获取全部的考核规则
        JoblistQiEvalRuleQueryDto dto = new JoblistQiEvalRuleQueryDto();
        dto.setQiId(qualityIndex.getId());
        dto.setIsEfficient(1);
        List<QualityEvalRule> qualityEvalRules = ruleService.queryJoblistQiEvalRuleByPage(dto, null);
        if (StringUtils.isNotEmpty(qualityEvalRules)) {
            List<Map<String, String>> formulaList = new ArrayList<>();
            for (QualityEvalRule qualityEvalRule : qualityEvalRules) {
                formulaList.clear();
                Map<String, String> map = new HashMap<>();
                map.put("formula", qualityEvalRule.getCondition());
                map.put("v", String.valueOf(qualityEvalRule.getScore()));
                formulaList.add(map);
                object.setFormulaMap(formulaList);
                try {
                    currentCompute = this.compute(qualityIndex.getObjectFlag(), qualityIndex.getObjectId(), object);
                    if (currentCompute != null) {
                        //已经命中
                        currentCompute.setRuleId(qualityEvalRule.getId());
                        currentCompute.setProgramId(qualityEvalRule.getProgramId());
                        currentCompute.setProgramName(qualityEvalRule.getProgramName());
                        currentCompute.setScore(qualityEvalRule.getScore());
                        return currentCompute;
                    }
                } catch (Exception e) {
                    log.error("考核分计算出现错误，资源标识：{},资源id: {}", qualityIndex.getObjectFlag(), object.getObjectId());
                }
            }
        }
        return currentCompute;
    }

    /**
     * 保存人员分数
     *
     * @return
     * <AUTHOR>
     * @date 2025/3/17
     * @params
     */
    private static void savePersonScore(QualityIndex qualityIndex, JoblistActivityExample jobBean, List<JobListExampleDutyPerson> listExampleDutyPeople, QualityEvalRule qualityEvalRule, Map<String, QualityScoreResult> scoreMap, QualityScoreResult compute, List<QualityScoreResult> results) {
        //对人员进行加分或者覆盖分
        for (JobListExampleDutyPerson listExampleDutyPerson : listExampleDutyPeople) {
            String key = qualityEvalRule.getId() + "_" + listExampleDutyPerson.getPersonId();
            QualityScoreResult oldResult = scoreMap.get(key);
            if (oldResult == null) {
                // 没有分数记录
                //拷贝计算结果给人员
                QualityScoreResult newResult = ObjUtils.copyTo(compute, QualityScoreResult.class);
                newResult.setRuleId(qualityEvalRule.getId());
                newResult.setPersonId(listExampleDutyPerson.getPersonId());
                newResult.setActivityPropertyId(jobBean.getActivityId());
                newResult.setActivityExampleId(jobBean.getId());
                newResult.setQualityId(qualityIndex.getId());
                newResult.setActivityType(jobBean.getIsParent());
                results.add(newResult);
            } else {
                //有分数记录
                if (qualityIndex.getAddScoreType() == 0) {
                    //如果是 不累加分数 直接修改分数
                    oldResult.setScore(compute.getScore());
                } else {
                    oldResult.setScore(oldResult.getScore() + compute.getScore());
                }
                results.add(oldResult);
            }
        }
    }
}
