package com.yunhesoft.qualityIndex.compute;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.qualityIndex.entity.dto.QualityIndexComputeParamsDto;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 触发聚合以及并发调用计算模块
 * @date 2025/3/17
 */
@Log4j2
@Service
public class ComputeService {

    @Autowired
    private QualityIndexComputeExector qualityIndexComputeExector;
    /**
     * 批量进行计算 使用两个线程
     *
     * @return
     * <AUTHOR>
     * @date 2025/4/1
     * @params
     */
    public void computeQualityIndexBatch(List<QualityIndexComputeParamsDto> computList) {
        log.info("开始批量计算质量指标");
        ExecutorService executorService = Executors.newFixedThreadPool(2);
        if (StringUtils.isEmpty(computList)) {
            return;
        }
        List<QualityIndexComputeParamsDto> activityList = computList.stream()
                .filter(item -> "activity".equals(item.getIndexflag()))
                .collect(Collectors.toList());
        SysUser currentUser = SysUserHolder.getCurrentUser();
        List<QualityIndexComputeParamsDto> ledgerList = computList.stream()
                .filter(item -> "ledger".equals(item.getIndexflag()))
                .collect(Collectors.toList());
        if(StringUtils.isNotEmpty(ledgerList)){
            log.info("计算台账质量指标");
            CountDownLatch countDownLatch = new CountDownLatch(ledgerList.size());
            if(StringUtils.isNotEmpty(ledgerList)){
                executorService.execute(() -> {
                    SysUserHolder.setCurrentUser(currentUser);
                    ledgerList.forEach(item -> {
                        qualityIndexComputeExector.ledgerCompute(item);
                        countDownLatch.countDown();
                    });
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("质量指标计算完毕");
        }
        log.info("计算活动质量指标");
        //提交活动计算任务
        if(StringUtils.isNotEmpty(activityList)){
            CountDownLatch countDownLatch = new CountDownLatch(activityList.size());
            executorService.execute(() -> {
                SysUserHolder.setCurrentUser(currentUser);
                activityList.forEach(item -> {
                    item.setIsUpdateFeedPerson(false);
                    qualityIndexComputeExector.activityCompute(item);
                    countDownLatch.countDown();
                });
            });
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                executorService.shutdown();
                throw new RuntimeException(e);

            }
        }
        executorService.shutdown();
    }
}
