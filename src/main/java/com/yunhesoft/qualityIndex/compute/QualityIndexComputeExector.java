package com.yunhesoft.qualityIndex.compute;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.accountTools.service.IAccountAppService;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.joblist.entity.dto.StandardJobLibQueryDto;
import com.yunhesoft.joblist.entity.po.JobListExampleDutyPerson;
import com.yunhesoft.joblist.entity.po.JoblistActivityExample;
import com.yunhesoft.joblist.entity.po.JoblistProgramAssMethod;
import com.yunhesoft.joblist.service.IJobExampleManagementService;
import com.yunhesoft.joblist.service.IJobGeneraterService;
import com.yunhesoft.joblist.service.IJoblistMethodService;
import com.yunhesoft.qualityIndex.entity.dto.JoblistQualityIndexQueryDto;
import com.yunhesoft.qualityIndex.entity.dto.QualityIndexComputeParamsDto;
import com.yunhesoft.qualityIndex.entity.po.QualityIndex;
import com.yunhesoft.qualityIndex.entity.po.QualityResult;
import com.yunhesoft.qualityIndex.entity.po.QualityScoreResult;
import com.yunhesoft.qualityIndex.service.IJoblistQualityIndexService;
import com.yunhesoft.qualityIndex.service.IQualityResultService;
import com.yunhesoft.system.kernel.service.EntityService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
@Service
public class QualityIndexComputeExector extends AbstractComputeExector {
    @Autowired
    private IJobExampleManagementService jobExampleManagementService;

    @Autowired
    private IJoblistQualityIndexService qualityIndexService;

    @Autowired
    private AccessRuleComputeExector ruleComputeExector;
    @Autowired
    private IJobGeneraterService jobGeneraterService;
    @Autowired
    private IQualityResultService qualityResultService;

    @Autowired
    private IJoblistMethodService methodService;

    //指标类型
    private static final ThreadLocal<String> INDEX_TYPE = new ThreadLocal<>();
    //因为可能在多线程环境下并发处理指标计算功能，针对线程中程序执行要求建立线程级对象
    //活动负责人员
    private static final ThreadLocal<List<JobListExampleDutyPerson>> DUTY_LIST = new ThreadLocal<>();
    //活动实例
    private static final ThreadLocal<JoblistActivityExample> ACTIVITY_EXAMPLE = new ThreadLocal<>();
    /**
     * 活动质量指标计算
     * 活动执行保存完毕 调用
     *
     * @param jobDto
     * @return
     * <AUTHOR>
     * @date 2025/3/14
     * @params
     */
    public void activityCompute(QualityIndexComputeParamsDto jobDto) {
        INDEX_TYPE.set("activity");
        if (StringUtils.isEmpty(jobDto.getObjectId())) {
            INDEX_TYPE.remove();
            DUTY_LIST.remove();
            ACTIVITY_EXAMPLE.remove();
            return;
        }
        //查询活动实例
        JoblistActivityExample jobBean = jobExampleManagementService.getJoblistActivityExampleById(jobDto.getObjectId());
        if (jobBean == null) {
            INDEX_TYPE.remove();
            DUTY_LIST.remove();
            ACTIVITY_EXAMPLE.remove();
            return;
        }
        ACTIVITY_EXAMPLE.set(jobBean);
        if(jobDto.getIsUpdateFeedPerson()==null){
            //默认是能维护责任人
            jobDto.setIsUpdateFeedPerson(true);
        }
        //准备参数  如果没有给传使用活动中的相同属性
        if(StringUtils.isEmpty(jobDto.getRq())){
            jobDto.setRq(jobBean.getTbrq());
        }
        if(StringUtils.isEmpty(jobDto.getOrgCode())){
            jobDto.setOrgCode(jobBean.getOrgCode());
        }
        if(StringUtils.isEmpty(jobDto.getOrgCode())){
            jobDto.setOrgCode(jobBean.getOrgCode());
        }
        if(StringUtils.isEmpty(jobDto.getShiftCode())){
            jobDto.setShiftCode(jobBean.getShiftClassCode());
        }
        if(StringUtils.isEmpty(jobDto.getSbsj())){
            jobDto.setSbsj(jobBean.getSbsj());
        }
        if(StringUtils.isEmpty(jobDto.getXbsj())){
            jobDto.setXbsj(jobBean.getXbsj());
        }
        if(StringUtils.isEmpty(jobDto.getIndexflag())){
            //这个方法就是用来处理活动的质量指标的
            jobDto.setIndexflag("activity");
        }
        //查询活动的负责人员  给负责人加分
        getIndexExampleDutyPersons(jobDto);
        List<JobListExampleDutyPerson> jobListExampleDutyPeople = DUTY_LIST.get();
        if(StringUtils.isEmpty(jobListExampleDutyPeople)){
            //如果是第一次反馈 责任人和反馈人肯定不会预先得知所以需要预先生成责任人
            //自动生成
            Map<String, Integer> map = new HashMap<>();
            map.put(jobDto.getObjectId(), 0);
            jobGeneraterService.updateActivityStatusByActivityId(map, () -> {
                return Collections.singletonList(ACTIVITY_EXAMPLE.get());
            });
            getIndexExampleDutyPersons(jobDto);
        }
        //根据活动id 查询配置的表单id
        //根据活动查询质量指标
        String actPropertyId = jobBean.getActivityId();
        jobDto.setActiveId(actPropertyId);
        JoblistQualityIndexQueryDto dto = new JoblistQualityIndexQueryDto();
        dto.setObjectId(actPropertyId);
        List<QualityIndex> qualityIndices = qualityIndexService.queryJoblistQualityIndexByPage(dto, null);
        if (StringUtils.isEmpty(qualityIndices) && (jobDto.getActivityStatusSkip()==null || (!jobDto.getActivityStatusSkip()))) {
            //未设置质量指标
            //直接尝试更新活动状态
            JSONArray activityArray = new JSONArray();
            JSONObject activityObj = new JSONObject();
            activityObj.put("taskId", jobBean.getId());
            activityArray.add(activityObj);
            jobGeneraterService.saveActivityStatusHandler(activityArray, jobDto.getIsUpdateFeedPerson());
            INDEX_TYPE.remove();
            DUTY_LIST.remove();
            ACTIVITY_EXAMPLE.remove();
            return;
        }
        List<String> qualityIdList = qualityIndices.stream().map(QualityIndex::getId).collect(Collectors.toList());
        //计算质量指标
        Map<String, List<QualityResult>> qualityResults = exeComputeToIndexObject(jobDto, qualityIdList, qualityIndices);
        //合并质量指标结果 以最新结果为准
        mergeResult(qualityResults.get("new"), qualityResults.get("old"));
        if(jobDto.getActivityStatusSkip()==null || (!jobDto.getActivityStatusSkip())){
            //根据指标结果更新活动状态
            updateActivityStatusByIndexResult(jobDto, qualityIndices, qualityResults.get("new"), qualityResults.get("old"));
        }
        //保存计算人员得分
        computePersonScoreAllIndex(qualityResults);
        INDEX_TYPE.remove();
        DUTY_LIST.remove();
        ACTIVITY_EXAMPLE.remove();
    }

    private void computePersonScoreAllIndex(Map<String, List<QualityResult>> qualityResults) {
        if(qualityResults==null){
            return;
        }
        List<JobListExampleDutyPerson> jobListExampleDutyPeople;
        jobListExampleDutyPeople = DUTY_LIST.get();
        //本活动 全部的人员得分情况
        List<JSONObject> scoreCaseAll = qualityResults.get("new").stream()
                .map(QualityResult::getProgramResult)
                .filter(item -> StringUtils.isNotEmpty(item))
                //当前活动的全部指标的得分情况
                .map(item -> JSONObject.parseObject(item))
                .collect(Collectors.toList());
        //将人员得分重新累计计算 ，此活动的所有指标每个人累计得多少分
        Map<String,Double> scoreMap = new HashMap<>();
        for (JSONObject jsonObject : scoreCaseAll) {
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String key = entry.getKey();
                BigDecimal v1 = new BigDecimal(scoreMap.getOrDefault(key,0.0));
                BigDecimal v2 = new BigDecimal(jsonObject.getDoubleValue(key));
                scoreMap.put(key,v1.add(v2).doubleValue());
            }
        }
        for (JobListExampleDutyPerson jobListExampleDutyPerson : jobListExampleDutyPeople) {
            //保存每个人的分数
            jobListExampleDutyPerson.setScore(scoreMap.get(jobListExampleDutyPerson.getPersonId()));
            jobListExampleDutyPerson.setRowFlag(1);
        }
        jobGeneraterService.saveDutyPerson(jobListExampleDutyPeople,true);
    }

    /**
     * 获取指标活动相关人员
     * <AUTHOR>
     * @date 2025/6/10
     * @params
     * @return
     *
    */
    private void getIndexExampleDutyPersons(QualityIndexComputeParamsDto jobDto) {
        //查询活动的负责人员  给负责人加分
        List<String> ids = new ArrayList<>();
        ids.add(jobDto.getObjectId());
        List<JobListExampleDutyPerson> listExampleDutyPeople = jobGeneraterService.getListExampleDutyPeople(ids);
        listExampleDutyPeople = listExampleDutyPeople.stream()
                .filter(item -> Objects.equals(0, item.getPersonType()))
                .collect(Collectors.toList());
        DUTY_LIST.set(listExampleDutyPeople);
    }


    /**
     * 台账 质量指标计算
     *
     * @param jobDto
     * @return
     * <AUTHOR>
     * @date 2025/3/14
     * @params
     */
    public void ledgerCompute(QualityIndexComputeParamsDto jobDto) {
        INDEX_TYPE.set("ledger");
        JoblistQualityIndexQueryDto dto = new JoblistQualityIndexQueryDto();
        dto.setObjectId(jobDto.getObjectId());
        List<QualityIndex> qualityIndices = qualityIndexService.queryJoblistQualityIndexByPage(dto, null);
        if (StringUtils.isEmpty(qualityIndices)) {
            return;
        }
        List<String> qualityIdList = qualityIndices.stream().map(QualityIndex::getId).collect(Collectors.toList());
        exeComputeToIndexObject(jobDto, qualityIdList, qualityIndices);
        INDEX_TYPE.remove();
    }

    private Map<String,List<QualityResult>> exeComputeToIndexObject(QualityIndexComputeParamsDto object,
                                                         List<String> qualityIdList,
                                         List<QualityIndex> qualityIndices) {
        Map<String,List<QualityResult>> reslutMap = new HashMap<>();
        //查询 已经有的质量指标结果
        QualityResult queryQualityResult = new QualityResult();
        queryQualityResult.setSourceId(object.getObjectId());
        queryQualityResult.setQualityIdList(qualityIdList);
        List<QualityResult> qualityResultList = qualityResultService.queryQualityResultByPage(queryQualityResult, null);
        Map<String, QualityResult> qualityMap = Optional.ofNullable(qualityResultList)
                .orElse(new ArrayList<>()).stream().
                collect(Collectors.toMap(item-> item.getQualityId()+"_"+item.getSourceId(), Function.identity()));
        List<QualityResult> resultList = new ArrayList<>();
        //计算质量指标
        for (QualityIndex qualityIndex : qualityIndices) {
            if(StringUtils.isEmpty(qualityIndex.getDataSourceFormula())){
                //没有配置数据来源公式
                continue;
            }
            QualityResult result = this.compute(qualityIndex.getObjectFlag(), object, qualityIndex, qualityMap);
            if (result != null) {
                resultList.add(result);
            }
        }
        if(StringUtils.isNotEmpty(resultList)){
            //保存质量指标结果
            this.saveResult(resultList);
        }
        reslutMap.put("new",resultList);
        reslutMap.put("old",qualityResultList);
        return reslutMap;
    }

    /**
     * 根据指标结果更新活动状态
     * <AUTHOR>
     * @date 2025/6/5
     * @params
     * @return
     *
    */
    private void updateActivityStatusByIndexResult(QualityIndexComputeParamsDto object, List<QualityIndex> qualityIndices, List<QualityResult> resultList, List<QualityResult> qualityResultList) {
        //查验质量指标中全部的关键指标是否完成
        //获取关键指标
        List<String> importantQualityIndices = qualityIndices
                .stream().filter(item-> Objects.equals(item.getIsImportant(),1))
                .map(QualityIndex::getId)
                .collect(Collectors.toList());
        //查验指标结果
        List<QualityResult> importResult = resultList.stream()
                //获取关键指标结果
                .filter(item -> importantQualityIndices.contains(item.getQualityId()))
                .collect(Collectors.toList());
                //关键指标结果全部已完成
        boolean allFinish = importResult.stream().allMatch((item) -> item.getResult() == 1);
        if(allFinish){
            //关键指标全部完成
            //执行确认流程并且更新状态
            JSONArray activityArray = new JSONArray();
            JSONObject activityObj = new JSONObject();
            activityObj.put("taskId", object.getObjectId());
            activityArray.add(activityObj);
            jobGeneraterService.saveActivityStatusHandler(activityArray);
        }else{
            //如果关键指标并未全部完成 则仅更新状态为已反馈
            Map<String, Integer> map = new HashMap<>();
            map.put(object.getObjectId(), 1);
            jobGeneraterService.updateActivityStatusByActivityId(map,false, () -> {
                return Collections.singletonList(ACTIVITY_EXAMPLE.get());
            });
        }
    }

    /**
     * 合并质量指标结果 以最新结果为准
     * <AUTHOR>
     * @date 2025/6/5
     * @params
     * @return
     *
    */
    private static void mergeResult(List<QualityResult> resultList, List<QualityResult> qualityResultList) {
        //整理最新结果中的全部质量指标结果
        List<String> newIds= resultList.stream().map(QualityResult::getId).collect(Collectors.toList());
        //整理数据库中最新的质量指标结果
        List<QualityResult> dataResult = qualityResultList
                .stream().filter(item -> !newIds.contains(item.getId())).collect(Collectors.toList());
        resultList.addAll(dataResult);
    }

    /**
     * 单步执行计算        得到指标计算结果
     *
     * @return
     * <AUTHOR>
     * @date 2025/3/14
     * @params
     */

    @Autowired
    private IAccountAppService serv; // 服务类
    public QualityResult compute(String sourceType, QualityIndexComputeParamsDto object, QualityIndex indexObj, Map<String, QualityResult> qualityResultMap) {
        QualityResult qualityResult = null;
        //解析数据来源公式进行计算得出结果
        object.setFormulaTxt(indexObj.getDataSourceFormula());
        //数据来源公式 计算的结果
        String dataResult = serv.parseFormula(object);
        //计算指标完成结果
        object.setVal(dataResult);
        List<Map<String, String>> formulaMap = new ArrayList<>();
        Map<String, String> pass = new HashMap<>();
        pass.put("formula",indexObj.getFinishFormula());
        pass.put("v","200");
        formulaMap.add(pass);
        object.setFormulaMap(formulaMap);
        //完成条件结果
        String finishResult = serv.judgeParse(object);
        QualityScoreResult qualityRuleScoreResult = null;
        if (qualityResultMap.containsKey(indexObj.getId()+"_"+object.getObjectId())) {
            //更新结果
            //保存计算公式得到的结果
            qualityResult = qualityResultMap.get(indexObj.getId()+"_"+object.getObjectId());
            qualityResult.setFormulaResult(dataResult);
            qualityResult.setCondition(indexObj.getFinishFormula());
            qualityResult.setResult("200".equals(finishResult)?1:-1);
        } else {
            //保存新结果
            qualityResult = new QualityResult();
            qualityResult.setSourceType(sourceType);
            qualityResult.setSourceId(object.getObjectId());
            qualityResult.setQualityId(indexObj.getId());
            qualityResult.setBusinessKey(object.getLedgerId());
            //解析公式进行计算得出结果
            //保存计算公式得到的结果
            qualityResult.setFormulaResult(dataResult);
            qualityResult.setCondition(indexObj.getFinishFormula());
            qualityResult.setResult("200".equals(finishResult)?1:-1);
        }
        //计算分数
        qualityRuleScoreResult = ruleComputeExector.exeCompute(indexObj, object);
        if (qualityRuleScoreResult!=null) {
            //记录分配方案
            qualityResult.setProgramId(qualityRuleScoreResult.getProgramId());
            qualityResult.setProgramName(qualityRuleScoreResult.getProgramName());
            if(!Objects.equals(qualityResult.getScore(), qualityRuleScoreResult.getScore())) {
                //更新总分数
                qualityResult.setScore(qualityRuleScoreResult.getScore());
            }
            if("activity".equals(INDEX_TYPE.get())){
                //计算活动的质量指标  分配详细活动得分
                qualityResult = getQualityScoreAllocateResult(qualityResult, qualityRuleScoreResult);
            }
        }
        return qualityResult;
    }

    private QualityResult getQualityScoreAllocateResult(QualityResult qualityResult, QualityScoreResult qualityScoreResult) {
        //将活动得分按照方案分配给人员
        //获得人员
        List<JobListExampleDutyPerson> dutyPeopleList = DUTY_LIST.get();
        if(StringUtils.isEmpty(dutyPeopleList)){
            return qualityResult;
        }
        //获得分配方案
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setPid(qualityScoreResult.getProgramId());
        List<JoblistProgramAssMethod> program = methodService.getJoblistProgramAssMethodList(queryDto);
        if (StringUtils.isEmpty(program)) {
            return qualityResult;
        }
        //根据方案获取分配分
        Map<String,Double> result = new HashMap<>();
        jobGeneraterService.getPostScoreProgramDetail(qualityScoreResult.getRuleId(), qualityScoreResult.getScore(),1,program,result);
        JSONObject jsonObject = new JSONObject();
        for (JobListExampleDutyPerson jobListExampleDutyPerson : dutyPeopleList) {
            Double v = result.get(qualityScoreResult.getRuleId()+"_"+jobListExampleDutyPerson.getPostId());
            jsonObject.put(jobListExampleDutyPerson.getPersonId(),v);
            jobListExampleDutyPerson.setScore(v);
        }
        //记录分配结果
        qualityResult.setProgramResult(jsonObject.toJSONString());
        return qualityResult;
    }


    /**
     * 保存计算结果
     *
     * @return
     * <AUTHOR>
     * @date 2025/3/14
     * @params
     */
    public void saveResult(List<QualityResult> result) {
        if (StringUtils.isEmpty(result)) {
            return;
        }
        List<QualityResult> insertList = result.stream()
                .filter(item -> StringUtils.isEmpty(item.getId())).collect(Collectors.toList());
        List<QualityResult> updateList = result.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getId())).collect(Collectors.toList());
        EntityService dao = SpringUtils.getBean(EntityService.class);
        if (StringUtils.isNotEmpty(insertList)) {
            for (QualityResult qualityResult : insertList) {
                qualityResult.setId(TMUID.getUID());
            }
            dao.insertBatch(insertList, 500);
        }
        if (StringUtils.isNotEmpty(updateList)) {
            dao.updateByIdBatch(updateList);
        }
    }
}
