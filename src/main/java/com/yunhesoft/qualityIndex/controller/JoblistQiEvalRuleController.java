package com.yunhesoft.qualityIndex.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.qualityIndex.entity.dto.JoblistQiEvalRuleQueryDto;
import com.yunhesoft.qualityIndex.entity.dto.SaveDto;
import com.yunhesoft.qualityIndex.service.IJoblistQiEvalRuleService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 活动质量指标考核细则(QualityEvalRule)表控制层
 *
 * <AUTHOR>
 * @Date 2025-03-08
 */
@RestController
@RequestMapping("/joblistQiEvalRule")
public class JoblistQiEvalRuleController extends BaseRestController {
    /**
     * 服务对象
     */
    @Autowired
    private IJoblistQiEvalRuleService joblistQiEvalRuleService;

    /**
     * 分页查询
     *
     * @param joblistQiEvalRule 筛选条件
     * @param pageRequest       分页对象
     * @return 查询结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @RequestMapping(value = "/queryJoblistQiEvalRuleByPage", method = {RequestMethod.POST})
    public Res<?> queryJoblistQiEvalRuleByPage(@RequestBody JoblistQiEvalRuleQueryDto joblistQiEvalRule) {
        return Res.OK(joblistQiEvalRuleService.queryJoblistQiEvalRuleByPage(joblistQiEvalRule, null));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @RequestMapping(value = "/queryJoblistQiEvalRuleById", method = {RequestMethod.POST})
    public Res<?> queryJoblistQiEvalRuleById(@RequestParam("id") String id) {
        return Res.OK(joblistQiEvalRuleService.queryJoblistQiEvalRuleById(id));
    }

    /**
     * 保存数据
     *
     * @param joblistQiEvalRule 实体
     * @return 保存结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @RequestMapping(value = "/saveJoblistQiEvalRule", method = {RequestMethod.POST})
    public Res<?> saveJoblistQiEvalRule(@RequestBody SaveDto saveDto) {
        return Res.OK(joblistQiEvalRuleService.saveJoblistQiEvalRule(saveDto));
    }
}

