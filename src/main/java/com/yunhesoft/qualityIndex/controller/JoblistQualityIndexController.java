package com.yunhesoft.qualityIndex.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.qualityIndex.compute.ComputeService;
import com.yunhesoft.qualityIndex.compute.QualityIndexComputeExector;
import com.yunhesoft.qualityIndex.entity.dto.JoblistQualityIndexQueryDto;
import com.yunhesoft.qualityIndex.entity.dto.QualityIndexComputeParamsDto;
import com.yunhesoft.qualityIndex.entity.dto.SaveDto;
import com.yunhesoft.qualityIndex.service.IJoblistQualityIndexService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 岗位工作清单活动质量指标设置(QualityIndex)表控制层
 *
 * <AUTHOR>
 * @Date 2025-03-08
 */
@RestController
@RequestMapping("/joblistQualityIndex")
public class JoblistQualityIndexController extends BaseRestController {
    /**
     * 服务对象
     */
    @Autowired
    private IJoblistQualityIndexService joblistQualityIndexService;
    @Autowired
    private ComputeService computeService;
    @Autowired
    private QualityIndexComputeExector qualityIndexComputeExector;

    /**
     * 分页查询
     *
     * @param joblistQualityIndex 筛选条件
     * @param pageRequest         分页对象
     * @return 查询结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @RequestMapping(value = "/queryJoblistQualityIndexByPage", method = {RequestMethod.POST})
    public Res<?> queryJoblistQualityIndexByPage(@RequestBody JoblistQualityIndexQueryDto joblistQualityIndex) {
        return Res.OK(joblistQualityIndexService.queryJoblistQualityIndexByPage(joblistQualityIndex, null));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @RequestMapping(value = "/queryJoblistQualityIndexById", method = {RequestMethod.POST})
    public Res<?> queryJoblistQualityIndexById(@RequestParam("id") String id) {
        return Res.OK(joblistQualityIndexService.queryJoblistQualityIndexById(id));
    }

    /**
     * 保存数据
     *
     * @param joblistQualityIndex 实体
     * @return 保存结果
     * <AUTHOR>
     * @Date 2025-03-08
     */
    @RequestMapping(value = "/saveJoblistQualityIndex", method = {RequestMethod.POST})
    public Res<?> saveJoblistQualityIndex(@RequestBody SaveDto saveDto) {
        return Res.OK(joblistQualityIndexService.saveJoblistQualityIndex(saveDto));
    }


    /**
     * 计算接口
     * <AUTHOR>
     * @date 2025/3/14
     * @params
     * @return
     *
     */
    @RequestMapping(value = "/computeQualityIndexBatch", method = {RequestMethod.POST})
    public Res<?> computeQualityIndexBatch(@RequestBody List<QualityIndexComputeParamsDto> dto) {
        if(StringUtils.isNotEmpty(dto)){
            for (QualityIndexComputeParamsDto qualityIndexComputeParamsDto : dto) {
                qualityIndexComputeParamsDto.setIsUpdateFeedPerson(false);
            }
            computeService.computeQualityIndexBatch(dto);
        }
        return Res.OK("计算完毕");
    }
    /**
     * 单条计算测试接口
     * <AUTHOR>
     * @date 2025/3/14
     * @params
     * @return
     *
    */
    @RequestMapping(value = "/computeQualityIndex", method = {RequestMethod.POST})
    public Res<?> computeQualityIndex(@RequestBody QualityIndexComputeParamsDto dto) {
        if("activity".equals(dto.getIndexflag())){
            qualityIndexComputeExector.activityCompute(dto);
        }else{
        qualityIndexComputeExector.ledgerCompute(dto);
        }
        return Res.OK("计算完毕");
    }
}

