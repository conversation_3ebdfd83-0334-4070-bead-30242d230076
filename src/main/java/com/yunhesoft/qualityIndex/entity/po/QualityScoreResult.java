package com.yunhesoft.qualityIndex.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

@ApiModel(value = "得分结果")
@Entity
@Data
@Table(name = "QUALITY_SCORE_RESULT")
public class QualityScoreResult extends BaseEntity {
    //活动配置id
    @Column(name = "ACTIVITY_PROPERTY_ID", length = 255)
    private String activityPropertyId;

    //活动实例id
    @Column(name = "ACTIVITY_EXAMPLE_ID", length = 255)
    private String activityExampleId;

    //活动实例类型
    @Column(name = "ACTIVITY_TYPE", length = 255)
    private Integer activityType;

    //指标id
    @Column(name = "QUALITY_ID", length = 255)
    private String qualityId;

    //细则id
    @Column(name = "RULE_ID", length = 255)
    private String ruleId;

    //人员id
    @Column(name = "SOURCE_TYPE", length = 255)
    private String personId;

    //人员得分
    @Column(name = "SCORE")
    private Double score;

    @Transient
    private String programId ;
    @Transient
    private String programName;

    @Transient
    private List<String> personIds;

    @Transient
    private List<String> objectIds;

}
