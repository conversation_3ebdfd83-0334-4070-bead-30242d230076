package com.yunhesoft.qualityIndex.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(value = "活动质量指标考核细则")
@Entity
@Data
@Table(name = "QUALITY_EVAL_RULE")
public class QualityEvalRule extends BaseEntity {
    //质量指标ID
    @ApiModelProperty(value = "质量指标ID")
    @Column(name = "QI_ID", length = 255)
    private String qiId;

    @ApiModelProperty(value = "对象id")
    @Column(name = "OBJECT_ID", length = 255)
    private String objectId;
    @ApiModelProperty(value = "对象标识  用于指定是什么模块的质量指标")
    @Column(name = "OBJECT_FLAG", length = 255)
    private String objectFlag;

    //条件 满足此条件将获取对应分数
    @ApiModelProperty(value = "条件 满足此条件将获取对应分数")
    @Column(name = "CONDITION", length = 255)
    private String condition;
    //得分
    @ApiModelProperty(value = "得分")
    @Column(name = "SCORE", length = 255)
    private Double score;

    @ApiModelProperty(value = "得分方案")
    @Column(name = "programName", length = 255)
    private String programName ;

    @ApiModelProperty(value = "得分方案id")
    @Column(name = "programId", length = 255)
    private String programId;

    //可用标识 1=可用，0=无效
    @ApiModelProperty(value = "可用标识 1=可用，0=无效")
    @Column(name = "IS_EFFICIENT")
    private Integer isEfficient;

    @ApiModelProperty(value = "可用标识 1=可用，0=无效")
    @Column(name = "TMUSED")
    private Integer tmused;

    @ApiModelProperty(value = "完成条件值")
    @Column(name = "formula_data" , length = 500)
    private String formulaData;
}

