package com.yunhesoft.qualityIndex.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.List;

@ApiModel(value = "质量指标计算结果")
@Entity
@Data
@Table(name = "QUALITY_RESULT",indexes = {
        @Index(name = "QUALITY_RESULT_SOURCE_ID_IDX", columnList = "SOURCE_ID")
})
public class QualityResult extends BaseEntity {
    //资源类型 activity : 活动指标  ledger：台账指标
    @ApiModelProperty(value = "资源类型 activity : 活动指标  ledger：台账指标")
    @Column(name = "SOURCE_TYPE", length = 255)
    private String sourceType;
    //质量指标id 记录由哪条质量指标计算得出的
    @ApiModelProperty(value = "质量指标id 记录由哪条质量指标计算得出的")
    @Column(name = "QUALITY_ID", length = 255)
    private String qualityId;
    //加索引
    //资源id 记录计算此质量指标所涉及的资源id   通常为活动id  台账id
    @ApiModelProperty(value = "资源id 记录计算此质量指标所涉及的资源id   通常为活动id  台账id，用于区分计算对象")
    @Column(name = "SOURCE_ID", length = 255)
    private String sourceId;

    //资源id 记录计算此质量指标所涉及的资源id   通常为活动id  台账id
    @ApiModelProperty(value = "业务主键 来自于与业务相关的其他主键数据")
    @Column(name = "BUSINESS_KEY", length = 255)
    private String businessKey;

    //完成条件 完成条件
    @ApiModelProperty(value = "完成条件 完成条件")
    @Column(name = "CONDITION", length = 255)
    private String condition;
    //公式统计值结果 记录数据来源公式计算的指标结果
    @ApiModelProperty(value = "公式统计值结果 记录数据来源公式计算的指标结果")
    @Column(name = "FORMULA_RESULT", length = 255)
    private String formulaResult;
    //指标完成情况 1：已完成 0未完成 空就是未计算或者未计算成功
    @ApiModelProperty(value = "指标完成情况 1：已完成 0未完成 空就是未计算或者未计算成功")
    @Column(name = "RESULT")
    private Integer result;

    @ApiModelProperty(value = "得分分配方案id")
    @Column(name = "PROGRAM_ID",length = 50)
    private String  programId;
    @ApiModelProperty(value = "得分分配方案")
    @Column(name = "PROGRAM_NAME",length = 50)
    private String  programName;

    @ApiModelProperty(value = "得分分配结果")
    @Column(name = "PROGRAM_RESULT",length = 2000)
    private String  programResult;

    //指标得分得分
    @Column(name = "SCORE")
    private Double score;

    @Transient
    private List<String> qualityIdList;
    @Transient
    private List<String> sourceIdList;
}

