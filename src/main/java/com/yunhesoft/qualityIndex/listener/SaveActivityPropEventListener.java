package com.yunhesoft.qualityIndex.listener;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.service.event.SaveActivityPropertiesEvent;
import com.yunhesoft.qualityIndex.entity.dto.SaveDto;
import com.yunhesoft.qualityIndex.service.IJoblistQualityIndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class SaveActivityPropEventListener implements ApplicationListener<SaveActivityPropertiesEvent> {
    @Autowired
    private IJoblistQualityIndexService joblistQualityIndexService;
    /**
     * Handle an application event.
     *
     * @param event the event to respond to
     */
    @Override
    public void onApplicationEvent(SaveActivityPropertiesEvent event) {
        SaveDto saveDto = new SaveDto();
        SaveActivityPropertiesEvent.EventData eventData = event.getEventData();
        String jobListQualityList = eventData.getJobListQualityList();
        if(StringUtils.isEmpty(jobListQualityList)){
            return;
        }
        JSONArray jobListQualityArray = JSONArray.parseArray(jobListQualityList);
        for (int i = 0; i < jobListQualityArray.size(); i++) {
            JSONObject jsonObject = jobListQualityArray.getJSONObject(i);
            jsonObject.put("objectId", eventData.getObjectId());
        }
        saveDto.setData(jobListQualityArray.toJSONString());
        joblistQualityIndexService.saveJoblistQualityIndex(saveDto);
    }
}
