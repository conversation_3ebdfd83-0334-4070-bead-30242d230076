package com.yunhesoft.mobile.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yunhesoft.accountTools.entity.dto.AccountParam;
import com.yunhesoft.accountTools.entity.vo.AccountDataVo;
import com.yunhesoft.accountTools.service.IAccountToolsService;
import com.yunhesoft.aip.iocr.entity.vo.ImageOcrTextVo;
import com.yunhesoft.aip.iocr.service.IOcrService;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.constant.JoblistConstant;
import com.yunhesoft.joblist.entity.dto.AccountSaveDto;
import com.yunhesoft.joblist.entity.dto.JoblistInputMappingDto;
import com.yunhesoft.joblist.service.IJoblistInputService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostuintVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampleDotByClassVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.mobile.entity.dto.DeviceStatusSwitchDto;
import com.yunhesoft.mobile.entity.dto.MobLeanCostingDto;
import com.yunhesoft.mobile.entity.po.AcctobjInput;
import com.yunhesoft.mobile.entity.po.AcctobjInputFl;
import com.yunhesoft.mobile.entity.po.AcctobjInputmx;
import com.yunhesoft.mobile.entity.vo.*;
import com.yunhesoft.mobile.exception.MobileException;
import com.yunhesoft.mobile.service.IMobLeanCostingService;
import com.yunhesoft.mobile.util.ControlTypeConverter;
import com.yunhesoft.rtdb.client.RtdbClient;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.system.applyConf.service.IApplyConfService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.transaction.Transactional;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
@Service

public class MobLeanCostingServiceImpl implements IMobLeanCostingService {
    @Autowired
    private ICostService iCostService;
    @Autowired
    private ICostuintService iCostunitService;
    @Autowired
    private EntityService entityService;
    @Autowired
    private UnitItemInfoService unitItemInfoService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private RtdbClient rtdbClient;
    @Autowired
    private ISysConfigService sysConfigSrv;
    //文件管理
    @Autowired
    private IFilesInfoService fileSrv;
    //文本识别
    @Autowired
    private IOcrService ocrSrv;
    @Autowired
    private IAccountToolsService accountToolsSrv;
    @Autowired
    private IJoblistInputService joblistInputService;
    @Autowired
    private IApplyConfService applyConfSrv;

    /**
     * 获取核算对象列表
     *
     * @param param
     * @return
     */
    @Override
    public List<MobAccountingObjectVo> getAccountingObjectList(MobLeanCostingDto param) {
        List<MobAccountingObjectVo> list = new ArrayList<>();
        //SysUser user = SysUserHolder.getCurrentUser();

        List<Costuint> costUnitList =null;

        try {
            long startTime = System.currentTimeMillis();
            //List<MaintenanceStandardsComboVo> costUnitList = iCostunitService.getDeviceIds(param.getFlid());
            costUnitList = iCostunitService.getDeviceIds(param.getFlid());
            //List<Costuint> costUnitList = iCostService.getCostuintListByOrgId(user.getOrgId(), user.getId(), user.getPostId(), 2);
            long endTime = System.currentTimeMillis();
            System.out.println("「移动端」获取核算对象列表时长：" + (endTime - startTime) + " ms");
        } catch (Exception e) {
            log.error("「移动端」获取核算对象列表失败", e);
        }

        if (ObjUtils.isEmpty(costUnitList)) {
            return list;
        }

        //按照移动端使用标识过滤
        //costUnitList = costUnitList.stream().filter(b -> {
        //    Integer mobileInput = b.getMobileInput();
        //    if (mobileInput != null && mobileInput == 1) {
        //        return true;
        //    }
        //    return false;
        //}).collect(Collectors.toList());

        //获取历史数据，更新核算对象已录状态
        String searchDate = param.getSearchDate();
        if (StringUtils.isEmpty(searchDate)) {
            searchDate = DateTimeUtils.getNowDateStr(); //默认今天
            param.setSearchDate(searchDate);
        }
        List<AcctobjInput> historyInputList = getAcctobjInputData(param);
        Map<String, AcctobjInput> historyInputMap = new HashMap<>();
        if (ObjUtils.notEmpty(historyInputList)) {
            historyInputMap = historyInputList.stream().collect(Collectors.toMap(AcctobjInput::getAcctobjId, Function.identity(), (oldValue, newValue) -> oldValue));
        }

        for (Costuint costUnit : costUnitList) {
            MobAccountingObjectVo vo = new MobAccountingObjectVo();
            vo.setAcctobjId(costUnit.getId());
            vo.setAcctobjName(costUnit.getName());

            // 更新已录入状态
            AcctobjInput hisInput = historyInputMap.get(vo.getAcctobjId());
            if (hisInput != null) {
                vo.setInputed(true);
            }
            // 经纬度
            vo.setAcctobjLongitude(costUnit.getCoordinateX());
            vo.setAcctobjLatitude(costUnit.getCoordinateY());
            // 作业半径
            vo.setOperatingRadius(costUnit.getOperatingRadius());

            list.add(vo);
        }

        return list;
    }

    /**
     * 获取指定日期已录入井状态
     * @param param
     * @return
     */
    @Override
    public List<MobAccountingObjectVo> getAccountingObjectInputedStatus(MobLeanCostingDto param) {
        List<MobAccountingObjectVo> list = new ArrayList<>();

        //获取历史数据，更新核算对象已录状态
        String searchDate = param.getSearchDate();
        if (StringUtils.isEmpty(searchDate)) {
            searchDate = DateTimeUtils.getNowDateStr(); //默认今天
            param.setSearchDate(searchDate);
        }
        List<AcctobjInput> historyInputList = getAcctobjInputData(param);
        Map<String, AcctobjInput> historyInputMap = new HashMap<>();
        if (ObjUtils.notEmpty(historyInputList)) {
            historyInputMap = historyInputList.stream().collect(Collectors.toMap(AcctobjInput::getAcctobjId, b -> b));
        }
        Set<String> acctobjIdSet = historyInputMap.keySet();
        for (String acctobjId : acctobjIdSet) {
            MobAccountingObjectVo vo = new MobAccountingObjectVo();
            vo.setAcctobjId(acctobjId);
            // 更新已录入状态
            vo.setInputed(true);
            list.add(vo);
        }

        return list;
    }

    /**
     * 获取核算对象采集点
     * @param param
     * @return
     */
    @Override
    public AcctobjInputVo getAccountingObjectCollectionPointList(MobLeanCostingDto param) {
        AcctobjInputVo inputData = new AcctobjInputVo();


        MobLeanCostingDto newParam = ObjUtils.copyTo(param, MobLeanCostingDto.class);
        newParam.setInputDateTime(null);

        //1. 查询当前核算对象、指定录入时间是否有历史数据，如有则返回历史数据。如没有当前录入时间的数据但有当前班次的数据，则使用任意历史数据做为新采集点数据返回给前端使用
        //当天数据
        List<AcctobjInputVo> hisData = getInputData(newParam);
        if (ObjUtils.notEmpty(hisData)) {
            String inputDateTime = param.getInputDateTime();
            //当前录入时间数据
            List<AcctobjInputVo> findData = hisData.stream().filter(o -> {
                Date inputTime = o.getInputTime();
                if (inputTime == null) {
                    return false;
                }
                String inputTimeStr = DateTimeUtils.formatDateTime(inputTime);
                return inputTimeStr.equals(inputDateTime);
            }).collect(Collectors.toList());

            if (ObjUtils.notEmpty(findData)) {
                inputData = findData.get(0);
            } else {
                String bcdm = param.getBcdm();
                if (StringUtils.isEmpty(bcdm)) {
                    throw new RuntimeException("班次代码无效");
                }

                //当前班次数据
                List<AcctobjInputVo> hisDataBc = hisData.stream().filter(o -> {
                    String bcdm2 = o.getBcdm();
                    return bcdm.equals(bcdm2);
                }).collect(Collectors.toList());

                inputData = hisDataBc.get(0);

                //需要清除无用数据值作为新数据返回
                clearInputDataVo(inputData);
            }
            return inputData;
        }

        //2. 当前班次无历史数据，则调用核算对象设置接口获取最新的采集点配置
        //List<Costunitsampledot> sampleDotList = null;
        List<SampleDotByClassVo> sampleDotByInputRightFromClass = null;

        try {
            long startTime = System.currentTimeMillis();
            //TODO 未启用多租户时，租户ID：user.getTenant_id()，默认为0，会导致接口报错：无 TENANT_ID 字段，暂时传空
            //List<Costunitsampledot> sampleDotList = unitItemInfoService.getSampleDot(param.getAcctobjId(), param.getInputDateTime(), null);
            //sampleDotList = unitItemInfoService.getSampleDotByInputRight(param.getAcctobjId(), param.getInputDateTime());
            sampleDotByInputRightFromClass = unitItemInfoService.getSampleDotByInputRightFromClass(param.getAcctobjId(), param.getInputDateTime());
            long endTime = System.currentTimeMillis();
            System.out.println("「移动端」获取采集点数据时长：" + (endTime - startTime) + " ms");
        } catch (Exception e) {
            log.error("「移动端」获取采集点数据失败", e);
        }

        if (ObjUtils.notEmpty(sampleDotByInputRightFromClass)) {
            List<AcctobjInputFlVo> flData = new ArrayList<>();
            int flIdx = -1;
            //采集点分类
            for (SampleDotByClassVo sampleDotByClassVo : sampleDotByInputRightFromClass) {
                flIdx++;
                AcctobjInputFlVo fl = new AcctobjInputFlVo();
                fl.setFlId(sampleDotByClassVo.getClassId());
                fl.setFlName(sampleDotByClassVo.getClassName());
                fl.setSn(flIdx + 1);

                List<Costunitsampledot> sampleDotList = sampleDotByClassVo.getDotList();
                if (ObjUtils.notEmpty(sampleDotList)) {
                    List<AcctobjInputmxVo> mxData = new ArrayList<>();
                    int i = 0;
                    //采集点
                    for (Costunitsampledot costunitsampledot : sampleDotList) {
                        AcctobjInputmxVo vo = new AcctobjInputmxVo();

                        vo.setCollectPointId(costunitsampledot.getId());
                        vo.setCollectPoint(costunitsampledot.getName());
                        //实时位号（仪表位号）
                        //vo.setTagNo(costunitsampledot.getTagnumber());
                        vo.setTagNo(costunitsampledot.getDatasource());
                        vo.setSn(i);
                        vo.setInputCompType("textfield");
                        vo.setIsWriteBackInfluxdb(costunitsampledot.getIsWriteBackInfluxdb());

                        mxData.add(vo);

                        //录入类型
                        vo.setInputCompType("textfield"); //默认
                        Integer controlType = costunitsampledot.getControlType();
                        if (controlType != null) {
                            String compType = ControlTypeConverter.convertToString(controlType).toString();
                            if (StringUtils.isNotEmpty(compType)) {
                                vo.setInputCompType(compType);

                                if ("combobox".equals(compType)) {
                                    //下拉框备选数据
                                    String combInitKey = costunitsampledot.getCombInitKey();
                                    String combInitVal = costunitsampledot.getCombInitVal();
                                    JSONArray options = parseComboBoxKeyValue(combInitKey, combInitVal);
                                    vo.setInputOptions(options.toJSONString());
                                }
                            }
                        }

                        //设备维保标识
                        vo.setIsEquipMaintenance(costunitsampledot.getIsEquipmentMaintenance());
                        if (vo.getIsEquipMaintenance() != null && vo.getIsEquipMaintenance() == 1) {
                            vo.setInputCompType("checkbox");
                        }

                        /*if ("checkbox".equals(vo.getInputCompType())) {
                            //默认勾选复选框
                            vo.setCollectPointVal("1");
                        }*/

                        // 填充默认值
                        Integer defaultVal = costunitsampledot.getDefaultVal();
                        vo.setDefaultVal(defaultVal);
                        getCollectionPointDefaultValue(vo);

                        i++;
                    }
                    fl.setMxData(mxData);
                    flData.add(fl);
                }
            }
            inputData.setFlData(flData);
        }

        return inputData;
    }

    /**
     * 获取当前登录人员信息
     * @return
     */
    @Override
    public MobUserVo getCurUserInfo(MobLeanCostingDto param) {
        MobUserVo mobUserInfo = new MobUserVo();
        SysUser user = SysUserHolder.getCurrentUser();

        String orgId = user.getOrgId();
		String userId = user.getId();// 人员ID
		String postId = user.getPostId();
		postId = orgId + "_" + postId;// 机构ID

        //String orgStatus = iCostService.userOrgIsManageOrg(param.getAcctobjId(), orgId);
        String orgStatus = iCostService.userOrgIsManageOrg(param.getFlid(), orgId,userId,postId);

        if ("operate".equals(orgStatus) || "other".equals(orgStatus)) { //操作机构
            long startTime = System.currentTimeMillis();
            //ShiftForeignVo shiftForeignVo = unitItemInfoService.getShiftInfo(param.getAcctobjId(), param.getSearchDate());
            ShiftForeignVo shiftForeignVo = unitItemInfoService.getShiftInfo(param.getFlid(), DateTimeUtils.getNowDateTimeStr());
            long endTime = System.currentTimeMillis();
            System.out.println("获取当班信息时长：" + (endTime - startTime) + " ms");
            log.info("「移动端」当班信息：（{}）\n查询条件：（业务活动ID：{}，查询日期：{}）", JSONObject.toJSONString(shiftForeignVo), param.getFlid(), param.getSearchDate());

            if ("operate".equals(orgStatus) && (shiftForeignVo == null || orgId.equals(shiftForeignVo.getOrgCode()) == false)) { //当前用户不当班
                orgStatus = "other";
            }

            if (shiftForeignVo != null) {
                mobUserInfo.setBcdm(shiftForeignVo.getShiftClassCode());
                mobUserInfo.setBcmc(shiftForeignVo.getShiftClassName());
                mobUserInfo.setTeamId(shiftForeignVo.getOrgCode());
                mobUserInfo.setTeamName(shiftForeignVo.getOrgName());

                //String sbsj = param.getSearchDate() + " " + shiftForeignVo.getSbsj() + ":00";
                //String xbsj = param.getSearchDate() + " " + shiftForeignVo.getXbsj() + ":00";
                String sbsj = shiftForeignVo.getSbsj();
                String xbsj = shiftForeignVo.getXbsj();
                if (xbsj.compareTo(sbsj) < 0) { //跨日
                    Date xbsjDate = DateTimeUtils.parseDateTime(xbsj);
                    xbsjDate = DateTimeUtils.addDays(xbsjDate, 1);
                    xbsj = DateTimeUtils.formatDateTime(xbsjDate);
                }
                mobUserInfo.setSbsj(sbsj);
                mobUserInfo.setXbsj(xbsj);
            }

        }

        mobUserInfo.setOrgStatus(orgStatus);

        return mobUserInfo;
    }

    /**
     * 获取班次列表
     * @param param
     * @return
     */
    @Override
    public List<MobShiftVo> getShiftData(MobLeanCostingDto param) {
        List<MobShiftVo> list = new ArrayList<>();

        List<ShiftForeignVo> shiftList = unitItemInfoService.getShiftList(param.getFlid(), param.getSearchDate());
        if (ObjUtils.notEmpty(shiftList)) {
            for (ShiftForeignVo shiftVo : shiftList) {
                MobShiftVo mobShiftVo = new MobShiftVo();
                mobShiftVo.setBcdm(shiftVo.getShiftClassCode());
                mobShiftVo.setBcmc(shiftVo.getShiftClassName());
                mobShiftVo.setTeamId(shiftVo.getOrgCode());

                //从Redis中获取机构名称
                String orgName = "";
                String orgKey = "SYSTEM:ORG:INFO";
                if (MultiTenantUtils.enalbe()) {// 多租户模式
                    orgKey = orgKey + ":" + MultiTenantUtils.getTenantId();
                }
                Map<String, LinkedHashMap> map = redisUtil.getMap(orgKey);
                if (map != null) {
                    LinkedHashMap orgInfoMap = map.get(shiftVo.getOrgCode());
                    if (orgInfoMap != null) {
                        orgName = (String) orgInfoMap.get("orgname");
                    }
                }
                mobShiftVo.setTeamName(orgName);
                String sbsj = param.getSearchDate() + " " + shiftVo.getSbsj() + ":00";
                String xbsj = param.getSearchDate() + " " + shiftVo.getXbsj() + ":00";
                if (xbsj.compareTo(sbsj) < 0) { //跨日
                    Date xbsjDate = DateTimeUtils.parseDateTime(xbsj);
                    xbsjDate = DateTimeUtils.addDays(xbsjDate, 1);
                    xbsj = DateTimeUtils.formatDateTime(xbsjDate);
                }
                mobShiftVo.setSbsj(sbsj);
                mobShiftVo.setXbsj(xbsj);
                list.add(mobShiftVo);
            }
        }

        return list;
    }

    /**
     * 获取主表录入数据
     * @param param
     * @return
     */
    public List<AcctobjInput> getAcctobjInputData(MobLeanCostingDto param) {
        List<AcctobjInput> list;

        Where where = new Where();
        where.eq(AcctobjInput::getTmused, 1);

        //核算对象ID
        String acctobjId = param.getAcctobjId();
        if (StringUtils.isNotEmpty(acctobjId)) {
            where.eq(AcctobjInput::getAcctobjId, acctobjId);
        }
        //查询日期
        String searchDate = param.getSearchDate();
        if (StringUtils.isNotEmpty(searchDate)) {
            String startDatetimeStr = searchDate + " 00:00:00";
            Date startDatetime = DateTimeUtils.parseDateTime(startDatetimeStr);
            Date endDatetime = DateTimeUtils.addDays(startDatetime, 1);
            where.ge(AcctobjInput::getInputTime, startDatetime);
            where.lt(AcctobjInput::getInputTime, endDatetime);
        }
        //录入批次时间
        String inputDateTime = param.getInputDateTime();
        if (StringUtils.isNotEmpty(inputDateTime)) {
            Date inputDateTimeDt = DateTimeUtils.parseDateTime(inputDateTime);
            where.eq(AcctobjInput::getInputTime, inputDateTimeDt);
        }

        Order order = new Order();
        order.orderByAsc(AcctobjInput::getInputTime);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), AcctobjInput.class, where, order);
        } else { //普通模式
            list = entityService.queryList(AcctobjInput.class, where, order);
        }

        return list;
    }

    /**
     * 获取子表录入数据
     * @param param
     * @return
     */
    public List<AcctobjInputmx> getAcctobjInputmxData(MobLeanCostingDto param) {
        List<AcctobjInputmx> list;

        Where where = new Where();
        where.eq(AcctobjInputmx::getTmused, 1);

        //核算对象ID
        String acctobjId = param.getAcctobjId();
        if (StringUtils.isNotEmpty(acctobjId)) {
            where.eq(AcctobjInput::getAcctobjId, acctobjId);
        }
        //查询日期
        String searchDate = param.getSearchDate();
        if (StringUtils.isNotEmpty(searchDate)) {
            String startDatetimeStr = searchDate + " 00:00:00";
            Date startDatetime = DateTimeUtils.parseDateTime(startDatetimeStr);
            Date endDatetime = DateTimeUtils.addDays(startDatetime, 1);
            where.ge(AcctobjInputmx::getInputTime, startDatetime);
            where.lt(AcctobjInputmx::getInputTime, endDatetime);
        }
        //录入批次时间
        String inputDateTime = param.getInputDateTime();
        if (StringUtils.isNotEmpty(inputDateTime)) {
            Date inputDateTimeDt = DateTimeUtils.parseDateTime(inputDateTime);
            where.eq(AcctobjInput::getInputTime, inputDateTimeDt);
        }

        Order order = new Order();
        order.orderByAsc(AcctobjInputmx::getInputTime);
        order.orderByAsc(AcctobjInputmx::getSn);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), AcctobjInputmx.class, where, order);
        } else { //普通模式
            list = entityService.queryList(AcctobjInputmx.class, where, order);
        }

        return list;
    }

    /**
     * 获取采集点分类数据
     * @param param
     * @return
     */
    public List<AcctobjInputFl> getAcctobjInputFlData(MobLeanCostingDto param) {
        List<AcctobjInputFl> list;

        Where where = new Where();
        where.eq(AcctobjInputFl::getTmused, 1);

        //核算对象ID
        String acctobjId = param.getAcctobjId();
        if (StringUtils.isNotEmpty(acctobjId)) {
            where.eq(AcctobjInput::getAcctobjId, acctobjId);
        }
        //查询日期
        String searchDate = param.getSearchDate();
        if (StringUtils.isNotEmpty(searchDate)) {
            String startDatetimeStr = searchDate + " 00:00:00";
            Date startDatetime = DateTimeUtils.parseDateTime(startDatetimeStr);
            Date endDatetime = DateTimeUtils.addDays(startDatetime, 1);
            where.ge(AcctobjInputFl::getInputTime, startDatetime);
            where.lt(AcctobjInputFl::getInputTime, endDatetime);
        }
        //录入批次时间
        String inputDateTime = param.getInputDateTime();
        if (StringUtils.isNotEmpty(inputDateTime)) {
            Date inputDateTimeDt = DateTimeUtils.parseDateTime(inputDateTime);
            where.eq(AcctobjInput::getInputTime, inputDateTimeDt);
        }

        Order order = new Order();
        order.orderByAsc(AcctobjInputFl::getInputTime);
        order.orderByAsc(AcctobjInputFl::getSn);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), AcctobjInputFl.class, where, order);
        } else { //普通模式
            list = entityService.queryList(AcctobjInputFl.class, where, order);
        }

        return list;
    }

    /**
     * 保存录入数据
     * @param param
     * @return
     */
    @Override
    @Transactional
    public String saveInputData(MobLeanCostingDto param) {
        SysUser user = SysUserHolder.getCurrentUser();

        AcctobjInputVo inputDataVo = param.getInputData();
        if (inputDataVo == null) {
            return "待保存的时间批次数据无效";
        }
        AcctobjInput inputData = ObjUtils.copyTo(inputDataVo, AcctobjInput.class);

        //String BaId = inputDataVo.getBaId();
        String taskId = inputDataVo.getTaskId();

        List<AcctobjInputFlVo> flData = inputDataVo.getFlData();
        if (ObjUtils.isEmpty(flData)) {
            return "待保存的采集点数据无效";
        }

        List<AcctobjInput> acctobjInputInsertList = new ArrayList<>();
        List<AcctobjInput> acctobjInputUpdateList = new ArrayList<>();
        List<AcctobjInputFl> acctobjInputFlInsertList = new ArrayList<>();
        List<AcctobjInputFl> acctobjInputFlUpdateList = new ArrayList<>();
        List<AcctobjInputmx> acctobjInputmxInsertList = new ArrayList<>();
        List<AcctobjInputmx> acctobjInputmxUpdateList = new ArrayList<>();

        Integer rowFlag = inputDataVo.getRowFlag();
        if (rowFlag == 1) {
            inputData.setId(TMUID.getUID());
            inputData.setTmused(1);
            acctobjInputInsertList.add(inputData);
        } else if (rowFlag == -1) {
            inputData.setTmused(0);
            acctobjInputUpdateList.add(inputData);
        } else {
            inputData.setUpdateTime(DateTimeUtils.getNowDate());
            inputData.setUpdateBy(user.getNickName());
            acctobjInputUpdateList.add(inputData);
        }

        for (AcctobjInputFlVo flVo : flData) {
            List<AcctobjInputmxVo> mxData = flVo.getMxData();

            if (rowFlag == 1) { //添加，使用主记录标识判断（整体添加、修改、删除）
                AcctobjInputFl fl = ObjUtils.copyTo(flVo, AcctobjInputFl.class);
                fl.setIptId(inputData.getId());
                fl.setAcctobjId(inputData.getAcctobjId());
                fl.setBaId(inputData.getBaId()); //业务活动ID
                fl.setId(TMUID.getUID());
                fl.setTmused(1);
                fl.setInputTime(inputData.getInputTime());
                acctobjInputFlInsertList.add(fl);

                for (AcctobjInputmxVo mxVo : mxData) {
                    AcctobjInputmx mx = ObjUtils.copyTo(mxVo, AcctobjInputmx.class);
                    mx.setIptId(inputData.getId());
                    mx.setAcctobjId(inputData.getAcctobjId());
                    mx.setBaId(inputData.getBaId()); //业务活动ID
                    mx.setId(TMUID.getUID());
                    mx.setTmused(1);
                    mx.setInputTime(inputData.getInputTime());
                    mx.setIptFlId(fl.getId());
                    acctobjInputmxInsertList.add(mx);
                }
            } else if (rowFlag == -1) { //删除
                AcctobjInputFl fl = ObjUtils.copyTo(flVo, AcctobjInputFl.class);
                fl.setTmused(0);
                fl.setUpdateBy(user.getId());
                acctobjInputFlUpdateList.add(fl);

                for (AcctobjInputmxVo mxVo : mxData) {
                    AcctobjInputmx mx = ObjUtils.copyTo(mxVo, AcctobjInputmx.class);
                    mx.setTmused(0);
                    mx.setUpdateTime(DateTimeUtils.getNowDate());
                    mx.setUpdateBy(user.getId());
                    acctobjInputmxUpdateList.add(mx);
                }
            } else { //修改
                AcctobjInputFl fl = ObjUtils.copyTo(flVo, AcctobjInputFl.class);
                fl.setUpdateTime(DateTimeUtils.getNowDate());
                fl.setUpdateBy(user.getId());
                acctobjInputFlUpdateList.add(fl);

                for (AcctobjInputmxVo mxVo : mxData) {
                    AcctobjInputmx mx = ObjUtils.copyTo(mxVo, AcctobjInputmx.class);
                    mx.setUpdateTime(DateTimeUtils.getNowDate());
                    mx.setUpdateBy(user.getId());
                    acctobjInputmxUpdateList.add(mx);
                }
            }
        }

        if (ObjUtils.notEmpty(acctobjInputInsertList)) {
            entityService.rawInsertBatch(acctobjInputInsertList);
        }
        if (ObjUtils.notEmpty(acctobjInputUpdateList)) {
            entityService.updateBatch(acctobjInputUpdateList);
        }
        if (ObjUtils.notEmpty(acctobjInputFlInsertList)) {
            entityService.rawInsertBatch(acctobjInputFlInsertList);
        }
        if (ObjUtils.notEmpty(acctobjInputFlUpdateList)) {
            entityService.updateBatch(acctobjInputFlUpdateList);
        }
        if (ObjUtils.notEmpty(acctobjInputmxInsertList)) {
            entityService.rawInsertBatch(acctobjInputmxInsertList);
        }
        if (ObjUtils.notEmpty(acctobjInputmxUpdateList)) {
            entityService.updateBatch(acctobjInputmxUpdateList);
        }

        // 同步数据给 R3DB 服务（最终更新实时数据库）
        transDataToR3DB(acctobjInputmxInsertList, acctobjInputmxUpdateList, null);

        //更新活动录入关系数据
        JoblistInputMappingDto joblistInputMappingDto = new JoblistInputMappingDto();
        joblistInputMappingDto.setJobType(JoblistConstant.JOB_TYPE_ROUTINE_WORK);
        joblistInputMappingDto.setMapType(JoblistConstant.MAP_TYPE_COLLECTION_POINT);
        joblistInputMappingDto.setMasterId(taskId);
        joblistInputMappingDto.setSlaveId(inputData.getId());
        joblistInputMappingDto.setRowFlag(rowFlag);

        Boolean ifConfirm = param.getIfConfirm();
        if (ifConfirm == null) {
            ifConfirm = false;
        }
        joblistInputService.updateInputMappingData(Arrays.asList(joblistInputMappingDto), ifConfirm);

        return "";
    }

    @Override
    public boolean syncDataWithAccount(AccountSaveDto source) throws MobileException {
        boolean ok = true;
        List<AcctobjInputVo> acctObjInputList = source.getCollectionPointInputData();

        if (ObjUtils.isEmpty(acctObjInputList)) {
            throw new MobileException("台账数据无效，采集点数据同步失败");
        }

        try {
            // 通过主记录统计业务活动 ID、核算对象 ID、录入批次时间
            LinkedHashMap<String, List<Date>> baIdAcctobjIdInputTimeMap = getBaIdAcctobjIdInputTimeMap(acctObjInputList);

            // 查询数据库获取历史录入数据
            List<AcctobjInputVo> historyInputData = getInputDataByBaIdAcctobjIdInputTimeMap(baIdAcctobjIdInputTimeMap);
            //LinkedHashMap<String, List<Date>> hisBaIdAcctobjIdInputTimeMap = getBaIdAcctobjIdInputTimeMap(historyInputData);
            LinkedHashMap<String, AcctobjInputVo> baIdAcctobjIdInputTimeDataMap = getBaIdAcctobjIdInputTimeDataMap(historyInputData);

            // 对比台账录入保存数据与采集点历史录入数据，判断新增、修改、删除
            AcctobjInputDbData dbData = compareInputDataWithAccount(acctObjInputList, baIdAcctobjIdInputTimeDataMap);

            // 保存对比后的数据结果（CRUD）
            try {
                ok = saveInputDataToDb(dbData);
            } catch (Exception e) {
                log.error("向数据库写入采集点数据失败", e);
                return false;
            }

            // 同步活动关系数据
            transDataToR3DB(dbData.getInsertAcctobjInputmxList(), dbData.getUpdateAcctobjInputmxList(), dbData.getDeleteAcctobjInputmxList());

            // 更新关系表数据
            updateMappingData(dbData, source);

        } catch (Exception e) {
            throw new MobileException("采集点数据同步产生内部错误", e);
        }

        return ok;
    }

    @Override
    public List<String> getOnTimeData(MobLeanCostingDto param) {
        List<String> unitOntimeList = applyConfSrv.getUnitOntimeList(param.getSbsj(), param.getXbsj());
        if (ObjUtils.isEmpty(unitOntimeList)) {
            unitOntimeList = new ArrayList<>();
        }

        return unitOntimeList;
    }

    /**
     * 更新关系表数据
     * @param dbData
     */
    private void updateMappingData(AcctobjInputDbData dbData, AccountSaveDto source) throws MobileException {
        //String formDataId = source.getFormDataId();
        //if (StringUtils.isEmpty(formDataId)) {
        //    throw new MobileException("更新关系表数据失败，行云流表单ID无效");
        //}

        List<JoblistInputMappingDto> inputMappingList = new ArrayList<>();

        //注释于 2024-8-9 15:32:32，主活动 ID 与行云流表单 ID 绑定由表单保存事件端完成，不在台账保存事件端处理
        //主活动 ID 与行云流表单数据 ID 关联关系数据，使用于 PC 端录入页面二次查询与进入行云流表单交互窗口组件加载历史数据
        //JoblistInputMappingDto joblistInputMappingDto = new JoblistInputMappingDto();
        //joblistInputMappingDto.setJobType(JoblistConstant.JOB_TYPE_ROUTINE_WORK);
        //joblistInputMappingDto.setMapType(JoblistConstant.MAP_TYPE_COLLECTION_POINT);
        //joblistInputMappingDto.setMasterId(source.getTaskId());
        //joblistInputMappingDto.setSlaveId(formDataId);
        ////joblistInputMappingDto.setRowFlag(rowFlag);
        //inputMappingList.add(joblistInputMappingDto);

        //子活动 ID 与行云流表单数据 ID 关联关系数据，使用于 APP 端录入页面二次查询与进入采集点录入页面加载历史数据
        dbData.getInsertAcctobjInputList().forEach(acctobjInput -> {
            String taskId = acctobjInput.getTaskId();
            String id = acctobjInput.getId();
            JoblistInputMappingDto mappingDto = new JoblistInputMappingDto();
            mappingDto.setJobType(JoblistConstant.JOB_TYPE_ROUTINE_WORK);
            mappingDto.setMapType(JoblistConstant.MAP_TYPE_COLLECTION_POINT);
            mappingDto.setMasterId(taskId);
            mappingDto.setSlaveId(id);
            inputMappingList.add(mappingDto);
        });
        dbData.getUpdateAcctobjInputList().forEach(acctobjInput -> {
            String taskId = acctobjInput.getTaskId();
            String id = acctobjInput.getId();
            JoblistInputMappingDto mappingDto = new JoblistInputMappingDto();
            mappingDto.setJobType(JoblistConstant.JOB_TYPE_ROUTINE_WORK);
            mappingDto.setMapType(JoblistConstant.MAP_TYPE_COLLECTION_POINT);
            mappingDto.setMasterId(taskId);
            mappingDto.setSlaveId(id);
            inputMappingList.add(mappingDto);
        });

        joblistInputService.updateInputMappingData(inputMappingList, false);
    }

    @Transactional
    public boolean saveInputDataToDb(AcctobjInputDbData dbData) {
        boolean ok = true;

        List<AcctobjInput> insertAcctobjInputList = dbData.getInsertAcctobjInputList();
        List<AcctobjInput> updateAcctobjInputList = dbData.getUpdateAcctobjInputList();
        List<AcctobjInput> deleteAcctobjInputList = dbData.getDeleteAcctobjInputList();
        List<AcctobjInputFl> insertAcctobjInputFlList = dbData.getInsertAcctobjInputFlList();
        List<AcctobjInputFl> updateAcctobjInputFlList = dbData.getUpdateAcctobjInputFlList();
        List<AcctobjInputFl> deleteAcctobjInputFlList = dbData.getDeleteAcctobjInputFlList();
        List<AcctobjInputmx> insertAcctobjInputmxList = dbData.getInsertAcctobjInputmxList();
        List<AcctobjInputmx> updateAcctobjInputmxList = dbData.getUpdateAcctobjInputmxList();
        List<AcctobjInputmx> deleteAcctobjInputmxList = dbData.getDeleteAcctobjInputmxList();

        if (ObjUtils.notEmpty(deleteAcctobjInputList)) {
            entityService.deleteByIdBatch(deleteAcctobjInputList);
        }
        if (ObjUtils.notEmpty(insertAcctobjInputList)) {
            entityService.insertBatch(insertAcctobjInputList);
        }
        if (ObjUtils.notEmpty(updateAcctobjInputList)) {
            entityService.updateBatch(updateAcctobjInputList);
        }
        if (ObjUtils.notEmpty(deleteAcctobjInputFlList)) {
            entityService.deleteByIdBatch(deleteAcctobjInputFlList);
        }
        if (ObjUtils.notEmpty(insertAcctobjInputFlList)) {
            entityService.insertBatch(insertAcctobjInputFlList);
        }
        if (ObjUtils.notEmpty(updateAcctobjInputFlList)) {
            entityService.updateBatch(updateAcctobjInputFlList);
        }
        if (ObjUtils.notEmpty(deleteAcctobjInputmxList)) {
            entityService.deleteByIdBatch(deleteAcctobjInputmxList);
        }
        if (ObjUtils.notEmpty(insertAcctobjInputmxList)) {
            entityService.insertBatch(insertAcctobjInputmxList);
        }
        if (ObjUtils.notEmpty(updateAcctobjInputmxList)) {
            entityService.updateBatch(updateAcctobjInputmxList);
        }

        return ok;
    }

    /**
     * 比对 app 端采集点已保存数据与 pc 端电子台账保存数据
     * @return
     */
    private AcctobjInputDbData compareInputDataWithAccount(List<AcctobjInputVo> acctObjInputList, LinkedHashMap<String, AcctobjInputVo> baIdAcctobjIdInputTimeDataMap) {
        AcctobjInputDbData res = new AcctobjInputDbData();
        SysUser user = SysUserHolder.getCurrentUser();

        for (AcctobjInputVo acctobjInputVo : acctObjInputList) {
            //通过核算对象、关键设备、录入时间定位 app 端采集点录入主数据
            String baId = acctobjInputVo.getBaId(); //核算对象 ID  台账id
            String acctobjId = acctobjInputVo.getAcctobjId(); //关键设备 ID

            Date inputTime = acctobjInputVo.getInputTime(); //录入时间
            String inputTimeStr;
            try {
                inputTimeStr = DateTimeUtils.formatDateTime(inputTime);
            } catch (Exception e) {
                inputTimeStr = "";
            }
            if ("".equals(inputTimeStr)) {
                log.warn("采集点数据同步，采集点数据录入时间格式不正确，忽略该条数据，核算对象 ID：{}，关键设备 ID：{}", baId, acctobjId);
                continue;
            }

            String key = String.format("%s::%s::%s::%s", acctobjInputVo.getBaId(), acctobjInputVo.getAcctobjId(), inputTimeStr, acctobjInputVo.getTeamId());
            AcctobjInputVo hisAcctobjInputVo = baIdAcctobjIdInputTimeDataMap.get(key);
            if (hisAcctobjInputVo == null) { //未找到 app 端采集点历史录入主数据
                Integer rowFlag = Optional.ofNullable(acctobjInputVo.getRowFlag()).orElse(0);
                if (rowFlag == 1 || rowFlag == 0) { //电子台账新增或修改数据（pc 端电子台账表格按数据行进行操作，对应 app 端采集点的一个表单的数据）
                    //pc 端电子台账新增数据（app 端未录入此数据），制作新采集点录入数据保存到数据库中
                    List<AcctobjInputFlVo> flData = acctobjInputVo.getFlData();
                    if (ObjUtils.isEmpty(flData)) {
                        log.warn("采集点数据同步，采集点分类数据为空，忽略该条数据，核算对象 ID：{}，关键设备 ID：{}", baId, acctobjId);
                        continue;
                    }

                    AcctobjInput acctobjInput = ObjUtils.copyTo(acctobjInputVo, AcctobjInput.class);
                    acctobjInput.setId(TMUID.getUID());
                    acctobjInput.setTmused(1);
                    acctobjInput.setTeamId(user.getOrgId());
                    acctobjInput.setTeamName(user.getOrgName());

                    List<AcctobjInputFl> insertAcctobjInputFlList = new ArrayList<>();

                    int flIdx = -1;

                    for (AcctobjInputFlVo flVo : flData) {
                        flIdx++;
                        flVo.setIptId(acctobjInput.getId());
                        flVo.setBaId(acctobjInput.getBaId());
                        flVo.setAcctobjId(acctobjInput.getAcctobjId());
                        flVo.setInputTime(acctobjInputVo.getInputTime());
                        flVo.setTmused(1);
                        flVo.setId(TMUID.getUID());
                        flVo.setSn(flIdx + 1);

                        List<AcctobjInputmxVo> mxDataVoList = flVo.getMxData();

                        List<AcctobjInputmx> mxDataList = ObjUtils.convertToList(AcctobjInputmx.class, mxDataVoList);
                        if (ObjUtils.isEmpty(flData)) {
                            log.warn("采集点数据同步，采集点明细数据为空，忽略该条数据，核算对象 ID：{}，关键设备 ID：{}", baId, acctobjId);
                            continue;
                        }

                        int mxIdx = -1;
                        for (AcctobjInputmx inputmx : mxDataList) {
                            mxIdx++;
                            inputmx.setId(TMUID.getUID());
                            inputmx.setIptFlId(flVo.getId());
                            inputmx.setIptId(acctobjInput.getId());
                            inputmx.setBaId(acctobjInput.getBaId());
                            inputmx.setJobInputTime(acctobjInputVo.getInputTime());
                            inputmx.setAcctobjId(acctobjInput.getAcctobjId());
                            inputmx.setInputTime(acctobjInputVo.getInputTime());
                            inputmx.setTmused(1);
                            inputmx.setSn(mxIdx + 1);
                        }
                        res.getInsertAcctobjInputmxList().addAll(mxDataList);
                        AcctobjInputFl fl = ObjUtils.copyTo(flVo, AcctobjInputFl.class);
                        insertAcctobjInputFlList.add(fl);
                    }

                    if (ObjUtils.notEmpty(insertAcctobjInputFlList)) {
                        res.getInsertAcctobjInputList().add(acctobjInput);
                        res.getInsertAcctobjInputFlList().addAll(insertAcctobjInputFlList);
                    }
                } else if (rowFlag == -1) { //电子台账删除数据（pc 端电子台账表格按数据行进行操作，对应 app 端采集点的一个表单的数据）
                    //删除数据直接忽略，无需处理
                    continue;
                } else {
                    log.warn("采集点数据同步，采集点数据主数据行标识不正确，忽略该条数据，核算对象 ID：{}，关键设备 ID：{}", baId, acctobjId);
                    continue;
                }
            } else { //有历史录入主数据
                Integer rowFlag = Optional.ofNullable(acctobjInputVo.getRowFlag()).orElse(0);
                if (rowFlag == 1 || rowFlag == 0) { //电子台账新增或修改数据（pc 端电子台账表格按数据行进行操作，对应 app 端采集点的一个表单的数据）
                    AcctobjInput hisAcctobjInput = ObjUtils.copyTo(hisAcctobjInputVo, AcctobjInput.class);
                    hisAcctobjInput.setUpdateBy(user.getId());
                    hisAcctobjInput.setUpdateTime(DateTimeUtils.getNowDate());

                    //采集点分类数据
                    List<AcctobjInputFlVo> flVoData = acctobjInputVo.getFlData();
                    if (ObjUtils.isEmpty(flVoData)) {
                        log.warn("采集点数据同步，采集点分类数据为空，忽略该条数据，核算对象 ID：{}，关键设备 ID：{}", baId, acctobjId);
                        continue;
                    }
                    List<AcctobjInputFlVo> hisFlData = hisAcctobjInputVo.getFlData();
                    if (ObjUtils.isEmpty(hisFlData)) {
                        hisFlData = new ArrayList<>();
                    }
                    int flIdx = -1;
                    LinkedHashMap<String, AcctobjInputFlVo> flIdMap = hisFlData.stream().collect(Collectors.toMap(AcctobjInputFlVo::getFlId, o -> o, (a, b) -> a, LinkedHashMap::new));
                    for (AcctobjInputFlVo flVo : flVoData) {
                        flIdx++;
                        String flId = flVo.getFlId();
                        AcctobjInputFlVo hisFlVo = flIdMap.get(flId);
                        if (hisFlVo == null) { //电子台账数据在 app 端采集点历史数据中找不到
                            flVo.setId(TMUID.getUID());
                            hisFlVo = flVo;

                            //新增数据
                            AcctobjInputFl newFl = ObjUtils.copyTo(flVo, AcctobjInputFl.class);
                            newFl.setIptId(hisAcctobjInput.getId());
                            newFl.setBaId(hisAcctobjInput.getBaId());
                            newFl.setAcctobjId(hisAcctobjInput.getAcctobjId());
                            newFl.setInputTime(hisAcctobjInput.getInputTime());
                            newFl.setTmused(1);
                            newFl.setSn(flIdx + 1);

                            res.getInsertAcctobjInputFlList().add(newFl);
                        } else {
                            //更新数据
                            AcctobjInputFl hisFl = ObjUtils.copyTo(hisFlVo, AcctobjInputFl.class);
                            hisFl.setUpdateBy(user.getId());
                            hisFl.setUpdateTime(DateTimeUtils.getNowDate());
                            hisFl.setSn(flIdx + 1);

                            res.getUpdateAcctobjInputFlList().add(hisFl);
                        }

                        //采集点明细数据
                        List<AcctobjInputmxVo> mxDataVoList = flVo.getMxData();
                        if (ObjUtils.isEmpty(mxDataVoList)) {
                            log.warn("采集点数据同步，采集点明细数据为空，忽略该条数据，核算对象 ID：{}，关键设备 ID：{}，采集点分类 ID：{}", baId, acctobjId, flId);
                            continue;
                        }
                        List<AcctobjInputmxVo> hisMxData = hisFlVo.getMxData();
                        if (ObjUtils.isEmpty(hisMxData)) {
                            hisMxData = new ArrayList<>();
                        }
                        LinkedHashMap<String, AcctobjInputmxVo> collectPointIdMap = hisMxData.stream().collect(Collectors.toMap(AcctobjInputmxVo::getCollectPointId, o -> o, (a, b) -> a, LinkedHashMap::new));

                        int mxIdx = -1;
                        for (AcctobjInputmxVo acctobjInputmxVo : mxDataVoList) {
                            mxIdx++;
                            String collectPointId = acctobjInputmxVo.getCollectPointId();

                            AcctobjInputmxVo mxVo = collectPointIdMap.get(collectPointId);
                            if (mxVo == null) { //电子台账数据在 app 端采集点历史数据中找不到
                                //新增数据
                                AcctobjInputmx newMx = ObjUtils.copyTo(acctobjInputmxVo, AcctobjInputmx.class);
                                newMx.setId(TMUID.getUID());
                                newMx.setIptFlId(hisFlVo.getId());
                                newMx.setIptId(hisAcctobjInput.getId());
                                newMx.setBaId(hisAcctobjInput.getBaId());
                                newMx.setAcctobjId(hisAcctobjInput.getAcctobjId());
                                newMx.setJobInputTime(hisAcctobjInput.getJobInputTime());
                                newMx.setInputTime(hisAcctobjInput.getInputTime());
                                newMx.setTmused(1);
                                newMx.setSn(mxIdx + 1);

                                res.getInsertAcctobjInputmxList().add(newMx);
                            } else { //电子台账数据在 app 端采集点历史数据中存在
                                //更新数据
                                AcctobjInputmx hisMx = ObjUtils.copyTo(mxVo, AcctobjInputmx.class);
                                hisMx.setUpdateBy(user.getId());
                                hisMx.setUpdateTime(DateTimeUtils.getNowDate());
                                hisMx.setCollectPointVal(acctobjInputmxVo.getCollectPointVal());
                                hisMx.setCollectPointText(acctobjInputmxVo.getCollectPointText());
                                hisMx.setSn(mxIdx + 1);

                                res.getUpdateAcctobjInputmxList().add(hisMx);
                            }
                        }
                    }
                    res.getUpdateAcctobjInputList().add(hisAcctobjInput);
                } else if (rowFlag == -1) { //电子台账删除数据（pc 端电子台账表格按数据行进行操作，对应 app 端采集点的一个表单的数据）
                    //删除历史数据（打标识，假删除）
                    AcctobjInput hisAcctobjInput = ObjUtils.copyTo(hisAcctobjInputVo, AcctobjInput.class);
                    hisAcctobjInput.setTmused(0);
                    hisAcctobjInput.setUpdateBy(user.getId());
                    hisAcctobjInput.setUpdateTime(DateTimeUtils.getNowDate());

                    List<AcctobjInputFlVo> hisFlData = hisAcctobjInputVo.getFlData();
                    for (AcctobjInputFlVo hisFl : hisFlData) {
                        hisFl.setTmused(0);
                        hisFl.setUpdateBy(user.getId());
                        hisFl.setUpdateTime(DateTimeUtils.getNowDate());

                        List<AcctobjInputmxVo> hisMxData = hisFl.getMxData();
                        for (AcctobjInputmxVo hisMx : hisMxData) {
                            hisMx.setTmused(0);
                            hisMx.setUpdateBy(user.getId());
                            hisMx.setUpdateTime(DateTimeUtils.getNowDate());
                        }
                        res.getUpdateAcctobjInputmxList().addAll(hisMxData);
                    }

                    res.getUpdateAcctobjInputList().add(hisAcctobjInput);
                    res.getUpdateAcctobjInputFlList().addAll(hisFlData);
                } else {
                    log.warn("采集点数据同步，采集点数据主数据行标识不正确，忽略该条数据，核算对象 ID：{}，关键设备 ID：{}", baId, acctobjId);
                }
            }
        }

        return res;
    }

    private LinkedHashMap<String, List<Date>> getBaIdAcctobjIdInputTimeMap(List<AcctobjInputVo> acctobjInputList) {
        return acctobjInputList.stream().collect(
                Collectors.toMap(o -> {
                            return String.format("%s::%s::%s", o.getBaId(), o.getAcctobjId(), o.getTeamId());
                        },
                        o -> Lists.newArrayList(o.getInputTime()),
                        (List<Date> oldValueList, List<Date> newValueList) ->
                        {
                            oldValueList.addAll(newValueList);
                            return oldValueList;
                        }, LinkedHashMap::new));
    }

    private LinkedHashMap<String, AcctobjInputVo> getBaIdAcctobjIdInputTimeDataMap(List<AcctobjInputVo> acctobjInputList) {
        return acctobjInputList.stream().collect(
                Collectors.toMap(o -> {
                            String inputTimeStr;
                            try {
                                inputTimeStr = DateTimeUtils.formatDateTime(o.getInputTime());
                            } catch (Exception e) {
                                inputTimeStr = "";
                            }
                            return String.format("%s::%s::%s::%s", o.getBaId(), o.getAcctobjId(), inputTimeStr, o.getTeamId());
                        },
                        o -> o,
                        (AcctobjInputVo oldValue, AcctobjInputVo newValue) ->
                                newValue, LinkedHashMap::new));
    }

    private List<AcctobjInputVo> getInputDataByBaIdAcctobjIdInputTimeMap(LinkedHashMap<String, List<Date>> baIdAcctobjIdInputTimeMap) {
        List<AcctobjInputVo> res = new ArrayList<>();

        Where where = new Where();
        where.eq(AcctobjInput::getTmused, 1);

        int index = -1;

        for (Map.Entry<String, List<Date>> entry : baIdAcctobjIdInputTimeMap.entrySet()) {
            index++;

            String key = entry.getKey();
            List<Date> values = entry.getValue();
            String[] idArr = key.split("::");
            String baId = idArr[0];
            String acctobjId = idArr[1];
            String teamId = idArr[2]; //机构编码

            if (index > 0) {
                where.or();
            }

            where.eq(AcctobjInput::getBaId, baId);
            where.eq(AcctobjInput::getAcctobjId, acctobjId);
            where.in(AcctobjInput::getInputTime, values.toArray());
            where.eq(AcctobjInput::getTeamId, teamId); //在辽化现场不同机构使用相同台账模型时，需要使用机构编码进行数据隔离
        }

        //获取历史主数据
        List<AcctobjInput> acctobjInputData = entityService.queryData(AcctobjInput.class, where, null, null);
        if (ObjUtils.isEmpty(acctobjInputData)) {
            return res;
        }

        //获取主数据 ID 数组
        res = ObjUtils.convertToList(AcctobjInputVo.class, acctobjInputData);
        List<String> mainDataIds = res.stream().map(o -> o.getId()).collect(Collectors.toList());

        //通过主数据 ID 查询采集点分类数据
        where = new Where();
        where.eq(AcctobjInputFl::getTmused, 1);
        where.in(AcctobjInputFl::getIptId, mainDataIds.toArray());

        List<AcctobjInputFl> acctobjInputFlData = entityService.queryData(AcctobjInputFl.class, where, null, null);
        List<String> iptFlIds = acctobjInputFlData.stream().map(o -> o.getId()).collect(Collectors.toList());

        //通过采集点分类数据 ID 查询明细数据
        where = new Where();
        where.eq(AcctobjInputmx::getTmused, 1);
        where.in(AcctobjInputmx::getIptFlId, iptFlIds.toArray());

        List<AcctobjInputmx> acctobjInputmxData = entityService.queryData(AcctobjInputmx.class, where, null, null);

        //将采集点分类数据按照主数据 ID 进行分组
        LinkedHashMap<String, List<AcctobjInputFl>> acctobjInputFlMap = acctobjInputFlData.stream().collect(
                Collectors.toMap(o -> o.getIptId(),
                        o -> Lists.newArrayList(o),
                        (List<AcctobjInputFl> oldValueList, List<AcctobjInputFl> newValueList) ->
                        {
                            oldValueList.addAll(newValueList);
                            return oldValueList;
                        }, LinkedHashMap::new));

        //将明细数据按照采集点分类 ID 进行分组
        LinkedHashMap<String, List<AcctobjInputmx>> acctobjInputmxMap = acctobjInputmxData.stream().collect(
                Collectors.toMap(o -> o.getIptFlId(),
                        o -> Lists.newArrayList(o),
                        (List<AcctobjInputmx> oldValueList, List<AcctobjInputmx> newValueList) ->
                        {
                            oldValueList.addAll(newValueList);
                            return oldValueList;
                        }, LinkedHashMap::new));

        //数据绑定：主数据 > 采集点分类 > 明细数据
        for (AcctobjInputVo main : res) {
            String id = main.getId();

            List<AcctobjInputFl> flList = acctobjInputFlMap.get(id);
            if (ObjUtils.notEmpty(flList)) {
                List<AcctobjInputFlVo> flVoList = ObjUtils.convertToList(AcctobjInputFlVo.class, flList);

                for (AcctobjInputFlVo flVo : flVoList) {
                    List<AcctobjInputmx> mxList = acctobjInputmxMap.get(flVo.getId());
                    if (ObjUtils.notEmpty(mxList)) {
                        List<AcctobjInputmxVo> mxVoList = ObjUtils.convertToList(AcctobjInputmxVo.class, mxList);
                        flVo.setMxData(mxVoList);
                    }
                }

                main.setFlData(flVoList);
            }
        }

        return res;
    }

    /**
     * 获取已录入数据
     * @param param
     * @return
     */
    @Override
    public List<AcctobjInputVo> getInputData(MobLeanCostingDto param) {
        List<AcctobjInputVo> list = new ArrayList<>();

        List<AcctobjInput> inputList = getAcctobjInputData(param);
        if (ObjUtils.isEmpty(inputList)) {
            return list;
        }
        List<AcctobjInputFl> inputFlList = getAcctobjInputFlData(param);
        if (ObjUtils.isEmpty(inputFlList)) {
            return list;
        }
        List<AcctobjInputmx> inputmxList = getAcctobjInputmxData(param);
        if (ObjUtils.isEmpty(inputmxList)) {
            return list;
        }

        //Map：主数据ID -> 采集点分类记录列表
        LinkedHashMap<String, List<AcctobjInputFl>> inputFlMap = inputFlList.stream().collect(
                Collectors.toMap(x -> x.getIptId(),
                        obj -> Lists.newArrayList(obj),
                        (List<AcctobjInputFl> oldValueList, List<AcctobjInputFl> newValueList) ->
                        {
                            oldValueList.addAll(newValueList);
                            return oldValueList;
                        }, LinkedHashMap::new));

        //Map：采集点分类数据ID -> 采集点记录列表
        LinkedHashMap<String, List<AcctobjInputmx>> inputmxMap = inputmxList.stream().collect(
                Collectors.toMap(x -> x.getIptFlId(),
                        obj -> Lists.newArrayList(obj),
                        (List<AcctobjInputmx> oldValueList, List<AcctobjInputmx> newValueList) ->
                        {
                            oldValueList.addAll(newValueList);
                            return oldValueList;
                        }, LinkedHashMap::new));

        List<AcctobjInputVo> inputVoList = ObjUtils.convertToList(AcctobjInputVo.class, inputList);
        for (AcctobjInputVo inputVo : inputVoList) {
            String iptId = inputVo.getId();
            List<AcctobjInputFl> iptFlList = inputFlMap.get(iptId);
            if (ObjUtils.notEmpty(iptFlList)) {
                List<AcctobjInputFlVo> inputFlVoList = ObjUtils.convertToList(AcctobjInputFlVo.class, iptFlList);
                inputVo.setFlData(inputFlVoList);
                for (AcctobjInputFlVo inputFlVo : inputFlVoList) {
                    String flId = inputFlVo.getId();
                    List<AcctobjInputmx> iptmxList = inputmxMap.get(flId);
                    if (ObjUtils.notEmpty(iptmxList)) {
                        List<AcctobjInputmxVo> inputmxVoList = ObjUtils.convertToList(AcctobjInputmxVo.class, iptmxList);
                        inputFlVo.setMxData(inputmxVoList);
                    }
                }
            }
        }

        list.addAll(inputVoList);

        return list;
    }

    /**
     * 同步数据至 R3DB 服务端
     * @param acctobjInputmxInsertList
     * @param acctobjInputmxUpdateList
     * @param acctobjInputmxDeleteList
     */
    public void transDataToR3DB(List<AcctobjInputmx> acctobjInputmxInsertList, List<AcctobjInputmx> acctobjInputmxUpdateList, List<AcctobjInputmx> acctobjInputmxDeleteList) {
        try {
            String host = ""; //格式：127.0.0.1:8890/tm4main

            String r3dbCfg = sysConfigSrv.getSysConfig("r3db_cfg");
            if (StringUtils.isEmpty(r3dbCfg)) {
                log.error("移动端录入数据保存 - R3DB配置无效");
                return;
            }
            JSONObject r3dbCfgObj = JSONObject.parseObject(r3dbCfg);
            if (!r3dbCfgObj.containsKey("value")) {
                log.error("移动端录入数据保存 - R3DB配置无效（无关键字：value）");
                return;
            }
            JSONObject valueObj = r3dbCfgObj.getJSONObject("value");
            //if (!valueObj.containsKey("server")) {
            //    log.error("移动端录入数据保存 - R3DB系统服务地址配置无效（无关键字：server）");
            //    return;
            //}
            host = valueObj.getString("server");
            if (StringUtils.isEmpty(host)) {
                log.error("移动端录入数据保存 - R3DB系统服务地址配置无效（空数据关键字：server）");
                return;
            }
            String authId = Optional.ofNullable(valueObj.getString("authId")).orElse("");
            //rdbid = valueObj != null && valueObj.containsKey("rdbid") ? valueObj.getString("rdbid") : null;

            if (ObjUtils.notEmpty(acctobjInputmxInsertList)) {
                List<Tag> tagList = new ArrayList<>();

                for (AcctobjInputmx acctobjInputmx : acctobjInputmxInsertList) {
                    //是否回写 influxDB 标识，1=回写，其它=不回写
                    Integer isWriteBackInfluxdb = Optional.ofNullable(acctobjInputmx.getIsWriteBackInfluxdb()).orElse(0);
                    if (isWriteBackInfluxdb != 1) {
                        continue;
                    }

                    String tagCode = acctobjInputmx.getTagNo();
                    if (StringUtils.isEmpty(tagCode)) {
                        continue;
                    }

                    Tag tag = new Tag();
                    List<TagData> datas = new ArrayList<>();
                    TagData data = new TagData();

                    long time = acctobjInputmx.getInputTime().getTime();
                    data.setTime(time);
                    data.setValue(acctobjInputmx.getCollectPointVal());
                    datas.add(data);
                    tag.setDatas(datas);

                    //String tagCode = acctobjInputmx.getCollectPointId();
                    tag.setTagCode(tagCode);

                    tagList.add(tag);
                }

                //批量新增
                rtdbClient.writeTagDatasBatch(host, authId, tagList);
            }

            if (ObjUtils.notEmpty(acctobjInputmxUpdateList)) {
                List<Tag> tagUpdList = new ArrayList<>();
                List<String> tagCodeDelList = new ArrayList<>();
                Date inputDateTime = null;

                for (AcctobjInputmx acctobjInputmx : acctobjInputmxUpdateList) {
                    //是否回写 influxDB 标识，1=回写，其它=不回写
                    Integer isWriteBackInfluxdb = Optional.ofNullable(acctobjInputmx.getIsWriteBackInfluxdb()).orElse(0);
                    if (isWriteBackInfluxdb != 1) {
                        continue;
                    }

                    //String tagCode = acctobjInputmx.getCollectPointId();
                    String tagCode = acctobjInputmx.getTagNo();
                    if (StringUtils.isEmpty(tagCode)) {
                        continue;
                    }

                    if (acctobjInputmx.getTmused() == 0) { //删除
                        inputDateTime = acctobjInputmx.getInputTime();

                        tagCodeDelList.add(tagCode);
                    } else { //修改
                        Tag tag = new Tag();
                        List<TagData> datas = new ArrayList<>();
                        TagData data = new TagData();

                        long time = acctobjInputmx.getInputTime().getTime();
                        data.setTime(time);
                        data.setValue(acctobjInputmx.getCollectPointVal());
                        datas.add(data);
                        tag.setDatas(datas);
                        tag.setTagCode(tagCode);

                        tagUpdList.add(tag);
                    }
                }

                //批量修改
                if (ObjUtils.notEmpty(tagUpdList)) {
                    rtdbClient.writeTagDatasBatch(host, authId, tagUpdList);
                }
                //批量删除
                if (ObjUtils.notEmpty(tagCodeDelList)) {
                    rtdbClient.deleteTagDatas(host, authId, tagCodeDelList, inputDateTime, inputDateTime);
                }
            }

            if (ObjUtils.notEmpty(acctobjInputmxDeleteList)) {
                List<String> tagCodeList = new ArrayList<>();
                Date inputDateTime = null;

                for (AcctobjInputmx acctobjInputmx : acctobjInputmxDeleteList) {
                    //是否回写 influxDB 标识，1=回写，其它=不回写
                    Integer isWriteBackInfluxdb = Optional.ofNullable(acctobjInputmx.getIsWriteBackInfluxdb()).orElse(0);
                    if (isWriteBackInfluxdb != 1) {
                        continue;
                    }

                    String tagCode = acctobjInputmx.getTagNo();
                    if (StringUtils.isEmpty(tagCode)) {
                        continue;
                    }

                    inputDateTime = acctobjInputmx.getInputTime();

                    //String tagCode = acctobjInputmx.getCollectPointId();
                    tagCodeList.add(tagCode);
                }

                //批量删除
                rtdbClient.deleteTagDatas(host, authId, tagCodeList, inputDateTime, inputDateTime);
            }
        } catch (Exception e) {
            log.error("移动端录入数据保存 - R3DB数据同步失败", e);
        }
    }

    /**
     * 获取核算对象分类树数据
     * @param param
     * @return
     */
    @Override
    public List<MobAccountingClass> getAcctClassTree(MobLeanCostingDto param) {
        List<MobAccountingClass> list = new ArrayList<>();

        // 从核算对象服务接口获取分类树数据
        CostDto costDto = new CostDto();
        costDto.setTreeKey(true);
        costDto.setProductiveKey(true);
        costDto.setProductive(1);
        costDto.setIsmobileInput(true);

        List<CostuintVo> costuintVoList = null;

        try {
            long startTime = System.currentTimeMillis();
            costuintVoList = iCostService.getCostuintTreeVo(costDto);
            long endTime = System.currentTimeMillis();
            System.out.println("「移动端」获取核算对象分类树形时长：" + (endTime - startTime) + " ms");
        } catch (Exception e) {
            log.error("「移动端」获取核算对象分类树形数据失败", e);
        }

        if (ObjUtils.isEmpty(costuintVoList)) {
            return list;
        }

        list = getAcctClassTreeChildren(costuintVoList);

        return list;
    }

    /**
     * 递归获取转换后的树形节点数据
     * @param costuintVoList
     * @return
     */
    private List<MobAccountingClass> getAcctClassTreeChildren(List<CostuintVo> costuintVoList) {
        List<MobAccountingClass> list = new ArrayList<>();

        if (ObjUtils.isEmpty(costuintVoList)) {
            return list;
        }

        for (CostuintVo costuintVo : costuintVoList) {
            MobAccountingClass el = new MobAccountingClass();
            el.setFlid(costuintVo.getId());
            el.setFlmc(costuintVo.getName());
            el.setAnalysisTemplateId(costuintVo.getAnalysisTemplate());
            el.setIsOntime(costuintVo.getIsOntime());
            if (ObjUtils.notEmpty(costuintVo.getChildren())) {
                List<MobAccountingClass> children = getAcctClassTreeChildren(costuintVo.getChildren());
                el.setChildren(children);
            }
            list.add(el);
        }

        return list;
    }

    /**
     * 解析下拉框备选数据
     * @param combInitKey
     * @param combInitVal
     * @return
     */
    private JSONArray parseComboBoxKeyValue(String combInitKey, String combInitVal) {
        JSONArray options = new JSONArray();

        if (StringUtils.isEmpty(combInitKey) || StringUtils.isEmpty(combInitVal)) {
            return options;
        }

        try {
            String[] keyArr = combInitKey.split(",");
            String[] valArr = combInitVal.split(",");
            if (keyArr.length != valArr.length) {
                log.error("移动端采集点下拉框备选数据解析失败，键值个数不匹配（键：{}，值：{}）", combInitKey, combInitVal);
                return options;
            }
            for (int i = 0; i < keyArr.length; i++) {
                String key = keyArr[i];
                String val = valArr[i];

                JSONObject op = new JSONObject();
                op.put("value", key);
                op.put("text", val);
                options.add(op);
            }
        } catch (Exception e) {
            log.error("移动端采集点下拉框备选数据解析失败（键：{}，值：{}）", combInitKey, combInitVal);
        }

        return options;
    }

    /**
     * 获取设备状态切换数据
     * @param paramDto 请求参数
     * @return
     */
    @Override
    public List<AcctobjInputmx> getDeviceStatusSwitchData(DeviceStatusSwitchDto paramDto) throws Exception {
        List<AcctobjInputmx> list = new ArrayList<>();

        //设备切换状态时间的采集点ID（固定值）
        final String collectPointId = "ChangeStatusOfEquipment_1";

        //参数检测
        String startDateTime = paramDto.getStartDateTime();
        if (StringUtils.isEmpty(startDateTime) || startDateTime.length() != 19) {
            throw new Exception("开始时间参数无效");
        }
        String endDateTime = paramDto.getEndDateTime();
        if (StringUtils.isEmpty(endDateTime) || endDateTime.length() != 19) {
            throw new Exception("结束时间参数无效");
        }
        List<String> acctobjIds = paramDto.getAcctobjIds();
        if (ObjUtils.isEmpty(acctobjIds)) {
            throw new Exception("核算对象列表参数无效");
        }

        //查询主数据ID集
        Where where = new Where();
        where.eq(AcctobjInputmx::getTmused, 1);
        where.in(AcctobjInputmx::getAcctobjId, acctobjIds.toArray());
        where.eq(AcctobjInputmx::getCollectPointId, collectPointId);
        where.ge(AcctobjInputmx::getCollectPointVal, startDateTime);
        where.le(AcctobjInputmx::getCollectPointVal, endDateTime);

        List<AcctobjInputmx> filterList = null;
        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            filterList = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), AcctobjInputmx.class, where);
        } else { //普通模式
            filterList = entityService.queryList(AcctobjInputmx.class, where);
        }
        if (ObjUtils.isEmpty(filterList)) {
            return list;
        }

        //通过主数据ID集查询采集点数据集
        List<String> iptIdList = filterList.stream().map(b -> b.getIptId()).distinct().collect(Collectors.toList());
        where = new Where();
        where.eq(AcctobjInputmx::getTmused, 1);
        where.in(AcctobjInputmx::getIptId, iptIdList.toArray());
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), AcctobjInputmx.class, where);
        } else { //普通模式
            list = entityService.queryList(AcctobjInputmx.class, where);
        }

        return list;
    }


    /**
     * 图片识别
     * @param image 图片文件
     * @param ocrType 识别类型 general_basic 标准通用 accurate_basic 高精度通用  license_plate车牌号
     * @return
     */
    @Override
    public JSONObject imageOcr(MultipartFile image, String ocrType) {
        //识别文本
        String ocrText = null;
        try {
            List<ImageOcrTextVo> ocrResult = ocrSrv.getImageOcrText(image, ocrType);
            if (StringUtils.isNotEmpty(ocrResult)) {
                ocrText = ocrResult.stream().map(ImageOcrTextVo::getText).collect(Collectors.joining(" "));
            }
        } catch (Exception e) {
            //有错误信息
            throw new RuntimeException(e.getMessage());
        }

        SysFilesInfo filesInfo = fileSrv.saveFiles(image, "mobile");
        String fileId = filesInfo.getId();
        JSONObject result = new JSONObject();
        result.put("text", ocrText);
        result.put("imageId", fileId);
        return result;
    }


    /**
     * 查询缩略图列表
     * @param param
     * @return
     */
    @Override
    public JSONArray getAccountingQueryImgList(MobLeanCostingDto param) {
        if (StringUtils.isAnyEmpty(param.getBaId(), param.getSearchDate())) {
            return null;
        }
        Where where = Where.create();
        where.eq(AcctobjInput::getTmused, 1);
        where.eq(AcctobjInput::getBaId, param.getBaId());
        //查询日期
        String startDatetimeStr = param.getSearchDate() + " 00:00:00";
        Date startDatetime = DateTimeUtils.parseDateTime(startDatetimeStr);
        Date endDatetime = DateTimeUtils.addDays(startDatetime, 1);
        where.ge(AcctobjInput::getInputTime, startDatetime);
        where.lt(AcctobjInput::getInputTime, endDatetime);

        Order order = new Order();
        order.orderByAsc(AcctobjInput::getInputTime);

        //查询录入数据
        List<AcctobjInput> inputList = entityService.queryData(AcctobjInput.class, where, order, null);
        if (StringUtils.isEmpty(inputList)) {
            return null;
        }

        //查询缩略图
        AccountParam accparam = new AccountParam();
        accparam.setUnitid(param.getBaId());
        accparam.setRq(param.getSearchDate());
        List<AccountDataVo> imgList = accountToolsSrv.mobilImgList(accparam);
        if (StringUtils.isEmpty(imgList)) {
            return null;
        }
        //缩略图根据班次分组
        Map<String, AccountDataVo> imgMap = imgList.stream().filter(item -> StringUtils.isNotEmpty(item.getClsno())).collect(Collectors.toMap(AccountDataVo::getClsno, v -> v, (v1, v2) -> v1));
        if (StringUtils.isEmpty(imgMap)) {
            return null;
        }

        //按班次过滤，每个班次只显示一次
        Set<String> bcdmSet = new HashSet<>();
        JSONArray result = new JSONArray();
        inputList.forEach(item -> {
            //有班次并且有图片的才显示
            String bcdm = item.getBcdm();
            if (StringUtils.isNotEmpty(bcdm) && !bcdmSet.contains(bcdm) && imgMap.containsKey(bcdm)) {
                AccountDataVo accountDataVo = imgMap.get(bcdm);
                String imgid = accountDataVo.getImgid();
                String minImg64 = accountDataVo.getMinImg64();
                if (!StringUtils.isAllEmpty(imgid, minImg64)) {
                    JSONObject obj = (JSONObject) JSONObject.toJSON(item);
                    obj.put("imgid", imgid);
                    obj.put("minImg64", minImg64);
                    result.add(obj);
                    bcdmSet.add(bcdm);
                }
            }
        });

        return result;
    }

    @Data
    public class AcctobjInputDbData {
        List<AcctobjInput> insertAcctobjInputList = new ArrayList<>();
        List<AcctobjInput> updateAcctobjInputList = new ArrayList<>();
        List<AcctobjInput> deleteAcctobjInputList = new ArrayList<>();
        List<AcctobjInputFl> insertAcctobjInputFlList = new ArrayList<>();
        List<AcctobjInputFl> updateAcctobjInputFlList = new ArrayList<>();
        List<AcctobjInputFl> deleteAcctobjInputFlList = new ArrayList<>();
        List<AcctobjInputmx> insertAcctobjInputmxList = new ArrayList<>();
        List<AcctobjInputmx> updateAcctobjInputmxList = new ArrayList<>();
        List<AcctobjInputmx> deleteAcctobjInputmxList = new ArrayList<>();
    }

    private void clearInputDataVo(AcctobjInputVo inputData) {
        if (inputData == null) {
            return;
        }
        inputData.setId(null);
        List<AcctobjInputFlVo> flData = inputData.getFlData();
        if (ObjUtils.isEmpty(flData)) {
            return;
        }
        flData.forEach(fl -> {
            fl.setId(null);

            List<AcctobjInputmxVo> mxData = fl.getMxData();
            if (ObjUtils.isEmpty(flData)) {
                return;
            }
            mxData.forEach(mx -> {
                mx.setId(null);
                mx.setCollectPointVal(null);
                mx.setCollectPointText(null);
                mx.setCollectPointImgId(null);
                mx.setEquipMtRemark(null);

                // 填充默认值
                getCollectionPointDefaultValue(mx);
            });
        });
        return;
    }

    /**
     * 获取采集点默认值
     * @param bean
     * @param <T>
     */
    private <T extends AcctobjInputmx> void getCollectionPointDefaultValue(T bean) {
        //默认值
        Integer defaultVal = bean.getDefaultVal();
        if (defaultVal != null && defaultVal == 1) {
            //当前登录人
            if ("selectUserSingle".equals(bean.getInputCompType()) || "selectUserMultiple".equals(bean.getInputCompType())) {
                bean.setCollectPointVal(Optional.ofNullable(SysUserHolder.getCurrentUser()).map(SysUser::getId).orElse(null));
                bean.setCollectPointText(Optional.ofNullable(SysUserHolder.getCurrentUser()).map(SysUser::getRealName).orElse(null));
            } else {
                bean.setCollectPointVal(Optional.ofNullable(SysUserHolder.getCurrentUser()).map(SysUser::getRealName).orElse(null));
            }
        } else if (defaultVal != null && defaultVal == 2) {
            //当前时间
            bean.setCollectPointVal(DateTimeUtils.getNowDateTimeStr());
        }
    }

}
