package com.yunhesoft.leanCosting.productSchedu.service.impl;

import com.yunhesoft.leanCosting.productSchedu.service.IProductScheduPlanService;
import com.yunhesoft.core.common.model.BaseTodoApp;
import com.yunhesoft.core.utils.spring.SpringUtils;

public class ProductScheduTodoApp extends BaseTodoApp {

	/**
	 * 获取待办项数量
	 * 
	 * @return
	 */
	public Integer getTodoInfoCount(int dataType) {
		int count = SpringUtils.getBean(IProductScheduPlanService.class).getTodoInfoCount(dataType);
		return count;
	}
}
