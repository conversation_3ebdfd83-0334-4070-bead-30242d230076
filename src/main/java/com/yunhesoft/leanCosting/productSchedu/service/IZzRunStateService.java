package com.yunhesoft.leanCosting.productSchedu.service;
/**
 * 装置运行状态
 */

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.CostProgStartTimeDto;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.ZzRunStateDto;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlanStart;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;


public interface IZzRunStateService {

	/**
	 * 判断传递过来的开始时间参数是否已存在，如果已存在，不允许保存
	 * 
	 * @param unitId        核算对象编码
	 * @param startDatetime 开始日期+时单
	 * @return
	 */
	boolean isCanSaveStartDateTime(CostProgStartTimeDto dto);

//	/**
//	 * 按核算对象保存开始日期+时间数据
//	 * 
//	 * @param unitId        核算对象编码
//	 * @param programid     方案编码
//	 * @param planId        计划编码
//	 * @param startDatetime 开始日期+时间
//	 * @param xbsj          下班时间
//	 * @param type          类型，0：结束日期+时间为下班时间，1：结束日期+时间为下班时间向后推算10年
//	 * @return
//	 */
//	boolean saveStartDateTime(String unitId, String programid, String planId, String startDatetime);

	/**
	 * 保存核算交接班录入的主数据
	 * @param unitId 核算对象编码
	 * @param tbrq 填写日期
	 * @param sbsj 上班时间
	 * @param xbsj 下班时间
	 * @param orgId 机构代码
	 * @param shiftId 班次代码
	 * @param tjrq 统计日期
	 * @return
	 */
	String saveCostInputMain(String unitId, String tbrq, String sbsj, String xbsj, String orgId, String shiftId,
			String tjrq);

	/**
	 * 获取装置运行状态工作台展示数据
	 * 
	 * @param dto
	 */
	JSONObject getZzRunStateData(ZzRunStateDto dto);

	/**
	 * 平稳率超限点判断的请求：传时间，得到所有的核算对象在传入时间点的方案ID，返回一个hashmap<核算对象ID,方案ID>。
	 * @param nowDt 日期+时间 不传默认取当前日期+时间
	 * @return
	 */
	LinkedHashMap<String, String> getUnitProgByDatetime(String nowDt);

	/**
	 * 根据id 获取交接班录入的主数据信息
	 * @param id
	 * @return
	 */
	CostTeamInfo getCostInputMainCostTeamInfo(String id);

	/**
	 * 传入一段时间，得到所有核算对象从开始时间到截止时间的方案切换情况，返回LinkedHashMap<核算对象ID,List<Vo>>
	 * @param unitId 核算对象编码，如果为空，查询所有的
	 * @param kssj 开始日期+时间
	 * @param jzsj 截止日期+时间
	 * @return
	 */
	LinkedHashMap<String, List<ProductScheduPlanStart>> getUnitProgByksrqjzrq(String unitId, String kssj, String jzsj);

	/**
	 * 根据核算对象获取方案数据
	 * @param unitId 核算对象编码
	 * @return
	 */
	List<ProgramItem> getProgListByUnit(String unitId);

	/**
	 * 
	 * @param unitId 核算对象编码
	 * @param programid 方案编码
	 * @param startDatetime 开始时间
	 * @param id 类型 有值，但表修改数据，无值，代表新添加数据
	 * @return
	 */
	boolean saveCostStartDateTime(CostProgStartTimeDto dto);

	/**
	 * 按开始和结束时间获取核算对象方案切换数据
	 * @param dto
	 * @return
	 */
	JSONObject getCostProgChangeData(ZzRunStateDto dto);

	/**
	 * 传入一个核算对象ID加一个时间点，接口返回这个时间点的方案
	 * @param unitId 核算对象编码
	 * @param dt 日期+时间
	 * @return key 核算对象编码 value 方案切换的数据
	 */
	LinkedHashMap<String, ProductScheduPlanStart> getCostProgDataByUnitDateTime(String unitId, String dt);

	/**
	 * 获取一个核算对象小于等于时间最大的数据
	 * @param dto 需要传递核算对象编码和日期+时间
	 * @return
	 */
	List<ProductScheduPlanStart> getZzRunState(List<CostProgStartTimeDto> dto);

	/**
	 * 判断是否允许进行方案切换
	 * @return 如果空，允许进行方案切换
	 */
	String getProgChangeRedisSize();

	/**
	 * 获取一个核算对象是否允许进行方案切换
	 * @param dto
	 * @return 如果为空，则允许核算对象进行方案切换操作
	 */
	String getProgChangeRedisUnit(CostProgStartTimeDto dto);

	/**
	 * 方案切换提取数据和计算redis赋值操作
	 * @param dto
	 */
	void setProrChangeRedisValue(CostProgStartTimeDto dto);

	/**
	 * 获取正在计算或提取平稳率数据的提示信息
	 * @param dto
	 * @return
	 */
	String getProgChangeRedisUnitCalValue(CostProgStartTimeDto dto);

	void insertProgChangeUnitSize();

	/**
	 * 判断核算方案是否可以删除，传入核算对象编码和方案编码
	 * @param dto
	 * @return
	 */
	boolean isCanDelUnitProg(CostProgStartTimeDto dto);

	/**
	 * 是否可以进行方案切换操作
	 * @return
	 */
	List<String> getChangeDays(String dt);

	
}
