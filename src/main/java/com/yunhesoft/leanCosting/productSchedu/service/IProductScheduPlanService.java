package com.yunhesoft.leanCosting.productSchedu.service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.ProductPlanFeedDto;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.ProductScheduPlanDto;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.ProductScheduPlanShSpDto;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.ProductTitleDto;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlan;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlanFeed;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlanLevel;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduReceiveItem;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduTitle;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.ProductPlanFeedVo;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.ProductScheduReceiveLibVo;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.ProductUnitProgShift;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.ProductionPlanUnitTodoVo;
import com.yunhesoft.leanCosting.programConfig.entity.vo.LeanLedgerFormVo;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.system.kernel.service.model.Pagination;

public interface IProductScheduPlanService {

	/**
	 * 数据审核，审批
	 * 
	 * @param data      数据
	 * @param dataState 1：审核通过，2：审批通过，-1：审核否决，-2：审批否决
	 * @return
	 */
	boolean saveProductScheduPlanShSp(ProductScheduPlanShSpDto dto);

	/**
	 * 保存排产计划数据
	 * 
	 * @param data
	 * @return
	 */
	boolean saveProductScheduPlan(ProductScheduPlanShSpDto dto);

	/**
	 * 获取排产计划数据
	 * 
	 * @param dto
	 * @param page
	 * @return
	 */
	List<ProductScheduPlan> getProductScheduPlan(ProductScheduPlanDto dto, Pagination<?> page);

	/**
	 * 获取待办数量
	 * 
	 * @param dataType 1：待审核，2：待审批
	 * @return
	 */
	Integer getTodoInfoCount(int dataType);

	/**
	 * 获取排产计划和核算工作台展示数据
	 * 
	 * @param dto
	 */
	JSONObject getProDuctPlanWorkData(ProductScheduPlanDto dto);

	/**
	 * 获取计划已反馈的数据
	 * 
	 * @param dto
	 * @return
	 */
	List<ProductPlanFeedVo> getPlanFeedDataList(ProductPlanFeedDto dto, Pagination<?> page);

	/**
	 * 保存排产计划反馈数据
	 * 
	 * @param dto
	 * @return
	 */
	boolean savePlanFeedData(ProductPlanFeedDto dto);

	/**
	 * 查询添加或修改反馈的数据
	 * 
	 * @param dto
	 * @return
	 */
	ProductPlanFeedVo getPlanAddOrEditFeedData(ProductPlanFeedDto dto);

	/**
	 * 排产计划反馈主页面获取排产计划数据的接口
	 * 
	 * @param dto
	 * @return
	 */
	List<ProductScheduPlan> getProductScheduPlanForFeedMain(ProductScheduPlanDto dto, Pagination<?> page);

	/**
	 * 修改反馈 的完成状态判断 修改成已完成，大于参数反馈日期的反馈数据中有未完成的，不允许将反馈的完成状态修改为已 完成 修改成未完成，小于参数反馈的日期反馈
	 * 数据中有已完成的，不允许将反馈的完成状态修改为未完成
	 * 
	 * @param dto
	 * @return true：可以修改 ，false：不可以修改
	 */
	boolean isChangFeedComplete(ProductPlanFeedDto dto);

	/**
	 * 获取合并反馈的数据
	 * 
	 * @param dto
	 * @param page
	 * @return
	 */
	List<ProductPlanFeedVo> getPlanFeedDataListGroup(ProductPlanFeedDto dto, Pagination<?> page);

	/**
	 * 获取已领取的物资数据
	 * 
	 * @param dto
	 * @param page
	 * @return
	 */
	List<ProductScheduReceiveItem> getFeedReceiveData(ProductPlanFeedDto dto, Pagination<?> page);

	/**
	 * 获取领取项目的数据，需要传递参数，方案编码，方案名称，日期，核算对象编码，核算对象名称
	 * 
	 * @param dto
	 * @param page
	 * @return
	 */
	List<ProductScheduReceiveLibVo> getReceiveLib(ProductPlanFeedDto dto, Pagination<?> page);

	/**
	 * 获取反馈时的输出项目 需要传递参数，方案编码，方案名称，日期，核算对象编码，核算对象名称
	 * 
	 * @param dto
	 * @param page
	 * @return
	 */
	List<ProductScheduReceiveLibVo> getFeedLib(ProductPlanFeedDto dto, Pagination<?> page);

	/**
	 * 获取合并反馈的数据(分组下的详细反馈数据）
	 * 
	 * @param dto
	 * @param page
	 * @return
	 */
	List<ProductPlanFeedVo> getPlanFeedDataListGroupMx(ProductPlanFeedDto dto, Pagination<?> page);

	/**
	 * 获取合并反馈的数据(分组下的详细反馈数据）
	 * 
	 * @param dto
	 * @param page
	 * @return
	 */
	JSONObject getPlanFeedDataListGroupMxJsonStr(ProductPlanFeedDto dto, Pagination<?> page);

	/**
	 * 获取表单的信息
	 * 
	 * @param programId 方案编码
	 * @param pVersion  日期
	 * @return
	 */
	List<LeanLedgerFormVo> getProgFormData(String programId, String pVersion, String unitId);

	/**
	 * 删除反馈的数据
	 * 
	 * @param dto
	 * @return
	 */
	boolean deleteFeedDataGroup(ProductPlanFeedDto dto);

	/**
	 * 保存设置的表头数据
	 * 
	 * @param dto
	 * @return
	 */
	boolean saveProducScheduTitle(ProductTitleDto dto);

	/**
	 * 获取排产计划或反馈的表头数据
	 * 
	 * @param unitId        核算对象编码
	 * @param columnUseType 1：计划，2：反馈
	 * @param cxType        0: 查询，1：设置
	 * @return
	 */
	List<ProductScheduTitle> getProducScheduTitle(String unitId, int columnUseType, int cxType);

	/**
	 * 判断批次号是否可用，true：可用，false：有重复的不可用
	 * 
	 * @param batchNo 批次号
	 * @param id      数据的id
	 * @param cxType  1：添加，0：修改
	 * @return
	 */
	boolean isCanSaveBatchNo(String batchNo, String id, int cxType);

	/**
	 * 根据核算对象，日期和班次代码，获取当班数据
	 * 
	 * @param unitId   核算对象编码
	 * @param feeddate 日期
	 * @param shiftId  班次代码
	 * @return
	 */
	List<ShiftForeignVo> getshiftList(String unitId, String feeddate, String shiftId);

	/**
	 * 按核算对象保存开始日期+时间数据
	 * 
	 * @param unitId        核算对象编码
	 * @param programid     方案编码
	 * @param planId        计划编码
	 * @param startDatetime 开始日期+时间
	 * @param xbsj          下班时间
	 * @param type          类型，0：结束日期+时间为下班时间，1：结束日期+时间为下班时间向后推算10年
	 * @return
	 */
	boolean savePlanStartDateTime(String unitId, String programid, String planId, String startDatetime, String xbsj,
			int type);

	/**
	 * 判断传递过来的开始时间参数是否已存在，如果已存在，不允许保存
	 * 
	 * @param unitId        核算对象编码
	 * @param startDatetime 开始日期+时单
	 * @return
	 */
	boolean isCanSavePlanStartDateTime(String unitId, String startDatetime);

	/**
	 * 判断一个时间点是否在当班的上下班时间范围内
	 * 
	 * @param unitId   核算对象编码
	 * @param feeddate 日期
	 * @param shiftId  班次代码
	 * @param nowDt    日期+时间 yyyy-mm-dd hh:mm:ss
	 * @return
	 */
	boolean isShowStartBtn(String unitId, String feeddate, String shiftId, String nowDt);

	/**
	 * 返回一个时间点的核算对象和方案数据和所有的倒班数据，最后拼接成转成，一个核算对象在某个时刻的所用到的方案和上班的班次信息
	 * 
	 * @param dt       日期+时间
	 * @param tenantId 租户编码
	 * @return
	 */
	List<ProductUnitProgShift> getUnitProgByDatetime(String dt, String tenantId);

	/**
	 * 按核算对象统计待审核或待审批的数据
	 * 
	 * @param type 1：待审核，2：待审批
	 * @return
	 */
	List<ProductionPlanUnitTodoVo> getProductScheduPlanTodoUnit(Integer type);

	/**
	 * 按反馈的byzd1 模糊检索，返回不重复的byzd1;
	 * 
	 * @param byzd1
	 * @return
	 */
	List<String> getKeyWordLikeByzd1(String byzd1);

//	/**
//	 * 根据byzd1 获取反馈数据，返回不重复的核算对象
//	 * @param listByzd1 byzd1List
//	 * @return
//	 */
//	List<ProductScheduPlanFeed> getUnitdataByFeedByzd1(List<String> listByzd1);

	/**
	 * 根据核算对象和备用字段1 返回反馈的数据
	 * 
	 * @param unitId 核算对象编码
	 * @param byzd1  备用字段1
	 * @return
	 */
	LinkedHashMap<String, List<ProductScheduPlanFeed>> getFeedataByUnitByzd1(String jsonData);

	/**
	 * 是否允许删除排产计划数据
	 * 
	 * @param listPlanId
	 * @return
	 */
	JSONObject isCanDelPlan(List<String> listPlanId);

	/**
	 * 判断当前用户是否允许录入所有时间的数据，true：可以，false：只能录入当班的数据
	 * 
	 * @param unitId  核算对象编码
	 * @param orgCode 机构代码
	 * @return
	 */
	boolean isCanInputAllData(String unitId, String orgCode);

	/**
	 * 根据日期+时间和机构代码获取当班信息
	 * 
	 * @param nowDt   日期+时间
	 * @param orgCode 机构代码
	 * @return
	 */
	ShiftForeignVo getShiftByDateTime(String nowDt, String orgCode);

	/**
	 * 根据id获取反馈的记录
	 * @param id
	 * @return
	 */
	ProductScheduPlanFeed getPlanFeedDataById(String id);

	/**
	 * 根据计划编码获取计划数据
	 * @param id 计划编码
	 * @return
	 */
	ProductScheduPlan getPlanDataById(String id);

	/**
	 * 获取一个核算对象小于日期时间参数的最大的一条记录的计划编码
	 * @param unitId 核算对象编码
	 * @param dt 日期+时间
	 * @return
	 */
	String getMaxStartPlanId(String unitId, String dt, String sbsj, String xbsj);

    Boolean isAllowDelProduct(String productId);

	/**
	 * 停止已开始的数据
	 * @param unitId 核算对象编码
	 * @param planId 计划编码
	 * @param sbsj 上班时间
	 * @param xbsj 下班时间
	 * @return
	 */
	boolean savePlanStopDateTime(String unitId, String planId, String sbsj, String xbsj);

	/**
	 * 获取待确认的计划核算对象信息
	 * @param dto
	 * @param page
	 * @return
	 */
	List<ProductionPlanUnitTodoVo> getProductScheduPlanQrUnit(ProductScheduPlanDto dto);

	/**
	 * 按核算对象获取计划待确认数据 
	 * @param dto
	 * @return
	 */
	List<ProductScheduPlan> getProductScheduPlanQr(ProductScheduPlanDto dto);

	/**
	 * 计划待确认的数据进行确认或删除
	 * @param dto
	 * @return
	 */
	boolean saveProductScheduPlanQrData(ProductScheduPlanShSpDto dto);

	/**
	 * 根据产品编码获取计划数据
	 * @param productid
	 * @return
	 */
	Map<String, List<ProductScheduPlan>> getPlanByProduct(String productid);

	/**
	 * 获取一个核算对象计划的所有反馈数据
	 * @param unitId 核算对象编码
	 * @param planId 计划编码
	 * @return
	 */
	List<ProductScheduPlanFeed> getPlanFeedByUnitPlanId(String unitId, String planId);

	/**
	 * 更新计划的实际完成量和状态
	 * @param objFeed
	 */
	void updatePlanWcztOrSjwcl(String planId);

	/**
	 * 获取计划级别
	 * @return
	 */
	List<ProductScheduPlanLevel> getPlanLevel();

	/**
	 * 获取反馈输出产品的价格
	 * @param feedDate 反馈日期
	 * @param itemId 反馈输出产品的ID
	 * @return 价格
	 */
	Double getItemPrice(String feedDate, String itemId);

	/**
	 * 通过产品id 查询计划 或  反馈数据
	 * <AUTHOR>
	 * @return
	 * @params
	*/
	List<ProductScheduPlan> getPlanByProductId(List<String> productId);

	/**
	 * 通过产品id 查询反馈数据
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	List<ProductScheduPlanFeed> getFeedDataByProductId(List<String> productId);
	
	/**
	 * 获取核算对象下操作班组的人，有反馈计划权限的
	 * @param unitCode
	 * @return
	 */
	List<String> getMessageSender(String unitid);

	/**
	 * 获取最新的的产品编号
	 * <AUTHOR>
	 * @return
	 * @params
	*/

    Integer getMaxProductNo(String unitid,String productNo);
}
