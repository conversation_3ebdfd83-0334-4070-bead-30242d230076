package com.yunhesoft.leanCosting.productSchedu.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 排产计划反馈 
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "PRODUCTSCHEDU_PLAN_FEED")
public class ProductScheduPlanFeed extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 计划编码 */
    @Column(name="PLANID", length=100)
    private String planid;
    
    /** 订单ID  */
    @Column(name="INDENTID", length=100)
    private String indentid;
    
    /** 产品名称  */
    @Column(name="PRODUCTNAME", length=100)
    private String productname;
    
    /** 产品ID  */
    @Column(name="PRODUCTID", length=100)
    private String productid;
    
    /** 核算对象ID  */
    @Column(name="UNITID", length=100)
    private String unitid;
    
    /** 核算对象名称  */
    @Column(name="UNITNAME", length=100)
    private String unitname;
    
    /** 方案ID  */
    @Column(name="PROGRAMID", length=100)
    private String programid;
    
    /** 方案名称  */
    @Column(name="PROGRAMNAME", length=100)
    private String programname;
    
    /** 开始日期和时间 */
    @Column(name="STARTDATETIME", length=19)
    private String startdatetime;
    
    /** 结束日期和时间 */
    @Column(name="ENDDATETIME", length=19)
    private String enddatetime;
    
    /** 反馈机构代码 */
    @Column(name="FEEDORGCODE", length=100)
    private String feedorgcode;
    
    /** 反馈机构名称 */
    @Column(name="FEEDORGNAME", length=100)
    private String feedorgname;
    
    /** 反馈人员编码 */
    @Column(name="FEEDUSERID", length=100)
    private String feeduserid;
    
    /** 反馈人员姓名 */
    @Column(name="FEEDUSERNAME", length=100)
    private String feedusername;
    
    /** 反馈完成量 */
    @Column(name="FEEDVALUE")
    private Double feedvalue;
    
    /** 批次号 */
    @Column(name="BATCHNO", length=100)
    private String batchno;
    
    /** 反馈日期 */
    @Column(name="FEEDDATE", length=10)
    private String feeddate;
    
    /** 反馈日期和时间 */
    @Column(name="FEEDDATETIME", length=19)
    private String feeddatetime;
    
    /** 是否完成（0：未完成，1：进行中，2：已完成 ） */
    @Column(name="ISCOMPLETE")
    private Integer iscomplete;
    
    /** 计划名称  */
    @Column(name="PLANNAME", length=500)
    private String planname;
    
    /** 反馈产生的项目编码 */
    @Column(name="ITEMID", length=100)
    private String itemid;
    
    /** 反馈产生的项目名称 */
    @Column(name="ITEMNAME", length=100)
    private String itemname;
    
    /** 汇总反馈编码 */
    @Column(name="GROUPID", length=100)
    private String groupid;
    
    /** 汇总反馈时间 */
    @Column(name="FEETTOTALDATE", length=20)
    private String feedtotaldate;
    
    /** 删除标识（0：已删除，1：未删除） */
    @Column(name="TMUSED")
    private Integer tmused;
    
    /** 班次编码 */
    @Column(name="SHIFTCLASSID", length=100)
    private String shiftClassId;
    
    /** 上班时间 */
    @Column(name="SBSJ", length=19)
    private String sbsj;
    
    /** 下班时间 */
    @Column(name="XBSJ", length=19)
    private String xbsj;
    
    /** 表单编码 */
    @Column(name="FORMID", length=100)
    private String fromId;
    
    /** 班次名称 */
    @Column(name="SHIFTCLASSNAME", length=100)
    private String shiftClassName;
    
    
    /** 备用字段1  */
    @Column(name="BYZD1", length=100)
    private String byzd1;

    /** 备用字段2  */
    @Column(name="BYZD2", length=100)
    private String byzd2;

    /** 备用字段3  */
    @Column(name="BYZD3", length=100)
    private String byzd3;

    /** 备用字段4  */
    @Column(name="BYZD4", length=100)
    private String byzd4;

    /** 备用字段5  */
    @Column(name="BYZD5", length=100)
    private String byzd5;

    /** 备用字段6  */
    @Column(name="BYZD6", length=100)
    private String byzd6;

    /** 备用字段7  */
    @Column(name="BYZD7", length=100)
    private String byzd7;

    /** 备用字段8  */
    @Column(name="BYZD8", length=100)
    private String byzd8;

    /** 备用字段9  */
    @Column(name="BYZD9", length=100)
    private String byzd9;

    /** 备用字段10  */
    @Column(name="BYZD10", length=100)
    private String byzd10;
    
    /** 批次号计算部分 */
    @Column(name="BATCHNOCALSTR", length=500)
    private String batchnocalstr;
    
    /** 批次号自动序号 */
    @Column(name="BATCHNOAUTONUM")
    private Integer batchnoautonum;
    
    /** 是否手动修改了批次号 */
    @Column(name="ISCHANGEBATCHNO")
    private Integer ischangebatchno;
    
    /** 是否手动修改了备用字段1 */
    @Column(name="ISCHANGEBYZD1")
    private Integer ischangebyzd1;
    
    /** 是否手动修改了备用字段2 */
    @Column(name="ISCHANGEBYZD2")
    private Integer ischangebyzd2;
    
    /** 是否手动修改了备用字段3 */
    @Column(name="ISCHANGEBYZD3")
    private Integer ischangebyzd3;
    
    /** 是否手动修改了备用字段4 */
    @Column(name="ISCHANGEBYZD4")
    private Integer ischangebyzd4;
    
    /** 是否手动修改了备用字段5 */
    @Column(name="ISCHANGEBYZD5")
    private Integer ischangebyzd5;
    
    /** 是否手动修改了备用字段6 */
    @Column(name="ISCHANGEBYZD6")
    private Integer ischangebyzd6;
    
    /** 是否手动修改了备用字段7 */
    @Column(name="ISCHANGEBYZD7")
    private Integer ischangebyzd7;
    
    /** 是否手动修改了备用字段8 */
    @Column(name="ISCHANGEBYZD8")
    private Integer ischangebyzd8;
    
    /** 是否手动修改了备用字段9 */
    @Column(name="ISCHANGEBYZD9")
    private Integer ischangebyzd9;
    
    /** 是否手动修改了备用字段10 */
    @Column(name="ISCHANGEBYZD10")
    private Integer ischangebyzd10;
    
    /** 单价 */
    @Column(name="ITEMPRICE")
    private Double itemprice;
}