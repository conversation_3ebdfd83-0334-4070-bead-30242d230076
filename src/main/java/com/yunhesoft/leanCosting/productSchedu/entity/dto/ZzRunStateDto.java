package com.yunhesoft.leanCosting.productSchedu.entity.dto;


import com.yunhesoft.core.common.dto.BaseQueryDto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ZzRunStateDto extends BaseQueryDto{
	/** 核算对象编码 */
    @ApiModelProperty(value = "核算对象编码（all:全部）")
    private String unitId;
    
    /** 核算对象名称 */
    @ApiModelProperty(value = "核算对象名称（all:全部）")
    private String unitName;
    
    
    /** 方案编码 */
    @ApiModelProperty(value = "方案编码（all:全部）")
    private String programid;
    
    /** 开始日期 */
    @ApiModelProperty(value = "开始日期")
    private String startDt;
    
    /** 截止日期 */
    @ApiModelProperty(value = "截止日期")
    private String endDt;
    
    /** 机构编码 */
    @ApiModelProperty(value = "机构编码）")
    private String orgIds;
    
}
