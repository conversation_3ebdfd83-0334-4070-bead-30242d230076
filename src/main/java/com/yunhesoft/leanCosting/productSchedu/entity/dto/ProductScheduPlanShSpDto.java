package com.yunhesoft.leanCosting.productSchedu.entity.dto;

import java.util.List;

import com.yunhesoft.core.common.dto.BaseQueryDto;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ProductScheduPlanShSpDto extends BaseQueryDto{

	/** 审核审批状态 */
    @ApiModelProperty(value = "数据状态（1：审核通过，2：审批通过，-1：审核否决，-2：审批否决，3：确认")
    private Integer dataState;
    
    /** 审核审批数据 */
    @ApiModelProperty(value = "数据状态（1：审核通过，2：审批通过，-1：审核否决，-2：审批否决")
    private List<ProductScheduPlan> data;
    
    /**  保存的类型   */
    @ApiModelProperty(value = "保存的类型（1：正常保存（添加，未通过前的），2：审批通过（这种状态下，保存的时候，不改变数据的状态）")
    private Integer saveType;
}
