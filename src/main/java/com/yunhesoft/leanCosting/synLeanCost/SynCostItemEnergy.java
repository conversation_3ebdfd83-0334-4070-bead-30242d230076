package com.yunhesoft.leanCosting.synLeanCost;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costlibraryitem;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryitemVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostlibraryitemService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitversion;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BeanVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostFormulaService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.synchronous.utils.SynModel;


/**
 *	核算项目（能流点）-08
 * <AUTHOR>
 * @date 2023-11-12
 */
@Service
public class SynCostItemEnergy extends SynModel {
	
	@Autowired
	private IUnitMethodService methodService; // 核算相关方法
	
	@Autowired
	private ICostlibraryitemService libraryitemService;
	
	@Autowired
	private ICostFormulaService formulaService;
		
	
	private String synUseFlagValue = synParam.get("synUseFlagValue"); //外部数据同步使用标识值
	
	private String tokenStr = null;
	
	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList, HashMap<String,String> synParam) {
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>(); //失败返回的数据
		if(StringUtils.isNotEmpty(dataList)) {
			if(StringUtils.isNotEmpty(synParam)) {
				this.synUseFlagValue = synParam.get("synUseFlagValue"); //外部数据同步使用标识值
			}
			if(StringUtils.isEmpty(this.synUseFlagValue)) {
				this.synUseFlagValue = "1"; //默认标识1为使用
			}
			
			List<Costlibraryitem> updLibraryitemList = new ArrayList<Costlibraryitem>(); //同步更新项目库中的计量单位
			List<Costclass> addClassList = new ArrayList<Costclass>();
			List<Costitem> addItemList = new ArrayList<Costitem>();
			List<Costitem> updItemList = new ArrayList<Costitem>();
			List<Costunitsampledot> addSampledotList = new ArrayList<Costunitsampledot>();
			List<Costunitsampledot> updSampledotList = new ArrayList<Costunitsampledot>();
			List<Costinstrument> addInstrumentList = new ArrayList<Costinstrument>();
			List<Costinstrument> updInstrumentList = new ArrayList<Costinstrument>();
			
			HashMap<String, String> hasR3dbMap = new HashMap<String, String>();
			List<Costunitsampledot> synR3dbList = new ArrayList<Costunitsampledot>();
			//数据库中存在的项目
			HashMap<String, Integer> newItemMap = new HashMap<String, Integer>();
			HashMap<String, Integer> maxSortMap = new HashMap<String, Integer>();
			HashMap<String, Costitem> queryMap = new HashMap<String, Costitem>();
			List<Costitem> queryList = methodService.getCostitemList(null);
			if(StringUtils.isNotEmpty(queryList)) {
				this.getDataMap(queryList, queryMap, maxSortMap);
			}
			
			//核算对象数据
			HashMap<String, Costuint> unitMap = new HashMap<String, Costuint>();
			List<Costuint> unitList = methodService.getCostuintList(null);
			if(StringUtils.isNotEmpty(unitList)) {
				this.getCostUnitMap(unitList, unitMap);
			}
			
			//核算对象版本数据
			HashMap<String, String> versionMap = new HashMap<String, String>();
			List<Costunitversion> versionList = methodService.getCostunitversionList(null);
			if(StringUtils.isNotEmpty(versionList)) { //有版本数据
				this.getVersionMap(versionList, versionMap);
			}
			
			//分类数据
			HashMap<String, HashMap<String, Costclass>> classMap = new HashMap<String, HashMap<String, Costclass>>();
			List<Costclass> classList = methodService.getCostclassList(null);
			if(StringUtils.isNotEmpty(classList)) {
				this.getCostClassMap(classList, classMap);
			}
			//分类类型
			List<BeanVo> classTypeList = methodService.getCostClassTypeList();
			
			//项目库
			HashMap<String, CostlibraryitemVo> libraryitemMap = new HashMap<String, CostlibraryitemVo>();
			List<CostlibraryitemVo> libraryitemList = libraryitemService.getData(null);
			if(StringUtils.isNotEmpty(libraryitemList)) {
				this.getLibraryItemMap(libraryitemList, libraryitemMap);
			}
			
			//采集点
			HashMap<String, Integer> newSampledotMap = new HashMap<String, Integer>();
			HashMap<String, Integer> sampledotMaxSortMap = new HashMap<String, Integer>();
			HashMap<String, Costunitsampledot> sampledotMap = new HashMap<String, Costunitsampledot>();
			MethodQueryDto dto = new MethodQueryDto();
			dto.setCtype("1"); //成本仪表
			List<Costunitsampledot> sampledotList = methodService.getCostunitsampledotList(dto);
			if(StringUtils.isNotEmpty(sampledotList)) {
				this.getSampledotMap(sampledotList, sampledotMap, sampledotMaxSortMap);
			}
			
			//成本仪表
			HashMap<String, Integer> newInstrumentMap = new HashMap<String, Integer>();
			HashMap<String, Integer> instrumentMaxSortMap = new HashMap<String, Integer>();
			HashMap<String, Costinstrument> instrumentMap = new HashMap<String, Costinstrument>();
			List<Costinstrument> instrumentList = methodService.getCostinstrumentList(null);
			if(StringUtils.isNotEmpty(instrumentList)) {
				this.getInstrumentMap(instrumentList, instrumentMap, instrumentMaxSortMap);
			}
			
			for(HashMap<String, Object> temp : dataList) {
				CostitemVo synVo = ObjUtils.convertToObject(CostitemVo.class, temp);
				if(synVo != null) { //数据读取成功
					String unitid_syn = synVo.getUnitid();
					String unitid = "";
					if(StringUtils.isNotEmpty(unitid_syn)&&StringUtils.isNotEmpty(unitMap)&&unitMap.containsKey(unitid_syn)) {
						Costuint unitObj = unitMap.get(unitid_syn);
						unitid = unitObj.getId();
					}
					if(StringUtils.isNotEmpty(unitid)) { //有此核算对象
						synVo.setUnitid(unitid);
						if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(versionMap)&&versionMap.containsKey(unitid)) {
							String begintime = versionMap.get(unitid);
							if(StringUtils.isNotEmpty(begintime)) { //有版本日期
								synVo.setBegintime(begintime);
								//判断是否存在分类数据，不存在时初始化；已存在直接使用；
								HashMap<String, Costclass> classDataMap = new HashMap<String, Costclass>();
								if(StringUtils.isNotEmpty(classMap)&&classMap.containsKey(unitid+"_"+begintime)) {
									classDataMap = classMap.get(unitid+"_"+begintime);
								}else { //不存在分类数据，初始化
									if(StringUtils.isNotEmpty(classTypeList)) {
						        		for (int i = 0; i < classTypeList.size(); i++) {
						        			String classType = classTypeList.get(i).getKey();
						            		// 默认新增分类【按照分类类型生成】
						                	Costclass classObj = new Costclass();
						                	classObj.setId(TMUID.getUID());
						                	classObj.setPid("root");
						                	classObj.setCcname(classType);
						                	classObj.setMemo("");
						                	classObj.setCctype(classType);
						                	classObj.setUnitid(unitid);
						                	classObj.setBegintime(begintime);
						                	classObj.setTmused(1);
						                	classObj.setTmsort(i+1);
						                	addClassList.add(classObj);
						                	if(!classDataMap.containsKey(unitid+"_"+begintime+"_"+classType)) {
						                		classDataMap.put(unitid+"_"+begintime+"_"+classType, classObj);
						                	}
										}
						        		classMap.put(unitid+"_"+begintime, classDataMap);
						        	}
								}
								
								String key = unitid+"_"+begintime+"_动力";
								if(StringUtils.isNotEmpty(key)&&StringUtils.isNotEmpty(classDataMap)&&classDataMap.containsKey(key)) { //有此分类
									String classId = classDataMap.get(key).getId();
									//项目库中获取项目编码
									String itemid_syn = synVo.getItemid();
									if(StringUtils.isNotEmpty(itemid_syn)&&StringUtils.isNotEmpty(libraryitemMap)&&libraryitemMap.containsKey(itemid_syn)) {
										CostlibraryitemVo libraryitemVo = libraryitemMap.get(itemid_syn);
										String libItemid = libraryitemVo.getId();
										synVo.setItemid(libItemid);
										String itemname = libraryitemVo.getCcname();
						                int sumtype = libraryitemVo.getSumtype();
						                String unit = libraryitemVo.getUnit() == null ? "" : libraryitemVo.getUnit();
						                String erpcode = libraryitemVo.getErpcode() == null ? "" : libraryitemVo.getErpcode();
						                String ioCode = synVo.getIoCode()==null?"0":synVo.getIoCode(); //物料流向
						                
						                if(StringUtils.isEmpty(unit)) { //项目库中计量单位为空（将接口中的计量单位同步更新到项目库中）
						                	String itemunit = synVo.getItemunit()==null?"":synVo.getItemunit(); //接口中的计量单位
						                	if(StringUtils.isNotEmpty(itemunit)) { //接口中计量单位不为空
						                		unit = itemunit;
						                		libraryitemVo.setUnit(itemunit);
						                		Costlibraryitem libraryitemObj = new Costlibraryitem();
						                		BeanUtils.copyProperties(libraryitemVo, libraryitemObj); // 赋予返回对象
						                		updLibraryitemList.add(libraryitemObj);
						                	}
						                }
						                
						                String itemid = "";
						                String itemKey = unitid+"_"+begintime+"_"+classId+"_"+libItemid;
										if(!queryMap.containsKey(itemKey)) { //新增项目
											String itemPxkey = unitid+"_"+begintime+"_"+classId;
											int maxPx = 0;
											if(StringUtils.isNotEmpty(maxSortMap)&&maxSortMap.containsKey(itemPxkey)) {
												maxPx = maxSortMap.get(itemPxkey);
											}
											maxPx += 1;
											maxSortMap.put(itemPxkey, maxPx);
											itemid = TMUID.getUID();
											Costitem addObj = new Costitem();
											addObj.setId(itemid);
											addObj.setUnitid(unitid);
											addObj.setBegintime(begintime);
											addObj.setPid(classId);
											addObj.setItemid(libItemid);
											addObj.setItemname(itemname);
											addObj.setItemunit(unit); //计量单位
											addObj.setErpcode(erpcode); //erp编码
											addObj.setItemprice(0d); //单价
											addObj.setConversionfactor(1d); //换算系数
											addObj.setEnergyfactor(0d); //能耗系数
											addObj.setApportionmentfactor(0d); //分摊因子
											addObj.setMaterialSupply(1); //物料提供者
											addObj.setFeedShowType(0); //项目显示方式
											addObj.setSumtype(sumtype); //汇总方式
											addObj.setComparetype(0); //比较方式
											addObj.setCalcprice(0); //推导价格
											addObj.setUsecalcprice(0); //参与价格推导
											addObj.setPricefactor(1d); //价格系数
											addObj.setPriceSource(0); //价格来源：0、价格管理；1、数据源
											addObj.setTdsAlias(""); //数据源别名
											addObj.setItemCreateType(0); //项目生成类型：1、新增项目（可以编辑项目名称）；0、项目库选择的项目（不能编辑项目名称）；默认值：0
											addObj.setBaseConsumption(0d); //标准单耗
											addObj.setIoCode(ioCode); //物料流向
											addObj.setTmsort(maxPx);
											addObj.setTmused(1);
											addItemList.add(addObj);
											queryMap.put(itemKey, addObj);
											newItemMap.put(itemKey, 1);
										}else {
											//存在项目，判断物料流向是否变化
											Costitem itemObj = queryMap.get(itemKey);
											itemid = itemObj.getId();
											if(!newItemMap.containsKey(itemKey)) {
												itemObj.setItemname(itemname);
												itemObj.setSumtype(sumtype); //汇总方式
												itemObj.setItemunit(unit); //计量单位
												itemObj.setErpcode(erpcode); //erp编码
												itemObj.setIoCode(ioCode);
												updItemList.add(itemObj);
											}
										}
										
										//根据采集点id，生成仪表数据
										String dotid = synVo.getDotid();
										if(StringUtils.isNotEmpty(dotid)) {
											Integer tmUsed = synVo.getTmused();
											if(synUseFlagValue.equals(String.valueOf(tmUsed))) { //使用
												tmUsed = 1;
											}else {
												tmUsed = 0;
											}
											String sampledotKey = unitid+"_"+begintime+"_"+dotid;
											String sampledotId = ""; //采集点id
											if(sampledotMap.containsKey(sampledotKey)) {
												Costunitsampledot dotObj = sampledotMap.get(sampledotKey);
												sampledotId = dotObj.getId();
												if(tmUsed==1&&!newSampledotMap.containsKey(sampledotKey)) { //仪表数据在使用，再生成采集点数据
													//获取同步r3db的采集点数据
													Costunitsampledot savedot = new Costunitsampledot();
													savedot.setName(synVo.getDotname());
													savedot.setDatasource(synVo.getDatasource());
													savedot.setTmused(1);
													savedot.setCtype("1");
													methodService.getSynR3dbSampledotData(dotObj, savedot, synR3dbList, hasR3dbMap);
													
													dotObj.setName(synVo.getDotname());
													dotObj.setTagnumber(synVo.getTagnumber()); //仪表位号
													dotObj.setDatasource(synVo.getDatasource()); //数据来源
													dotObj.setSdUnit(synVo.getItemunit()); //计量单位
													updSampledotList.add(dotObj);
													sampledotMap.put(sampledotKey, dotObj);
												}
											}else {
												if(tmUsed==1) { //仪表数据在使用，再生成采集点数据
													String sampledotPxKey = unitid+"_"+begintime;
													int sampledotMaxPx = 0;
													if(StringUtils.isNotEmpty(sampledotMaxSortMap)&&sampledotMaxSortMap.containsKey(sampledotPxKey)) {
														sampledotMaxPx = sampledotMaxSortMap.get(sampledotPxKey);
													}
													sampledotMaxPx += 1;
													sampledotMaxSortMap.put(sampledotPxKey, sampledotMaxPx);
													
													sampledotId = TMUID.getUID();
													Costunitsampledot dotObj = new Costunitsampledot();
													dotObj.setUnitid(unitid);
													dotObj.setBegintime(begintime);
													dotObj.setId(sampledotId);
													dotObj.setName(synVo.getDotname());
													dotObj.setTagnumber(synVo.getTagnumber()); //仪表位号
													dotObj.setDatasource(synVo.getDatasource()); //数据来源
													dotObj.setSdUnit(synVo.getItemunit()); //计量单位
													dotObj.setPid("root");
													dotObj.setSourceype("1"); //来源类型：1、influxdb；2、数据源；3、手工填写；
													dotObj.setCtype("1"); //采集类型：1、成本仪表；2、控制指标；3、lims指标；
													dotObj.setTmused(1);
													dotObj.setTmsort(sampledotMaxPx);
													dotObj.setMdmCode(dotid);
													addSampledotList.add(dotObj);
													sampledotMap.put(sampledotKey, dotObj);
													newSampledotMap.put(sampledotKey, 1);
													//获取同步r3db的采集点数据
													methodService.getSynR3dbSampledotData(dotObj, null, synR3dbList, hasR3dbMap);
												}
											}
											if(StringUtils.isNotEmpty(sampledotId)) {
												String instrumentKey = unitid+"_"+begintime+"_"+itemid+"_"+sampledotId;
												if(tmUsed==0) { //删除
													if(instrumentMap.containsKey(instrumentKey)) {
														Costinstrument instrumentObj = instrumentMap.get(instrumentKey);
														if(!newInstrumentMap.containsKey(instrumentKey)) {
															instrumentObj.setTmused(0);
															updInstrumentList.add(instrumentObj);
														}
													}
												}else { //新增、修改
													if(instrumentMap.containsKey(instrumentKey)) {
														Costinstrument instrumentObj = instrumentMap.get(instrumentKey);
														if(!newInstrumentMap.containsKey(instrumentKey)) {
															instrumentObj.setName(synVo.getDotname()); //仪表名称
															instrumentObj.setInstrumentRange(synVo.getInstrumentRange()); //仪表量程
															instrumentObj.setConversionfactor(synVo.getInstrumentRatio()); //仪表系数
															instrumentObj.setTagnumber(synVo.getTagnumber()); //仪表位号
															instrumentObj.setMetDownlim(synVo.getMetDownlim()); //仪表量程下限
															updInstrumentList.add(instrumentObj);
														}
													}else { //数据库中没有仪表数据，新增
														String xhKey = unitid+"_"+begintime+"_"+itemid;
														int maxPx = 0;
														if(StringUtils.isNotEmpty(instrumentMaxSortMap)&&instrumentMaxSortMap.containsKey(xhKey)) {
															maxPx = instrumentMaxSortMap.get(xhKey);
														}
														maxPx += 1;
														instrumentMaxSortMap.put(xhKey, maxPx);
														
														Costinstrument instrumentObj = new Costinstrument();
														instrumentObj.setId(TMUID.getUID());
														instrumentObj.setDotid(sampledotId);
														instrumentObj.setPid(itemid);
														instrumentObj.setName(synVo.getDotname()); //仪表名称
														instrumentObj.setUnitid(unitid);
														instrumentObj.setBegintime(begintime);
														instrumentObj.setTmused(1);
														instrumentObj.setTmsort(maxPx);
														instrumentObj.setInstrumentRange(synVo.getInstrumentRange()); //仪表量程
														instrumentObj.setConversionfactor(synVo.getInstrumentRatio()); //仪表系数
														instrumentObj.setTagnumber(synVo.getTagnumber()); //仪表位号
														instrumentObj.setInstrumentType(1);
														instrumentObj.setMetDownlim(synVo.getMetDownlim()); //仪表量程下限
														addInstrumentList.add(instrumentObj);
														instrumentMap.put(instrumentKey, instrumentObj);
														newInstrumentMap.put(instrumentKey, 1);
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}else {
					result.add(temp);//记录到失败数据中
				}
			}
			this.errorInfo = "";
			if("".equals(this.errorInfo)&&StringUtils.isNotEmpty(updLibraryitemList)) {
				String ret = libraryitemService.saveLibraryItemData(null, updLibraryitemList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
			if("".equals(this.errorInfo)&&StringUtils.isNotEmpty(addClassList)) {
				String ret = methodService.saveDataCostclass(addClassList, null, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
			if("".equals(this.errorInfo)&&(StringUtils.isNotEmpty(addItemList)||StringUtils.isNotEmpty(updItemList))) {
				String ret = methodService.saveDataCostitem(addItemList, updItemList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
			if("".equals(this.errorInfo)&&StringUtils.isNotEmpty(addItemList)) {
				try {
					//新增项目成功后，调用初始化公式接口（赵维民）
					formulaService.initCostitem(addItemList);
				}catch(Exception e) {
					e.printStackTrace();
				}
			}
			if("".equals(this.errorInfo)&&(StringUtils.isNotEmpty(addSampledotList)||StringUtils.isNotEmpty(updSampledotList))) {
				String ret = methodService.saveSampledotData(addSampledotList, updSampledotList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}else { //保存成功，同步R3DB
					if(StringUtils.isNotEmpty(synR3dbList)) {
						ret = methodService.synR3dbDataBySampledot(synR3dbList, tokenStr);
						if(StringUtils.isNotEmpty(ret)) {
							this.errorInfo += ret+";";
						}
					}
				}
			}
			if("".equals(this.errorInfo)&&(StringUtils.isNotEmpty(addInstrumentList)||StringUtils.isNotEmpty(updInstrumentList))) {
				String ret = methodService.saveDataCostinstrument(addInstrumentList, updInstrumentList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
			if("".equals(this.errorInfo)&&StringUtils.isNotEmpty(addInstrumentList)) {
				try {
					//新增仪表成功后，调用初始化公式接口（赵维民）
					formulaService.initCostinstrument(addInstrumentList);
				}catch(Exception e) {
					e.printStackTrace();
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取数据Map
	 * @param list
	 * @param dataMap
	 * @param maxSortMap
	 */
	private void getDataMap(List<Costitem> list, HashMap<String, Costitem> dataMap, HashMap<String, Integer> maxSortMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, Costitem>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costitem obj = list.get(i);
				String unitid = obj.getUnitid();
				String begintime = obj.getBegintime();
				String pid = obj.getPid();
				String itemid = obj.getItemid();
				String key1 = unitid+"_"+begintime+"_"+pid+"_"+itemid;
				if(!dataMap.containsKey(key1)) {
					dataMap.put(key1, obj);
				}
				String key2 = unitid+"_"+begintime+"_"+pid;
				int tmsort = obj.getTmsort()==null?0:obj.getTmsort();
				if(maxSortMap.containsKey(key2)) {
					int maxPx = maxSortMap.get(key2);
					if(tmsort>maxPx) {
						maxSortMap.put(key2, tmsort);
					}
				}else {
					maxSortMap.put(key2, tmsort);
				}
			}
		}
	}
	
	/**
	 *	获取核算单元Map
	 * @param list
	 * @param dataMap
	 */
	private void getCostUnitMap(List<Costuint> list, HashMap<String, Costuint> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, Costuint>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costuint obj = list.get(i);
				String mdmCode = obj.getMdmCode();
				if(StringUtils.isNotEmpty(mdmCode)&&!dataMap.containsKey(mdmCode)) {
					dataMap.put(mdmCode, obj);
				}
			}
		}
	}
	
	/**
	 *	获取最大版本Map
	 * @param list
	 * @param dataMap
	 */
	private void getVersionMap(List<Costunitversion> list, HashMap<String, String> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, String>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costunitversion obj = list.get(i);
				String unitid = obj.getUnitid();
				String begintime = obj.getBegintime();
				if(!dataMap.containsKey(unitid)) {
					dataMap.put(unitid, begintime);
				}
			}
		}
	}
	
	/**
	 *	获取分类Map
	 * @param list
	 * @param dataMap
	 */
	private void getCostClassMap(List<Costclass> list, HashMap<String, HashMap<String, Costclass>> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, HashMap<String, Costclass>>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costclass obj = list.get(i);
				String unitid = obj.getUnitid();
				String begintime = obj.getBegintime();
				String cctype = obj.getCctype();
				String key1 = unitid+"_"+begintime;
				String key2 = unitid+"_"+begintime+"_"+cctype;
				if(dataMap.containsKey(key1)) {
					HashMap<String, Costclass> map = dataMap.get(key1);
					map.put(key2, obj);
					dataMap.put(key1, map);
				}else {
					HashMap<String, Costclass> map = new HashMap<String, Costclass>();
					map.put(key2, obj);
					dataMap.put(key1, map);
				}
			}
		}
	}
	
	/**
	 *	获取项目库Map
	 * @param list
	 * @param dataMap
	 */
	private void getLibraryItemMap(List<CostlibraryitemVo> list, HashMap<String, CostlibraryitemVo> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, CostlibraryitemVo>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostlibraryitemVo obj = list.get(i);
				String mdmCode = obj.getMdmCode();
				if(StringUtils.isNotEmpty(mdmCode)&&!dataMap.containsKey(mdmCode)) {
					dataMap.put(mdmCode, obj);
				}
			}
		}
	}
	
	/**
	 *	获取采集点Map
	 * @param list
	 * @param dataMap
	 * @param maxSortMap
	 */
	private void getSampledotMap(List<Costunitsampledot> list, HashMap<String, Costunitsampledot> dataMap,
		HashMap<String, Integer> maxSortMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, Costunitsampledot>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costunitsampledot obj = list.get(i);
				String unitid = obj.getUnitid()==null?"":obj.getUnitid().trim();
				String begintime = obj.getBegintime()==null?"":obj.getBegintime().trim();
				String mdmCode = obj.getMdmCode()==null?"":obj.getMdmCode().trim();
				String key = unitid+"_"+begintime+"_"+mdmCode;
				if(StringUtils.isNotEmpty(mdmCode)&&!dataMap.containsKey(key)) {
					dataMap.put(key, obj);
				}
				String keyPx = unitid+"_"+begintime;
				if(maxSortMap.containsKey(keyPx)) {
					int tmSort = obj.getTmsort()==null?0:obj.getTmsort();
					int maxPx = maxSortMap.get(keyPx);
					if(tmSort>maxPx) {
						maxSortMap.put(keyPx, tmSort);
					}
				}else {
					maxSortMap.put(keyPx, 1);
				}
			}
		}
	}
	
	/**
	 *	获取仪表Map
	 * @param list
	 * @param dataMap
	 * @param maxSortMap
	 */
	private void getInstrumentMap(List<Costinstrument> list, HashMap<String, Costinstrument> dataMap,
		HashMap<String, Integer> maxSortMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, Costinstrument>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costinstrument obj = list.get(i);
				String unitid = obj.getUnitid();
				String begintime = obj.getBegintime();
				String pid = obj.getPid();
				String dotid = obj.getDotid();
				String key = unitid+"_"+begintime+"_"+pid+"_"+dotid;
				if(!dataMap.containsKey(key)) {
					dataMap.put(key, obj);
				}
				String keyPx = unitid+"_"+begintime+"_"+pid;
				int tmsort = obj.getTmsort()==null?0:obj.getTmsort();
				if(maxSortMap.containsKey(keyPx)) {
					int maxPx = maxSortMap.get(keyPx);
					if(tmsort>maxPx) {
						maxSortMap.put(keyPx, tmsort);
					}
				}else {
					maxSortMap.put(keyPx, tmsort);
				}
			}
		}
	}
	
	@Override
	protected List<Object> getPullData(String whereSql, HashMap<String,String> synParam) {
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

	
}
