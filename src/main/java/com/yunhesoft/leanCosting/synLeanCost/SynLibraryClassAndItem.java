package com.yunhesoft.leanCosting.synLeanCost;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costlibraryclass;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costlibraryitem;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryclassVo;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryitemVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostlibraryclassService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostlibraryitemService;
import com.yunhesoft.system.synchronous.utils.SynModel;


/**
 *	介质信息-04
 * <AUTHOR>
 * @date 2023-11-08
 */
@Service
public class SynLibraryClassAndItem extends SynModel {
	
	@Autowired
	private ICostlibraryclassService libraryclassService;
	
	@Autowired
	private ICostlibraryitemService libraryitemService;
	
	private String synUseFlagValue = synParam.get("synUseFlagValue"); //外部数据同步使用标识值
	
	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList, HashMap<String,String> synParam) {
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>(); //失败返回的数据
		if(StringUtils.isNotEmpty(dataList)) {
			if(StringUtils.isNotEmpty(synParam)) {
				this.synUseFlagValue = synParam.get("synUseFlagValue"); //外部数据同步使用标识值
			}
			if(StringUtils.isEmpty(this.synUseFlagValue)) {
				this.synUseFlagValue = "X"; //默认标识X为使用
			}
			List<Costlibraryclass> addClassList = new ArrayList<Costlibraryclass>();
			List<Costlibraryclass> updClassList = new ArrayList<Costlibraryclass>();
			List<Costlibraryitem> addItemList = new ArrayList<Costlibraryitem>();
			List<Costlibraryitem> updItemList = new ArrayList<Costlibraryitem>();
			
			HashMap<String, String> hasClassMap = new HashMap<String, String>(); //父级是否存在
			
			//数据库中存在的项目
			HashMap<String, Integer> hasMdmCodeMap = new HashMap<String, Integer>();
			HashMap<String, Integer> maxSortItemMap = new HashMap<String, Integer>();
			HashMap<String, CostlibraryitemVo> queryItemMap = new HashMap<String, CostlibraryitemVo>();
			List<CostlibraryitemVo> queryItemList = libraryitemService.getData(null);
			if(StringUtils.isNotEmpty(queryItemList)) {
				this.getItemDataMap(queryItemList, queryItemMap, maxSortItemMap);
			}
			
			//数据库中存在的分类
			HashMap<String, Integer> maxSortClassMap = new HashMap<String, Integer>(); //父id的最大序号Map
			HashMap<String, CostlibraryclassVo> queryClassMap = new HashMap<String, CostlibraryclassVo>(); //外部数据编码为key（数据中）
			List<CostlibraryclassVo> queryClassList = libraryclassService.getData(null);
			if(StringUtils.isNotEmpty(queryClassList)) {
				this.getClassDataMap(queryClassList, queryClassMap, maxSortClassMap);
			}
			
			for(HashMap<String, Object> temp : dataList) {
				CostlibraryitemVo synVo = ObjUtils.convertToObject(CostlibraryitemVo.class, temp);
				if(synVo != null) { //数据读取成功
					String mdmCode = synVo.getMdmCode();
					if(StringUtils.isNotEmpty(mdmCode)&&!hasMdmCodeMap.containsKey(mdmCode)) {
						hasMdmCodeMap.put(mdmCode, 1);
						int tmused = 1;
						String rowFlag = synVo.getRowFlag();
						if(!synUseFlagValue.equals(rowFlag)) { //删除
							tmused = 0;
						}
						if(tmused==0) { //删除
							if(StringUtils.isNotEmpty(queryItemMap)&&queryItemMap.containsKey(mdmCode)) {
								CostlibraryitemVo obj = queryItemMap.get(mdmCode);
								Costlibraryitem delObj = new Costlibraryitem();
								BeanUtils.copyProperties(obj, delObj); // 赋予返回对象
								delObj.setTmused(0);
								updItemList.add(delObj);
							}
						}else { //新增或修改
							synVo.setTmused(tmused);
							
							String pid = synVo.getPid();
							String pname = synVo.getPname();
							String pid_data = "";
							if(StringUtils.isNotEmpty(pid)) {
								//判断父级是否存在，如果存在有原来的id，不存在用新id，作为子级记录的pid
								if(StringUtils.isNotEmpty(queryClassMap)&&queryClassMap.containsKey(pid)) {
									CostlibraryclassVo pVo = queryClassMap.get(pid);
									pid_data = pVo.getId();
									String ccname_data = pVo.getCcname();
									if(StringUtils.isNotEmpty(pname)&&!pname.equals(ccname_data)&&!hasClassMap.containsKey(pid)) {
										pVo.setCcname(pname);
										Costlibraryclass pObj = new Costlibraryclass();
										BeanUtils.copyProperties(pVo, pObj); // 赋予返回对象
										updClassList.add(pObj);
									}
								}else {
									if(!hasClassMap.containsKey(pid)) {
										hasClassMap.put(pid, pid);
										int maxPx = 0;
										if(StringUtils.isNotEmpty(maxSortClassMap)&&maxSortClassMap.containsKey("nullStr")) {
											maxPx = maxSortClassMap.get("nullStr");
										}
										maxPx += 1;
										maxSortClassMap.put("nullStr", maxPx);
										pid_data = TMUID.getUID();
										Costlibraryclass pObj = new Costlibraryclass();
										pObj.setId(pid_data);
										pObj.setPid(null);
										pObj.setMdmCode(pid);
										if(StringUtils.isNotEmpty(pname)) {
											pObj.setCcname(pname);
										}else {
											pObj.setCcname(pid);
										}
										pObj.setTmused(1);
										pObj.setTmsort(maxPx);
										addClassList.add(pObj);
										CostlibraryclassVo pVo = new CostlibraryclassVo();
										BeanUtils.copyProperties(pObj, pVo); // 赋予返回对象
										queryClassMap.put(pid, pVo);
									}
								}
							}
							
							if(StringUtils.isNotEmpty(pid_data)) { //有父级分类再执行项目数据
								synVo.setPid(pid_data);
								
								String ccname = synVo.getCcname();
								if(StringUtils.isNotEmpty(queryItemMap)&&queryItemMap.containsKey(mdmCode)) {
									CostlibraryitemVo itemVo = queryItemMap.get(mdmCode);
									String id = itemVo.getId();
									Costlibraryitem updObj = new Costlibraryitem();
									BeanUtils.copyProperties(synVo, updObj); // 赋予返回对象
									updObj.setId(id);
									updObj.setCiname(ccname);
									updItemList.add(updObj);
								}else {
									if(StringUtils.isNotEmpty(maxSortItemMap)&&maxSortItemMap.containsKey(pid_data)) {
										int maxPx = maxSortItemMap.get(pid_data);
										maxPx += 1;
										synVo.setTmsort(maxPx);
										maxSortItemMap.put(pid_data, maxPx);
									}else {
										synVo.setTmsort(1);
										maxSortItemMap.put(pid_data, 1);
									}
									Costlibraryitem addObj = new Costlibraryitem();
									BeanUtils.copyProperties(synVo, addObj); // 赋予返回对象
									addObj.setId(TMUID.getUID());
									addObj.setCiname(ccname);
									addObj.setSumtype(0); //默认累加
									addObj.setDataSources("综合数据平台");
									addItemList.add(addObj);
								}
							}
						}
					}else {
						result.add(temp);//记录到失败数据中
					}
				}else {
					result.add(temp);//记录到失败数据中
				}
			}
			if(StringUtils.isNotEmpty(addClassList)||StringUtils.isNotEmpty(updClassList)) {
				String ret = libraryclassService.saveLibraryClassData(addClassList, updClassList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
			if(StringUtils.isNotEmpty(addItemList)||StringUtils.isNotEmpty(updItemList)) {
				String ret = libraryitemService.saveLibraryItemData(addItemList, updItemList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取分类数据Map
	 * @param list
	 * @param dataMap
	 * @param maxSortMap
	 */
	private void getClassDataMap(List<CostlibraryclassVo> list, HashMap<String, CostlibraryclassVo> dataMap, HashMap<String, Integer> maxSortMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, CostlibraryclassVo>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostlibraryclassVo obj = list.get(i);
				String mdmCode = obj.getMdmCode();
				if(StringUtils.isNotEmpty(mdmCode)&&!dataMap.containsKey(mdmCode)) {
					dataMap.put(mdmCode, obj);
				}
				String pid = obj.getPid();
				if(StringUtils.isEmpty(pid)) {
					pid = "nullStr";
				}
				int tmsort = obj.getTmsort()==null?0:obj.getTmsort();
				if(maxSortMap.containsKey(pid)) {
					int maxPx = maxSortMap.get(pid);
					if(tmsort>maxPx) {
						maxSortMap.put(pid, tmsort);
					}
				}else {
					maxSortMap.put(pid, tmsort);
				}
			}
		}
	}
	
	/**
	 *	获取项目数据Map
	 * @param list
	 * @param dataMap
	 * @param maxSortMap
	 */
	private void getItemDataMap(List<CostlibraryitemVo> list, HashMap<String, CostlibraryitemVo> dataMap, HashMap<String, Integer> maxSortMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, CostlibraryitemVo>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostlibraryitemVo obj = list.get(i);
				String mdmCode = obj.getMdmCode();
				if(StringUtils.isNotEmpty(mdmCode)&&!dataMap.containsKey(mdmCode)) {
					dataMap.put(mdmCode, obj);
				}
				String pid = obj.getPid();
				if(StringUtils.isEmpty(pid)) {
					pid = "nullStr";
				}
				int tmsort = obj.getTmsort()==null?0:obj.getTmsort();
				if(maxSortMap.containsKey(pid)) {
					int maxPx = maxSortMap.get(pid);
					if(tmsort>maxPx) {
						maxSortMap.put(pid, tmsort);
					}
				}else {
					maxSortMap.put(pid, tmsort);
				}
			}
		}
	}
	
	@Override
	protected List<Object> getPullData(String whereSql, HashMap<String,String> synParam) {
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

	
}
