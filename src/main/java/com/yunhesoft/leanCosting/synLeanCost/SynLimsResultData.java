package com.yunhesoft.leanCosting.synLeanCost;

import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.Maths;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.CostEnvironmentConfig;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.calcLogic.IGetLimsDataService;
import com.yunhesoft.leanCosting.calcLogic.TeamLimsService;
import com.yunhesoft.leanCosting.samplePlan.entity.po.ProdLimsResult;
import com.yunhesoft.system.synchronous.utils.SynModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * LIMS化验结果-12
 * 
 * <AUTHOR>
 * @date 2023-12-12
 */
@Service
public class SynLimsResultData extends SynModel {

	@Autowired
	private IGetLimsDataService glds;
	@Autowired
	private ICostToolService cts;
	@Autowired
	private TeamLimsService itlsi;

	private HashMap<String, ProdLimsResult> alm = new HashMap<String, ProdLimsResult>();

	private String synDelFlagValue = synParam.get("synDelFlagValue"); // 删除标识值

	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList,
			HashMap<String, String> synParam) {
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>(); // 失败返回的数据
		if (StringUtils.isNotEmpty(dataList)) {
			if (StringUtils.isNotEmpty(synParam)) {
				this.synDelFlagValue = synParam.get("synDelFlagValue"); // 删除标识值
			}
			if (StringUtils.isEmpty(this.synDelFlagValue)) {
				this.synDelFlagValue = "T"; // 默认标识T为删除
			}
			this.version = "";
			Integer ts = -5;
			List<CostEnvironmentConfig> maxtsl = this.cts.getCostEnvironmentConfigList("LIMS",
					"DaysBetweenSampleAndReleased");
			if (maxtsl != null && maxtsl.size() > 0) {
				CostEnvironmentConfig tsl = maxtsl.get(0);
				String sts = tsl.getParamName();
				if (Coms.isNum(sts)) {
					ts = 0 - Integer.parseInt(sts);
				}
			}
			Date curdt = DateTimeUtils.getNowDate();
			Date xdsj = DateTimeUtils.addDays(curdt, ts);
			String key, sampledate, releasedon, drawtime, pjsj;
			Date cysj;
			Long zdsj = (long) Maths.abs(ts * 24 * 3600);
			Set<Map.Entry<String, ProdLimsResult>> entries = this.alm.entrySet();
			Iterator<Map.Entry<String, ProdLimsResult>> iterator = entries.iterator();
			while (iterator.hasNext()) {
				Map.Entry<String, ProdLimsResult> next = iterator.next();
				ProdLimsResult pr = next.getValue();
				sampledate = pr.getSampledDate();
				if (StringUtils.isEmpty(sampledate)) {
					continue;// 无取样日期的不用
				}
				Long sjc = Maths.abs(DateTimeUtils.diffSecond(curdt, DateTimeUtils.parseDateTime(sampledate)));
				if (sjc > zdsj) {
					iterator.remove();
				}
			}

			List<ProdLimsResult> addList = new ArrayList<ProdLimsResult>();
			List<ProdLimsResult> upList = new ArrayList<ProdLimsResult>();
			int count = dataList.size();
			for (int i = 0; count > i; i++) {
				HashMap<String, Object> temp = dataList.get(i);
				ProdLimsResult synVo = ObjUtils.convertToObject(ProdLimsResult.class, temp);
				if (synVo != null) { // 数据读取成功
					drawtime = synVo.getDrawtime();
					if (StringUtils.isEmpty(drawtime)) {
						continue;// 无取样时间的不用
					}
					sampledate = synVo.getSampledDate();
					if (StringUtils.isEmpty(sampledate)) {
						continue;// 无取样日期的不用
					}
					synVo.setOSampledDate(sampledate);
					if (sampledate.length() >= 10) {
						if (drawtime.length() > 10) {
							pjsj = sampledate.substring(0, 10) + drawtime.substring(10);
						} else {
							continue;// 采样时间不满足格式要求，不用
						}
					} else {
						continue;// 不满足日期格式，不用
					}
					cysj = DateTimeUtils.parseDateTime(pjsj);
					synVo.setSampledDate(pjsj);
					if (DateTimeUtils.bjDate(cysj, xdsj) >= 0) {
						// 采样时间比合理采集时间起点晚，这时使用采样数据
						String processUnitName = synVo.getProcessunit();
						if (StringUtils.isEmpty(processUnitName)) {
							continue;
						}
						String productName = synVo.getProductName();
						if (StringUtils.isEmpty(productName)) {
							continue;
						}
						String samplingPoint = synVo.getSamplingPoint();
						if (StringUtils.isEmpty(samplingPoint)) {
							continue;
						}
						String analysisName = synVo.getAnalysisName();
						if (StringUtils.isEmpty(analysisName)) {
							continue;
						}
						String analysisSubName = synVo.getItemName();
						if (StringUtils.isEmpty(analysisSubName)) {
							continue;
						}
						releasedon = synVo.getReleasedon();
						key = (new StringBuffer(pjsj).append(".").append(processUnitName).append(productName)
								.append(samplingPoint).append(analysisName).append(analysisSubName)).toString();
						if (this.alm.containsKey(key)) {
							ProdLimsResult ab = this.alm.get(key);
							ab.setDrawtime(drawtime);
							ab.setOSampledDate(sampledate);
							ab.setSampledDate(pjsj);
							ab.setFormattedEntry(synVo.getFormattedEntry());
							ab.setReleasedon(releasedon);
							ab.setResultInSpec(synVo.getResultInSpec());
							upList.add(ab);
						} else {
							synVo.setId(TMUID.getUID());
							if (StringUtils.isEmpty(releasedon)) {
								continue;// 没有返回版本的数据不用
							}
							if (StringUtils.isEmpty(this.version)) {
								this.setVersion(DateTimeUtils.formatDateTime(
										DateTimeUtils.addSeconds(DateTimeUtils.parseDateTime(releasedon), 1)));// 返回版本加1秒，避免重复采集
							} else {
								if (DateTimeUtils.bjDate(DateTimeUtils.parseDate(releasedon),
										DateTimeUtils.parseDate(this.version)) > 0) {
									this.setVersion(DateTimeUtils.formatDateTime(
											DateTimeUtils.addSeconds(DateTimeUtils.parseDateTime(releasedon), 1)));// 返回版本加1秒，避免重复采集
								}
							}
							this.alm.put(key, synVo);
							addList.add(synVo);
						}

					}
				}
			}

			if (StringUtils.isNotEmpty(addList) || StringUtils.isNotEmpty(upList)) {
				String ret = this.glds.saveLimsData(upList, addList);
				if (StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret + ";";
				} else {
					itlsi.addLimsDataToTeamResult(addList);
				}
			}
		}
		return result;
	}

	@Override
	protected List<Object> getPullData(String whereSql, HashMap<String, String> synParam) {
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

}
