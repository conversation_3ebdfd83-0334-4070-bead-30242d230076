package com.yunhesoft.leanCosting.synLeanCost;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.entity.po.ProjectType;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryitemVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostProjectTypeService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostlibraryitemService;
import com.yunhesoft.leanCosting.programConfig.entity.dto.ProgramQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramVersion;
import com.yunhesoft.leanCosting.programConfig.service.IProgramLibraryCostService;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitversion;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BeanVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostFormulaService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.synchronous.utils.SynModel;

/**
 *	核算项目-07
 * <AUTHOR>
 * @date 2023-11-09
 */
@Service
public class SynCostItem extends SynModel {
	
	@Autowired
	private IUnitMethodService methodService; // 核算相关方法
	
	@Autowired
	private ICostlibraryitemService libraryitemService;
	
	@Autowired
	private ICostProjectTypeService projectTypeService;
	
	@Autowired
	private IProgramService programService;
	
	@Autowired
	private IProgramLibraryCostService programLibraryService;
	
	@Autowired
	private EntityService entityService;
	
	@Autowired
	private ICostFormulaService formulaService;
	
	
	private String synUseFlagValue = synParam.get("synUseFlagValue"); //外部数据同步使用标识值
	
	private String tokenStr = null;
	
	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList, HashMap<String,String> synParam) {
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>(); //失败返回的数据
		if(StringUtils.isNotEmpty(dataList)) {
			if(StringUtils.isNotEmpty(synParam)) {
				this.synUseFlagValue = synParam.get("synUseFlagValue"); //外部数据同步使用标识值
			}
			if(StringUtils.isEmpty(this.synUseFlagValue)) {
				this.synUseFlagValue = "1"; //默认标识1为使用
			}
			
			List<Costclass> addClassList = new ArrayList<Costclass>();
			List<Costitem> addList = new ArrayList<Costitem>();
			List<Costitem> updList = new ArrayList<Costitem>();
			List<Costunitsampledot> addSampledotList = new ArrayList<Costunitsampledot>();
			List<Costunitsampledot> updSampledotList = new ArrayList<Costunitsampledot>();
			List<Costinstrument> addInstrumentList = new ArrayList<Costinstrument>();
			List<Costinstrument> updInstrumentList = new ArrayList<Costinstrument>();
			List<ProgramItem> addProgramList = new ArrayList<ProgramItem>();
			List<ProgramVersion> addVersionList = new ArrayList<ProgramVersion>();
			
			HashMap<String, String> hasR3dbMap = new HashMap<String, String>();
			List<Costunitsampledot> synR3dbList = new ArrayList<Costunitsampledot>();
			//同步公式数据
			Map<String, String> formula_costItem_map = new HashMap<String, String>();
			Map<String, Map<String, Costinstrument>> formula_costInstrument_map = new HashMap<String, Map<String, Costinstrument>>();
			
			//数据库中存在的项目
			HashMap<String, Integer> newItemMap = new HashMap<String, Integer>();
			HashMap<String, Integer> maxSortMap = new HashMap<String, Integer>();
			HashMap<String, Costitem> queryMap = new HashMap<String, Costitem>();
			List<Costitem> queryList = methodService.getCostitemList(null);
			if(StringUtils.isNotEmpty(queryList)) {
				this.getDataMap(queryList, queryMap, maxSortMap);
			}
			
			//核算对象数据
			HashMap<String, Costuint> unitMap = new HashMap<String, Costuint>();
			List<Costuint> unitList = methodService.getCostuintList(null);
			if(StringUtils.isNotEmpty(unitList)) {
				this.getCostUnitMap(unitList, unitMap);
			}
			
			//核算对象版本数据
			HashMap<String, String> versionMap = new HashMap<String, String>();
			List<Costunitversion> versionList = methodService.getCostunitversionList(null);
			if(StringUtils.isNotEmpty(versionList)) { //有版本数据
				this.getVersionMap(versionList, versionMap);
			}
			
			//分类数据
			HashMap<String, HashMap<String, Costclass>> classMap = new HashMap<String, HashMap<String, Costclass>>();
			List<Costclass> classList = methodService.getCostclassList(null);
			if(StringUtils.isNotEmpty(classList)) {
				this.getCostClassMap(classList, classMap);
			}
			//分类类型
			List<BeanVo> classTypeList = methodService.getCostClassTypeList();
			
			//项目库
			HashMap<String, CostlibraryitemVo> libraryitemMap = new HashMap<String, CostlibraryitemVo>();
			List<CostlibraryitemVo> libraryitemList = libraryitemService.getData(null);
			if(StringUtils.isNotEmpty(libraryitemList)) {
				this.getLibraryItemMap(libraryitemList, libraryitemMap);
			}
			
			//采集点
			HashMap<String, Integer> newSampledotMap = new HashMap<String, Integer>();
			HashMap<String, Integer> sampledotMaxSortMap = new HashMap<String, Integer>();
			HashMap<String, Costunitsampledot> sampledotMap = new HashMap<String, Costunitsampledot>();
			MethodQueryDto dto = new MethodQueryDto();
			dto.setCtype("1"); //成本仪表
			List<Costunitsampledot> sampledotList = methodService.getCostunitsampledotList(dto);
			if(StringUtils.isNotEmpty(sampledotList)) {
				this.getSampledotMap(sampledotList, sampledotMap, sampledotMaxSortMap);
			}
			
			//成本仪表
			HashMap<String, Integer> newInstrumentMap = new HashMap<String, Integer>();
			HashMap<String, Integer> instrumentMaxSortMap = new HashMap<String, Integer>();
			HashMap<String, Costinstrument> instrumentMap = new HashMap<String, Costinstrument>();
			HashMap<String, List<Costinstrument>> instrumentGroupMap = new HashMap<String, List<Costinstrument>>();
			List<Costinstrument> instrumentList = methodService.getCostinstrumentList(null);
			if(StringUtils.isNotEmpty(instrumentList)) {
				this.getInstrumentMap(instrumentList, instrumentMap, instrumentMaxSortMap, instrumentGroupMap);
			}
			
			//侧线Map
			HashMap<String, String> sidelineMap = this.getSidelineMap();
			//侧线对照的采集点Map
			HashMap<String, HashMap<String, String>> measureNodeMap = this.getMeasureNodeMap();
			
			//方案数据
			HashMap<String, Integer> programMaxSortMap = new HashMap<String, Integer>();
			HashMap<String, ProgramItem> programMap = new HashMap<String, ProgramItem>();
			ProgramQueryDto programDto = new ProgramQueryDto();
			programDto.setPId("root");
			List<ProgramItem> programList = programService.getProgramItemList(programDto);
			if(StringUtils.isNotEmpty(programList)) {
				this.getProgramItemMap(programList, programMap, programMaxSortMap);
			}
			
			// 查询运行状态
			String deviceStatus = "";
			List<ProjectType> typeList = projectTypeService.getData();
			if(StringUtils.isNotEmpty(typeList)) {
				for (int i = 0; i < typeList.size(); i++) {
					ProjectType typeObj = typeList.get(i);
					int isDef = typeObj.getISDefault();
					if(isDef==1) {
						deviceStatus = typeObj.getId();
						break;
					}
				}
			}
			
			//先遍历一下数据，判断是否有“使用”的核算项目（因为数据中同时存在“使用”和“删除”的记录），没有“使用”的核算项目再“删除”核算项目
			HashMap<String, String> hasUseItemMap = new HashMap<String, String>();
			List<CostitemVo> synList = new ArrayList<CostitemVo>();
			for(HashMap<String, Object> temp : dataList) {
				CostitemVo synVo = ObjUtils.convertToObject(CostitemVo.class, temp);
				if(synVo != null) { //数据读取成功
					Integer tmUsed = synVo.getTmused();
					if(synUseFlagValue.equals(String.valueOf(tmUsed))) { //使用
						tmUsed = 1;
					}else {
						tmUsed = 0;
					}
					synVo.setTmused(tmUsed);
					synList.add(synVo);
					
					String unitid_syn = synVo.getUnitid();
					String itemid_syn = synVo.getItemid();
					if(StringUtils.isNotEmpty(unitid_syn)&&StringUtils.isNotEmpty(itemid_syn)&&tmUsed==1) {
						String key = unitid_syn+"_"+itemid_syn;
						if(!hasUseItemMap.containsKey(key)) {
							hasUseItemMap.put(key, "1");
						}
					}
				}else {
					result.add(temp);//记录到失败数据中
				}
			}
			
			if(StringUtils.isNotEmpty(synList)) {
				for (int m = 0; m < synList.size(); m++) {
					CostitemVo synVo = synList.get(m);
					if(synVo != null) { //数据读取成功
						String unitid_syn = synVo.getUnitid();
						String unitid = "";
						String unittype = "";
						if(StringUtils.isNotEmpty(unitid_syn)&&StringUtils.isNotEmpty(unitMap)&&unitMap.containsKey(unitid_syn)) {
							Costuint unitObj = unitMap.get(unitid_syn);
							unitid = unitObj.getId();
							unittype = unitObj.getUnittype();
						}
						if(StringUtils.isNotEmpty(unitid)) { //有此核算对象
							synVo.setUnitid(unitid);
							if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(versionMap)&&versionMap.containsKey(unitid)) {
								String begintime = versionMap.get(unitid);
								if(StringUtils.isNotEmpty(begintime)) { //有版本日期
									synVo.setBegintime(begintime);
									String pid_syn = synVo.getPid();
									if(StringUtils.isNotEmpty(pid_syn)) { //判断在哪个分类下新增项目
										//用到了分类，判断是否存在分类数据，不存在时初始化；已存在直接使用；
										HashMap<String, Costclass> classDataMap = new HashMap<String, Costclass>();
										if(classMap.containsKey(unitid+"_"+begintime)) {
											classDataMap = classMap.get(unitid+"_"+begintime);
										}else { //不存在分类数据，初始化
											if(StringUtils.isNotEmpty(classTypeList)) {
								        		for (int i = 0; i < classTypeList.size(); i++) {
								        			String classType = classTypeList.get(i).getKey();
								            		// 默认新增分类【按照分类类型生成】
								                	Costclass classObj = new Costclass();
								                	classObj.setId(TMUID.getUID());
								                	classObj.setPid("root");
								                	classObj.setCcname(classType);
								                	classObj.setMemo("");
								                	classObj.setCctype(classType);
								                	classObj.setUnitid(unitid);
								                	classObj.setBegintime(begintime);
								                	classObj.setTmused(1);
								                	classObj.setTmsort(i+1);
								                	addClassList.add(classObj);
								                	if(!classDataMap.containsKey(unitid+"_"+begintime+"_"+classType)) {
								                		classDataMap.put(unitid+"_"+begintime+"_"+classType, classObj);
								                	}
												}
								        		classMap.put(unitid+"_"+begintime, classDataMap);
								        	}
										}
										
										String key = "";
										if("进".equals(pid_syn)) { //原料
											key = unitid+"_"+begintime+"_原料";
										}else if("出".equals(pid_syn)) { //产品
											key = unitid+"_"+begintime+"_产品";
										}
										if(StringUtils.isNotEmpty(key)&&StringUtils.isNotEmpty(classDataMap)&&classDataMap.containsKey(key)) { //有此分类
											String classId = classDataMap.get(key).getId();
											synVo.setPid(classId);
											//项目库中获取项目编码
											String itemid_syn = synVo.getItemid();
											if(StringUtils.isNotEmpty(itemid_syn)&&StringUtils.isNotEmpty(libraryitemMap)&&libraryitemMap.containsKey(itemid_syn)) {
												CostlibraryitemVo libraryitemVo = libraryitemMap.get(itemid_syn);
												String itemid = libraryitemVo.getId();
												synVo.setItemid(itemid);
												String itemname = libraryitemVo.getCcname();
								                int sumtype = libraryitemVo.getSumtype();
								                String unit = libraryitemVo.getUnit() == null ? "" : libraryitemVo.getUnit();
								                String erpcode = libraryitemVo.getErpcode() == null ? "" : libraryitemVo.getErpcode();
								                String dataKey = unitid+"_"+begintime+"_"+classId+"_"+itemid;
								                
								                Integer tmUsed = synVo.getTmused()==null?0:synVo.getTmused();
								                if(tmUsed==0) { //删除（接口数据中没有使用的项目，再删除）
													if(!hasUseItemMap.containsKey(unitid_syn+"_"+itemid_syn)&&StringUtils.isNotEmpty(queryMap)&&queryMap.containsKey(dataKey)) {
														Costitem delObj = queryMap.get(dataKey);
														delObj.setTmused(0);
														updList.add(delObj);
														//删除项目下的仪表
														String groupKey = unitid+"_"+begintime+"_"+itemid;
														if(StringUtils.isNotEmpty(instrumentGroupMap)&&instrumentGroupMap.containsKey(groupKey)) {
															List<Costinstrument> instrumentGroupList = instrumentGroupMap.get(groupKey);
															if(StringUtils.isNotEmpty(instrumentGroupList)) {
																for (int j = 0; j < instrumentGroupList.size(); j++) {
																	Costinstrument instrumentGroupObj = instrumentGroupList.get(j);
																	instrumentGroupObj.setTmused(0);
																	updInstrumentList.add(instrumentGroupObj);
																	
																	String delInstrumentKey = groupKey+"_"+instrumentGroupObj.getDotid();
																	if(StringUtils.isNotEmpty(instrumentMap)&&instrumentMap.containsKey(delInstrumentKey)) {
																		instrumentMap.remove(delInstrumentKey);
																	}
																}
															}
														}
													}
												}else {
													if(StringUtils.isNotEmpty(queryMap)&&queryMap.containsKey(dataKey)) { //修改
														Costitem updObj = queryMap.get(dataKey);
														synVo.setId(updObj.getId());
														if(!newItemMap.containsKey(dataKey)) {
															BeanUtils.copyProperties(synVo, updObj); // 赋予返回对象
															updList.add(updObj);
														}
													}else { //新增
														int maxPx = 0;
														String pxKey = unitid+"_"+begintime+"_"+classId;
														if(StringUtils.isNotEmpty(maxSortMap)&&maxSortMap.containsKey(pxKey)) {
															maxPx = maxSortMap.get(pxKey);
														}
														maxPx += 1;
														maxSortMap.put(pxKey, maxPx);
														synVo.setId(TMUID.getUID());
														synVo.setItemname(itemname);
														synVo.setItemunit(unit); //计量单位
														synVo.setErpcode(erpcode); //erp编码
														synVo.setItemprice(0d); //单价
														synVo.setConversionfactor(1d); //换算系数
														synVo.setEnergyfactor(0d); //能耗系数
														synVo.setApportionmentfactor(0d); //分摊因子
														synVo.setMaterialSupply(1); //物料提供者
														synVo.setFeedShowType(0); //项目显示方式
														synVo.setSumtype(sumtype); //汇总方式
														synVo.setComparetype(0); //比较方式
														synVo.setCalcprice(0); //推导价格
														synVo.setUsecalcprice(0); //参与价格推导
														synVo.setPricefactor(1d); //价格系数
														synVo.setPriceSource(0); //价格来源：0、价格管理；1、数据源
														synVo.setTdsAlias(""); //数据源别名
														synVo.setTmsort(maxPx);
														Costitem addObj = new Costitem();
														BeanUtils.copyProperties(synVo, addObj); // 赋予返回对象
														addList.add(addObj);
														queryMap.put(dataKey, addObj);
														newItemMap.put(dataKey, 1);
													}
													
													//根据侧线id，生成采集点及仪表数据
													String lineCode = synVo.getDotid();
													if(StringUtils.isNotEmpty(lineCode)&&tmUsed==1) {
														if(StringUtils.isNotEmpty(sidelineMap)&&sidelineMap.containsKey(lineCode)) {
															String areaForm = sidelineMap.get(lineCode);
															if(StringUtils.isNotEmpty(areaForm)) { //从公式中解析仪表位号，生成采集点和仪表数据
																HashMap<String, Integer> hasYbMap = new HashMap<String, Integer>();
																String regex = "\\{(.*?)\\}";
																Pattern pattern = Pattern.compile(regex);
																Matcher matcher = pattern.matcher(areaForm);
																while(matcher.find()) {
																	String tagNumber = matcher.group(1);
																	if(StringUtils.isNotEmpty(tagNumber)&&!hasYbMap.containsKey(tagNumber)) {
																		hasYbMap.put(tagNumber, 1); //从公式中取出仪表位号（去重复）
																		
																		HashMap<String, String> dotMap = new HashMap<String, String>();
																		if(StringUtils.isNotEmpty(measureNodeMap)&&measureNodeMap.containsKey(tagNumber)) {
																			dotMap = measureNodeMap.get(tagNumber);
																		}
																		//采集点数据
																		String sampledotKey = unitid+"_"+begintime+"_"+tagNumber;
																		String sampledotId = ""; //采集点id
																		if(sampledotMap.containsKey(sampledotKey)) {
																			Costunitsampledot dotObj = sampledotMap.get(sampledotKey);
																			sampledotId = dotObj.getId();
																			if(StringUtils.isNotEmpty(dotMap)&&!newSampledotMap.containsKey(sampledotKey)) {
																				String nodeAlias = dotMap.get("NODE_ALIAS");  //仪表位号
																				String nodeName = dotMap.get("NODE_NAME");  //采集点名称
																				String tag = dotMap.get("TAG");  //实时仪表
																				//获取同步r3db的采集点数据
																				Costunitsampledot savedot = new Costunitsampledot();
																				savedot.setName(nodeName);
																				savedot.setDatasource(tag);
																				savedot.setTmused(1);
																				savedot.setCtype("1");
																				methodService.getSynR3dbSampledotData(dotObj, savedot, synR3dbList, hasR3dbMap);
																			
																				dotObj.setName(nodeName);
																				dotObj.setTagnumber(nodeAlias); //仪表位号
																				dotObj.setDatasource(tag); //数据来源
																				updSampledotList.add(dotObj);
																				sampledotMap.put(sampledotKey, dotObj);
																			}
																		}else {
																			if(StringUtils.isNotEmpty(dotMap)) {
																				String sampledotPxKey = unitid+"_"+begintime;
																				int sampledotMaxPx = 0;
																				if(StringUtils.isNotEmpty(sampledotMaxSortMap)&&sampledotMaxSortMap.containsKey(sampledotPxKey)) {
																					sampledotMaxPx = sampledotMaxSortMap.get(sampledotPxKey);
																				}
																				sampledotMaxPx += 1;
																				sampledotMaxSortMap.put(sampledotPxKey, sampledotMaxPx);
																				
																				String nodeAlias = dotMap.get("NODE_ALIAS");  //仪表位号
																				String nodeName = dotMap.get("NODE_NAME");  //采集点名称
																				String tag = dotMap.get("TAG");  //实时仪表
																				
																				sampledotId = TMUID.getUID();
																				Costunitsampledot dotObj = new Costunitsampledot();
																				dotObj.setUnitid(unitid);
																				dotObj.setBegintime(begintime);
																				dotObj.setId(sampledotId);
																				dotObj.setName(nodeName);
																				dotObj.setTagnumber(nodeAlias); //仪表位号
																				dotObj.setDatasource(tag); //数据来源
																				dotObj.setSdUnit(null); //计量单位
																				dotObj.setPid("root");
																				dotObj.setSourceype("1"); //来源类型：1、influxdb；2、数据源；3、手工填写；
																				dotObj.setCtype("1"); //采集类型：1、成本仪表；2、控制指标；3、lims指标；
																				dotObj.setTmused(1);
																				dotObj.setTmsort(sampledotMaxPx);
																				dotObj.setMdmCode(sampledotId); //存数据id，没有实际意义（方便判断是外部接口数据）
																				addSampledotList.add(dotObj);
																				sampledotMap.put(sampledotKey, dotObj);
																				newSampledotMap.put(sampledotKey, 1);
																				//获取同步r3db的采集点数据
																				methodService.getSynR3dbSampledotData(dotObj, null, synR3dbList, hasR3dbMap);
																			}
																		}
																		
																		//仪表数据
																		if(StringUtils.isNotEmpty(sampledotId)&&StringUtils.isNotEmpty(dotMap)) {
																			String nodeAlias = dotMap.get("NODE_ALIAS");  //仪表位号
																			String nodeName = dotMap.get("NODE_NAME");  //采集点名称
																			String upSpan = dotMap.get("UP_SPAN");  //量程上限
																			String dowmSpan = dotMap.get("DOWM_SPAN");  //量程下限
																			double instrumentRange = 0d;
																			if(StringUtils.isNotEmpty(upSpan)&&"null".equals(upSpan)) {
																				instrumentRange = Double.valueOf(upSpan);
																			}
																			double metDownlim = 0d;
																			if(StringUtils.isNotEmpty(dowmSpan)&&"null".equals(dowmSpan)) {
																				metDownlim = Double.valueOf(dowmSpan);
																			}
																			String instrumentKey = unitid+"_"+begintime+"_"+synVo.getId()+"_"+sampledotId;
																			Costinstrument instrumentObj = new Costinstrument();
																			if(instrumentMap.containsKey(instrumentKey)) {
																				instrumentObj = instrumentMap.get(instrumentKey);
																				if(!newInstrumentMap.containsKey(instrumentKey)) {
																					instrumentObj.setName(nodeName);
																					instrumentObj.setInstrumentRange(instrumentRange);
																					instrumentObj.setTagnumber(nodeAlias);
																					instrumentObj.setMetDownlim(metDownlim);
																					updInstrumentList.add(instrumentObj);
																				}
																			}else {
																				instrumentObj.setName(nodeName);
																				instrumentObj.setInstrumentRange(instrumentRange);
																				instrumentObj.setTagnumber(nodeAlias);
																				instrumentObj.setMetDownlim(metDownlim);
																				String xhKey = unitid+"_"+begintime+"_"+synVo.getId();
																				int maxPx = 0;
																				if(StringUtils.isNotEmpty(instrumentMaxSortMap)&&instrumentMaxSortMap.containsKey(xhKey)) {
																					maxPx = instrumentMaxSortMap.get(xhKey);
																				}
																				maxPx += 1;
																				instrumentMaxSortMap.put(xhKey, maxPx);
																				instrumentObj.setId(TMUID.getUID());
																				instrumentObj.setDotid(sampledotId);
																				instrumentObj.setPid(synVo.getId());
																				instrumentObj.setUnitid(unitid);
																				instrumentObj.setBegintime(begintime);
																				instrumentObj.setTmused(1);
																				instrumentObj.setTmsort(maxPx);
																				instrumentObj.setConversionfactor(1d);
																				instrumentObj.setInstrumentType(1);
																				addInstrumentList.add(instrumentObj);
																				instrumentMap.put(instrumentKey, instrumentObj);
																				newInstrumentMap.put(instrumentKey, 1);
																			}
																			
																			//整理初始化公式数据
																			if(StringUtils.isNotEmpty(nodeAlias)) {
																				String costItemId = synVo.getId();
																				if(StringUtils.isNotEmpty(costItemId)) {
																					if(formula_costInstrument_map.containsKey(costItemId)) {
																						Map<String, Costinstrument> map = formula_costInstrument_map.get(costItemId);
																						if(!map.containsKey(nodeAlias)) {
																							map.put(nodeAlias, instrumentObj);
																							formula_costInstrument_map.put(costItemId, map);
																						}
																					}else {
																						Map<String, Costinstrument> map = new HashMap<String, Costinstrument>();
																						map.put(nodeAlias, instrumentObj);
																						formula_costInstrument_map.put(costItemId, map);
																					}
																					if(!formula_costItem_map.containsKey(costItemId)) {
																						formula_costItem_map.put(costItemId, areaForm);
																					}
																				}
																			}
																		}
																	}
																}
															}
														}
													}
													
													//生成默认方案数据
													String programId = synVo.getProgramId();
													String programName = synVo.getProgramName();
													if(StringUtils.isNotEmpty(programId)&&StringUtils.isNotEmpty(programName)&&StringUtils.isNotEmpty(unittype)) {
														if(!programMap.containsKey(unittype+"_"+programId)) {
															//生成默认方案
															String xhKey = unittype+"_root";
															int maxPx = 0;
															if(StringUtils.isNotEmpty(programMaxSortMap)&&programMaxSortMap.containsKey(xhKey)) {
																maxPx = programMaxSortMap.get(xhKey);
															}
															maxPx += 1;
															programMaxSortMap.put(xhKey, maxPx);
															String newProgramId = TMUID.getUID();
															ProgramItem piObj = new ProgramItem();
															piObj.setId(newProgramId);
															piObj.setPId("root");
															piObj.setPiName(programName);
															piObj.setMemo("");
															piObj.setDeviceType(unittype);
															piObj.setDeviceStatus(deviceStatus); //设备状态
															piObj.setTmUsed(1);
															piObj.setTmSort(maxPx);
															piObj.setMdmCode(programId);
															addProgramList.add(piObj);
															programMap.put(unittype+"_"+programId, piObj);
															
															//方案版本
															String pVersion = DateTimeUtils.getNowYear() + "-01-01";
															ProgramVersion versionObj = new ProgramVersion();
															versionObj.setId(TMUID.getUID());
															versionObj.setProjectDataId(newProgramId);
															versionObj.setPVersion(pVersion);
															addVersionList.add(versionObj);
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
			
			this.errorInfo = "";
			if("".equals(this.errorInfo)&&StringUtils.isNotEmpty(addClassList)) {
				String ret = methodService.saveDataCostclass(addClassList, null, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
			if("".equals(this.errorInfo)&&(StringUtils.isNotEmpty(addList)||StringUtils.isNotEmpty(updList))) {
				String ret = methodService.saveDataCostitem(addList, updList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
			if("".equals(this.errorInfo)&&(StringUtils.isNotEmpty(addSampledotList)||StringUtils.isNotEmpty(updSampledotList))) {
				String ret = methodService.saveSampledotData(addSampledotList, updSampledotList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}else { //保存成功，同步R3DB
					if(StringUtils.isNotEmpty(synR3dbList)) {
						ret = methodService.synR3dbDataBySampledot(synR3dbList, tokenStr);
						if(StringUtils.isNotEmpty(ret)) {
							this.errorInfo += ret+";";
						}
					}
				}
			}
			if("".equals(this.errorInfo)&&(StringUtils.isNotEmpty(addInstrumentList)||StringUtils.isNotEmpty(updInstrumentList))) {
				String ret = methodService.saveDataCostinstrument(addInstrumentList, updInstrumentList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
			if("".equals(this.errorInfo)&&StringUtils.isNotEmpty(addProgramList)) {
				String ret = programService.saveDataProgramItem(addProgramList, null, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
			if("".equals(this.errorInfo)&&StringUtils.isNotEmpty(addVersionList)) {
				String ret = programLibraryService.saveDataProgramVersion(addVersionList, null, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
			if("".equals(this.errorInfo)&&StringUtils.isNotEmpty(formula_costInstrument_map)) {
				try { //调用初始化公式接口（赵维民）
					formulaService.initCostItemHxl(formula_costItem_map, formula_costInstrument_map);
				}catch(Exception e) {
					e.printStackTrace();
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取数据Map
	 * @param list
	 * @param dataMap
	 * @param maxSortMap
	 */
	private void getDataMap(List<Costitem> list, HashMap<String, Costitem> dataMap, HashMap<String, Integer> maxSortMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, Costitem>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costitem obj = list.get(i);
				String unitid = obj.getUnitid();
				String begintime = obj.getBegintime();
				String classId = obj.getPid();
				String itemid = obj.getItemid();
				String dataKey = unitid+"_"+begintime+"_"+classId+"_"+itemid;
				if(!dataMap.containsKey(dataKey)) {
					dataMap.put(dataKey, obj);
				}
				String pxKey = unitid+"_"+begintime+"_"+classId;
				int tmsort = obj.getTmsort()==null?0:obj.getTmsort();
				if(maxSortMap.containsKey(pxKey)) {
					int maxPx = maxSortMap.get(pxKey);
					if(tmsort>maxPx) {
						maxSortMap.put(pxKey, tmsort);
					}
				}else {
					maxSortMap.put(pxKey, tmsort);
				}
			}
		}
	}
	
	/**
	 *	获取核算单元Map
	 * @param list
	 * @param dataMap
	 */
	private void getCostUnitMap(List<Costuint> list, HashMap<String, Costuint> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, Costuint>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costuint obj = list.get(i);
				String mdmCode = obj.getMdmCode();
				if(StringUtils.isNotEmpty(mdmCode)&&!dataMap.containsKey(mdmCode)) {
					dataMap.put(mdmCode, obj);
				}
			}
		}
	}
	
	/**
	 *	获取最大版本Map
	 * @param list
	 * @param dataMap
	 */
	private void getVersionMap(List<Costunitversion> list, HashMap<String, String> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, String>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costunitversion obj = list.get(i);
				String unitid = obj.getUnitid();
				String begintime = obj.getBegintime();
				if(!dataMap.containsKey(unitid)) {
					dataMap.put(unitid, begintime);
				}
			}
		}
	}
	
	/**
	 *	获取分类Map
	 * @param list
	 * @param dataMap
	 */
	private void getCostClassMap(List<Costclass> list, HashMap<String, HashMap<String, Costclass>> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, HashMap<String, Costclass>>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costclass obj = list.get(i);
				String unitid = obj.getUnitid();
				String begintime = obj.getBegintime();
				String cctype = obj.getCctype();
				String key1 = unitid+"_"+begintime;
				String key2 = unitid+"_"+begintime+"_"+cctype;
				if(dataMap.containsKey(key1)) {
					HashMap<String, Costclass> map = dataMap.get(key1);
					map.put(key2, obj);
					dataMap.put(key1, map);
				}else {
					HashMap<String, Costclass> map = new HashMap<String, Costclass>();
					map.put(key2, obj);
					dataMap.put(key1, map);
				}
			}
		}
	}
	
	/**
	 *	获取项目库Map
	 * @param list
	 * @param dataMap
	 */
	private void getLibraryItemMap(List<CostlibraryitemVo> list, HashMap<String, CostlibraryitemVo> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, CostlibraryitemVo>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostlibraryitemVo obj = list.get(i);
				String mdmCode = obj.getMdmCode();
				if(StringUtils.isNotEmpty(mdmCode)&&!dataMap.containsKey(mdmCode)) {
					dataMap.put(mdmCode, obj);
				}
			}
		}
	}
	
	/**
	 *	获取采集点Map
	 * @param list
	 * @param dataMap
	 * @param maxSortMap
	 */
	private void getSampledotMap(List<Costunitsampledot> list, HashMap<String, Costunitsampledot> dataMap,
		HashMap<String, Integer> maxSortMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, Costunitsampledot>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costunitsampledot obj = list.get(i);
				String unitid = obj.getUnitid()==null?"":obj.getUnitid().trim();
				String begintime = obj.getBegintime()==null?"":obj.getBegintime().trim();
				String tagNumber = obj.getTagnumber()==null?"":obj.getTagnumber().trim();
				String key = unitid+"_"+begintime+"_"+tagNumber;
				if(!dataMap.containsKey(key)) {
					dataMap.put(key, obj);
				}
				String keyPx = unitid+"_"+begintime;
				if(maxSortMap.containsKey(keyPx)) {
					int tmSort = obj.getTmsort()==null?0:obj.getTmsort();
					int maxPx = maxSortMap.get(keyPx);
					if(tmSort>maxPx) {
						maxSortMap.put(keyPx, tmSort);
					}
				}else {
					maxSortMap.put(keyPx, 1);
				}
			}
		}
	}
	
	/**
	 *	获取仪表Map
	 * @param list
	 * @param dataMap
	 * @param instrumentMaxSortMap
	 * @param instrumentGroupMap
	 */
	private void getInstrumentMap(List<Costinstrument> list, HashMap<String, Costinstrument> dataMap,
		HashMap<String, Integer> instrumentMaxSortMap, HashMap<String, List<Costinstrument>> instrumentGroupMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, Costinstrument>();
		}
		if(instrumentMaxSortMap==null) {
			instrumentMaxSortMap = new HashMap<String, Integer>();
		}
		if(instrumentGroupMap==null) {
			instrumentGroupMap = new HashMap<String, List<Costinstrument>>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costinstrument obj = list.get(i);
				String unitid = obj.getUnitid();
				String begintime = obj.getBegintime();
				String pid = obj.getPid();
				String dotid = obj.getDotid();
				String key = unitid+"_"+begintime+"_"+pid+"_"+dotid;
				if(!dataMap.containsKey(key)) {
					dataMap.put(key, obj);
				}
				String keyPx = unitid+"_"+begintime+"_"+pid;
				int tmsort = obj.getTmsort()==null?0:obj.getTmsort();
				if(instrumentMaxSortMap.containsKey(keyPx)) {
					int maxPx = instrumentMaxSortMap.get(keyPx);
					if(tmsort>maxPx) {
						instrumentMaxSortMap.put(keyPx, tmsort);
					}
				}else {
					instrumentMaxSortMap.put(keyPx, tmsort);
				}
				String groupKey = unitid+"_"+begintime+"_"+pid;
				if(instrumentGroupMap.containsKey(groupKey)) {
					List<Costinstrument> groupList = instrumentGroupMap.get(groupKey);
					groupList.add(obj);
					instrumentGroupMap.put(groupKey, groupList);
				}else {
					List<Costinstrument> groupList = new ArrayList<Costinstrument>();
					groupList.add(obj);
					instrumentGroupMap.put(groupKey, groupList);
				}
			}
		}
	}
	
	/**
	 *	获取方案Map
	 * @param list
	 * @param dataMap
	 * @param maxSortMap
	 */
	private void getProgramItemMap(List<ProgramItem> list, HashMap<String, ProgramItem> dataMap,
		HashMap<String, Integer> maxSortMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, ProgramItem>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				ProgramItem obj = list.get(i);
				String deviceType = obj.getDeviceType();
				String mdmCode = obj.getMdmCode();
				String key1 = deviceType+"_"+mdmCode;
				if(!dataMap.containsKey(key1)) {
					dataMap.put(key1, obj);
				}
				String pId = obj.getPId();
				String keyPx = deviceType+"_"+pId;
				int tmSort = obj.getTmSort()==null?0:obj.getTmSort();
				if(maxSortMap.containsKey(keyPx)) {
					int maxPx = maxSortMap.get(keyPx);
					if(tmSort>maxPx) {
						maxSortMap.put(keyPx, tmSort);
					}
				}else {
					maxSortMap.put(keyPx, tmSort);
				}
			}
		}
	}
	
	//获取侧线数据Map
	private HashMap<String, String> getSidelineMap() {
		HashMap<String, String> map = new HashMap<String, String>();
		String sql = "SELECT NODE_CODE,AREA_FORM FROM PM_SIDELINE_T WHERE USE_FLAG=1 ORDER BY NODE_CODE";
		SqlRowSet rowSet = entityService.rawQuery(sql);
		if (rowSet != null) {
			while (rowSet.next()){
				String nodeCode = rowSet.getString("NODE_CODE")==null?"":rowSet.getString("NODE_CODE").trim();
				String areaForm = rowSet.getString("AREA_FORM")==null?"":rowSet.getString("AREA_FORM").trim();
				if(StringUtils.isNotEmpty(nodeCode)&&StringUtils.isNotEmpty(areaForm)) {
					if(!map.containsKey(nodeCode)) {
						map.put(nodeCode, areaForm);
					}
				}
			}
		}
		return map;
	}
	
	//获取采集点数据Map
	private HashMap<String, HashMap<String, String>> getMeasureNodeMap() {
		HashMap<String, HashMap<String, String>> map = new HashMap<String, HashMap<String, String>>();
		String sql = "SELECT NODE_NAME,NODE_ALIAS,TAG,UP_SPAN,DOWM_SPAN FROM PM_MEASURE_NODE_T WHERE USE_FLAG=1 ORDER BY NODE_ALIAS";
		SqlRowSet rowSet = entityService.rawQuery(sql);
		if (rowSet != null) {
			while (rowSet.next()){
				String nodeAlias = rowSet.getString("NODE_ALIAS")==null?"":rowSet.getString("NODE_ALIAS").trim();
				String nodeName = rowSet.getString("NODE_NAME")==null?"":rowSet.getString("NODE_NAME").trim();
				String tag = rowSet.getString("TAG")==null?"":rowSet.getString("TAG").trim();
				String upSpan = rowSet.getString("UP_SPAN")==null?"":rowSet.getString("UP_SPAN").trim();
				String dowmSpan = rowSet.getString("DOWM_SPAN")==null?"":rowSet.getString("DOWM_SPAN").trim();
				if(StringUtils.isNotEmpty(nodeAlias)) {
					if(!map.containsKey(nodeAlias)) {
						HashMap<String, String> dataMap = new HashMap<String, String>();
						dataMap.put("NODE_ALIAS", nodeAlias); //仪表位号
						dataMap.put("NODE_NAME", nodeName); //采集点名称
						dataMap.put("TAG", tag); //实时仪表
						dataMap.put("UP_SPAN", upSpan); //量程上限
						dataMap.put("DOWM_SPAN", dowmSpan); //量程下限
						map.put(nodeAlias, dataMap);
					}
				}
			}
		}
		return map;
	}
		
	
	@Override
	protected List<Object> getPullData(String whereSql, HashMap<String,String> synParam) {
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

	
}
