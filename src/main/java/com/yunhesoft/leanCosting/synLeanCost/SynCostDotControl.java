package com.yunhesoft.leanCosting.synLeanCost;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitversion;
import com.yunhesoft.leanCosting.unitConf.entity.po.OutSystemTagNumber;
import com.yunhesoft.leanCosting.unitConf.entity.po.OutSystemUnitCompare;
import com.yunhesoft.leanCosting.unitConf.service.IOutSystemService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.synchronous.utils.SynModel;

/**
 *	控制指标采集点-09
 * <AUTHOR>
 * @date 2023-11-14
 */
@Service
public class SynCostDotControl extends SynModel {
	
	@Autowired
	private IOutSystemService outSystemService;
	
	@Autowired
	private IUnitMethodService methodService; // 核算相关方法
	
	private String synUseFlagValue = synParam.get("synUseFlagValue"); //外部数据同步使用标识值
	
	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList, HashMap<String,String> synParam) {
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>(); //失败返回的数据
		if(StringUtils.isNotEmpty(dataList)) {
			if(StringUtils.isNotEmpty(synParam)) {
				this.synUseFlagValue = synParam.get("synUseFlagValue"); //外部数据同步使用标识值
			}
			if(StringUtils.isEmpty(this.synUseFlagValue)) {
				this.synUseFlagValue = "X"; //默认标识X为使用
			}
			List<OutSystemTagNumber> addList = new ArrayList<OutSystemTagNumber>();
			List<OutSystemTagNumber> updList = new ArrayList<OutSystemTagNumber>();
			
			//数据库中存在的数据
			HashMap<String, Integer> maxSortMap = new HashMap<String, Integer>();
			HashMap<String, Integer> hasDataMap = new HashMap<String, Integer>();
			HashMap<String, OutSystemTagNumber> queryMap = new HashMap<String, OutSystemTagNumber>();
			MethodQueryDto dto = new MethodQueryDto();
			dto.setDataType(0); //控制指标
			List<OutSystemTagNumber> queryList = outSystemService.getOutSystemTagNumberList(dto);
			if(StringUtils.isNotEmpty(queryList)) {
				this.getDataMap(queryList, queryMap, maxSortMap);
			}
			
			//核算对象对比数据
			List<OutSystemUnitCompare> unitCompareList = outSystemService.getOutSystemUnitCompareList();
			HashMap<String, String> unitCompareMap = this.getUnitCompareMap(unitCompareList);
			
			//核算对象数据
			HashMap<String, Costuint> unitMap = new HashMap<String, Costuint>();
			List<Costuint> unitList = methodService.getCostuintList(null);
			if(StringUtils.isNotEmpty(unitList)) {
				this.getCostUnitMap(unitList, unitMap);
			}
			
			//核算对象版本数据
			HashMap<String, String> versionMap = new HashMap<String, String>();
			List<Costunitversion> versionList = methodService.getCostunitversionList(null);
			if(StringUtils.isNotEmpty(versionList)) { //有版本数据
				this.getVersionMap(versionList, versionMap);
			}
			
			for(HashMap<String, Object> temp : dataList) {
				OutSystemTagNumber synObj = ObjUtils.convertToObject(OutSystemTagNumber.class, temp);
				if(synObj != null) { //数据读取成功
					String tagName = synObj.getTagName()==null?"":synObj.getTagName().trim();
					if(StringUtils.isNotEmpty(tagName)&&!hasDataMap.containsKey(tagName)) {
						hasDataMap.put(tagName, 1); //去掉重复记录
						synObj.setTagName(tagName);
						//外部核算对象编码（通过对比接口，获取核算对象编码）
						String outRdiCode = tagName.split("_")[0].trim();
						if(StringUtils.isNotEmpty(outRdiCode)) {
							String unitid = "";
							String unitname = "";
							String unitMdmCode = outRdiCode;
							if(StringUtils.isNotEmpty(unitCompareMap)&&unitCompareMap.containsKey(outRdiCode)) {
								unitMdmCode = unitCompareMap.get(outRdiCode);
							}
							if(StringUtils.isNotEmpty(unitMdmCode)&&StringUtils.isNotEmpty(unitMap)&&unitMap.containsKey(unitMdmCode)) {
								Costuint unitObj = unitMap.get(unitMdmCode);
								unitid = unitObj.getId();
								unitname = unitObj.getName();
							}
							if(StringUtils.isNotEmpty(unitid)) {
								synObj.setUnitid(unitid);
								synObj.setUnitname(unitname);
								if(StringUtils.isNotEmpty(versionMap)&&versionMap.containsKey(unitid)) {
									String begintime = versionMap.get(unitid);
									if(StringUtils.isNotEmpty(begintime)) { //有版本日期
										synObj.setBegintime(begintime);
										String key = unitid+"_"+begintime+"_"+tagName;
										String activeChk = synObj.getActiveChk()==null?"":synObj.getActiveChk().trim();
										if(synUseFlagValue.equals(activeChk)) { //使用
											activeChk = "X";
										}else {
											activeChk = "F";
										}
										if("X".equals(activeChk)) { //新增或修改
											synObj.setActiveChk(activeChk);
											if(StringUtils.isNotEmpty(queryMap)&&queryMap.containsKey(key)) {
												OutSystemTagNumber updObj = queryMap.get(key);
												String id = updObj.getId();
												BeanUtils.copyProperties(synObj, updObj); // 赋予返回对象
												updObj.setId(id);
												updList.add(updObj);
											}else {
												String id_syn = synObj.getId();
												if(StringUtils.isEmpty(id_syn)) {
													synObj.setId(TMUID.getUID());
												}
												int maxPx = 0;
												int parentTagNo = synObj.getParentTagNo()==null?0:synObj.getParentTagNo();
												String keyPx = unitid+"_"+begintime+"_"+parentTagNo;
												if(StringUtils.isNotEmpty(maxSortMap)&&maxSortMap.containsKey(keyPx)) {
													maxPx = maxSortMap.get(keyPx);
												}
												maxPx += 1;
												maxSortMap.put(keyPx, maxPx);
												synObj.setDataType(0); //控制指标
												synObj.setTmSort(maxPx);
												addList.add(synObj);
											}
										}else { //删除
											if(StringUtils.isNotEmpty(queryMap)&&queryMap.containsKey(key)) {
												OutSystemTagNumber delObj = queryMap.get(key);
												delObj.setActiveChk("F");
												updList.add(delObj);
											}
										}
									}
								}
							}
						}
					}
				}else {
					result.add(temp);//记录到失败数据中
				}
			}
			if(StringUtils.isNotEmpty(addList)||StringUtils.isNotEmpty(updList)) {
				String ret = outSystemService.saveDataOutSystemTagNumber(addList, updList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取数据Map
	 * @param list
	 * @param dataMap
	 * @param maxSortMap
	 */
	private void getDataMap(List<OutSystemTagNumber> list, HashMap<String, OutSystemTagNumber> dataMap,
		HashMap<String, Integer> maxSortMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, OutSystemTagNumber>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				OutSystemTagNumber obj = list.get(i);
				String unitid = obj.getUnitid()==null?"":obj.getUnitid().trim();
				String begintime = obj.getBegintime()==null?"":obj.getBegintime().trim();
				String tagName = obj.getTagName()==null?"":obj.getTagName().trim();
				String key = unitid+"_"+begintime+"_"+tagName;
				if(!dataMap.containsKey(key)) {
					dataMap.put(key, obj);
				}
				int parentTagNo = obj.getParentTagNo()==null?0:obj.getParentTagNo();
				String keyPx = unitid+"_"+begintime+"_"+parentTagNo;
				if(maxSortMap.containsKey(keyPx)) {
					int tmSort = obj.getTmSort()==null?0:obj.getTmSort();
					int xh = maxSortMap.get(keyPx);
					if(tmSort>xh) {
						maxSortMap.put(keyPx, tmSort);
					}
				}else {
					maxSortMap.put(keyPx, 1);
				}
			}
		}
	}
	
	//核算对象对比Map
	private HashMap<String, String> getUnitCompareMap(List<OutSystemUnitCompare> list) {
		HashMap<String, String> map = new HashMap<String, String>();
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				OutSystemUnitCompare obj = list.get(i);
				String outRdiCode = obj.getOutRdiCode()==null?"":obj.getOutRdiCode().trim();
				String unitMdmCode = obj.getUnitMdmCode()==null?"":obj.getUnitMdmCode().trim();
				if(StringUtils.isNotEmpty(outRdiCode)&&StringUtils.isNotEmpty(unitMdmCode)) {
					if(!map.containsKey(outRdiCode)) {
						map.put(outRdiCode, unitMdmCode);
					}
				}
			}
		}
		return map;
	}
	
	/**
	 *	获取核算单元Map
	 * @param list
	 * @param dataMap
	 */
	private void getCostUnitMap(List<Costuint> list, HashMap<String, Costuint> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, Costuint>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costuint obj = list.get(i);
				String mdmCode = obj.getMdmCode()==null?"":obj.getMdmCode().trim();
				if(StringUtils.isNotEmpty(mdmCode)&&!dataMap.containsKey(mdmCode)) {
					dataMap.put(mdmCode, obj);
				}
			}
		}
	}
	
	/**
	 *	获取最大版本Map
	 * @param list
	 * @param dataMap
	 */
	private void getVersionMap(List<Costunitversion> list, HashMap<String, String> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, String>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costunitversion obj = list.get(i);
				String unitid = obj.getUnitid()==null?"":obj.getUnitid().trim();
				String begintime = obj.getBegintime()==null?"":obj.getBegintime().trim();
				if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)&&!dataMap.containsKey(unitid)) {
					dataMap.put(unitid, begintime);
				}
			}
		}
	}
	
	@Override
	protected List<Object> getPullData(String whereSql, HashMap<String,String> synParam) {
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

	
}
