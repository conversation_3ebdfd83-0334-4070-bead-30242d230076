package com.yunhesoft.leanCosting.synLeanCost;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostKeyDeviceConf;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitversion;
import com.yunhesoft.leanCosting.unitConf.entity.po.OutSystemUnitCompare;
import com.yunhesoft.leanCosting.unitConf.service.IKeyDeviceService;
import com.yunhesoft.leanCosting.unitConf.service.IOutSystemService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.synchronous.utils.SynModel;

/**
 *	关键设备-11
 * <AUTHOR>
 * @date 2023-11-26
 */
@Service
public class SynCostKeyDevice extends SynModel {
	
	@Autowired
	private IOutSystemService outSystemService;
	
	@Autowired
	private IKeyDeviceService keyDeviceService;
	
	@Autowired
	private IUnitMethodService methodService; // 核算相关方法
	
	private String synDelFlagValue = synParam.get("synDelFlagValue"); //外部数据同步删除标识值
	
	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList, HashMap<String,String> synParam) {
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>(); //失败返回的数据
		if(StringUtils.isNotEmpty(dataList)) {
			if(StringUtils.isNotEmpty(synParam)) {
				this.synDelFlagValue = synParam.get("synDelFlagValue"); //外部数据同步删除标识值
			}
			if(StringUtils.isEmpty(this.synDelFlagValue)) {
				this.synDelFlagValue = "1"; //默认标识1为删除
			}
			List<CostKeyDeviceConf> addList = new ArrayList<CostKeyDeviceConf>();
			List<CostKeyDeviceConf> updList = new ArrayList<CostKeyDeviceConf>();
			
			//数据库中存在的数据
			HashMap<String, Integer> maxSortMap = new HashMap<String, Integer>();
			HashMap<String, Integer> hasDataMap = new HashMap<String, Integer>();
			HashMap<String, CostKeyDeviceConf> queryMap = new HashMap<String, CostKeyDeviceConf>();
			List<CostKeyDeviceConf> queryList = keyDeviceService.getKeyDeviceConfList(null);
			if(StringUtils.isNotEmpty(queryList)) {
				this.getDataMap(queryList, queryMap, maxSortMap);
			}
			
			//核算对象对比数据
			List<OutSystemUnitCompare> unitCompareList = outSystemService.getOutSystemUnitCompareList();
			HashMap<String, String> unitCompareMap = this.getUnitCompareMap(unitCompareList);
			
			//核算对象数据
			HashMap<String, Costuint> unitMap = new HashMap<String, Costuint>();
			List<Costuint> unitList = methodService.getCostuintList(null);
			if(StringUtils.isNotEmpty(unitList)) {
				this.getCostUnitMap(unitList, unitMap);
			}
			
			//核算对象版本数据
			HashMap<String, String> versionMap = new HashMap<String, String>();
			List<Costunitversion> versionList = methodService.getCostunitversionList(null);
			if(StringUtils.isNotEmpty(versionList)) { //有版本数据
				this.getVersionMap(versionList, versionMap);
			}
			
			for(HashMap<String, Object> temp : dataList) {
				CostKeyDeviceConf synObj = ObjUtils.convertToObject(CostKeyDeviceConf.class, temp);
				if(synObj != null) { //数据读取成功
					String mdmCode = synObj.getMdmCode()==null?"":synObj.getMdmCode().trim();
					if(StringUtils.isNotEmpty(mdmCode)&&!hasDataMap.containsKey(mdmCode)) {
						hasDataMap.put(mdmCode, 1); //去掉重复记录
						synObj.setMdmCode(mdmCode);
						String unitid = "";
						String unitMdmCode = synObj.getUnitid();
						if(StringUtils.isNotEmpty(unitCompareMap)&&unitCompareMap.containsKey(unitMdmCode)) {
							unitMdmCode = unitCompareMap.get(unitMdmCode);
						}
						if(StringUtils.isNotEmpty(unitMdmCode)&&StringUtils.isNotEmpty(unitMap)&&unitMap.containsKey(unitMdmCode)) {
							Costuint unitObj = unitMap.get(unitMdmCode);
							unitid = unitObj.getId();
						}
						if(StringUtils.isNotEmpty(unitid)) {
							synObj.setUnitid(unitid);
							if(StringUtils.isNotEmpty(versionMap)&&versionMap.containsKey(unitid)) {
								String begintime = versionMap.get(unitid);
								if(StringUtils.isNotEmpty(begintime)) { //有版本日期
									synObj.setBegintime(begintime);
									String key = unitid+"_"+begintime+"_"+mdmCode;
									Integer tmUsed = synObj.getTmUsed()==null?0:synObj.getTmUsed();
									if(synDelFlagValue.equals(String.valueOf(tmUsed))) { //删除
										tmUsed = 0;
									}else {
										tmUsed = 1;
									}
									if(tmUsed==1) { //新增或修改
										synObj.setTmUsed(tmUsed);
										if(StringUtils.isNotEmpty(queryMap)&&queryMap.containsKey(key)) {
											CostKeyDeviceConf updObj = queryMap.get(key);
											String id = updObj.getId();
											BeanUtils.copyProperties(synObj, updObj); // 赋予返回对象
											updObj.setId(id);
											updList.add(updObj);
										}else {
											String id_syn = synObj.getId();
											if(StringUtils.isEmpty(id_syn)) {
												synObj.setId(TMUID.getUID());
											}
											int maxPx = 0;
											String pid = "root";
											String keyPx = unitid+"_"+begintime+"_"+pid;
											if(StringUtils.isNotEmpty(maxSortMap)&&maxSortMap.containsKey(keyPx)) {
												maxPx = maxSortMap.get(keyPx);
											}
											maxPx += 1;
											maxSortMap.put(keyPx, maxPx);
											synObj.setPid(pid);
											synObj.setTmSort(maxPx);
											synObj.setIsUseToRecordEvent(0);
											addList.add(synObj);
										}
									}else { //删除
										if(StringUtils.isNotEmpty(queryMap)&&queryMap.containsKey(key)) {
											CostKeyDeviceConf delObj = queryMap.get(key);
											delObj.setTmUsed(0);
											updList.add(delObj);
										}
									}
								}
							}
						}
					}
				}else {
					result.add(temp);//记录到失败数据中
				}
			}
			if(StringUtils.isNotEmpty(addList)||StringUtils.isNotEmpty(updList)) {
				String ret = keyDeviceService.saveDataCostKeyDevice(addList, updList, null);
				if(StringUtils.isNotEmpty(ret)) {
					this.errorInfo += ret+";";
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取数据Map
	 * @param list
	 * @param dataMap
	 * @param maxSortMap
	 */
	private void getDataMap(List<CostKeyDeviceConf> list, HashMap<String, CostKeyDeviceConf> dataMap,
		HashMap<String, Integer> maxSortMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, CostKeyDeviceConf>();
		}
		if(maxSortMap==null) {
			maxSortMap = new HashMap<String, Integer>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostKeyDeviceConf obj = list.get(i);
				String unitid = obj.getUnitid()==null?"":obj.getUnitid().trim();
				String begintime = obj.getBegintime()==null?"":obj.getBegintime().trim();
				String mdmCode = obj.getMdmCode()==null?"":obj.getMdmCode().trim();
				String key = unitid+"_"+begintime+"_"+mdmCode;
				if(!dataMap.containsKey(key)) {
					dataMap.put(key, obj);
				}
				String pid = obj.getPid();
				if(StringUtils.isEmpty(pid)) {
					pid = "nullStr";
				}else {
					pid = pid.trim();
				}
				String keyPx = unitid+"_"+begintime+"_"+pid;
				if(maxSortMap.containsKey(keyPx)) {
					int tmSort = obj.getTmSort()==null?0:obj.getTmSort();
					int xh = maxSortMap.get(keyPx);
					if(tmSort>xh) {
						maxSortMap.put(keyPx, tmSort);
					}
				}else {
					maxSortMap.put(keyPx, 1);
				}
			}
		}
	}
	
	/**
	 *	获取核算单元Map
	 * @param list
	 * @param dataMap
	 */
	private void getCostUnitMap(List<Costuint> list, HashMap<String, Costuint> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, Costuint>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costuint obj = list.get(i);
				String mdmCode = obj.getMdmCode()==null?"":obj.getMdmCode().trim();
				if(StringUtils.isNotEmpty(mdmCode)&&!dataMap.containsKey(mdmCode)) {
					dataMap.put(mdmCode, obj);
				}
			}
		}
	}
	
	/**
	 *	获取最大版本Map
	 * @param list
	 * @param dataMap
	 */
	private void getVersionMap(List<Costunitversion> list, HashMap<String, String> dataMap) {
		if(dataMap==null) {
			dataMap = new HashMap<String, String>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				Costunitversion obj = list.get(i);
				String unitid = obj.getUnitid()==null?"":obj.getUnitid().trim();
				String begintime = obj.getBegintime()==null?"":obj.getBegintime().trim();
				if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)&&!dataMap.containsKey(unitid)) {
					dataMap.put(unitid, begintime);
				}
			}
		}
	}
	
	//核算对象对比Map
	private HashMap<String, String> getUnitCompareMap(List<OutSystemUnitCompare> list) {
		HashMap<String, String> map = new HashMap<String, String>();
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				OutSystemUnitCompare obj = list.get(i);
				String outRdiCode = obj.getOutRdiCode()==null?"":obj.getOutRdiCode().trim();
				String unitMdmCode = obj.getUnitMdmCode()==null?"":obj.getUnitMdmCode().trim();
				if(StringUtils.isNotEmpty(outRdiCode)&&StringUtils.isNotEmpty(unitMdmCode)) {
					if(!map.containsKey(outRdiCode)) {
						map.put(outRdiCode, unitMdmCode);
					}
				}
			}
		}
		return map;
	}
	
	@Override
	protected List<Object> getPullData(String whereSql, HashMap<String,String> synParam) {
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

	
}
