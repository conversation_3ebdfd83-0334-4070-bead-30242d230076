package com.yunhesoft.leanCosting.priceManage.entity.dto;

import com.yunhesoft.core.common.dto.BaseQueryDto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CoefficientQueryDto extends BaseQueryDto {

	
	@ApiModelProperty(name="主表ID",example="ITEMPRICECHANGEINFO.ID")
	private String pId;
	
	@ApiModelProperty(name="核算单元ID")
	private String costuintId;
	
	
}
