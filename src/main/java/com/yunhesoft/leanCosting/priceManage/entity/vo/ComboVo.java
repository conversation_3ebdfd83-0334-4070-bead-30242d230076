package com.yunhesoft.leanCosting.priceManage.entity.vo;


import lombok.Getter;
import lombok.Setter;


/**
 *	下拉框
 */
@Setter
@Getter
public class ComboVo {
	
	
	private String startStr; //开始日期（字符串）
	
	private String endStr; //截止日期（字符串）
	
	public ComboVo () {
		
	}
	
	
	
	private String value; //下拉框值

	private String label; //下拉框显示名称
	
	public ComboVo (String value,String label) {
		this.value = value;
		this.label = label;
	}
	
	
	
	private String alias;
	
	private String header;
	
	private String align;
	
	private Integer width;
	
	public ComboVo (String alias,String header,String align,Integer width) {
		this.alias = alias;
		this.header = header;
		this.align = align;
		this.width = width;
	}
	
	
}
