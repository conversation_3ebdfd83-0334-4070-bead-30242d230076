package com.yunhesoft.leanCosting.priceManage.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.leanCosting.priceManage.entity.dto.CoefficientQueryDto;
import com.yunhesoft.leanCosting.priceManage.entity.vo.OrgUnitProcessingVo;
import com.yunhesoft.leanCosting.priceManage.entity.vo.OrgUnitProcessingExcleVo;
import com.yunhesoft.leanCosting.priceManage.service.IOrgUnitProcessingService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostuintQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.kernel.utils.excel.ExcelImport;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/priceManage/unitPriceProcessing")
@Api(tags = "单位加工费")
public class OrgUnitProcessingController {

	@Autowired
	private IOrgUnitProcessingService orgUnitProcessingServiceImpl;

	@Autowired
	private ICostuintService costuintService;

	@Autowired
	private HttpServletResponse response;

	@Autowired
	private HttpServletRequest request;
	
	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation("获得单位加工费")
	public Res<?> getData(@RequestBody CoefficientQueryDto dto) {
		Res<List<OrgUnitProcessingVo>> res = new Res<List<OrgUnitProcessingVo>>();
		List<OrgUnitProcessingVo> list = orgUnitProcessingServiceImpl.getData(dto);
		res.setResult(list);
		return res;
	}

	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation("保存")
	public Res<?> saveData(@RequestBody List<OrgUnitProcessingVo> data) {
		Res<String> res = new Res<String>();
		String msg = orgUnitProcessingServiceImpl.saveData(data);
		res.setResult(msg);
		return res;
	}

	/**
	 * 导出Excel
	 * 
	 * @param querySysRoleDto
	 */
	@ApiOperation("数据导出Excel")
	@RequestMapping(value = "/toExcel", method = RequestMethod.POST)
	public void toExcel(@RequestBody CoefficientQueryDto dto) {
		List<OrgUnitProcessingVo> list = orgUnitProcessingServiceImpl.getData(dto);
		CostuintQueryDto dto_ = new CostuintQueryDto();
		// 核算对象名称
		List<Costuint> _list = costuintService.getDatas(dto_);
		List<String> selectList = new ArrayList<String>();
		for (Costuint vo : _list) {
			String name = vo.getName();
			selectList.add(name);
		}
		Map<String, List<String>> selectMap = new HashMap<String, List<String>>();
		selectMap.put(ExcelExport.resolveColumnName(OrgUnitProcessingVo::getName), selectList);
		List<OrgUnitProcessingExcleVo> list_ = ObjUtils.copyToList(list, OrgUnitProcessingExcleVo.class);
		ExcelExport.exportExcel("单位加工费", null, true, OrgUnitProcessingExcleVo.class, list_, selectMap, response);
	}

	@ApiOperation("数据导入")
	@RequestMapping(value = "/import", method = { RequestMethod.POST })
	public Res<?> importExcel(@RequestParam("file") MultipartFile file) throws Exception {
		String pid = request.getParameter("pid");
		ExcelImportResult<?> result;
		try {
			result = ExcelImport.importExcel(file.getInputStream(), OrgUnitProcessingExcleVo.class, 1, 1, false);
			if (result != null) {
				if (result.isVerifyFail()) {// 校验失败
					return Res.OK(result.getFailList());// 校验失败的数据
				}
				List<?> list = result.getList();// 导入的结果数据
				// 将excel集合转类型
				List<OrgUnitProcessingExcleVo> teList = ObjUtils.copyToList(list, OrgUnitProcessingExcleVo.class);
				
				String message = orgUnitProcessingServiceImpl.importExcel(teList,pid);
				return Res.OK(message);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return Res.FAIL("");
	}
}
