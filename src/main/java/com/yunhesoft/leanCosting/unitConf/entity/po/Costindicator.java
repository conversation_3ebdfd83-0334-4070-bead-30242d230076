package com.yunhesoft.leanCosting.unitConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * 核算指标
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "COSTINDICATOR")
public class Costindicator extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 名称 */
    @Column(name="CPNAME", length=200)
    private String cpname;
    
    /** 标准值 */
    @Column(name="STANDARDVAL")
    private Double standardval;
    
    /** 比较方式：0、越大越好；1、越小越好；2、趋近考核值 */
    @Column(name="COMPARETYPE")
    private Integer comparetype;
    
    /** 版本 */
    @Column(name="BEGINTIME", length=10)
    private String begintime;
    
    /** 核算对象ID */
    @Column(name="UNITID", length=100)
    private String unitid;
    
    /** 无用 */
    @Column(name="TMUSED")
    private Integer tmused;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** 计量单位 */
	@Column(name = "ITEMUNIT", length = 50)
	private String itemunit;
    
	/** 是否分析项目 */
    @Column(name="ISANALYSISITEM")
    private Integer isAnalysisItem;
    
    /** 是否查询展示 */
    @Column(name="ISSELSHOW")
    private Integer isSelShow;
    
    /** 是否用于记事：1、是；其他不是（默认） */
    @Column(name="ISUSETORECORDEVENT")
    private Integer isUseToRecordEvent;
    
    @ApiModelProperty(value="核算单元-核算指标ID",example = "DEVICETYPELIBPARAM.ID")
    @Column(name="DEVICETYPELIBPARAMID")
    private String deviceTypelibParamId;
    
    /** 标准单耗 */
    @Column(name="BASECONSUMPTION")
    private Double baseConsumption;
    
}