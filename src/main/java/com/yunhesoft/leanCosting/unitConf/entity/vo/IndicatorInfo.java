package com.yunhesoft.leanCosting.unitConf.entity.vo;

import java.util.HashMap;
import java.util.List;

import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramAlertReceiver;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramContingencyPlan;

/**
 * 得到指标属性
 */
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class IndicatorInfo {

	// 核算对象ID
	private String unitid;

	// 采集点ID
	private String sdid;

	// 采集点的版本
	private String begindate;

	// 指标方案的版本ID
	private String pvid;

	// 采集点父分类
	private String pid;

	// 仪表位号
	private String tagnumber;

	// 名称
	private String name;

	// 数据来源
	private String datasource;

	// 来源类型
	private String sourceype;

	// 采集值区间
	private String valrange;
	
	//采样间隔
	private Integer sampleInterval;

	// 采集类型
	private String ctype;

	// 采集点排序
	private Integer tmsort;

	// 合格上限
	private Double operateUpLimit;

	// 合格下限
	private Double operateLowLimit;

	// 调整上限
	private Double keyUpLimit;

	// 调整下限
	private Double keyLowLimit;

	// 指标的超限预案
	private List<ProgramContingencyPlan> planList;

	// 预案的接收人
	private HashMap<String, List<ProgramAlertReceiver>> ReceiverMap;

}
