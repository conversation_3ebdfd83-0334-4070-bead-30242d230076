package com.yunhesoft.leanCosting.unitConf.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 实时仪表组件数据
 * 
 * @category 实时仪表组件数据
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Table(name = "COST_SAMPLEDOT_WIDGET_TEMPLATE")
public class CostunitSampledotWidgetTemplate extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "创建人")
    @Column(name = "CREATE_BY_NAME")
    private String createByName;

    @ApiModelProperty(value = "修改人")
    @Column(name = "UPDATE_BY_NAME")
    private String updateByName;

    @ApiModelProperty(value = "模板名称")
    @Column(name="NAME")
    private String name;

    @ApiModelProperty(value = "间隔(采集频次)")
    @Column(name="DATA_INTERVAL")
    private int dataInterval;

    @ApiModelProperty(value = "排序")
    @Column(name="TMSORT")
    private int tmsort;


    @ApiModelProperty(value = "1新增 0修改 -1删除")
    @Transient
    private int rowflag = 0;

}