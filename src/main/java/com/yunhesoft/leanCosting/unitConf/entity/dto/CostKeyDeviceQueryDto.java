package com.yunhesoft.leanCosting.unitConf.entity.dto;


import com.yunhesoft.core.common.dto.BaseQueryDto;
import com.yunhesoft.system.kernel.service.model.Pagination;

import lombok.Getter;
import lombok.Setter;


/**
 *	关键设备查询类
 */
@Setter
@Getter
public class CostKeyDeviceQueryDto extends BaseQueryDto {
	
	/** 核算对象id */
	private String unitid;

	/** 版本日期 */
	private String begintime;

	/** 是否用于记事：1、是 */
	private Integer isUseToRecordEvent;
	
	/** 设备名称模糊检索 */
	private String deviceName;
	
	/** 分页 */
	private Pagination<?> page;
	
	private String searchName; //名称
	
	private Integer statusNum; //启停状态
	
}
