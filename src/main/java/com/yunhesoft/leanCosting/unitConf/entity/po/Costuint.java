package com.yunhesoft.leanCosting.unitConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * 核算对象
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "COSTUINT")
public class Costuint extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 父对象ID */
    @Column(name="PID", length=100)
    private String pid;
    
    /** 名称 */
    @Column(name="NAME", length=200)
    private String name;
    
    /** 注释 */
    @Column(name="MEMO", length=1000)
    private String memo;
    
    /** 排序号 */
    @Column(name="TMSORT", length=1000)
    private String tmSort;
    
    /** 单元类型ID */
    @Column(name="UNITTYPE", length=100)
    private String unittype;
    
    /** 使用状态,0分类：1叶子 */
    @Column(name="TYPE")
    private Integer type;
    
    /** 用于台账录入 */
    @Column(name="LEDGERENTRY")
    private Integer ledgerEntry;
    
    /** 用于移动端录入 */
    @Column(name="MOBILEINPUT")
    private Integer mobileInput;
    
    
    @ApiModelProperty(value = "实际坐标X")
    @Column(name = "COORDINATEX")
    private Double coordinateX;
    
    @ApiModelProperty(value = "实际坐标Y")
    @Column(name = "coordinateY")
    private Double coordinateY;
    
    @ApiModelProperty(value = "作业半径")
    @Column(name = "operatingRadius")
    private Double operatingRadius;
    
    /** 使用状态 */
    @Column(name="USSTATUS")
    private Integer usStatus;
    
    /** 维护机构ID */
    @Column(name="ORGID", length=100)
    private String orgId;
    
    /** 无用 */
    @Column(name="TMUSED")
    private Integer tmused;
    
    /** 绑定的表单ID */
    @Column(name="FORMID", length=100)
    private String formId;
    
    /** 小数位数*/
    @Column(name="DIGIT")
    private Integer digit;

    
    @ApiModelProperty(value="外部数据编码")
    @Column(name = "MDMCODE",length = 50)
    private String mdmCode;
    
    @ApiModelProperty(value="外部数据装置别名")
    @Column(name = "DEVICEALIAS",length = 200)
    private String deviceAlias;
    
    @ApiModelProperty(value="单元号")
    @Column(name = "UNITNO",length = 50)
    private String unitNo;
    
    
    
    
    
    
    //交接班组件使用
    @ApiModelProperty(value="默认选中使用")
    @Transient
    private boolean defaultKey;
    
    
    //TODO:2024-02-27添加
    /** 生产活动：0、核算对象；1、作业活动；2、台账模型 */
    @Column(name="PRODUCTIVE")
    private Integer productive;
    
    @Column(name="PRODUCTIVETYPE")
    private Integer productiveType;
    
    /** 调度台体现 */
    @Column(name="DISPATCHDESK")
    private Integer dispatchDesk;
    
    /** 使用核算 */
    @Column(name="USEACCOUNTING")
    private Integer useAccounting;
    
    /** 按时录入 */
    @Column(name="isOntime")
    private Integer isOntime;
    
    
    
    /** 设备ID */
    @Column(name="DEVICEIDS",length = 3000)
    private String deviceIds;
    
    /** 设备Name */
    @Column(name="DEVICENAMES",length = 3000)
    @Transient
    private String deviceNames;

    //TODO:2024-02-27添加
    /** 手机分析模版 */
    @ApiModelProperty(value = "手机分析模版")
    @Column(name="ANALYSISTEMPLATE")
    private String analysisTemplate;
    
    
}
