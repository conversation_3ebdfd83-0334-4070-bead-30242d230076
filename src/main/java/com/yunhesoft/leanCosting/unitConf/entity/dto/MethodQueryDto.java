package com.yunhesoft.leanCosting.unitConf.entity.dto;


import java.util.List;

import com.yunhesoft.system.kernel.service.model.Pagination;

import lombok.Data;

@Data
public class MethodQueryDto {

//    传all 时默认查询所有 包含已删除
    private String usedCase;
    private String unitid; //核算对象ID
    
    private String begintime; //版本日期
    
    
    private List<String> unitids; //核算对象ID
    
    private List<String> begintimes; //版本日期
    
    
    private String pid; //父id
    
    private List<String> pidList; //父id列表
    
    private String id; //id
    
    private List<String> idList; //id列表
    
    private String orgid; //机构ID
    
    private String objType; //对象类型：org-机构；user-人员；post-岗位；
    
    /**
     * 核算对象查询使用
     */
    private List<String> orgids; //机构IDs	
    
    private Pagination<?> page; //分页
    
    private String ctype; //采样点的采集类型
    
    private String ctypeNot; // 采样点的采集类型（不等于）
    
    private String name; // 名称（模糊检索）
    
    private Boolean isNotNull_dotid = false; //采集点id不为空
    
    private String tenantId; // 租户ID
    
    private String cctype; // 分类类型
    
    private String maxBegintime; //小于等于最大版本日期
    
    private String unittype; //单元类型
    
    private String unittype_not; //不等于此单元类型
    
    private List<String> itemidList; //项目id列表
    
    private Integer dataType; //数据类型
    
    private Boolean isSelUnitOrgId = true; //是否查询核算对象通过维护机构过滤（兼容超级管理员的判断）
    
    private List<String> unitidList; //核算对象ID列表
    
    private boolean IsLedgerEntry; //是否过滤【用于台账录入】
    
    private boolean IsmobileInput;//是否过滤【用于移动端录入】
    
    private String excelFileName; //Excel文件名
    
    private Boolean hasDotClassInputRight = false; //查询采集点分类有录入权限的数据
    
    private Integer productive; //核算对象数据是否为作业活动数据：1、是；其他不是；
	
	/** 按时录入 */
	private Boolean isOntime = false;
	
	private String elemId; //要素ID
    
}
