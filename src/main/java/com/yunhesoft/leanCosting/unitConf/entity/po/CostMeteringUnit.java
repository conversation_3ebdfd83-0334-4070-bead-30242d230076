package com.yunhesoft.leanCosting.unitConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * 计量单位
 */
@Entity
@Setter
@Getter
@Table(name = "COSTMETERINGUNIT")
public class CostMeteringUnit extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 计量单位名称 */
    @Column(name="MUNAME", length=100)
    private String muName;
    
    /** 计量单位类型 */
    @Column(name="MUTYPE", length=100)
    private String muType;
    
    /** 国际单位制 */
    @Column(name="MUSTANDARD", length=100)
    private String muStandard;
    
    /** 计量单位别名 */
    @Column(name="MUALIAS", length=100)
    private String muAlias;
    
    /** 计量单位英文名称 */
    @Column(name="MUENGNAME", length=100)
    private String muEngName;
    
    /** 计量单位符号 */
    @Column(name="MUSYMBOL", length=100)
    private String muSymbol;
    
    /** 排序号 */
    @Column(name="TMSORT")
    private Integer tmSort;
    
    /** 是否使用 */
    @Column(name="TMUSED")
    private Integer tmUsed;
    
    
    @ApiModelProperty(value="外部数据编码")
    @Column(name = "MDMCODE",length = 50)
    private String mdmCode;
    
    
}
