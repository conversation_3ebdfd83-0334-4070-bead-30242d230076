package com.yunhesoft.leanCosting.unitConf.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.CostEnvironmentConfig;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.baseConfig.service.ICostDeviceTypeService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.calcLogic.IAutoShiftCalcLogic;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.vo.ComboVo;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CopyDataDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostBindOrgDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostuintQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.RetrieveProductDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.ReturnDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostuintExcelVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostuintVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.ProgressBarVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.kernel.utils.excel.ExcelImport;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.tmsf.form.entity.po.SFForm;
import com.yunhesoft.tmsf.form.service.IFormManageService;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/cost")
@Api(tags = "核算类")
public class CostController {

	@Autowired
	private ICostService costService;

	@Autowired
	private IFormManageService rormManageService;

	@Autowired
	private ICostDeviceTypeService costDeviceTypeService;

	@Autowired
	private EntityService entityService;

	@Autowired
	private UnitItemInfoService unitItemInfoService;

	@Autowired
	private ICostToolService toolService;

	@Autowired
	private HttpServletResponse response;
	
	@Autowired
	private ISysOrgService sysOrgService;
	
	@Autowired
	private ICostToolService costToolService;
	
	@Autowired
	private IAutoShiftCalcLogic autoShiftCalcLogic;
	
	@Autowired
	private RedisUtil redisUtil;
	
	
	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation("获取数据")
	public Res<?> getData(@RequestBody CostDto dto) {
		Boolean isadmin = SysUserUtil.isAdmin();
		String orgId = SysUserUtil.getCurrentUser().getOrgId();
		Res<ReturnDto> res = new Res<ReturnDto>();
		SysUser user = SysUserHolder.getCurrentUser();
		if (ObjUtils.notEmpty(user)) {
			dto.setUserId(user.getId());
			dto.setUserName(user.getRealName());
			dto.setUserOrgCode(user.getOrgId());
			dto.setUserOrgName(user.getOrgName());
			dto.setUser(user);
		}
		Integer productive = dto.getProductive()==null?0:dto.getProductive(); // 0、核算对象；1、生产活动；2、台账模型；
		dto.setProductive(productive);
		Pagination<?> page = null;
		if (dto.getPageSize() != null && dto.getPageSize() > 0) {
			page = Pagination.create(dto.getPageNum() == null ? 1 : dto.getPageNum(), dto.getPageSize());
			dto.setPage(page);
		}
		List<CostuintVo> listVo = new ArrayList<CostuintVo>();
		List<Costuint> list = costService.getData(dto);
		// 判断是是树形
		if (dto.isTreeKey()) {
			listVo =toolService.tree(list,dto.isChildrenTreeKey());
		} else {
			boolean isSelUnitOrgId = dto.getIsSelUnitOrgId(); //是否查询核算对象通过维护机构过滤（兼容超级管理员的判断）
			for (Costuint costuint : list) {
				String _orgId = costuint.getOrgId();
				if (isadmin||!isSelUnitOrgId) {//超级管理员,查询全部
					CostuintVo voObj = new CostuintVo();
					ObjUtils.copyTo(costuint, voObj);
					listVo.add(voObj);
				}else if (_orgId != null && _orgId.equals(orgId)) {
					CostuintVo voObj = new CostuintVo();
					ObjUtils.copyTo(costuint, voObj);
					listVo.add(voObj);
				}
			}

		}
		ReturnDto r = new ReturnDto();
		r.setClist(listVo);
		r.setPage(dto.getPage());
		res.setResult(r);
		return res;
	}
	
	
	@RequestMapping(value = "/getDataTree", method = RequestMethod.POST)
	@ApiOperation("获取树形数据")
	public Res<?> getDataTree(@RequestBody CostDto dto) {
		Res<Map<String, Object>> res = new Res<Map<String, Object>>();
		SysUser user = SysUserHolder.getCurrentUser();
		if (ObjUtils.notEmpty(user)) {
			dto.setUserId(user.getId());
			dto.setUserName(user.getRealName());
			dto.setUserOrgCode(user.getOrgId());
			dto.setUserOrgName(user.getOrgName());
			dto.setUser(user);
		}
		// 判断是是树形
		List<CostuintVo> listVo = null;
		if (StringUtils.isNotNull(dto.getPid())) {
			Pagination<?> page = null;
			if (dto.getPageSize() != null && dto.getPageSize() > 0) {// 创建分页信息
				page = Pagination.create(dto.getPageNum() == null ? 1 : dto.getPageNum(), dto.getPageSize());
			}
			dto.setPage(page);
			listVo = toolService.getCopy(costService.getData(dto));
			 if (dto != null) {
                res.setTotal(page.getTotal());// 总数量赋值
            }
		}else {
			// 判断是是树形
			listVo = toolService.tree(costService.getData(dto),dto.isChildrenTreeKey());
		}
		
		Map<String, Object> map_ = new HashMap<String, Object>();
		map_.put("data", listVo);
		List<CostuintVo> keys = costToolService.costuintTreeExpansion(listVo, dto.getTreeExpansion());
		map_.put("keys", keys);
		res.setResult(map_);
		return res;
	}
	

	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation("保存数据")
	public Res<?> saveNodeData(@RequestBody CostuintVo vo) {
		Res<String> res = new Res<String>();
		String s = costService.saveDataVo(vo);
		res.setMessage(s);
		if (s.length() > 0) {
			res.setSuccess(false);
		} else {
			res.ok();
		}
		return res;
	}

	@RequestMapping(value = "/deleteBean", method = RequestMethod.POST)
	@ApiOperation("删除数据")
	public Res<?> deleteBean(@RequestBody CostuintVo bean) {
		Res<Boolean> res = new Res<Boolean>();
		res.setResult(false);
		
		Where where = Where.create();
		where.eq(CostTeamInfo::getUnitId, bean.getId());
		List<CostTeamInfo> list = entityService.queryList(CostTeamInfo.class, where, null);
		if(list.size()>0) {
			res.setMessage("该核算对象已存在录入数据！");
			return res;	
		}
		Costuint vo = entityService.queryObjectById(Costuint.class, bean.getId());
		vo.setTmused(0);
		int i = entityService.rawUpdateById(vo);
		if (i > 0) {
			res.setResult(true);
		}
		return res;
	}

//	public Res<?> saveNodeData(@RequestBody CostDto dto) {
//		Res<String> res = new Res<String>();
//		res.setResult(costService.saveData(dto));
//		return res;
//	}

	@RequestMapping(value = "/addVersion", method = RequestMethod.POST)
	@ApiOperation("添加版本数据")
	public Res<?> addVersionData(@RequestBody CostDto dto) {
		Res<String> res = new Res<String>();
		res.setResult(costService.addVersion(dto));
		return res;
	}

	@RequestMapping(value = "/getVersionList", method = RequestMethod.POST)
	@ApiOperation("获取版本数据")
	public Res<?> getVersionList(@RequestBody CostDto dto) {
		Res<List<String>> res = new Res<List<String>>();
		res.setResult(costService.getVersionList(dto));
		return res;
	}

	/**
	 * 获取核算单元绑定机构数据
	 * 
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/getCostUnitBindOrg", method = RequestMethod.POST)
	@ApiOperation("获取核算单元绑定机构数据")
	public Res<?> getCostUnitBindOrg(@RequestBody CostBindOrgDto dto) {
		Res<CostBindOrgDto> res = new Res<CostBindOrgDto>();
		CostBindOrgDto orgObj = costService.getCostUnitBindOrg(dto);
		res.setResult(orgObj);
		return res;
	}

	/**
	 * 保存核算单元绑定机构数据
	 * 
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/saveCostUnitBindOrgData", method = RequestMethod.POST)
	@ApiOperation("保存核算单元绑定机构数据")
	public Res<?> saveCostUnitBindOrgData(@RequestBody CostBindOrgDto saveDto) {
		Res<String> res = new Res<String>();
		String s = costService.saveCostUnitBindOrgData(saveDto);
		res.setMessage(s);
		if (s.length() > 0) {
			res.setSuccess(false);
		} else {
			res.ok();
		}
		return res;
	}

	/**
	 * 核算单元另存为
	 * 
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/copyCostUnit", method = RequestMethod.POST)
	@ApiOperation("保存核算单元绑定机构数据")
	public Res<?> copyCostUnit(@RequestBody CopyDataDto saveDto) {
		return Res.OK(costService.copyCostUnit(saveDto));
	}

	@RequestMapping(value = "/queryFormInfoList", method = RequestMethod.POST)
	@ApiOperation("获得表单")
	public Res<?> queryFormInfoList() {
		// TODO:获得表单后期需要替换
		List<SFForm> forms = rormManageService.queryTenantFormAllList();
		return Res.OK(forms);
	}

	/**
	 * 移动端获取核算对象下拉框
	 * 
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	@RequestMapping(value = "/getCostObjComboData", method = RequestMethod.POST)
	@ApiOperation("获取核算对象下拉数据")
	public Res<?> getCostObjComboData() {
		return Res.OK(costService.getCostObjComboData());
	}

	@RequestMapping(value = "/getCostuintObjById", method = RequestMethod.POST)
	@ApiOperation("根据id获取核算对象")
	public Res<?> getCostuintObjById(@RequestParam("costid") String id) {
		return Res.OK(costService.getCostuintObjById(id));
	}

	@RequestMapping(value = "/getProductItem", method = RequestMethod.POST)
	@ApiOperation("获取核算对象产品数据")
	public Res<?> getProductItem(@RequestBody RetrieveProductDto rpDto) {
		return Res.OK(unitItemInfoService.getProductItem(rpDto));
	}

	/**
	 * 周报录入使用
	 * 
	 * @param dto
	 * @return
	 */
	@RequestMapping(value = "/getCostById", method = RequestMethod.POST)
	@ApiOperation("外部通过ID获取数据")
	public Res<?> getCostById(@RequestBody CostDto dto) {
		return Res.OK(entityService.queryObjectById(Costuint.class, dto.getId()));
	}

	@RequestMapping(value = "/saveCostByIdDigit", method = RequestMethod.POST)
	@ApiOperation("外部通过ID,修改小数位数")
	public Res<?> saveCostByIdDigit(@RequestBody CostuintVo saveDate) {
		Costuint costuint = entityService.queryObjectById(Costuint.class, saveDate.getId());
		Integer digit = saveDate.getDigit();
		if (digit != null) {
			costuint.setDigit(digit);
			entityService.rawUpdateById(costuint);
		}
		return Res.OK();
	}

	

	/**
	 * 获取核算环境配置数据
	 * 
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/getCostEnvironmentConfigList", method = RequestMethod.POST)
	@ApiOperation("获取核算环境配置数据")
	public Res<?> getCostEnvironmentConfigList(@RequestBody CostDto dto) {
		Res<List<CostEnvironmentConfig>> res = new Res<List<CostEnvironmentConfig>>();
		List<CostEnvironmentConfig> list = new ArrayList<CostEnvironmentConfig>();
		if (StringUtils.isNotNull(dto)) {
			String pageConfig = dto.getPageConfig();
			String configParam = dto.getConfigParam();
			if (StringUtils.isNotEmpty(pageConfig)) {
				List<CostEnvironmentConfig> queryList = toolService.getCostEnvironmentConfigList(pageConfig,
						configParam);
				if (StringUtils.isNotEmpty(queryList)) {
					list = queryList;
				}
			}
		}
		res.setResult(list);
		return res;
	}

	/**
	 * 
	 * @param draggingNode
	 * @param dropType		before、after、inner
	 * @return
	 */
	@RequestMapping(value = "/dragTree", method = RequestMethod.POST)
	@ApiOperation("拖拽树形")
	public Res<?> dragTree(@RequestBody List<CostuintVo> draggingNodes,String dropType) {
		CostuintVo draggingNode = draggingNodes.get(0);//原树形,需要更新
		CostuintVo dropNode = draggingNodes.get(1);//目标树形,锚节点
		String id = dropNode.getId();
		Costuint _dataObj = entityService.queryObjectById(Costuint.class, id);
		//目标序号
		String pSort = _dataObj.getTmSort();
		if("inner".equals(dropType)) {//内部
			draggingNode.setPid(id);//赋值Pid
			//目标内容
			String tmSort = costService.getCostuitSort(id, pSort);
			draggingNode.setTmSort(tmSort);
			Costuint dataObj = new Costuint();
			BeanUtils.copyProperties(draggingNode, dataObj); // 赋予返回对象
			entityService.update(dataObj);
		}else if("before".equals(dropType)||"after".equals(dropType)) {//前
			String draggSort = dropNode.getTmSort();
			if("after".equals(dropType)) {//后需要序号字段+1
				draggSort = String.valueOf(Long.parseLong(draggSort)+1);
			}
			draggingNode.setPid(dropNode.getPid());//赋值Pid
			List<Costuint> list = costService.getData(dropNode.getPid());
			List<Costuint> updateList = new ArrayList<Costuint>();
			Map<String, Costuint> map = new LinkedHashMap<String, Costuint>();
			//内容
			draggingNode.setPid(dropNode.getPid());
			Costuint dataObj = new Costuint();
			BeanUtils.copyProperties(draggingNode, dataObj); // 赋予返回对象
			map.put(dataObj.getId(), dataObj);
			updateList.add(dataObj);
			String oldTmsort = draggSort;
			boolean key = false;
			for (Costuint costuint : list) {
				String tmSort = costuint.getTmSort();
				if(Long.parseLong(draggSort)<=Long.parseLong(tmSort)) {
					if(!key) {
						key = true;
						oldTmsort = tmSort;
					}
				}
				if(key) {
					if(!map.containsKey(costuint.getId())) {
						map.put(costuint.getId(), costuint);
						updateList.add(costuint);
					}
				}
			}
			costService.getCostLists(updateList, oldTmsort,1);
			entityService.updateBatch(updateList);
		}
		return Res.OK();
	}
	
	
	
	
	
	/**
	 * 导出Excel
	 * 
	 * @param querySysRoleDto
	 */
	@ApiOperation("数据导出Excel")
	@RequestMapping(value = "/toExcel", method = RequestMethod.POST)
	public void toExcel() {
		//单元类型
		List<CostuintExcelVo> listVo = costService.toExcel();
		List<Devicetypelibrary> typeList = costDeviceTypeService.getData();
		// 判断是是树形
		 // 导出数据带下拉框
        List<String> selectList = new ArrayList<String>();
        for (Devicetypelibrary vo : typeList) {
        	String ccName = vo.getDtname();
        	selectList.add(ccName);
		}
        //机构名称
        List<String> selectList1 = new ArrayList<String>();
        List<SysOrg> orgList = sysOrgService.getOrgList();
        for (SysOrg sysOrg : orgList) {
        	selectList1.add(sysOrg.getOrgname());
		}
        Map<String, List<String>> selectMap = new HashMap<String, List<String>>();
        selectMap.put(ExcelExport.resolveColumnName(CostuintExcelVo::getUnittypeName), selectList);
        selectMap.put(ExcelExport.resolveColumnName(CostuintExcelVo::getOrgName), selectList1);
		ExcelExport.exportExcel("项目库列表", null, true, CostuintExcelVo.class, listVo, selectMap, response);
	}

	@ApiOperation("数据导入")
	@RequestMapping(value = "/import", method = { RequestMethod.POST })
	public Res<?> importExcel(@RequestParam("file") MultipartFile file) throws Exception {
		ExcelImportResult<?> result = ExcelImport.importExcel(file.getInputStream(), CostuintExcelVo.class, 1, 1, false);
		if (result != null) {
			if (result.isVerifyFail()) {// 校验失败
				return Res.OK(result.getFailList());// 校验失败的数据
			}
			List<?> list = result.getList();// 导入的结果数据
			// 将excel集合转类型
            List<CostuintExcelVo> teList = ObjUtils.copyToList(list, CostuintExcelVo.class);
            String message = costService.importExcel(teList);
            return Res.OK(message);
		}
		return Res.FAIL("");
	}

	@RequestMapping(value = "/getCostuintSysUserOrgList", method = RequestMethod.POST)
	@ApiOperation("当前用户所在机构下-核算对象所对应数据")
	public Res<?> getCostuintSysOrgList() {
		String orgId = SysUserUtil.getCurrentUser().getOrgId(); 
		List<SysOrg> sysOrgs = costService.getCostuintSysOrgList(orgId, 2);
		Res<List<ComboVo>> res = new Res<List<ComboVo>>();
		List<ComboVo> list = new ArrayList<ComboVo>();
		for (SysOrg sysOrg : sysOrgs) {
			ComboVo bean = new ComboVo();
			String orgcode = sysOrg.getOrgcode();
			String orgName = sysOrg.getOrgname();
			bean.setLabel(orgcode);
			bean.setValue(orgName);
			list.add(bean);
		}
		res.setResult(list);
		return res;
	}
	
	@RequestMapping(value = "/getCostuintSysOrgList", method = RequestMethod.POST)
	@ApiOperation("当前机构下-核算对象所对应数据")
	public Res<?> getCostuintSysOrgList(String orgId) {
		List<SysOrg> sysOrgs = costService.getCostuintSysOrgList(orgId, 2);
		Res<List<ComboVo>> res = new Res<List<ComboVo>>();
		List<ComboVo> list = new ArrayList<ComboVo>();
		for (SysOrg sysOrg : sysOrgs) {
			ComboVo bean = new ComboVo();
			String orgcode = sysOrg.getOrgcode();
			String orgName = sysOrg.getOrgname();
			bean.setLabel(orgcode);
			bean.setValue(orgName);
			list.add(bean);
		}
		res.setResult(list);
		return res;
	}
	
	
	/**
	 * 
	 * @param orgIds	机构id；逗号分割
	 * @return
	 */
	@RequestMapping(value = "/getCostuintListOrgId", method = RequestMethod.POST)
	@ApiOperation("维护机构下-核算对象所对应数据")
	public Res<?> getCostuintListOrgId(@RequestBody CostuintQueryDto dto) {
		List<String> orgIds = dto.getOrgIds();
		String orgIds_ = "";
		for (String orgId : orgIds) {
			orgIds_ += ","+orgId;
		}
		if(orgIds_.length()>0) {
			orgIds_ = orgIds_.substring(1);
		}
		Res<List<Costuint>> res = new Res<List<Costuint>>();
		List<Costuint> costuints_ = costService.getCostuintListOrgId(dto,orgIds_);
		res.setResult(costuints_);
		return res;
	}
	
	
	@RequestMapping(value = "/getOperationData", method = RequestMethod.POST)
	@ApiOperation("核算对象下-维护机构")
	public Res<?> getOperationData(@RequestBody CostuintQueryDto dto) {
		String orgId = SysUserUtil.getCurrentUser().getOrgId();
//		Costuint costuint = costService.getCostuintObjById(dto.getId());
//		String costOrgId = costuint.getOrgId();
		List<String> id_s = new ArrayList<String>();
		List<String> org_s = new ArrayList<String>();
		if(dto!=null) {
			String id = dto.getId();
			List<String> ids = dto.getIds();
			if(id!=null&&id.trim().length()>0) {
				id_s.add(id);
				Costuint costUint = costService.getCostuintObjById(id);
				org_s.add(costUint.getOrgId());
			}
			if(ids!=null&&ids.size()>0) {
				for (String id_ : ids) {
					id_s.add(id_);
					Costuint costUint = costService.getCostuintObjById(id_);
					org_s.add(costUint.getOrgId());
				}
			}
		}
		List<SysOrg> sysOrgs = costService.getCostuintSysOrgList(orgId, 2);
		Res<List<ComboVo>> res = new Res<List<ComboVo>>();
		List<ComboVo> list = new ArrayList<ComboVo>();
		for (SysOrg sysOrg : sysOrgs) {
			ComboVo bean = new ComboVo();
			String orgcode = sysOrg.getOrgcode();
			String orgName = sysOrg.getOrgname();
			if(org_s.contains(orgcode)) {
				bean.setDefaultKey(true);
			}
			bean.setLabel(orgcode);
			bean.setValue(orgName);
			list.add(bean);
		}
		res.setResult(list);
		return res;
	}
	
	@RequestMapping(value = "/batchcalculation", method = RequestMethod.POST)
	@ApiOperation("批量计算")
	public Res<?> batchcalculation(@RequestBody CostuintQueryDto dto) throws InterruptedException {
		Res<String> res = new Res<String>();
		String refetch = "0";
		if(dto.isChecked()) {//默认false
			refetch = "1";
		}
		
		Date startTime = DateTimeUtils.parseDate(dto.getStartTime());
		Date endTime = DateTimeUtils.parseDate(dto.getEndTime());
		
		int time = DateTimeUtils.getExistDays(startTime, endTime);
		if(time>=0) {
			SysUser user = SysUserUtil.getCurrentUser();
			String redisKey = "COST:BATCHCALCULATION:"+user.getId();
			//TODO:开始进度
			Object redisList = redisUtil.getObject(redisKey);
			if(redisList==null) {
				for (int i = 0; i <= time; i++) {
					Date date_ = DateTimeUtils.addDays(startTime, i);
					String time_ = DateTimeUtils.formatDateTime(date_).substring(0, 10);
//					System.out.println(time_);
					ProgressBarVo vo = new ProgressBarVo(dto);
					vo.getText(time_, i);//当前日期
					//交接班
					String progressBarTest = "正在计算"+vo.getCurrentDate()+"交接班......";;
					vo.setProgressBarTest(progressBarTest);
					redisUtil.setObject(redisKey, vo);
					autoShiftCalcLogic.batchCalc(dto.getUnitid(), time_, time_,refetch);
					//日报
					String progressBarTest1 = "正在计算"+vo.getCurrentDate()+"日报......";;
					vo.setProgressBarTest(progressBarTest1);
					redisUtil.setObject(redisKey, vo);
					autoShiftCalcLogic.calcDayReport(dto.getUnitid(), time_);
					//周报
					String progressBarTest2 = "正在计算"+vo.getCurrentDate()+"周报......";;
					vo.setProgressBarTest(progressBarTest2);
					redisUtil.setObject(redisKey, vo);
					autoShiftCalcLogic.calcWeekReport(dto.getUnitid(), time_);
				}
			}
			redisUtil.delete(redisKey);
		}
		res.setResult("计算成功!");
		res.setSuccess(true);
		return res;
	}
	
	@ApiOperation("批量进度条")
	@RequestMapping(value = "/batchcalculationText", method = RequestMethod.POST)
	public Res<?> batchcalculationText() {
		Res<Object> res = new Res<Object>();
		SysUser user = SysUserUtil.getCurrentUser();
		String redisKey = "COST:BATCHCALCULATION:"+user.getId();
		//TODO:开始进度
		Object redisList = redisUtil.getObject(redisKey);
		if(redisList==null) {
			res.setSuccess(false);
		}else {
			res.setSuccess(true);
			res.setResult(redisList);
		}
		return res;
	}
}
