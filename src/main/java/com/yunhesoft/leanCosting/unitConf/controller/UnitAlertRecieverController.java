package com.yunhesoft.leanCosting.unitConf.controller;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.unitConf.entity.dto.UnitAlertRecieverQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.UnitAlertRecieverSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.vo.UnitAlertRecieverConfigVo;
import com.yunhesoft.leanCosting.unitConf.service.IUnitAlertRecieverService;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@RestController
@RequestMapping("/leanCosting/unitAlertReciever")
@Api(tags = "核算对象的默认预警设置")
public class UnitAlertRecieverController extends BaseRestController {

	
	@Autowired
	private IUnitAlertRecieverService unitAlertRecieverService;
	
	
	/**
	 *	获取核算对象的默认预警设置数据
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/getUnitAlertRecieverConfigVoList", method = RequestMethod.POST)
	@ApiOperation("获取核算对象的默认预警设置数据")
	public Res<?> getCostKeyDeviceConfVoList(@RequestBody UnitAlertRecieverQueryDto queryDto) {
		Res<List<UnitAlertRecieverConfigVo>> res = new Res<List<UnitAlertRecieverConfigVo>>();
		List<UnitAlertRecieverConfigVo> list = unitAlertRecieverService.getUnitAlertRecieverConfigVoList(queryDto);
		res.setResult(list);
		return res;
	}
	
	
	/**
	 *	保存核算对象的默认预警设置数据
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/saveUnitAlertRecieverData", method = RequestMethod.POST)
	@ApiOperation("保存核算对象的默认预警设置数据")
	public Res<?> saveUnitAlertRecieverData(@RequestBody UnitAlertRecieverSaveDto saveDto) {
		Res<String> res = new Res<String>();
		String ret = unitAlertRecieverService.saveUnitAlertRecieverData(saveDto);
		res.setResult(ret);
		return res;
	}
	
	
}
