package com.yunhesoft.leanCosting.unitConf.controller;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.unitConf.entity.dto.ElemLibQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.ElemLibSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.vo.ElemLibTreeVo;
import com.yunhesoft.leanCosting.unitConf.service.IElemLibService;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@RestController
@RequestMapping("/leanCosting/unitConf/elemLib")
@Api(tags = "要素库设置")
public class ElemLibController extends BaseRestController {

	
	@Autowired
	private IElemLibService elemLibService;
	
	//-------------------------------- 表单台账设置 ↓ ----------------------------------
	
	/**
	 *	获取要素库树形数据
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/getElemLibTreeList", method = RequestMethod.POST)
	@ApiOperation("获取要素库树形数据")
	public Res<?> getElemLibTreeList(@RequestBody ElemLibQueryDto queryDto) {
		Res<List<ElemLibTreeVo>> res = new Res<List<ElemLibTreeVo>>();
		List<ElemLibTreeVo> treeList = elemLibService.getElemLibTreeList(queryDto);
		res.setResult(treeList);
		return res;
	}
	
	/**
	 *	保存要素库数据
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/saveElemLibData", method = RequestMethod.POST)
	@ApiOperation("保存要素库数据")
	public Res<?> saveElemLibData(@RequestBody ElemLibSaveDto saveDto) {
		return Res.OK(elemLibService.saveElemLibData(saveDto));
	}
	
	/**
	 *	判断采集要素是否在采集点中使用
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/isUseByElemId", method = RequestMethod.POST)
	@ApiOperation("判断采集要素是否在采集点中使用")
	public Res<?> isUseByElemId(@RequestBody ElemLibQueryDto queryDto) {
		Res<Boolean> res = new Res<Boolean>();
		Boolean ret = false;
		if(queryDto!=null) {
			String elemId = queryDto.getElemId();
			ret = elemLibService.isUseByElemId(elemId);
		}
		res.setResult(ret);
		return res;
	}
	
	/**
	 *	保存要素排序数据
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/saveElemLibSort", method = RequestMethod.POST)
	@ApiOperation("保存要素排序数据")
	public Res<?> saveElemLibSort(@RequestBody ElemLibSaveDto saveDto) {
		return Res.OK(elemLibService.saveElemLibSort(saveDto));
	}
	
}
