package com.yunhesoft.leanCosting.unitConf.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costlibraryitem;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.baseConfig.service.ICostDeviceTypeService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CopyDataDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostBindOrgDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostuintQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.SynchronousCostUnitDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitcycle;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitmanager;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitversion;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostuintExcelVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostuintVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitInterfaceService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.post.entity.dto.PostParamDto;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.post.service.ISysPostService;
import com.yunhesoft.tmsf.form.entity.po.SFForm;
import com.yunhesoft.tmsf.form.service.IFormManageService;

@Service
public class CostServiceImpl implements ICostService {

	@Autowired
	private EntityService entityService;

	@Autowired
	private ISysOrgService orgService; // 机构

	@Autowired
	private IUnitInterfaceService interfaceService; // 核算相关接口

	@Autowired
	private IUnitMethodService methodService; // 核算相关方法

	@Autowired
	private IFormManageService formManageService;// 表单

	@Autowired
	private IProgramService programService;

	@Autowired
	private ICostDeviceTypeService costDeviceTypeService;

	@Autowired
	private ISysOrgService sysOrgService;

	@Autowired
	private ISysEmployeeInfoService sysEmployeeInfoService;

	@Autowired
	private ISysPostService sysPostService;

	@Autowired
	private ICostToolService toolService;
	
	@Override
	public List<CostuintVo> getCostuintTreeVo(CostDto dto){
		Boolean isadmin = SysUserUtil.isAdmin();
		String orgId = SysUserUtil.getCurrentUser().getOrgId();
		SysUser user = SysUserHolder.getCurrentUser();
		if (ObjUtils.notEmpty(user)) {
			dto.setUserId(user.getId());
			dto.setUserName(user.getRealName());
			dto.setUserOrgCode(user.getOrgId());
			dto.setUserOrgName(user.getOrgName());
			dto.setUser(user);
		}
		Pagination<?> page = null;
		if (dto.getPageSize() != null && dto.getPageSize() > 0) {
			page = Pagination.create(dto.getPageNum() == null ? 1 : dto.getPageNum(), dto.getPageSize());
			dto.setPage(page);
		}
		List<CostuintVo> listVo = new ArrayList<CostuintVo>();
		boolean ismobileInput = false;
		if(dto.isIsmobileInput()) {//移动端,且移动端叶子
			ismobileInput = true;
			dto.setIsmobileInput(false);
		}
		List<Costuint> list = getData(dto);
		List<Costuint> list_ = new ArrayList<Costuint>();
		if (!dto.isProductiveKey()) {// 生产活动，显示生产活动叶子节点
			boolean isSelUnitOrgId = dto.getIsSelUnitOrgId(); // 是否查询核算对象通过维护机构过滤（兼容超级管理员的判断）
			for (Costuint costuint : list) {
				Integer productive = costuint.getProductive();
				if(productive==null) {
					productive = 0;
					costuint.setProductive(productive);
				}
				String _orgId = costuint.getOrgId();
				if (isadmin || !isSelUnitOrgId) {// 超级管理员,查询全部
					list_.add(costuint);
				} else if (_orgId != null && _orgId.equals(orgId)) {
					list_.add(costuint);
				}
			}
		}else {
			list_ = getCostuintListByOrgId(orgId,3);//从操作机构，人，岗位过滤
		}
		if (list_.size() > 0) {
			list = list_;
		}
		// 判断是是树形
		if (dto.isTreeKey()) {
			// TODO:过滤条件
			listVo = toolService.tree(list, dto.isChildrenTreeKey());
			if (dto.isProductiveKey()) {// 生产活动，显示生产活动叶子节点
				if(dto.isFilterateProductive()) {
					listVo = toolService.FilterateProductive(list,listVo,dto.getProductive());
				}else {
					//移动端树形
					listVo = toolService.treeProductive(list,listVo,dto.getProductive(),ismobileInput);
				}
			}
		} else {
			listVo = ObjUtils.copyToList(list, CostuintVo.class);
		}
		return listVo;
	}
	
	/**
	 * 
	 * @param updateList
	 * @param cot
	 */
	@Override
	public void getCostLists(List<Costuint> updateList, String oldTmsort, int cot) {
		// 前序号
		String p_sort = oldTmsort.substring(0, oldTmsort.length() - 4);
		// 后序号
		String s_sort = oldTmsort.substring(oldTmsort.length() - 4, oldTmsort.length());
		int cons = 0;
		for (Costuint costuint : updateList) {
			String sort = String.valueOf(Integer.parseInt(s_sort) + cons);
			if (sort.length() == 1) {
				sort = "000" + sort;
			} else if (sort.length() == 2) {
				sort = "00" + sort;
			} else if (sort.length() == 3) {
				sort = "0" + sort;
			}
			sort = p_sort + sort;
			costuint.setTmSort(sort);
			cons = cons + cot;
		}
	}

	@Override
	public List<Costuint> getData(String pid) {
		// 查询PID类型
		Where where = Where.create();
		if (pid.length() > 0) {
			where.eq(Costuint::getPid, pid);
		} else {
			where.eq(Costuint::getPid, pid);
			where.or();
			where.isNull(Costuint::getPid);
		}
		where.eq(Costuint::getTmused, 1);
		Order order = Order.create();
		order.orderByAsc(Costuint::getTmSort);
		List<Costuint> list = entityService.queryList(Costuint.class, where, order);
		boolean key = arrangeList(list);
		if (key) {
			list = entityService.queryList(Costuint.class, where, order);
		}
		return list;
	}

	@Override
	public String getCostuitSort(String pid, String pSort) {
		// 查询PID类型
		Where where = Where.create();
		if (StringUtils.isNotNull(pid)) {
			where.eq(Costuint::getPid, pid);
		} else {
			where.isNull(Costuint::getPid);
		}
		where.eq(Costuint::getTmused, 1);
		Order order = Order.create();
		order.orderByDesc(Costuint::getTmSort);
		List<Costuint> list = entityService.queryList(Costuint.class, where, order);
		boolean key = arrangeList(list);
		if (key) {
			list = entityService.queryList(Costuint.class, where, order);
		}

		String sort = "0001";// 默认
		if (list.size() > 0) {
			String __sort = list.get(0).getTmSort();
			// 前序号
			String p_sort = __sort.substring(0, __sort.length() - 4);
			// 后序号
			String s_sort = __sort.substring(__sort.length() - 4, __sort.length());
			sort = String.valueOf(Integer.parseInt(s_sort) + 1);
			if (sort.length() == 1) {
				sort = "000" + sort;
			} else if (sort.length() == 2) {
				sort = "00" + sort;
			} else if (sort.length() == 3) {
				sort = "0" + sort;
			}
			sort = p_sort + sort;
		} else {
			if (pSort != null) {
				sort = pSort + sort;
			}
		}
		return sort;
	}

	/**
	 * 重新整理排序位数
	 * 
	 * @param list
	 */
	private boolean arrangeList(List<Costuint> list) {
		Map<String, Costuint> dataMap = list.stream().collect(Collectors.toMap(Costuint::getId, Function.identity()));
		boolean key = false;
		List<Costuint> list_ = new ArrayList<Costuint>();
		for (Costuint costuint : list) {
			String pid = costuint.getPid();
			String tmSort = costuint.getTmSort();
			if (pid == null) {
				costuint.setPid("");
			}
			if (tmSort.length() == 3) {
				list_.add(costuint);
			}
		}
		List<Costuint> updateList = new ArrayList<Costuint>();
		for (Costuint costuint : list_) {
			String TmSort = costuint.getTmSort();
			StringBuffer sql = new StringBuffer();
			String dbtype = entityService.getDatabaseType();
			if ("sqlserver".equalsIgnoreCase(dbtype)) {
				sql.append("select * from Costuint where TMUSED = 1 and (LEN(TMSORT)%3) = 0 and left(TMSORT,3) = '"
						+ TmSort + "' order by TMSORT  ");
			}else if ("mysql".equalsIgnoreCase(dbtype)) {
				sql.append("select * from Costuint where TMUSED = 1 and (LENGTH(TMSORT)%3) = 0 and left(TMSORT,3) = '"
						+ TmSort + "' order by TMSORT  ");
			} else {
				sql.append("select * from Costuint where TMUSED = 1 and MOD(length(TMSORT),3) = 0 and subStr(TMSORT,0,3) = '"
						+ TmSort + "' order by TMSORT ");
			}
			List<Costuint> result = entityService.rawQueryList(Costuint.class, sql.toString());
			if (result != null) {
				for (Costuint costuint_ : result) {
					String tmSort = costuint_.getTmSort();
					int sort_ = tmSort.length() / 3;
					String newSort_ = "";
					for (int i = 0; i < sort_; i++) {
						// 前序号
//					String p_sort = tmSort.substring(0, tmSort.length() - 3);
						// 后序号
						String s_sort = tmSort.substring(tmSort.length() - 3, tmSort.length());
						newSort_ += "0" + s_sort;
					}
					String id_ = costuint_.getId();
					if (dataMap.containsKey(id_)) {
						Costuint bean = dataMap.get(id_);
						bean.setTmSort(newSort_);
						updateList.add(bean);
					}

				}
			}
		}
		if (updateList.size() > 0) {
			entityService.updateByIdBatch(updateList);
			key = true;
		}
		return key;
	}

	@Override
	public List<Costuint> getData(CostDto dto) {
		MethodQueryDto queryDto = new MethodQueryDto();
		if (StringUtils.isNotNull(dto)) {
			queryDto.setIsLedgerEntry(dto.isIsLedgerEntry());
			queryDto.setIsmobileInput(dto.isIsmobileInput());
			queryDto.setIsOntime(dto.isOntime());
			queryDto.setIdList(dto.getIdList());
			queryDto.setPage(dto.getPage());
			if (StringUtils.isNotNull(dto.getPid())) {
				queryDto.setPid(dto.getPid());
			}
			if (dto.getProductive()!=null) {
				queryDto.setProductive(dto.getProductive());
			}
			queryDto.setPage(dto.getPage());
			queryDto.setIsSelUnitOrgId(dto.getIsSelUnitOrgId()); //是否查询核算对象通过维护机构过滤（兼容超级管理员的判断）
		} else {
			queryDto = null;
		}
		List<Costuint> list = methodService.getCostuintList(queryDto);
		boolean key = arrangeList(list);
		if (key) {
			list = methodService.getCostuintList(queryDto);
		}
		return list;
	}

	/**
	 * 获取核算对象
	 * 
	 * @param id
	 * @return
	 */
	@Override
	public Costuint getCostuintObjById(String id) {
		Costuint result = new Costuint();
		if (StringUtils.isNotEmpty(id)) {
			Costuint queryObj = entityService.queryObjectById(Costuint.class, id);
			if (StringUtils.isNotNull(queryObj)) {
				result = queryObj;
			}
		}
		return result;
	}

	@Override
	public String saveData(CostDto dto) {
		String rv = "";
		if (StringUtils.isNotNull(dto)) {
			String editType = dto.getEditType();
			List<CostuintVo> list = dto.getData();
			if (StringUtils.isNotEmpty(editType) && StringUtils.isNotEmpty(list)) {
				List<Costuint> saveList = new ArrayList<Costuint>();
				for (CostuintVo vo : list) {
					Costuint obj = ObjUtils.copyTo(vo, Costuint.class);
					saveList.add(obj);
				}
				if (StringUtils.isNotEmpty(saveList)) {
					MethodSaveDto saveDto = new MethodSaveDto();
					saveDto.setEditType(editType);
					saveDto.setCostuint_list(saveList);
					rv = methodService.saveCostuintData(saveDto);
				}
			}
		}
		return rv;
	}
	
	/**
	 *	作业活动保存
	 * @param vo 保存数据
	 * @param rowFlat	操作状态：-1、删除；其他是保存；
	 * @return
	 */
	@Override
	public String saveDataVo(Costuint vo, Integer rowFlat) {
		String result = "";
		List<Costuint> addList = new ArrayList<Costuint>();
		List<Costuint> updList = new ArrayList<Costuint>();
		if(vo != null && StringUtils.isNotEmpty(vo.getId())) {
			rowFlat = rowFlat==null?0:rowFlat;
			if(rowFlat == -1) { //删除
				vo.setTmused(0);
				updList.add(vo);
			}else { //保存
				String pid_v = vo.getPid()==null?"":vo.getPid();
				
				//查询存在记录
				String maxPx = "";
				Map<String, Costuint> sameNameMap = new HashMap<String, Costuint>();
				Map<String, Costuint> dataMap = new HashMap<String, Costuint>();
				MethodQueryDto dto = new MethodQueryDto();
				dto.setProductive(vo.getProductive());
				dto.setPid(vo.getPid());
				List<Costuint> dataList = methodService.getCostuintList(dto);
				if (StringUtils.isNotEmpty(dataList)) {
					for (int i = 0; i < dataList.size(); i++) {
						Costuint dataObj = dataList.get(i);
						String id = dataObj.getId();
						if(StringUtils.isNotEmpty(id)&&!dataMap.containsKey(id)) {
							dataMap.put(id, dataObj);
						}
						String pid = dataObj.getPid()==null?"":dataObj.getPid();
						if(pid_v.equals(pid)) { //空值会查询全部，通过次判断过滤数据
							String name = dataObj.getName();
							if(StringUtils.isNotEmpty(name)&&!sameNameMap.containsKey(name)) {
								sameNameMap.put(name, dataObj);
							}
							String tmSort = dataObj.getTmSort();
							if(StringUtils.isNotEmpty(tmSort)) {
								maxPx = tmSort;
							}
						}
					}
				}
				String saveId = vo.getId();
				String saveName = vo.getName()==null? "" : vo.getName().trim(); //名称
				String labelStr = "";
				Integer productive_vo = vo.getProductive();
				if(productive_vo!=null) {
					if(productive_vo==1) {
						labelStr = "活动";
					}else if(productive_vo==2) {
						labelStr = "台账模型";
					}
				}
				Costuint unitObj = methodService.getCostuintById(saveId);
				if (unitObj!=null) { //修改
					if (StringUtils.isNotEmpty(sameNameMap) && StringUtils.isNotEmpty(saveName) && sameNameMap.containsKey(saveName)) {
						String sameId = sameNameMap.get(saveName).getId();
						if (StringUtils.isNotEmpty(sameId) && !sameId.equals(saveId)) {
							result = labelStr+"名称【" + saveName + "】已存在，不能重复设置！";
							return result;
						}
					}
					String pid_u = unitObj.getPid()==null?"":unitObj.getPid();
					if(!pid_v.equals(pid_u)) { //分类改变活动重新排序
						String newMaxPx = this.getNewSort(maxPx, 1);
						vo.setTmSort(newMaxPx);
					}
					updList.add(vo);
				} else { //新增
					if(StringUtils.isNotEmpty(sameNameMap)&&StringUtils.isNotEmpty(saveName)&&sameNameMap.containsKey(saveName)) {
						result = labelStr+"名称【" + saveName + "】已存在，不能重复设置！";
						return result;
					}
					String newMaxPx = this.getNewSort(maxPx, 1);
					vo.setTmSort(newMaxPx);
					addList.add(vo);
				}
			}
		}else {
			result = "没有需要保存的数据！";
		}
		if(StringUtils.isEmpty(result)&&(StringUtils.isNotEmpty(addList)||StringUtils.isNotEmpty(updList))) {
			result = methodService.saveCostuintData(addList, updList, null);
		}
		return result;
	}
	

	@Override
	public String saveDataVo(CostuintVo vo) {
		String result = "";
		List<Costuint> dataList = methodService.getCostuintList(null);
		Map<String, Costuint> sameNameMap = new HashMap<String, Costuint>();
		Map<String, Costuint> dataMap = new HashMap<String, Costuint>();
		if (StringUtils.isNotEmpty(dataList)) {
			sameNameMap = dataList.stream().collect(Collectors.toMap(Costuint::getName, Function.identity()));
			dataMap = dataList.stream().collect(Collectors.toMap(Costuint::getId, Function.identity()));
		}

		String saveId = vo.getId();
		String name = vo.getName() == null ? "" : vo.getName().trim();
		if (StringUtils.isNotEmpty(dataMap) && StringUtils.isNotEmpty(saveId) && dataMap.containsKey(saveId)) { // 修改
			if (StringUtils.isNotEmpty(sameNameMap) && StringUtils.isNotEmpty(name) && sameNameMap.containsKey(name)) {
				String sameId = sameNameMap.get(name).getId();
				if (StringUtils.isNotEmpty(sameId) && !sameId.equals(saveId)) {
					result = "考核对象名称【" + name + "】已存在，不能重复设置！";
					return result;
				}
			}
		} else {
			if (sameNameMap.containsKey(name)) {
				return "考核对象名称【" + name + "】已存在，不能添加";
			}
		}

		int key = 0;
		Costuint dataObj = new Costuint();
		BeanUtils.copyProperties(vo, dataObj); // 赋予返回对象
		if (StringUtils.isNotNull(dataObj.getId())) {

			Costuint _dataObj = entityService.queryObjectById(Costuint.class, dataObj.getId());
			_dataObj.setUnittype(dataObj.getUnittype());
			_dataObj.setOrgId(dataObj.getOrgId());
			_dataObj.setMemo(dataObj.getMemo());
			_dataObj.setName(dataObj.getName());
			_dataObj.setUsStatus(dataObj.getUsStatus());
			_dataObj.setMobileInput(dataObj.getMobileInput());
			_dataObj.setCoordinateX(dataObj.getCoordinateX());
			_dataObj.setCoordinateY(dataObj.getCoordinateY());
			_dataObj.setOperatingRadius(dataObj.getOperatingRadius());
			_dataObj.setLedgerEntry(dataObj.getLedgerEntry());
			//TODO:2024-02-27添加
			_dataObj.setProductive(dataObj.getProductive());
			_dataObj.setProductiveType(dataObj.getProductiveType());
			_dataObj.setFormId(dataObj.getFormId());
			_dataObj.setDispatchDesk(dataObj.getDispatchDesk());
			_dataObj.setUseAccounting(dataObj.getUseAccounting());
			_dataObj.setIsOntime(dataObj.getIsOntime());
			_dataObj.setDeviceIds(dataObj.getDeviceIds());
			_dataObj.setAnalysisTemplate(dataObj.getAnalysisTemplate());
			// 更新
			key = entityService.update(_dataObj);
			if (key > 1) {
				result = "更新失败（核算对象）！";
			}
		} else {
			String pid = vo.getPid();
			// 获得上级sort
			Costuint result1 = getCostuintObjById(pid);
			String _sort = result1.getTmSort();
			// 查询PID类型
			// 获得新字段排序
			String sort = getCostuitSort(pid, _sort);
			dataObj.setId(TMUID.getUID());
			dataObj.setTmSort(sort);
			dataObj.setTmused(1);
			key = entityService.insert(dataObj);
			if (key > 1) {
				result = "添加失败（核算对象）！";
			} else {
				String deviceType = dataObj.getUnittype();
				programService.initDefaultProgramItem(deviceType);
			}

		}
		BeanUtils.copyProperties(dataObj, vo); // 赋予返回对象
		return result;
	}

	@Override
	public String addVersion(CostDto dto) {
		String result = "";
		if (StringUtils.isNotNull(dto)) {
			String unitid = dto.getUnitCode();
			String begintime_copy = dto.getBegintime_copy();

			Costunitversion obj = new Costunitversion();
			obj.setId(TMUID.getUID());
			obj.setUnitid(unitid);
			obj.setBegintime(dto.getVersion());
			obj.setMemo(dto.getMemo());

			List<Costunitversion> costunitversion_list = new ArrayList<Costunitversion>();
			costunitversion_list.add(obj);

			MethodSaveDto saveDto = new MethodSaveDto();
			saveDto.setUnitid_copy(unitid);
			saveDto.setBegintime_copy(begintime_copy);
			saveDto.setCostunitversion_list(costunitversion_list);
			String ret = methodService.saveCostunitversionData(saveDto);
			if (StringUtils.isNotEmpty(ret)) {
				result = "false";
			}
		}
		return result;
	}

	@Override
	public List<String> getVersionList(CostDto dto) {
		Where where = Where.create();
		where.eq(Costunitversion::getUnitid, dto.getUnitCode());
		Order order = Order.create();
		order.orderByDesc(Costunitversion::getBegintime);
		List<Costunitversion> list = entityService.queryList(Costunitversion.class, where, order);

		if (StringUtils.isEmpty(list)) {
			Costunitversion obj = new Costunitversion();
			obj.setId(TMUID.getUID());
			// obj.setBegintime(DateTimeUtils.getNowYear()+"-01-01");
			obj.setBegintime("2020-01-01");
			obj.setUnitid(dto.getUnitCode());
			obj.setMemo("系统自动生成");
			entityService.insert(obj);
			list.add(obj);
		}

		List<String> rlist = new ArrayList<String>();
		for (Costunitversion obj : list) {
			rlist.add(obj.getBegintime());
		}

		return rlist;
	}
	
	@Override
	public List<Costunitoperator> getCostunitoperatorOrgList(CostBindOrgDto dto) {
		List<Costunitoperator> result = new ArrayList<Costunitoperator>();
		try {
			String unitid = ""; // 核算对象ID
			String objType = ""; // 机构类型
			String objid = ""; // 机构ID
			List<String> objids = new ArrayList<String>(); // 机构ID
			if (StringUtils.isNotNull(dto)) {
				unitid = dto.getUnitid();
				objid = dto.getObjid();
				objType = dto.getObjType();
				objids = dto.getObjids();
			}

			// 检索条件
			Where where = Where.create();
			if (StringUtils.isEmpty(objType)) {
				return null;
			}else {
				where.eq(Costunitoperator::getObjType,objType);
			}
			if (StringUtils.isNotEmpty(unitid)) {
				where.eq(Costunitoperator::getUnitid, unitid);
			}
			if (StringUtils.isNotEmpty(objid)) {
				where.eq(Costunitoperator::getObjid, objid);
			}else if (StringUtils.isNotEmpty(objids)) {
				where.in(Costunitoperator::getObjid, objids.toArray());
			}

			// 排序
			Order order = Order.create();
			order.orderByAsc(Costunitoperator::getTmsort);
			result = entityService.queryData(Costunitoperator.class, where, order, null);
		} catch (Exception e) {
			result = null;
		}
		return result;
	}

	/**
	 * 获取核算对象的操作对象数据列表
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public List<Costunitoperator> getCostunitoperatorList(CostBindOrgDto dto) {
		List<Costunitoperator> result = new ArrayList<Costunitoperator>();
		try {
			String unitid = ""; // 核算对象ID
			String objType = ""; // 机构类型
			String objid = ""; // 机构ID
			List<String> objids = new ArrayList<String>(); // 机构ID
			if (StringUtils.isNotNull(dto)) {
				unitid = dto.getUnitid();
				objid = dto.getObjid();
				objType = dto.getObjType();
				objids = dto.getObjids();
			}

			// 检索条件
			Where where = Where.create();
			if (StringUtils.isNotEmpty(unitid)) {
				where.eq(Costunitoperator::getUnitid, unitid);
			}
			if (StringUtils.isNotEmpty(objid)) {
				where.eq(Costunitoperator::getObjid, objid);
			}else if (StringUtils.isNotEmpty(objids)) {
				where.in(Costunitoperator::getObjid, objids.toArray());
			}

			// 排序
			Order order = Order.create();
			order.orderByAsc(Costunitoperator::getTmsort);

			List<Costunitoperator> list = entityService.queryData(Costunitoperator.class, where, order, null);
			List<String> unitids = new ArrayList<String>();
			for (Costunitoperator costunitoperator : list) {
				String unitid_ = costunitoperator.getUnitid();
				unitids.add(unitid_);
			}
			//需要剔除的核算对象ID
			List<String> costUnitId = new ArrayList<String>();
			if(unitids.size()>0) {
					
				if("user".equals(objType)) {
					
				}else if("post".equals(objType)) {
					// 检索条件
					Where where1 = Where.create();
					if (StringUtils.isNotEmpty(unitids)) {
						where1.in(Costunitoperator::getUnitid, unitids.toArray());
					}
					List<Costunitoperator> list1 = entityService.queryData(Costunitoperator.class, where1, order, null);
					for (Costunitoperator costunitoperator : list1) {
						String objtype = costunitoperator.getObjType();
						String unitid_ = costunitoperator.getUnitid();
						if("user".equals(objtype)){
							if(!costUnitId.contains(unitid_)) {
								costUnitId.add(unitid_);
							}
						}
					}
				}else if("org".equals(objType)) {
					// 检索条件
					Where where1 = Where.create();
					if (StringUtils.isNotEmpty(unitids)) {
						where1.in(Costunitoperator::getUnitid, unitids.toArray());
					}
					List<Costunitoperator> list1 = entityService.queryData(Costunitoperator.class, where1, order, null);
					for (Costunitoperator costunitoperator : list1) {
						String objtype = costunitoperator.getObjType();
						String unitid_ = costunitoperator.getUnitid();
						if("user".equals(objtype)||"post".equals(objtype)){
							if(!costUnitId.contains(unitid_)) {
								costUnitId.add(unitid_);
							}
						}
					}
				}
			}
			
			for (int i = 0; i < list.size(); i++) {
				Costunitoperator costunitoperator = list.get(i);
				String unitid_ = costunitoperator.getUnitid();
				if(costUnitId.contains(unitid_)) {
					list.remove(costunitoperator);
					i--;
				}
			}
			
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}

	/**
	 * 获取核算对象的操作对象id列表--Map
	 * 
	 * @param tenantId 租户id
	 * @param unitid   核算对象id
	 * @return
	 */
	@Override
	public LinkedHashMap<String, List<String>> getCostunitoperatorMap(String tenantId, String unitid) {
		LinkedHashMap<String, List<String>> map = new LinkedHashMap<String, List<String>>();
		MethodQueryDto dto = new MethodQueryDto();
		dto.setTenantId(tenantId);
		dto.setUnitid(unitid);
		List<Costunitoperator> operatorList = methodService.getCostunitoperatorList(dto);
		if (StringUtils.isNotEmpty(operatorList)) {
			for (int i = 0; i < operatorList.size(); i++) {
				Costunitoperator obj = operatorList.get(i);
				String unitid_o = obj.getUnitid();
				String objid_o = obj.getObjid();
				if (map.containsKey(unitid_o)) {
					List<String> list = map.get(unitid_o);
					if (!list.contains(objid_o)) {
						list.add(objid_o);
						map.put(unitid_o, list);
					}
				} else {
					List<String> list = new ArrayList<String>();
					list.add(objid_o);
					map.put(unitid_o, list);
				}
			}
		}
		return map;
	}

	/**
	 * 获取核算对象的管理对象数据列表
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public List<Costunitmanager> getCostunitmanagerList(CostBindOrgDto dto) {
		List<Costunitmanager> result = new ArrayList<Costunitmanager>();
		try {
			String unitid = ""; // 核算对象ID
			String objid = ""; // 机构ID
			List<String> objids = new ArrayList<String>(); // 机构ID
			if (StringUtils.isNotNull(dto)) {
				unitid = dto.getUnitid();
				objid = dto.getObjid();
				objids = dto.getObjids();
			}

			// 检索条件
			Where where = Where.create();
			if (StringUtils.isNotEmpty(unitid)) {
				where.eq(Costunitmanager::getUnitid, unitid);
			}
			if (StringUtils.isNotEmpty(objid)) {
				where.eq(Costunitmanager::getObjid, objid);
			}else if (StringUtils.isNotEmpty(objids)) {
				where.in(Costunitmanager::getObjid, objids.toArray());
			}

			// 排序
			Order order = Order.create();
			order.orderByAsc(Costunitmanager::getTmsort);

			List<Costunitmanager> list = entityService.queryData(Costunitmanager.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}

	/**
	 * 获取核算对象的绑定机构列表
	 * 
	 * @param dto
	 * @return
	 */
	public CostBindOrgDto getCostUnitBindOrg(CostBindOrgDto dto) {
		CostBindOrgDto result = null;
		if (StringUtils.isNotNull(dto)) {
			String unitid = dto.getUnitid(); // 核算对象ID
			if (StringUtils.isNotEmpty(unitid)) {
				String operOrgId = "";
				String operOrgName = "";
				String operUserId = "";// 人员ID
				String operUserName = "";
				String operOrgPostId = "";// 机构岗位ID
				String operOrgPostName = "";

				String manageOrgId = "";
				String manageOrgName = "";

				//
				List<SysOrg> orgList = new ArrayList<SysOrg>();
				List<SysEmployeeInfo> infoList = new ArrayList<SysEmployeeInfo>();
				List<SysPost> postList = new ArrayList<SysPost>();
				//
				List<String> orgIdList = new ArrayList<String>();
				List<String> postIdList = new ArrayList<String>();
				List<String> empIdList = new ArrayList<String>();
				// 操作机构
				List<Costunitoperator> operList = this.getCostunitoperatorList(dto);
//				String ids_ = "";// 机构iD
//				String posts_ = "";
				if (StringUtils.isNotEmpty(operList)) {
					for (int i = 0; i < operList.size(); i++) {
						Costunitoperator obj = operList.get(i);
						String objid = obj.getObjid();
						String objType = obj.getObjType();
						if (StringUtils.isNotEmpty(objid) && !orgIdList.contains(objid)) {
							if ("post".equals(objType)) {
								postIdList.add(objid);
							} else if ("user".equals(objType)) {
								empIdList.add(objid);
							}else{
//								 if ("org".equals(objType))
								//默认ORG
								orgIdList.add(objid);
							}
						}
					}
//					if (orgIdList.size() > 0) {
//						for (String id : orgIdList) {
//							ids_ += "," + id;
//						}
//						ids_ = ids_.substring(1);
//					}

				}
				// 管理机构
				List<Costunitmanager> manageList = this.getCostunitmanagerList(dto);
				if (StringUtils.isNotEmpty(manageList)) {
					for (int i = 0; i < manageList.size(); i++) {
						Costunitmanager obj = manageList.get(i);
						String objid = obj.getObjid();
						if (StringUtils.isNotEmpty(objid) && !orgIdList.contains(objid)) {
							orgIdList.add(objid);
						}
					}
				}
				// 根据机构ID获取机构信息
				if (StringUtils.isNotEmpty(orgIdList)) {
					try {
						orgList = orgService.getOrgListById(orgIdList);
					} catch (Exception e) {
						orgList = null;
					}
				}
				// 人员
				if (StringUtils.isNotEmpty(empIdList)) {
					try {
						String userIds_ = "";
						if (empIdList.size() > 0) {
							for (String id : empIdList) {
								userIds_ += "," + id;
							}
							userIds_ = userIds_.substring(1);
						}
						EmpParamDto param = new EmpParamDto();
						param.setEmpid(userIds_);
						infoList = sysEmployeeInfoService.getEmployee(param);// OrgListById(orgIdList);
					} catch (Exception e) {
						infoList = null;
					}
				}
				// 机构岗位
				if (StringUtils.isNotEmpty(postIdList)) {
					try {
						PostParamDto param = new PostParamDto();
						String posts = "";
						for (String postId : postIdList) {
							String[] post_ = postId.split("_");
							posts += "," + post_[1];
						}
						posts = posts.substring(1);
						param.setId(posts);
						postList = sysPostService.getPost(param);
					} catch (Exception e) {
						postList = null;
					}
				}
//				if (infoList != null) {// 人员
				/**
				 * 操作
				 */
				if (infoList != null) {// 人员
					for (SysEmployeeInfo sysEmployeeInfo : infoList) {
						String objid = sysEmployeeInfo.getId();
						String orgname = sysEmployeeInfo.getEmpname();
						if (StringUtils.isNotEmpty(objid)) {
							operUserId += ",";
							operUserName += ",";
						}
						operUserId += objid;
						operUserName += orgname;
					}
					if (operUserId.trim().length() > 0) {
						operUserId = operUserId.substring(1);
						operUserName = operUserName.substring(1);
					}
				}
				if (postList != null) {//岗位
//					Map<String, SysPost> orgMap = new HashMap<String, SysPost>();
//					if (orgList.size() > 0) {
//						orgMap = postList.stream().collect(Collectors.toMap(SysPost::getId, Function.identity()));
//					}
					for (String objid : postIdList) {
						String[] post_ = objid.split("_");
						String postId_ = post_[1];
						SysPost sysPost = entityService.queryObjectById(SysPost.class, postId_);
						String orgname = sysPost.getName();
						if (StringUtils.isNotEmpty(objid)) {
							operOrgPostId += ",";
							operOrgPostName += ",";
						}
						operOrgPostId += objid;
						operOrgPostName += orgname;
					}
					if (operOrgPostId.trim().length() > 0) {
						operOrgPostId = operOrgPostId.substring(1);
						operOrgPostName = operOrgPostName.substring(1);
					}
				}
				
//				}

				if (orgList != null) {
					Map<String, SysOrg> orgMap = new HashMap<String, SysOrg>();
					if (orgList.size() > 0) {
						orgMap = orgList.stream().collect(Collectors.toMap(SysOrg::getId, Function.identity()));
					}
					if (StringUtils.isNotEmpty(operList)) {
						for (int i = 0; i < operList.size(); i++) {
							Costunitoperator obj = operList.get(i);
							String objid = obj.getObjid();
							if (StringUtils.isNotEmpty(objid) && StringUtils.isNotEmpty(orgMap)
									&& orgMap.containsKey(objid)) {
								String orgname = orgMap.get(objid).getOrgname();
								if (StringUtils.isNotEmpty(operOrgId)) {
									operOrgId += ",";
									operOrgName += ",";
								}
								operOrgId += objid;
								operOrgName += orgname;
							}
						}
					}
					if (StringUtils.isNotEmpty(manageList)) {
						for (int i = 0; i < manageList.size(); i++) {
							Costunitmanager obj = manageList.get(i);
							String objid = obj.getObjid();
							if (StringUtils.isNotEmpty(objid) && StringUtils.isNotEmpty(orgMap)
									&& orgMap.containsKey(objid)) {
								String orgname = orgMap.get(objid).getOrgname();
								if (StringUtils.isNotEmpty(manageOrgId)) {
									manageOrgId += ",";
									manageOrgName += ",";
								}
								manageOrgId += objid;
								manageOrgName += orgname;
							}
						}
					}
				}
				// 获取周期数据
				List<Costunitcycle> cycleList = this.getCostunitcycleList(dto);

				// 获得核算对象内容
				Costuint costuint = entityService.queryObjectById(Costuint.class, unitid);
				// 制造类型ID
				String unittype = costuint.getUnittype();
				String unittypeName = null;
				if (unittype != null) {
					Devicetypelibrary devicetypelibrary = entityService.queryObjectById(Devicetypelibrary.class,
							unittype);
					if (devicetypelibrary != null) {
						unittypeName = devicetypelibrary.getDtname();
					}

				}

				// 表单ID
				String formId = costuint.getFormId();
				String formName = null;
				if (formId != null) {
					SFForm sfForm = formManageService.queryFormInfoById(formId);
					if (sfForm != null) {
						formName = sfForm.getName();
					}
				}

				// 组队返回数据
				result = new CostBindOrgDto();
				result.setUnitid(unitid); // 核算对象ID
				result.setOperOrgId(operOrgId);
				result.setOperOrgName(operOrgName);

				result.setOperUserId(operUserId);// 人员ID
				result.setOperUserName(operUserName);
				result.setOperOrgPostId(operOrgPostId);// 机构岗位ID
				result.setOperOrgPostName(operOrgPostName);
				result.setManageOrgId(manageOrgId);
				result.setManageOrgName(manageOrgName);

				// 表单
				result.setFormId(formId);
				result.setFormName(formName);
				// 类型
				result.setUnittype(unittype);
				result.setUnittypeName(unittypeName);

				result.setOrgId(costuint.getOrgId());
				if (costuint.getOrgId() != null) {
					SysOrg sysOrg = orgService.getOrgByOrgcode(costuint.getOrgId());
					if (sysOrg != null) {
						String orgName = sysOrg.getOrgname();
						result.setOrgName(orgName);
					}
				}
				result.setMemo(costuint.getMemo());
				result.setName(costuint.getName());
				result.setLedgerEntry(costuint.getLedgerEntry());
				result.setUsStatus(costuint.getUsStatus());
				result.setMobileInput(costuint.getMobileInput());
				result.setCoordinateX(costuint.getCoordinateX());
				result.setCoordinateY(costuint.getCoordinateY());
				result.setOperatingRadius(costuint.getOperatingRadius());

				//TODO:2024-02-27添加
				result.setProductive(costuint.getProductive());
				result.setProductiveType(costuint.getProductiveType());
				result.setDispatchDesk(costuint.getDispatchDesk());
				result.setIsOntime(costuint.getIsOntime());
				result.setUseAccounting(costuint.getUseAccounting());
				result.setDeviceIds(costuint.getDeviceIds());
				result.setAnalysisTemplate(costuint.getAnalysisTemplate());
				
				if(costuint.getDeviceIds()!=null) {
					Where where = Where.create();
					String[] deviceIds = costuint.getDeviceIds().split(",");
					where.in(Costuint::getId, deviceIds);
					Order order = Order.create();
					order.orderByDesc(Costuint::getTmSort);
					List<Costuint> list = entityService.queryList(Costuint.class, where, order);
					String deviceNames = "";
					for (Costuint costuint2 : list) {
						deviceNames += ","+costuint2.getName();
					}
					if(deviceNames.length()>0) {
						deviceNames = deviceNames.substring(1);
					}
					result.setDeviceNames(deviceNames);
				}
				result.setCycleList(cycleList);
			}
		}
		return result;
	}

	/**
	 * 保存方案项目树形数据
	 * 
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveCostUnitBindOrgData(CostBindOrgDto saveDto) {
		String result = "";
		List<Costunitoperator> addOperList = new ArrayList<Costunitoperator>();
		List<Costunitoperator> updOperList = new ArrayList<Costunitoperator>();
		List<Costunitoperator> delOperList = new ArrayList<Costunitoperator>();
		List<Costunitmanager> addManageList = new ArrayList<Costunitmanager>();
		List<Costunitmanager> updManageList = new ArrayList<Costunitmanager>();
		List<Costunitmanager> delManageList = new ArrayList<Costunitmanager>();
		if (saveDto != null) {
			String unitid = saveDto.getUnitid();
			String operOrgId = saveDto.getOperOrgId();// 操作机构ID
			String operUserId = saveDto.getOperUserId();// 操作人员ID
			String operOrgPostId = saveDto.getOperOrgPostId();// 操作机构岗位ID

			String manageOrgId = saveDto.getManageOrgId();

			String unittype = saveDto.getUnittype();

			String formId = saveDto.getFormId();

			String pid = saveDto.getPid();

			Integer ledgerEntry = saveDto.getLedgerEntry();
			Integer usStatus = saveDto.getUsStatus();
			Integer mobileInput = saveDto.getMobileInput();
		    
		    Double coordinateX = saveDto.getCoordinateX();
		    Double coordinateY = saveDto.getCoordinateY();
		    Double operatingRadius = saveDto.getOperatingRadius();
			
			String orgId = saveDto.getOrgId();

//		    String orgName  = saveDto.getOrgName();
			String memo = saveDto.getMemo();
			String name = saveDto.getName();
			Integer type = saveDto.getType();
			
			//TODO:2024-02-27添加
			Integer productive = saveDto.getProductive();
			Integer productiveType = saveDto.getProductiveType();
			Integer isOntime = saveDto.getIsOntime();
			Integer dispatchDesk = saveDto.getDispatchDesk();
			Integer useAccounting = saveDto.getUseAccounting();
						
			String deviceIds = saveDto.getDeviceIds();
			String deviceNames = saveDto.getDeviceNames();
			String analysisTemplate = saveDto.getAnalysisTemplate();
			/** 名称 */
//		    String unittypeName = saveDto.getUnittypeName();

			Costuint costuint = new Costuint();
			CostuintVo obj = new CostuintVo();
			if (StringUtils.isNotEmpty(unitid)) {
				// 编辑时
				costuint = entityService.queryObjectById(Costuint.class, unitid);
			}
			// 制造类型ID
			costuint.setUnittype(unittype);
			costuint.setPid(pid);
			// 表单ID
			costuint.setFormId(formId);
			costuint.setOrgId(orgId);
			costuint.setMemo(memo);
			costuint.setType(type);
			costuint.setName(name);
			costuint.setLedgerEntry(ledgerEntry);
			costuint.setUsStatus(usStatus);
			
			costuint.setMobileInput(mobileInput);
			costuint.setCoordinateX(coordinateX);
			costuint.setCoordinateY(coordinateY);
			costuint.setOperatingRadius(operatingRadius);

			//TODO:2024-02-27添加
			costuint.setProductive(productive);
			costuint.setProductiveType(productiveType);
			costuint.setIsOntime(isOntime);
			costuint.setDispatchDesk(dispatchDesk);
			costuint.setUseAccounting(useAccounting);
			costuint.setDeviceIds(deviceIds);
			costuint.setDeviceNames(deviceNames);
			costuint.setAnalysisTemplate(analysisTemplate);
			
			BeanUtils.copyProperties(costuint, obj); // 赋予返回对象
			String str = saveDataVo(obj);
			if (str.trim().length() > 0) {
				return str;
			} else {
				unitid = obj.getId();
				saveDto.setNewUnitid(unitid);
			}
			// 管理机构和操作机构
			CostBindOrgDto dto = new CostBindOrgDto();
			dto.setUnitid(unitid);
			// 操作机构
			Map<String, Costunitoperator> operMap = new HashMap<String, Costunitoperator>();
			List<Costunitoperator> operList = this.getCostunitoperatorList(dto);
			if (StringUtils.isNotEmpty(operList)) {
				operMap = operList.stream().collect(Collectors.toMap(Costunitoperator::getObjid, Function.identity()));
			}
			// 管理机构
			Map<String, Costunitmanager> manageMap = new HashMap<String, Costunitmanager>();
			List<Costunitmanager> manageList = this.getCostunitmanagerList(dto);
			if (StringUtils.isNotEmpty(manageList)) {
				manageMap = manageList.stream()
						.collect(Collectors.toMap(Costunitmanager::getObjid, Function.identity()));
			}
			// 新增或修改
			if (StringUtils.isNotEmpty(operOrgId)) {// 机构
				String[] operOrgIdArr = operOrgId.split(",");
				for (int i = 0; i < operOrgIdArr.length; i++) {
					String objid = operOrgIdArr[i];
					if (StringUtils.isNotEmpty(operMap) && operMap.containsKey(objid)) {
						Costunitoperator operObj = operMap.get(objid);
						int tmsort = operObj.getTmsort() == null ? 0 : operObj.getTmsort();
						if (tmsort != i + 1) {
							operObj.setTmsort(i + 1);
							updOperList.add(operObj);
						}
						operMap.remove(objid);
					} else {
						Costunitoperator operObj = new Costunitoperator();
						operObj.setId(TMUID.getUID());
						operObj.setObjid(objid);
						operObj.setObjType("org");
						operObj.setUnitid(unitid);
						operObj.setTmsort(i + 1);
						addOperList.add(operObj);
					}
				}
			}
			if (StringUtils.isNotEmpty(operUserId)) {// 人员
				String[] operOrgIdArr = operUserId.split(",");
				for (int i = 0; i < operOrgIdArr.length; i++) {
					String objid = operOrgIdArr[i];
					if (StringUtils.isNotEmpty(operMap) && operMap.containsKey(objid)) {
						Costunitoperator operObj = operMap.get(objid);
						int tmsort = operObj.getTmsort() == null ? 0 : operObj.getTmsort();
						if (tmsort != i + 1) {
							operObj.setTmsort(i + 1);
							updOperList.add(operObj);
						}
						operMap.remove(objid);
					} else {
						Costunitoperator operObj = new Costunitoperator();
						operObj.setId(TMUID.getUID());
						operObj.setObjid(objid);
						operObj.setObjType("user");
						operObj.setUnitid(unitid);
						operObj.setTmsort(i + 1);
						addOperList.add(operObj);
					}
				}
			}
			if (StringUtils.isNotEmpty(operOrgPostId)) {// 岗位
				String[] operOrgIdArr = operOrgPostId.split(",");
				for (int i = 0; i < operOrgIdArr.length; i++) {
					String objid = operOrgIdArr[i];
					if (StringUtils.isNotEmpty(operMap) && operMap.containsKey(objid)) {
						Costunitoperator operObj = operMap.get(objid);
						int tmsort = operObj.getTmsort() == null ? 0 : operObj.getTmsort();
						if (tmsort != i + 1) {
							operObj.setTmsort(i + 1);
							updOperList.add(operObj);
						}
						operMap.remove(objid);
					} else {
						Costunitoperator operObj = new Costunitoperator();
						operObj.setId(TMUID.getUID());
						operObj.setObjid(objid);
						operObj.setObjType("post");
						operObj.setUnitid(unitid);
						operObj.setTmsort(i + 1);
						addOperList.add(operObj);
					}
				}
			}

			if (StringUtils.isNotEmpty(manageOrgId)) {
				String[] manageOrgIdArr = manageOrgId.split(",");
				for (int i = 0; i < manageOrgIdArr.length; i++) {
					String objid = manageOrgIdArr[i];
					if (StringUtils.isNotEmpty(manageMap) && manageMap.containsKey(objid)) {
						Costunitmanager operObj = manageMap.get(objid);
						int tmsort = operObj.getTmsort() == null ? 0 : operObj.getTmsort();
						if (tmsort != i + 1) {
							operObj.setTmsort(i + 1);
							updManageList.add(operObj);
						}
						manageMap.remove(objid);
					} else {
						Costunitmanager operObj = new Costunitmanager();
						operObj.setId(TMUID.getUID());
						operObj.setObjid(objid);
						operObj.setUnitid(unitid);
						operObj.setTmsort(i + 1);
						addManageList.add(operObj);
					}
				}
			}
			// 删除
			if (StringUtils.isNotEmpty(operMap)) {
				Iterator<Map.Entry<String, Costunitoperator>> operIterMap = operMap.entrySet().iterator();
				while (operIterMap.hasNext()) {
					Map.Entry<String, Costunitoperator> entryMap = operIterMap.next();
					Costunitoperator orgObj = entryMap.getValue();
					delOperList.add(orgObj);
				}
			}
			if (StringUtils.isNotEmpty(manageMap)) {
				Iterator<Map.Entry<String, Costunitmanager>> manageIterMap = manageMap.entrySet().iterator();
				while (manageIterMap.hasNext()) {
					Map.Entry<String, Costunitmanager> entryMap = manageIterMap.next();
					Costunitmanager orgObj = entryMap.getValue();
					delManageList.add(orgObj);
				}
			}

//			if (entityService.updateById(costuint) == 0) {
//				result = "类型/表单更新失败！";
//			}

		}
		if ("".equals(result) && StringUtils.isNotEmpty(addOperList)) {
			if (entityService.insertBatch(addOperList) == 0) {
				result = "操作机构添加失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updOperList)) {
			if (entityService.updateByIdBatch(updOperList) == 0) {
				result = "操作机构更新失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(delOperList)) {
			if (entityService.deleteByIdBatch(delOperList) == 0) {
				result = "操作机构更新失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(addManageList)) {
			if (entityService.insertBatch(addManageList) == 0) {
				result = "管理机构添加失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updManageList)) {
			if (entityService.updateByIdBatch(updManageList) == 0) {
				result = "管理机构更新失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(delManageList)) {
			if (entityService.deleteByIdBatch(delManageList) == 0) {
				result = "管理机构更新失败！";
			}
		}
		if ("".equals(result)) { // 保存周期数据
			result = this.saveCostunitcycleData(saveDto);
		}
		return result;
	}
	
	/**
	 * 增加过滤台账查询
	 * @param orgId			机构ID
	 * @param selType		类型
	 * @param ledgerEntry	是否用于台账		true过滤否用于台账；false，不过滤
	 * @return
	 */
	@Override
	public List<Costuint> getCostuintListByOrgId(String orgId, int selType,boolean ledgerEntry) {
		List<Costuint> list =  getCostuintListByOrgId(orgId,selType);
		if(!ledgerEntry) {
			return list;
		}else{
			List<Costuint> newlist = new ArrayList<Costuint>();
			for (Costuint costuint : list) {
				Integer ledgerEntry_ = costuint.getLedgerEntry();
				if(ledgerEntry_!=null && ledgerEntry_==1) {
					newlist.add(costuint);
				}
			}
			return newlist;
		}
	}

	@Override
	public List<Costuint> getCostuintListByOrgId(String orgId, int selType) {
		SysUser user = SysUserHolder.getCurrentUser();
		String userId = null;
		String postId = null;
		if (selType != 1) {
			userId = user.getId();// 人员ID
			postId = user.getPostId();
			postId = orgId + "_" + postId;// 机构ID
		}
		return getCostuintListByOrgId(orgId, userId, postId, selType);
	}
	
	@Override
	public boolean getCostuintListByOrgId(String costUintId,SysUser user) {
		if(user==null) {
			user = SysUserHolder.getCurrentUser();
		}
		String userId = user.getId();// 人员ID
		String postId = user.getPostId();
		String orgId = user.getOrgId();
		postId = user.getOrgId() + "_" + postId;// 机构ID
		CostBindOrgDto dto = new CostBindOrgDto();
		List<String> unitidList =getCostunitoperator(userId, postId, orgId, dto);
		if(unitidList.contains(costUintId)) {
			return true;
		}
		
//		CostBindOrgDto dto = new CostBindOrgDto();
//		dto.setObjid(orgId);
//		dto.setObjType("org");
//		// 操作机构
//		List<Costunitoperator> oOrgList = this.getCostunitoperatorList(dto);// 机构ID
//		if (userId != null) {
//			dto.setObjid(userId);
//			dto.setObjType("user");
//		}
//		List<Costunitoperator> oOrgList1 = this.getCostunitoperatorList(dto);// 人员ID
//		oOrgList.addAll(oOrgList1);// 添加主数据
//		if (postId != null) {
//			dto.setObjid(postId);
//			dto.setObjType("post");
//		}
//		List<Costunitoperator> oOrgList2 = this.getCostunitoperatorList(dto);// 机构岗位ID
//		oOrgList.addAll(oOrgList2);// 添加主数据
//		if (StringUtils.isNotEmpty(oOrgList)) {
//			List<String> unitidList = oOrgList.stream().filter(item -> StringUtils.isNotEmpty(item.getUnitid()))
//					.map(item -> item.getUnitid()).collect(Collectors.toList());
//			if(unitidList.contains(costUintId)) {
//				return true;
//			}
//		}
		return false;
	}
	public List<Costuint> getCostuintListByUser(String orgid,int selType){
		return getCostuintListByUser(orgid, selType, 0);//默认查询核算对象
	}
	
	@Override
	public List<Costuint> getCostuintListByUser(String orgid,int selType,Integer productive){
		List<Costuint> result = new ArrayList<Costuint>();
		List<String> unitidList = new ArrayList<String>();
		CostBindOrgDto dto = new CostBindOrgDto();
		dto.setObjid(orgid);
		if (StringUtils.isNotEmpty(orgid)) {
			// 仅查询操作机构
			if (selType == 1) { // 绑定操作机构
				List<Costunitoperator> bindOrgList = this.getCostunitoperatorList(dto);
				if (StringUtils.isNotEmpty(bindOrgList)) {
					unitidList = bindOrgList.stream().filter(item -> StringUtils.isNotEmpty(item.getUnitid()))
							.map(item -> item.getUnitid()).collect(Collectors.toList());
				}
			}

			// 维护、操作和管理机构包含的核算对象(公式使用)
			if (selType == 2) { // 维护机构,操作机构,管理机构
				List<Costunitoperator> oOrgList = this.getCostunitoperatorList(dto);// 机构ID
				if (StringUtils.isNotEmpty(oOrgList)) {
					unitidList = oOrgList.stream().filter(item -> StringUtils.isNotEmpty(item.getUnitid()))
							.map(item -> item.getUnitid()).collect(Collectors.toList());
				}
				// 核算对象中维护机构
				Where where = Where.create();
				where.eq(Costuint::getOrgId, orgid);
				where.eq(Costuint::getTmused ,1);
				Order order = Order.create();
				order.orderByDesc(Costuint::getTmSort);
				List<Costuint> list = entityService.queryList(Costuint.class, where, order);
				boolean key = arrangeList(list);
				if (key) {
					list = entityService.queryList(Costuint.class, where, order);
				}
				for (Costuint costuint : list) {
					String unitid = costuint.getId();
					if (!unitidList.contains(unitid)) {
						unitidList.add(unitid);
					}
				}
				// 通过管理机构查找核算项目
				List<Costunitmanager> bindOrgList = this.getCostunitmanagerList(dto);
				// 通过核算项目-管理机构查询核算
				for (Costunitmanager costunitmanager : bindOrgList) {
					String unitid = costunitmanager.getUnitid();
					if (!unitidList.contains(unitid)) {
						unitidList.add(unitid);
					}
				}
			}
		}
		if (StringUtils.isNotEmpty(unitidList)) {
			CostDto costDto = new CostDto();
			costDto.setIdList(unitidList);
			if(productive!=null) {
				costDto.setProductive(productive);
			}
			List<Costuint> queryList = this.getData(costDto);
			if (StringUtils.isNotEmpty(queryList)) {
				result = queryList;
			}
		}
		return result;
	}
	
	/**
	 * 获取核算对象列表（根据绑定机构ID），过滤掉活动
	 * 
	 * @param orgId     	绑定机构ID
	 * @param userId    	操作人员ID
	 * @param orgPostId 	操作机构岗位ID（orgId_postId）
	 * @param selType   	查询类型：1、操作机构；2、全部（操作机构,管理机构）
	 * @return
	 */
	public List<Costuint> getCostuintListByOrgId(String orgId, String userId, String orgPostId, int selType) {
		return getCostuintListByOrgId(orgId, userId, orgPostId, selType, 0);//默认查询核算对象
	}

	/**
	 * 获取核算对象列表（根据绑定机构ID）
	 * 
	 * @param orgId     	绑定机构ID
	 * @param userId    	操作人员ID
	 * @param orgPostId 	操作机构岗位ID（orgId_postId）
	 * @param selType   	查询类型：1、操作机构；2、全部（操作机构,管理机构）
	 * @param productive	生产活动：0、核算对象；1、作业活动；2、台账模型
	 * @return
	 */
	@Override
	public List<Costuint> getCostuintListByOrgId(String orgId, String userId, String orgPostId, int selType,Integer productive) {
		Boolean isadmin = SysUserUtil.isAdmin();
		if (isadmin) {
			MethodQueryDto queryDto = new MethodQueryDto();
			return methodService.getCostuintList(queryDto);
		}
		List<Costuint> result = new ArrayList<Costuint>();
		List<String> unitidList = new ArrayList<String>();
		CostBindOrgDto dto = new CostBindOrgDto();
		dto.setObjid(orgId);
		CostBindOrgDto dto1 = new CostBindOrgDto();
		dto1.setObjid(orgId);
		if (StringUtils.isNotEmpty(orgId)) {
			// 仅查询操作机构
			if (selType == 1) { // 绑定操作机构
				List<Costunitoperator> bindOrgList = this.getCostunitoperatorList(dto);
				if (StringUtils.isNotEmpty(bindOrgList)) {
					unitidList = bindOrgList.stream().filter(item -> StringUtils.isNotEmpty(item.getUnitid()))
							.map(item -> item.getUnitid()).collect(Collectors.toList());
				}
			}

			// 维护、操作和管理机构包含的核算对象(公式使用)
			if (selType == 2) { // 维护机构,操作机构,管理机构
				//TODO:操作人>岗位>机构
				unitidList = getCostunitoperator(userId, orgPostId, orgId, dto);
				// 核算对象中维护机构
				Where where = Where.create();
				where.eq(Costuint::getOrgId, orgId);
				Order order = Order.create();
				order.orderByDesc(Costuint::getTmSort);
				List<Costuint> list = entityService.queryList(Costuint.class, where, order);
				boolean key = arrangeList(list);
				if (key) {
					list = entityService.queryList(Costuint.class, where, order);
				}
				for (Costuint costuint : list) {
					String unitid = costuint.getId();
					if (!unitidList.contains(unitid)) {
						unitidList.add(unitid);
					}
				}
				// 添加核算项目的管理机构
				// unitidList.add(item.getOrgId());
				// 通过管理机构查找核算项目
				List<Costunitmanager> bindOrgList = this.getCostunitmanagerList(dto1);
				// 通过核算项目-管理机构查询核算
				unitidList = this.containsUnitid(bindOrgList,unitidList);
			}else {
				//TODO:操作人>岗位>机构
				unitidList = getCostunitoperator(userId, orgPostId, orgId, dto);
				// 核算对象中维护机构
				Where where = Where.create();
				where.eq(Costuint::getOrgId, orgId);
				Order order = Order.create();
				order.orderByDesc(Costuint::getTmSort);
				List<Costuint> list = entityService.queryList(Costuint.class, where, order);
				boolean key = arrangeList(list);
				if (key) {
					list = entityService.queryList(Costuint.class, where, order);
				}
				for (Costuint costuint : list) {
					String unitid = costuint.getId();
					if (!unitidList.contains(unitid)) {
						unitidList.add(unitid);
					}
				}
				List<Costunitmanager> bindOrgList = this.getCostunitmanagerList(dto1);
				for (Costunitmanager costuint : bindOrgList) {
					String unitid = costuint.getUnitid();
					if (!unitidList.contains(unitid)) {
						unitidList.add(unitid);
					}
				}
			}
		}
		if (StringUtils.isNotEmpty(unitidList)) {
			CostDto costDto = new CostDto();
			costDto.setIdList(unitidList);
			if(productive!=null) {
				//查询活动类型
				costDto.setProductive(productive);
			}
			List<Costuint> queryList = this.getData(costDto);
			if (StringUtils.isNotEmpty(queryList)) {
				result = queryList;
			}
		}
		return result;
	}
	
	private List<String> containsUnitid(List<Costunitmanager> bindOrgList, List<String> unitidList) {
		if (bindOrgList!=null && bindOrgList.size()>0) {
			CostBindOrgDto _dto = new CostBindOrgDto();
			List<String> objIds = new ArrayList<String>();
			for (Costunitmanager costunitmanager : bindOrgList) {
				objIds.add(costunitmanager.getObjid());
			}
			_dto.setObjids(objIds);
			List<Costunitmanager> costunitmanagers = this.getCostunitmanagerList(_dto);
			for (Costunitmanager bean : costunitmanagers) {
				String unitid = bean.getUnitid();
				if (!unitidList.contains(unitid)) {
					unitidList.add(unitid);
				}
			}
		}
		return unitidList;
	}
	
	/**
	 * 获得操作机构详细数据
	 * 按照操作人>岗位>机构
	 * @param userId
	 * @param orgPostId
	 * @param orgId
	 * @param dto
	 * @return
	 */
	private List<String> getCostunitoperator(String userId,String orgPostId,String orgId,CostBindOrgDto dto){
		List<String> unitidList = new ArrayList<String>();
		//TODO:操作人>岗位>机构
		List<Costunitoperator> oOrgList = new ArrayList<Costunitoperator>();
		if (userId != null) {
			dto.setObjid(userId);
			dto.setObjType("user");
		}
		List<Costunitoperator> oOrgList1 = this.getCostunitoperatorList(dto);// 人员ID
		if(oOrgList1.size()>0) {
			oOrgList.addAll(oOrgList1);// 添加主数据
		}
		//操作岗位
		if (orgPostId != null) {
			dto.setObjid(orgPostId);
			dto.setObjType("post");
		}
		List<Costunitoperator> oOrgList3 = this.getCostunitoperatorList(dto);// 机构岗位ID
		if(oOrgList3.size()>0) {
			oOrgList.addAll(oOrgList3);// 添加主数据
		}
		// 操作机构
		if (orgId != null) {
			dto.setObjid(orgId);
			dto.setObjType("org");
		}
		List<Costunitoperator> oOrgList2 = this.getCostunitoperatorList(dto);// 机构ID
		oOrgList.addAll(oOrgList2);// 添加主数据
		
		if (StringUtils.isNotEmpty(oOrgList)) {
			unitidList = oOrgList.stream().filter(item -> StringUtils.isNotEmpty(item.getUnitid()))
					.map(item -> item.getUnitid()).collect(Collectors.toList());
		}
		return unitidList;
	}

	/**
	 * 获取核算对象的周期列表
	 * 
	 * @param dto
	 * @return
	 */
	public List<Costunitcycle> getCostunitcycleList(CostBindOrgDto dto) {
		List<Costunitcycle> result = new ArrayList<Costunitcycle>();
		try {
			String unitid = ""; // 核算对象ID
			if (StringUtils.isNotNull(dto)) {
				unitid = dto.getUnitid();
			}

			// 检索条件
			Where where = Where.create();
			if (StringUtils.isNotEmpty(unitid)) {
				where.eq(Costunitcycle::getUnitid, unitid);
			}
			// 排序
			Order order = Order.create();
			order.orderByAsc(Costunitcycle::getCycleCode);

			List<Costunitcycle> list = entityService.queryData(Costunitcycle.class, where, null, null);
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}

	/**
	 * 保存核算对象的周期数据
	 * 
	 * @param dto
	 * @return
	 */
	public String saveCostunitcycleData(CostBindOrgDto dto) {
		String result = "";
		List<Costunitcycle> addList = new ArrayList<Costunitcycle>();
		List<Costunitcycle> updList = new ArrayList<Costunitcycle>();
		if (StringUtils.isNotNull(dto)) {
			String unitid = dto.getUnitid();
			List<Costunitcycle> cycleList = dto.getCycleList();
			if (StringUtils.isNotEmpty(unitid) && StringUtils.isNotEmpty(cycleList)) {
				CostBindOrgDto queryDto = new CostBindOrgDto();
				queryDto.setUnitid(unitid);
				Map<Integer, Costunitcycle> cycleMap = new HashMap<Integer, Costunitcycle>();
				List<Costunitcycle> queryList = getCostunitcycleList(queryDto);
				if (StringUtils.isNotEmpty(queryList)) {
					cycleMap = queryList.stream()
							.collect(Collectors.toMap(Costunitcycle::getCycleCode, Function.identity()));
				}
				for (int i = 0; i < cycleList.size(); i++) {
					Costunitcycle cycleObj = cycleList.get(i);
					int cycleCode = cycleObj.getCycleCode();
					if (StringUtils.isNotEmpty(cycleMap) && cycleMap.containsKey(cycleCode)) { // 修改
						Costunitcycle obj = cycleMap.get(cycleCode);
						String id = obj.getId();
						BeanUtils.copyProperties(cycleObj, obj); // 赋予返回对象
						obj.setId(id);
						updList.add(obj);
					} else { // 新增
						Costunitcycle obj = new Costunitcycle();
						BeanUtils.copyProperties(cycleObj, obj); // 赋予返回对象
						obj.setId(TMUID.getUID());
						obj.setUnitid(unitid);
						addList.add(obj);
					}
				}
			}else {
				if(!StringUtils.isNotEmpty(unitid)) {
					for (int i = 0; i < cycleList.size(); i++) {
						Costunitcycle cycleObj = cycleList.get(i);
						Costunitcycle obj = new Costunitcycle();
						BeanUtils.copyProperties(cycleObj, obj); // 赋予返回对象
						obj.setId(TMUID.getUID());
						obj.setUnitid(dto.getNewUnitid());
						addList.add(obj);
					}
				}
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
			if (entityService.insertBatch(addList) == 0) {
				result = "核算对象周期设置添加失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
			if (entityService.updateByIdBatch(updList) == 0) {
				result = "核算对象周期设置更新失败！";
			}
		}
		return result;
	}

	/**
	 * 核算对象数据另存为
	 * 
	 * @param saveDto
	 * @return
	 */
	@Override
	public String copyCostUnit(CopyDataDto saveDto) {
		String result = "";
		if (StringUtils.isNotNull(saveDto)) {
			String id_copy = saveDto.getId_copy(); // 从此记录复制数据
			String name_new = saveDto.getName_new(); // 新名称
			String begintime_new = saveDto.getBegintime_new(); // 新版本日期
			result = interfaceService.copyCostUnit(id_copy, name_new, begintime_new);
		}
		return result;
	}

	/**
	 * 生产反馈核算对象下拉框数据
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public JSONArray getCostObjComboData() {
		JSONArray jsonArray = new JSONArray();
		String orgId = SysUserUtil.getCurrentUser().getOrgId();
		List<Costuint> costuintListByOrgId = this.getCostuintListByOrgId(orgId, 2);
		for (Costuint costuint : costuintListByOrgId) {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("text", costuint.getName());
			jsonObject.put("value", costuint.getId());
			jsonArray.add(jsonObject);
		}
		return jsonArray;
	}

	@Override
	public String userOrgIsManageOrg(String unitId, String userOrg,String empId,String postId) {
		String rtn = "other";
		if (StringUtils.isEmpty(userOrg)||StringUtils.isEmpty(empId)||StringUtils.isEmpty(postId)) {
			// 机构代码未正确传入，返回other
		} else {
			// 先检索核算对象得到维护机构
			Where where = Where.create();
			where.eq(Costuint::getId, unitId);
			List<Costuint> list = entityService.queryList(Costuint.class, where, null);
			boolean key = arrangeList(list);
			if (key) {
				list = entityService.queryList(Costuint.class, where, null);
			}
			if (list != null && list.size() > 0) {
				String dx = list.get(0).getOrgId();
				if (!StringUtils.isEmpty(dx)) {
					if (userOrg.equals(dx)) {
						// 传入机构是维护机构
						rtn = "manage";
					}
				}
				if (!"manage".equals(rtn)) {
					// 传入机构不是管理机构，这时检验是否是操作机构
					String cz;
//					String objType;
					Where wherex = Where.create();
					wherex.eq(Costunitoperator::getUnitid, unitId);
					List<Costunitoperator> czl = entityService.queryList(Costunitoperator.class, wherex, null);
					if (czl != null && czl.size() > 0) {
						//如果key值有null，默认机构
						for (Costunitoperator y : czl) {
							String objType = y.getObjType();
							if (!StringUtils.isNotNull(objType)) {
								y.setObjType("org");
							}
						}
						Map<String, List<Costunitoperator>> classMap = czl.stream().collect(Collectors.groupingBy(Costunitoperator::getObjType));
						String objId = userOrg;
						//操作人>岗位>机构
						List<Costunitoperator> costunitoperators = new ArrayList<Costunitoperator>();
						if(classMap.containsKey("user")) {
							costunitoperators = classMap.get("user");
							objId = empId;
						}else if(classMap.containsKey("post")) {
							costunitoperators = classMap.get("post");
							objId = postId;
						}else if(classMap.containsKey("org")) {
							costunitoperators = classMap.get("org");
							objId = userOrg;
						}
						if (costunitoperators != null && costunitoperators.size() > 0) {
							for (Costunitoperator y : costunitoperators) {
								cz = y.getObjid();
								if (!StringUtils.isEmpty(cz)) {
									// 操作机构代码不为空
									if (objId.equals(cz)) {
										rtn = "operate";
										break;
									}
								}
							}
						}
						
						// 设置了操作机构
//						for (Costunitoperator y : czl) {
//							cz = y.getObjid();
//							objType = y.getObjType();//user:empId人员；post：岗位；org：机构
//							if (!StringUtils.isEmpty(cz)) {
//								// 操作机构代码不为空
//								if (userOrg.equals(cz)) {
//									rtn = "operate";
//									break;
//								}
//							}
//						}
					}
				}
			} else {
				// 传入的核算对象ID不存在，这时按照other处理
			}
		}
		return rtn;
	}

	@Override
	public boolean synchronousCostUnit(List<SynchronousCostUnitDto> list) {
		List<SynchronousCostUnitDto> dtos = new ArrayList<SynchronousCostUnitDto>();
		for (SynchronousCostUnitDto bean : list) {
			String id = bean.getCostlibraryitemId();
			String name = bean.getCostlibraryitemName();
			Costlibraryitem vo = entityService.queryObjectById(Costlibraryitem.class, id);
			if (vo != null) {
				if (name.equals(vo.getCiname())) {
					dtos.add(bean);
				}
			}
		}
		// TODO：使用宋熙军接口

		return false;
	}

	@Override
	public String importExcel(List<CostuintExcelVo> teList) {
		CostDto dto = new CostDto();
		dto.setTreeKey(false);
		SysUser user = SysUserHolder.getCurrentUser();
		if (ObjUtils.notEmpty(user)) {
			dto.setUserId(user.getId());
			dto.setUserName(user.getRealName());
			dto.setUserOrgCode(user.getOrgId());
			dto.setUserOrgName(user.getOrgName());
			dto.setUser(user);
		}
//		List<CostuintExcelVo> listVo = new ArrayList<CostuintExcelVo>();
		List<Costuint> list = getData(dto);
		Map<String, Costuint> costUintMap = list.stream()
				.collect(Collectors.toMap(Costuint::getName, Function.identity()));
		// 单元类型
		List<Devicetypelibrary> typeList = costDeviceTypeService.getData();
		Map<String, Devicetypelibrary> libMap = typeList.stream()
				.collect(Collectors.toMap(Devicetypelibrary::getDtname, Function.identity()));
		// 机构名称
		List<SysOrg> orgList = sysOrgService.getOrgList();
		Map<String, SysOrg> sysMap = orgList.stream()
				.collect(Collectors.toMap(SysOrg::getOrgname, Function.identity()));
		Map<String, Costuint> saveClassMap = new LinkedHashMap<String, Costuint>();// 名称验证重复使用
		Map<String, Costuint> saveIdMap = new LinkedHashMap<String, Costuint>();// 排序寻找ID使用
		Map<String, Integer> savePIdMap = new LinkedHashMap<String, Integer>();// 排序寻找PID使用
		if (teList != null && teList.size() != 0) {
			for (CostuintExcelVo costuintExcelVo : teList) {
				String names = costuintExcelVo.getName();
				String unittypeName = costuintExcelVo.getUnittypeName();
				String orgName = costuintExcelVo.getOrgName();
				String memo = costuintExcelVo.getMemo();
				String[] names_ = names.split("->");
				// 项目分类
				String mapKey = "";// 分类名称
				for (String flmc : names_) {
					if (flmc.trim().length() == 0) {
						continue;
					}
					// 第一列根分类
					Costuint costuint = new Costuint();
					// TODO:判断层级，添加Pid和ID
					costuint.setName(flmc);
					if (libMap.containsKey(unittypeName)) {// 类型
						Devicetypelibrary unittype = libMap.get(unittypeName);
						costuint.setUnittype(unittype.getId());
					}
					if (sysMap.containsKey(orgName)) {// 类型
						SysOrg sysOrg = sysMap.get(orgName);
						costuint.setOrgId(sysOrg.getId());
					}
					costuint.setMemo(memo);
					costuint.setId(TMUID.getUID());
					costuint.setTmused(1);
					String PmapKey = mapKey;// 父级Key
					// 分类Map保存
					mapKey += "->" + flmc;
					if (!saveClassMap.containsKey(mapKey)) {
						String pid = null;
						if (saveClassMap.containsKey(PmapKey)) {
							Costuint pBean = saveClassMap.get(PmapKey);
							pid = pBean.getId();
						}
						costuint.setPid(pid);
						// 数据过滤，验证重复
						if (!costUintMap.containsKey(costuint.getName())) {
							// 不存在可以添加
							saveClassMap.put(mapKey, costuint);
						}

					}
				}
			}
			// 归纳数据
			LinkedHashMap<Integer, List<Costuint>> hashMap = new LinkedHashMap<Integer, List<Costuint>>();
			int max = 0;
			for (Entry<String, Costuint> entry : saveClassMap.entrySet()) {
				String key = entry.getKey();
				Costuint e = entry.getValue();
				int key_ = key.split("->").length - 1;
				List<Costuint> costuints = new ArrayList<Costuint>();
				if (hashMap.containsKey(key_)) {
					costuints = hashMap.get(key_);
				}
				String pid = e.getPid();
				if (pid == null) {// 新增ID中没有则从数据库查询
					String[] names_ = key.split("->");
					if (costUintMap.containsKey(names_[names_.length - 2])) {// 数据库甄别,查询上级ID作为PID
						Costuint costuint = costUintMap.get(names_[names_.length - 2]);
						pid = costuint.getId();
					}
					e.setPid(pid);
				}
				costuints.add(e);
				if (max < key_) {
					max = key_;
				}
				hashMap.put(key_, costuints);

			}
			// 起始排序
			Integer minSort = 0;
			List<Costuint> list_ = getData("");
			for (Costuint costuint : list_) {
				String sort = costuint.getTmSort();
				Integer integer = 0;
				try {
					integer = Integer.parseInt(sort);
				} catch (Exception e) {
				}
				if (minSort < integer) {
					minSort = integer;
				}
			}
			List<Costuint> save = new ArrayList<Costuint>();
			// 数据过滤，排序
			for (int i = 0; i <= max; i++) {
				if (hashMap.containsKey(i)) {
					List<Costuint> costuints = hashMap.get(i);
					int j = 1;// 当前序列排序
					if (i == 0) {
						// 序号排序
						j = minSort + 1;
					}
					for (Costuint costuint : costuints) {
						if (savePIdMap.containsKey(costuint.getPid())) {// 查询本级sort
							j = savePIdMap.get(costuint.getPid()) + 1;
						} else {// 父节点没有，序号重置
							j = 1;
						}
						savePIdMap.put(costuint.getPid(), j);
						String sort = String.valueOf(savePIdMap.get(costuint.getPid()));
						if (sort.length() == 1) {
							sort = "000" + sort;
						} else if (sort.length() == 2) {
							sort = "00" + sort;
						} else if (sort.length() == 3) {
							sort = "0" + sort;
						}
						String pid = costuint.getPid();
						if (pid != null) {
							if (saveIdMap.containsKey(pid)) {// 查询上级sort
								String pTmSort = saveIdMap.get(pid).getTmSort();
								sort = pTmSort + sort;
							} else {// 数据库中查询上级sort
								String sql = " SELECT MAX(TMSORT) AS SORT FROM  COSTUINT where pid = '" + pid + "' ";
								SqlRowSet result = entityService.rawQuery(sql.toString());
								if (result != null) {
									while (result.next()) {// 数据库查询
										String sort1 = result.getString("SORT");
										int cos = sort1.length() / 3;
										String qsort = sort1.substring(0, ((cos - 1) * 3));
										String hsort = sort1.substring((cos - 1) * 3, ((cos) * 3));
										Costuint costuint2 = new Costuint();
										costuint2.setTmSort(qsort);
										saveIdMap.put(pid, costuint2);
										savePIdMap.put(costuint.getPid(), Integer.parseInt(hsort));
										// 需要加1
										sort = String.valueOf(Integer.parseInt(hsort) + 1);
										if (sort.length() == 1) {
											sort = "000" + sort;
										} else if (sort.length() == 2) {
											sort = "00" + sort;
										} else if (sort.length() == 3) {
											sort = "0" + sort;
										}
										sort = qsort + sort;
									}
								}
							}

						}
						String tmSort = sort;
						costuint.setTmSort(tmSort);
						// 层级添加使用
						saveIdMap.put(costuint.getId(), costuint);
					}
					save.addAll(costuints);
				}
			}
			// 保存数据
			if (save.size() > 0) {
//				List<CostuintVo> costuints = classTreeVo(save);//转树形

				List<Costuint> costuints = new ArrayList<Costuint>();
				for (Costuint vo : save) {
					Costuint obj = ObjUtils.copyTo(vo, Costuint.class);
					String pid = obj.getPid();
					if (pid == null) {
						obj.setPid("root");
					}
					costuints.add(obj);
				}
				Map<String, List<Costuint>> saveMap = costuints.stream()
						.collect(Collectors.groupingBy(Costuint::getPid, Collectors.toList()));
				// 数据需要过滤，记录非ID
				List<String> ids = new ArrayList<String>();// 记录叶子ID
				List<String> pids = new ArrayList<String>();// 记录非叶子PID
				for (Costuint costuint2 : save) {// 过滤第一次
					String id = costuint2.getId();
					String pid = costuint2.getPid();
					if (!saveMap.containsKey(id)) {
						// 叶子
						costuint2.setType(1);
						ids.add(id);
					} else {
						if (!pids.contains(pid)) {
							pids.add(pid);
						}
					}
				}
				for (Costuint costuint2 : save) {// 过滤第二次
					String pid = costuint2.getPid();
					if (pids.contains(pid)) {
						costuint2.setType(null);
					}
				}
				entityService.insertBatch(save);
			}
		}
		return null;
	}

//	/**
//	 * 树形整理
//	 * 
//	 * @param treeVos
//	 * @param content
//	 */
//	private List<CostuintVo> classTreeVo(List<Costuint> saves) {
//		List<CostuintVo> costuints = new ArrayList<CostuintVo>();
//		for (Costuint vo : saves) {
//			CostuintVo obj = ObjUtils.copyTo(vo, CostuintVo.class);
//			String pid = obj.getPid();
//			if (pid == null) {
//				obj.setPid("root");
//			}
//			costuints.add(obj);
//		}
//		List<CostuintVo> _costuintVos = new ArrayList<CostuintVo>();
//		for (CostuintVo treeVo : costuints) {
//			// 机构id
//			if (treeVo.getPid() == null) {
//				treeVo.setPid("");
//				// 根节点
//				_costuintVos.add(treeVo);
//			} else if ("".equals(treeVo.getPid())) {
//				_costuintVos.add(treeVo);
//			}
//		}
//		Map<String, List<CostuintVo>> map = costuints.stream().collect(Collectors.groupingBy(CostuintVo::getPid));
//		// 合并到分类中
//		for (CostuintVo costlibraryclassVo : costuints) {
//			String id = costlibraryclassVo.getId();
//			List<CostuintVo> children = map.get(id);
//			if (children != null) {
//				getCostlibraryclassTreeVoData(children, map);
//			}
//		}
//		return _costuintVos;
//	}
//
//	private List<CostuintVo> getCostlibraryclassTreeVoData(List<CostuintVo> _list, Map<String, List<CostuintVo>> map) {
//		for (CostuintVo costlibraryclassVo : _list) {
//			String id = costlibraryclassVo.getId();
//			if (map.containsKey(id)) {
//				List<CostuintVo> children = map.get(id);
//				if (children != null) {
//					List<CostuintVo> treeVos = costlibraryclassVo.getChildren();
//					if (treeVos != null && treeVos.size() > 0) {
//						children.addAll(treeVos);
//					}
//					costlibraryclassVo.setChildren(children);
//					getCostlibraryclassTreeVoData(children, map);
//				}
//			}
//		}
//		return _list;
//	}

	@Override
	public List<CostuintExcelVo> toExcel() {
		List<Devicetypelibrary> typeList = costDeviceTypeService.getData();
		Map<String, Devicetypelibrary> deviceTypeMap = typeList.stream()
				.collect(Collectors.toMap(Devicetypelibrary::getId, Function.identity()));
		CostDto dto = new CostDto();
		dto.setTreeKey(false);
		SysUser user = SysUserHolder.getCurrentUser();
		if (ObjUtils.notEmpty(user)) {
			dto.setUserId(user.getId());
			dto.setUserName(user.getRealName());
			dto.setUserOrgCode(user.getOrgId());
			dto.setUserOrgName(user.getOrgName());
			dto.setUser(user);
		}
		List<CostuintExcelVo> listVo = new ArrayList<CostuintExcelVo>();
		List<Costuint> list = getData(dto);
		Map<String, Costuint> costUintMap = list.stream()
				.collect(Collectors.toMap(Costuint::getId, Function.identity()));
		for (Costuint costuint : list) {
			String name = costuint.getName();
			String unittype = costuint.getUnittype();
			String memo = costuint.getMemo();
			String orgId = costuint.getOrgId();
			String pId = costuint.getPid();

			CostuintExcelVo costuintExcelVo = new CostuintExcelVo();
			String ciName = "->" + name;
			while (costUintMap.containsKey(pId)) {
				Costuint bean = costUintMap.get(pId);
				pId = bean.getPid();
				ciName = "->" + bean.getName() + ciName;
			}
			ciName = ciName.substring(2);
			costuintExcelVo.setMemo(memo);
			costuintExcelVo.setName(ciName);
			SysOrg org = sysOrgService.getOrgByOrgcode(orgId);
			if (org != null) {
				costuintExcelVo.setOrgName(org.getOrgname());
			}
			if (deviceTypeMap.containsKey(unittype)) {
				Devicetypelibrary typeObj = deviceTypeMap.get(unittype);
				costuintExcelVo.setUnittypeName(typeObj.getDtname());
			}
			listVo.add(costuintExcelVo);
		}
		return listVo;
	}

	/**
	 * 获取新序号
	 * 
	 * @param tmSort
	 * @param addNum
	 * @return
	 */
	@Override
	public String getNewSort(String tmSort, int addNum) {
		String sort = "0001"; // 默认
		if (tmSort != null && tmSort.length() >= 4) {
			// 前序号
			String p_sort = tmSort.substring(0, tmSort.length() - 4);
			// 后序号
			String s_sort = tmSort.substring(tmSort.length() - 4, tmSort.length());
			sort = String.valueOf(Integer.parseInt(s_sort) + addNum);
			if (sort.length() == 1) {
				sort = "000" + sort;
			} else if (sort.length() == 2) {
				sort = "00" + sort;
			} else if (sort.length() == 3) {
				sort = "0" + sort;
			}
			sort = p_sort + sort;
		}
		return sort;
	}

	@Override
	public List<SysOrg> getCostuintSysOrgList(String orgId,int selType) {
		List<Costuint> costuints = getCostuintListByOrgId(orgId,selType);
//		List<Costuint> costuints1 = getCostuintListOrgId(costuints);
		List<String> orgIds = new ArrayList<String>();	
		for (Costuint costuint : costuints) {
			String orgid = costuint.getOrgId();
			if(!orgIds.contains(orgid)) {
				orgIds.add(orgid);
			}
		}
		List<SysOrg> list = new ArrayList<SysOrg>();
		if(orgIds.size()>0) {
			Where where = Where.create();
			where.in(SysOrg::getOrgcode, orgIds.toArray());
			where.eq(SysOrg::getUsed, 1);
			Order order = Order.create();
			order.orderByAsc(SysOrg::getTmSort);
			list = entityService.queryList(SysOrg.class, where, order);
		}
		return list;
	}
	
	/**
	 * 
	 * @param listKeyStream
	 * @return
	 */
//	private List<Costuint> getCostuintListOrgId(List<Costuint> listKeyStream){
//		//按照树形进行排序
//		List<Costuint> costuintVos = new ArrayList<Costuint>();
//		
//		List<String> insertId = new ArrayList<String>();
//		int listSize =  listKeyStream.size();
//		for (Costuint costuint : listKeyStream) {
//			String pid = costuint.getPid();
//			String id = costuint.getId();
//			if(pid==null||pid.trim().length()==0) {
//				costuintVos.add(costuint);
//				insertId.add(id);
//			}
//		}
//		listKeyStream.removeAll(costuintVos);
//		if(costuintVos.size()>0) {
//			//处理器：当累加器基数不发生变化时，跳出该循环
//			int accumulator = -1;
//			while (costuintVos.size()!=listSize) {
//				if(accumulator!=costuintVos.size()) {
//					accumulator = costuintVos.size();
//				}else {
//					break;
//				}
//				for (int i = 0; i < costuintVos.size(); i++) {
//					Costuint costuint = costuintVos.get(i);
//					String id = costuint.getId();
//					boolean insert = false;
//					for (int j = listKeyStream.size()-1; j >= 0 ; j--) {
//						Costuint costuint1 = listKeyStream.get(j);
//						String pid = costuint1.getPid();
//						String id_ = costuint1.getPid();
//						if(pid!=null&&pid.trim().length()>0) {
//							if(insertId.contains(pid)) {
//								if(id.equals(pid)) {
//									costuintVos.add(i+1, costuint1);
//									insertId.add(id_);
//									insert = true;
//									listKeyStream.remove(costuint1);
//									break;
//								}
//							}else {
//								//历史垃圾数据，需要清理
//								listKeyStream.remove(costuint1);
//								listSize--;
//								break;
//							}
//						}
//					}
//					if(insert) {
//						break;
//					}
//				}
//			}
//		}
//		return costuintVos;
//	}

	@Override
	public List<Costuint> getCostuintListOrgId(String orgIds) {
		return getCostuintListOrgId(null,orgIds);
	}
	
	@Override
	public List<Costuint> getCostuintListOrgId(CostuintQueryDto dto,String orgIds) {
		List<String> id_s = new ArrayList<String>();
		List<String> org_s = new ArrayList<String>();
		if(dto!=null) {
			String id = dto.getId();
			List<String> ids = dto.getIds();
			if(id!=null&&id.trim().length()>0) {
				id_s.add(id);
				Costuint costUint = getCostuintObjById(id);
				org_s.add(costUint.getOrgId());
			}
			if(ids!=null&&ids.size()>0) {
				for (String id_ : ids) {
					id_s.add(id_);
					Costuint costUint = getCostuintObjById(id_);
					org_s.add(costUint.getOrgId());
				}
			}
		}
		
		String orgId_ = SysUserUtil.getCurrentUser().getOrgId();
		List<Costuint> costuints_ = getCostuintListByOrgId(orgId_,2);//从数据中过滤
		List<Costuint> costuints = new ArrayList<Costuint>();
		List<String> newOrgIds = new ArrayList<String>();
		if(orgIds!=null) {
			String [] orgId_s = orgIds.split(",");
			for (String orgId : orgId_s) {
				if(!newOrgIds.contains(orgId)) {
					newOrgIds.add(orgId);
				}
				for (Costuint costuint : costuints_) {
					String orgid = costuint.getOrgId();
					if (StringUtils.isNotEmpty(orgid)) {
						if(orgid.equals(orgId)) {
							costuints.add(costuint);
						}
					}
				}
			}
		}
		Map<String, Costuint> map = new LinkedHashMap<String, Costuint>();
		for (Costuint costuint : costuints) {
			String orgid = costuint.getOrgId();
			String id = costuint.getId();
			String key = costuint.getId();
			if(id_s.contains(id)) {
				costuint.setDefaultKey(true);
			}
			if(newOrgIds.contains(orgid)) {//相同机构下添加到显示list中
				map.put(key, costuint);
			}
		}
		List<Costuint> listKeyStream = map.values().stream().collect(Collectors.toList());
		return listKeyStream;
//		return getCostuintListOrgId(listKeyStream);
	}

	/**
	 * 根据传入的核算对象ID，得到对象的维护机构，操作机构和管理机构
	 * @param costuintId	核算对象Id
	 * @return
	 */
	@Override
	public Map<String, List<String>> getCostuintById(String costuintId) {
		Map<String, List<String>> map = new LinkedHashMap<String, List<String>>();
		if(StringUtils.isNotEmpty(costuintId)) {
			List<String> sysOrgs = new ArrayList<String>();//机构
			List<String> employeeInfos = new ArrayList<String>();//人员
			List<String> employeeInfoOrgPosts = new ArrayList<String>();//岗位
			Costuint costuint = entityService.queryObjectById(Costuint.class, costuintId);
			if(costuint!=null) {
				String orgId = costuint.getOrgId();//维护机构
				if(StringUtils.isNotEmpty(orgId)) {
					sysOrgs.add(orgId);//添加维护机构
				}
				CostBindOrgDto dto = new CostBindOrgDto();
				dto.setUnitid(costuintId);
				//维护机构
				List<Costunitmanager> bindOrgList = this.getCostunitmanagerList(dto);
				for (Costunitmanager costunitmanager : bindOrgList) {
					sysOrgs.add(costunitmanager.getObjid());
				}
				//操作机构，人员，岗位
				List<Costunitoperator> oOrgList = this.getCostunitoperatorList(dto);
				for (Costunitoperator  costunitoperator : oOrgList) {
					String objId = costunitoperator.getObjid();
					String objType = costunitoperator.getObjType();
					if(!StringUtils.isNotEmpty(objType)) {
						sysOrgs.add(objId);
						continue;
					}
					if(objType.equals("org")) {
						sysOrgs.add(objId);
					}
					if(objType.equals("user")) {
						employeeInfos.add(objId);
					}
					if(objType.equals("post")) {
						employeeInfoOrgPosts.add(objId);
					}
				}
				if(StringUtils.isNotEmpty(sysOrgs)) {
					map.put("org", sysOrgs);
				}
				if(StringUtils.isNotEmpty(employeeInfos)) {
					map.put("user", employeeInfos);
				}
				if(StringUtils.isNotEmpty(employeeInfoOrgPosts)) {
					map.put("post", employeeInfoOrgPosts);
				}
			}
		}
		return map;
	}
}
