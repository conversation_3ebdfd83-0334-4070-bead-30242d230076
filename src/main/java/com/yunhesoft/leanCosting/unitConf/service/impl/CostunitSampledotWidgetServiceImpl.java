package com.yunhesoft.leanCosting.unitConf.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.rtdbShow.service.IRtdbShowService;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostunitSampledotWidgetConfig;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostunitSampledotWidgetData;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostunitSampledotWidgetTemplate;
import com.yunhesoft.leanCosting.unitConf.service.ICostunitSampledotWidgetService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class CostunitSampledotWidgetServiceImpl implements ICostunitSampledotWidgetService {

    @Autowired
    private EntityService dao;

    @Autowired
    private IUnitMethodService unitSrv;

//    @Autowired
//    private HttpClientService httpSrv;
//    @Autowired
//    private ISysConfigService sysConfigSrv;

    @Autowired
    private IRtdbShowService rtdbSrv;


    /**
     * 查询模板
     * @param name
     * @return
     */
    @Override
    public List<CostunitSampledotWidgetTemplate> queryTemplateList(String name) {
        Where where = Where.create();
        if (StringUtils.isNotEmpty(name)) {
            where.like(CostunitSampledotWidgetTemplate::getName, name);
        }
        return dao.queryData(CostunitSampledotWidgetTemplate.class, where, Order.create().orderByAsc(CostunitSampledotWidgetTemplate::getTmsort), null);
    }

    /**
     * 保存模板
     * @param list
     * @return
     */
    @Override
    public List<CostunitSampledotWidgetTemplate> saveTemplateList(List<CostunitSampledotWidgetTemplate> list) {
        if (StringUtils.isEmpty(list)) {
            return list;
        }
        SysUser currentUser = SysUserHolder.getCurrentUser();
        List<CostunitSampledotWidgetTemplate> result = new ArrayList<>();
        List<CostunitSampledotWidgetTemplate> updateList = new ArrayList<>();
        List<CostunitSampledotWidgetTemplate> insertList = new ArrayList<>();
        List<CostunitSampledotWidgetTemplate> deleteList = new ArrayList<>();

        for (CostunitSampledotWidgetTemplate item : list) {
            if (item.getRowflag() == -1) {
                //删除
                deleteList.add(item);
            } else if (StringUtils.isEmpty(item.getId())) {
                //新增
                item.setId(TMUID.getUID());
                item.setCreateByName(currentUser.getRealName());
                insertList.add(item);
            } else {
                //修改
                item.setUpdateByName(currentUser.getRealName());
                updateList.add(item);
            }

            if (item.getRowflag() != -1) {
                result.add(item);
            }
        }

        //新增和修改的记录需检测新名称是否重复
        List<CostunitSampledotWidgetTemplate> toCheckRepeatList = Stream.concat(insertList.stream(), updateList.stream()).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(toCheckRepeatList)) {
            List<String> nameList = toCheckRepeatList.stream().map(CostunitSampledotWidgetTemplate::getName).collect(Collectors.toList());
            List<CostunitSampledotWidgetTemplate> oldList = dao.queryData(CostunitSampledotWidgetTemplate.class, Where.create().in(CostunitSampledotWidgetTemplate::getName, nameList.toArray()), null, null);
            if (StringUtils.isNotEmpty(oldList)) {
                LinkedHashSet<String> repeatNameList = getRepeatNameList(toCheckRepeatList, oldList);
                if (StringUtils.isNotEmpty(repeatNameList)) {
                    throw new RuntimeException("模板名称"+StringUtils.join(repeatNameList, "、")+"重复");
                }
            }
        }



        if (StringUtils.isNotEmpty(insertList)) {
            dao.insertBatch(insertList);
        }
        if (StringUtils.isNotEmpty(updateList)) {
            dao.updateBatch(updateList);
        }
        if (StringUtils.isNotEmpty(deleteList)) {
            dao.rawDeleteByIdBatch(deleteList);
        }

        return result;
    }

    private static LinkedHashSet<String> getRepeatNameList(List<CostunitSampledotWidgetTemplate> toCheckRepeatList, List<CostunitSampledotWidgetTemplate> oldList) {
        LinkedHashSet<String> repeatNameList = new LinkedHashSet<>();
        for (CostunitSampledotWidgetTemplate tpl : toCheckRepeatList) {
            for (CostunitSampledotWidgetTemplate old : oldList) {
                if (old.getName().equals(tpl.getName()) && !old.getId().equals(tpl.getId())) {
                    //同名不同id
                    repeatNameList.add(old.getName());
                    break;
                }
            }
        }
        return repeatNameList;
    }

    /**
     * 批量删除模板
     * @param idList
     * @return
     */
    @Override
    public String deleteTemplateByIdList(List<String> idList) {
        int res = dao.deleteIn(CostunitSampledotWidgetTemplate.class, CostunitSampledotWidgetTemplate::getId, idList.toArray());
        if (res <0) {
            throw new RuntimeException("删除失败");
        }
        return null;
    }

    /**
     * 根据模板id查询设置
     * @param templateId
     * @return
     */
    @Override
    public List<CostunitSampledotWidgetConfig> queryConfigListByTemplateId(String templateId) {
        return dao.queryData(CostunitSampledotWidgetConfig.class, Where.create().eq(CostunitSampledotWidgetConfig::getTemplateId, templateId), Order.create().orderByAsc(CostunitSampledotWidgetConfig::getTmsort), null);
    }

    /**
     * 保存设置
     * @param list
     * @return
     */
    @Override
    public List<CostunitSampledotWidgetConfig> saveConfigList(List<CostunitSampledotWidgetConfig> list) {
        if (StringUtils.isEmpty(list)) {
            return list;
        }
        List<CostunitSampledotWidgetConfig> result = new ArrayList<>();
        List<CostunitSampledotWidgetConfig> updateList = new ArrayList<>();
        List<CostunitSampledotWidgetConfig> insertList = new ArrayList<>();
        List<CostunitSampledotWidgetConfig> deleteList = new ArrayList<>();
        for (CostunitSampledotWidgetConfig item : list) {
            if (item.getRowflag() == -1) {
                //删除
                deleteList.add(item);
            } else if (StringUtils.isEmpty(item.getId())) {
                //新增
                item.setId(TMUID.getUID());
                insertList.add(item);
            } else {
                //修改
                updateList.add(item);
            }

            if (item.getRowflag() != -1) {
                result.add(item);
            }
        }
        if (StringUtils.isNotEmpty(insertList)) {
            dao.insertBatch(insertList);
        }
        if (StringUtils.isNotEmpty(updateList)) {
            dao.updateBatch(updateList);
        }
        if (StringUtils.isNotEmpty(deleteList)) {
            dao.rawDeleteByIdBatch(deleteList);
        }

        return result;
    }

    /**
     * 删除设置
     * @param idList
     * @return
     */
    @Override
    public String deleteConfigByIdList(List<String> idList) {
        int res = dao.deleteIn(CostunitSampledotWidgetConfig.class, CostunitSampledotWidgetConfig::getId, idList.toArray());
        if (res <0) {
            throw new RuntimeException("删除失败");
        }
        return null;
    }

    @Override
    public CostunitSampledotWidgetData getSampledotWidgetData(String templateId, String dataId, String compId, String unitId, Date startTime, Date endTime) {
        if (StringUtils.isAnyEmpty(templateId, dataId)) {
            return null;
        }
        Where where = Where.create().eq(CostunitSampledotWidgetData::getTemplateId, templateId).eq(CostunitSampledotWidgetData::getDataId, dataId);
        if (StringUtils.isNotEmpty(compId)) {
            where.eq(CostunitSampledotWidgetData::getCompId, compId);
        }
        if (StringUtils.isNotEmpty(unitId)) {
            where.eq(CostunitSampledotWidgetData::getUnitId, unitId);
        }
        if (startTime != null) {
            where.eq(CostunitSampledotWidgetData::getStartTime, startTime);
        }
        if (endTime != null) {
            where.eq(CostunitSampledotWidgetData::getEndTime, endTime);
        }
        List<CostunitSampledotWidgetData> list = dao.queryData(CostunitSampledotWidgetData.class, where, null, null);
        return StringUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public CostunitSampledotWidgetData saveSampledotWidgetData(CostunitSampledotWidgetData data) {
        if (StringUtils.isAnyEmpty(data.getTemplateId(), data.getDataId())) {
            return null;
        }
        CostunitSampledotWidgetData bean = this.getSampledotWidgetData(data.getTemplateId(), data.getDataId(), data.getCompId(), data.getUnitId(), null, null);
        boolean insertflag = false;
        if (bean == null) {
            //新增
            insertflag = true;
            bean = new CostunitSampledotWidgetData();
            bean.setId(TMUID.getUID());
            bean.setDataId(data.getDataId());
            bean.setTemplateId(data.getTemplateId());
            bean.setCompId(data.getCompId());
        }
        bean.setStartTime(data.getStartTime());
        bean.setEndTime(data.getEndTime());
        bean.setUnitId(data.getUnitId());
        bean.setDataTemp(data.getDataTemp());
        if (insertflag) {
            dao.insert(bean);
        } else {
            dao.update(bean);
        }
        return bean;
    }

    @Override
    public JSONObject querySampledotWidgetColumnAndData(String templateId, String dataId, String compId, String unitId, Date startTime, Date endTime) {
        CostunitSampledotWidgetTemplate template = dao.queryObjectById(CostunitSampledotWidgetTemplate.class, templateId);
        if (template == null) {
            throw new RuntimeException("找不到对应模板");
        }
        JSONObject result = new JSONObject();

        //如果传入数据id，则按以保存的参数为准
        if (StringUtils.isNotEmpty(dataId) && StringUtils.isNotEmpty(compId)) {
            CostunitSampledotWidgetData sampledotWidgetData = this.getSampledotWidgetData(templateId, dataId, compId, null, null, null);
            if (sampledotWidgetData != null) {
                result.put("saveData", sampledotWidgetData);
                unitId = StringUtils.isNotEmpty(unitId) ? unitId : sampledotWidgetData.getUnitId();
                startTime = startTime != null ? startTime : sampledotWidgetData.getStartTime();
                endTime = endTime != null ? endTime : sampledotWidgetData.getEndTime();
            }
        }

        List<CostunitSampledotWidgetConfig> configList = this.queryConfigListByTemplateId(templateId);
        if (StringUtils.isEmpty(configList)) {
            return result;
        }


//        JSONArray columnArray = new JSONArray();
//        for (CostunitSampledotWidgetConfig config : configList) {
//            JSONObject obj = new JSONObject();
//            obj.put("name", config.getName());
//            obj.put("alias", config.getName());
//            columnArray.add(obj);
//        }
        // TODO 过滤采集点
        //unitId核算对象id
        //configList输出列对象
        List<CostunitSampledotWidgetConfig> list = StringUtils.isEmpty(unitId) ? configList : unitSrv.getUnitSampleDot(unitId, configList);

        Map<String, List<String>> tagnumberMap = new HashMap<>();
//        List<CostunitSampledotWidgetConfig> configResult = new ArrayList<>();
        JSONArray columnArray = new JSONArray();
        JSONObject timeCol = new JSONObject();
        timeCol.put("alias", "__time");
        timeCol.put("header", "时间");
        timeCol.put("align", "center");
        timeCol.put("width", 150);
        columnArray.add(timeCol);
        List<String> tagCodes = new ArrayList<>();
        for (CostunitSampledotWidgetConfig config : list) {
            JSONObject colObj = new JSONObject();
            colObj.put("alias", config.getId());
            colObj.put("header", config.getName());
            colObj.put("align", "right");
            columnArray.add(colObj);

            //仪表位号
            String tagnumber = config.getTagnumber();
            if (StringUtils.isEmpty(tagnumber)) {
                continue;
            } else if (tagnumber.contains(",")) {
                //包含多个表号时默认取第一个
                config.setTagnumber(tagnumber.substring(0, tagnumber.indexOf(",")));
            }
            tagCodes.add(config.getTagnumber());

            List<String> configIds = tagnumberMap.computeIfAbsent(config.getTagnumber().toUpperCase(), k -> new ArrayList<>());
            configIds.add(config.getId());
        }

        if (StringUtils.isNotEmpty(tagCodes) && startTime != null && endTime != null) {
            String stime = DateTimeUtils.formatDate(startTime);
            String etime = DateTimeUtils.formatDate(endTime);
            int interval = template.getDataInterval();
//            JSONObject resObj = rtdbSrv.queryRtdbData(tagCodes, stime, etime, interval*60);
            List<Tag> dataResult = rtdbSrv.queryRtdbTagData(tagCodes, stime, etime, interval*60);
//            result.put("data", resObj);
//            if (resObj != null && resObj.containsKey("result")) {
//            if (StringUtils.isNotEmpty(resObj)) {
//                JSONArray dataResult = resObj.getJSONArray("result");
                if (StringUtils.isNotEmpty(dataResult)) {
                    JSONObject rowMap = new JSONObject();
                    for (int i=0; i<dataResult.size(); i++) {
//                        JSONObject jsonObject = dataResult.getJSONObject(i);
//                        String tagCode = jsonObject.getString("tagCode");
//                        JSONArray datas = jsonObject.getJSONArray("datas");
                        Tag jsonObject = dataResult.get(i);
                        String tagCode = jsonObject.getTagCode();
                        List<TagData> datas = jsonObject.getDatas();

                        for (int j=0; j<datas.size(); j++) {
//                            JSONObject each = datas.getJSONObject(j);
//                            String datetime = each.getString("datetime");
                            TagData each = datas.get(j);
                            String datetime = each.getDatetime();
                            if (datetime != null && datetime.length() > 19) {
                                datetime = datetime.substring(0,19);
                            }
//                            String value = each.getString("value");
                            String value = each.getValue() == null ? null : each.getValue().toString();
                            JSONObject o = (JSONObject) rowMap.computeIfAbsent(datetime, v -> new JSONObject());
                            List<String> configIds = tagnumberMap.get(tagCode.toUpperCase());
                            if (StringUtils.isEmpty(configIds)) {
                                continue;
                            }
                            if (Coms.judgeDouble(value)) {
                                value = new BigDecimal(value).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
                            }
                            for (String configId : configIds) {
                                o.put(configId, value);
                            }
                        }
                    }
                    Set<String> times = rowMap.keySet();
                    List<String> timeList = times.stream().sorted().collect(Collectors.toList());

                    JSONArray data = new JSONArray();
                    for (String t : timeList) {
                        JSONObject jsonObject = rowMap.getJSONObject(t);
                        jsonObject.put("__time", t);
                        data.add(jsonObject);
                    }

                    result.put("data", data);
                }
//                if (result1 != null && result1.containsKey("result")) {
//                    JSONArray result2 = result1.getJSONArray("result");
//                    if (StringUtils.isNotEmpty(result2)) {
//                        //有数据
//
//
//                    }
//                }
//            }
//            result.put("data", data);
        }


        result.put("columnArray", columnArray);
        result.put("configList", list);
//        result.put("configTagnumberResult", configResult);
//        result.put("tagCodes", tagCodes);
        result.put("dataInterval", template.getDataInterval()*60);
        return result;
    }

    @Override
    public JSONArray querySampledotWidgetDataTemp(String dataId, String unitId, Date startTime, Date endTime) {
        Where where = Where.create();
        if (StringUtils.isNotEmpty(dataId)) {
            where.eq(CostunitSampledotWidgetData::getDataId, dataId);
        }
        if (StringUtils.isEmpty(unitId)) {
            return null;
        }
        if (startTime == null && endTime == null) {
            return null;
        }
        startTime = startTime == null ? endTime : startTime;
        endTime = endTime == null ? startTime : endTime;
        where.eq(CostunitSampledotWidgetData::getUnitId, unitId);
        where.eq(CostunitSampledotWidgetData::getStartTime, startTime);
        where.eq(CostunitSampledotWidgetData::getEndTime, endTime);

        List<CostunitSampledotWidgetData> list = dao.queryData(CostunitSampledotWidgetData.class, where, null, null);
        CostunitSampledotWidgetData data = StringUtils.isEmpty(list) ? null : list.get(0);
        if (data == null) {
            return null;
        }
        String dataTemp = data.getDataTemp();
        if (StringUtils.isEmpty(dataTemp)) {
            return null;
        }
        JSONArray array = (JSONArray) JSONObject.parse(dataTemp, Feature.OrderedField);
//        if (String) {
//
//        }
        return array;
    }

}
