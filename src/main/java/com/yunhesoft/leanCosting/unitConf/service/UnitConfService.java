package com.yunhesoft.leanCosting.unitConf.service;

import java.util.List;

import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.paramDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampledotVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountMeterVo;

public interface UnitConfService {
	 /**
	 * 查询数据
	 * 
	 * @param dto
	 * @return
	 */
	public CostitemVo loadTree(paramDto dto);
	
	/**
	 * @category 保存节点
	 * @param dto
	 * @return
	 */
	public CostitemVo saveNode(paramDto dto);
	
	/**
	 * @category 保存引用仪表采集点数据
	 * @param dto
	 * @return
	 */
	public List<CostitemVo> saveDotNodes(paramDto dto);

	
	/**
	 * 查询采集点数数据
	 * 
	 * @param dto
	 * @return
	 */
	public CostitemVo loadPotTree(paramDto dto);
	
	/**
	 * @category 保存采集点节点
	 * @param dto
	 * @return
	 */
	public SampledotVo savePotNode(paramDto dto);
	
	
	/**
	 * @category 外部获取项目、指标列表信息（只包含名称nameStr、类型nodeTypeMark、父名称parentName
	 * @param dto
	 * @return
	 */
	public List<CostitemVo> getDataList(paramDto dto);

	
	/**
	 *	保存项目数据
	 * @param saveDto
	 * @return
	 */
	public String saveCostitemData(MethodSaveDto saveDto);
	
	
	/**
	 * @category 获取核算单元或机构的全部仪表
	 * @param dto
	 * @return
	 */
	public List<SampledotVo> getUnitDotList(paramDto dto);
	
	/**
	 * @category 获取数据源台账设置的仪表
	 * @param dto
	 * @return
	 */
	public List<TdsAccountMeterVo> getTdsAccountMeter(paramDto dto);
	
	/**
	 * @category 获取核算对象列表数据
	 * @param dto
	 * @return
	 */
	public List<Costuint> getCostuintList(paramDto dto);
	/**
	 * @category 获取核算对象列表数据
	 * @param dto
	 * @return
	 */
	public List<Costuint> getOrgCostuintList(paramDto dto);
	
	
	/**
	 *	保存采集点拖拽排序数据
	 * @param saveDto
	 * @return
	 */
	public String saveSampledotSortData(MethodSaveDto saveDto);
	
	/**
	 *	保存成本项目拖拽排序数据
	 * @param saveDto
	 * @return
	 */
	public String saveCostItemSortData(MethodSaveDto saveDto);
	
}
