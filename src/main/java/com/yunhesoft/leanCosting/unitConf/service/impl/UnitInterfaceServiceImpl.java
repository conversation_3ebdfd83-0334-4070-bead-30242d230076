package com.yunhesoft.leanCosting.unitConf.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostItemTreeQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.paramDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostStipulateTime;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitcycle;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitmanager;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitversion;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BeanVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostItemTreeVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampledotVo;
import com.yunhesoft.leanCosting.unitConf.service.IUnitInterfaceService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Pagination;

@Service
public class UnitInterfaceServiceImpl implements IUnitInterfaceService {

	@Autowired
	private EntityService entityService;
	
	@Autowired
	private IUnitMethodService methodService;
	
	@Autowired
	private UnitItemInfoService unitItemInfoService;
	
	@Autowired
	private RedisUtil redisUtil;
	
	
	/**
	 *	复制核算单元
	 * @param id_copy
	 * @param name_new
	 * @param begintime_new
	 * @return
	 */
	@Override
	public String copyCostUnit(String id_copy,String name_new,String begintime_new) {
		String result = "";
		if(StringUtils.isNotEmpty(id_copy)&&StringUtils.isNotEmpty(name_new)) {
			name_new = name_new.trim();
			if(StringUtils.isEmpty(begintime_new)) {
				begintime_new = DateTimeUtils.getNowYear()+"-01-01";
			}
			//核算对象（查询）
        	Map<String, Costuint> costuintMap = new HashMap<String, Costuint>();
        	List<Costuint> costuintList = methodService.getCostuintList(null);
        	if(StringUtils.isNotEmpty(costuintList)) {
        		costuintMap = costuintList.stream().collect(Collectors.toMap(Costuint::getId,Function.identity()));
        	}
        	if(StringUtils.isNotEmpty(costuintMap)&&costuintMap.containsKey(id_copy)) {
        		Costuint copyObj = costuintMap.get(id_copy);
        		List<Costuint> costuint_list = new ArrayList<Costuint>();
        		String id_new = TMUID.getUID();
    			Costuint dataObj = new Costuint();
				BeanUtils.copyProperties(copyObj, dataObj); //赋予返回对象
				dataObj.setId(id_new);
				dataObj.setName(name_new);
				costuint_list.add(dataObj);
				MethodSaveDto saveDto = new MethodSaveDto();
				saveDto.setEditType("copy");
				saveDto.setCostuint_list(costuint_list);
				result = methodService.saveCostuintData(saveDto);
				if(StringUtils.isEmpty(result)) { //核算对象复制成功后，复制相关属性记录
					result = this.copyDataByCostUnit(id_copy, id_new, begintime_new);
				}
        	}else {
        		result = "未获取有效的复制记录！";
        	}
		}
		return result;
	}
	
	
	/**
	 *	根据核算对象复制数据
	 * @param id_copy
	 * @param id_new
	 * @param begintime_new
	 * @return
	 */
	public String copyDataByCostUnit(String id_copy,String id_new,String begintime_new) {
		String result = "";
		List<Costunitoperator> costunitoperator_list = new ArrayList<Costunitoperator>(); //核算对象的操作对象
		List<Costunitmanager> costunitmanager_list = new ArrayList<Costunitmanager>(); //核算对象的管理对象
		List<Costunitcycle> costunitcycle_list = new ArrayList<Costunitcycle>(); //核算对象周期
		
		if(StringUtils.isNotEmpty(id_copy)&&StringUtils.isNotEmpty(id_new)&&StringUtils.isNotEmpty(begintime_new)) {
			//查询条件
			MethodQueryDto queryDto = new MethodQueryDto();
			queryDto.setUnitid(id_copy);
			
			//核算对象的操作对象（查询）
			List<Costunitoperator> costunitoperatorList = methodService.getCostunitoperatorList(queryDto);
			if(StringUtils.isNotEmpty(costunitoperatorList)) {
				for (int i = 0; i < costunitoperatorList.size(); i++) {
					Costunitoperator costunitoperatorObj = costunitoperatorList.get(i);
					costunitoperatorObj.setId(TMUID.getUID());
					costunitoperatorObj.setUnitid(id_new);
					costunitoperator_list.add(costunitoperatorObj);
				}
			}
			//核算对象的管理对象（查询）
			List<Costunitmanager> costunitmanagerList = methodService.getCostunitmanagerList(queryDto);
			if(StringUtils.isNotEmpty(costunitmanagerList)) {
				for (int i = 0; i < costunitmanagerList.size(); i++) {
					Costunitmanager costunitmanagerObj = costunitmanagerList.get(i);
					costunitmanagerObj.setId(TMUID.getUID());
					costunitmanagerObj.setUnitid(id_new);
					costunitmanager_list.add(costunitmanagerObj);
				}
			}
			//核算对象周期（查询）
			List<Costunitcycle> costunitcycleList = methodService.getCostunitcycleList(queryDto);
			if(StringUtils.isNotEmpty(costunitcycleList)) {
				for (int i = 0; i < costunitcycleList.size(); i++) {
					Costunitcycle costunitcycleObj = costunitcycleList.get(i);
					costunitcycleObj.setId(TMUID.getUID());
					costunitcycleObj.setUnitid(id_new);
					costunitcycle_list.add(costunitcycleObj);
				}
			}
			//核算单元版本（查询）
			List<Costunitversion> costunitversionList = methodService.getCostunitversionList(queryDto);
			if(StringUtils.isNotEmpty(costunitversionList)) {
				Costunitversion costunitversionObj = costunitversionList.get(0); //复制数据使用最新版本
				MethodSaveDto saveDto = new MethodSaveDto();
				List<Costunitversion> costunitversion_list = new ArrayList<Costunitversion>();
				Costunitversion addVersionObj = new Costunitversion();
				addVersionObj.setUnitid(id_new);
				addVersionObj.setBegintime(begintime_new);
				costunitversion_list.add(addVersionObj);
				saveDto.setCostunitversion_list(costunitversion_list);
				saveDto.setUnitid_copy(costunitversionObj.getUnitid());
				saveDto.setBegintime_copy(costunitversionObj.getBegintime());
				String ret = methodService.saveCostunitversionData(saveDto);
				if(StringUtils.isNotEmpty(ret)) {
					result += "、" + ret;
				}
			}
		}
		
		MethodSaveDto saveDto = new MethodSaveDto();
		saveDto.setEditType("save");
		saveDto.setUnitid(id_new);
		//核算对象的操作对象
		if(StringUtils.isNotEmpty(costunitoperator_list)) {
			saveDto.setCostunitoperator_list(costunitoperator_list);
			String ret = methodService.saveCostunitoperatorData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		//核算对象的管理对象
		if(StringUtils.isNotEmpty(costunitmanager_list)) {
			saveDto.setCostunitmanager_list(costunitmanager_list);
			String ret = methodService.saveCostunitmanagerData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		//核算对象周期
		if(StringUtils.isNotEmpty(costunitcycle_list)) {
			saveDto.setCostunitcycle_list(costunitcycle_list);
			String ret = methodService.saveCostunitcycleData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		if(StringUtils.isNotEmpty(result)) {
			result = result.substring(1);
		}
		return result;
	}
	
	/**
	 *	根据版本复制数据
	 * @param unitid_copy  原核算单元id
	 * @param unitid_new  新核算单元id
	 * @param begintime_copy  原版本日期
	 * @param begintime_new   新版本日期
	 * @return
	 */
	@Override
	public String copyDataByVersion(String unitid_copy,String unitid_new,String begintime_copy,String begintime_new) {
		String result = "";
		List<Costunitsampleclass> costunitsampleclass_list = new ArrayList<Costunitsampleclass>(); //核算对象的采集点分类
		List<Costunitsampledot> costunitsampledot_list = new ArrayList<Costunitsampledot>(); //核算对象采集点
		List<Costclass> costclass_list = new ArrayList<Costclass>(); //成本项目分类
		List<Costitem> costitem_list = new ArrayList<Costitem>(); //成本项目
		List<Costinstrument> costinstrument_list = new ArrayList<Costinstrument>(); //成本项目仪表
		List<CostStipulateTime> costStipulateTime_list = new ArrayList<CostStipulateTime>(); //项目仪表的规定时间
		List<Costindicator> costindicator_list = new ArrayList<Costindicator>(); //核算指标
		List<CostItemFormula> costItemFormula_list = new ArrayList<CostItemFormula>(); //核算公式
		
		if(StringUtils.isNotEmpty(unitid_copy)&&StringUtils.isNotEmpty(unitid_new)&&StringUtils.isNotEmpty(begintime_copy)&&StringUtils.isNotEmpty(begintime_new)) {
			//查询条件
			MethodQueryDto queryDto = new MethodQueryDto();
			queryDto.setUnitid(unitid_copy);
			queryDto.setBegintime(begintime_copy);
			
			//核算对象的采集点分类（查询）
			Map<String, List<Costunitsampleclass>> costunitsampleclassMap = new HashMap<String, List<Costunitsampleclass>>();
			List<Costunitsampleclass> costunitsampleclassList = methodService.getCostunitsampleclassList(queryDto);
			if(StringUtils.isNotEmpty(costunitsampleclassList)) {
				costunitsampleclassMap = costunitsampleclassList.stream().collect(Collectors.groupingBy(Costunitsampleclass::getPid));
			}
			//核算对象采集点（查询）
			Map<String, Costunitsampledot> costunitsampledot_oldId_newObjMap = new HashMap<String, Costunitsampledot>(); //旧id_新对象Map
			Map<String, List<Costunitsampledot>> costunitsampledotMap = new HashMap<String, List<Costunitsampledot>>();
			List<Costunitsampledot> costunitsampledotList = methodService.getCostunitsampledotList(queryDto);
			if(StringUtils.isNotEmpty(costunitsampledotList)) {
				costunitsampledotMap = costunitsampledotList.stream().collect(Collectors.groupingBy(Costunitsampledot::getPid));
			}
			//整理数据
			this.getChildSaveListBySampleclass(costunitsampleclass_list, costunitsampledot_list,costunitsampledot_oldId_newObjMap,
				 begintime_new, costunitsampleclassMap, "root", "root", costunitsampledotMap);
			
			List<String> pidList = new ArrayList<String>(); //查询公式数据用
			//成本项目分类（查询）
			Map<String, List<Costclass>> costclassMap = new HashMap<String, List<Costclass>>();
			List<Costclass> costclassList = methodService.getCostclassList(queryDto);
			if(StringUtils.isNotEmpty(costclassList)) {
				costclassMap = costclassList.stream().collect(Collectors.groupingBy(Costclass::getPid));
			}
			//成本项目（查询）
			Map<String, List<Costitem>> costitemMap = new HashMap<String, List<Costitem>>();
			List<Costitem> costitemList = methodService.getCostitemList(queryDto);
			if(StringUtils.isNotEmpty(costitemList)) {
				costitemMap = costitemList.stream().collect(Collectors.groupingBy(Costitem::getPid));
				List<String> idList = costitemList.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
				if(StringUtils.isNotEmpty(idList)) {
					pidList.addAll(idList);
				}
			}
			//成本项目仪表（查询）
			Map<String, List<Costinstrument>> costinstrumentMap = new HashMap<String, List<Costinstrument>>();
			List<Costinstrument> costinstrumentList = methodService.getCostinstrumentList(queryDto);
			if(StringUtils.isNotEmpty(costinstrumentList)) {
				costinstrumentMap = costinstrumentList.stream().collect(Collectors.groupingBy(Costinstrument::getPid));
				List<String> idList = costinstrumentList.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
				if(StringUtils.isNotEmpty(idList)) {
					pidList.addAll(idList);
				}
			}
			
			//项目仪表的规定时间（查询）
			Map<String, List<CostStipulateTime>> costStipulateTimeMap = new HashMap<String, List<CostStipulateTime>>();
			//先判断此核算对象是否有班次数据，有班次数据，再复制规定时间
			String currDateStr = DateTimeUtils.getNowDateStr();
			List<ShiftForeignVo> shiftClassList = unitItemInfoService.getShiftList(unitid_new, currDateStr);
			if(StringUtils.isNotEmpty(shiftClassList)) {
				Map<String, List<ShiftForeignVo>> shiftClassCodeMap = shiftClassList.stream().collect(Collectors.groupingBy(ShiftForeignVo::getShiftClassCode));
				if(StringUtils.isNotEmpty(shiftClassCodeMap)) {
					List<CostStipulateTime> copyCostStipulateTimeList = new ArrayList<CostStipulateTime>();
					List<CostStipulateTime> copyCostStipulateTimeList_all = methodService.getCostStipulateTimeList(queryDto);
					if(StringUtils.isNotEmpty(copyCostStipulateTimeList_all)) {
						for (int i = 0; i < copyCostStipulateTimeList_all.size(); i++) {
							CostStipulateTime copyCostStipulateTimeObj = copyCostStipulateTimeList_all.get(i);
							String shiftClassCode = copyCostStipulateTimeObj.getShiftClassCode();
							if(shiftClassCodeMap.containsKey(shiftClassCode)) {
								copyCostStipulateTimeList.add(copyCostStipulateTimeObj);
							}
						}
					}
					if(StringUtils.isNotEmpty(copyCostStipulateTimeList)) {
						costStipulateTimeMap = copyCostStipulateTimeList.stream().collect(Collectors.groupingBy(CostStipulateTime::getPid));
					}
				}
			}
			
			//核算指标（查询）
			List<Costindicator> costindicatorList = methodService.getCostindicatorList(queryDto);
			if(StringUtils.isNotEmpty(costindicatorList)) {
				List<String> idList = costindicatorList.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
				if(StringUtils.isNotEmpty(idList)) {
					pidList.addAll(idList);
				}
			}
			//成本项目公式（查询）
			Map<String, List<CostItemFormula>> costItemFormulaMap = new HashMap<String, List<CostItemFormula>>();
			if(StringUtils.isNotEmpty(pidList)) {
				MethodQueryDto dto = new MethodQueryDto();
				dto.setPidList(pidList);
				List<CostItemFormula> costItemFormulaList = methodService.getCostItemFormulaList(dto);
				if(StringUtils.isNotEmpty(costItemFormulaList)) {
					costItemFormulaMap = costItemFormulaList.stream().collect(Collectors.groupingBy(CostItemFormula::getPid));
				}
			}
			//整理数据（核算项目分类）
			this.getChildSaveListByCostclass(costclass_list, costitem_list, costinstrument_list, costStipulateTime_list, costItemFormula_list,
					begintime_new, costclassMap, "root", "root", costitemMap, costinstrumentMap, costStipulateTimeMap, costItemFormulaMap,costunitsampledot_oldId_newObjMap);
			//整理数据（核算指标）
			if(StringUtils.isNotEmpty(costindicatorList)) {
				for (int i = 0; i < costindicatorList.size(); i++) {
					Costindicator indicatorObj = costindicatorList.get(i);
					String copy_indicator_id = indicatorObj.getId();
					String new_indicator_id = TMUID.getUID();
					indicatorObj.setId(new_indicator_id);
					indicatorObj.setBegintime(begintime_new);
					costindicator_list.add(indicatorObj);
					//处理子记录（公式）
					if(StringUtils.isNotEmpty(costItemFormulaMap)&&costItemFormulaMap.containsKey(copy_indicator_id)) {
						List<CostItemFormula> formulaList = costItemFormulaMap.get(copy_indicator_id);
						if(StringUtils.isNotEmpty(formulaList)) {
							for (int m = 0; m < formulaList.size(); m++) {
								CostItemFormula formulaObj = formulaList.get(m);
								formulaObj.setId(TMUID.getUID());
								formulaObj.setPid(new_indicator_id);
								formulaObj.setBegintime(begintime_new);
								costItemFormula_list.add(formulaObj);
							}
						}
					}
				}
			}
		}
		
		MethodSaveDto saveDto = new MethodSaveDto();
		saveDto.setEditType("save");
		saveDto.setUnitid(unitid_new);
		saveDto.setBegintime(begintime_new);
		//核算对象的采集点分类
		if(StringUtils.isNotEmpty(costunitsampleclass_list)) {
			saveDto.setCostunitsampleclass_list(costunitsampleclass_list);
			String ret = methodService.saveCostunitsampleclassData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		//核算对象采集点
		if(StringUtils.isNotEmpty(costunitsampledot_list)) {
			saveDto.setCostunitsampledot_list(costunitsampledot_list);
			String ret = methodService.saveCostunitsampledotData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		//成本项目分类
		if(StringUtils.isNotEmpty(costclass_list)) {
			saveDto.setCostclass_list(costclass_list);
			String ret = methodService.saveCostclassData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		//成本项目
		if(StringUtils.isNotEmpty(costitem_list)) {
			saveDto.setCostitem_list(costitem_list);
			String ret = methodService.saveCostitemData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		//成本项目仪表
		if(StringUtils.isNotEmpty(costinstrument_list)) {
			saveDto.setCostinstrument_list(costinstrument_list);
			String ret = methodService.saveCostinstrumentData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		//项目仪表的规定时间
		if(StringUtils.isNotEmpty(costStipulateTime_list)) {
			saveDto.setCostStipulateTime_list(costStipulateTime_list);
			String ret = methodService.saveCostStipulateTimeData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		//核算指标
		if(StringUtils.isNotEmpty(costindicator_list)) {
			saveDto.setCostindicator_list(costindicator_list);
			String ret = methodService.saveCostindicatorData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		//核算公式
		if(StringUtils.isNotEmpty(costItemFormula_list)) {
			List<String> pidList = costItemFormula_list.stream().filter(item -> StringUtils.isNotEmpty(item.getPid())).map(item -> item.getPid()).collect(Collectors.toList());
			saveDto.setPidList(pidList);
			saveDto.setCostItemFormula_list(costItemFormula_list);
			String ret = methodService.saveCostItemFormulaData(saveDto);
			if(StringUtils.isNotEmpty(ret)) {
				result += "、" + ret;
			}
		}
		if(StringUtils.isNotEmpty(result)) {
			result = result.substring(1);
		}
		return result;
	}
	
	
	/**
	 *	根据核算对象的采集点分类获取需要保存的子级数据
	 * @param costunitsampleclass_list
	 * @param costunitsampledot_list
	 * @param costunitsampledot_oldId_newObjMap
	 * @param begintime_new
	 * @param costunitsampleclassMap
	 * @param pid
	 * @param pid_new
	 * @param costunitsampledotMap
	 */
	private void getChildSaveListBySampleclass(List<Costunitsampleclass> costunitsampleclass_list,List<Costunitsampledot> costunitsampledot_list,Map<String, Costunitsampledot> costunitsampledot_oldId_newObjMap,
		String begintime_new,Map<String, List<Costunitsampleclass>> costunitsampleclassMap,String pid,String pid_new,Map<String, List<Costunitsampledot>> costunitsampledotMap) {
		if(StringUtils.isNotEmpty(costunitsampleclassMap)&&StringUtils.isNotEmpty(pid)&&costunitsampleclassMap.containsKey(pid)) {
			List<Costunitsampleclass> costunitsampleclassList = costunitsampleclassMap.get(pid);
			if(StringUtils.isNotEmpty(costunitsampleclassList)) {
				for (int i = 0; i < costunitsampleclassList.size(); i++) {
					Costunitsampleclass classObj = costunitsampleclassList.get(i);
					String copy_class_id = classObj.getId();
					String new_class_id = TMUID.getUID();
					classObj.setId(new_class_id);
					classObj.setPid(pid_new);
					classObj.setBegintime(begintime_new);
					costunitsampleclass_list.add(classObj);
					//处理子记录
					if(StringUtils.isNotEmpty(costunitsampledotMap)&&costunitsampledotMap.containsKey(copy_class_id)) {
						List<Costunitsampledot> sampledotList = costunitsampledotMap.get(copy_class_id);
						if(StringUtils.isNotEmpty(sampledotList)) {
							for (int j = 0; j < sampledotList.size(); j++) {
								Costunitsampledot sampledotObj = sampledotList.get(j);
								String id = sampledotObj.getId();
								sampledotObj.setId(TMUID.getUID());
								sampledotObj.setPid(new_class_id);
								sampledotObj.setBegintime(begintime_new);
								costunitsampledot_list.add(sampledotObj);
								costunitsampledot_oldId_newObjMap.put(id, sampledotObj);
							}
						}
					}
					this.getChildSaveListBySampleclass(costunitsampleclass_list, costunitsampledot_list,costunitsampledot_oldId_newObjMap,
							 begintime_new, costunitsampleclassMap, copy_class_id, new_class_id, costunitsampledotMap);
				}
			}
		}
	}
	
	
	/**
	 *	根据核算项目分类获取需要保存的子级数据
	 * @param costclass_list
	 * @param costitem_list
	 * @param costinstrument_list
	 * @param costItemFormula_list
	 * @param begintime_new
	 * @param costclassMap
	 * @param pid
	 * @param pid_new
	 * @param costitemMap
	 * @param costinstrumentMap
	 * @param costItemFormulaMap
	 * @param costunitsampledot_oldId_newObjMap
	 */
	private void getChildSaveListByCostclass(List<Costclass> costclass_list,List<Costitem> costitem_list,List<Costinstrument> costinstrument_list, List<CostStipulateTime> costStipulateTime_list,
		List<CostItemFormula> costItemFormula_list,
		String begintime_new,Map<String, List<Costclass>> costclassMap,String pid,String pid_new,Map<String, List<Costitem>> costitemMap,Map<String, List<Costinstrument>> costinstrumentMap, Map<String, List<CostStipulateTime>> costStipulateTimeMap,
		Map<String, List<CostItemFormula>> costItemFormulaMap,Map<String, Costunitsampledot> costunitsampledot_oldId_newObjMap) {
		if(StringUtils.isNotEmpty(costclassMap)&&StringUtils.isNotEmpty(pid)&&costclassMap.containsKey(pid)) {
			List<Costclass> costclassList = costclassMap.get(pid);
			if(StringUtils.isNotEmpty(costclassList)) {
				for (int i = 0; i < costclassList.size(); i++) {
					Costclass costclassObj = costclassList.get(i);
					String copy_class_id = costclassObj.getId();
					String new_class_id = TMUID.getUID();
					costclassObj.setId(new_class_id);
					costclassObj.setPid(pid_new);
					costclassObj.setBegintime(begintime_new);
					costclass_list.add(costclassObj);
					//处理子记录
					if(StringUtils.isNotEmpty(costitemMap)&&costitemMap.containsKey(copy_class_id)) {
						List<Costitem> itemList = costitemMap.get(copy_class_id);
						if(StringUtils.isNotEmpty(itemList)) {
							for (int j = 0; j < itemList.size(); j++) {
								Costitem itemObj = itemList.get(j);
								String copy_item_id = itemObj.getId();
								String new_item_id = TMUID.getUID();
								itemObj.setId(new_item_id);
								itemObj.setPid(new_class_id);
								itemObj.setBegintime(begintime_new);
								costitem_list.add(itemObj);
								//处理子记录
								if(StringUtils.isNotEmpty(costinstrumentMap)&&costinstrumentMap.containsKey(copy_item_id)) {
									List<Costinstrument> instrumentList = costinstrumentMap.get(copy_item_id);
									if(StringUtils.isNotEmpty(instrumentList)) {
										for (int k = 0; k < instrumentList.size(); k++) {
											Costinstrument instrumentObj = instrumentList.get(k);
											String copy_instrument_id = instrumentObj.getId();
											String new_instrument_id = TMUID.getUID();
											String instrument_dotid = instrumentObj.getDotid();
											String instrument_dotid_new = "";
											boolean isAdd = true;
											if(StringUtils.isNotEmpty(instrument_dotid)) {
												if(StringUtils.isNotEmpty(costunitsampledot_oldId_newObjMap)&&costunitsampledot_oldId_newObjMap.containsKey(instrument_dotid)) {
													instrument_dotid_new = costunitsampledot_oldId_newObjMap.get(instrument_dotid).getId();
												}else {
													isAdd = false;
												}
											}
											if(isAdd) {
												instrumentObj.setId(new_instrument_id);
												instrumentObj.setPid(new_item_id);
												instrumentObj.setBegintime(begintime_new);
												instrumentObj.setDotid(instrument_dotid_new);
												costinstrument_list.add(instrumentObj);
												//处理子记录（公式）
												if(StringUtils.isNotEmpty(costItemFormulaMap)&&costItemFormulaMap.containsKey(copy_instrument_id)) {
													List<CostItemFormula> formulaList = costItemFormulaMap.get(copy_instrument_id);
													if(StringUtils.isNotEmpty(formulaList)) {
														for (int m = 0; m < formulaList.size(); m++) {
															CostItemFormula formulaObj = formulaList.get(m);
															formulaObj.setId(TMUID.getUID());
															formulaObj.setPid(new_instrument_id);
															formulaObj.setBegintime(begintime_new);
															costItemFormula_list.add(formulaObj);
														}
													}
												}
												//规定时间（公式）
												if(StringUtils.isNotEmpty(costStipulateTimeMap)&&costStipulateTimeMap.containsKey(copy_instrument_id)) {
													List<CostStipulateTime> stipulateTimeList = costStipulateTimeMap.get(copy_instrument_id);
													if(StringUtils.isNotEmpty(stipulateTimeList)) {
														for (int m = 0; m < stipulateTimeList.size(); m++) {
															CostStipulateTime stipulateTimeObj = stipulateTimeList.get(m);
															stipulateTimeObj.setId(TMUID.getUID());
															stipulateTimeObj.setPid(new_instrument_id);
															stipulateTimeObj.setBegintime(begintime_new);
															costStipulateTime_list.add(stipulateTimeObj);
														}
													}
												}
											}
										}
									}
								}
								//处理子记录（公式）
								if(StringUtils.isNotEmpty(costItemFormulaMap)&&costItemFormulaMap.containsKey(copy_item_id)) {
									List<CostItemFormula> formulaList = costItemFormulaMap.get(copy_item_id);
									if(StringUtils.isNotEmpty(formulaList)) {
										for (int m = 0; m < formulaList.size(); m++) {
											CostItemFormula formulaObj = formulaList.get(m);
											formulaObj.setId(TMUID.getUID());
											formulaObj.setPid(new_item_id);
											formulaObj.setBegintime(begintime_new);
											costItemFormula_list.add(formulaObj);
										}
									}
								}
							}
						}
					}
					this.getChildSaveListByCostclass(costclass_list, costitem_list, costinstrument_list, costStipulateTime_list, costItemFormula_list,
							begintime_new, costclassMap, copy_class_id, new_class_id, costitemMap, costinstrumentMap, costStipulateTimeMap, costItemFormulaMap,costunitsampledot_oldId_newObjMap);
				}
			}
		}
	}
	
	
	/**
	 *	获取采集点数据Map
	 * @param unitid
	 * @param begintime
	 * @return
	 */
	@Override
	public Map<String, Costunitsampledot> getSampledotMap(String unitid,String begintime) {
		Map<String, Costunitsampledot> map = new HashMap<String, Costunitsampledot>();
		List<Costunitsampledot> list = new ArrayList<Costunitsampledot>();
		if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)) {
			String redisKey = "COST:COSTUNITSAMPLEDOT:"+unitid+":"+begintime;
			Object redisList = redisUtil.getObject(redisKey);
			if(redisList!=null) {
				try {
					ObjectMapper mapper = new ObjectMapper();
					TypeReference<List<Costunitsampledot>> typeRef = new TypeReference<List<Costunitsampledot>>(){};
					list = mapper.readValue(JSON.toJSONString(redisList), typeRef);
				}catch(Exception e) {}
			}else {
				MethodQueryDto methodDto = new MethodQueryDto();
				methodDto.setUnitid(unitid);
				methodDto.setBegintime(begintime);
				List<Costunitsampledot> sampledotList = methodService.getCostunitsampledotList(methodDto);
				if(StringUtils.isNotEmpty(sampledotList)) {
					list = sampledotList;
					redisUtil.setObject(redisKey, sampledotList);
				}
			}
		}
		if(StringUtils.isNotEmpty(list)) {
			map = list.stream().collect(Collectors.toMap(Costunitsampledot::getId,Function.identity()));
		}
		return map;
	}
	
	
	/**
	 *	获取采集点数据（上级分类、最大版本）
	 * @param unitid
	 * @param begintime
	 * @param not_ctype
	 * @param itemName
	 * @param page
	 * @return
	 */
	@Override
	public List<SampledotVo> getSampledotListByClass(String unitid,String begintime,String not_ctype,String itemName,Pagination<?> page) {
		List<SampledotVo> result = new ArrayList<SampledotVo>();
		if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)) {
			String maxVer_class = methodService.getMaxVersionByCostunitsampleclass(unitid, begintime);
			String maxVer_dot = methodService.getMaxVersionByCostunitsampledot(unitid, begintime);
			if(StringUtils.isNotEmpty(maxVer_class)&&StringUtils.isNotEmpty(maxVer_dot)) {
				Map<String, List<SampledotVo>> dotMap = new HashMap<String, List<SampledotVo>>();
				//采集点分类
				MethodQueryDto classDto = new MethodQueryDto();
				classDto.setUnitid(unitid);
				classDto.setBegintime(maxVer_class);
				Map<String, Costunitsampleclass> classMap = new HashMap<String, Costunitsampleclass>();
				List<Costunitsampleclass> classList = methodService.getCostunitsampleclassList(classDto);
				if(StringUtils.isNotEmpty(classList)) {
					classMap = classList.stream().collect(Collectors.toMap(Costunitsampleclass::getId,Function.identity()));
				}
				//采集点
				MethodQueryDto dotDto = new MethodQueryDto();
				dotDto.setUnitid(unitid);
				dotDto.setBegintime(maxVer_dot);
				dotDto.setCtypeNot(not_ctype);
				dotDto.setName(itemName);
				List<Costunitsampledot> dotList = methodService.getCostunitsampledotList(dotDto);
				if(StringUtils.isNotEmpty(dotList)) {
					for (int i = 0; i < dotList.size(); i++) {
						Costunitsampledot dotObj = dotList.get(i);
						String pid = dotObj.getPid();
						String name = dotObj.getName();
						if(StringUtils.isNotEmpty(pid)&&StringUtils.isNotEmpty(name)&&StringUtils.isNotEmpty(classMap)&&classMap.containsKey(pid)) {
							name = name.trim();
							String className = classMap.get(pid).getName();
							SampledotVo vo = new SampledotVo();
							BeanUtils.copyProperties(dotObj, vo);
							vo.setParentName(className);
							vo.setName(name);
							if(StringUtils.isNotEmpty(dotMap)&&dotMap.containsKey(pid)) {
								List<SampledotVo> list = dotMap.get(pid);
								list.add(vo);
								dotMap.put(pid, list);
							}else {
								List<SampledotVo> list = new ArrayList<SampledotVo>();
								list.add(vo);
								dotMap.put(pid, list);
							}
						}
					}
				}
				if(StringUtils.isNotEmpty(classList)&&StringUtils.isNotEmpty(dotMap)) {
					for (int i = 0; i < classList.size(); i++) {
						String classId = classList.get(i).getId();
						if(dotMap.containsKey(classId)) {
							result.addAll(dotMap.get(classId));
						}
					}
				}
			}
		}
		if(page!=null) {
			if(StringUtils.isNotEmpty(result)) {
				page.setTotal(result.size());
				result = this.listPage(result, page);
			}else {
				page.setTotal(0);
			}
		}
		return result;
	}
	
	//分页
	private List<SampledotVo> listPage(List<SampledotVo> list, Pagination<?> page) {
		int intPage = page.getPage();
		int number = page.getSize();
		int start = (intPage - 1) * number;
		int totalCount = list.size();
		int pageCount = totalCount % number == 0 ? totalCount / number : totalCount / number + 1;
		List<SampledotVo> result = null;
		if(intPage == pageCount) {
			result = list.subList(start,totalCount);  
		}else {
			result = list.subList(start,start+number);  
		}
		return result;
	}
	
	//——————————————————————————————————————————   复制采集点 ↓ ————————————————————————————————————————————————————————————
	
	/**
	 *	从其他核算对象复制采集点数据
	 * @param unitid  本核算对象ID
	 * @param begintime 本核算对象版本日期
	 * @param unitid_copy 复制核算对象ID
	 * @param ctype 采样点的采集类型                                     采集类型：1、成本仪表；2、控制指标；3、lims指标；0、无；
	 * @param ctypeNot 采样点的采集类型（不等于）
	 * @param newDotIdMap 新采集点idMap（<旧id,新id>）
	 * @return
	 */
	@Override
	public String copySampledotByUnitId(String unitid, String begintime, String unitid_copy,String ctype,String ctypeNot,HashMap<String, String> newDotIdMap) {
		String result = "";
		if(newDotIdMap==null) {
			newDotIdMap = new HashMap<String, String>();
		}
		List<Costunitsampleclass> addClassList = new ArrayList<Costunitsampleclass>();
		List<Costunitsampledot> addDotList = new ArrayList<Costunitsampledot>();
		List<Costunitsampledot> updDotList = new ArrayList<Costunitsampledot>();
		List<CostStipulateTime> add_costStipulateTime_list = new ArrayList<CostStipulateTime>(); //采集时间
		//调用同步其他数据的接口
		List<Costunitsampledot> synOtherList = new ArrayList<Costunitsampledot>();
		if(StringUtils.isNotEmpty(unitid_copy)) {
			String begintime_copy = methodService.getMaxVersionCostunit(unitid_copy, null);
			if(StringUtils.isNotEmpty(begintime_copy)) {
				//复制的采集点数据
				MethodQueryDto copyDotDto = new MethodQueryDto();
				copyDotDto.setUnitid(unitid_copy);
				copyDotDto.setBegintime(begintime_copy);
				copyDotDto.setCtype(ctype);
				copyDotDto.setCtypeNot(ctypeNot);
				List<Costunitsampledot> copyDotList = methodService.getCostunitsampledotList(copyDotDto);
				if(StringUtils.isNotEmpty(copyDotList)) { //有复制的采集点数据
					if(StringUtils.isNotEmpty(ctype)&&"1".equals(ctype)) { //复制成本仪表，没有分类，可以重名，直接复制即可
						//删除已存在采集点（成本仪表）
						MethodQueryDto hasDotDto = new MethodQueryDto();
						hasDotDto.setUnitid(unitid);
						hasDotDto.setBegintime(begintime);
						hasDotDto.setCtype(ctype);
						List<Costunitsampledot> hasDotList = methodService.getCostunitsampledotList(hasDotDto);
						if(StringUtils.isNotEmpty(hasDotList)) {
							for (int i = 0; i < hasDotList.size(); i++) {
								Costunitsampledot hasDotObj = hasDotList.get(i);
								hasDotObj.setTmused(0);
								updDotList.add(hasDotObj);
							}
						}
						//遍历复制记录
						for (int i = 0; i < copyDotList.size(); i++) {
							Costunitsampledot copyDotObj = copyDotList.get(i);
							String oldDotId = copyDotObj.getId();
							String newDotId = TMUID.getUID();
							Costunitsampledot addDotObj = new Costunitsampledot();
							BeanUtils.copyProperties(copyDotObj, addDotObj); //赋予返回对象
							addDotObj.setId(newDotId);
							addDotObj.setUnitid(unitid);
							addDotObj.setBegintime(begintime);
							addDotList.add(addDotObj);
							newDotIdMap.put(oldDotId, newDotId);
						}
					}else { //复制非成本仪表的采集点数据，需要判断是否新增分类，是否重名等
						//获取已存在采集点数据（用于判断是否需要新增）
						MethodQueryDto hasDotDto = new MethodQueryDto();
						hasDotDto.setUnitid(unitid);
						hasDotDto.setBegintime(begintime);
						hasDotDto.setCtypeNot("1");
						List<Costunitsampledot> hasDotList = methodService.getCostunitsampledotList(hasDotDto);
						Map<String, Costunitsampledot> hasDotMap = new HashMap<String, Costunitsampledot>();
						if(StringUtils.isNotEmpty(hasDotList)) {
							hasDotMap = hasDotList.stream().collect(Collectors.toMap(Costunitsampledot::getName,Function.identity()));
						}
						
						//获取已存在的采集点分类数据（用于判断是否需要新增）
						MethodQueryDto hasClassDto = new MethodQueryDto();
						hasClassDto.setUnitid(unitid);
						hasClassDto.setBegintime(begintime);
						List<Costunitsampleclass> hasClassList = methodService.getCostunitsampleclassList(hasClassDto);
						HashMap<String, Costunitsampleclass> hasKeyClassMap = new HashMap<String, Costunitsampleclass>();
						HashMap<String, String> hasIdKeyMap = new HashMap<String, String>();
						if(StringUtils.isNotEmpty(hasClassList)) {
							this.getClassMap(hasClassList, hasIdKeyMap, hasKeyClassMap);
						}
						
						//复制的采集点分类
						MethodQueryDto copyDto = new MethodQueryDto();
						copyDto.setUnitid(unitid_copy);
						copyDto.setBegintime(begintime_copy);
						List<Costunitsampleclass> copyClassList = methodService.getCostunitsampleclassList(copyDto);
						HashMap<String, String> copyIdKeyMap = new HashMap<String, String>();
						HashMap<String, Costunitsampleclass> copyKeyClassMap = new HashMap<String, Costunitsampleclass>();
						if(StringUtils.isNotEmpty(copyClassList)) {
							this.getClassMap(copyClassList, copyIdKeyMap, copyKeyClassMap);
						}
						
						//复制的规定时间
						Map<String, List<CostStipulateTime>> copyCostStipulateTimeMap = new HashMap<String, List<CostStipulateTime>>();
						//先判断此核算对象是否有班次数据，有班次数据，再复制规定时间
						String currDateStr = DateTimeUtils.getNowDateStr();
						List<ShiftForeignVo> shiftClassList = unitItemInfoService.getShiftList(unitid, currDateStr);
						if(StringUtils.isNotEmpty(shiftClassList)) {
							Map<String, List<ShiftForeignVo>> shiftClassCodeMap = shiftClassList.stream().collect(Collectors.groupingBy(ShiftForeignVo::getShiftClassCode));
							if(StringUtils.isNotEmpty(shiftClassCodeMap)) {
								List<CostStipulateTime> copyCostStipulateTimeList = new ArrayList<CostStipulateTime>();
								List<CostStipulateTime> copyCostStipulateTimeList_all = methodService.getCostStipulateTimeList(copyDto);
								if(StringUtils.isNotEmpty(copyCostStipulateTimeList_all)) {
									for (int i = 0; i < copyCostStipulateTimeList_all.size(); i++) {
										CostStipulateTime copyCostStipulateTimeObj = copyCostStipulateTimeList_all.get(i);
										String shiftClassCode = copyCostStipulateTimeObj.getShiftClassCode();
										if(shiftClassCodeMap.containsKey(shiftClassCode)) {
											copyCostStipulateTimeList.add(copyCostStipulateTimeObj);
										}
									}
								}
								if(StringUtils.isNotEmpty(copyCostStipulateTimeList)) {
									copyCostStipulateTimeMap = copyCostStipulateTimeList.stream().collect(Collectors.groupingBy(CostStipulateTime::getPid));
								}
							}
						}
						
						//遍历复制记录
						for (int i = 0; i < copyDotList.size(); i++) {
							Costunitsampledot copyDotObj = copyDotList.get(i);
							String name_copy = copyDotObj.getName();
							if(StringUtils.isEmpty(hasDotMap)||!hasDotMap.containsKey(name_copy)) { //不存在此采集点，新增
								String pid_copy  = copyDotObj.getPid();
								String oldDotId = copyDotObj.getId();
								String newDotId = TMUID.getUID();
								Costunitsampledot addDotObj = new Costunitsampledot();
								BeanUtils.copyProperties(copyDotObj, addDotObj); //赋予返回对象
								addDotObj.setId(newDotId);
								addDotObj.setUnitid(unitid);
								addDotObj.setBegintime(begintime);
								newDotIdMap.put(oldDotId, newDotId);
								String dot_pid = "";
								//判断采集点分类情况（是否需要新增）
								if(StringUtils.isNotEmpty(copyIdKeyMap)&&copyIdKeyMap.containsKey(pid_copy)) { //复制的分类有此ID
									String classKey_copy = copyIdKeyMap.get(pid_copy);
									if(StringUtils.isNotEmpty(hasKeyClassMap)&&hasKeyClassMap.containsKey(classKey_copy)) { //已存在此采集点分类，使用此分类ID作为采集点的pid
										Costunitsampleclass hasClassObj = hasKeyClassMap.get(classKey_copy);
										dot_pid = hasClassObj.getId();
									}else { //不存在此采集点分类，新增
										if(StringUtils.isNotEmpty(copyKeyClassMap)&&copyKeyClassMap.containsKey(classKey_copy)) {
											Costunitsampleclass copyClassObj = copyKeyClassMap.get(classKey_copy);
											Costunitsampleclass addClassObj = new Costunitsampleclass();
											BeanUtils.copyProperties(copyClassObj, addClassObj); //赋予返回对象
											String newId = TMUID.getUID();
											addClassObj.setId(newId);
											addClassObj.setUnitid(unitid);
											addClassObj.setBegintime(begintime);
											String pClassId = this.getParentClassSaveData(addClassList,hasKeyClassMap,copyKeyClassMap,classKey_copy,unitid,begintime);
											addClassObj.setPid(pClassId);
											addClassList.add(addClassObj);
											dot_pid = newId;
											hasKeyClassMap.put(classKey_copy, addClassObj);
										}
									}
								}
								if(StringUtils.isNotEmpty(dot_pid)) {
									addDotObj.setPid(dot_pid);
									addDotList.add(addDotObj);
									synOtherList.add(addDotObj);
									//采集时间
									if(StringUtils.isNotEmpty(copyCostStipulateTimeMap)&&copyCostStipulateTimeMap.containsKey(oldDotId)) {
										List<CostStipulateTime> stipulateTimeList = copyCostStipulateTimeMap.get(oldDotId);
										if(StringUtils.isNotEmpty(stipulateTimeList)) {
											for (int m = 0; m < stipulateTimeList.size(); m++) {
												CostStipulateTime stipulateTimeObj = stipulateTimeList.get(m);
												stipulateTimeObj.setId(TMUID.getUID());
												stipulateTimeObj.setPid(newDotId);
												stipulateTimeObj.setUnitid(unitid);
												stipulateTimeObj.setBegintime(begintime);
												add_costStipulateTime_list.add(stipulateTimeObj);
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
		boolean isHasCopyData = false;
		if ("".equals(result) && StringUtils.isNotEmpty(addClassList)) {
			isHasCopyData = true;
			result = methodService.saveSampleclassData(addClassList, null, null);
		}
		if ("".equals(result) && (StringUtils.isNotEmpty(addDotList)||StringUtils.isNotEmpty(updDotList))) {
			isHasCopyData = true;
			result = methodService.saveSampledotData(addDotList, updDotList, null);
		}
		//调用同步其他数据的接口
		if ("".equals(result) && StringUtils.isNotEmpty(synOtherList)) {
			methodService.synOtherInterfaceBySampledotChange(synOtherList, null);
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costStipulateTime_list)) {
			isHasCopyData = true;
			result = methodService.saveDataCostStipulateTime(add_costStipulateTime_list, null, null);
		}
		if ("".equals(result) && !isHasCopyData) {
			result = "没有可复制的采集点数据！";
		}
		return result;
	}
	
	//获取父级分类新增记录（返回父级ID）
	private String getParentClassSaveData(List<Costunitsampleclass> addClassList,HashMap<String, Costunitsampleclass> hasKeyClassMap,
		HashMap<String, Costunitsampleclass> copyKeyClassMap,String classKey_copy,String unitid,String begintime) {
		String pid = ""; //父ID
		if(StringUtils.isNotEmpty(classKey_copy)) {
			String[] classKey_copy_arr = classKey_copy.split("___");
			if(classKey_copy_arr.length>2) {
				String classKey_copy_new = "";
				for (int i = 0; i < classKey_copy_arr.length-1; i++) {
					classKey_copy_new += "___"+classKey_copy_arr[i];
				}
				classKey_copy_new = classKey_copy_new.substring(3);
				if(StringUtils.isNotEmpty(hasKeyClassMap)&&hasKeyClassMap.containsKey(classKey_copy_new)) { //已存在此采集点分类，使用此分类ID作为采集点的pid
					Costunitsampleclass hasClassObj = hasKeyClassMap.get(classKey_copy_new);
					pid = hasClassObj.getId();
				}else {
					if(StringUtils.isNotEmpty(copyKeyClassMap)&&copyKeyClassMap.containsKey(classKey_copy_new)) {
						Costunitsampleclass copyClassObj = copyKeyClassMap.get(classKey_copy_new);
						Costunitsampleclass addClassObj = new Costunitsampleclass();
						BeanUtils.copyProperties(copyClassObj, addClassObj); //赋予返回对象
						String newId = TMUID.getUID();
						addClassObj.setId(newId);
						addClassObj.setUnitid(unitid);
						addClassObj.setBegintime(begintime);
						String pClassId = this.getParentClassSaveData(addClassList,hasKeyClassMap,copyKeyClassMap,classKey_copy_new,unitid,begintime);
						addClassObj.setPid(pClassId);
						addClassList.add(addClassObj);
						pid = newId;
						hasKeyClassMap.put(classKey_copy_new, addClassObj);
					}
				}
			}else {
				pid = "root";
			}
		}
		return pid;
	}
	
	//获取分类Map
	private void getClassMap(List<Costunitsampleclass> classList, HashMap<String, String> idKeyMap, HashMap<String, Costunitsampleclass> keyClassMap) {
		if(idKeyMap==null) {
			idKeyMap = new HashMap<String, String>();
		}
		if(keyClassMap==null) {
			keyClassMap = new HashMap<String, Costunitsampleclass>();
		}
		if(StringUtils.isNotEmpty(classList)) {
			Map<String, List<Costunitsampleclass>> classMap = classList.stream().collect(Collectors.groupingBy(Costunitsampleclass::getPid));
			for (int i = 0; i < classList.size(); i++) {
				Costunitsampleclass classObj = classList.get(i);
				String id = classObj.getId();
				String name = classObj.getName();
				String pid = classObj.getPid();
				if(StringUtils.isNotEmpty(pid)&&"root".equals(pid)) {
					String key = pid+"___"+name;
					if(!idKeyMap.containsKey(id)) {
						idKeyMap.put(id, key);
					}
					if(!keyClassMap.containsKey(key)) {
						keyClassMap.put(key, classObj);
					}
					this.getChildClassIdKeyMap(classMap, key, id, idKeyMap, keyClassMap);
				}
			}
		}
	}
	
	//获取子级分类Map
	private void getChildClassIdKeyMap(Map<String, List<Costunitsampleclass>> classMap,String dataKey,String dataId,HashMap<String, String> idKeyMap,
		HashMap<String, Costunitsampleclass> keyClassMap) {
		if(StringUtils.isNotEmpty(classMap)&&classMap.containsKey(dataId)) {
			List<Costunitsampleclass> classList = classMap.get(dataId);
			for (int i = 0; i < classList.size(); i++) {
				Costunitsampleclass classObj = classList.get(i);
				String id = classObj.getId();
				String name = classObj.getName();
				String key = dataKey+"___"+name;
				if(!idKeyMap.containsKey(id)) {
					idKeyMap.put(id, key);
				}
				if(!keyClassMap.containsKey(key)) {
					keyClassMap.put(key, classObj);
				}
				this.getChildClassIdKeyMap(classMap, key, id, idKeyMap, keyClassMap);
			}
		}
	}
	
	/**
	 *	采集点另存为到其他分类下
	 * @param dto
	 * @return
	 */
	@Override
	public String dotCopyToOtherClass(paramDto dto) {
		String result = "";
		List<Costunitsampledot> addDotList = new ArrayList<Costunitsampledot>();
		List<CostStipulateTime> add_costStipulateTime_list = new ArrayList<CostStipulateTime>(); //采集时间
		//调用同步其他数据的接口
		List<Costunitsampledot> synOtherList = new ArrayList<Costunitsampledot>();
		if(StringUtils.isNotNull(dto)) {
			String pid = dto.getPid();
			List<String> dotIdList = dto.getList();
			if(StringUtils.isNotEmpty(pid)&&StringUtils.isNotEmpty(dotIdList)) {
				
				//已存在记录
				int maxPx = 0;
				Map<String, Costunitsampledot> nameMap = new HashMap<String, Costunitsampledot>();
				MethodQueryDto hasDotDto = new MethodQueryDto();
				hasDotDto.setPid(pid);
				hasDotDto.setCtypeNot("1");
				List<Costunitsampledot> hasDotList = methodService.getCostunitsampledotList(hasDotDto);
				if(StringUtils.isNotEmpty(hasDotList)) {
					nameMap = hasDotList.stream().collect(Collectors.toMap(Costunitsampledot::getName, Function.identity(), (key1, key2) -> key2));
					Integer tmsort = hasDotList.get(0).getTmsort();
					if(tmsort!=null) {
						maxPx = tmsort;
					}
				}
				
				//被复制记录
				MethodQueryDto copyDotDto = new MethodQueryDto();
				copyDotDto.setIdList(dotIdList);
				copyDotDto.setCtypeNot("1");
				List<Costunitsampledot> copyDotList = methodService.getCostunitsampledotList(copyDotDto);
				if(StringUtils.isNotEmpty(copyDotList)) {
					
					//被复制规定时间
					Map<String, List<CostStipulateTime>> copyTimeMap = new HashMap<String, List<CostStipulateTime>>();
					if(StringUtils.isNotEmpty(dotIdList)) {
						MethodQueryDto timeDto = new MethodQueryDto();
						timeDto.setPidList(dotIdList);
						List<CostStipulateTime> copyCostStipulateTimeList = methodService.getCostStipulateTimeList(timeDto);
						if(StringUtils.isNotEmpty(copyCostStipulateTimeList)) {
							if(StringUtils.isNotEmpty(copyCostStipulateTimeList)) {
								copyTimeMap = copyCostStipulateTimeList.stream().collect(Collectors.groupingBy(CostStipulateTime::getPid));
							}
						}
					}
					
					for (int i = 0; i < copyDotList.size(); i++) {
						Costunitsampledot copyDot = copyDotList.get(i);
						String id_copy = copyDot.getId();
						String name_copy = copyDot.getName();
						if(StringUtils.isNotEmpty(name_copy)) {
							if(StringUtils.isEmpty(nameMap)||!nameMap.containsKey(name_copy)) {
								maxPx += 1;
								Costunitsampledot addObj = new Costunitsampledot();
								BeanUtils.copyProperties(copyDot, addObj); // 赋予返回对象
								addObj.setId(TMUID.getUID());
								addObj.setPid(pid);
								addObj.setTmsort(maxPx);
								addDotList.add(addObj);
								synOtherList.add(addObj);
								nameMap.put(name_copy, addObj);
								
								//规定时间
								if(StringUtils.isNotEmpty(copyTimeMap)&&copyTimeMap.containsKey(id_copy)) {
									List<CostStipulateTime> copyTimeList = copyTimeMap.get(id_copy);
									if(StringUtils.isNotEmpty(copyTimeList)) {
										for (int k = 0; k < copyTimeList.size(); k++) {
											CostStipulateTime copyTimeObj = copyTimeList.get(k);
											CostStipulateTime addTimeObj = new CostStipulateTime();
											BeanUtils.copyProperties(copyTimeObj, addTimeObj); // 赋予返回对象
											addTimeObj.setId(TMUID.getUID());
											addTimeObj.setPid(addObj.getId());
											add_costStipulateTime_list.add(addTimeObj);
										}
									}
								}
							}
						}
					}
				}
			}
		}
		
		if ("".equals(result) && StringUtils.isNotEmpty(addDotList)) {
			if (entityService.insertBatch(addDotList, 500) == 0) {
				result = "添加失败（核算对象的采集点）！";
			}
		}
		//调用同步其他数据的接口
		if ("".equals(result) && StringUtils.isNotEmpty(synOtherList)) {
			methodService.synOtherInterfaceBySampledotChange(synOtherList, null);
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costStipulateTime_list)) {
			if (entityService.insertBatch(add_costStipulateTime_list, 500) == 0) {
				result = "添加失败（采集时间）！";
			}
		}
		return result;
	}
	
	//——————————————————————————————————————————   复制采集点 ↑ ————————————————————————————————————————————————————————————
	
	
	//——————————————————————————————————————————   复制核算项目 ↓ ————————————————————————————————————————————————————————————
	
	/**
	 *	从其他核算对象复制核算项目数据
	 * @param unitid  本核算对象ID
	 * @param begintime 本核算对象版本日期
	 * @param unitid_copy 复制核算对象ID
	 * @param ctype 采样点的采集类型                                     采集类型：1、成本仪表；2、控制指标；3、lims指标；0、无；
	 * @param ctypeNot 采样点的采集类型（不等于）
	 * @return
	 */
	public String copyCostItemByUnitId(String unitid,String begintime,String unitid_copy,String ctype,String ctypeNot) {
		String result = "";
		
		List<Costclass> add_costclass_list = new ArrayList<Costclass>(); //成本项目分类
		List<Costitem> add_costitem_list = new ArrayList<Costitem>(); //成本项目
		List<Costinstrument> add_costinstrument_list = new ArrayList<Costinstrument>(); //成本项目仪表
		List<CostStipulateTime> add_costStipulateTime_list = new ArrayList<CostStipulateTime>(); //规定时间
		List<Costindicator> add_costindicator_list = new ArrayList<Costindicator>(); //核算指标
		List<CostItemFormula> add_costItemFormula_list = new ArrayList<CostItemFormula>(); //公式
		
		List<CostItemFormula> costItemFormula_list = new ArrayList<CostItemFormula>(); //临时存放公式（替换公式前）
		HashMap<String, String> newDotIdMap = new HashMap<String, String>(); //新旧采集点id的Map
		
		String begintime_copy = "";
		if(StringUtils.isNotEmpty(unitid_copy)) { //被复制对象的最大版本日期
			begintime_copy = methodService.getMaxVersionCostunit(unitid_copy, null);
		}
		if(StringUtils.isNotEmpty(unitid_copy)&&StringUtils.isNotEmpty(begintime_copy)) {
			//复制采集点数据（采集类型：成本仪表）
			this.copySampledotByUnitId(unitid, begintime, unitid_copy, ctype, ctypeNot, newDotIdMap);
			
			MethodQueryDto copyDto = new MethodQueryDto();
			copyDto.setUnitid(unitid_copy);
			copyDto.setBegintime(begintime_copy);
			copyDto.setCtype(ctype);
			copyDto.setCtypeNot(ctypeNot);
			//复制的采集点数据
//			List<Costunitsampledot> copyDotList = methodService.getCostunitsampledotList(copyDto);
//			Map<String, Costunitsampledot> copyDotIdMap = new HashMap<String, Costunitsampledot>(); //采集点名称Map
//        	if(StringUtils.isNotEmpty(copyDotList)) {
//        		copyDotIdMap = copyDotList.stream().collect(Collectors.toMap(Costunitsampledot::getId,Function.identity()));
//        	}
			
			//复制的核算项目分类
			List<Costclass> copyCostClassList = methodService.getCostclassList(copyDto);
			Map<String, List<Costclass>> copyCostClassMap = new HashMap<String, List<Costclass>>();
			if(StringUtils.isNotEmpty(copyCostClassList)) {
				copyCostClassMap = copyCostClassList.stream().collect(Collectors.groupingBy(Costclass::getPid));
			}
			
			//复制的核算项目
			List<Costitem> copyCostItemList = methodService.getCostitemList(copyDto);
			Map<String, List<Costitem>> copyCostItemMap = new HashMap<String, List<Costitem>>();
			if(StringUtils.isNotEmpty(copyCostItemList)) {
				copyCostItemMap = copyCostItemList.stream().collect(Collectors.groupingBy(Costitem::getPid));
			}
			
			//复制的成本项目仪表
			List<Costinstrument> copyCostinstrumentList = methodService.getCostinstrumentList(copyDto);
			Map<String, List<Costinstrument>> copyCostinstrumentMap = new HashMap<String, List<Costinstrument>>();
			if(StringUtils.isNotEmpty(copyCostinstrumentList)) {
				copyCostinstrumentMap = copyCostinstrumentList.stream().collect(Collectors.groupingBy(Costinstrument::getPid));
			}
			
			//复制的规定时间
			Map<String, List<CostStipulateTime>> copyCostStipulateTimeMap = new HashMap<String, List<CostStipulateTime>>();
			//先判断此核算对象是否有班次数据，有班次数据，再复制规定时间
			String currDateStr = DateTimeUtils.getNowDateStr();
			List<ShiftForeignVo> shiftClassList = unitItemInfoService.getShiftList(unitid, currDateStr);
			if(StringUtils.isNotEmpty(shiftClassList)) {
				Map<String, List<ShiftForeignVo>> shiftClassCodeMap = shiftClassList.stream().collect(Collectors.groupingBy(ShiftForeignVo::getShiftClassCode));
				if(StringUtils.isNotEmpty(shiftClassCodeMap)) {
					List<CostStipulateTime> copyCostStipulateTimeList = new ArrayList<CostStipulateTime>();
					List<CostStipulateTime> copyCostStipulateTimeList_all = methodService.getCostStipulateTimeList(copyDto);
					if(StringUtils.isNotEmpty(copyCostStipulateTimeList_all)) {
						for (int i = 0; i < copyCostStipulateTimeList_all.size(); i++) {
							CostStipulateTime copyCostStipulateTimeObj = copyCostStipulateTimeList_all.get(i);
							String shiftClassCode = copyCostStipulateTimeObj.getShiftClassCode();
							if(shiftClassCodeMap.containsKey(shiftClassCode)) {
								copyCostStipulateTimeList.add(copyCostStipulateTimeObj);
							}
						}
					}
					if(StringUtils.isNotEmpty(copyCostStipulateTimeList)) {
						copyCostStipulateTimeMap = copyCostStipulateTimeList.stream().collect(Collectors.groupingBy(CostStipulateTime::getPid));
					}
				}
			}
			
			//复制的指标数据
			List<Costindicator> copyCostindicatorList = methodService.getCostindicatorList(copyDto);
			
			//复制的公式
			List<CostItemFormula> copyCostItemFormulaList = methodService.getCostItemFormulaList(copyDto);
			Map<String, List<CostItemFormula>> copyCostItemFormulaMap = new HashMap<String, List<CostItemFormula>>();
			if(StringUtils.isNotEmpty(copyCostItemFormulaList)) {
				copyCostItemFormulaMap = copyCostItemFormulaList.stream().collect(Collectors.groupingBy(CostItemFormula::getPid));
			}
			
			//已存在采集点数据（使用名称对比赋值）
//			MethodQueryDto hasDto = new MethodQueryDto();
//			hasDto.setUnitid(unitid);
//			hasDto.setBegintime(begintime);
//			hasDto.setCtype(ctype);
//			hasDto.setCtypeNot(ctypeNot);
//			List<Costunitsampledot> hasDotList = methodService.getCostunitsampledotList(hasDto);
//			Map<String, Costunitsampledot> hasDotNameMap = new HashMap<String, Costunitsampledot>(); //采集点名称Map
//        	if(StringUtils.isNotEmpty(hasDotList)) {
//        		//hasDotNameMap = hasDotList.stream().collect(Collectors.toMap(Costunitsampledot::getName,Function.identity()));
//        	}
        	
        	//替换公式参数用的数据
        	BeanVo replace_costObj = null; //核算对象
        	List<String> unitIdList = new ArrayList<String>();
        	unitIdList.add(unitid);
        	unitIdList.add(unitid_copy);
        	MethodQueryDto unitQueryDto = new MethodQueryDto();
        	unitQueryDto.setIdList(unitIdList);
        	List<Costuint> costuintList = methodService.getCostuintList(unitQueryDto);
        	if(StringUtils.isNotEmpty(costuintList)&&costuintList.size()==2) {
        		replace_costObj = new BeanVo();
        		for (int i = 0; i < costuintList.size(); i++) {
        			Costuint costuintObj = costuintList.get(i);
        			String id = costuintObj.getId();
        			String name = costuintObj.getName();
        			if(id.equals(unitid_copy)) {
        				replace_costObj.setCopyId(id);
        				replace_costObj.setCopyName(name);
        			}else if(id.equals(unitid)) {
        				replace_costObj.setNewId(id);
        				replace_costObj.setNewName(name);
        			}
				}
        	}
        	List<BeanVo> replace_itemList = new ArrayList<BeanVo>(); //核算项目
        	List<BeanVo> replace_instrumentList = new ArrayList<BeanVo>(); //仪表
        	List<BeanVo> replace_indicatorList = new ArrayList<BeanVo>(); //指标
        	
			//整理数据（核算项目分类、核算项目、核算项目仪表）
			this.getCopyChildSaveData(add_costclass_list, add_costitem_list, add_costinstrument_list, add_costStipulateTime_list, costItemFormula_list,
				replace_itemList, replace_instrumentList,
				unitid, begintime, copyCostClassMap, "root", "root", copyCostItemMap, copyCostinstrumentMap, copyCostStipulateTimeMap, newDotIdMap,copyCostItemFormulaMap);
			
			//整理数据（核算指标）
			if(StringUtils.isNotEmpty(copyCostindicatorList)) {
				for (int i = 0; i < copyCostindicatorList.size(); i++) {
					Costindicator indicatorObj = copyCostindicatorList.get(i);
					String copy_indicator_id = indicatorObj.getId();
					String new_indicator_id = TMUID.getUID();
					indicatorObj.setId(new_indicator_id);
					indicatorObj.setUnitid(unitid);
					indicatorObj.setBegintime(begintime);
					add_costindicator_list.add(indicatorObj);
					//替换公式用
					BeanVo indicatorBean = new BeanVo();
					indicatorBean.setCopyId(copy_indicator_id);
					indicatorBean.setNewId(new_indicator_id);
					replace_indicatorList.add(indicatorBean);
					//指标公式
					if(StringUtils.isNotEmpty(copyCostItemFormulaMap)&&copyCostItemFormulaMap.containsKey(copy_indicator_id)) {
						List<CostItemFormula> formulaList = copyCostItemFormulaMap.get(copy_indicator_id);
						if(StringUtils.isNotEmpty(formulaList)) {
							for (int m = 0; m < formulaList.size(); m++) {
								CostItemFormula formulaObj = formulaList.get(m);
								formulaObj.setId(TMUID.getUID());
								formulaObj.setPid(new_indicator_id);
								formulaObj.setUnitId(unitid);
								formulaObj.setBegintime(begintime);
								costItemFormula_list.add(formulaObj);
							}
						}
					}
				}
			}
			
			//有公式--替换公式
			if(StringUtils.isNotEmpty(costItemFormula_list)) {
				for (int i = 0; i < costItemFormula_list.size(); i++) {
					CostItemFormula formulaObj = costItemFormula_list.get(i);
					String formula = formulaObj.getFormula();
					String cFormula = formulaObj.getCFormula();
					if(StringUtils.isNotEmpty(formula)&&StringUtils.isNotEmpty(cFormula)&&replace_costObj!=null) {
						//替换公式中的核算对象ID、名称
						String copyCostId = replace_costObj.getCopyId();
						String copyCostName = replace_costObj.getCopyName();
						String newCostId = replace_costObj.getNewId();
						String newCostName = replace_costObj.getNewName();
						formula = formula.replaceAll(copyCostId+".", newCostId+".");
						cFormula = cFormula.replaceAll(copyCostName+".", newCostName+".");
						//替换公式中的核算项目ID（名称未变化不需要替换）
						if(StringUtils.isNotEmpty(replace_itemList)) {
							for (int j = 0; j < replace_itemList.size(); j++) {
								BeanVo replace_itemObj = replace_itemList.get(j);
								String copyItemId = replace_itemObj.getCopyId();
								String newItemId = replace_itemObj.getNewId();
								formula = formula.replaceAll("."+copyItemId+".", "."+newItemId+".");
							}
						}
						//替换公式中的仪表ID（名称未变化不需要替换）
						if(StringUtils.isNotEmpty(replace_instrumentList)) {
							for (int j = 0; j < replace_instrumentList.size(); j++) {
								BeanVo replace_instrumentObj = replace_instrumentList.get(j);
								String copyInstrumentId = replace_instrumentObj.getCopyId();
								String newInstrumentId = replace_instrumentObj.getNewId();
								formula = formula.replaceAll("."+copyInstrumentId+".", "."+newInstrumentId+".");
							}
						}
						//替换公式中的仪表ID（名称未变化不需要替换）
						if(StringUtils.isNotEmpty(replace_indicatorList)) {
							for (int j = 0; j < replace_indicatorList.size(); j++) {
								BeanVo replace_indicatorObj = replace_indicatorList.get(j);
								String copyIndicatorId = replace_indicatorObj.getCopyId();
								String newIndicatorId = replace_indicatorObj.getNewId();
								formula = formula.replaceAll(copyIndicatorId, newIndicatorId);
							}
						}
					}
					formulaObj.setFormula(formula);
					formulaObj.setCFormula(cFormula);
					add_costItemFormula_list.add(formulaObj);
				}
			}
		
		}
		//保存数据
		boolean isHasCopyData = false;
		if ("".equals(result) && StringUtils.isNotEmpty(add_costclass_list)) {
			this.deleteInitClassByCopy(unitid, begintime, "root"); //复制分类时，删除初始化的分类“核算项目”
			isHasCopyData = true;
			result = methodService.saveDataCostclass(add_costclass_list, null, null);
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costitem_list)) {
			isHasCopyData = true;
			result = methodService.saveDataCostitem(add_costitem_list, null, null);
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costinstrument_list)) {
			isHasCopyData = true;
			result = methodService.saveDataCostinstrument(add_costinstrument_list, null, null);
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costStipulateTime_list)) {
			isHasCopyData = true;
			result = methodService.saveDataCostStipulateTime(add_costStipulateTime_list, null, null);
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costindicator_list)) {
			isHasCopyData = true;
			result = methodService.saveDataCostindicator(add_costindicator_list, null, null);
		}
		if ("".equals(result) && StringUtils.isNotEmpty(add_costItemFormula_list)) {
			isHasCopyData = true;
			result = methodService.saveDataCostItemFormula(add_costItemFormula_list, null, null);
		}
		if ("".equals(result) && !isHasCopyData) {
			result = "没有可复制的核算项目数据！";
		}
		return result;
	}
	
	//复制数据时，如果根节点下存在“核算项目”分类，删除原来的默认分类“核算项目”，重新复制。
	private void deleteInitClassByCopy(String unitid,String begintime,String pid) {
		if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)&&StringUtils.isNotEmpty(pid)) {
			//默认分类Map
			Map<String, List<BeanVo>> classTypeMap = new HashMap<String, List<BeanVo>>();
			List<BeanVo> classTypeList = methodService.getCostClassTypeList();
        	if(StringUtils.isNotEmpty(classTypeList)) {
        		classTypeMap = classTypeList.stream().collect(Collectors.groupingBy(BeanVo::getKey, Collectors.toList()));
        	}
			//已存在的分类
			List<Costclass> updList = new ArrayList<Costclass>();
			MethodQueryDto dot = new MethodQueryDto();
			dot.setUnitid(unitid);
			dot.setBegintime(begintime);
			dot.setPid(pid);
			//核算项目分类
			List<Costclass> list = methodService.getCostclassList(dot);
			if(StringUtils.isNotEmpty(list)) {
				for (int i = 0; i < list.size(); i++) {
					Costclass obj = list.get(i);
					String ccname = obj.getCcname();
					if(StringUtils.isNotEmpty(ccname)&&StringUtils.isNotEmpty(classTypeMap)&&classTypeMap.containsKey(ccname)) {
						obj.setTmused(0);
						updList.add(obj);
					}
				}
			}
			if(StringUtils.isNotEmpty(updList)) {
				entityService.updateByIdBatchIncludeNull(updList, 500);
			}
		}
	}
	
	//整理数据（核算项目分类、核算项目、核算项目仪表）
	private void getCopyChildSaveData(List<Costclass> add_costclass_list,List<Costitem> add_costitem_list,List<Costinstrument> add_costinstrument_list,List<CostStipulateTime> add_costStipulateTime_list,List<CostItemFormula> costItemFormula_list,
		List<BeanVo> replace_itemList,List<BeanVo> replace_instrumentList,
		String unitid,String begintime,
		Map<String, List<Costclass>> costclassMap,String pid,String pid_new,Map<String, List<Costitem>> costitemMap,Map<String, List<Costinstrument>> costinstrumentMap,Map<String, List<CostStipulateTime>> copyCostStipulateTimeMap,
		HashMap<String, String> newDotIdMap,Map<String, List<CostItemFormula>> copyCostItemFormulaMap) {
		// 核算项目分类
		if(StringUtils.isNotEmpty(costclassMap)&&StringUtils.isNotEmpty(pid)&&costclassMap.containsKey(pid)) {
			List<Costclass> costclassList = costclassMap.get(pid);
			if(StringUtils.isNotEmpty(costclassList)) {
				for (int i = 0; i < costclassList.size(); i++) {
					Costclass costclassObj = costclassList.get(i);
					String copy_class_id = costclassObj.getId();
					String new_class_id = TMUID.getUID();
					costclassObj.setId(new_class_id);
					costclassObj.setPid(pid_new);
					costclassObj.setUnitid(unitid);
					costclassObj.setBegintime(begintime);
					add_costclass_list.add(costclassObj);
					//核算项目
					if(StringUtils.isNotEmpty(costitemMap)&&costitemMap.containsKey(copy_class_id)) {
						List<Costitem> itemList = costitemMap.get(copy_class_id);
						if(StringUtils.isNotEmpty(itemList)) {
							for (int j = 0; j < itemList.size(); j++) {
								Costitem itemObj = itemList.get(j);
								String copy_item_id = itemObj.getId();
								String new_item_id = TMUID.getUID();
								itemObj.setId(new_item_id);
								itemObj.setPid(new_class_id);
								itemObj.setUnitid(unitid);
								itemObj.setBegintime(begintime);
								add_costitem_list.add(itemObj);
								//替换公式用
								BeanVo itemBean = new BeanVo();
								itemBean.setCopyId(copy_item_id);
								itemBean.setNewId(new_item_id);
								replace_itemList.add(itemBean);
								//核算项目公式
								if(StringUtils.isNotEmpty(copyCostItemFormulaMap)&&copyCostItemFormulaMap.containsKey(copy_item_id)) {
									List<CostItemFormula> formulaList = copyCostItemFormulaMap.get(copy_item_id);
									if(StringUtils.isNotEmpty(formulaList)) {
										for (int m = 0; m < formulaList.size(); m++) {
											CostItemFormula formulaObj = formulaList.get(m);
											formulaObj.setId(TMUID.getUID());
											formulaObj.setPid(new_item_id);
											formulaObj.setUnitId(unitid);
											formulaObj.setBegintime(begintime);
											costItemFormula_list.add(formulaObj);
										}
									}
								}
								//核算项目仪表
								if(StringUtils.isNotEmpty(costinstrumentMap)&&costinstrumentMap.containsKey(copy_item_id)) {
									List<Costinstrument> instrumentList = costinstrumentMap.get(copy_item_id);
									if(StringUtils.isNotEmpty(instrumentList)) {
										for (int k = 0; k < instrumentList.size(); k++) {
											Costinstrument instrumentObj = instrumentList.get(k);
											String instrument_dotid = instrumentObj.getDotid();
											String newDotId = "";
											//使用原采集点id获取采集点名称，用采集点名称获取新记录的采集点ID
											boolean isAdd = true;
											if(StringUtils.isNotEmpty(instrument_dotid)) {
												if(StringUtils.isNotEmpty(newDotIdMap)&&newDotIdMap.containsKey(instrument_dotid)) {
													newDotId = newDotIdMap.get(instrument_dotid);
												}else {
													isAdd = false;
												}
											}
											if(isAdd) {
												//生成新仪表数据
												String copy_instrument_id = instrumentObj.getId();
												String new_instrument_id = TMUID.getUID();
												instrumentObj.setId(new_instrument_id);
												instrumentObj.setPid(new_item_id);
												instrumentObj.setUnitid(unitid);
												instrumentObj.setBegintime(begintime);
												instrumentObj.setDotid(newDotId);
												add_costinstrument_list.add(instrumentObj);
												//替换公式用
												BeanVo instrumentBean = new BeanVo();
												instrumentBean.setCopyId(copy_instrument_id);
												instrumentBean.setNewId(new_instrument_id);
												replace_instrumentList.add(instrumentBean);
												//项目仪表公式
												if(StringUtils.isNotEmpty(copyCostItemFormulaMap)&&copyCostItemFormulaMap.containsKey(copy_instrument_id)) {
													List<CostItemFormula> formulaList = copyCostItemFormulaMap.get(copy_instrument_id);
													if(StringUtils.isNotEmpty(formulaList)) {
														for (int m = 0; m < formulaList.size(); m++) {
															CostItemFormula formulaObj = formulaList.get(m);
															formulaObj.setId(TMUID.getUID());
															formulaObj.setPid(new_instrument_id);
															formulaObj.setUnitId(unitid);
															formulaObj.setBegintime(begintime);
															costItemFormula_list.add(formulaObj);
														}
													}
												}
												//规定时间
												if(StringUtils.isNotEmpty(copyCostStipulateTimeMap)&&copyCostStipulateTimeMap.containsKey(copy_instrument_id)) {
													List<CostStipulateTime> stipulateTimeList = copyCostStipulateTimeMap.get(copy_instrument_id);
													if(StringUtils.isNotEmpty(stipulateTimeList)) {
														for (int m = 0; m < stipulateTimeList.size(); m++) {
															CostStipulateTime stipulateTimeObj = stipulateTimeList.get(m);
															stipulateTimeObj.setId(TMUID.getUID());
															stipulateTimeObj.setPid(new_instrument_id);
															stipulateTimeObj.setUnitid(unitid);
															stipulateTimeObj.setBegintime(begintime);
															add_costStipulateTime_list.add(stipulateTimeObj);
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					//递归
					this.getCopyChildSaveData(add_costclass_list, add_costitem_list, add_costinstrument_list, add_costStipulateTime_list, costItemFormula_list,
						replace_itemList, replace_instrumentList,
						unitid, begintime, costclassMap, copy_class_id, new_class_id, costitemMap, costinstrumentMap, copyCostStipulateTimeMap, newDotIdMap,copyCostItemFormulaMap);
				}
			}
		}
	}
	
	//——————————————————————————————————————————   复制核算项目 ↑ ————————————————————————————————————————————————————————————
	
	
	/**
	 *	获取核算项目树形数据
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<CostItemTreeVo> getCostItemTree(CostItemTreeQueryDto queryDto) {
		List<CostItemTreeVo> result = new ArrayList<CostItemTreeVo>();
		if(StringUtils.isNotNull(queryDto)) {
			String unitid= queryDto.getUnitid();
			String begintime = queryDto.getBegintime();
			int showLevel = queryDto.getShowLevel()==null?2:queryDto.getShowLevel();
			if(StringUtils.isNotEmpty(unitid)&&StringUtils.isNotEmpty(begintime)) {
				//查询条件
		        MethodQueryDto dto = new MethodQueryDto();
		        dto.setUnitid(unitid);
		        dto.setBegintime(begintime);
		        //核算分类信息
		        List<CostItemTreeVo> rootClassList = new ArrayList<CostItemTreeVo>(); 
		        Map<String, List<CostItemTreeVo>> classMap = new HashMap<String, List<CostItemTreeVo>>();
		        List<CostItemTreeVo> classVoList = new ArrayList<CostItemTreeVo>();
		        List<Costclass> classList = methodService.getCostclassList(dto);
		        if(StringUtils.isNotEmpty(classList)) { //分类节点下有数据
		        	for (int i = 0; i < classList.size(); i++) {
		        		Costclass classObj = classList.get(i);
		        		CostItemTreeVo classVo = new CostItemTreeVo();
	    				BeanUtils.copyProperties(classObj, classVo); // 赋予返回对象
	    				String pid = classObj.getPid();
	    				classVo.setPid(pid);
	    				classVo.setNodeId(classObj.getId());
	    				classVo.setNodeName(classObj.getCcname());
	    				classVo.setNodeLevel(1);
	    				classVo.setIsLeaf(0);
	    				classVo.setDisabled(true);
	    				classVoList.add(classVo);
	    				if(StringUtils.isNotEmpty(pid)&&"root".equals(pid)) {
	    					rootClassList.add(classVo);
	    				}
					}
		        	if(StringUtils.isNotEmpty(classVoList)) {
		        		classMap = classVoList.stream().collect(Collectors.groupingBy(CostItemTreeVo::getPid, Collectors.toList()));
	    			}
		        	//项目数据
		    		Map<String, List<CostItemTreeVo>> itemMap = new HashMap<String, List<CostItemTreeVo>>();
		    		List<Costitem> itemList = methodService.getCostitemList(dto);
		    		if(StringUtils.isNotEmpty(itemList)) {
		    			List<CostItemTreeVo> itemVoList = new ArrayList<CostItemTreeVo>();
		    			for (int i = 0; i < itemList.size(); i++) {
		    				Costitem itemObj = itemList.get(i);
		    				CostItemTreeVo itemVo = new CostItemTreeVo();
		    				BeanUtils.copyProperties(itemObj, itemVo); // 赋予返回对象
		    				itemVo.setPid(itemObj.getPid());
		    				itemVo.setNodeId(itemObj.getId());
		    				itemVo.setNodeName(itemObj.getItemname());
		    				itemVo.setNodeLevel(2);
		    				itemVo.setIsLeaf(0);
		    				itemVo.setDisabled(false);
		    				itemVoList.add(itemVo);
						}
		    			if(StringUtils.isNotEmpty(itemVoList)) {
		    				itemMap = itemVoList.stream().collect(Collectors.groupingBy(CostItemTreeVo::getPid, Collectors.toList()));
		    			}
		    		}
		    		//仪表数据
		    		Map<String, List<CostItemTreeVo>> instrumentMap = new HashMap<String, List<CostItemTreeVo>>();
		    		if(showLevel==3) { //显示仪表
		    			List<Costinstrument> instrumentList = methodService.getCostinstrumentList(dto);
			            if(StringUtils.isNotEmpty(instrumentList)) {
			            	List<CostItemTreeVo> instrumentVoList = new ArrayList<CostItemTreeVo>();
			            	for (int i = 0; i < instrumentList.size(); i++) {
			            		Costinstrument instrumentObj = instrumentList.get(i);
			    				CostItemTreeVo instrumentVo = new CostItemTreeVo();
			    				BeanUtils.copyProperties(instrumentObj, instrumentVo); // 赋予返回对象
			    				instrumentVo.setPid(instrumentObj.getPid());
			    				instrumentVo.setNodeId(instrumentObj.getId());
			    				instrumentVo.setNodeName(instrumentObj.getName());
			    				instrumentVo.setNodeLevel(3);
			    				instrumentVo.setIsLeaf(1);
			    				instrumentVo.setDisabled(false);
			    				instrumentVoList.add(instrumentVo);
							}
			            	if(StringUtils.isNotEmpty(instrumentVoList)) {
			            		instrumentMap = instrumentVoList.stream().collect(Collectors.groupingBy(CostItemTreeVo::getPid, Collectors.toList()));
			    			}
			            }
		    		}
		            //组队树形数据
		    		//核算项目
		            if(StringUtils.isNotEmpty(rootClassList)) {
		            	for (int i = 0; i < rootClassList.size(); i++) {
		            		CostItemTreeVo vo = rootClassList.get(i);
		            		this.setTreeChildData(vo, classMap, itemMap, instrumentMap, showLevel);
		            		result.add(vo);
						}
		            }
		        }
		        
		        //核算指标
		        CostItemTreeVo zbClassVo = new CostItemTreeVo();
		        zbClassVo.setId("indexNodeId");
		        zbClassVo.setPid("root");
		        zbClassVo.setNodeId("indexNodeId");
		        zbClassVo.setNodeName("核算指标");
		        zbClassVo.setNodeLevel(100);
		        zbClassVo.setIsLeaf(0);
		        zbClassVo.setDisabled(true);
		        List<CostItemTreeVo> childIndicatorList = new ArrayList<CostItemTreeVo>();
		        List<Costindicator> indicatorList = methodService.getCostindicatorList(dto);
		        if(StringUtils.isNotEmpty(indicatorList)) {
		        	for (int i = 0; i < indicatorList.size(); i++) {
		        		Costindicator indicatorObj = indicatorList.get(i);
	    				CostItemTreeVo indicatorVo = new CostItemTreeVo();
	    				BeanUtils.copyProperties(indicatorObj, indicatorVo); // 赋予返回对象
	    				indicatorVo.setPid("indexNodeId");
	    				indicatorVo.setNodeId(indicatorObj.getId());
	    				indicatorVo.setNodeName(indicatorObj.getCpname());
	    				indicatorVo.setNodeLevel(101);
	    				indicatorVo.setDisabled(false);
	    				childIndicatorList.add(indicatorVo);
					}
		        }
		        zbClassVo.setChildren(childIndicatorList);
		        result.add(zbClassVo);
			}
		}
		return result;
	}

	/*
	 *	设置树形子节点
	 */
	private void setTreeChildData(CostItemTreeVo vo, Map<String, List<CostItemTreeVo>> classMap,
		Map<String, List<CostItemTreeVo>> itemMap, Map<String, List<CostItemTreeVo>> instrumentMap, int showLevel) {
		if (StringUtils.isNotNull(vo)) {
			String pid = vo.getId();
			List<CostItemTreeVo> childTreeList = new ArrayList<CostItemTreeVo>();
			// 分类
			if (StringUtils.isNotEmpty(classMap) && classMap.containsKey(pid)) {
				List<CostItemTreeVo> childClassList = classMap.get(pid);
				if (StringUtils.isNotEmpty(childClassList)) {
					for (int i = 0; i < childClassList.size(); i++) {
						CostItemTreeVo childClassObj = childClassList.get(i);
						childClassObj.setIsLeaf(0);
						this.setTreeChildData(childClassObj, classMap, itemMap, instrumentMap, showLevel);
						childTreeList.add(childClassObj);
					}
				}
			}
			// 项目
			if (StringUtils.isNotEmpty(itemMap) && itemMap.containsKey(pid)) {
				List<CostItemTreeVo> childItemList = itemMap.get(pid);
				if (StringUtils.isNotEmpty(childItemList)) {
					for (int i = 0; i < childItemList.size(); i++) {
						CostItemTreeVo childItemObj = childItemList.get(i);
						if(showLevel==2) { //项目
							childItemObj.setIsLeaf(1);
						}else if(showLevel==3) { //仪表
							childItemObj.setIsLeaf(0);
						}
						this.setTreeChildData(childItemObj, classMap, itemMap, instrumentMap, showLevel);
						childTreeList.add(childItemObj);
					}
				}
			}
			// 仪表
			if (StringUtils.isNotEmpty(instrumentMap) && instrumentMap.containsKey(pid)) {
				List<CostItemTreeVo> childInstrumentList = instrumentMap.get(pid);
				if (StringUtils.isNotEmpty(childInstrumentList)) {
					for (int i = 0; i < childInstrumentList.size(); i++) {
						CostItemTreeVo childInstrumentObj = childInstrumentList.get(i);
						childInstrumentObj.setIsLeaf(1);
						childTreeList.add(childInstrumentObj);
					}
				}
			}
			if (StringUtils.isNotEmpty(childTreeList)) {
				vo.setChildren(childTreeList);
			}
		}
	}
	
	
}
