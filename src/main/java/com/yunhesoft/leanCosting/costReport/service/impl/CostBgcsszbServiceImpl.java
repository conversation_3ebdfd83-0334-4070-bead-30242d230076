package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbInternalDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszbInternal;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszbInternalUse;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszbTemplate;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostBbCodeNameVo;
import com.yunhesoft.leanCosting.costReport.service.CostBgcsszbService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class CostBgcsszbServiceImpl implements CostBgcsszbService {

	@Autowired
	private EntityService dao;
	
	@Autowired
	private RedisUtil redis;

	/**
	 * 
	 * @param tableName bb00 批次核算 数据查询
	 * @param tableType 表头，标题
	 */

	private LinkedHashMap<String, CostBgcsszb> getCostBgcsszbMap(String tableName, String tableType, String unitid, String formId) {
		LinkedHashMap<String, CostBgcsszb> result = new LinkedHashMap<String, CostBgcsszb>();
		int i = 1;
//		if ("bb00".equals(tableName)) {// 批次核算数据查询
//			if ("表头".equals(tableType)) {
//				CostBgcsszb obj = new CostBgcsszb();
//				obj.setColumnCode("itemName");
//				obj.setColumnshowName("项目名称");
//				obj.setIsTotal(0);
//				obj.setObjalg(0);
//				obj.setObjdis(0);
//				obj.setObjins(null);
//				obj.setSparse(0);
//				obj.setTableName(tableName);
//				obj.setTableType(tableType);
//				obj.setTmsort(i);
//				obj.setTmused(1);
//				obj.setUnitId(unitid);
//				obj.setFormId(formId);
//				obj.setIsTotal(0);
//				result.put("itemName", obj);
//				i = i + 1;
//
//				CostBgcsszb obj1 = new CostBgcsszb();
//				obj1.setColumnCode("instrumentName");
//				obj1.setColumnshowName("仪表位号");
//				obj1.setIsTotal(0);
//				obj1.setObjalg(0);
//				obj1.setObjdis(0);
//				obj1.setObjins(null);
//				obj1.setSparse(0);
//				obj1.setTableName(tableName);
//				obj1.setTableType(tableType);
//				obj1.setTmsort(i);
//				obj1.setTmused(1);
//				obj1.setUnitId(unitid);
//				obj1.setFormId(formId);
//				obj1.setIsTotal(0);
//				result.put("instrumentName", obj1);
//				i = i + 1;
//
//				CostBgcsszb obj2 = new CostBgcsszb();
//				obj2.setColumnCode("itemUnit");
//				obj2.setColumnshowName("计量单位");
//				obj2.setIsTotal(0);
//				obj2.setObjalg(0);
//				obj2.setObjdis(0);
//				obj2.setObjins(null);
//				obj2.setSparse(0);
//				obj2.setTableName(tableName);
//				obj2.setTableType(tableType);
//				obj2.setTmsort(i);
//				obj2.setTmused(1);
//				obj2.setUnitId(unitid);
//				obj2.setFormId(formId);
//				obj2.setIsTotal(0);
//				result.put("itemUnit", obj2);
//				i = i + 1;
//
//				CostBgcsszb obj3 = new CostBgcsszb();
//				obj3.setColumnCode("itemPrice");
//				obj3.setColumnshowName("单价");
//				obj3.setIsTotal(0);
//				obj3.setObjalg(1);
//				obj3.setObjdis(4);
//				obj3.setObjins(null);
//				obj3.setSparse(0);
//				obj3.setTableName(tableName);
//				obj3.setTableType(tableType);
//				obj3.setTmsort(i);
//				obj3.setTmused(1);
//				obj3.setUnitId(unitid);
//				obj3.setFormId(formId);
//				obj3.setIsTotal(0);
//				result.put("itemPrice", obj3);
//				i = i + 1;
//
//				CostBgcsszb obj4 = new CostBgcsszb();
//				obj4.setColumnCode("dwcbde");
//				obj4.setColumnshowName("单位成本定额");
//				obj4.setIsTotal(0);
//				obj4.setObjalg(1);
//				obj4.setObjdis(4);
//				obj4.setObjins(null);
//				obj4.setSparse(0);
//				obj4.setTableName(tableName);
//				obj4.setTableType(tableType);
//				obj4.setTmsort(i);
//				obj4.setTmused(1);
//				obj4.setUnitId(unitid);
//				obj4.setFormId(formId);
//				obj4.setIsTotal(0);
//				result.put("dwcbde", obj4);
//				i = i + 1;
//
//				CostBgcsszb obj5 = new CostBgcsszb();
//				obj5.setColumnCode("dhde");
//				obj5.setColumnshowName("单耗定额");
//				obj5.setIsTotal(0);
//				obj5.setObjalg(1);
//				obj5.setObjdis(4);
//				obj5.setObjins(null);
//				obj5.setSparse(0);
//				obj5.setTableName(tableName);
//				obj5.setTableType(tableType);
//				obj5.setTmsort(i);
//				obj5.setTmused(1);
//				obj5.setUnitId(unitid);
//				obj5.setFormId(formId);
//				obj5.setIsTotal(0);
//				result.put("dhde", obj5);
//				i = i + 1;
//
//				CostBgcsszb obj6 = new CostBgcsszb();
//				obj6.setColumnCode("previousReadOut");
//				obj6.setColumnshowName("前表数");
//				obj6.setIsTotal(0);
//				obj6.setObjalg(1);
//				obj6.setObjdis(4);
//				obj6.setObjins(null);
//				obj6.setSparse(0);
//				obj6.setTableName(tableName);
//				obj6.setTableType(tableType);
//				obj6.setTmsort(i);
//				obj6.setTmused(1);
//				obj6.setUnitId(unitid);
//				obj6.setFormId(formId);
//				obj6.setIsTotal(0);
//				result.put("previousReadOut", obj6);
//				i = i + 1;
//
//				CostBgcsszb obj7 = new CostBgcsszb();
//				obj7.setColumnCode("lastReadOut");
//				obj7.setColumnshowName("后表数");
//				obj7.setIsTotal(0);
//				obj7.setObjalg(1);
//				obj7.setObjdis(4);
//				obj7.setObjins(null);
//				obj7.setSparse(0);
//				obj7.setTableName(tableName);
//				obj7.setTableType(tableType);
//				obj7.setTmsort(i);
//				obj7.setTmused(1);
//				obj7.setUnitId(unitid);
//				obj7.setFormId(formId);
//				obj7.setIsTotal(0);
//				result.put("lastReadOut", obj7);
//				i = i + 1;
//
//				CostBgcsszb obj8 = new CostBgcsszb();
//				obj8.setColumnCode("dbxh");
//				obj8.setColumnshowName("单表消耗");
//				obj8.setIsTotal(0);
//				obj8.setObjalg(1);
//				obj8.setObjdis(4);
//				obj8.setObjins(null);
//				obj8.setSparse(0);
//				obj8.setTableName(tableName);
//				obj8.setTableType(tableType);
//				obj8.setTmsort(i);
//				obj8.setTmused(1);
//				obj8.setUnitId(unitid);
//				obj8.setFormId(formId);
//				obj8.setIsTotal(0);
//				result.put("dbxh", obj8);
//				i = i + 1;
//
//				CostBgcsszb obj9 = new CostBgcsszb();
//				obj9.setColumnCode("xhl");
//				obj9.setColumnshowName("消耗量");
//				obj9.setIsTotal(0);
//				obj9.setObjalg(1);
//				obj9.setObjdis(4);
//				obj9.setObjins(null);
//				obj9.setSparse(0);
//				obj9.setTableName(tableName);
//				obj9.setTableType(tableType);
//				obj9.setTmsort(i);
//				obj9.setTmused(1);
//				obj9.setUnitId(unitid);
//				obj9.setFormId(formId);
//				obj9.setIsTotal(0);
//				result.put("xhl", obj9);
//				i = i + 1;
//
//				CostBgcsszb obj10 = new CostBgcsszb();
//				obj10.setColumnCode("dh");
//				obj10.setColumnshowName("单耗");
//				obj10.setIsTotal(0);
//				obj10.setObjalg(1);
//				obj10.setObjdis(4);
//				obj10.setObjins(null);
//				obj10.setSparse(0);
//				obj10.setTableName(tableName);
//				obj10.setTableType(tableType);
//				obj10.setTmsort(i);
//				obj10.setTmused(1);
//				obj10.setUnitId(unitid);
//				obj10.setFormId(formId);
//				obj10.setIsTotal(0);
//				result.put("dh", obj10);
//				i = i + 1;
//
//				CostBgcsszb obj11 = new CostBgcsszb();
//				obj11.setColumnCode("dwcb");
//				obj11.setColumnshowName("单位成本");
//				obj11.setIsTotal(0);
//				obj11.setObjalg(1);
//				obj11.setObjdis(4);
//				obj11.setObjins(null);
//				obj11.setSparse(0);
//				obj11.setTableName(tableName);
//				obj11.setTableType(tableType);
//				obj11.setTmsort(i);
//				obj11.setTmused(1);
//				obj11.setUnitId(unitid);
//				obj11.setFormId(formId);
//				obj11.setIsTotal(0);
//				result.put("dwcb", obj11);
//				i = i + 1;
//
//				CostBgcsszb obj12 = new CostBgcsszb();
//				obj12.setColumnCode("zcb");
//				obj12.setColumnshowName("总成本");
//				obj12.setIsTotal(0);
//				obj12.setObjalg(1);
//				obj12.setObjdis(4);
//				obj12.setObjins(null);
//				obj12.setSparse(0);
//				obj12.setTableName(tableName);
//				obj12.setTableType(tableType);
//				obj12.setTmsort(i);
//				obj12.setTmused(1);
//				obj12.setUnitId(unitid);
//				obj12.setFormId(formId);
//				obj12.setIsTotal(0);
//				result.put("zcb", obj12);
//				i = i + 1;
//
//				CostBgcsszb obj13 = new CostBgcsszb();
//				obj13.setColumnCode("dhcz");
//				obj13.setColumnshowName("单耗差值");
//				obj13.setIsTotal(0);
//				obj13.setObjalg(1);
//				obj13.setObjdis(4);
//				obj13.setObjins(null);
//				obj13.setSparse(0);
//				obj13.setTableName(tableName);
//				obj13.setTableType(tableType);
//				obj13.setTmsort(i);
//				obj13.setTmused(1);
//				obj13.setUnitId(unitid);
//				obj13.setFormId(formId);
//				obj13.setIsTotal(0);
//				result.put("dhcz", obj13);
//				i = i + 1;
//
//				CostBgcsszb obj14 = new CostBgcsszb();
//				obj14.setColumnCode("dwcbcz");
//				obj14.setColumnshowName("单位成本差值");
//				obj14.setIsTotal(0);
//				obj14.setObjalg(1);
//				obj14.setObjdis(4);
//				obj14.setObjins(null);
//				obj14.setSparse(0);
//				obj14.setTableName(tableName);
//				obj14.setTableType(tableType);
//				obj14.setTmsort(i);
//				obj14.setTmused(1);
//				obj14.setUnitId(unitid);
//				obj14.setFormId(formId);
//				obj14.setIsTotal(0);
//				result.put("dwcbcz", obj14);
//				i = i + 1;
//			} else if ("标题".equals(tableType)) {
//				CostBgcsszb obj = new CostBgcsszb();
//				obj.setColumnCode("bbTitle");
//				obj.setColumnshowName("批次核算数据查询");
//				obj.setIsTotal(0);
//				obj.setObjalg(0);
//				obj.setObjdis(0);
//				obj.setObjins(null);
//				obj.setSparse(0);
//				obj.setTableName(tableName);
//				obj.setTableType(tableType);
//				obj.setTmsort(1);
//				obj.setTmused(1);
//				obj.setUnitId(unitid);
//				obj.setFormId(formId);
//				result.put("bbTitle", obj);
//			}
//		} else 
		if ("bb01".equals(tableName)) {// 班组经济核算投入产出利润表
			if ("表头".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("itemName");
				obj.setColumnshowName("项目名称");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(i);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				obj.setIsTotal(0);
				result.put("itemName", obj);
				i = i + 1;

				CostBgcsszb obj1 = new CostBgcsszb();
				obj1.setColumnCode("instrumentName");
				obj1.setColumnshowName("仪表名称");
				obj1.setIsTotal(0);
				obj1.setObjalg(0);
				obj1.setObjdis(0);
				obj1.setObjins(null);
				obj1.setSparse(0);
				obj1.setTableName(tableName);
				obj1.setTableType(tableType);
				obj1.setTmsort(i);
				obj1.setTmused(1);
				obj1.setUnitId(unitid);
				obj1.setFormId(formId);
				obj1.setIsTotal(0);
				result.put("instrumentName", obj1);
				i = i + 1;

				CostBgcsszb obj2 = new CostBgcsszb();
				obj2.setColumnCode("itemUnit");
				obj2.setColumnshowName("计量单位");
				obj2.setIsTotal(0);
				obj2.setObjalg(0);
				obj2.setObjdis(0);
				obj2.setObjins(null);
				obj2.setSparse(0);
				obj2.setTableName(tableName);
				obj2.setTableType(tableType);
				obj2.setTmsort(i);
				obj2.setTmused(1);
				obj2.setUnitId(unitid);
				obj2.setFormId(formId);
				obj2.setIsTotal(0);
				result.put("itemUnit", obj2);
				i = i + 1;

				CostBgcsszb obj3 = new CostBgcsszb();
				obj3.setColumnCode("itemPrice");
				obj3.setColumnshowName("单价");
				obj3.setIsTotal(0);
				obj3.setObjalg(1);
				obj3.setObjdis(4);
				obj3.setObjins(null);
				obj3.setSparse(0);
				obj3.setTableName(tableName);
				obj3.setTableType(tableType);
				obj3.setTmsort(i);
				obj3.setTmused(1);
				obj3.setUnitId(unitid);
				obj3.setFormId(formId);
				obj3.setIsTotal(0);
				result.put("itemPrice", obj3);
				i = i + 1;

				CostBgcsszb obj4 = new CostBgcsszb();
				obj4.setColumnCode("dwcbde");
				obj4.setColumnshowName("单位成本定额");
				obj4.setIsTotal(0);
				obj4.setObjalg(1);
				obj4.setObjdis(4);
				obj4.setObjins(null);
				obj4.setSparse(0);
				obj4.setTableName(tableName);
				obj4.setTableType(tableType);
				obj4.setTmsort(i);
				obj4.setTmused(1);
				obj4.setUnitId(unitid);
				obj4.setFormId(formId);
				obj4.setIsTotal(0);
				result.put("dwcbde", obj4);
				i = i + 1;

				CostBgcsszb obj5 = new CostBgcsszb();
				obj5.setColumnCode("dhde");
				obj5.setColumnshowName("单耗定额");
				obj5.setIsTotal(0);
				obj5.setObjalg(1);
				obj5.setObjdis(4);
				obj5.setObjins(null);
				obj5.setSparse(0);
				obj5.setTableName(tableName);
				obj5.setTableType(tableType);
				obj5.setTmsort(i);
				obj5.setTmused(1);
				obj5.setUnitId(unitid);
				obj5.setFormId(formId);
				obj5.setIsTotal(0);
				result.put("dhde", obj5);
				i = i + 1;

				CostBgcsszb obj6 = new CostBgcsszb();
				obj6.setColumnCode("previousReadOut");
				obj6.setColumnshowName("前表数");
				obj6.setIsTotal(0);
				obj6.setObjalg(1);
				obj6.setObjdis(4);
				obj6.setObjins(null);
				obj6.setSparse(0);
				obj6.setTableName(tableName);
				obj6.setTableType(tableType);
				obj6.setTmsort(i);
				obj6.setTmused(1);
				obj6.setUnitId(unitid);
				obj6.setFormId(formId);
				obj6.setIsTotal(0);
				result.put("previousReadOut", obj6);
				i = i + 1;

				CostBgcsszb obj7 = new CostBgcsszb();
				obj7.setColumnCode("lastReadOut");
				obj7.setColumnshowName("后表数");
				obj7.setIsTotal(0);
				obj7.setObjalg(1);
				obj7.setObjdis(4);
				obj7.setObjins(null);
				obj7.setSparse(0);
				obj7.setTableName(tableName);
				obj7.setTableType(tableType);
				obj7.setTmsort(i);
				obj7.setTmused(1);
				obj7.setUnitId(unitid);
				obj7.setFormId(formId);
				obj7.setIsTotal(0);
				result.put("lastReadOut", obj7);
				i = i + 1;

				CostBgcsszb obj8 = new CostBgcsszb();
				obj8.setColumnCode("dbxh");
				obj8.setColumnshowName("单表消耗");
				obj8.setIsTotal(0);
				obj8.setObjalg(1);
				obj8.setObjdis(4);
				obj8.setObjins(null);
				obj8.setSparse(0);
				obj8.setTableName(tableName);
				obj8.setTableType(tableType);
				obj8.setTmsort(i);
				obj8.setTmused(1);
				obj8.setUnitId(unitid);
				obj8.setFormId(formId);
				obj8.setIsTotal(0);
				result.put("dbxh", obj8);
				i = i + 1;

				CostBgcsszb obj9 = new CostBgcsszb();
				obj9.setColumnCode("xhl");
				obj9.setColumnshowName("消耗量");
				obj9.setIsTotal(0);
				obj9.setObjalg(1);
				obj9.setObjdis(4);
				obj9.setObjins(null);
				obj9.setSparse(0);
				obj9.setTableName(tableName);
				obj9.setTableType(tableType);
				obj9.setTmsort(i);
				obj9.setTmused(1);
				obj9.setUnitId(unitid);
				obj9.setFormId(formId);
				obj9.setIsTotal(0);
				result.put("xhl", obj9);
				i = i + 1;

				CostBgcsszb obj10 = new CostBgcsszb();
				obj10.setColumnCode("dh");
				obj10.setColumnshowName("单耗");
				obj10.setIsTotal(0);
				obj10.setObjalg(1);
				obj10.setObjdis(4);
				obj10.setObjins(null);
				obj10.setSparse(0);
				obj10.setTableName(tableName);
				obj10.setTableType(tableType);
				obj10.setTmsort(i);
				obj10.setTmused(1);
				obj10.setUnitId(unitid);
				obj10.setFormId(formId);
				obj10.setIsTotal(0);
				result.put("dh", obj10);
				i = i + 1;

				CostBgcsszb obj11 = new CostBgcsszb();
				obj11.setColumnCode("dwcb");
				obj11.setColumnshowName("单位成本");
				obj11.setIsTotal(0);
				obj11.setObjalg(1);
				obj11.setObjdis(4);
				obj11.setObjins(null);
				obj11.setSparse(0);
				obj11.setTableName(tableName);
				obj11.setTableType(tableType);
				obj11.setTmsort(i);
				obj11.setTmused(1);
				obj11.setUnitId(unitid);
				obj11.setFormId(formId);
				obj11.setIsTotal(0);
				result.put("dwcb", obj11);
				i = i + 1;

				CostBgcsszb obj12 = new CostBgcsszb();
				obj12.setColumnCode("zcb");
				obj12.setColumnshowName("总成本");
				obj12.setIsTotal(0);
				obj12.setObjalg(1);
				obj12.setObjdis(4);
				obj12.setObjins(null);
				obj12.setSparse(0);
				obj12.setTableName(tableName);
				obj12.setTableType(tableType);
				obj12.setTmsort(i);
				obj12.setTmused(1);
				obj12.setUnitId(unitid);
				obj12.setFormId(formId);
				obj12.setIsTotal(0);
				result.put("zcb", obj12);
				i = i + 1;

				CostBgcsszb obj13 = new CostBgcsszb();
				obj13.setColumnCode("dhcz");
				obj13.setColumnshowName("单耗差值");
				obj13.setIsTotal(0);
				obj13.setObjalg(1);
				obj13.setObjdis(4);
				obj13.setObjins(null);
				obj13.setSparse(0);
				obj13.setTableName(tableName);
				obj13.setTableType(tableType);
				obj13.setTmsort(i);
				obj13.setTmused(1);
				obj13.setUnitId(unitid);
				obj13.setFormId(formId);
				obj13.setIsTotal(0);
				result.put("dhcz", obj13);
				i = i + 1;

				CostBgcsszb obj14 = new CostBgcsszb();
				obj14.setColumnCode("dwcbcz");
				obj14.setColumnshowName("单位成本差值");
				obj14.setIsTotal(0);
				obj14.setObjalg(1);
				obj14.setObjdis(4);
				obj14.setObjins(null);
				obj14.setSparse(0);
				obj14.setTableName(tableName);
				obj14.setTableType(tableType);
				obj14.setTmsort(i);
				obj14.setTmused(1);
				obj14.setUnitId(unitid);
				obj14.setFormId(formId);
				obj14.setIsTotal(0);
				result.put("dwcbcz", obj14);
				i = i + 1;
				
				CostBgcsszb obj15 = new CostBgcsszb();
				obj15.setColumnCode("nh");
				obj15.setColumnshowName("能耗");
				obj15.setIsTotal(0);
				obj15.setObjalg(1);
				obj15.setObjdis(4);
				obj15.setObjins(null);
				obj15.setSparse(0);
				obj15.setTableName(tableName);
				obj15.setTableType(tableType);
				obj15.setTmsort(i);
				obj15.setTmused(1);
				obj15.setUnitId(unitid);
				obj15.setFormId(formId);
				obj15.setIsTotal(0);
				result.put("nh", obj15);
				i = i + 1;
				
			} else if ("标题".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("bbTitle");
				obj.setColumnshowName("批次核算数据查询");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(1);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				result.put("bbTitle", obj);
			}
		} else if ("bb04".equals(tableName)) {// 装置经济核算投入产出利润表(日)
			if ("表头".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("itemName");
				obj.setColumnshowName("项目名称");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(i);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				obj.setIsTotal(0);
				result.put("itemName", obj);
				i = i + 1;

				CostBgcsszb obj1 = new CostBgcsszb();
				obj1.setColumnCode("itemUnit");
				obj1.setColumnshowName("计量单位");
				obj1.setIsTotal(0);
				obj1.setObjalg(0);
				obj1.setObjdis(0);
				obj1.setObjins(null);
				obj1.setSparse(0);
				obj1.setTableName(tableName);
				obj1.setTableType(tableType);
				obj1.setTmsort(i);
				obj1.setTmused(1);
				obj1.setUnitId(unitid);
				obj1.setFormId(formId);
				obj1.setIsTotal(0);
				result.put("itemUnit", obj1);
				i = i + 1;

				CostBgcsszb obj2 = new CostBgcsszb();
				obj2.setColumnCode("itemPrice");
				obj2.setColumnshowName("单价");
				obj2.setIsTotal(0);
				obj2.setObjalg(1);
				obj2.setObjdis(4);
				obj2.setObjins(null);
				obj2.setSparse(0);
				obj2.setTableName(tableName);
				obj2.setTableType(tableType);
				obj2.setTmsort(i);
				obj2.setTmused(1);
				obj2.setUnitId(unitid);
				obj2.setFormId(formId);
				obj2.setIsTotal(0);
				result.put("itemPrice", obj2);
				i = i + 1;

				CostBgcsszb obj3 = new CostBgcsszb();
				obj3.setColumnCode("dwcbde");
				obj3.setColumnshowName("单位成本定额");
				obj3.setIsTotal(0);
				obj3.setObjalg(1);
				obj3.setObjdis(4);
				obj3.setObjins(null);
				obj3.setSparse(0);
				obj3.setTableName(tableName);
				obj3.setTableType(tableType);
				obj3.setTmsort(i);
				obj3.setTmused(1);
				obj3.setUnitId(unitid);
				obj3.setFormId(formId);
				obj3.setIsTotal(0);
				result.put("dwcbde", obj3);
				i = i + 1;

				CostBgcsszb obj4 = new CostBgcsszb();
				obj4.setColumnCode("dhde");
				obj4.setColumnshowName("单耗定额");
				obj4.setIsTotal(0);
				obj4.setObjalg(1);
				obj4.setObjdis(4);
				obj4.setObjins(null);
				obj4.setSparse(0);
				obj4.setTableName(tableName);
				obj4.setTableType(tableType);
				obj4.setTmsort(i);
				obj4.setTmused(1);
				obj4.setUnitId(unitid);
				obj4.setFormId(formId);
				obj4.setIsTotal(0);
				result.put("dhde", obj4);
				i = i + 1;

				CostBgcsszb obj5 = new CostBgcsszb();
				obj5.setColumnCode("xhl");
				obj5.setColumnshowName("消耗量");
				obj5.setIsTotal(0);
				obj5.setObjalg(1);
				obj5.setObjdis(4);
				obj5.setObjins(null);
				obj5.setSparse(0);
				obj5.setTableName(tableName);
				obj5.setTableType(tableType);
				obj5.setTmsort(i);
				obj5.setTmused(1);
				obj5.setUnitId(unitid);
				obj5.setFormId(formId);
				obj5.setIsTotal(0);
				result.put("xhl", obj5);
				i = i + 1;

				CostBgcsszb obj6 = new CostBgcsszb();
				obj6.setColumnCode("dh");
				obj6.setColumnshowName("单耗");
				obj6.setIsTotal(0);
				obj6.setObjalg(1);
				obj6.setObjdis(4);
				obj6.setObjins(null);
				obj6.setSparse(0);
				obj6.setTableName(tableName);
				obj6.setTableType(tableType);
				obj6.setTmsort(i);
				obj6.setTmused(1);
				obj6.setUnitId(unitid);
				obj6.setFormId(formId);
				obj6.setIsTotal(0);
				result.put("dh", obj6);
				i = i + 1;

				CostBgcsszb obj7 = new CostBgcsszb();
				obj7.setColumnCode("dwcb");
				obj7.setColumnshowName("单位成本");
				obj7.setIsTotal(0);
				obj7.setObjalg(1);
				obj7.setObjdis(4);
				obj7.setObjins(null);
				obj7.setSparse(0);
				obj7.setTableName(tableName);
				obj7.setTableType(tableType);
				obj7.setTmsort(i);
				obj7.setTmused(1);
				obj7.setUnitId(unitid);
				obj7.setFormId(formId);
				obj7.setIsTotal(0);
				result.put("dwcb", obj7);
				i = i + 1;

				CostBgcsszb obj8 = new CostBgcsszb();
				obj8.setColumnCode("zcb");
				obj8.setColumnshowName("总成本");
				obj8.setIsTotal(0);
				obj8.setObjalg(1);
				obj8.setObjdis(4);
				obj8.setObjins(null);
				obj8.setSparse(0);
				obj8.setTableName(tableName);
				obj8.setTableType(tableType);
				obj8.setTmsort(i);
				obj8.setTmused(1);
				obj8.setUnitId(unitid);
				obj8.setFormId(formId);
				obj8.setIsTotal(0);
				result.put("zcb", obj8);
				i = i + 1;

				CostBgcsszb obj9 = new CostBgcsszb();
				obj9.setColumnCode("dhcz");
				obj9.setColumnshowName("单耗差值");
				obj9.setIsTotal(0);
				obj9.setObjalg(1);
				obj9.setObjdis(4);
				obj9.setObjins(null);
				obj9.setSparse(0);
				obj9.setTableName(tableName);
				obj9.setTableType(tableType);
				obj9.setTmsort(i);
				obj9.setTmused(1);
				obj9.setUnitId(unitid);
				obj9.setFormId(formId);
				obj9.setIsTotal(0);
				result.put("dhcz", obj9);
				i = i + 1;

				CostBgcsszb obj10 = new CostBgcsszb();
				obj10.setColumnCode("dwcbcz");
				obj10.setColumnshowName("单位成本差值");
				obj10.setIsTotal(0);
				obj10.setObjalg(1);
				obj10.setObjdis(4);
				obj10.setObjins(null);
				obj10.setSparse(0);
				obj10.setTableName(tableName);
				obj10.setTableType(tableType);
				obj10.setTmsort(i);
				obj10.setTmused(1);
				obj10.setUnitId(unitid);
				obj10.setFormId(formId);
				obj10.setIsTotal(0);
				result.put("dwcbcz", obj10);
				i = i + 1;
				
				CostBgcsszb obj11 = new CostBgcsszb();
				obj11.setColumnCode("nh");
				obj11.setColumnshowName("能耗");
				obj11.setIsTotal(0);
				obj11.setObjalg(1);
				obj11.setObjdis(4);
				obj11.setObjins(null);
				obj11.setSparse(0);
				obj11.setTableName(tableName);
				obj11.setTableType(tableType);
				obj11.setTmsort(i);
				obj11.setTmused(1);
				obj11.setUnitId(unitid);
				obj11.setFormId(formId);
				obj11.setIsTotal(0);
				result.put("nh", obj11);
				i = i + 1;
				
			} else if ("标题".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("bbTitle");
				obj.setColumnshowName("装置经济核算投入产出利润表(日)");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(1);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				result.put("bbTitle", obj);
			}
		} else if ("bb07".equals(tableName)) {// 装置经济核算投入产出利润汇总表(月)
			if ("表头".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("itemName");
				obj.setColumnshowName("项目名称");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(i);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				obj.setIsTotal(0);
				result.put("itemName", obj);
				i = i + 1;

				CostBgcsszb obj1 = new CostBgcsszb();
				obj1.setColumnCode("itemUnit");
				obj1.setColumnshowName("计量单位");
				obj1.setIsTotal(0);
				obj1.setObjalg(0);
				obj1.setObjdis(0);
				obj1.setObjins(null);
				obj1.setSparse(0);
				obj1.setTableName(tableName);
				obj1.setTableType(tableType);
				obj1.setTmsort(i);
				obj1.setTmused(1);
				obj1.setUnitId(unitid);
				obj1.setFormId(formId);
				obj1.setIsTotal(0);
				result.put("itemUnit", obj1);
				i = i + 1;

				CostBgcsszb obj2 = new CostBgcsszb();
				obj2.setColumnCode("itemPrice");
				obj2.setColumnshowName("单价");
				obj2.setIsTotal(0);
				obj2.setObjalg(1);
				obj2.setObjdis(4);
				obj2.setObjins(null);
				obj2.setSparse(0);
				obj2.setTableName(tableName);
				obj2.setTableType(tableType);
				obj2.setTmsort(i);
				obj2.setTmused(1);
				obj2.setUnitId(unitid);
				obj2.setFormId(formId);
				obj2.setIsTotal(0);
				result.put("itemPrice", obj2);
				i = i + 1;

				CostBgcsszb obj3 = new CostBgcsszb();
				obj3.setColumnCode("dwcbde");
				obj3.setColumnshowName("单位成本定额");
				obj3.setIsTotal(0);
				obj3.setObjalg(1);
				obj3.setObjdis(4);
				obj3.setObjins(null);
				obj3.setSparse(0);
				obj3.setTableName(tableName);
				obj3.setTableType(tableType);
				obj3.setTmsort(i);
				obj3.setTmused(1);
				obj3.setUnitId(unitid);
				obj3.setFormId(formId);
				obj3.setIsTotal(0);
				result.put("dwcbde", obj3);
				i = i + 1;

				CostBgcsszb obj4 = new CostBgcsszb();
				obj4.setColumnCode("dhde");
				obj4.setColumnshowName("单耗定额");
				obj4.setIsTotal(0);
				obj4.setObjalg(1);
				obj4.setObjdis(4);
				obj4.setObjins(null);
				obj4.setSparse(0);
				obj4.setTableName(tableName);
				obj4.setTableType(tableType);
				obj4.setTmsort(i);
				obj4.setTmused(1);
				obj4.setUnitId(unitid);
				obj4.setFormId(formId);
				obj4.setIsTotal(0);
				result.put("dhde", obj4);
				i = i + 1;

				CostBgcsszb obj5 = new CostBgcsszb();
				obj5.setColumnCode("xhl");
				obj5.setColumnshowName("消耗量");
				obj5.setIsTotal(0);
				obj5.setObjalg(1);
				obj5.setObjdis(4);
				obj5.setObjins(null);
				obj5.setSparse(0);
				obj5.setTableName(tableName);
				obj5.setTableType(tableType);
				obj5.setTmsort(i);
				obj5.setTmused(1);
				obj5.setUnitId(unitid);
				obj5.setFormId(formId);
				obj5.setIsTotal(0);
				result.put("xhl", obj5);
				i = i + 1;

				CostBgcsszb obj6 = new CostBgcsszb();
				obj6.setColumnCode("dh");
				obj6.setColumnshowName("单耗");
				obj6.setIsTotal(0);
				obj6.setObjalg(1);
				obj6.setObjdis(4);
				obj6.setObjins(null);
				obj6.setSparse(0);
				obj6.setTableName(tableName);
				obj6.setTableType(tableType);
				obj6.setTmsort(i);
				obj6.setTmused(1);
				obj6.setUnitId(unitid);
				obj6.setFormId(formId);
				obj6.setIsTotal(0);
				result.put("dh", obj6);
				i = i + 1;

				CostBgcsszb obj7 = new CostBgcsszb();
				obj7.setColumnCode("dwcb");
				obj7.setColumnshowName("单位成本");
				obj7.setIsTotal(0);
				obj7.setObjalg(1);
				obj7.setObjdis(4);
				obj7.setObjins(null);
				obj7.setSparse(0);
				obj7.setTableName(tableName);
				obj7.setTableType(tableType);
				obj7.setTmsort(i);
				obj7.setTmused(1);
				obj7.setUnitId(unitid);
				obj7.setFormId(formId);
				obj7.setIsTotal(0);
				result.put("dwcb", obj7);
				i = i + 1;

				CostBgcsszb obj8 = new CostBgcsszb();
				obj8.setColumnCode("zcb");
				obj8.setColumnshowName("总成本");
				obj8.setIsTotal(0);
				obj8.setObjalg(1);
				obj8.setObjdis(4);
				obj8.setObjins(null);
				obj8.setSparse(0);
				obj8.setTableName(tableName);
				obj8.setTableType(tableType);
				obj8.setTmsort(i);
				obj8.setTmused(1);
				obj8.setUnitId(unitid);
				obj8.setFormId(formId);
				obj8.setIsTotal(0);
				result.put("zcb", obj8);
				i = i + 1;

				CostBgcsszb obj9 = new CostBgcsszb();
				obj9.setColumnCode("dhcz");
				obj9.setColumnshowName("单耗差值");
				obj9.setIsTotal(0);
				obj9.setObjalg(1);
				obj9.setObjdis(4);
				obj9.setObjins(null);
				obj9.setSparse(0);
				obj9.setTableName(tableName);
				obj9.setTableType(tableType);
				obj9.setTmsort(i);
				obj9.setTmused(1);
				obj9.setUnitId(unitid);
				obj9.setFormId(formId);
				obj9.setIsTotal(0);
				result.put("dhcz", obj9);
				i = i + 1;

				CostBgcsszb obj10 = new CostBgcsszb();
				obj10.setColumnCode("dwcbcz");
				obj10.setColumnshowName("单位成本差值");
				obj10.setIsTotal(0);
				obj10.setObjalg(1);
				obj10.setObjdis(4);
				obj10.setObjins(null);
				obj10.setSparse(0);
				obj10.setTableName(tableName);
				obj10.setTableType(tableType);
				obj10.setTmsort(i);
				obj10.setTmused(1);
				obj10.setUnitId(unitid);
				obj10.setFormId(formId);
				obj10.setIsTotal(0);
				result.put("dwcbcz", obj10);
				i = i + 1;
				
				CostBgcsszb obj11 = new CostBgcsszb();
				obj11.setColumnCode("nh");
				obj11.setColumnshowName("能耗");
				obj11.setIsTotal(0);
				obj11.setObjalg(1);
				obj11.setObjdis(4);
				obj11.setObjins(null);
				obj11.setSparse(0);
				obj11.setTableName(tableName);
				obj11.setTableType(tableType);
				obj11.setTmsort(i);
				obj11.setTmused(1);
				obj11.setUnitId(unitid);
				obj11.setFormId(formId);
				obj11.setIsTotal(0);
				result.put("nh", obj11);
				i = i + 1;
				
			} else if ("标题".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("bbTitle");
				obj.setColumnshowName("装置经济核算投入产出利润汇总表(月)");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(1);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				result.put("bbTitle", obj);
			}
		} else if ("bb08".equals(tableName)) {// 经济核算投入产出利润汇总表
			if ("表头".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("itemName");
				obj.setColumnshowName("项目名称");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(i);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				obj.setIsTotal(0);
				result.put("itemName", obj);
				i = i + 1;

				CostBgcsszb obj1 = new CostBgcsszb();
				obj1.setColumnCode("itemUnit");
				obj1.setColumnshowName("计量单位");
				obj1.setIsTotal(0);
				obj1.setObjalg(0);
				obj1.setObjdis(0);
				obj1.setObjins(null);
				obj1.setSparse(0);
				obj1.setTableName(tableName);
				obj1.setTableType(tableType);
				obj1.setTmsort(i);
				obj1.setTmused(1);
				obj1.setUnitId(unitid);
				obj1.setFormId(formId);
				obj1.setIsTotal(0);
				result.put("itemUnit", obj1);
				i = i + 1;

				CostBgcsszb obj2 = new CostBgcsszb();
				obj2.setColumnCode("itemPrice");
				obj2.setColumnshowName("单价");
				obj2.setIsTotal(0);
				obj2.setObjalg(1);
				obj2.setObjdis(4);
				obj2.setObjins(null);
				obj2.setSparse(0);
				obj2.setTableName(tableName);
				obj2.setTableType(tableType);
				obj2.setTmsort(i);
				obj2.setTmused(1);
				obj2.setUnitId(unitid);
				obj2.setFormId(formId);
				obj2.setIsTotal(0);
				result.put("itemPrice", obj2);
				i = i + 1;

				CostBgcsszb obj3 = new CostBgcsszb();
				obj3.setColumnCode("dwcbde");
				obj3.setColumnshowName("单位成本定额");
				obj3.setIsTotal(0);
				obj3.setObjalg(1);
				obj3.setObjdis(4);
				obj3.setObjins(null);
				obj3.setSparse(0);
				obj3.setTableName(tableName);
				obj3.setTableType(tableType);
				obj3.setTmsort(i);
				obj3.setTmused(1);
				obj3.setUnitId(unitid);
				obj3.setFormId(formId);
				obj3.setIsTotal(0);
				result.put("dwcbde", obj3);
				i = i + 1;

				CostBgcsszb obj4 = new CostBgcsszb();
				obj4.setColumnCode("dhde");
				obj4.setColumnshowName("单耗定额");
				obj4.setIsTotal(0);
				obj4.setObjalg(1);
				obj4.setObjdis(4);
				obj4.setObjins(null);
				obj4.setSparse(0);
				obj4.setTableName(tableName);
				obj4.setTableType(tableType);
				obj4.setTmsort(i);
				obj4.setTmused(1);
				obj4.setUnitId(unitid);
				obj4.setFormId(formId);
				obj4.setIsTotal(0);
				result.put("dhde", obj4);
				i = i + 1;

				CostBgcsszb obj5 = new CostBgcsszb();
				obj5.setColumnCode("xhl");
				obj5.setColumnshowName("消耗量");
				obj5.setIsTotal(0);
				obj5.setObjalg(1);
				obj5.setObjdis(4);
				obj5.setObjins(null);
				obj5.setSparse(0);
				obj5.setTableName(tableName);
				obj5.setTableType(tableType);
				obj5.setTmsort(i);
				obj5.setTmused(1);
				obj5.setUnitId(unitid);
				obj5.setFormId(formId);
				obj5.setIsTotal(0);
				result.put("xhl", obj5);
				i = i + 1;

				CostBgcsszb obj6 = new CostBgcsszb();
				obj6.setColumnCode("dh");
				obj6.setColumnshowName("单耗");
				obj6.setIsTotal(0);
				obj6.setObjalg(1);
				obj6.setObjdis(4);
				obj6.setObjins(null);
				obj6.setSparse(0);
				obj6.setTableName(tableName);
				obj6.setTableType(tableType);
				obj6.setTmsort(i);
				obj6.setTmused(1);
				obj6.setUnitId(unitid);
				obj6.setFormId(formId);
				obj6.setIsTotal(0);
				result.put("dh", obj6);
				i = i + 1;

				CostBgcsszb obj7 = new CostBgcsszb();
				obj7.setColumnCode("dwcb");
				obj7.setColumnshowName("单位成本");
				obj7.setIsTotal(0);
				obj7.setObjalg(1);
				obj7.setObjdis(4);
				obj7.setObjins(null);
				obj7.setSparse(0);
				obj7.setTableName(tableName);
				obj7.setTableType(tableType);
				obj7.setTmsort(i);
				obj7.setTmused(1);
				obj7.setUnitId(unitid);
				obj7.setFormId(formId);
				obj7.setIsTotal(0);
				result.put("dwcb", obj7);
				i = i + 1;

				CostBgcsszb obj8 = new CostBgcsszb();
				obj8.setColumnCode("zcb");
				obj8.setColumnshowName("总成本");
				obj8.setIsTotal(0);
				obj8.setObjalg(1);
				obj8.setObjdis(4);
				obj8.setObjins(null);
				obj8.setSparse(0);
				obj8.setTableName(tableName);
				obj8.setTableType(tableType);
				obj8.setTmsort(i);
				obj8.setTmused(1);
				obj8.setUnitId(unitid);
				obj8.setFormId(formId);
				obj8.setIsTotal(0);
				result.put("zcb", obj8);
				i = i + 1;

				CostBgcsszb obj9 = new CostBgcsszb();
				obj9.setColumnCode("dhcz");
				obj9.setColumnshowName("单耗差值");
				obj9.setIsTotal(0);
				obj9.setObjalg(1);
				obj9.setObjdis(4);
				obj9.setObjins(null);
				obj9.setSparse(0);
				obj9.setTableName(tableName);
				obj9.setTableType(tableType);
				obj9.setTmsort(i);
				obj9.setTmused(1);
				obj9.setUnitId(unitid);
				obj9.setFormId(formId);
				obj9.setIsTotal(0);
				result.put("dhcz", obj9);
				i = i + 1;

				CostBgcsszb obj10 = new CostBgcsszb();
				obj10.setColumnCode("dwcbcz");
				obj10.setColumnshowName("单位成本差值");
				obj10.setIsTotal(0);
				obj10.setObjalg(1);
				obj10.setObjdis(4);
				obj10.setObjins(null);
				obj10.setSparse(0);
				obj10.setTableName(tableName);
				obj10.setTableType(tableType);
				obj10.setTmsort(i);
				obj10.setTmused(1);
				obj10.setUnitId(unitid);
				obj10.setFormId(formId);
				obj10.setIsTotal(0);
				result.put("dwcbcz", obj10);
				i = i + 1;
				
				CostBgcsszb obj11 = new CostBgcsszb();
				obj11.setColumnCode("nh");
				obj11.setColumnshowName("能耗");
				obj11.setIsTotal(0);
				obj11.setObjalg(1);
				obj11.setObjdis(4);
				obj11.setObjins(null);
				obj11.setSparse(0);
				obj11.setTableName(tableName);
				obj11.setTableType(tableType);
				obj11.setTmsort(i);
				obj11.setTmused(1);
				obj11.setUnitId(unitid);
				obj11.setFormId(formId);
				obj11.setIsTotal(0);
				result.put("nh", obj11);
				i = i + 1;
			} else if ("标题".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("bbTitle");
				obj.setColumnshowName("经济核算投入产出利润汇总表");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(1);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				result.put("bbTitle", obj);
			}
		} else if ("bb09".equals(tableName)) {// 班组交接班数据录入
			if ("表头".equals(tableType)) {
				CostBgcsszb objProg = new CostBgcsszb();
				objProg.setColumnCode("programName");
				objProg.setColumnshowName("工况");
				objProg.setIsTotal(0);
				objProg.setObjalg(0);
				objProg.setObjdis(0);
				objProg.setObjins(null);
				objProg.setSparse(0);
				objProg.setTableName(tableName);
				objProg.setTableType(tableType);
				objProg.setTmsort(i);
				objProg.setTmused(0);
				objProg.setUnitId(unitid);
				objProg.setFormId(formId);
				objProg.setIsTotal(0);
				result.put("programName", objProg);
				i = i + 1;
				
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("className");
				obj.setColumnshowName("分类名称");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(i);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				obj.setIsTotal(0);
				result.put("className", obj);
				i = i + 1;
				
				CostBgcsszb obj1 = new CostBgcsszb();
				obj1.setColumnCode("itemName");
				obj1.setColumnshowName("项目名称");
				obj1.setIsTotal(0);
				obj1.setObjalg(0);
				obj1.setObjdis(0);
				obj1.setObjins(null);
				obj1.setSparse(0);
				obj1.setTableName(tableName);
				obj1.setTableType(tableType);
				obj1.setTmsort(i);
				obj1.setTmused(1);
				obj1.setUnitId(unitid);
				obj1.setFormId(formId);
				obj1.setIsTotal(0);
				result.put("itemName", obj1);
				i = i + 1;
				
				CostBgcsszb obj2 = new CostBgcsszb();
				obj2.setColumnCode("instrumentName");
				obj2.setColumnshowName("仪表名称");
				obj2.setIsTotal(0);
				obj2.setObjalg(0);
				obj2.setObjdis(0);
				obj2.setObjins(null);
				obj2.setSparse(0);
				obj2.setTableName(tableName);
				obj2.setTableType(tableType);
				obj2.setTmsort(i);
				obj2.setTmused(1);
				obj2.setUnitId(unitid);
				obj2.setFormId(formId);
				obj2.setIsTotal(0);
				result.put("instrumentName", obj2);

				CostBgcsszb obj3 = new CostBgcsszb();
				obj3.setColumnCode("itemUnit");
				obj3.setColumnshowName("计量单位");
				obj3.setIsTotal(0);
				obj3.setObjalg(0);
				obj3.setObjdis(0);
				obj3.setObjins(null);
				obj3.setSparse(0);
				obj3.setTableName(tableName);
				obj3.setTableType(tableType);
				obj3.setTmsort(i);
				obj3.setTmused(1);
				obj3.setUnitId(unitid);
				obj3.setFormId(formId);
				obj3.setIsTotal(0);
				result.put("itemUnit", obj3);
				i = i + 1;

				CostBgcsszb obj4 = new CostBgcsszb();
				obj4.setColumnCode("previousReadOut");
				obj4.setColumnshowName("前表数");
				obj4.setIsTotal(0);
				obj4.setObjalg(1);
				obj4.setObjdis(4);
				obj4.setObjins(null);
				obj4.setSparse(0);
				obj4.setTableName(tableName);
				obj4.setTableType(tableType);
				obj4.setTmsort(i);
				obj4.setTmused(1);
				obj4.setUnitId(unitid);
				obj4.setFormId(formId);
				obj4.setIsTotal(0);
				result.put("previousReadOut", obj4);
				i = i + 1;

				CostBgcsszb obj5 = new CostBgcsszb();
				obj5.setColumnCode("lastReadOut");
				obj5.setColumnshowName("后表数");
				obj5.setIsTotal(0);
				obj5.setObjalg(1);
				obj5.setObjdis(4);
				obj5.setObjins(null);
				obj5.setSparse(0);
				obj5.setTableName(tableName);
				obj5.setTableType(tableType);
				obj5.setTmsort(i);
				obj5.setTmused(1);
				obj5.setUnitId(unitid);
				obj5.setFormId(formId);
				obj5.setIsTotal(0);
				result.put("lastReadOut", obj5);
				i = i + 1;
				
				CostBgcsszb obj6 = new CostBgcsszb();
				obj6.setColumnCode("calcVal");
				obj6.setColumnshowName("本班量");
				obj6.setIsTotal(0);
				obj6.setObjalg(1);
				obj6.setObjdis(4);
				obj6.setObjins(null);
				obj6.setSparse(0);
				obj6.setTableName(tableName);
				obj6.setTableType(tableType);
				obj6.setTmsort(i);
				obj6.setTmused(1);
				obj6.setUnitId(unitid);
				obj6.setFormId(formId);
				obj6.setIsTotal(0);
				result.put("calcVal", obj6);
				i = i + 1;
				
				CostBgcsszb objlastReadTime = new CostBgcsszb();
				objlastReadTime.setColumnCode("lastReadTime");
				objlastReadTime.setColumnshowName("采集时间");
				objlastReadTime.setIsTotal(0);
				objlastReadTime.setObjalg(1);
				objlastReadTime.setObjdis(0);
				objlastReadTime.setObjins(null);
				objlastReadTime.setSparse(0);
				objlastReadTime.setTableName(tableName);
				objlastReadTime.setTableType(tableType);
				objlastReadTime.setTmsort(i);
				objlastReadTime.setTmused(1);
				objlastReadTime.setUnitId(unitid);
				objlastReadTime.setFormId(formId);
				objlastReadTime.setIsTotal(0);
				result.put("lastReadTime", objlastReadTime);
				i = i + 1;
				
				CostBgcsszb objaddItemRemark = new CostBgcsszb();
				objaddItemRemark.setColumnCode("addItemRemark");
				objaddItemRemark.setColumnshowName("描述");
				objaddItemRemark.setIsTotal(0);
				objaddItemRemark.setObjalg(1);
				objaddItemRemark.setObjdis(0);
				objaddItemRemark.setObjins(null);
				objaddItemRemark.setSparse(0);
				objaddItemRemark.setTableName(tableName);
				objaddItemRemark.setTableType(tableType);
				objaddItemRemark.setTmsort(i);
				objaddItemRemark.setTmused(0);
				objaddItemRemark.setUnitId(unitid);
				objaddItemRemark.setFormId(formId);
				objaddItemRemark.setIsTotal(0);
				result.put("addItemRemark", objaddItemRemark);
				i = i + 1;
				
			} else if ("标题".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("bbTitle");
				obj.setColumnshowName("班组交接班数据录入");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(1);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				result.put("bbTitle", obj);
			}
		} else if ("bb05".equals(tableName)) {// 装置经济核算投入产出利润表(周)
			if ("表头".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("itemName");
				obj.setColumnshowName("项目名称");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(i);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				obj.setIsTotal(0);
				result.put("itemName", obj);
				i = i + 1;

				CostBgcsszb obj1 = new CostBgcsszb();
				obj1.setColumnCode("itemUnit");
				obj1.setColumnshowName("计量单位");
				obj1.setIsTotal(0);
				obj1.setObjalg(0);
				obj1.setObjdis(0);
				obj1.setObjins(null);
				obj1.setSparse(0);
				obj1.setTableName(tableName);
				obj1.setTableType(tableType);
				obj1.setTmsort(i);
				obj1.setTmused(1);
				obj1.setUnitId(unitid);
				obj1.setFormId(formId);
				obj1.setIsTotal(0);
				result.put("itemUnit", obj1);
				i = i + 1;

				CostBgcsszb obj2 = new CostBgcsszb();
				obj2.setColumnCode("itemPrice");
				obj2.setColumnshowName("单价");
				obj2.setIsTotal(0);
				obj2.setObjalg(1);
				obj2.setObjdis(4);
				obj2.setObjins(null);
				obj2.setSparse(0);
				obj2.setTableName(tableName);
				obj2.setTableType(tableType);
				obj2.setTmsort(i);
				obj2.setTmused(1);
				obj2.setUnitId(unitid);
				obj2.setFormId(formId);
				obj2.setIsTotal(0);
				result.put("itemPrice", obj2);
				i = i + 1;

				CostBgcsszb obj3 = new CostBgcsszb();
				obj3.setColumnCode("dwcbde");
				obj3.setColumnshowName("单位成本定额");
				obj3.setIsTotal(0);
				obj3.setObjalg(1);
				obj3.setObjdis(4);
				obj3.setObjins(null);
				obj3.setSparse(0);
				obj3.setTableName(tableName);
				obj3.setTableType(tableType);
				obj3.setTmsort(i);
				obj3.setTmused(1);
				obj3.setUnitId(unitid);
				obj3.setFormId(formId);
				obj3.setIsTotal(0);
				result.put("dwcbde", obj3);
				i = i + 1;

				CostBgcsszb obj4 = new CostBgcsszb();
				obj4.setColumnCode("dhde");
				obj4.setColumnshowName("单耗定额");
				obj4.setIsTotal(0);
				obj4.setObjalg(1);
				obj4.setObjdis(4);
				obj4.setObjins(null);
				obj4.setSparse(0);
				obj4.setTableName(tableName);
				obj4.setTableType(tableType);
				obj4.setTmsort(i);
				obj4.setTmused(1);
				obj4.setUnitId(unitid);
				obj4.setFormId(formId);
				obj4.setIsTotal(0);
				result.put("dhde", obj4);
				i = i + 1;

				CostBgcsszb obj5 = new CostBgcsszb();
				obj5.setColumnCode("xhl");
				obj5.setColumnshowName("消耗量");
				obj5.setIsTotal(0);
				obj5.setObjalg(1);
				obj5.setObjdis(4);
				obj5.setObjins(null);
				obj5.setSparse(0);
				obj5.setTableName(tableName);
				obj5.setTableType(tableType);
				obj5.setTmsort(i);
				obj5.setTmused(1);
				obj5.setUnitId(unitid);
				obj5.setFormId(formId);
				obj5.setIsTotal(0);
				result.put("xhl", obj5);
				i = i + 1;

				CostBgcsszb obj6 = new CostBgcsszb();
				obj6.setColumnCode("dh");
				obj6.setColumnshowName("单耗");
				obj6.setIsTotal(0);
				obj6.setObjalg(1);
				obj6.setObjdis(4);
				obj6.setObjins(null);
				obj6.setSparse(0);
				obj6.setTableName(tableName);
				obj6.setTableType(tableType);
				obj6.setTmsort(i);
				obj6.setTmused(1);
				obj6.setUnitId(unitid);
				obj6.setFormId(formId);
				obj6.setIsTotal(0);
				result.put("dh", obj6);
				i = i + 1;

				CostBgcsszb obj7 = new CostBgcsszb();
				obj7.setColumnCode("dwcb");
				obj7.setColumnshowName("单位成本");
				obj7.setIsTotal(0);
				obj7.setObjalg(1);
				obj7.setObjdis(4);
				obj7.setObjins(null);
				obj7.setSparse(0);
				obj7.setTableName(tableName);
				obj7.setTableType(tableType);
				obj7.setTmsort(i);
				obj7.setTmused(1);
				obj7.setUnitId(unitid);
				obj7.setFormId(formId);
				obj7.setIsTotal(0);
				result.put("dwcb", obj7);
				i = i + 1;

				CostBgcsszb obj8 = new CostBgcsszb();
				obj8.setColumnCode("zcb");
				obj8.setColumnshowName("总成本");
				obj8.setIsTotal(0);
				obj8.setObjalg(1);
				obj8.setObjdis(4);
				obj8.setObjins(null);
				obj8.setSparse(0);
				obj8.setTableName(tableName);
				obj8.setTableType(tableType);
				obj8.setTmsort(i);
				obj8.setTmused(1);
				obj8.setUnitId(unitid);
				obj8.setFormId(formId);
				obj8.setIsTotal(0);
				result.put("zcb", obj8);
				i = i + 1;

				CostBgcsszb obj9 = new CostBgcsszb();
				obj9.setColumnCode("dhcz");
				obj9.setColumnshowName("单耗差值");
				obj9.setIsTotal(0);
				obj9.setObjalg(1);
				obj9.setObjdis(4);
				obj9.setObjins(null);
				obj9.setSparse(0);
				obj9.setTableName(tableName);
				obj9.setTableType(tableType);
				obj9.setTmsort(i);
				obj9.setTmused(1);
				obj9.setUnitId(unitid);
				obj9.setFormId(formId);
				obj9.setIsTotal(0);
				result.put("dhcz", obj9);
				i = i + 1;

				CostBgcsszb obj10 = new CostBgcsszb();
				obj10.setColumnCode("dwcbcz");
				obj10.setColumnshowName("单位成本差值");
				obj10.setIsTotal(0);
				obj10.setObjalg(1);
				obj10.setObjdis(4);
				obj10.setObjins(null);
				obj10.setSparse(0);
				obj10.setTableName(tableName);
				obj10.setTableType(tableType);
				obj10.setTmsort(i);
				obj10.setTmused(1);
				obj10.setUnitId(unitid);
				obj10.setFormId(formId);
				obj10.setIsTotal(0);
				result.put("dwcbcz", obj10);
				i = i + 1;
				
				CostBgcsszb obj11 = new CostBgcsszb();
				obj11.setColumnCode("nh");
				obj11.setColumnshowName("能耗");
				obj11.setIsTotal(0);
				obj11.setObjalg(1);
				obj11.setObjdis(4);
				obj11.setObjins(null);
				obj11.setSparse(0);
				obj11.setTableName(tableName);
				obj11.setTableType(tableType);
				obj11.setTmsort(i);
				obj11.setTmused(1);
				obj11.setUnitId(unitid);
				obj11.setFormId(formId);
				obj11.setIsTotal(0);
				result.put("nh", obj11);
				i = i + 1;
			} else if ("标题".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("bbTitle");
				obj.setColumnshowName("装置经济核算投入产出利润表(周)");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(1);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				result.put("bbTitle", obj);
			}
		} else if ("bb06".equals(tableName)) {// 班组经济核算投入产出利润表
			if ("表头".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("itemName");
				obj.setColumnshowName("项目名称");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(i);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				obj.setIsTotal(0);
				result.put("itemName", obj);
				i = i + 1;

				CostBgcsszb obj1 = new CostBgcsszb();
				obj1.setColumnCode("instrumentName");
				obj1.setColumnshowName("仪表名称");
				obj1.setIsTotal(0);
				obj1.setObjalg(0);
				obj1.setObjdis(0);
				obj1.setObjins(null);
				obj1.setSparse(0);
				obj1.setTableName(tableName);
				obj1.setTableType(tableType);
				obj1.setTmsort(i);
				obj1.setTmused(1);
				obj1.setUnitId(unitid);
				obj1.setFormId(formId);
				obj1.setIsTotal(0);
				result.put("instrumentName", obj1);
				i = i + 1;

				CostBgcsszb obj2 = new CostBgcsszb();
				obj2.setColumnCode("itemUnit");
				obj2.setColumnshowName("计量单位");
				obj2.setIsTotal(0);
				obj2.setObjalg(0);
				obj2.setObjdis(0);
				obj2.setObjins(null);
				obj2.setSparse(0);
				obj2.setTableName(tableName);
				obj2.setTableType(tableType);
				obj2.setTmsort(i);
				obj2.setTmused(1);
				obj2.setUnitId(unitid);
				obj2.setFormId(formId);
				obj2.setIsTotal(0);
				result.put("itemUnit", obj2);
				i = i + 1;

				CostBgcsszb obj3 = new CostBgcsszb();
				obj3.setColumnCode("itemPrice");
				obj3.setColumnshowName("单价");
				obj3.setIsTotal(0);
				obj3.setObjalg(1);
				obj3.setObjdis(4);
				obj3.setObjins(null);
				obj3.setSparse(0);
				obj3.setTableName(tableName);
				obj3.setTableType(tableType);
				obj3.setTmsort(i);
				obj3.setTmused(1);
				obj3.setUnitId(unitid);
				obj3.setFormId(formId);
				obj3.setIsTotal(0);
				result.put("itemPrice", obj3);
				i = i + 1;

				CostBgcsszb obj4 = new CostBgcsszb();
				obj4.setColumnCode("dwcbde");
				obj4.setColumnshowName("单位成本定额");
				obj4.setIsTotal(0);
				obj4.setObjalg(1);
				obj4.setObjdis(4);
				obj4.setObjins(null);
				obj4.setSparse(0);
				obj4.setTableName(tableName);
				obj4.setTableType(tableType);
				obj4.setTmsort(i);
				obj4.setTmused(1);
				obj4.setUnitId(unitid);
				obj4.setFormId(formId);
				obj4.setIsTotal(0);
				result.put("dwcbde", obj4);
				i = i + 1;

				CostBgcsszb obj5 = new CostBgcsszb();
				obj5.setColumnCode("dhde");
				obj5.setColumnshowName("单耗定额");
				obj5.setIsTotal(0);
				obj5.setObjalg(1);
				obj5.setObjdis(4);
				obj5.setObjins(null);
				obj5.setSparse(0);
				obj5.setTableName(tableName);
				obj5.setTableType(tableType);
				obj5.setTmsort(i);
				obj5.setTmused(1);
				obj5.setUnitId(unitid);
				obj5.setFormId(formId);
				obj5.setIsTotal(0);
				result.put("dhde", obj5);
				i = i + 1;

				CostBgcsszb obj6 = new CostBgcsszb();
				obj6.setColumnCode("previousReadOut");
				obj6.setColumnshowName("前表数");
				obj6.setIsTotal(0);
				obj6.setObjalg(1);
				obj6.setObjdis(4);
				obj6.setObjins(null);
				obj6.setSparse(0);
				obj6.setTableName(tableName);
				obj6.setTableType(tableType);
				obj6.setTmsort(i);
				obj6.setTmused(1);
				obj6.setUnitId(unitid);
				obj6.setFormId(formId);
				obj6.setIsTotal(0);
				result.put("previousReadOut", obj6);
				i = i + 1;

				CostBgcsszb obj7 = new CostBgcsszb();
				obj7.setColumnCode("lastReadOut");
				obj7.setColumnshowName("后表数");
				obj7.setIsTotal(0);
				obj7.setObjalg(1);
				obj7.setObjdis(4);
				obj7.setObjins(null);
				obj7.setSparse(0);
				obj7.setTableName(tableName);
				obj7.setTableType(tableType);
				obj7.setTmsort(i);
				obj7.setTmused(1);
				obj7.setUnitId(unitid);
				obj7.setFormId(formId);
				obj7.setIsTotal(0);
				result.put("lastReadOut", obj7);
				i = i + 1;

				CostBgcsszb obj8 = new CostBgcsszb();
				obj8.setColumnCode("dbxh");
				obj8.setColumnshowName("单表消耗");
				obj8.setIsTotal(0);
				obj8.setObjalg(1);
				obj8.setObjdis(4);
				obj8.setObjins(null);
				obj8.setSparse(0);
				obj8.setTableName(tableName);
				obj8.setTableType(tableType);
				obj8.setTmsort(i);
				obj8.setTmused(1);
				obj8.setUnitId(unitid);
				obj8.setFormId(formId);
				obj8.setIsTotal(0);
				result.put("dbxh", obj8);
				i = i + 1;

				CostBgcsszb obj9 = new CostBgcsszb();
				obj9.setColumnCode("xhl");
				obj9.setColumnshowName("消耗量");
				obj9.setIsTotal(0);
				obj9.setObjalg(1);
				obj9.setObjdis(4);
				obj9.setObjins(null);
				obj9.setSparse(0);
				obj9.setTableName(tableName);
				obj9.setTableType(tableType);
				obj9.setTmsort(i);
				obj9.setTmused(1);
				obj9.setUnitId(unitid);
				obj9.setFormId(formId);
				obj9.setIsTotal(0);
				result.put("xhl", obj9);
				i = i + 1;

				CostBgcsszb obj10 = new CostBgcsszb();
				obj10.setColumnCode("dh");
				obj10.setColumnshowName("单耗");
				obj10.setIsTotal(0);
				obj10.setObjalg(1);
				obj10.setObjdis(4);
				obj10.setObjins(null);
				obj10.setSparse(0);
				obj10.setTableName(tableName);
				obj10.setTableType(tableType);
				obj10.setTmsort(i);
				obj10.setTmused(1);
				obj10.setUnitId(unitid);
				obj10.setFormId(formId);
				obj10.setIsTotal(0);
				result.put("dh", obj10);
				i = i + 1;

				CostBgcsszb obj11 = new CostBgcsszb();
				obj11.setColumnCode("dwcb");
				obj11.setColumnshowName("单位成本");
				obj11.setIsTotal(0);
				obj11.setObjalg(1);
				obj11.setObjdis(4);
				obj11.setObjins(null);
				obj11.setSparse(0);
				obj11.setTableName(tableName);
				obj11.setTableType(tableType);
				obj11.setTmsort(i);
				obj11.setTmused(1);
				obj11.setUnitId(unitid);
				obj11.setFormId(formId);
				obj11.setIsTotal(0);
				result.put("dwcb", obj11);
				i = i + 1;

				CostBgcsszb obj12 = new CostBgcsszb();
				obj12.setColumnCode("zcb");
				obj12.setColumnshowName("总成本");
				obj12.setIsTotal(0);
				obj12.setObjalg(1);
				obj12.setObjdis(4);
				obj12.setObjins(null);
				obj12.setSparse(0);
				obj12.setTableName(tableName);
				obj12.setTableType(tableType);
				obj12.setTmsort(i);
				obj12.setTmused(1);
				obj12.setUnitId(unitid);
				obj12.setFormId(formId);
				obj12.setIsTotal(0);
				result.put("zcb", obj12);
				i = i + 1;

				CostBgcsszb obj13 = new CostBgcsszb();
				obj13.setColumnCode("dhcz");
				obj13.setColumnshowName("单耗差值");
				obj13.setIsTotal(0);
				obj13.setObjalg(1);
				obj13.setObjdis(4);
				obj13.setObjins(null);
				obj13.setSparse(0);
				obj13.setTableName(tableName);
				obj13.setTableType(tableType);
				obj13.setTmsort(i);
				obj13.setTmused(1);
				obj13.setUnitId(unitid);
				obj13.setFormId(formId);
				obj13.setIsTotal(0);
				result.put("dhcz", obj13);
				i = i + 1;

				CostBgcsszb obj14 = new CostBgcsszb();
				obj14.setColumnCode("dwcbcz");
				obj14.setColumnshowName("单位成本差值");
				obj14.setIsTotal(0);
				obj14.setObjalg(1);
				obj14.setObjdis(4);
				obj14.setObjins(null);
				obj14.setSparse(0);
				obj14.setTableName(tableName);
				obj14.setTableType(tableType);
				obj14.setTmsort(i);
				obj14.setTmused(1);
				obj14.setUnitId(unitid);
				obj14.setFormId(formId);
				obj14.setIsTotal(0);
				result.put("dwcbcz", obj14);
				i = i + 1;
				
				CostBgcsszb obj15 = new CostBgcsszb();
				obj15.setColumnCode("nh");
				obj15.setColumnshowName("能耗");
				obj15.setIsTotal(0);
				obj15.setObjalg(1);
				obj15.setObjdis(4);
				obj15.setObjins(null);
				obj15.setSparse(0);
				obj15.setTableName(tableName);
				obj15.setTableType(tableType);
				obj15.setTmsort(i);
				obj15.setTmused(1);
				obj15.setUnitId(unitid);
				obj15.setFormId(formId);
				obj15.setIsTotal(0);
				result.put("nh", obj15);
				i = i + 1;
			} else if ("标题".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("bbTitle");
				obj.setColumnshowName("班组经济核算投入产出利润表");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(1);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				result.put("bbTitle", obj);
			}
		} else if ("bb10".equals(tableName)) {// 分时段报表
			if ("表头".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("itemName");
				obj.setColumnshowName("项目名称");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(i);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				obj.setIsTotal(0);
				result.put("itemName", obj);
				i = i + 1;

				CostBgcsszb obj1 = new CostBgcsszb();
				obj1.setColumnCode("itemUnit");
				obj1.setColumnshowName("计量单位");
				obj1.setIsTotal(0);
				obj1.setObjalg(0);
				obj1.setObjdis(0);
				obj1.setObjins(null);
				obj1.setSparse(0);
				obj1.setTableName(tableName);
				obj1.setTableType(tableType);
				obj1.setTmsort(i);
				obj1.setTmused(1);
				obj1.setUnitId(unitid);
				obj1.setFormId(formId);
				obj1.setIsTotal(0);
				result.put("itemUnit", obj1);
				i = i + 1;

				CostBgcsszb obj2 = new CostBgcsszb();
				obj2.setColumnCode("itemPrice");
				obj2.setColumnshowName("单价");
				obj2.setIsTotal(0);
				obj2.setObjalg(1);
				obj2.setObjdis(4);
				obj2.setObjins(null);
				obj2.setSparse(0);
				obj2.setTableName(tableName);
				obj2.setTableType(tableType);
				obj2.setTmsort(i);
				obj2.setTmused(1);
				obj2.setUnitId(unitid);
				obj2.setFormId(formId);
				obj2.setIsTotal(0);
				result.put("itemPrice", obj2);
				i = i + 1;

				CostBgcsszb obj3 = new CostBgcsszb();
				obj3.setColumnCode("dwcbde");
				obj3.setColumnshowName("单位成本定额");
				obj3.setIsTotal(0);
				obj3.setObjalg(1);
				obj3.setObjdis(4);
				obj3.setObjins(null);
				obj3.setSparse(0);
				obj3.setTableName(tableName);
				obj3.setTableType(tableType);
				obj3.setTmsort(i);
				obj3.setTmused(1);
				obj3.setUnitId(unitid);
				obj3.setFormId(formId);
				obj3.setIsTotal(0);
				result.put("dwcbde", obj3);
				i = i + 1;

				CostBgcsszb obj4 = new CostBgcsszb();
				obj4.setColumnCode("dhde");
				obj4.setColumnshowName("单耗定额");
				obj4.setIsTotal(0);
				obj4.setObjalg(1);
				obj4.setObjdis(4);
				obj4.setObjins(null);
				obj4.setSparse(0);
				obj4.setTableName(tableName);
				obj4.setTableType(tableType);
				obj4.setTmsort(i);
				obj4.setTmused(1);
				obj4.setUnitId(unitid);
				obj4.setFormId(formId);
				obj4.setIsTotal(0);
				result.put("dhde", obj4);
				i = i + 1;

				CostBgcsszb obj5 = new CostBgcsszb();
				obj5.setColumnCode("xhl");
				obj5.setColumnshowName("消耗量");
				obj5.setIsTotal(0);
				obj5.setObjalg(1);
				obj5.setObjdis(4);
				obj5.setObjins(null);
				obj5.setSparse(0);
				obj5.setTableName(tableName);
				obj5.setTableType(tableType);
				obj5.setTmsort(i);
				obj5.setTmused(1);
				obj5.setUnitId(unitid);
				obj5.setFormId(formId);
				obj5.setIsTotal(0);
				result.put("xhl", obj5);
				i = i + 1;

				CostBgcsszb obj6 = new CostBgcsszb();
				obj6.setColumnCode("dh");
				obj6.setColumnshowName("单耗");
				obj6.setIsTotal(0);
				obj6.setObjalg(1);
				obj6.setObjdis(4);
				obj6.setObjins(null);
				obj6.setSparse(0);
				obj6.setTableName(tableName);
				obj6.setTableType(tableType);
				obj6.setTmsort(i);
				obj6.setTmused(1);
				obj6.setUnitId(unitid);
				obj6.setFormId(formId);
				obj6.setIsTotal(0);
				result.put("dh", obj6);
				i = i + 1;

				CostBgcsszb obj7 = new CostBgcsszb();
				obj7.setColumnCode("dwcb");
				obj7.setColumnshowName("单位成本");
				obj7.setIsTotal(0);
				obj7.setObjalg(1);
				obj7.setObjdis(4);
				obj7.setObjins(null);
				obj7.setSparse(0);
				obj7.setTableName(tableName);
				obj7.setTableType(tableType);
				obj7.setTmsort(i);
				obj7.setTmused(1);
				obj7.setUnitId(unitid);
				obj7.setFormId(formId);
				obj7.setIsTotal(0);
				result.put("dwcb", obj7);
				i = i + 1;

				CostBgcsszb obj8 = new CostBgcsszb();
				obj8.setColumnCode("zcb");
				obj8.setColumnshowName("总成本");
				obj8.setIsTotal(0);
				obj8.setObjalg(1);
				obj8.setObjdis(4);
				obj8.setObjins(null);
				obj8.setSparse(0);
				obj8.setTableName(tableName);
				obj8.setTableType(tableType);
				obj8.setTmsort(i);
				obj8.setTmused(1);
				obj8.setUnitId(unitid);
				obj8.setFormId(formId);
				obj8.setIsTotal(0);
				result.put("zcb", obj8);
				i = i + 1;

				CostBgcsszb obj9 = new CostBgcsszb();
				obj9.setColumnCode("dhcz");
				obj9.setColumnshowName("单耗差值");
				obj9.setIsTotal(0);
				obj9.setObjalg(1);
				obj9.setObjdis(4);
				obj9.setObjins(null);
				obj9.setSparse(0);
				obj9.setTableName(tableName);
				obj9.setTableType(tableType);
				obj9.setTmsort(i);
				obj9.setTmused(1);
				obj9.setUnitId(unitid);
				obj9.setFormId(formId);
				obj9.setIsTotal(0);
				result.put("dhcz", obj9);
				i = i + 1;

				CostBgcsszb obj10 = new CostBgcsszb();
				obj10.setColumnCode("dwcbcz");
				obj10.setColumnshowName("单位成本差值");
				obj10.setIsTotal(0);
				obj10.setObjalg(1);
				obj10.setObjdis(4);
				obj10.setObjins(null);
				obj10.setSparse(0);
				obj10.setTableName(tableName);
				obj10.setTableType(tableType);
				obj10.setTmsort(i);
				obj10.setTmused(1);
				obj10.setUnitId(unitid);
				obj10.setFormId(formId);
				obj10.setIsTotal(0);
				result.put("dwcbcz", obj10);
				i = i + 1;
				
				CostBgcsszb obj11 = new CostBgcsszb();
				obj11.setColumnCode("nh");
				obj11.setColumnshowName("能耗");
				obj11.setIsTotal(0);
				obj11.setObjalg(1);
				obj11.setObjdis(4);
				obj11.setObjins(null);
				obj11.setSparse(0);
				obj11.setTableName(tableName);
				obj11.setTableType(tableType);
				obj11.setTmsort(i);
				obj11.setTmused(1);
				obj11.setUnitId(unitid);
				obj11.setFormId(formId);
				obj11.setIsTotal(0);
				result.put("nh", obj11);
				i = i + 1;
			} else if ("标题".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("bbTitle");
				obj.setColumnshowName("分时段报表");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(1);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				result.put("bbTitle", obj);
			}
		}  else if ("bb11".equals(tableName)) {// 分时段报表
			if ("表头".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("itemName");
				obj.setColumnshowName("项目名称");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(i);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				obj.setIsTotal(0);
				result.put("itemName", obj);
				i = i + 1;

				CostBgcsszb obj1 = new CostBgcsszb();
				obj1.setColumnCode("itemUnit");
				obj1.setColumnshowName("计量单位");
				obj1.setIsTotal(0);
				obj1.setObjalg(0);
				obj1.setObjdis(0);
				obj1.setObjins(null);
				obj1.setSparse(0);
				obj1.setTableName(tableName);
				obj1.setTableType(tableType);
				obj1.setTmsort(i);
				obj1.setTmused(1);
				obj1.setUnitId(unitid);
				obj1.setFormId(formId);
				obj1.setIsTotal(0);
				result.put("itemUnit", obj1);
				i = i + 1;

				CostBgcsszb obj2 = new CostBgcsszb();
				obj2.setColumnCode("itemPrice");
				obj2.setColumnshowName("单价");
				obj2.setIsTotal(0);
				obj2.setObjalg(1);
				obj2.setObjdis(4);
				obj2.setObjins(null);
				obj2.setSparse(0);
				obj2.setTableName(tableName);
				obj2.setTableType(tableType);
				obj2.setTmsort(i);
				obj2.setTmused(1);
				obj2.setUnitId(unitid);
				obj2.setFormId(formId);
				obj2.setIsTotal(0);
				result.put("itemPrice", obj2);
				i = i + 1;

				CostBgcsszb obj3 = new CostBgcsszb();
				obj3.setColumnCode("dwcbde");
				obj3.setColumnshowName("单位成本定额");
				obj3.setIsTotal(0);
				obj3.setObjalg(1);
				obj3.setObjdis(4);
				obj3.setObjins(null);
				obj3.setSparse(0);
				obj3.setTableName(tableName);
				obj3.setTableType(tableType);
				obj3.setTmsort(i);
				obj3.setTmused(1);
				obj3.setUnitId(unitid);
				obj3.setFormId(formId);
				obj3.setIsTotal(0);
				result.put("dwcbde", obj3);
				i = i + 1;

				CostBgcsszb obj4 = new CostBgcsszb();
				obj4.setColumnCode("dhde");
				obj4.setColumnshowName("单耗定额");
				obj4.setIsTotal(0);
				obj4.setObjalg(1);
				obj4.setObjdis(4);
				obj4.setObjins(null);
				obj4.setSparse(0);
				obj4.setTableName(tableName);
				obj4.setTableType(tableType);
				obj4.setTmsort(i);
				obj4.setTmused(1);
				obj4.setUnitId(unitid);
				obj4.setFormId(formId);
				obj4.setIsTotal(0);
				result.put("dhde", obj4);
				i = i + 1;

				CostBgcsszb obj5 = new CostBgcsszb();
				obj5.setColumnCode("xhl");
				obj5.setColumnshowName("消耗量");
				obj5.setIsTotal(0);
				obj5.setObjalg(1);
				obj5.setObjdis(4);
				obj5.setObjins(null);
				obj5.setSparse(0);
				obj5.setTableName(tableName);
				obj5.setTableType(tableType);
				obj5.setTmsort(i);
				obj5.setTmused(1);
				obj5.setUnitId(unitid);
				obj5.setFormId(formId);
				obj5.setIsTotal(0);
				result.put("xhl", obj5);
				i = i + 1;

				CostBgcsszb obj6 = new CostBgcsszb();
				obj6.setColumnCode("dh");
				obj6.setColumnshowName("单耗");
				obj6.setIsTotal(0);
				obj6.setObjalg(1);
				obj6.setObjdis(4);
				obj6.setObjins(null);
				obj6.setSparse(0);
				obj6.setTableName(tableName);
				obj6.setTableType(tableType);
				obj6.setTmsort(i);
				obj6.setTmused(1);
				obj6.setUnitId(unitid);
				obj6.setFormId(formId);
				obj6.setIsTotal(0);
				result.put("dh", obj6);
				i = i + 1;

				CostBgcsszb obj7 = new CostBgcsszb();
				obj7.setColumnCode("dwcb");
				obj7.setColumnshowName("单位成本");
				obj7.setIsTotal(0);
				obj7.setObjalg(1);
				obj7.setObjdis(4);
				obj7.setObjins(null);
				obj7.setSparse(0);
				obj7.setTableName(tableName);
				obj7.setTableType(tableType);
				obj7.setTmsort(i);
				obj7.setTmused(1);
				obj7.setUnitId(unitid);
				obj7.setFormId(formId);
				obj7.setIsTotal(0);
				result.put("dwcb", obj7);
				i = i + 1;

				CostBgcsszb obj8 = new CostBgcsszb();
				obj8.setColumnCode("zcb");
				obj8.setColumnshowName("总成本");
				obj8.setIsTotal(0);
				obj8.setObjalg(1);
				obj8.setObjdis(4);
				obj8.setObjins(null);
				obj8.setSparse(0);
				obj8.setTableName(tableName);
				obj8.setTableType(tableType);
				obj8.setTmsort(i);
				obj8.setTmused(1);
				obj8.setUnitId(unitid);
				obj8.setFormId(formId);
				obj8.setIsTotal(0);
				result.put("zcb", obj8);
				i = i + 1;

				CostBgcsszb obj9 = new CostBgcsszb();
				obj9.setColumnCode("dhcz");
				obj9.setColumnshowName("单耗差值");
				obj9.setIsTotal(0);
				obj9.setObjalg(1);
				obj9.setObjdis(4);
				obj9.setObjins(null);
				obj9.setSparse(0);
				obj9.setTableName(tableName);
				obj9.setTableType(tableType);
				obj9.setTmsort(i);
				obj9.setTmused(1);
				obj9.setUnitId(unitid);
				obj9.setFormId(formId);
				obj9.setIsTotal(0);
				result.put("dhcz", obj9);
				i = i + 1;

				CostBgcsszb obj10 = new CostBgcsszb();
				obj10.setColumnCode("dwcbcz");
				obj10.setColumnshowName("单位成本差值");
				obj10.setIsTotal(0);
				obj10.setObjalg(1);
				obj10.setObjdis(4);
				obj10.setObjins(null);
				obj10.setSparse(0);
				obj10.setTableName(tableName);
				obj10.setTableType(tableType);
				obj10.setTmsort(i);
				obj10.setTmused(1);
				obj10.setUnitId(unitid);
				obj10.setFormId(formId);
				obj10.setIsTotal(0);
				result.put("dwcbcz", obj10);
				i = i + 1;
				
				CostBgcsszb obj11 = new CostBgcsszb();
				obj11.setColumnCode("nh");
				obj11.setColumnshowName("能耗");
				obj11.setIsTotal(0);
				obj11.setObjalg(1);
				obj11.setObjdis(4);
				obj11.setObjins(null);
				obj11.setSparse(0);
				obj11.setTableName(tableName);
				obj11.setTableType(tableType);
				obj11.setTmsort(i);
				obj11.setTmused(1);
				obj11.setUnitId(unitid);
				obj11.setFormId(formId);
				obj11.setIsTotal(0);
				result.put("nh", obj11);
				i = i + 1;
			} else if ("标题".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("bbTitle");
				obj.setColumnshowName("班组成本核算月度汇总表");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(1);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				result.put("bbTitle", obj);
			}
		} else if ("bb12".equals(tableName)) {// 表头带方案的班组交接班数据录入
			if ("表头".equals(tableType)) {
				
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("className");
				obj.setColumnshowName("分类名称");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(i);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				obj.setIsTotal(0);
				result.put("className", obj);
				i = i + 1;
				
				CostBgcsszb obj1 = new CostBgcsszb();
				obj1.setColumnCode("itemName");
				obj1.setColumnshowName("项目名称");
				obj1.setIsTotal(0);
				obj1.setObjalg(0);
				obj1.setObjdis(0);
				obj1.setObjins(null);
				obj1.setSparse(0);
				obj1.setTableName(tableName);
				obj1.setTableType(tableType);
				obj1.setTmsort(i);
				obj1.setTmused(1);
				obj1.setUnitId(unitid);
				obj1.setFormId(formId);
				obj1.setIsTotal(0);
				result.put("itemName", obj1);
				i = i + 1;
				
				CostBgcsszb obj2 = new CostBgcsszb();
				obj2.setColumnCode("instrumentName");
				obj2.setColumnshowName("仪表名称");
				obj2.setIsTotal(0);
				obj2.setObjalg(0);
				obj2.setObjdis(0);
				obj2.setObjins(null);
				obj2.setSparse(0);
				obj2.setTableName(tableName);
				obj2.setTableType(tableType);
				obj2.setTmsort(i);
				obj2.setTmused(1);
				obj2.setUnitId(unitid);
				obj2.setFormId(formId);
				obj2.setIsTotal(0);
				result.put("instrumentName", obj2);

				CostBgcsszb obj3 = new CostBgcsszb();
				obj3.setColumnCode("itemUnit");
				obj3.setColumnshowName("计量单位");
				obj3.setIsTotal(0);
				obj3.setObjalg(0);
				obj3.setObjdis(0);
				obj3.setObjins(null);
				obj3.setSparse(0);
				obj3.setTableName(tableName);
				obj3.setTableType(tableType);
				obj3.setTmsort(i);
				obj3.setTmused(1);
				obj3.setUnitId(unitid);
				obj3.setFormId(formId);
				obj3.setIsTotal(0);
				result.put("itemUnit", obj3);
				i = i + 1;

				CostBgcsszb obj4 = new CostBgcsszb();
				obj4.setColumnCode("previousReadOut");
				obj4.setColumnshowName("前表数");
				obj4.setIsTotal(0);
				obj4.setObjalg(1);
				obj4.setObjdis(4);
				obj4.setObjins(null);
				obj4.setSparse(0);
				obj4.setTableName(tableName);
				obj4.setTableType(tableType);
				obj4.setTmsort(i);
				obj4.setTmused(1);
				obj4.setUnitId(unitid);
				obj4.setFormId(formId);
				obj4.setIsTotal(0);
				result.put("previousReadOut", obj4);
				i = i + 1;

				CostBgcsszb obj5 = new CostBgcsszb();
				obj5.setColumnCode("lastReadOut");
				obj5.setColumnshowName("后表数");
				obj5.setIsTotal(0);
				obj5.setObjalg(1);
				obj5.setObjdis(4);
				obj5.setObjins(null);
				obj5.setSparse(0);
				obj5.setTableName(tableName);
				obj5.setTableType(tableType);
				obj5.setTmsort(i);
				obj5.setTmused(1);
				obj5.setUnitId(unitid);
				obj5.setFormId(formId);
				obj5.setIsTotal(0);
				result.put("lastReadOut", obj5);
				i = i + 1;
				
				CostBgcsszb obj6 = new CostBgcsszb();
				obj6.setColumnCode("calcVal");
				obj6.setColumnshowName("本班量");
				obj6.setIsTotal(0);
				obj6.setObjalg(1);
				obj6.setObjdis(4);
				obj6.setObjins(null);
				obj6.setSparse(0);
				obj6.setTableName(tableName);
				obj6.setTableType(tableType);
				obj6.setTmsort(i);
				obj6.setTmused(1);
				obj6.setUnitId(unitid);
				obj6.setFormId(formId);
				obj6.setIsTotal(0);
				result.put("calcVal", obj6);
				i = i + 1;
				
			} else if ("标题".equals(tableType)) {
				CostBgcsszb obj = new CostBgcsszb();
				obj.setColumnCode("bbTitle");
				obj.setColumnshowName("班组交接班数据录入");
				obj.setIsTotal(0);
				obj.setObjalg(0);
				obj.setObjdis(0);
				obj.setObjins(null);
				obj.setSparse(0);
				obj.setTableName(tableName);
				obj.setTableType(tableType);
				obj.setTmsort(1);
				obj.setTmused(1);
				obj.setUnitId(unitid);
				obj.setFormId(formId);
				result.put("bbTitle", obj);
			}
		} 
		return result;
	}

	/**
	 * 获取报表格式设置数据
	 * 
	 * @param tableName 表名，bb00:批次核算数据查询 bb01:
	 * @param tableType 标题 ，表头
	 * @param unitid    核算对象编码
	 * @param formId 表单编码
	 * @return
	 */
	@Override
	public List<CostBgcsszb> getCostBgcsszbList(CostBgcsszbDto dto) {
		List<CostBgcsszb> result = new ArrayList<CostBgcsszb>();
		
		List<CostBgcsszb> listTemp = new ArrayList<CostBgcsszb>();
		List<CostBgcsszb> list = new ArrayList<CostBgcsszb>();
//		LinkedHashMap<String, CostBgcsszb> costBgcsDefMap = getCostBgcsszbMap(dto.getTableName(), dto.getTableType(),
//				dto.getUnitid(), dto.getFormId());
		List<CostBgcsszbTemplate> listTemplate = getCsotBgcsTemplate(dto);
		Where where = Where.create();
		where.eq(CostBgcsszb::getTableName, dto.getTableName());
		where.eq(CostBgcsszb::getTableType, dto.getTableType());
//		if("bb09".equals(dto.getTableName())) {
//			where.eq(CostBgcsszb::getFormId, dto.getFormId());
//		}else {
			where.eq(CostBgcsszb::getUnitId, dto.getUnitid());
//		}
//		if (dto.getTmused() != 0) {
//			if(dto.getTmused()==1) {
//				where.eq(CostBgcsszb::getTmused, 1);
//			}else {
//				where.eq(CostBgcsszb::getTmused, 0);
//			}
//		}
		Order order = Order.create();
		order.orderByAsc(CostBgcsszb::getTmsort);
		list = dao.queryData(CostBgcsszb.class, where, order, null);
//		if("bb09".equals(dto.getTableName())) {
//			CostBgcsszb obj2 = new CostBgcsszb();
//			obj2.setColumnCode("instrumentName");
//			obj2.setColumnshowName("仪表位号");
//			obj2.setIsTotal(0);
//			obj2.setObjalg(0);
//			obj2.setObjdis(0);
//			obj2.setObjins(null);
//			obj2.setSparse(0);
//			obj2.setTableName("bb09");
//			obj2.setTableType("表头");
//			obj2.setTmsort(3);
//			obj2.setTmused(1);
//			obj2.setIsTotal(0);
//			result.add(obj2);
//
//			CostBgcsszb obj4 = new CostBgcsszb();
//			obj4.setColumnCode("previousReadOut");
//			obj4.setColumnshowName("前表数");
//			obj4.setIsTotal(0);
//			obj4.setObjalg(1);
//			obj4.setObjdis(4);
//			obj4.setObjins(null);
//			obj4.setSparse(0);
//			obj2.setTableName("bb09");
//			obj2.setTableType("表头");
//			obj4.setTmsort(5);
//			obj4.setTmused(1);
//			obj4.setIsTotal(0);
//			result.add(obj4);
//
//			CostBgcsszb obj5 = new CostBgcsszb();
//			obj5.setColumnCode("lastReadOut");
//			obj5.setColumnshowName("后表数");
//			obj5.setIsTotal(0);
//			obj5.setObjalg(1);
//			obj5.setObjdis(4);
//			obj5.setObjins(null);
//			obj5.setSparse(0);
//			obj2.setTableName("bb09");
//			obj2.setTableType("表头");
//			obj5.setTmsort(6);
//			obj5.setTmused(1);
//			obj5.setIsTotal(0);
//			result.add(obj5);
//		}else {
			if (dto.getCxType() == 0) {// 如果是查询模式没有保存过数据的话，从默念中获取，保证能获取到表头数据
				if (list == null || list.size() <= 0) {// 没有保存过数据，则获取默认的
					for (CostBgcsszbTemplate temp : listTemplate) {
						CostBgcsszb obj = new CostBgcsszb();
						obj =  ObjUtils.copyTo(temp, CostBgcsszb.class);
						obj.setUnitId(dto.getUnitid());
						listTemp.add(obj);
					}
				} else {// 设置 过数据
					LinkedHashMap<String, CostBgcsszb> costBgcsDataBase = new LinkedHashMap<String, CostBgcsszb>();
					for (CostBgcsszb temp : list) {
						costBgcsDataBase.put(temp.getColumnCode(), temp);
//						if(dto.getTmused()==0) {// 设置
//							listTemp.add(temp);
//						}else {
//							if(temp.getTmused()==1) {
//								listTemp.add(temp);
//							}
//						}
						listTemp.add(temp);
					}
					for (CostBgcsszbTemplate temp : listTemplate) {
						if (costBgcsDataBase != null && costBgcsDataBase.containsKey(temp.getColumnCode())) {// 数据库中已包含了默认的字段
	
						} else {// 数据库中不包含默认的字段，此 种情况 是保存错误或新加了字段
							CostBgcsszb obj = new CostBgcsszb();
							obj =  ObjUtils.copyTo(temp, CostBgcsszb.class);
							obj.setUnitId(dto.getUnitid());
							listTemp.add(obj);
						}
					}
				}
			}else {
				listTemp.addAll(list);
			}
//		}
		if(StringUtils.isNotEmpty(listTemp)) {
			result.addAll(listTemp);
		}
		String key = "COST:BGCSSZB:"+dto.getUnitid()+":"+dto.getTableName();
		if(StringUtils.isNotEmpty(list)) {
			
		}else {
			if(StringUtils.isNotEmpty(list)) {
				redis.setList(key, list);
				result.addAll(list);
			}
		}
		return result;
	}

	/**
	 * 保存数据
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public boolean saveCostBgcsszbdata(CostBgcsszbDto dto) {
		boolean result = false;
		List<CostBgcsszb> list = dto.getList();
		dto.setCxType(1);
		dto.setTmused(0);
		List<CostBgcsszb> listDataBase = getCostBgcsszbList(dto);
		List<CostBgcsszb> listUp = new ArrayList<CostBgcsszb>();
		List<CostBgcsszb> listAdd = new ArrayList<CostBgcsszb>();
		LinkedHashMap<String, CostBgcsszb> mapDataBase = new LinkedHashMap<String, CostBgcsszb>();
		LinkedHashMap<String, CostBgcsszb> map = new LinkedHashMap<String, CostBgcsszb>();
//		LinkedHashMap<String, CostBgcsszb> costBgcsDefMap = getCostBgcsszbMap(dto.getTableName(), dto.getTableType(),
//				dto.getUnitid(), dto.getFormId());
		List<CostBgcsszbTemplate> listTemplate = getCsotBgcsTemplate(dto);
		if (StringUtils.isNotEmpty(list)) {

			if (StringUtils.isNotEmpty(listDataBase)) {// 已保存了设置数据
				for (CostBgcsszb temp : listDataBase) {
					mapDataBase.put(temp.getColumnCode(), temp);
				}
				for (CostBgcsszb temp : list) {
					map.put(temp.getColumnCode(), temp);
				}
				LinkedHashMap<String, CostBgcsszb> mapAdd = new LinkedHashMap<String, CostBgcsszb>();
				for (CostBgcsszbTemplate temp : listTemplate) {
					CostBgcsszb obj = new CostBgcsszb();
					if (mapDataBase != null && mapDataBase.containsKey(temp.getColumnCode())) {// 数据库中保存过此字段
						if (map != null && map.containsKey(temp.getColumnCode())) {// 前台页面有这个字段，获取前台页面的值，前台没有这个字段，则代表本次未修改这个字段，不需要保存
							obj = map.get(temp.getColumnCode());
							obj.setId(mapDataBase.get(temp.getColumnCode()).getId());
							listUp.add(obj);
						}
					} else {// 数据库中未保存过此字段，此种情况一般出现于保存错误或增加了新字段，则需要将这些字段补充进去
						if (map != null && map.containsKey(temp.getColumnCode())) {
							obj = map.get(temp.getColumnCode());
						} else {
							obj =  ObjUtils.copyTo(temp, CostBgcsszb.class);
						}
						obj.setId(TMUID.getUID());
						obj.setUnitId(dto.getUnitid());
						listAdd.add(obj);
						mapAdd.put(obj.getColumnCode(), obj);
					}
				}

				for (CostBgcsszb temp : list) {
					if (mapDataBase != null && mapDataBase.containsKey(temp.getColumnCode())) {// 数据库中已保存了此字段，更新
						temp.setId(mapDataBase.get(temp.getColumnCode()).getId());
						listUp.add(temp);
					} else {// 数据库中未保存过此字段，添加
						if(mapAdd.containsKey(temp.getColumnCode())) {
							
						}else {
							temp.setId(TMUID.getUID());
							listAdd.add(temp);
						}
					}
				}
			} else {// 未保存过设置数据，从默认中获取过来，然后保存到数据库
				for (CostBgcsszb temp : list) {
					map.put(temp.getColumnCode(), temp);
				}
				for (CostBgcsszbTemplate temp : listTemplate) {
					CostBgcsszb obj = new CostBgcsszb();
					if (map != null && map.containsKey(temp.getColumnCode())) {// 前台传递过来的列此字段，取前台的数据
						obj = map.get(temp.getColumnCode());
					} else {
						obj =  ObjUtils.copyTo(temp, CostBgcsszb.class);
					}
					obj.setUnitId(dto.getUnitid());
					obj.setId(TMUID.getUID());
					listAdd.add(obj);
				}
			}
		}
		boolean blnAdd = false;
		if (StringUtils.isNotEmpty(listAdd)) {
			int row = dao.insertBatch(listAdd);
			if (row <= 0) {

			} else {
				blnAdd = true;
			}
		} else {
			blnAdd = true;
		}
		boolean blnUp = false;
		if (StringUtils.isNotEmpty(listUp)) {
			int row = dao.updateBatch(listUp);
			if (row <= 0) {

			} else {
				blnUp = true;
			}
		} else {
			blnUp = true;
		}
		if (blnAdd && blnUp) {
			result = true;
		}
		String key = "COST:BGCSSZB:"+dto.getUnitid()+":"+dto.getTableName();
		redis.delete(key);
		return result;
	}

	/**
	 * 报表编码和报表名称
	 * 
	 * @return
	 */
	@Override
	public List<CostBbCodeNameVo> getBbNameCode() {
		List<CostBbCodeNameVo> result = new ArrayList<CostBbCodeNameVo>();
		CostBbCodeNameVo obj = new CostBbCodeNameVo();
		obj.setBbCode("bb00");
		obj.setBbName("批次核算数据查询");
		result.add(obj);

		CostBbCodeNameVo obj1 = new CostBbCodeNameVo();
		obj1.setBbCode("bb01");
		obj1.setBbName("批次核算数据查询");
		result.add(obj1);

		CostBbCodeNameVo obj2 = new CostBbCodeNameVo();
		obj2.setBbCode("bb04");
		obj2.setBbName("装置经济核算投入产出利润表(日)");
		result.add(obj2);

		CostBbCodeNameVo obj3 = new CostBbCodeNameVo();
		obj3.setBbCode("bb07");
		obj3.setBbName("装置经济核算投入产出利润汇总表(月)");
		result.add(obj3);

		CostBbCodeNameVo obj4 = new CostBbCodeNameVo();
		obj4.setBbCode("bb08");
		obj4.setBbName("经济核算投入产出利润汇总表");
		result.add(obj4);
		
		CostBbCodeNameVo obj5 = new CostBbCodeNameVo();
		obj5.setBbCode("bb09");
		obj5.setBbName("班组交接班数据录入");
		result.add(obj5);
		
		CostBbCodeNameVo obj6 = new CostBbCodeNameVo();
		obj6.setBbCode("bb05");
		obj6.setBbName("装置经济核算投入产出利润表(周)");
		result.add(obj6);
		
		CostBbCodeNameVo obj7 = new CostBbCodeNameVo();
		obj7.setBbCode("bb06");
		obj7.setBbName("班组经济核算投入产出利润表");
		result.add(obj7);
		
		CostBbCodeNameVo obj10 = new CostBbCodeNameVo();
		obj10.setBbCode("bb10");
		obj10.setBbName("分时段报表");
		result.add(obj10);
		
		CostBbCodeNameVo obj11 = new CostBbCodeNameVo();
		obj11.setBbCode("bb11");
		obj11.setBbName("班组成本核算月度汇总表");
		result.add(obj10);
		
		return result;
	}
	
	/**
	 * 保存内表头数据（包括启用和停用标识）
	 * @param dto
	 * @return
	 */
	@Override
	public boolean saveCostBgcsszbInternal(CostBgcsszbInternalDto dto) {
		boolean result = false;
		String unitId = dto.getUnitId();// 核算对象编码
		String classId = dto.getClassId();// 分类编码
		String tableName= dto.getTableName();// 类型
		int tmUsed = dto.getTmused()==null?1:dto.getTmused();// 是否启用
		Where whereUsed = Where.create();
		whereUsed.eq(CostBgcsszbInternalUse::getUnitId, unitId);
		whereUsed.eq(CostBgcsszbInternalUse::getClassId, classId);
		whereUsed.eq(CostBgcsszbInternalUse::getTableName, tableName);

		List<CostBgcsszbInternal> listAdd = new ArrayList<CostBgcsszbInternal>();
		List<CostBgcsszbInternal> listUp = new ArrayList<CostBgcsszbInternal>();
		List<CostBgcsszbInternalUse> listUsed = new ArrayList<CostBgcsszbInternalUse>();
		listUsed = dao.queryData(CostBgcsszbInternalUse.class, whereUsed, null, null);
		if(StringUtils.isNotEmpty(listUsed)) {// 已设置过了启用或未启用数据，则更新数据
			CostBgcsszbInternalUse bean = listUsed.get(0);
			bean.setTmused(tmUsed);
			dao.update(bean);
		}else {// 未设置启用或未启用数据，需要插入一条数据
			CostBgcsszbInternalUse bean = new CostBgcsszbInternalUse();
			bean.setClassId(classId);
			bean.setId(TMUID.getUID());
			bean.setTableName(tableName);
			bean.setTmused(tmUsed);
			bean.setUnitId(unitId);
			dao.insert(bean);
		}
		List<CostBgcsszbInternal> data = dto.getList();
		// 获取模板的数据
		LinkedHashMap<String, CostBgcsszb> costBgcsDefMap = getCostBgcsszbMap(tableName, "表头",unitId, "");
		Where where = Where.create();
		where.eq(CostBgcsszbInternal::getUnitId, unitId);
		where.eq(CostBgcsszbInternal::getClassId, classId);
		where.eq(CostBgcsszbInternal::getTableName, tableName);
		List<CostBgcsszbInternal> listDataBase = dao.queryData(CostBgcsszbInternal.class, where, null, null);
		if(StringUtils.isNotEmpty(data)) {// 有设置的表头数据
			LinkedHashMap<String, CostBgcsszbInternal> mapData = new LinkedHashMap<String, CostBgcsszbInternal>();
			for(CostBgcsszbInternal temp : data) {
				mapData.put(temp.getColumnCode(), temp);
			}
			
			for(CostBgcsszbInternal temp : data) {
				mapData.put(temp.getColumnCode(), temp);
			}
			LinkedHashMap<String, CostBgcsszbInternal> mapDataBase = new LinkedHashMap<String, CostBgcsszbInternal>();
			
			if(StringUtils.isNotEmpty(listDataBase)) {// 保存过数据
				for(CostBgcsszbInternal temp : listDataBase) {
					mapDataBase.put(temp.getColumnCode(), temp);
				}
				for(CostBgcsszbInternal temp : data) {
					String columnCode = temp.getColumnCode();
					if(mapDataBase!=null && mapDataBase.containsKey(columnCode)) {// 数据库中有此字段，用前台的返回的数据
						temp.setId(mapDataBase.get(columnCode).getId());
						listUp.add(temp);
					}else {// 数据库中没有此字段，需要插入此字段的数据
						temp.setId(TMUID.getUID());
						listAdd.add(temp);
					}
				}
				
			}else {// 没有保存过数据，需要把模板中的数据插入到数据中
				for(Entry<String, CostBgcsszb> entry : costBgcsDefMap.entrySet()) {
					CostBgcsszbInternal obj = new CostBgcsszbInternal();
					if(mapData!=null && mapData.containsKey(entry.getKey())) {// 前台传过的数据，则用前台设置的数据
						obj = mapData.get(entry.getKey());
						obj.setUnitId(dto.getUnitId());
						obj.setId(TMUID.getUID());
						listAdd.add(obj);
					}else {// 前台没有这个数据，需要插入一条数据
						obj =  ObjUtils.copyTo(entry.getValue(), CostBgcsszbInternal.class);
						obj.setUnitId(dto.getUnitId());
						obj.setClassId(classId);
						obj.setId(TMUID.getUID());
						listAdd.add(obj);
					}
				}
			}
		}else {
			if(tmUsed==1) {
				if(StringUtils.isNotEmpty(listDataBase)) {// 保存过数据
					
				}else {
					for(Entry<String, CostBgcsszb> entry : costBgcsDefMap.entrySet()) {
						CostBgcsszbInternal obj = new CostBgcsszbInternal();
						obj =  ObjUtils.copyTo(entry.getValue(), CostBgcsszbInternal.class);
						obj.setUnitId(dto.getUnitId());
						obj.setClassId(classId);
						obj.setId(TMUID.getUID());
						listAdd.add(obj);
					}
				}
			}
		}
		boolean blnAdd = false;
		if(StringUtils.isNotEmpty(listAdd)) {
			int row = dao.insertBatch(listAdd);
			if(row>0) {
				blnAdd = true;
			}
		}else {
			blnAdd = true;
		}
		boolean blnUp = false;
		if(StringUtils.isNotEmpty(listUp)) {
			int row = dao.updateBatch(listUp);
			if(row>0) {
				blnUp = true;
			}
		}else {
			blnUp = true;
		}
		if(blnAdd && blnUp) {
			result = true;
		}
		return result;
	}
	
	/**
	 * 获取内表头数据（包括启用和停用标识），按项目编码获取，给设置用
	 * @param dto
	 * @return
	 */
	@Override
	public CostBgcsszbInternalDto getCostBgcsszbInternal(CostBgcsszbInternalDto dto) {
		CostBgcsszbInternalDto result = new CostBgcsszbInternalDto();
		String unitId = dto.getUnitId();
		String classId = dto.getClassId();
		String tableName= dto.getTableName();
		// 获取启用和停用标识
		Where whereUsed = Where.create();
		whereUsed.eq(CostBgcsszbInternalUse::getUnitId, unitId);
		whereUsed.eq(CostBgcsszbInternalUse::getClassId, classId);
		whereUsed.eq(CostBgcsszbInternalUse::getTableName, tableName);
		List<CostBgcsszbInternalUse> listUsed = dao.queryData(CostBgcsszbInternalUse.class, whereUsed, null, null);
		if(StringUtils.isNotEmpty(listUsed)) {// 有启用或停用的设置数据，则从数据库中获取
			CostBgcsszbInternalUse obj = listUsed.get(0);
			result.setClassId(classId);
			result.setTableName(tableName);
			result.setTmused(obj.getTmused()==null?0:obj.getTmused());
			result.setUnitId(unitId);
		}else {// 数据库中未保存过启用或停用的数据，则默认为不启用
			result.setClassId(classId);
			result.setTableName(tableName);
			result.setTmused(0);
			result.setUnitId(unitId);
		}
//		if(result.getTmused()==1) {// 启用了，需要查询内表头数据
			LinkedHashMap<String, CostBgcsszb> costBgcsDefMap = getCostBgcsszbMap(tableName, "表头",unitId, "");
			Where where = Where.create();
			where.eq(CostBgcsszbInternal::getUnitId, unitId);
			where.eq(CostBgcsszbInternal::getClassId, classId);
			where.eq(CostBgcsszbInternal::getTableName, tableName);
			Order order = Order.create();
			order.orderByAsc(CostBgcsszbInternal::getTmsort);
			List<CostBgcsszbInternal> listDataBase = dao.queryData(CostBgcsszbInternal.class, where, order, null);
			if(StringUtils.isNotEmpty(listDataBase)) {
				LinkedHashMap<String, CostBgcsszbInternal> mapDataBase = new LinkedHashMap<String, CostBgcsszbInternal>();
				for(CostBgcsszbInternal temp : listDataBase) {
					mapDataBase.put(temp.getColumnCode(), temp);
				}
				for(Entry<String, CostBgcsszb> entry : costBgcsDefMap.entrySet()) {
					if(mapDataBase!=null && mapDataBase.containsKey(entry.getKey())) {
						
					}else {
						CostBgcsszbInternal obj = new CostBgcsszbInternal();
						obj =  ObjUtils.copyTo(entry.getValue(), CostBgcsszbInternal.class);
						obj.setClassId(classId);
						obj.setTmused(0);
						listDataBase.add(obj);
					}
				}
				result.setList(listDataBase);
			}else {
				List<CostBgcsszbInternal> list = new ArrayList<CostBgcsszbInternal>();
				for(Entry<String, CostBgcsszb> entry : costBgcsDefMap.entrySet()) {
					CostBgcsszbInternal obj = new CostBgcsszbInternal();
					obj =  ObjUtils.copyTo(entry.getValue(), CostBgcsszbInternal.class);
					obj.setClassId(classId);
					list.add(obj);
				}
				if(StringUtils.isNotEmpty(list)) {
					result.setList(list);
				}
			}
//		}
		return result;
	}
	/**
	 * 获取一个核算对象的内表头数据
	 * @param dto
	 * @return key 分类编码 value map<key 字段  value 属性>
	 */
	@Override
	public LinkedHashMap<String, LinkedHashMap<String, CostBgcsszbInternal>> getCostBgcsszbInternalMap(CostBgcsszbInternalDto dto) {
		LinkedHashMap<String, LinkedHashMap<String, CostBgcsszbInternal>> result = new LinkedHashMap<String, LinkedHashMap<String, CostBgcsszbInternal>>();
		String unitId = dto.getUnitId();
		String tableName= dto.getTableName();
		// 获取核算对象内表头启用的分类数据
		Where whereUsed = Where.create();
		whereUsed.eq(CostBgcsszbInternalUse::getUnitId, unitId);
		whereUsed.eq(CostBgcsszbInternalUse::getTableName, tableName);
		whereUsed.eq(CostBgcsszbInternalUse::getTmused, 1);
		List<CostBgcsszbInternalUse> listUsed = dao.queryData(CostBgcsszbInternalUse.class, whereUsed, null, null);
		// key  分类编码 value 启用标识
		LinkedHashMap<String, Integer> mapUsed = new LinkedHashMap<String, Integer>();
		if(StringUtils.isNotEmpty(listUsed)) {
			for(CostBgcsszbInternalUse temp : listUsed) {
				mapUsed.put(temp.getClassId(), temp.getTmused()==null?0:temp.getTmused());
			}
		}
		// 获取模板数据
		LinkedHashMap<String, CostBgcsszb> costBgcsDefMap = getCostBgcsszbMap(tableName, "表头", unitId, "");
		
		// 获取内表头设置的数据
		Where where = Where.create();
		where.eq(CostBgcsszbInternal::getUnitId, unitId);
		where.eq(CostBgcsszbInternal::getTableName, tableName);
		Order order = Order.create();
		order.orderByAsc(CostBgcsszbInternal::getClassId);
		order.orderByAsc(CostBgcsszbInternal::getTmsort);
		List<CostBgcsszbInternal> listDataBase = dao.queryData(CostBgcsszbInternal.class, where, order, null);
		// key  分类编码 value  内表头数据
		LinkedHashMap<String, List<CostBgcsszbInternal>> mapInternal = new LinkedHashMap<String, List<CostBgcsszbInternal>>();
		if(StringUtils.isNotEmpty(listDataBase)) {
			for(CostBgcsszbInternal temp : listDataBase) {
				String classId = temp.getClassId()==null?"":temp.getClassId();
				List<CostBgcsszbInternal> listTemp = new ArrayList<CostBgcsszbInternal>();
				if(mapInternal!=null && mapInternal.containsKey(classId)) {
					listTemp = mapInternal.get(classId);
					listTemp.add(temp);
				}else {
					listTemp.add(temp);
				}
				mapInternal.put(classId, listTemp);
			}
		}
		for(Entry<String, Integer> entryUsed : mapUsed.entrySet()) {// 只获取启用的
			String classId = entryUsed.getKey();
			if(mapInternal!=null && mapInternal.containsKey(classId)) {// 设置过数据从设置的数据中获取
				List<CostBgcsszbInternal> listTemp = mapInternal.get(classId);
				if(StringUtils.isNotEmpty(listTemp)) {
					
					for(CostBgcsszbInternal temp : listTemp) {
						LinkedHashMap<String, CostBgcsszbInternal> mapTemp = new LinkedHashMap<String, CostBgcsszbInternal>();
						if(result!=null && result.containsKey(classId)) {
							mapTemp = result.get(classId);
							mapTemp.put(temp.getColumnCode(), temp);
						}else {
							mapTemp.put(temp.getColumnCode(), temp);
						}
						result.put(classId, mapTemp);
					}
				}
//				result.put(classId, listTemp);
			}else {// 未设置过数据从模板中获取
//				List<CostBgcsszbInternal> listTemp = new ArrayList<CostBgcsszbInternal>();
				for(Entry<String, CostBgcsszb> entry : costBgcsDefMap.entrySet()) {
					LinkedHashMap<String, CostBgcsszbInternal> mapTemp = new LinkedHashMap<String, CostBgcsszbInternal>();
					CostBgcsszbInternal obj = new CostBgcsszbInternal();
					obj =  ObjUtils.copyTo(entry.getValue(), CostBgcsszbInternal.class);
					obj.setUnitId(dto.getUnitId());
					obj.setClassId(classId);
					if(result!=null && result.containsKey(classId)) {
						mapTemp = result.get(classId);
						mapTemp.put(obj.getColumnCode(), obj);
					}else {
						mapTemp.put(obj.getColumnCode(), obj);
					}
					result.put(classId, mapTemp);
				}
			}
		}
		return result;
	}
	
	@Override
	public List<CostBgcsszbTemplate> getCsotBgcsTemplate(CostBgcsszbDto dto) {
		LinkedHashMap<String, CostBgcsszb> costBgcsDefMap = getCostBgcsszbMap(dto.getTableName(), dto.getTableType(),
				dto.getUnitid(), dto.getFormId());
		List<CostBgcsszbTemplate> result = new ArrayList<CostBgcsszbTemplate>();
		Where where = Where.create();
		List<CostBgcsszbTemplate> list = new ArrayList<CostBgcsszbTemplate>();
		List<CostBgcsszbTemplate> listAdd = new ArrayList<CostBgcsszbTemplate>();
		where.eq(CostBgcsszbTemplate::getTableName, dto.getTableName());
		where.eq(CostBgcsszbTemplate::getTableType, dto.getTableType());
		Order order = Order.create();
		order.orderByAsc(CostBgcsszbTemplate::getTmsort);
		list = dao.queryData(CostBgcsszbTemplate.class, where, order, null);
		
		if (list == null || list.size() <= 0) {// 没有保存过数据，则获取默认的
			for (Entry<String, CostBgcsszb> entry : costBgcsDefMap.entrySet()) {
				CostBgcsszbTemplate obj = new CostBgcsszbTemplate();
				obj =  ObjUtils.copyTo(entry.getValue(), CostBgcsszbTemplate.class);
				obj.setUnitId(dto.getUnitid());
				obj.setId(TMUID.getUID());
				listAdd.add(obj);
				result.add(obj);
			}
		} else {// 设置 过数据
			LinkedHashMap<String, CostBgcsszbTemplate> costBgcsDataBase = new LinkedHashMap<String, CostBgcsszbTemplate>();
			for (CostBgcsszbTemplate temp : list) {
				temp.setUnitId(dto.getUnitid());
				costBgcsDataBase.put(temp.getColumnCode(), temp);
				result.add(temp);
			}
			for (Entry<String, CostBgcsszb> entry : costBgcsDefMap.entrySet()) {
				if (costBgcsDataBase != null && costBgcsDataBase.containsKey(entry.getKey())) {// 数据库中已包含了默认的字段

				} else {// 数据库中不包含默认的字段，此 种情况 是保存错误或新加了字段
					CostBgcsszbTemplate obj = new CostBgcsszbTemplate();
					obj =  ObjUtils.copyTo(entry.getValue(), CostBgcsszbTemplate.class);
					obj.setUnitId(dto.getUnitid());
					obj.setId(TMUID.getUID());
					listAdd.add(obj);
					result.add(obj);
				}
			}
		}
		if(StringUtils.isNotEmpty(listAdd)) {
			dao.insertBatch(listAdd);
		}
		return result;
	}
	/**
	 * 将报表格式放到redis中
	 * @param dto
	 * @return
	 */
	@Override
	public List<CostBgcsszb> getCostBgcsszbListRedis(CostBgcsszbDto dto) {
		List<CostBgcsszb> result = new ArrayList<CostBgcsszb>();
		String key = "COST:BGCSSZB:"+dto.getUnitid()+":"+dto.getTableName();
		List<CostBgcsszb> list = redis.getList(key);
		if(StringUtils.isNotEmpty(list)) {
			result.addAll(list);
		}else {
			list = getCostBgcsszbList(dto);
		}
		return result;
	}
}
