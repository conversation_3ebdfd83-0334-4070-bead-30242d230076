package com.yunhesoft.leanCosting.costReport.service;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBatchOnDutyDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostDataInputDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostUnitItemCountDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchOnDuty;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostDataInputVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostInputQueryStringVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostItemForWriteVo;

public interface CostDataInputService {

	/**
	 * 获取核算项目及录入的数据
	 * @param dto
	 * @return
	 */
//	CostDataInputDto getCostDataInput(CostDataInputDto dto);

	/**
	 * 保存核算录入数据
	 * @param dto
	 * @return
	 */
	boolean saveCostInputData(CostDataInputDto dto);

	/**
	 * 获取数据
	 * @param dto
	 * @return
	 */
	boolean getInputDataByBtn(CostDataInputDto dto);

	/**
	 * 批次数据查询
	 * @param mainId 批次数据id
	 * @return
	 */
	List<CostInputQueryStringVo> CostInputDataQuery(String mainId);

	/**
	 * 获取批次数据
	 * @param writeDay 填写日期
	 * @param unitId 核算对象编码
	 * @return
	 */
	List<CostBatchInfo> getCostBatchInfo(String writeDay, String unitId);

	/**
	 * 批次查询的数据转出excel
	 * @param dto
	 */
	void costBatchInfoToExcel(CostDataInputDto dto, HttpServletResponse response);

	/**
	 * 删除
	 * @param mainId
	 * @return
	 */
	boolean deleteCostInputData(List<String> mainIdList);

	/**
	 * 获取交接班主数据的信息
	 * @param mainId
	 * @return
	 */
	CostBatchOnDuty getMainInputData(String mainId);

	/**
	 * 获取自定义添加项目下拉框数据
	 * @param unitId 核算对象编码
	 * @param writeDay 填写日期
	 * @param programId 方案编码
	 * @return
	 */
	List<CostItemForWriteVo> getAddShowTypeItem(String mainId, String unitId, String writeDay, String programId);

	/**
	 * 获取项目下的仪表，返回录入的格式
	 * @param itemId 项目编码
	 * @param mainId 主数据编码 
	 * @param unitId 核算对象编码
	 * @param writeDay 填写日期
	 * @param programId 方案编码
	 * @return
	 */
	List<CostDataInputVo> getAddShowTypeItemInstrucment(String itemId, String mainId, String unitId, String writeDay, String programId);

	/**
	 * 获取核算项目及录入的数据（判断是否保存过主数据，未保存需要保存）
	 * 
	 * @param dto
	 * @return
	 */
	CostDataInputDto getCostDataInputNew(CostBatchOnDutyDto dto);

	/**
	 * 更新交接录入主数据
	 * @param dto
	 * @return
	 */
	boolean udpateCostBatchOnDuty(CostBatchOnDutyDto dto);

	/**
	 * 是否显示获取数据按钮 
	 * @param unitId 核算对象编码
	 * @return
	 */
	boolean isShowGetDataBtn(String unitId);

	/**
	 * 获取一个核算对象是否有录入的项目
	 * @param dto
	 * @return
	 */
	JSONObject getUnitItemCount(CostUnitItemCountDto dto);

	/**
	 * 在方案切换中获取方案，如果没获取到给默认方案0
	 * @param dto
	 * @return
	 */
	List<CostDataInputDto> getCostDataInputNewList(CostBatchOnDutyDto dto);

	/**
	 * 批量保存方案的交接班数据
	 * @param dto
	 * @return
	 */
	boolean saveCostInputDataList(CostDataInputDto dto);

}
