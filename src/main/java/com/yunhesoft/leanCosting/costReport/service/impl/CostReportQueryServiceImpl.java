package com.yunhesoft.leanCosting.costReport.service.impl;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Maths;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.calcLogic.ICalcTeamProjectLogic;
import com.yunhesoft.leanCosting.costReport.entity.dto.BzybReportCxDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbInternalDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.DayReportDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.DayReportTitleDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ReportBzyhzQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.SaveReportDataDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamReportInputDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszbInternal;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryParamData;
import com.yunhesoft.leanCosting.costReport.entity.vo.CommonlyBean;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostItemForWriteVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.DayReportVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.ItemConsumption;
import com.yunhesoft.leanCosting.costReport.service.CostBgcsszbService;
import com.yunhesoft.leanCosting.costReport.service.CostReportQueryService;
import com.yunhesoft.leanCosting.costReport.service.GetCostItemInfoService;
import com.yunhesoft.leanCosting.costReport.service.GetItemConsumptionService;
import com.yunhesoft.leanCosting.costReport.service.IInstallationMonthReportService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostItemInfoVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.tds.entity.vo.TdsTableColumn;

@Service
public class CostReportQueryServiceImpl implements CostReportQueryService {
	
	@Autowired
	CostBgcsszbService cbSer;
	
	@Autowired
	EntityService entityService;
	
	@Autowired
	GetCostItemInfoService costItemSer;
	
	@Autowired
	UnitItemInfoService uiiSer;
	
	@Autowired
	ICostService costSer;
	
	@Autowired
	SysUserUtil user;
	
	@Autowired
	ICalcTeamProjectLogic  ictpLogic;
	
	@Autowired
	IUnitMethodService ims;
	
	@Autowired
	IInstallationMonthReportService imrSer;
	
	@Autowired
	GetItemConsumptionService gics;
	
	
	
	/**
	 * 获取主记录
	 * 
	 * @param unitCode
	 * @param rq
	 * @return
	 */
	@Override
	public CostSummaryInfo getTableInfo(ReportQueryDto dto) {
		Where where = Where.create();
    	
		where.eq(CostSummaryInfo::getUnitId, dto.getOrgCode());
    	where.eq(CostSummaryInfo::getReportNo, dto.getBbbh());
    	where.eq(CostSummaryInfo::getProgramId, dto.getProjectId());
    	if("sdhz".equals(dto.getQueryType())) {
    		where.eq(CostSummaryInfo::getTeamId, dto.getBzdm());
    	}
      
    	List<CostSummaryInfo> infoList = entityService.queryList(CostSummaryInfo.class, where, null);
		if (infoList != null && infoList.size() > 0) {
			return infoList.get(0);
		} else {
			return null;
		}
	}
    
    /**
     * 获取表头
     * @param dto
     * @return
     */
    @Override
    public List<TdsTableColumn> getReportHeaderList(ReportQueryDto dto1) {
        // 根据报表类型生成对应的基础数据类
        List<TdsTableColumn> res = new ArrayList<>();
        CostBgcsszbDto dto = new CostBgcsszbDto();
        dto.setUnitid(dto1.getOrgCode());
        dto.setTableType("表头");
        dto.setCxType(0);
        dto.setTmused(1);
        if("zzrb".equals(dto1.getQueryType())) {
        	dto.setTableName("bb04");
        }else if("zzyb".equals(dto1.getQueryType())||"sdhz".equals(dto1.getQueryType())) {
        	dto.setTableName("bb07");
        }else if("fsdbb".equals(dto1.getQueryType())){
        	dto.setTableName("bb10");
        }
        List<CostBgcsszb> headerList = cbSer.getCostBgcsszbList(dto);
        
        if (ObjectUtils.isEmpty(headerList)) {
            return null;
        }
        for (int i = 0; i < headerList.size(); i++) {
        	CostBgcsszb row = headerList.get(i);
            if (row.getTmused()==0) {
                continue;
            }
            
            TdsTableColumn col = new TdsTableColumn();
            col.setAlias(row.getColumnCode());
			col.setHeader(row.getColumnshowName());
			//col.setAlign("center");
			if(row.getColumnwidth()!=null&&row.getColumnwidth()!=0) {
				col.setWidth(row.getColumnwidth().toString());
			}else {
				col.setWidth("200");
			}
			
			if(row.getObjalg()==0) {
				col.setAlign("left");
			}else if(row.getObjalg()==1) {
				col.setAlign("right");
			}else if(row.getObjalg()==2) {
				col.setAlign("center");
			}
			
			//col.setComType(ids1.get(i).getComType());
            res.add(col);
        }
        return res;
    }
    
    /**
     * 获取表头
     * @param dto1
     * @return
     */
    @Override
    public Map<String, CostBgcsszb> getReportHeaderMap(ReportQueryDto dto1) {
        // 根据报表类型生成对应的基础数据类
    	Map<String, CostBgcsszb> res = new LinkedHashMap<String, CostBgcsszb>();
        CostBgcsszbDto dto = new CostBgcsszbDto();
        dto.setUnitid(dto1.getOrgCode());
        dto.setTableType("表头");
        dto.setCxType(0);
        dto.setTmused(1);
        if("zzrb".equals(dto1.getQueryType())) {
        	dto.setTableName("bb04");
        }else if("zzyb".equals(dto1.getQueryType())||"sdhz".equals(dto1.getQueryType())) {
        	dto.setTableName("bb07");
        }else if("fsdbb".equals(dto1.getQueryType())){
        	dto.setTableName("bb10");
        }
        List<CostBgcsszb> headerList = cbSer.getCostBgcsszbList(dto);
        
        if (ObjectUtils.isEmpty(headerList)) {
            return null;
        }
        for (int i = 0; i < headerList.size(); i++) {
        	CostBgcsszb row = headerList.get(i);
            if (row.getTmused()==0) {
                continue;
            }
            res.put(row.getColumnCode(),row);
        }
        return res;
    }
    
    /**
     * 成本项目
     * @param dto
     * @return
     */
    @Override
    public JSONArray getDataList(ReportQueryDto dto) {
    	JSONArray res = new JSONArray();
    	List<TdsTableColumn> headerlist = getReportHeaderList(dto);
    	
    	if (ObjectUtils.isEmpty(headerlist)) {
           return null;
        }
    	
    	Where where = Where.create();
    	
		where.eq(CostSummaryInfo::getUnitId, dto.getOrgCode());
    	where.eq(CostSummaryInfo::getReportNo, dto.getBbbh());
    	where.eq(CostSummaryInfo::getProgramId, dto.getProjectId());
    	if("sdhz".equals(dto.getQueryType())) {
    		where.eq(CostSummaryInfo::getTeamId, dto.getBzdm());
    	}
      
    	List<CostSummaryInfo> infoList = entityService.queryList(CostSummaryInfo.class, where, null);
    	if(infoList!=null&&infoList.size()>0) {
    		String xmid,flid,pid;
    		pid=infoList.get(0).getId();
    		CostItemInfoVo fl = uiiSer.getCostData(dto.getOrgCode(), dto.getTime());
    		List<Costclass> costClass = fl.getClassList();//分类
    		List<Costitem>  costitem = fl.getItemList();//项目
    		List<Costindicator> costZb = fl.getIndicatorList();//指标
    		Map<String, List<Costitem>> flMap = new LinkedHashMap<String, List<Costitem>>();//分类下的项目
    		HashMap<String,String> xm2fl=new HashMap<String,String>();//根据项目ID得到所属的分类ID
    		if(costitem!=null&&costitem.size()>0) {
    			for(Costitem xm : costitem) {
    				xmid=xm.getId();
    				flid=xm.getPid();
    				xm2fl.put(xmid, flid);
    				if(flMap.containsKey(flid)) {
    					List<Costitem> iList = flMap.get(flid);
    					iList.add(xm);
    					flMap.put(flid,iList);
    				}else {
    					List<Costitem> iList = new ArrayList<Costitem>();
    					iList.add(xm);
    					flMap.put(flid,iList);
    				}
    			}
    		}
    		
    		Where where1 = Where.create();
        	where1.eq(CostSummaryItemData::getPid, pid);
        	List<CostSummaryItemData> xmData = entityService.queryList(CostSummaryItemData.class, where1, null);
        	Map<String, CostSummaryItemData> xmMap = new LinkedHashMap<String, CostSummaryItemData>();
        	HashMap<String,String> syflm=new HashMap<String,String>();//有数据的分类才会有用
        	if(xmData!=null&&xmData.size()>0) { 
    			for(CostSummaryItemData xml:xmData) {
    				xmid=xml.getItemId();
    				if (xm2fl.containsKey(xmid)) {
    					syflm.put(xm2fl.get(xmid), "1");
    				}
    				xmMap.put(xmid, xml);
    			}
        	}
        	
        	Where where2 = Where.create();
        	where2.eq(CostSummaryParamData::getPid, pid);
        	Map<String, CostSummaryParamData> zbMap = new LinkedHashMap<String, CostSummaryParamData>();
        	List<CostSummaryParamData> zbData = entityService.queryList(CostSummaryParamData.class, where1, null);
        	if(zbData!=null&&zbData.size()>0) { 
    			for(int i=0;i<zbData.size();i++) {
    				zbMap.put(zbData.get(i).getParamId(), zbData.get(i));
    			}
        	}
        	//表头
        	Map<String, CostBgcsszb> headMap = this.getReportHeaderMap(dto);
        	//内表头
        	CostBgcsszbInternalDto dto2 = new CostBgcsszbInternalDto();
    		dto2.setUnitId(dto.getOrgCode());
    		if("zzrb".equals(dto.getQueryType())) {
    			dto2.setTableName("bb04");
            }else if("zzyb".equals(dto.getQueryType())||"sdhz".equals(dto.getQueryType())) {
            	dto2.setTableName("bb07");
            }else if("fsdbb".equals(dto.getQueryType())){
            	dto2.setTableName("bb10");
            }
    		Map<String, LinkedHashMap<String, CostBgcsszbInternal>> nbt = cbSer.getCostBgcsszbInternalMap(dto2);//内表头
    		HashMap<String,HashMap<String,Double>> flhzm=new HashMap<String,HashMap<String,Double>>();
    		HashMap<String,Map<String,String>> flrm=new HashMap<String,Map<String,String>>();
        	
        	if(costClass!=null&&costClass.size()>0) {
        		Integer fyxm,bbxs;
        		Double val,dh,dhde;
    			for(Costclass xfl:costClass) {
    				flid=xfl.getId();
    				if (!syflm.containsKey(flid)) {
    					continue;//没有数据的分类不显示
    				}
    				if(nbt.containsKey(flid)) {
    					Map<String, CostBgcsszbInternal> nbtMap = nbt.get(flid);
    					Map<String, String> map = new HashMap<>();
    					map.put("ctype", "nbt");
    					for (Map.Entry<String, CostBgcsszbInternal> entry : nbtMap.entrySet()) {
    					    String key = entry.getKey();
    					    CostBgcsszbInternal value = entry.getValue();
    					    
    					    if(value.getTmused()==1) {
    					    	map.put(key, value.getColumnshowName());
    					    }else {
    					    	map.put(key, "");
    					    }
    					    // 处理每个key-value对
    					}
    					res.add(map);//分类
    				}
    				Map<String, String> map = new HashMap<>();
    				map.put("ctype", "fl");
    				map.put("itemName", xfl.getCcname());
    				flrm.put(flid, map);
    				res.add(map);//分类
    				if(flMap.containsKey(flid)) {
    					//用分类ID到map1找项目，得到分类下的项目，再用项目找是否有项目数据，无数据的继续下一个项目，有数据时添加到报表。
    					List<Costitem> iList = flMap.get(flid);
    					int yn1=iList.size();
    					for(int l=0;l<yn1;l++) {
    						Costitem cyn=iList.get(l);
    						xmid=cyn.getId();
    						if (xmid==null) {
    							continue;//项目ID不对
    						}
    						bbxs=cyn.getIsShowInQuery();
    						if (bbxs==null) {
    							bbxs=1;
    						}
    						if (bbxs==0) {
    							continue;//报表查询不显示的项目
    						}
    						fyxm=cyn.getIsFeeItem();
    						if (fyxm==null) {
    							fyxm=0;
    						}
    						if(xmMap.containsKey(xmid)) {
    							CostSummaryItemData xmdata = xmMap.get(xmid);
    							//有数据 加一行
    							Map<String, String> row = new HashMap<String,String>();
    							HashMap<String,Double> hzm=null;
    							if (flhzm.containsKey(flid)) {
    								hzm=flhzm.get(flid);
    							} else {
    								hzm=new HashMap<String,Double>();
    							}
    							row.put("itemName", iList.get(l).getItemname());
    							row.put("itemUnit", iList.get(l).getItemunit());
    							row.put("itemPrice",this.getColumnValue("itemPrice", xmdata.getItemPrice(), fyxm, headMap,hzm));
    							dhde=xmdata.getBaseUnitConsumption();
    							row.put("dhde", this.getColumnValue("dhde", dhde, fyxm, headMap,hzm));
    							if("zzyb".equals(dto.getQueryType())) {
    								val = xmdata.getWriteConsumption();
    							}else {
    								val = xmdata.getConsumption(); 
    							}
    							row.put("xhl", this.getColumnValue("xhl", val, fyxm, headMap,hzm));
    							dh=xmdata.getUnitConsumption();
    							row.put("dh", this.getColumnValue("dh", dh, fyxm, headMap,hzm));
    							row.put("dwcb", this.getColumnValue("dwcb", xmdata.getUnitCost(), fyxm, headMap,hzm));
    							row.put("zcb", this.getColumnValue("zcb", xmdata.getItemCost(), fyxm, headMap,hzm));
    							val = (dh != null ? dh : 0.0) - (dhde != null ? dhde : 0.0);
    							row.put("dhcz", this.getColumnValue("dhcz", val, fyxm, headMap,hzm));
    							row.put("nh", this.getColumnValue("nh", xmdata.getBaseCost(), fyxm, headMap,hzm));
    							flhzm.put(flid, hzm);
    							res.add(row);//项目
    						}
    					}
    				}
    			}
        	}
        	
        	//循环分类
        	Double val;
    		String cn;
    		for (Entry<String,Map<String,String>> yn:flrm.entrySet()) {
    			flid=yn.getKey();
    			if (flhzm.containsKey(flid)) {
    				HashMap<String,Double> hzm=flhzm.get(flid);
    				Map<String,String> row=yn.getValue();
    				for (Entry<String,Double> yl:hzm.entrySet()) {
    					cn=yl.getKey();
    					if (StringUtils.isEmpty(cn)) {
    						continue;
    					}
    					val=yl.getValue();
    					row.put(cn, this.getColumnValue(cn, val, 0, headMap,null));
    				}
    			}
    		}
        	
        	if(costZb!=null&&costZb.size()>0) { 
        		Map<String, String> map = new HashMap<>();
        		map.put("ctype", "fl");
				map.put("itemName", "核算指标");
				res.add(map);//分类
				String zbid;
				Integer isvsb;
    			for(int i=0;i<costZb.size();i++) {
    				Costindicator zb=costZb.get(i);
    				zbid=zb.getId();
    				isvsb=zb.getIsSelShow();
    				if (isvsb==null) {
    					isvsb=1;
    				}
    				if (isvsb==0) {
    					continue;
    				}
    				if(zbMap.containsKey(zbid)) {
    					CostSummaryParamData zbdata = zbMap.get(zbid);
    					Map<String, String> row = new HashMap<>();
    					row.put("itemName", zb.getCpname());
    					row.put("itemUnit", zb.getItemunit());
    					row.put("itemPrice", "");
    					row.put("dwcbde", "");
    					row.put("dhde", this.getColumnValue("dhde", zbdata.getBaseVal(), 0, headMap,null));
    					row.put("xhl", this.getColumnValue("xhl", zbdata.getCalcVal(), 0, headMap,null));
    					row.put("dh", "");
    					row.put("dwcb", "");
    					row.put("zcb", "");
    					row.put("dhcz", "");
    					row.put("dwcbcz", "");
    					row.put("nh", "");
    					res.add(row);//指标
    				}
    			}
        	}
    	}
        return res;
    }
    
    /**
     * @category	格式化值
     * @param cn
     * @param val
     * @param isfy
     * @param cfm
     * @return
     */
    private String getColumnValue(String cn,Double val,Integer isfy,Map<String,CostBgcsszb> cfm,HashMap<String,Double> fm) {
    	CostBgcsszb fb = cfm.get(cn);
    	Double dv=0.0;
    	String sv="";
    	if (val!=null) {
    		dv=val;
    		if (isfy==1) {
        		if ("itemPrice".equals(cn) || "xhl".equals(cn)) {
        			dv=0.0;//费用项目无单价和消耗量
        		}
        	}
    		if (dv.compareTo(0.0)!=0) {
    			sv=String.valueOf(dv);
        		if(fb!=null) {
        			StringBuffer Format = new StringBuffer("#");
        			Integer num = fb.getObjdis();
        			if (num==null) {
        				num=4;
        			}
        			if (num>0) {
        				Format.append(".");
        				for(int o=0;o<num;o++){
        					Format.append("#");
        				}
        			}
        			DecimalFormat df = new DecimalFormat(Format.toString());
        			sv = df.format(dv);
        			if (fm!=null) {
        				Integer hz=fb.getIsTotal();
        				if (hz==null) {
        					hz=0;
        				}
        				if (hz==1) {
        					if (fm.containsKey(cn)) {
        						fm.put(cn,fm.get(cn)+Maths.round(dv,num));
        					} else {
        						fm.put(cn, Maths.round(dv,num));
        					}
        				}
        			}
        		}
    		}
    	}
		return sv;
    }
    
    /**
     * 获取标题
     * @return
     */
    @Override
    public DayReportTitleDto getTitle(ReportQueryDto dto) {
    	DayReportTitleDto res = new DayReportTitleDto();
    	CostBgcsszbDto dto1 = new CostBgcsszbDto();
    	dto1.setUnitid(dto.getOrgCode());
    	dto1.setTableType("标题");
    	dto1.setCxType(0);
    	dto1.setTmused(1);
        if("zzrb".equals(dto.getQueryType())) {
        	dto1.setTableName("bb04");
        }else if("zzyb".equals(dto.getQueryType())||"sdhz".equals(dto.getQueryType())) {
        	dto1.setTableName("bb07");
        }
        List<CostBgcsszb> headerList = cbSer.getCostBgcsszbList(dto1);
        if(headerList!=null&&headerList.size()>0) {
        	res.setTitle(headerList.get(0).getColumnshowName());
		}
        SysUser user = SysUserHolder.getCurrentUser();
        if(user!=null) {
        	List<Costuint> list = costSer.getCostuintListByOrgId(user.getOrgId(), 2);
    		if(list!=null&&list.size()>0) {
    			for(int i=0;i<list.size();i++) {
    				CommonlyBean info = new CommonlyBean();
    				info.setId(list.get(i).getId());
    				info.setName(list.get(i).getName());
    				if(dto.getOrgCode().equals(list.get(i).getId())) {
    					res.setHsdy(list.get(i).getName());
    				}
    			}
    		}
        }
		return res;
    }
    
    /**
	 * 保存分析内容
	 */
	@Override
	public String saveData(String remark,String unitCode,String rq,String projectId) {
		String result = "error";
		Where where = Where.create();
        where.eq(CostSummaryInfo::getUnitId, unitCode);
        where.eq(CostSummaryInfo::getReportNo, rq);
        where.eq(CostSummaryInfo::getProgramId, projectId);
        
        List<CostSummaryInfo> infoList = entityService.queryList(CostSummaryInfo.class, where, null);
        
        if(infoList!=null&&infoList.size()>0) {
        	CostSummaryInfo info = infoList.get(0);
        	info.setRemark(remark);
        	List<CostSummaryInfo> updList = new ArrayList<CostSummaryInfo>();
        	updList.add(info);
        	entityService.rawUpdateByIdBatch(updList);
			result = "success";
        }
		return result; 
	}
    
	/**
     * 替换字符列表查询
     * 
     * @param queryParam 检索条件
     * @param page       分页信息
     * @return
     */
    @Override
    public List<CostSummaryInfo> queryReportList(String unitCode,String starTime, String endTime, Pagination<?> page) {
    	List<CostSummaryInfo> res = new ArrayList<CostSummaryInfo>();
        // where 条件
    	if(unitCode!=null&&unitCode.length()>0&&starTime!=null&&starTime.length()>0&&endTime!=null&&endTime.length()>0) {
	    	List<String> listrq = new ArrayList<String>();// 查询子表数据用-日期区间
	    	listrq.add(starTime);
	    	listrq.add(endTime);
	    	
	    	List<String> unitCodeS = Arrays.asList(unitCode.split(","));
	    	
	        Where where = Where.create();
	        where.in(CostSummaryInfo::getUnitId, unitCodeS.toArray());
	        where.between(CostSummaryInfo::getReportYf, listrq.toArray());
	        // 排序
	        Order order = Order.create();
	        order.orderByDesc(CostSummaryInfo::getCreateTime);
	        
	        res = entityService.queryData(CostSummaryInfo.class, where, order, page);
    	}
        return res;
    }
	
	/**
     * 保存报表数据
     */
    @Override
    public boolean saveReportData(SaveReportDataDto param) {
        boolean bln = false;
        if (param != null && StringUtils.isNotEmpty(param.getData())) {
            List<CostSummaryInfo> insertList = new ArrayList<CostSummaryInfo>();
            List<CostSummaryInfo> upateList = new ArrayList<CostSummaryInfo>();
            List<CostSummaryInfo> deleteList = new ArrayList<CostSummaryInfo>();
            JSONArray datas = JSONArray.parseArray(param.getData());
            SysUser userInfo = user.getCurrentUser();
            
            if (datas != null) {
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject row = datas.getJSONObject(i);
                    Integer rowFlag = row.getInteger("TDSROW_rowFlag"); // 标识字段
                    CostSummaryInfo bean = new CostSummaryInfo();
                    String id = row.getString("id");// 主键
                    bean.setId(id);
                    bean.setUnitId(row.getString("unitId"));
                    bean.setProgramId(row.getString("programId"));
                    bean.setTeamId(row.getString("teamId"));
                    bean.setBeginTime(row.getString("beginTime"));
                    bean.setEndTime(row.getString("endTime"));
                    bean.setRemark(row.getString("remark"));
                    bean.setReportMemo(row.getString("reportMemo"));
                    bean.setReportPerson(userInfo.getRealName());
                    bean.setReportDay(row.getString("reportDay"));
                    bean.setReportYf(row.getString("reportYf"));
                    if(bean.getReportYf()!=null&&bean.getReportYf().length()>0) {
                    	String reportNo = this.getReportNo(bean.getReportYf());
                    	bean.setReportNo(reportNo);
                    }
                    if (rowFlag == null || rowFlag == 0) {// 添加
                        bean.setId(TMUID.getUID());
                        insertList.add(bean);
                    } else if (rowFlag == 1) {// 修改
                        bean.setUpdateTime(new Date());
                        upateList.add(bean);
                    } else {// 删除
                        if (StringUtils.isNotEmpty(id)) {
                            deleteList.add(bean);
                        }
                    }
                }
            }
            if (StringUtils.isNotEmpty(insertList)) {
            	bln = entityService.insertBatch(insertList) > 0? true : false;// 添加
            	TeamReportInputDto reportinfo = new TeamReportInputDto();
            	reportinfo.setPid(insertList.get(0).getId());
            	reportinfo.setWriteDay(insertList.get(0).getReportDay());
            	reportinfo.setUnitId(insertList.get(0).getUnitId());
            	reportinfo.setProgramId(insertList.get(0).getProgramId());
            	reportinfo.setTeamId(insertList.get(0).getTeamId());
            	reportinfo.setBegintime(insertList.get(0).getBeginTime());
            	reportinfo.setEndtime(insertList.get(0).getEndTime());
            	ictpLogic.calcPeriodReport(reportinfo);
            }
            if (StringUtils.isNotEmpty(upateList)) {
            	bln = entityService.updateByIdBatch(upateList) > 0? true : false;// 更新\
            	TeamReportInputDto reportinfo = new TeamReportInputDto();
            	reportinfo.setPid(upateList.get(0).getId());
            	reportinfo.setWriteDay(upateList.get(0).getReportDay());
            	reportinfo.setUnitId(upateList.get(0).getUnitId());
            	reportinfo.setProgramId(upateList.get(0).getProgramId());
            	reportinfo.setTeamId(upateList.get(0).getTeamId());
            	reportinfo.setBegintime(upateList.get(0).getBeginTime());
            	reportinfo.setEndtime(upateList.get(0).getEndTime());
            	ictpLogic.calcPeriodReport(reportinfo);
            }
            if (StringUtils.isNotEmpty(deleteList)) {
            	bln = entityService.deleteByIdBatch(deleteList) > 0? true : false;// 删除
            }
        }
        return bln;

    }
    
    /**
     * 获取报表编码
     * @param reportYf
     * @return
     */
    public String getReportNo(String reportYf) {
    	Where where = Where.create();
        where.eq(CostSummaryInfo::getReportYf, reportYf);
        
        List<CostSummaryInfo> list = entityService.queryData(CostSummaryInfo.class, where, null, null);
        
        Integer num = list.size()+1;
        
        return reportYf.replace("-", "")+"0000"+num;
    }
    
    /**
	 * 获取机构下拉
	 */
	@Override
	public List<CommonlyBean> getLxOperation(String uintId) {
		List<CommonlyBean> result = new ArrayList<CommonlyBean>();
		List<CommonlyBean> newResult = new ArrayList<CommonlyBean>();
		CommonlyBean info2 = new CommonlyBean();
		info2.setId("0");
		info2.setName("装置");
		//result.add(info2);
		newResult.add(info2);
		SysUser user = SysUserHolder.getCurrentUser();
		if (user != null) {
			MethodQueryDto queryDto = new MethodQueryDto();
			queryDto.setUnitid(uintId);
			queryDto.setObjType("org");
			List<Costunitoperator> list = ims.getCostunitoperatorList(queryDto);
			if (list != null && list.size() > 0) {
				List<String> ids = new ArrayList<String>();
				for (int i = 0; i < list.size(); i++) {
					CommonlyBean info = new CommonlyBean();
					info.setId(list.get(i).getObjid());
					ids.add(list.get(i).getObjid());
					result.add(info);
				}
				Where where = Where.create();
				where.in(SysOrg::getId, ids.toArray());
				List<SysOrg> infoList = entityService.queryList(SysOrg.class, where, null);
				if(infoList!=null&&infoList.size()>0) {
					for (int i = 0; i < infoList.size(); i++) {
						SysOrg info = infoList.get(i);
						for(int l=0;l<result.size();l++) {
							if(result.get(l).getId().equals(info.getId())) {
								String orgname = info.getOrgname();
								if(orgname!=null&&orgname.trim().length()>0) {
									result.get(l).setName(info.getOrgname());
									newResult.add(result.get(l));
									break;
								}
							}
						}
					}
				}
			}
		}
		return newResult;
	}
	
	
	/**
	 * 获取操作机构
	 */
	private List<CommonlyBean> getOperation(String uintId) {
		List<CommonlyBean> result = new ArrayList<CommonlyBean>();
		SysUser user = SysUserHolder.getCurrentUser();
		if (user != null) {
			MethodQueryDto queryDto = new MethodQueryDto();
			queryDto.setUnitid(uintId);
			queryDto.setObjType("org");
			List<Costunitoperator> list = ims.getCostunitoperatorList(queryDto);
			if (list != null && list.size() > 0) {
				List<String> ids = new ArrayList<String>();
				for (int i = 0; i < list.size(); i++) {
					CommonlyBean info = new CommonlyBean();
					info.setId(list.get(i).getObjid());
					ids.add(list.get(i).getObjid());
					result.add(info);
				}
				Where where = Where.create();
				where.in(SysOrg::getId, ids.toArray());
				List<SysOrg> infoList = entityService.queryList(SysOrg.class, where, null);
				if(infoList!=null&&infoList.size()>0) {
					for (int i = 0; i < infoList.size(); i++) {
						SysOrg info = infoList.get(i);
						for(int l=0;l<result.size();l++) {
							if(result.get(l).getId().equals(info.getId())) {
								result.get(l).setName(info.getOrgname());
								break;
							}
						}
					}
				}
			}
		}
		return result;
	}
	
	/**
	 * 查询主表
	 * @param dto
	 * @return
	 */
	@Override
	public List<CostSummaryInfo> getBzybInfo(ReportBzyhzQueryDto dto){
		List<CostSummaryInfo> result = new ArrayList<CostSummaryInfo>();
		Where where = Where.create();
		where.eq(CostSummaryInfo::getUnitId, dto.getUnitId());
		where.eq(CostSummaryInfo::getProgramId, dto.getProjectId());
		where.eq(CostSummaryInfo::getReportNo, dto.getYf().replace("-", "")+"000000");
		result = entityService.queryList(CostSummaryInfo.class, where, null);
		
		if (result == null || result.size() == 0) {
			// 创建
			SysUser userInfo = user.getCurrentUser();
			CostSummaryInfo bean = new CostSummaryInfo();
			bean.setId(TMUID.getUID());
			bean.setUnitId(dto.getUnitId());
			bean.setReportNo(dto.getYf().replace("-", "")+"000000");
			bean.setProgramId(dto.getProjectId());
			bean.setTeamId(null);
			bean.setBeginTime(dto.getKssj());
			bean.setEndTime(dto.getJzsj());
			bean.setReportPerson(userInfo.getRealName());
			bean.setReportDay(dto.getRq());
            result.add(bean);
            entityService.insertBatch(result);
            
            //调文哥方法
            
		}
		return result;
	}
	
	/**
	 * 获取map
	 * @param xhlList
	 * @return
	 */
	private Map<String, DayReportVo> getXhlArray(List<DayReportVo> xhlList) {
		Map<String, DayReportVo> Map = new LinkedHashMap<String, DayReportVo>();
		if(xhlList!=null&&xhlList.size()>0) {
			for(int i=0;i<xhlList.size();i++) {
				Map.put(xhlList.get(i).getTeamId(), xhlList.get(i));
			}
		}
		return Map;
	}
	
	/**
	 * 查询数据
	 */
	@Override
	public List<DayReportDto> getBzybData(ReportBzyhzQueryDto dto) {
		List<DayReportDto> result = new ArrayList<DayReportDto>();
		Where where = Where.create();
		where.eq(CostSummaryInfo::getUnitId, dto.getUnitId());
		where.eq(CostSummaryInfo::getProgramId, dto.getProjectId());
		where.eq(CostSummaryInfo::getReportNo, dto.getYf().replace("-", "")+"000000");
		List<CostSummaryInfo> infoList = entityService.queryList(CostSummaryInfo.class, where, null);
		if (infoList == null || infoList.size() == 0) {
			// 没有数据时，应该调用班组月汇总的自动生成
			TeamReportInputDto reportinfo = new TeamReportInputDto();
			reportinfo.setUnitId(dto.getUnitId());
			reportinfo.setWriteDay(dto.getYf());// yyyy-mm格式的月份
			reportinfo.setProgramId(dto.getProjectId());
			reportinfo.setBegintime(dto.getKssj());
			reportinfo.setEndtime(dto.getJzsj());
			String cshr = this.ictpLogic.calcTeamMonthReportAuto(reportinfo);
			if ("".equals(cshr)) {
				infoList = entityService.queryList(CostSummaryInfo.class, where, null);// 再次检索制表信息
			} else {
				return result;// 初始化出现问题，不再继续
			}
		}
		if (infoList != null && infoList.size() > 0) {
			CostSummaryInfo info = infoList.get(0);
			Map<String, List<DayReportVo>> xhkMap = new LinkedHashMap<String, List<DayReportVo>>();
			HashMap<String, Double> djm = new HashMap<String, Double>();// 装置项目单价
			if ("1".equals(dto.getIsCxhq())) {
				info.setBeginTime(dto.getKssj());
				info.setEndTime(dto.getJzsj());
//				entityService.update(info);
				//价格
				HashMap<String, Double> jgm = this.costItemSer.getItemPrice("priceConfig", info.getEndTime());
				if (jgm==null) {
					jgm=new HashMap<String, Double>();
				}
				// 重新提取数据
				HashMap<String, Costitem> xmm = new HashMap<String, Costitem>();
				
				List<Costitem> cil = uiiSer.getItem(dto.getUnitId(), info.getEndTime());
				if (cil != null) {
					for (Costitem x : cil) {
						xmm.put(x.getId(), x);
					}
				}
				MethodQueryDto queryDto = new MethodQueryDto();
				queryDto.setUnitid(dto.getUnitId());
				queryDto.setObjType("org");
				List<Costunitoperator> tlist = ims.getCostunitoperatorList(queryDto);
				HashMap<String, HashMap<String, String>> ydm = new HashMap<String, HashMap<String, String>>();
				List<ItemConsumption> bzsjl = this.gics.getTeamPeriodItemConsumption(dto.getUnitId(),
						info.getBeginTime(), info.getEndTime(), info.getProgramId(), xmm);
				if (bzsjl != null) {
					// 先求出项目单价
					Double xhl, dj, val;
					String itemid, teamid,erpcode;
					HashMap<String, Double> dxhm = new HashMap<String, Double>();// 装置项目消耗
					HashMap<String, Double> cbm = new HashMap<String, Double>();// 装置项目总金额
					for (ItemConsumption x : bzsjl) {
						itemid = x.getItemId();
						teamid= x.getTeamId();
						if (ydm.containsKey(itemid)) {
							ydm.get(itemid).put(teamid, "1");
						} else {
							HashMap<String, String> dm = new HashMap<String, String>();
							dm.put(teamid, "1");
							ydm.put(itemid, dm);
						}
						// 消耗量
						xhl = x.getConsumption();
						if (xhl == null) {
							xhl = 0.0;
						}
						if (dxhm.containsKey(itemid)) {
							dxhm.put(itemid, dxhm.get(itemid) + xhl);
						} else {
							dxhm.put(itemid, xhl);
						}
						// 得到总的金额，用于后续计算项目单价
						dj = x.getItemPrice();
						if (dj == null) {
							dj = 0.0;
						}
						if (cbm.containsKey(itemid)) {
							cbm.put(itemid, cbm.get(itemid) + dj * xhl);
						} else {
							cbm.put(itemid, dj * xhl);
						}
					}
					// 项目单价
					for (Entry<String, Double> xh : dxhm.entrySet()) {
						itemid = xh.getKey();
						dj=0.0;
						if (xmm.containsKey(itemid)) {
							Costitem wz=xmm.get(itemid);
							erpcode=wz.getErpcode();
							if (erpcode!=null) {
								if (jgm.containsKey(erpcode)) {
									dj=jgm.get(erpcode);
								}
							}
						}
						xhl = xh.getValue();
						// 单价，需要根据总成本和量重新计算
						if (cbm.containsKey(itemid)) {
							val = cbm.get(itemid);
							if (xhl.compareTo(0.0) != 0) {
								dj = val / xhl;
							}
						}
						djm.put(itemid, Maths.round(dj,2));
					}
					for (ItemConsumption x : bzsjl) {
						itemid = x.getItemId();
						teamid = x.getTeamId();
						if ("0".equals(teamid)){
							continue;
						}
						if (djm.containsKey(itemid)) {
							dj = djm.get(itemid);
						} else {
							dj = 0.0;
						}
						DayReportVo bean = new DayReportVo();
						bean.setDj(dj);
						bean.setXhl(Maths.round(x.getConsumption(),3));
						bean.setTeamId(teamid);
						bean.setItemId(itemid);
						List<DayReportVo> l = null;
						if (xhkMap.containsKey(itemid)) {
							l = xhkMap.get(itemid);
						} else {
							l = new ArrayList<DayReportVo>();
						}
						l.add(bean);
						xhkMap.put(itemid, l);
					}
					// 添加没有量的班组
					if (tlist != null) {
						for (Entry<String, Double> e : dxhm.entrySet()) {
							itemid = e.getKey();
							if (ydm.containsKey(itemid)) {
								HashMap<String, String> dm = ydm.get(itemid);
								for (Costunitoperator tid : tlist) {
									teamid = tid.getObjid();
									if (!dm.containsKey(teamid)) {
										if (djm.containsKey(itemid)) {
											dj = djm.get(itemid);
										} else {
											dj = 0.0;
										}
										DayReportVo bean = new DayReportVo();
										bean.setDj(dj);
										bean.setXhl(0.0);
										bean.setTeamId(teamid);
										bean.setItemId(itemid);
										List<DayReportVo> l = null;
										if (xhkMap.containsKey(itemid)) {
											l = xhkMap.get(itemid);
										} else {
											l = new ArrayList<DayReportVo>();
										}
										l.add(bean);
										xhkMap.put(itemid, l);
									}
								}
							}
						}
					}
				}
			} else {
				// 从已保存的数据提取
				List<CostSummaryItemData> rbData = new ArrayList<CostSummaryItemData>();
				Where where1 = Where.create();
				where1.eq(CostSummaryItemData::getPid, info.getId());
				rbData = entityService.queryList(CostSummaryItemData.class, where1, null);
				if (rbData != null) {
					// 制作消耗量map
					String teamid,itemid;
					for (CostSummaryItemData rb : rbData) {
						teamid=rb.getTeamId();
						if ("0".equals(teamid)){
							continue;
						}
						itemid=rb.getItemId();
						djm.put(itemid, rb.getItemPrice());
						DayReportVo bean = new DayReportVo();
						bean.setDj(rb.getItemPrice());
						bean.setXhl(Maths.round(rb.getConsumption(),3));
						bean.setTeamId(teamid);
						bean.setItemId(itemid);
						List<DayReportVo> val = new ArrayList<DayReportVo>();
						if (xhkMap.containsKey(rb.getItemId())) {
							val = xhkMap.get(rb.getItemId());
						}
						val.add(bean);
						xhkMap.put(rb.getItemId(), val);
					}
				}
			}
			// 录入显示的项目
			List<CostItemForWriteVo> flList = costItemSer.sumWriteItemInfo(dto.getUnitId(), dto.getJzsj(),
					dto.getProjectId());
			Map<String, Integer> flMap = new LinkedHashMap<String, Integer>();
			List<DayReportDto> temper = new ArrayList<DayReportDto>();
			if (flList != null && flList.size() > 0) {
				Double val;
				String itemid;
				for (CostItemForWriteVo tt : flList) {
					itemid = tt.getItemId();
					if (xhkMap.containsKey(itemid)) {
						// 有数据的项目才加载
						DayReportDto bean = new DayReportDto();
						bean.setWzdm(itemid);
						bean.setFldm(tt.getClassId());
						bean.setFl(tt.getClassName());
						bean.setXm(tt.getItemName());
						bean.setDw(tt.getItemUnit());
						if (djm.containsKey(itemid)) {
							val=djm.get(itemid);
							bean.setDj(Maths.round(val, 2));
						}
						bean.setEdit(false);
						bean.setRowspan(0);
						bean.setColspan(0);
						List<DayReportVo> xhlList = xhkMap.get(tt.getItemId());
						bean.setXhlList(xhlList);
						bean.setXhlArray(this.getXhlArray(xhlList)); 
						if (flMap.containsKey(tt.getClassId())) {
							Integer count = flMap.get(tt.getClassId());
							flMap.put(tt.getClassId(), count + 1);
						} else {
							flMap.put(tt.getClassId(), 1);
						}
						temper.add(bean);
					}
				}
				if (flMap.size() > 0) {
					for (String key : flMap.keySet()) {
						if (temper != null && temper.size() > 0) {
							for (int i = 0; i < temper.size(); i++) {
								if (key.equals(temper.get(i).getFldm())) {
									result.add(temper.get(i));
								}
							}
						}
					}
				}
				if (result != null && result.size() > 0) {
					for (int i = 0; i < flList.size(); i++) {
						// 处理分类合并单元格信息
						for (int l = 0; l < result.size(); l++) {
							DayReportDto bean = result.get(l);
							if (flList.get(i).getClassId().equals(bean.getFldm())) {
								Integer rowspan = flMap.get(bean.getFldm());
								bean.setRowspan(rowspan);
								bean.setColspan(1);
								break;
							}
						}
					}
				}
			}
		}
		return result;
	}
	
	/**
	 * 保存班组月报数据
	 */
	@Override
	public String saveBzybData(List<DayReportDto> list, ReportBzyhzQueryDto dto) {
		String result = "error";
		if (list != null & list.size() > 0) {
			Where where = Where.create();
			where.eq(CostSummaryInfo::getUnitId, dto.getUnitId());
			where.eq(CostSummaryInfo::getProgramId, dto.getProjectId());
			where.eq(CostSummaryInfo::getReportNo, dto.getYf().replace("-", "")+"000000");
			List<CostSummaryInfo> iniflist = entityService.queryList(CostSummaryInfo.class, where, null);
			
			CostSummaryInfo infoObj = null;
			if (iniflist != null && iniflist.size() > 0) {
				infoObj = iniflist.get(0);
				
				//if("1".equals(dto.getIsCxhq())) {
					infoObj.setBeginTime(dto.getKssj());
					infoObj.setEndTime(dto.getJzsj());
					infoObj.setReportDay(dto.getRq());
					//更新时间
					entityService.rawUpdateById(infoObj);
				//}
			}
			
			if (infoObj != null) {
				String pid = infoObj.getId();
				Where where1 = Where.create();
				where1.eq(CostSummaryItemData::getPid, pid);
				List<CostSummaryItemData> rbData = entityService.queryList(CostSummaryItemData.class, where1, null);
				if(rbData!=null&&rbData.size()>0) {
					entityService.deleteByIdBatch(rbData);
				}
			
				List<CostSummaryItemData> addList = new ArrayList<CostSummaryItemData>();
				for (int i = 0; i < list.size(); i++) {
					Map<String, DayReportVo> map = list.get(i).getXhlArray();
					for (String key : map.keySet()) {
						DayReportVo info2 = map.get(key);
						CostSummaryItemData info = new CostSummaryItemData();
						info.setId(TMUID.getUID());
						info.setPid(pid);
						info.setUnitId(dto.getUnitId());
						info.setProgramId(dto.getProjectId());
						info.setItemPrice(info2.getDj());
						info.setItemId(info2.getItemId());
						info.setTeamId(info2.getTeamId());
						info.setConsumption(info2.getXhl());
						info.setReportType("bzybReport");
						addList.add(info);
					}
					
//					List<DayReportVo> list2 = list.get(i).getXhlList();
//					if(list2!=null&&list2.size()>0) {
//						for (int l = 0; l < list2.size(); l++) {
//							CostSummaryItemData info = new CostSummaryItemData();
//							info.setId(TMUID.getUID());
//							info.setPid(pid);
//							info.setUnitId(dto.getUnitId());
//							info.setProgramId(dto.getProjectId());
//							info.setItemPrice(list2.get(l).getDj());
//							info.setItemId(list2.get(l).getItemId());
//							info.setTeamId(list2.get(l).getTeamId());
//							info.setConsumption(list2.get(l).getXhl());
//							info.setReportType("bzybReport");
//							addList.add(info);
//						}
//					}
				}
				if (addList != null && addList.size() > 0) {
					entityService.insertBatch(addList);
					TeamReportInputDto reportinfo=new TeamReportInputDto();
					reportinfo.setUnitId(dto.getUnitId());
					reportinfo.setProgramId(dto.getProjectId());
					reportinfo.setWriteDay(dto.getYf());	
					reportinfo.setBegintime(dto.getKssj());
					reportinfo.setEndtime(dto.getJzsj());
					this.ictpLogic.calcTeamMonthReport(reportinfo);
					result = "success";
				}
			}
		}
		return result;
	}
	
	/**
	 * 班组月汇总报表数据
	 * @param dto
	 * @return
	 */
	@Override
	public BzybReportCxDto getBzybReport(ReportBzyhzQueryDto dto) {
		BzybReportCxDto res = new BzybReportCxDto();
		
		Where where = Where.create();
		where.eq(CostSummaryInfo::getUnitId, dto.getUnitId());
		where.eq(CostSummaryInfo::getProgramId, dto.getProjectId());
		where.eq(CostSummaryInfo::getReportNo, dto.getYf().replace("-", "")+"000000");
		//where.eq(CostSummaryInfo::getReportDay, dto.getRq());
		List<CostSummaryInfo> iniflist = entityService.queryList(CostSummaryInfo.class, where, null);
		
		if(iniflist!=null&&iniflist.size()>0) {
			CostBgcsszbInternalDto dto2 = new CostBgcsszbInternalDto();
			dto2.setUnitId(dto.getUnitId());
			dto2.setTableName("bb11");
			Map<String, LinkedHashMap<String, CostBgcsszbInternal>> nbt = cbSer.getCostBgcsszbInternalMap(dto2);//内表头
			
			res.setInfo(iniflist.get(0));//主表
			res.setColumn(this.getBzybColumn(dto));//字段
			res.setData(this.getBzybCxData(iniflist.get(0),dto.getColFormat(),nbt));//数据
			
			CostBgcsszbDto dto1 = new CostBgcsszbDto();
	    	dto1.setUnitid(dto.getUnitId());
	    	dto1.setTableType("标题");
	    	dto1.setCxType(0);
	    	dto1.setTmused(1);
	    	dto1.setTableName("bb11");
	        List<CostBgcsszb> headerList = cbSer.getCostBgcsszbList(dto1);
	        if(headerList!=null&&headerList.size()>0) {
	        	res.setTitle(headerList.get(0).getColumnshowName());//表头
			}
		}
		
		return res;
	}
	
	private String convertAlign(Integer dqfs) {
		String yn="left";
		if (dqfs==0) {
			yn="left";
		} else if (dqfs==1) {
			yn="right";
		} else if (dqfs==2) {
			yn="center";
		}
		return yn;
	}
	
	/**
	 * 获取表头
	 * @param dto1
	 * @return
	 */
	public JSONArray getBzybColumn(ReportBzyhzQueryDto dto1) {
		CostBgcsszbDto dto = new CostBgcsszbDto();
        dto.setUnitid(dto1.getUnitId());
        dto.setTableType("表头");
        dto.setCxType(0);
        dto.setTmused(1);
        dto.setTableName("bb11");
        Integer dqfs;
        List<CostBgcsszb> headerList = cbSer.getCostBgcsszbList(dto);
        if (ObjectUtils.isEmpty(headerList)) {
            return null;
        }
        HashMap<String,CostBgcsszb> xsm=new HashMap<String,CostBgcsszb>();
        JSONArray res = new JSONArray();
        for (int i = 0; i < headerList.size(); i++) {
        	CostBgcsszb row = headerList.get(i);
            if (row.getTmused()==0) {
                continue;
            }
            xsm.put(row.getColumnCode(), row) ;
            JSONObject col = new JSONObject();
            if("itemName".equals(row.getColumnCode())) {
            	col.put("Alias", row.getColumnCode());
                col.put("Header", row.getColumnshowName());
                col.put("Width", "200");
                if(row.getColumnwidth()!=null&&row.getColumnwidth()>0) {
                	col.put("Width", row.getColumnwidth().toString());
                }
                dqfs=row.getObjalg();
            	if (dqfs==null) {
            		dqfs=0;
            	}
                col.put("Align", this.convertAlign(dqfs));
                res.add(col);
            }
        }
        dto1.setColFormat(xsm);
        JSONObject col = new JSONObject();
        col.put("Alias", "khz");
        col.put("Header", "考核值");
        //col.put("Width", "500");
        col.put("Align", "center");
        JSONArray khzChild = new JSONArray();
        for (int i = 0; i < headerList.size(); i++) {
        	CostBgcsszb row = headerList.get(i);
            if (row.getTmused()==0) {
                continue;
            }
            JSONObject col1 = new JSONObject();
            if("itemUnit".equals(row.getColumnCode())||"itemPrice".equals(row.getColumnCode())||"dhde".equals(row.getColumnCode())) {
            	col1.put("Alias", row.getColumnCode());
            	col1.put("Header", row.getColumnshowName());
            	col1.put("Width", "200");
            	if(row.getColumnwidth()!=null&&row.getColumnwidth()>0) {
            		col1.put("Width", row.getColumnwidth().toString());
                }
            	dqfs=row.getObjalg();
             	if (dqfs==null) {
             		dqfs=0;
             	}
                col1.put("Align", this.convertAlign(dqfs));
                khzChild.add(col1);
            }
        }
        col.put("child", khzChild);
        res.add(col);
        
        JSONObject col3 = new JSONObject();
        col3.put("Alias", "bysj");
        col3.put("Header", "本月实际");
        //col.put("Width", "500");
        col3.put("Align", "center");
        
        List<CommonlyBean> list3 = this.getOperation(dto1.getUnitId());
		if (list3 != null && list3.size() > 0) {
			JSONArray bysjChild = new JSONArray();
			for (int i = 0; i < list3.size(); i++) {
				JSONObject col4 = new JSONObject();
				col4.put("Alias", list3.get(i).getId());
				col4.put("Header", list3.get(i).getName());
		        //col.put("Width", "500");
				col4.put("Align", "center");
				JSONArray bysjChildChild = new JSONArray();
				for (int l = 0; l < headerList.size(); l++) {
		        	CostBgcsszb row = headerList.get(l);
		            if (row.getTmused()==0) {
		                continue;
		            }
		            JSONObject col5 = new JSONObject();
		            if("xhl".equals(row.getColumnCode())||"dh".equals(row.getColumnCode())||"dwcb".equals(row.getColumnCode())||"zcb".equals(row.getColumnCode())||"nh".equals(row.getColumnCode())) {
		            	col5.put("Alias", list3.get(i).getId()+row.getColumnCode());
		            	col5.put("Header", row.getColumnshowName());
		            	col5.put("Width", "200");
		            	if(row.getColumnwidth()!=null&&row.getColumnwidth()>0) {
		            		col5.put("Width", row.getColumnwidth().toString());
		                }
		            	dqfs=row.getObjalg();
		             	if (dqfs==null) {
		             		dqfs=0;
		             	}
		                col5.put("Align", this.convertAlign(dqfs));
			            bysjChildChild.add(col5);
		            }
		        }
				col4.put("child", bysjChildChild);
				bysjChild.add(col4);
			}
			col3.put("child", bysjChild);
		}
		res.add(col3);
		
		JSONObject col6 = new JSONObject();
		col6.put("Alias", "hj");
		col6.put("Header", "合计");
        //col.put("Width", "500");
		col6.put("Align", "center");
		JSONArray hjChild = new JSONArray();
		for (int l = 0; l < headerList.size(); l++) {
        	CostBgcsszb row = headerList.get(l);
            if (row.getTmused()==0) {
                continue;
            }
            JSONObject col5 = new JSONObject();
            if("xhl".equals(row.getColumnCode())||"dh".equals(row.getColumnCode())||"dwcb".equals(row.getColumnCode())||"zcb".equals(row.getColumnCode())||"nh".equals(row.getColumnCode())) {
            	col5.put("Alias", "hj"+row.getColumnCode());
            	col5.put("Header", row.getColumnshowName());
            	col5.put("Width", "200");
            	if(row.getColumnwidth()!=null&&row.getColumnwidth()>0) {
            		col5.put("Width", row.getColumnwidth().toString());
                }
            	dqfs=row.getObjalg();
             	if (dqfs==null) {
             		dqfs=0;
             	}
                col5.put("Align", this.convertAlign(dqfs));
            	hjChild.add(col5);
            }
        }
		col6.put("child", hjChild);
		res.add(col6);
        
		for (int i = 0; i < headerList.size(); i++) {
        	CostBgcsszb row = headerList.get(i);
            if (row.getTmused()==0) {
                continue;
            }
            if("dhcz".equals(row.getColumnCode())) {
            	JSONObject col7 = new JSONObject();
        		col7.put("Alias", "ykhzbj");
        		col7.put("Header", "与考核值比较");
        		col7.put("Align", "center");
        		JSONArray khzbjChild = new JSONArray();
        		JSONObject khzbjDhsl = new JSONObject();
            	khzbjDhsl.put("Alias", "dhcz");
            	khzbjDhsl.put("Header", row.getColumnshowName());
            	khzbjDhsl.put("Width", "200");
                if(row.getColumnwidth()!=null&&row.getColumnwidth()>0) {
                	khzbjDhsl.put("Width", row.getColumnwidth().toString());
                }
                dqfs=row.getObjalg();
            	if (dqfs==null) {
            		dqfs=0;
            	}
            	khzbjDhsl.put("Align", this.convertAlign(dqfs));
            	khzbjChild.add(khzbjDhsl);
            	col7.put("child", khzbjChild);
        		res.add(col7);
            }
        }
        return res;
	}
	
	
	/**
	 * 获取班组月汇总数据
	 * @param info
	 * @return
	 */
	public JSONArray getBzybCxData(CostSummaryInfo info,HashMap<String, CostBgcsszb> xsm,Map<String, LinkedHashMap<String, CostBgcsszbInternal>> nbt) {
    	JSONArray res = new JSONArray();
    	String flid,xmid,sjid;
		CostItemInfoVo fl = uiiSer.getCostData(info.getUnitId(), info.getEndTime());
		List<Costclass> costClass = fl.getClassList();//分类
		List<Costitem>  costitem = fl.getItemList();//项目
		List<Costindicator> costZb = fl.getIndicatorList();//指标
		Map<String, List<Costitem>> flMap = new LinkedHashMap<String, List<Costitem>>();
		HashMap<String,String> xm2fl=new HashMap<String,String>();
		HashMap<String,String> syflm=new HashMap<String,String>();
		if(costitem!=null&&costitem.size()>0) { 
			for(Costitem xm : costitem) {
				flid=xm.getPid();
				xmid=xm.getId();
				xm2fl.put(xmid, flid);
				if(flMap.containsKey(flid)) {
					List<Costitem> iList = flMap.get(flid);
					iList.add(xm);
					flMap.put(flid,iList);
				}else {
					List<Costitem> iList = new ArrayList<Costitem>();
					iList.add(xm);
					flMap.put(flid,iList);
				}
			}
		}
		
		Where where1 = Where.create();
    	where1.eq(CostSummaryItemData::getPid, info.getId());
    	
    	List<CostSummaryItemData> xmData = entityService.queryList(CostSummaryItemData.class, where1, null);
    	Map<String, Map<String, CostSummaryItemData>> xmMap = new LinkedHashMap<String, Map<String, CostSummaryItemData>>();
    	if(xmData!=null&&xmData.size()>0) { 
			for(CostSummaryItemData xm : xmData) {
				xmid=xm.getItemId();
				if (xm2fl.containsKey(xmid)) {
					syflm.put(xm2fl.get(xmid), "1");
				}
				Map<String, CostSummaryItemData> child = new LinkedHashMap<String, CostSummaryItemData>();
				xmMap.put(xmid, child);
			}
			
			for(Entry<String, Map<String, CostSummaryItemData>> entry:xmMap.entrySet()) {
				sjid=entry.getKey();
				if (sjid==null) {
					continue;
				}
				Map<String, CostSummaryItemData> child = entry.getValue();
				for(CostSummaryItemData xm : xmData) {
					xmid=xm.getItemId();
					if (xmid==null) {
						continue;
					}
					if(sjid.equals(xmid)) {
						child.put(xm.getTeamId(), xm);
					}
				}
				xmMap.put(entry.getKey(), child);
			}
    	}
    	
    	Where where2 = Where.create();
    	where2.eq(CostSummaryParamData::getPid, info.getId());
    	Map<String, Map<String, CostSummaryParamData>> zbMap = new LinkedHashMap<String, Map<String, CostSummaryParamData>>();
    	List<CostSummaryParamData> zbData = entityService.queryList(CostSummaryParamData.class, where1, null);
    	if(zbData!=null&&zbData.size()>0) { 
			for(int i=0;i<zbData.size();i++) {
				Map<String, CostSummaryParamData> child = new LinkedHashMap<String, CostSummaryParamData>();
				zbMap.put(zbData.get(i).getParamId(), child);
			}
			
			for(Map.Entry<String, Map<String, CostSummaryParamData>> entry:zbMap.entrySet()) {
				Map<String, CostSummaryParamData> child = entry.getValue();
				for(int i=0;i<zbData.size();i++) {
					if(entry.getKey().equals(zbData.get(i).getParamId())) {
						child.put(zbData.get(i).getTeamId(), zbData.get(i));
					}
				}
				zbMap.put(entry.getKey(), child);
			}
    	}
    	HashMap<String,HashMap<String,Double>> flhzm=new HashMap<String,HashMap<String,Double>>();
    	HashMap<String,Map<String,String>> flrm=new HashMap<String,Map<String,String>>();
    	HashMap<String,String> cndzm=new HashMap<String,String>();
    	Double val,dhde,dh=0.0;
    	if(costClass!=null&&costClass.size()>0) {
			for(Costclass xfl : costClass) {
				flid=xfl.getId();
				if (!syflm.containsKey(flid)) {
					continue;
				}
				if(nbt.containsKey(flid)) {
					Map<String, CostBgcsszbInternal> nbtMap = nbt.get(flid);
					Map<String, String> map = new HashMap<>();
					map.put("ctype", "nbt");
					for (Map.Entry<String, CostBgcsszbInternal> entry : nbtMap.entrySet()) {
					    String key = entry.getKey();
					    CostBgcsszbInternal value = entry.getValue();
					    
					    if(value.getTmused()==1) {
					    	map.put(key, value.getColumnshowName());
					    }else {
					    	map.put(key, "");
					    }
					    // 处理每个key-value对
					}
					if(flMap.containsKey(flid)) {
						//用分类ID到map1找项目，得到分类下的项目，再用项目找是否有项目数据，无数据的继续下一个项目，有数据时添加到报表。
						List<Costitem> iList = flMap.get(flid);
						for(int l=0;l<iList.size();l++) {
							xmid=iList.get(l).getId();
							if(xmMap.containsKey(xmid)) {
								Map<String, CostSummaryItemData> xmList = xmMap.get(xmid);
								//有数据 加一行
								for(Map.Entry<String, CostSummaryItemData> entry1:xmList.entrySet()) {
									String key1 = entry1.getKey();
									if("0".equals(entry1.getKey())) {
										key1="hj";
									}
									CostBgcsszbInternal xhlNbt = nbtMap.get("xhl");
									if(xhlNbt!=null&&xhlNbt.getTmused()==1) {
								    	map.put(key1+"xhl", xhlNbt.getColumnshowName());
								    }else {
								    	map.put(key1+"xhl", "");
								    }
									
									CostBgcsszbInternal dhNbt = nbtMap.get("dh");
									if(dhNbt!=null&&xhlNbt.getTmused()==1) {
								    	map.put(key1+"dh", dhNbt.getColumnshowName());
								    }else {
								    	map.put(key1+"dh", "");
								    }
									
									CostBgcsszbInternal dwcbNbt = nbtMap.get("dwcb");
									if(dwcbNbt!=null&&dwcbNbt.getTmused()==1) {
								    	map.put(key1+"dwcb", dwcbNbt.getColumnshowName());
								    }else {
								    	map.put(key1+"dwcb", "");
								    }
									
									CostBgcsszbInternal zcbNbt = nbtMap.get("zcb");
									if(zcbNbt!=null&&zcbNbt.getTmused()==1) {
								    	map.put(key1+"zcb", zcbNbt.getColumnshowName());
								    }else {
								    	map.put(key1+"zcb", "");
								    }
									
									CostBgcsszbInternal nhNbt = nbtMap.get("nh");
									if(nhNbt!=null&&nhNbt.getTmused()==1) {
										map.put(key1+"nh", nhNbt.getColumnshowName());
									}else {
										map.put(key1+"nh", "");
									}
								}
							}
						}
				    }
					res.add(map);//分类
				}
				
				Map<String, String> map = new HashMap<>();
				map.put("ctype", "fl");
				map.put("itemName", xfl.getCcname());
				flrm.put(flid, map);
				res.add(map);//分类
				String key,ckey;
				Integer fyxm,bbxs;
				HashMap<String,Double> hzm=null;
				if (flhzm.containsKey(flid)) {
					hzm=flhzm.get(flid);
				} else {
					hzm=new HashMap<String,Double>();
				}
				if(flMap.containsKey(flid)) {
					//用分类ID到map1找项目，得到分类下的项目，再用项目找是否有项目数据，无数据的继续下一个项目，有数据时添加到报表。
					List<Costitem> iList = flMap.get(flid);
					int ync=iList.size();
					for(int l=0;l<ync;l++) {
						Costitem ci=iList.get(l);
						xmid=ci.getId();
						bbxs=ci.getIsShowInQuery();
						if (bbxs==null) {
							bbxs=1;
						}
						if (bbxs==0) {
							continue;//报表不显示的项目
						}
						fyxm=ci.getIsFeeItem();
						if (fyxm==null) {
							fyxm=0;//默认不是费用项目
						}
						if(xmMap.containsKey(xmid)) {
							Map<String, CostSummaryItemData> xmList = xmMap.get(xmid);
							//有数据 加一行
							Map<String, String> row = new HashMap<>();
							row.put("itemName", ci.getItemname());
							CostSummaryItemData xmInfo = null;
							for(Map.Entry<String, CostSummaryItemData> entry:xmList.entrySet()) {
								xmInfo = entry.getValue();
								key = entry.getKey();
								if("0".equals(entry.getKey())) {
									key="hj";
									dh=xmInfo.getUnitConsumption();
								}
								val=xmInfo.getConsumption();
								ckey=(new StringBuffer(key).append("xhl")).toString();
								row.put(ckey, this.getColumnValue("xhl",ckey, val,fyxm, xsm,hzm));
								cndzm.put(ckey, "xhl");
								val=xmInfo.getUnitConsumption();
								ckey=(new StringBuffer(key).append("dh")).toString();
								row.put(ckey, this.getColumnValue("dh",ckey,val,fyxm, xsm,hzm));
								cndzm.put(ckey, "dh");
								val=xmInfo.getUnitCost();
								ckey=(new StringBuffer(key).append("dwcb")).toString();
								row.put(ckey, this.getColumnValue("dwcb",ckey,val,fyxm, xsm,hzm));
								cndzm.put(ckey, "dwcb");
								val=xmInfo.getItemCost();
								ckey=(new StringBuffer(key).append("zcb")).toString();
								row.put(ckey, this.getColumnValue("zcb",ckey,val,fyxm, xsm,hzm));
								cndzm.put(ckey, "zcb");
								val=xmInfo.getBaseCost();
								ckey=(new StringBuffer(key).append("nh")).toString();
								row.put(ckey, this.getColumnValue("nh",ckey,val,fyxm, xsm,hzm));
								cndzm.put(ckey, "nh");
							}
							row.put("itemUnit", ci.getItemunit());
							val=xmInfo.getItemPrice();
							row.put("itemPrice", this.getColumnValue("itemPrice", "itemPrice",val,fyxm, xsm,hzm));
							dhde=xmInfo.getBaseUnitConsumption();
							row.put("dhde", this.getColumnValue("dhde","dhde",dhde,fyxm, xsm,hzm));
							val = (dh != null ? dh : 0.0) - (dhde != null ? dhde : 0.0);
							row.put("dhcz", this.getColumnValue("dhcz", "dhcz",val,fyxm, xsm,hzm));
							flhzm.put(flid, hzm);
							res.add(row);//项目
						}
					}
				}
			}
    	}
    	
    	//循环分类
		String cn,cbn;
		for (Entry<String,Map<String,String>> yn:flrm.entrySet()) {
			flid=yn.getKey();
			if (flhzm.containsKey(flid)) {
				HashMap<String,Double> hzm=flhzm.get(flid);
				Map<String,String> row=yn.getValue();
				for (Entry<String,Double> yl:hzm.entrySet()) {
					cn=yl.getKey();
					if (StringUtils.isEmpty(cn)) {
						continue;
					}
					if (cndzm.containsKey(cn)) {
						cbn=cndzm.get(cn);
					} else {
						cbn=cn;
					}
					val=yl.getValue();
					row.put(cn, this.getColumnValue(cbn,cn, val, 0, xsm,null));
				}
			}
		}
    	
    	if(costZb!=null&&costZb.size()>0) { 
    		Map<String, String> map = new HashMap<>();
    		map.put("ctype", "fl");
			map.put("itemName", "核算指标");
			res.add(map);//分类
			Integer isvsb;
			String zbid;
			for(int i=0;i<costZb.size();i++) {
				Costindicator zb=costZb.get(i);
				zbid=zb.getId();
				isvsb=zb.getIsSelShow();
				if (isvsb==null) {
					isvsb=1;
				}
				if (isvsb==0) {
					continue;
				}
				if(zbMap.containsKey(zbid)) {
					Map<String, CostSummaryParamData> zbList = zbMap.get(zbid);
					Map<String, String> row = new HashMap<>();
					row.put("itemName", zb.getCpname());
					row.put("itemUnit", zb.getItemunit());
					row.put("itemPrice", "");
					row.put("dwcbde", "");
					
					for(Map.Entry<String, CostSummaryParamData> entry:zbList.entrySet()) {
						String key = entry.getKey();
						if("0".equals(entry.getKey())) {
							key="hj";
						}
						val=entry.getValue().getBaseVal();
						row.put(key+"dhde", this.getColumnValue("dhde", "dhde",val,0, xsm,null));
						val=entry.getValue().getCalcVal();
						row.put(key+"xhl", this.getColumnValue("xhl", "xhl",val,0, xsm,null));
					}
					
					row.put("dh", "");
					row.put("dwcb", "");
					row.put("zcb", "");
					row.put("nh", "");
					row.put("dhcz", "");
					row.put("dwcbcz", "");
					row.put("nh", "");
					res.add(row);//指标
				}
			}
    	}
    	
    	if(zbData!=null&&zbData.size()>0) { 
    		
			for(int i=0;i<zbData.size();i++) {
				
				//zbMap.put(zbData.get(i).getParamId(), zbData.get(i));
			}
    	}
       
        return res;
    
	}
	
	/**
	 * 获取tab页
	 * @param dto
	 * @return
	 */
	@Override
	public List<CommonlyBean> getTabTitel(ReportBzyhzQueryDto dto){
		List<CommonlyBean> res = new ArrayList<CommonlyBean>();
		return res;
	}

	/**
     * @category	格式化值
     * @param cn
     * @param val
     * @param isfy
     * @param cfm
     * @return
     */
    private String getColumnValue(String cn,String bcn,Double val,Integer isfy,Map<String,CostBgcsszb> cfm,HashMap<String,Double> fm) {
    	CostBgcsszb fb = cfm.get(cn);
    	Double dv=0.0;
    	String sv="";
    	if (val!=null) {
    		dv=val;
    		if (isfy==1) {
        		if ("itemPrice".equals(cn) || "xhl".equals(cn)) {
        			dv=0.0;//费用项目无单价和消耗量
        		}
        	}
    		if (dv.compareTo(0.0)!=0) {
    			sv=String.valueOf(dv);
        		if(fb!=null) {
        			StringBuffer Format = new StringBuffer("#");
        			Integer num = fb.getObjdis();
        			if (num==null) {
        				num=4;
        			}
        			if (num>0) {
        				Format.append(".");
        				for(int o=0;o<num;o++){
        					Format.append("#");
        				}
        			}
        			DecimalFormat df = new DecimalFormat(Format.toString());
        			sv = df.format(dv);
        			if (fm!=null) {
        				Integer hz=fb.getIsTotal();
        				if (hz==null) {
        					hz=0;
        				}
        				if (hz==1) {
        					if (fm.containsKey(bcn)) {
        						fm.put(bcn,fm.get(bcn)+Maths.round(dv,num));
        					} else {
        						fm.put(bcn, Maths.round(dv,num));
        					}
        				}
        			}
        		}
    		}
    	}
		return sv;
    }

}
