package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.aliyuncs.utils.StringUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostreportExcelVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.calcLogic.ICalcTeamProjectLogic;
import com.yunhesoft.leanCosting.costReport.entity.dto.BzybReportCxDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostOutBzyhzdExcelDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostOutFsdExcelDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostOutRbExcelDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ReportBzyhzQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.vo.ComboVo;
import com.yunhesoft.leanCosting.costReport.service.CostBgcsszbService;
import com.yunhesoft.leanCosting.costReport.service.CostReportQueryService;
import com.yunhesoft.leanCosting.costReport.service.DayReportOfDeviceService;
import com.yunhesoft.leanCosting.costReport.service.GetCostItemInfoService;
import com.yunhesoft.leanCosting.costReport.service.GetItemConsumptionService;
import com.yunhesoft.leanCosting.costReport.service.ICostExcelService;
import com.yunhesoft.leanCosting.costReport.service.ICostSumMaryService;
import com.yunhesoft.leanCosting.costReport.service.IInstallationMonthReportService;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;

@Service
public class CostExcelServiceImpl implements ICostExcelService {

	@Autowired
	CostBgcsszbService cbSer;

	@Autowired
	EntityService entityService;

	@Autowired
	GetCostItemInfoService costItemSer;

	@Autowired
	UnitItemInfoService uiiSer;

	@Autowired
	ICostService costSer;

	@Autowired
	SysUserUtil user;

	@Autowired
	ICalcTeamProjectLogic ictpLogic;

	@Autowired
	IUnitMethodService ims;

	@Autowired
	IInstallationMonthReportService imrSer;

	@Autowired
	GetItemConsumptionService gics;

	@Autowired
	private ICostuintService costuintService;

	@Autowired
	private ICostToolService costToolService;

	@Autowired
	private CostReportQueryService costReportQueryService;

	@Autowired
	private CostReportQueryService costService;

	@Autowired
	private IProgramService programService;

	@Autowired
	private ICostSumMaryService costSumMaryService;
	
	@Autowired
	private DayReportOfDeviceService  dayReportOfDeviceService;

	@Override
	public void exportRbCxExcel(CostOutRbExcelDto dto1, HttpServletResponse response) {
		ReportQueryDto dto = ObjUtils.copyTo(dto1, ReportQueryDto.class);
		Map<String, CostBgcsszb> BgcsszbMap = costReportQueryService.getReportHeaderMap(dto);
		CostReportQueryDto dto11 = new CostReportQueryDto();
		dto11.setUnitId(dto.getOrgCode());
		dto11.setReportNo(dto.getBbbh());
		List<ComboVo> comboVos = getCombo(dto11);
		Map<String, String> ftitleNames = new LinkedHashMap<String, String>();// 副标题
		Map<String, JSONArray> datas = new LinkedHashMap<String, JSONArray>();// 数据
		Map<String, String> remarks = new LinkedHashMap<String, String>();// 简要分析
		for (ComboVo comboVo : comboVos) {
			String sheetName = comboVo.getLabel();
			String value = comboVo.getValue();
			if (value == null) {
				value = "0";
			}
			dto.setProjectId(value);
			// 数据
			JSONArray array = costReportQueryService.getDataList(dto);
			datas.put(sheetName, array);
			CostSummaryInfo info = dayReportOfDeviceService.getInfo(dto.getOrgCode(), dto.getBbbh(),value);
			// 副标题
			String time = dto.getBbbh() != null ? dto.getBbbh() : "";
			String ftitleName = "核算对象：" + costuintService.getUnitId(dto.getOrgCode()).getName() + "    查询时间" + time;
			ftitleNames.put(sheetName, ftitleName);
			//简要分析
			String remark = info.getRemark();
			remarks.put(sheetName, remark);
		}
		// 标题
		String titleName = dto1.getExcelFileName();
		CostreportExcelVo excelVo = new CostreportExcelVo(titleName, ftitleNames, remarks, BgcsszbMap, datas);
		// 创建工作薄对象
		HSSFWorkbook workbook = costToolService.newWorkbook(excelVo);
		// 文档输出
		ExcelExport.downLoadExcel(dto1.getExcelFileName(), response, workbook);
	}

	@Override
	public void costMonthReportExcel(CostOutBzyhzdExcelDto dto1, HttpServletResponse response) {
		CostBgcsszbDto dto = new CostBgcsszbDto();
		dto.setUnitid(dto1.getUnitId());
		dto.setTableType("表头");
		dto.setCxType(0);
		dto.setTmused(1);
		dto.setTableName("bb11");
		List<CostBgcsszb> headerList = cbSer.getCostBgcsszbList(dto);
		Map<String, CostBgcsszb> BgcsszbMap = new LinkedHashMap<String, CostBgcsszb>();
		for (int i = 0; i < headerList.size(); i++) {
			CostBgcsszb row = headerList.get(i);
			if (row.getTmused() == 0) {
				continue;
			}
			BgcsszbMap.put(row.getColumnCode(), row);
		}

		ReportBzyhzQueryDto param = ObjUtils.copyTo(dto1, ReportBzyhzQueryDto.class);
		BzybReportCxDto list = costService.getBzybReport(param);// 检索数据
		// 标题
		JSONArray column = list.getColumn();// 合并表头
		String titleName = list.getTitle();
//		CostSummaryInfo info = list.getInfo();

		CostReportQueryDto dto11 = new CostReportQueryDto();
		dto11.setUnitId(dto.getUnitid());
		String bh=dto1.getTableName();
		if (bh==null) {
			bh="";
		}
		if ("bb11".equals(bh)) {
			dto11.setReportNo(dto1.getYf().replace("-", "")+"000000");
		} else {
			dto11.setReportNo(dto1.getYf());
		}
		List<ComboVo> comboVos = getCombo(dto11);
		Map<String, String> ftitleNames = new LinkedHashMap<String, String>();// 副标题
		Map<String, JSONArray> datas = new LinkedHashMap<String, JSONArray>();
		Map<String, String> remarks = new LinkedHashMap<String, String>();// 简要分析
		for (ComboVo comboVo : comboVos) {
			String sheetName = comboVo.getLabel();
			String value = comboVo.getValue();
			if (value == null) {
				value = "0";
			}
			param.setProjectId(value);
			BzybReportCxDto list1 = costService.getBzybReport(param);// 检索数据
			JSONArray array = list1.getData();
			datas.put(sheetName, array);
			// 副标题
			CostSummaryInfo info = list1.getInfo();
			String time = info.getReportDay() != null ? info.getReportDay() : "";
			String ftitleName = "统计时段：" + info.getBeginTime() + "~" + info.getEndTime() + "    制表日期:" + time;
			ftitleNames.put(sheetName, ftitleName);
			//简要分析
			String remark = info.getRemark();
			remarks.put(sheetName, remark);
		}
		// 副标题
		// 数据
		CostreportExcelVo excelVo = new CostreportExcelVo(titleName, ftitleNames, remarks, BgcsszbMap, datas);
		// 合并表头
		excelVo.setColumn(column);
		// 创建工作薄对象
		HSSFWorkbook workbook = costToolService.newWorkbook(excelVo);
		// 文档输出
		ExcelExport.downLoadExcel(dto1.getExcelFileName(), response, workbook);
	}

	@Override
	public void reportFsdCxExcel(CostOutFsdExcelDto dto1, HttpServletResponse response) {
		ReportQueryDto dto = ObjUtils.copyTo(dto1, ReportQueryDto.class);
		Map<String, CostBgcsszb> BgcsszbMap = costReportQueryService.getReportHeaderMap(dto);
//		JSONArray array = costReportQueryService.getDataList(dto);
		CostReportQueryDto dto11 = new CostReportQueryDto();
		dto11.setUnitId(dto1.getOrgCode());
		dto11.setReportNo(dto.getBbbh());
		dto11.setContentId(dto1.getProjectId());
		dto11.setMethodId(dto1.getBzdm());
		Map<String, String> ftitleNames = new LinkedHashMap<String, String>();// 副标题
		Map<String, JSONArray> datas = new LinkedHashMap<String, JSONArray>();
		Map<String, String> remarks = new LinkedHashMap<String, String>();// 简要分析
		//简要分析
		Map<String,CostSummaryInfo> remarks_ = new LinkedHashMap<String,CostSummaryInfo>();
		List<CostSummaryInfo> costSummaryInfos = costSumMaryService.getInfoList(dto11);
		for (CostSummaryInfo costSummaryInfo : costSummaryInfos) {
			String programId = costSummaryInfo.getProgramId();
			remarks_.put(programId,costSummaryInfo);
		}
		List<ComboVo> comboVos = getCombog(dto1.getOrgCode(),costSummaryInfos);
		for (ComboVo comboVo : comboVos) {
			String sheetName = comboVo.getLabel();
			String value = comboVo.getValue();
			if (value == null) {
				value = "0";
			}
			dto.setProjectId(value);
			// 数据
//			JSONArray array = costReportQueryService.getDataList(dto);
			JSONArray array = costService.getDataList(dto);
			datas.put(sheetName, array);
			// 副标题，分时段只有一个副标题
			String time = dto1.getStarTime() != null ? dto1.getStarTime() : "";
			String ftitleName = "核算对象：" + costuintService.getUnitId(dto.getOrgCode()).getName() + "    查询时间" + time;
			ftitleNames.put(sheetName, ftitleName);
			if(remarks_.containsKey(value)) {
				remarks.put(sheetName, remarks_.get(value).getRemark());
			}
		}
		CostreportExcelVo excelVo = new CostreportExcelVo("", ftitleNames, remarks, BgcsszbMap, datas);
		// 创建工作薄对象
		HSSFWorkbook workbook = costToolService.newWorkbook(excelVo);
		// 文档输出
		ExcelExport.downLoadExcel(dto1.getExcelFileName(), response, workbook);
	}

	@Override
	public void costReportExcel(CostOutRbExcelDto dto1, HttpServletResponse response) {
		ReportQueryDto dto = ObjUtils.copyTo(dto1, ReportQueryDto.class);
		Map<String, CostBgcsszb> BgcsszbMap = costReportQueryService.getReportHeaderMap(dto);

		CostReportQueryDto dto11 = new CostReportQueryDto();
		dto11.setUnitId(dto.getOrgCode());
		dto11.setReportNo(dto.getBbbh());
		List<ComboVo> comboVos = getCombo(dto11);
		Map<String, String> ftitleNames = new LinkedHashMap<String, String>();// 副标题
		Map<String, JSONArray> datas = new LinkedHashMap<String, JSONArray>();
		Map<String, String> remarks = new LinkedHashMap<String, String>();// 简要分析
		for (ComboVo comboVo : comboVos) {
			String sheetName = comboVo.getLabel();
			String value = comboVo.getValue();
			if (value == null) {
				value = "0";
			}
			dto.setProjectId(value);
			// 数据
			JSONArray array = costReportQueryService.getDataList(dto);
			datas.put(sheetName, array);
			// 副标题
			String time = dto.getBbbh() != null ? dto.getBbbh() : "";
			String ftitleName = "核算对象：" + costuintService.getUnitId(dto.getOrgCode()).getName() + "    查询时间" + time;
			ftitleNames.put(sheetName, ftitleName);
			
			CostSummaryInfo info = dayReportOfDeviceService.getInfo(dto.getOrgCode(), dto.getBbbh(),value);
			//简要分析
			String remark = info.getRemark();
			remarks.put(sheetName, remark);
		}

		// 标题
		String titleName = dto1.getExcelFileName();
		CostreportExcelVo excelVo = new CostreportExcelVo(titleName, ftitleNames, remarks, BgcsszbMap, datas);
		// 创建工作薄对象
		HSSFWorkbook workbook = costToolService.newWorkbook(excelVo);
		// 文档输出
		ExcelExport.downLoadExcel(dto1.getExcelFileName(), response, workbook);
	}

	@Override
	public List<ComboVo> reportRbCxContent(CostOutRbExcelDto dto) {
		CostReportQueryDto dto1 = new CostReportQueryDto();
		dto1.setUnitId(dto.getOrgCode());
		dto1.setReportNo(dto.getBbbh());
		return getCombo(dto1);
	}

	@Override
	public List<ComboVo> costMonthReportContent(CostOutBzyhzdExcelDto dto) {
		String yf,bh;
		bh=dto.getTableName();
		if(bh==null) {
			bh="";
		}
		yf=dto.getYf();
		if (yf==null) {
			yf="";
		}
		CostReportQueryDto dto1 = new CostReportQueryDto();
		dto1.setUnitId(dto.getUnitId());
		if ("bb11".equals(bh)) {
			dto1.setReportNo(dto.getYf().replace("-", "")+"000000");
		} else {
			dto1.setReportNo(dto.getYf());
		}
		return getCombo(dto1);
	}

	@Override
	public List<ComboVo> costReportContent(CostOutRbExcelDto dto) {
		CostReportQueryDto dto1 = new CostReportQueryDto();
		dto1.setUnitId(dto.getOrgCode());
		dto1.setReportNo(dto.getBbbh());
		return getCombo(dto1);
	}

	private List<ComboVo> getCombo(CostReportQueryDto dto1) {
		List<ComboVo> vos = new ArrayList<ComboVo>();
		List<CostSummaryInfo> datas = costSumMaryService.getDataList(dto1);
		Map<String, String> map = new HashMap<String, String>();
		for (CostSummaryInfo costSummaryInfo : datas) {
			String programId = costSummaryInfo.getProgramId();
			String id = costSummaryInfo.getId();
			if (!map.containsKey(programId)) {
				map.put(programId, id);
			}
		}

		List<ProgramItem> list = programService.getProgramItemListByUnitid(dto1.getUnitId());
		// 周报-交接班 都有汇总
		if (map.containsKey("0")) {
			ComboVo vo = new ComboVo();
			vo.setLabel("汇总");
			vo.setValue("0");
			vos.add(vo);
		}
		// 获取
		for (ProgramItem programItem : list) {
			String id = programItem.getId();
			String name = programItem.getPiName();
			if (map.containsKey(id)) {
				String pid = map.get(id);
				ComboVo vo = new ComboVo();
				vo.setLabel(name);
				vo.setValue(id);
				vo.setPid(pid);
				vos.add(vo);
			}
		}
		return vos;
	}

	private List<ComboVo> getCombog(String unitid,List<CostSummaryInfo> costSummaryInfos) {
		List<ComboVo> vos = new ArrayList<ComboVo>();
		if (costSummaryInfos !=null && costSummaryInfos.size()>0) {
			CostSummaryInfo yn=costSummaryInfos.get(0);
			String programId = yn.getProgramId();
			if (StringUtils.isEmpty(programId)) {
				programId="0";
			}
			String id = yn.getId();
			if ("0".equals(programId)) {
				ComboVo vo = new ComboVo();
				vo.setLabel("汇总");
				vo.setValue(programId);
				vo.setPid(id);
				vos.add(vo);
			} else {
				List<ProgramItem> list = programService.getProgramItemListByUnitid(unitid);
				// 获取
				for (ProgramItem programItem : list) {
					String faid = programItem.getId();
					String name = programItem.getPiName();
					if (programId.equals(faid)) {
						ComboVo vo = new ComboVo();
						vo.setLabel(name);
						vo.setValue(faid);
						vo.setPid(id);
						vos.add(vo);
					}
				}
			}
		}
		return vos;
	}
}
