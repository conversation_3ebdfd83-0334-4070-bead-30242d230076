package com.yunhesoft.leanCosting.costReport.service.impl;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.Maths;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.baseConfig.service.impl.ToolServiceImpl;
import com.yunhesoft.leanCosting.calcLogic.IGetPeriodForReportService;
import com.yunhesoft.leanCosting.calcLogic.PublicMethods;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostBgcsszbInternalDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszbInternal;
import com.yunhesoft.leanCosting.costReport.entity.po.CostReportAnalysis;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.vo.ComboVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostInputQueryVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostSumMaryVo;
import com.yunhesoft.leanCosting.costReport.service.CostBgcsszbService;
import com.yunhesoft.leanCosting.costReport.service.ICostChangeShiftsService;
import com.yunhesoft.leanCosting.costReport.service.ICostReportAnalysisService;
import com.yunhesoft.leanCosting.costReport.service.ICostSumMaryService;
import com.yunhesoft.leanCosting.costReport.service.ICostTeamInfoService;
import com.yunhesoft.leanCosting.costReport.service.ICostWeeklyService;
import com.yunhesoft.leanCosting.order.entity.vo.ExcleCell;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostBindOrgDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;

@Service
public class CostWeeklyServiceImpl implements ICostWeeklyService {

	@Autowired
	private ICostService costService;

	@Autowired
	private IGetPeriodForReportService getPeriodForReportService;

	@Autowired
	private ICostSumMaryService costSumMaryService;

	@Autowired
	private IProgramService programService;

	@Autowired
	private ISysOrgService orgService; // 机构

	@Autowired
	private CostBgcsszbService costBgService;

	@Autowired
	private UnitItemInfoService unitItemInfoService;

	@Autowired
	private EntityService entityService;

	@Autowired
	private ICostChangeShiftsService costChangeShiftsService;

	@Autowired
	private ICostReportAnalysisService costReportAnalysisService;

	@Autowired
	private ICostToolService costToolService;

	@Autowired
	private ICostTeamInfoService costTeamInfoService;

	private PublicMethods pm = new PublicMethods();

	@Override
	public List<ComboVo> getCostUnit() {
		List<ComboVo> vos = new ArrayList<ComboVo>();
		SysUser user = SysUserHolder.getCurrentUser();
		String orgId = user.getOrgId();
		String userId = user.getId();// 人员ID
		String postId = user.getPostId();
		postId = orgId + "_" + postId;// 机构ID
		List<Costuint> costuints = costService.getCostuintListByOrgId(orgId, userId, postId, 2);
		for (Costuint costuint : costuints) {
			String id = costuint.getId();
			String name = costuint.getName();
			Integer productive = costuint.getProductive() == null ? 0 : costuint.getProductive();
			if(productive==0) {
				ComboVo vo = new ComboVo();
				vo.setLabel(name);
				vo.setValue(id);
				vos.add(vo);
			}
		}
		return vos;
	}

	@Override
	public List<ComboVo> getContent(CostReportQueryDto dto) {
		List<ComboVo> vos = new ArrayList<ComboVo>();
		// 方案
		List<ProgramItem> list = programService.getProgramItemListByUnitid(dto.getUnitId());
		Map<String, String> map = new HashMap<String, String>();
		if ("3".equals(dto.getReportType())) {// 周报
			List<CostSummaryItemData> datas = costSumMaryService.getCostSummaryItemDatas(dto);
			// 方案正在使用方案
			for (CostSummaryItemData costSummaryItemData : datas) {
				String programId = costSummaryItemData.getProgramId();
				String id = costSummaryItemData.getId();
				if (!map.containsKey(programId)) {
					map.put(programId, id);
				}
			}

		} else {// 交接班
			String jzrq = dto.getJzrq();
			String SHIFTID = dto.getBc();
			StringBuffer sql = new StringBuffer();
			sql.append(" select  ID,PROGRAMID from COSTTEAMINFO where UNITID = '" + dto.getUnitId()
					+ "' AND WRITEDAY = '" + jzrq + "' AND SHIFTID = '" + SHIFTID + "' group by  ID,PROGRAMID ");
			SqlRowSet result = entityService.rawQuery(sql.toString());
			while (result.next()) {
				String programId = result.getString("PROGRAMID");// 班组ID
				String id = result.getString("ID");// PID
				if (!map.containsKey(programId)) {
					map.put(programId, id);
				}
			}
		}
		// 周报-交接班 都有汇总
		if (map.containsKey("0")) {
			ComboVo vo = new ComboVo();
			vo.setLabel("汇总");
			vo.setValue("0");
			vos.add(vo);
		}
		// 获取
		for (ProgramItem programItem : list) {
			String id = programItem.getId();
			String name = programItem.getPiName();
			if (map.containsKey(id)) {
				String pid = map.get(id);
				ComboVo vo = new ComboVo();
				vo.setLabel(name);
				vo.setValue(id);
				vo.setPid(pid);
				vos.add(vo);
			}
		}

		return vos;
	}

	@Override
	public List<ComboVo> method(CostReportQueryDto dto) {
		List<CostSummaryItemData> datas = costSumMaryService.getCostSummaryItemDatas(dto);
		List<String> keys = new ArrayList<String>();
		// 方案正在使用方案
		for (CostSummaryItemData costSummaryItemData : datas) {
			String teamId = costSummaryItemData.getTeamId();
			if (!keys.contains(teamId)) {
				keys.add(teamId);
			}
		}
		List<ComboVo> vos = new ArrayList<ComboVo>();
		// 单元默认添加，单元的机构ID是0;
		if (keys.contains("0")) {
			ComboVo vo_ = new ComboVo();
			vo_.setLabel("单元");
			vo_.setValue("0");
			vos.add(vo_);
		}
		// 核算单元操作机构
		CostBindOrgDto dto_ = new CostBindOrgDto();
		dto_.setUnitid(dto.getUnitId());
		dto_.setObjType("org");
		List<Costunitoperator> costunitoperators = costService.getCostunitoperatorList(dto_);
		for (Costunitoperator costunitoperator : costunitoperators) {
			String id = costunitoperator.getObjid();
			SysOrg org = orgService.findOrgById(id);
			if (org != null) {
				String name = org.getOrgname();
				if (keys.contains(id)) {
					ComboVo vo = new ComboVo();
					vo.setLabel(name);
					vo.setValue(id);
					vos.add(vo);
				}
			}
		}
		return vos;
	}

	@Override
	public Map<String, String> cycle(CostReportQueryDto dto) {
		String tbrq = DateTimeUtils.getNowDateStr();
		return getPeriodForReportService.getReportPeriod(dto.getUnitId(), tbrq, dto.getReportType());
	}

	@Override
	public List<ComboVo> getBc(CostReportQueryDto dto) {
		String jzrq = dto.getJzrq();
		List<ComboVo> vos = new ArrayList<ComboVo>();
		List<ShiftForeignVo> foreignVos = unitItemInfoService.getShiftList(dto.getUnitId(), dto.getJzrq());
		if (foreignVos != null) {
			Map<String, ShiftForeignVo> map = foreignVos.stream()
					.collect(Collectors.toMap(ShiftForeignVo::getShiftClassCode, a -> a, (k1, k2) -> k1));
			StringBuffer sql = new StringBuffer();
			sql.append(" select  SHIFTID,TEAMID  from COSTTEAMINFO where UNITID = '" + dto.getUnitId()
					+ "' AND WRITEDAY = '" + jzrq + "' group by  SHIFTID,TEAMID ");
			SqlRowSet result = entityService.rawQuery(sql.toString());
			while (result.next()) {
				// 2023-07-11不需要判断状态
				String TEAMID = result.getString("TEAMID");// 班组ID
				String teamName = orgService.getOrgByOrgcode(TEAMID).getOrgname();// 班组名称
				String SHIFTID = result.getString("SHIFTID");// 班次ID

				String bcName = "";
				if (map.containsKey(SHIFTID)) {
					bcName = map.get(SHIFTID).getShiftClassName();
				}
				ComboVo vo = new ComboVo();
				vo.setLabel(bcName + "(" + teamName + ")");
				vo.setValue(SHIFTID);
				vos.add(vo);
			}
		}
		return vos;
	}

	@Override
	public List<CostSumMaryVo> getData(CostReportQueryDto dto) {
		return costSumMaryService.getData(dto);
	}

	@Override
	public String saveData(List<CostSumMaryVo> dto) {
		String str = costSumMaryService.saveData(dto);
		if (!"".equals(str)) {
			return str;
		}
		return null;
	}

	/**
	 * 表格数据转表单数据CostInputQueryVo
	 * 
	 * @param list
	 * @return
	 */
	@Override
	public List<CostInputQueryVo> escapeVo(List<CostSumMaryVo> list, CostReportQueryDto dto) {
		CostBgcsszbInternalDto dto1 = new CostBgcsszbInternalDto();
		dto1.setUnitId(dto.getUnitId());
		String reportType = dto.getReportType();
		if ("3".equals(reportType)) {// 1:周报，
			dto1.setTableName("bb05");
		} else if ("2".equals(reportType)) {// 2:交接班
			dto1.setTableName("bb06");
		}
		// 内表头
		LinkedHashMap<String, LinkedHashMap<String, CostBgcsszbInternal>> mapInternalTitle = costBgService
				.getCostBgcsszbInternalMap(dto1);
		// 表头
		Map<String, CostBgcsszb> bgcsszbMap = costChangeShiftsService.getCostBgcsszbMap(dto);
		List<CostInputQueryVo> vos = new ArrayList<CostInputQueryVo>();
		HashMap<String,HashMap<String,Double>> flhzm=new HashMap<String,HashMap<String,Double>>();
		HashMap<String,CostInputQueryVo> flrm=new HashMap<String,CostInputQueryVo>();
		Double val,dh,dhde;
		Integer ws,hz;
		String classId = "", classId_, className = "", className_, type_;
		for (CostSumMaryVo costSumMaryVo : list) {
			classId_ = costSumMaryVo.getClassId();
			className_ = costSumMaryVo.getClassName();
			type_ = costSumMaryVo.getType();
			HashMap<String,Double> hzm=null;
			if (flhzm.containsKey(classId_)) {
				hzm=flhzm.get(classId_);
			} else {
				hzm=new HashMap<String,Double>();
			}
			// 核算对象
			CostInputQueryVo vo1 = ObjUtils.copyTo(costSumMaryVo, CostInputQueryVo.class);
			if (type_.equals(ToolServiceImpl.HSXMTYPE) || type_.equals(ToolServiceImpl.JJBHSXMTYPE)) {
				// 周报
				// 单耗
				dh = costSumMaryVo.getUnitConsumption();
				try {
					if (dh != null && dh.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("dh");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setDh(getDigit(ws, dh));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("dh")) {
								hzm.put("dh",hzm.get("dh")+Maths.round(dh,ws));
							} else {
								hzm.put("dh", Maths.round(dh,ws));
							}
						}
					} else {
						vo1.setDh(null);
					}
				} catch (Exception e) {
					vo1.setDh(null);
				}
				// 单位成本
				try {
					val = costSumMaryVo.getUnitCost();
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("dwcb");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setDwcb(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("dwcb")) {
								hzm.put("dwcb",hzm.get("dwcb")+Maths.round(val,ws));
							} else {
								hzm.put("dwcb", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setDwcb(null);
					}
				} catch (Exception e) {
					vo1.setDwcb(null);
				}
				// 标准单耗
				dhde = costSumMaryVo.getBaseUnitConsumption();
				try {
					if (dhde != null && dhde.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("dhde");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setDhde(getDigit(ws, dhde));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("dhde")) {
								hzm.put("dhde",hzm.get("dhde")+Maths.round(dhde,ws));
							} else {
								hzm.put("dhde", Maths.round(dhde,ws));
							}
						}
					} else {
						vo1.setDhde(null);
					}
				} catch (Exception e) {
					vo1.setDhde(null);
				}
				// 标准成本
				try {
					// 能耗
					val = costSumMaryVo.getBaseCost();
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("nh");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setNh(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("nh")) {
								hzm.put("nh",hzm.get("nh")+Maths.round(val,ws));
							} else {
								hzm.put("nh", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setNh(null);
					}
				} catch (Exception e) {
					vo1.setNh(null);
				}
				// 单价
				try {
					val = this.pm.convertDouble(costSumMaryVo.getItemPrice(), 0.0);
					if (val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("itemPrice");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setItemPrice(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("itemPrice")) {
								hzm.put("itemPrice",hzm.get("itemPrice")+Maths.round(val,ws));
							} else {
								hzm.put("itemPrice", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setItemPrice(null);
					}
				} catch (Exception e) {
					vo1.setItemPrice(null);
				}
				// 成本
				try {
					val = costSumMaryVo.getItemCost();
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("zcb");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setZcb(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("zcb")) {
								hzm.put("zcb",hzm.get("zcb")+Maths.round(val,ws));
							} else {
								hzm.put("zcb", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setZcb(null);
					}
				} catch (Exception e) {
					vo1.setZcb(null);
				}
				// 单耗差值
				try {
					val = (dh != null ? dh : 0.0) - (dhde != null ? dhde : 0.0);
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("dhcz");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setDhcz(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("dhcz")) {
								hzm.put("dhcz",hzm.get("dhcz")+Maths.round(val,ws));
							} else {
								hzm.put("dhcz", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setDhcz(null);
					}
				} catch (Exception e) {
					vo1.setDhcz(null);
				}
				vo1.setDwcbcz(null);// 由于没有单位成本定额，暂不提供单位成本差值
				// 消耗量
				try {
					val = this.pm.convertDouble(costSumMaryVo.getXhl(), 0.0);
					if (val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("xhl");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setXhl(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("xhl")) {
								hzm.put("xhl",hzm.get("xhl")+Maths.round(val,ws));
							} else {
								hzm.put("xhl", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setXhl(null);
					}
				} catch (Exception e) {
					vo1.setXhl(null);
				}
			} else if (type_.equals(ToolServiceImpl.JJBHSYBTYPE)) {
				// 交接班
				// 仪表
				// 前表数
				try {
					val = costSumMaryVo.getPreviousReadOut();
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("previousReadOut");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setPreviousReadOut(getDigit(ws,val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("previousReadOut")) {
								hzm.put("previousReadOut",hzm.get("previousReadOut")+Maths.round(val,ws));
							} else {
								hzm.put("previousReadOut", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setPreviousReadOut(null);
					}
				} catch (Exception e) {
					vo1.setPreviousReadOut(null);
				}
				// 后表数
				try {
					val = costSumMaryVo.getLastReadOut();
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("lastReadOut");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setLastReadOut(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("lastReadOut")) {
								hzm.put("lastReadOut",hzm.get("lastReadOut")+Maths.round(val,ws));
							} else {
								hzm.put("lastReadOut", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setLastReadOut(null);
					}
				} catch (Exception e) {
					vo1.setLastReadOut(null);
				}
				// 单表消耗
				try {
					val = costSumMaryVo.getDbxh();
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("dbxh");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setDbxh(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("dbxh")) {
								hzm.put("dbxh",hzm.get("dbxh")+Maths.round(val,ws));
							} else {
								hzm.put("dbxh", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setDbxh(null);
					}
				} catch (Exception e) {
					vo1.setDbxh(null);
				}
				// 消耗量
				try {
					val = this.pm.convertDouble(costSumMaryVo.getXhl(), 0.0);
					if (val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("xhl");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setXhl(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("xhl")) {
								hzm.put("xhl",hzm.get("xhl")+Maths.round(val,ws));
							} else {
								hzm.put("xhl", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setXhl(null);
					}
				} catch (Exception e) {
					vo1.setXhl(null);
				}
				// 单耗
				dh = costSumMaryVo.getUnitConsumption();
				try {
					if (dh != null && dh.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("dh");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setDh(getDigit(ws, dh));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("dh")) {
								hzm.put("dh",hzm.get("dh")+Maths.round(dh,ws));
							} else {
								hzm.put("dh", Maths.round(dh,ws));
							}
						}
					} else {
						vo1.setDh(null);
					}
				} catch (Exception e) {
					vo1.setDh(null);
				}
				// 单位成本
				try {
					val = costSumMaryVo.getUnitCost();
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("dwcb");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setDwcb(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("dwcb")) {
								hzm.put("dwcb",hzm.get("dwcb")+Maths.round(val,ws));
							} else {
								hzm.put("dwcb", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setDwcb(null);
					}
				} catch (Exception e) {
					vo1.setDwcb(null);
				}
				// 标准单耗
				dhde = costSumMaryVo.getBaseUnitConsumption();
				try {
					if (dhde != null && dhde.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("dhde");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setDhde(getDigit(ws, dhde));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("dhde")) {
								hzm.put("dhde",hzm.get("dhde")+Maths.round(dhde,ws));
							} else {
								hzm.put("dhde", Maths.round(dhde,ws));
							}
						}
					} else {
						vo1.setDhde(null);
					}
				} catch (Exception e) {
					vo1.setDhde(null);
				}
				// 标准成本
				try {
					val = costSumMaryVo.getBaseCost();
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("nh");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setNh(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("nh")) {
								hzm.put("nh",hzm.get("nh")+Maths.round(val,ws));
							} else {
								hzm.put("nh", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setNh(null);
					}
				} catch (Exception e) {
					vo1.setNh(null);
				}
				// 单价
				try {
					val = this.pm.convertDouble(costSumMaryVo.getItemPrice(), 0.0);
					if (val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("itemPrice");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setItemPrice(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("itemPrice")) {
								hzm.put("itemPrice",hzm.get("itemPrice")+Maths.round(val,ws));
							} else {
								hzm.put("itemPrice", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setItemPrice(null);
					}
				} catch (Exception e) {
					vo1.setItemPrice(null);
				}
				// 成本
				try {
					val = costSumMaryVo.getItemCost();
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("zcb");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setZcb(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("zcb")) {
								hzm.put("zcb",hzm.get("zcb")+Maths.round(val,ws));
							} else {
								hzm.put("zcb", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setZcb(null);
					}
				} catch (Exception e) {
					vo1.setZcb(null);
				}
				try {
					val = (dh != null ? dh : 0.0) - (dhde != null ? dhde : 0.0);
					if (val != null && val.compareTo(0.0) != 0) {
						CostBgcsszb fb=bgcsszbMap.get("dhcz");
						ws = fb.getObjdis();// 小数数位
						if (ws==null) {
							ws=4;
						}
						vo1.setDhcz(getDigit(ws, val));
						hz=fb.getIsTotal();
						if (hz==null) {
							hz=0;
						}
						if (hz==1) {
							if (hzm.containsKey("dhcz")) {
								hzm.put("dhcz",hzm.get("dhcz")+Maths.round(val,ws));
							} else {
								hzm.put("dhcz", Maths.round(val,ws));
							}
						}
					} else {
						vo1.setDhcz(null);
					}
				} catch (Exception e) {
					vo1.setDhcz(null);
				}
			} else if (type_.equals(ToolServiceImpl.HSZBTYPE) || type_.equals(ToolServiceImpl.JJBHSZBTYPE)) {
				// 核算指标
				try {
					String xhl1 = costSumMaryVo.getXhl();
					if (xhl1 == null) {
						xhl1 = costSumMaryVo.getWriteConsumption();
					}
					Integer xhl_ = bgcsszbMap.get("xhl").getObjdis();// 小数数位
					String _xhl = getDigit(xhl_, Double.parseDouble(xhl1));
					vo1.setXhl(_xhl);
				} catch (Exception e) {
					vo1.setXhl(null);
				}
			}
			if (!classId_.equals(classId)) {
				classId = classId_;
				className = className_;
				if (mapInternalTitle.containsKey(classId)) {
					//内表头
					LinkedHashMap<String, CostBgcsszbInternal> mapTitleTemp = mapInternalTitle.get(classId);
					CostInputQueryVo objTitleData = new CostInputQueryVo();
					String showText = "";
					showText = getInternalTitle("itemName", mapTitleTemp);
					objTitleData.setClassId(classId);
					objTitleData.setClassName(showText);
					objTitleData.setDataType(4);
					showText = getInternalTitle("dbxh", mapTitleTemp);
					objTitleData.setDbxh(showText);

					showText = getInternalTitle("dh", mapTitleTemp);
					objTitleData.setDh(showText);

					showText = getInternalTitle("dhcz", mapTitleTemp);
					objTitleData.setDhcz(showText);

					showText = getInternalTitle("dhde", mapTitleTemp);
					objTitleData.setDhde(showText);

					showText = getInternalTitle("dwcb", mapTitleTemp);
					objTitleData.setDwcb(showText);

					showText = getInternalTitle("dwcbcz", mapTitleTemp);
					objTitleData.setDwcbcz(showText);

					objTitleData.setInstrumentId("");

					showText = getInternalTitle("instrumentName", mapTitleTemp);
					objTitleData.setInstrumentName(showText);
					objTitleData.setItemHbCount(1);
					objTitleData.setItemId("");

					showText = getInternalTitle("itemName", mapTitleTemp);
					objTitleData.setItemName(showText);

					showText = getInternalTitle("itemPrice", mapTitleTemp);
					objTitleData.setItemPrice(showText);

					showText = getInternalTitle("itemUnit", mapTitleTemp);
					objTitleData.setItemUnit(showText);

					showText = getInternalTitle("lastReadOut", mapTitleTemp);
					objTitleData.setLastReadOut(showText);

					showText = getInternalTitle("previousReadOut", mapTitleTemp);
					objTitleData.setPreviousReadOut(showText);

					objTitleData.setUnitId(dto.getUnitId());

					showText = getInternalTitle("xhl", mapTitleTemp);
					objTitleData.setXhl(showText);

					showText = getInternalTitle("zcb", mapTitleTemp);
					objTitleData.setZcb(showText);

					showText = getInternalTitle("nh", mapTitleTemp);
					objTitleData.setNh(showText);

					objTitleData.setDataType(4);
					vos.add(objTitleData);

				}
				CostInputQueryVo vo = new CostInputQueryVo();
				vo.setDataType(1);
				vo.setItemName(className);// 显示名称
				//分类的
				vos.add(vo);
				flrm.put(classId, vo);
			}
			vo1.setDataType(2);
			vos.add(vo1);
			flhzm.put(classId_, hzm);
		}
		//循环分类
		String flid,cn;
		for (Entry<String,CostInputQueryVo> yn:flrm.entrySet()) {
			flid=yn.getKey();
			if (flhzm.containsKey(flid)) {
				HashMap<String,Double> hzm=flhzm.get(flid);
				CostInputQueryVo row=yn.getValue();
				for (Entry<String,Double> yl:hzm.entrySet()) {
					cn=yl.getKey();
					if (StringUtils.isEmpty(cn)) {
						continue;
					}
					val=yl.getValue();
					CostBgcsszb fb=bgcsszbMap.get(cn);
					ws = fb.getObjdis();// 小数数位
					if (ws==null) {
						ws=4;
					}
					if ("previousReadOut".equals(cn)) {
						row.setPreviousReadOut(getDigit(ws, val));;
					} else if ("lastReadOut".equals(cn)) {
						row.setLastReadOut(getDigit(ws, val));
					} else if ("dbxh".equals(cn)) {
						row.setDbxh(getDigit(ws, val));
					} else if ("xhl".equals(cn)) {
						row.setXhl(getDigit(ws, val));
					} else if ("itemPrice".equals(cn)) {
						row.setItemPrice(getDigit(ws, val));
					} else if ("dhde".equals(cn)) {
						row.setDhde(getDigit(ws, val));
					} else if ("dhcz".equals(cn)) {
						row.setDhcz(getDigit(ws, val));
					} else if ("dh".equals(cn)) {
						row.setDh(getDigit(ws, val));
					} else if ("zcb".equals(cn)) {
						row.setZcb(getDigit(ws, val));
					} else if ("nh".equals(cn)) {
						row.setNh(getDigit(ws, val));
					} else if ("dwcb".equals(cn)) {
						row.setDwcb(getDigit(ws, val));
					}
				}
			}
		}
		return vos;
	}

	@Override
	public void exeOut(CostReportQueryDto dto, HttpServletResponse response) {
		CostBgcsszbInternalDto dto1 = new CostBgcsszbInternalDto();
		dto1.setUnitId(dto.getUnitId());
		String reportType = dto.getReportType();
		CostReportQueryDto dto_ = new CostReportQueryDto();
		dto_.setYf(dto.getYf());
		dto_.setReportType(dto.getReportType());
		dto_.setZs(dto.getZs());
		dto_.setMethodId(dto.getMethodId());
		dto_.setUnitId(dto.getUnitId());
		List<String> keys = new ArrayList<String>();
		if ("3".equals(reportType)) {// 1:周报，
			dto1.setTableName("bb05");
			List<CostSummaryItemData> datas = costSumMaryService.getCostSummaryItemDatas(dto_);
			// 方案正在使用方案
			for (CostSummaryItemData costSummaryItemData : datas) {
				String programId = costSummaryItemData.getProgramId();
				if (!keys.contains(programId)) {
					keys.add(programId);
				}
			}
		} else if ("2".equals(reportType)) {// 2:交接班,获取数据
			dto1.setTableName("bb06");
			dto_.setJzrq(dto.getJzrq());// TODO：交接班使用截止日期
			dto_.setShiftId(dto.getBc());
			List<CostTeamInfo> datas = costTeamInfoService.getCostTeamInfoDatas(dto_);
			// 方案正在使用方案
			for (CostTeamInfo costTeamInfo : datas) {
				String programId = costTeamInfo.getProgramId();
				if (!keys.contains(programId)) {
					keys.add(programId);
				}
			}
		}
		LinkedHashMap<String, LinkedHashMap<String, CostBgcsszbInternal>> mapInternalTitle = costBgService
				.getCostBgcsszbInternalMap(dto1);
		List<ComboVo> vos = new ArrayList<ComboVo>();
		if (keys.contains("0")) {
			ComboVo vo = new ComboVo();
			vo.setLabel("汇总");
			vo.setValue("0");
			vos.add(vo);
		}
		List<ProgramItem> list = programService.getProgramItemListByUnitid(dto.getUnitId());
		// 获取
		for (ProgramItem programItem : list) {
			String id = programItem.getId();
			String name = programItem.getPiName();
			if (keys.contains(id)) {
				ComboVo vo = new ComboVo();
				vo.setLabel(name);
				vo.setValue(id);
				vos.add(vo);
			}
		}
		String yf = dto.getYf();
		String dateTime = null;
		String methodName_ = "";
		String methodId_ = dto.getMethodId();// 汇总方式ID
		List<ComboVo> mthodMap = method(dto);
		for (ComboVo comboVo : mthodMap) {
			String label = comboVo.getLabel();
			String value = comboVo.getValue();
			if (methodId_.equals(value)) {
				methodName_ = label;
			}
		}
		Costuint costuint_ = entityService.queryObjectById(Costuint.class, dto.getUnitId());
		/**
		 * 标题
		 */
		CostBgcsszbDto param = new CostBgcsszbDto();
		param.setCxType(0);
		if ("3".equals(reportType)) {// 1:周报，
			param.setTableName("bb05");
		} else if ("2".equals(reportType)) {// 2:交接班
			param.setTableName("bb06");
		}
		param.setTmused(1);
		param.setUnitid(dto.getUnitId());
		param.setTableType("标题");
		List<CostBgcsszb> costBgcsszbs1 = costBgService.getCostBgcsszbList(param);
		/**
		 * END
		 */

		String titleName = "";
		if (costBgcsszbs1 != null && costBgcsszbs1.size() > 0) {
			titleName = costBgcsszbs1.get(0).getColumnshowName();
		}
		String ftitleName = costuint_.getName() + " 日期：";// \"+yf+\" 班次：白班 班组：五班\"
		String bcName = "";
		if ("3".equals(reportType)) {
			dateTime = yf;
//			titleName = "周报查询";
			String zs_ = dto.getZs();
			ftitleName += dateTime + "第" + zs_ + "周" + ";汇总方式：" + methodName_;
		} else {
			String bc = dto.getBc();
			List<ComboVo> bcMap = getBc(dto);
			for (ComboVo comboVo : bcMap) {
				String label = comboVo.getLabel();
				String value = comboVo.getValue();
				if (bc.equals(value)) {
					bcName = label;
				}
			}
			dateTime = dto.getJzrq();
			ftitleName += dateTime + ";班次：" + bcName;
		}

		/**
		 * 显示
		 */
		Map<String, CostBgcsszb> BgcsszbMap = getCostBgcsszbMap(dto_);
		// 创建工作薄对象
		HSSFWorkbook workbook = new HSSFWorkbook();
		// 汇总内容
		for (int i = 0; i < vos.size(); i++) {
			ComboVo comboVo = vos.get(i);
			String contentId = comboVo.getValue();
			String contentName = comboVo.getLabel();
			CostReportQueryDto queryDto = ObjUtils.copyTo(dto, CostReportQueryDto.class);
			queryDto.setContentId(contentId);
			// sheet页查询
			List<CostSumMaryVo> list1 = getData(queryDto);
			List<CostInputQueryVo> costInputQueryVos = escapeVo(list1, dto);
			Map<String, Integer> map2 = new LinkedHashMap<String, Integer>();
			for (CostInputQueryVo bean : costInputQueryVos) {
				String itemId = bean.getItemId();
				Integer i1 = 0;
				if (map2.containsKey(itemId)) {
					i1 = map2.get(itemId);
				}
				i1++;
				map2.put(itemId, i1);
			}
			for (CostInputQueryVo bean : costInputQueryVos) {
				String itemId = bean.getItemId();
				Integer dataType = bean.getDataType();
				if (dataType == 4) {
					bean.setRowspan(1);
					bean.setColspan(1);
					continue;
				}
				if (map2.containsKey(itemId)) {
					if (map2.get(itemId) > 0) {
						bean.setRowspan(map2.get(itemId));
						bean.setColspan(1);
						map2.put(itemId, 0);
					} else {
						bean.setRowspan(0);
						bean.setColspan(0);
					}
				}
			}
			// 创建表格样式
			HSSFCellStyle style = workbook.createCellStyle();
			HSSFFont font = workbook.createFont();
			// 准备订单表头
			List<ExcleCell> titleCellOrder = new ArrayList<>();
			this.buildStandbyTitle(titleCellOrder, workbook, style, font, BgcsszbMap);
			// 准备产品表头
			HSSFCellStyle style1 = workbook.createCellStyle();
			HSSFFont font1 = workbook.createFont();
			List<ExcleCell> titleCellPro = new ArrayList<>();
			this.buildStandbyProductTitle(titleCellPro, workbook, style1, font1, BgcsszbMap);
			Map<String, ExcleCell> titleProMap = titleCellPro.stream()
					.collect(Collectors.toMap(ExcleCell::getProperty, a -> a, (k1, k2) -> k1));
			// 开始构建excle
			// 创建sheet
			contentName = costToolService.sheetName(contentName);
			HSSFSheet sheet = workbook.getSheet(contentName);
			if (sheet == null) {
				sheet = workbook.createSheet(contentName);
			}
			sheet.setActive(true);
			sheet.createFreezePane(2, 3);
			// 单元格高度宽度
			this.columnWidth(titleCellOrder.size(), sheet);
			// 设置sheet内容
			// 创建行
			int rowIndex = 0;
			// 设置
			HSSFRow row = sheet.createRow(rowIndex);
			HSSFCell cell = row.createCell(0);
			// 标题
			cell.setCellValue(titleName);
			cell.setCellStyle(style);
			for (int i1 = 1; i1 < (titleCellOrder.size()); i1++) {
				HSSFCell cell1 = row.createCell(i1);
				cell1.setCellValue("");
				cell1.setCellStyle(style);
			}
			rowIndex++;
			sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titleCellOrder.size() - 1));
			HSSFRow row1 = sheet.createRow(rowIndex);
			HSSFCell cell1 = row1.createCell(0);
			// 副标题
			cell1.setCellValue(ftitleName);
			cell1.setCellStyle(style);
			for (int i1 = 1; i1 < (titleCellOrder.size()); i1++) {
				HSSFCell cell11 = row.createCell(i1);
				cell11.setCellValue("");
				cell11.setCellStyle(style);
			}
			rowIndex++;
			sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, titleCellOrder.size() - 1));
			this.buildTitle(rowIndex, sheet, titleCellOrder);
			// 将订单信息设置
			int orderNo = 0;
			HashMap<String, HSSFCellStyle> csm = new HashMap<String, HSSFCellStyle>();
			HSSFFont fontOrder = workbook.createFont();
			for (CostInputQueryVo order : costInputQueryVos) {
				String classId = order.getClassId();
				++rowIndex;
				++orderNo;
				Integer dataType = order.getDataType();
				Integer type = 99;
				if (dataType == 1) {// 分类
					type = 98;
				}
				if (dataType == 4) {// 内表头
					if (mapInternalTitle.containsKey(classId)) {
						LinkedHashMap<String, CostBgcsszbInternal> costBgcsszbMap = mapInternalTitle.get(classId);
						// 内表头
						List<ExcleCell> ntitleCellOrder = new ArrayList<>();
						this.buildStandbyNtitle(ntitleCellOrder, workbook, style, font, BgcsszbMap, costBgcsszbMap,
								rowIndex, reportType);
						this.buildTitle(rowIndex, sheet, ntitleCellOrder);
					}
				} else {
					this.buildExcle(order, type, orderNo, rowIndex, titleProMap, sheet, workbook, csm, fontOrder,
							dataType, BgcsszbMap);
				}
			}
			// 增加简要分析
			dto.setShiftId(dto.getBc());
			dto.setContentId(contentId);
			List<CostReportAnalysis> analysis = costReportAnalysisService.getData(dto);
			if (analysis.size() > 0) {
				rowIndex++;
				String analysisContent = analysis.get(0).getAnalysisContent();
				this.buildjyfx(workbook, style, font, rowIndex, orderNo, sheet, analysisContent, titleCellOrder.size());
			}
			contentName = costToolService.sheetName(contentName);
			workbook.setSheetName(i, contentName);// 设置sheet的Name
		}
		// 文档输出
		ExcelExport.downLoadExcel(dto.getExcelFileName(), response, workbook);
	}

	// 增加简要分析
	private void buildjyfx(HSSFWorkbook workbook, HSSFCellStyle style, HSSFFont font, int rowIndex, int orderNo,
			HSSFSheet sheet, String analysisContent, int rowNum) {
		List<ExcleCell> titleCellOrder1 = new ArrayList<>();
		String oNo = String.valueOf(orderNo);
		// 获取表头信息进行编制
		// 第一列
		HSSFRow orderRow = sheet.createRow(rowIndex);
		HSSFCellStyle style_1 = workbook.createCellStyle();
		HSSFFont font_1 = workbook.createFont();
		titleCellOrder1.add(new ExcleCell(oNo, "no", 97, 0, workbook, style_1, font_1));
		HSSFCellStyle style_ = workbook.createCellStyle();
		style_.setAlignment(HorizontalAlignment.LEFT);
		HSSFFont font_ = workbook.createFont();
		titleCellOrder1.add(new ExcleCell("★简要分析", "itemName", 98, 1, workbook, style_, font_));
		titleCellOrder1.add(new ExcleCell(analysisContent, "itemName", 98, 2, workbook, style_, font_));
		for (ExcleCell orderCell : titleCellOrder1) {
			HSSFCell cell = orderRow.createCell(orderCell.getIndex());
			cell.setCellValue(orderCell.getValue());
			cell.setCellStyle(orderCell.getStyle());
		}
		for (int i = 3; i < rowNum; i++) {
			HSSFCell cell = orderRow.createCell(i);
			cell.setCellValue("");
			cell.setCellStyle(style_);
		}
		sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 2, rowNum - 1));
	}

	/**
	 * 获得数据
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public Map<String, CostBgcsszb> getCostBgcsszbMap(CostReportQueryDto dto) {
		String reportType = dto.getReportType();
		Map<String, CostBgcsszb> BgcsszbMap = new LinkedHashMap<String, CostBgcsszb>();
		Map<Integer, String> Bgcsszb_Map = new LinkedHashMap<Integer, String>();
		CostBgcsszbDto param = new CostBgcsszbDto();
		param.setCxType(0);
		if ("3".equals(reportType)) {// 1:周报，
			param.setTableName("bb05");
		} else if ("2".equals(reportType)) {// 2:交接班
			param.setTableName("bb06");
		}
		param.setTableType("表头");
		param.setTmused(1);
		param.setUnitid(dto.getUnitId());
		List<CostBgcsszb> costBgcsszbs = costBgService.getCostBgcsszbList(param);

		for (CostBgcsszb costBgcsszb : costBgcsszbs) {
			Integer tmused = costBgcsszb.getTmused();
			Integer tmSort = costBgcsszb.getTmsort();
			String columnCode = costBgcsszb.getColumnCode();
			if (tmused == 1) {
				BgcsszbMap.put(columnCode, costBgcsszb);
				Bgcsszb_Map.put(tmSort, columnCode);
			}
		}
		Map<String, CostBgcsszb> newBgcsszbMap = new LinkedHashMap<String, CostBgcsszb>();
		for (Entry<Integer, String> entry : Bgcsszb_Map.entrySet()) {
			String key = entry.getValue();
			if (BgcsszbMap.containsKey(key)) {
				newBgcsszbMap.put(key, BgcsszbMap.get(key));
			}
		}
		return newBgcsszbMap;
	}

	/**
	 * 查询核算指标数据
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public List<CostSummaryParamData> getWeepCostSummaryParamDatas(CostReportQueryDto dto) {
		Where where = Where.create();
		if (dto != null) {
			if (!StringUtils.isEmpty(dto.getUnitId())) {
				where.eq(CostSummaryParamData::getUnitId, dto.getUnitId());
			}
			if (!StringUtils.isEmpty(dto.getYf())) {
				String writeDay = dto.getYf();
				if ("3".equals(dto.getReportType())) {// TODO：报表类型，1:周报，2：月报
					writeDay += "-" + dto.getZs();
				}
				where.eq(CostSummaryParamData::getWriteDay, writeDay);
			}
			if (!"3".equals(dto.getReportType())) {
				if (!StringUtils.isEmpty(dto.getJzrq())) {// 班组ID
					where.eq(CostSummaryItemData::getWriteDay, dto.getJzrq());
				}
			}
			if (!StringUtils.isEmpty(dto.getContentId())) {// 方案ID
				where.eq(CostSummaryParamData::getProgramId, dto.getContentId());
			}
			if (!StringUtils.isEmpty(dto.getMethodId())) {// 班组ID
				where.eq(CostSummaryParamData::getTeamId, dto.getMethodId());
			}
		}
		Order order = Order.create();
		return entityService.queryList(CostSummaryParamData.class, where, order);
	}

	/**
	 * 查询汇总表项目信息
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public List<CostSummaryItemData> getWeepCostSummaryItemDatas(CostReportQueryDto dto) {
		Where where = Where.create();
		if (dto != null) {
			if (!StringUtils.isEmpty(dto.getUnitId())) {
				where.eq(CostSummaryItemData::getUnitId, dto.getUnitId());
			}
			if (!StringUtils.isEmpty(dto.getYf())) {
				String writeDay = dto.getYf();
				if ("3".equals(dto.getReportType())) {// TODO：报表类型，1:周报，2：月报
					writeDay += "-" + dto.getZs();
				}
				where.eq(CostSummaryItemData::getWriteDay, writeDay);
			}
			if (!"3".equals(dto.getReportType())) {
				if (!StringUtils.isEmpty(dto.getJzrq())) {// 班组ID
					where.eq(CostSummaryItemData::getWriteDay, dto.getJzrq());
				}
			}
			if (!StringUtils.isEmpty(dto.getContentId())) {// 方案ID
				where.eq(CostSummaryItemData::getProgramId, dto.getContentId());
			}
			if (!StringUtils.isEmpty(dto.getMethodId())) {// 班组ID
				where.eq(CostSummaryItemData::getTeamId, dto.getMethodId());
			}
		}
		Order order = Order.create();
		return entityService.queryList(CostSummaryItemData.class, where, order);
	}

	/**
	 * 
	 * @param <T>
	 * @param order
	 * @param type
	 * @param orderNo
	 * @param rowIndex
	 * @param titleOrderMap
	 * @param sheet
	 * @param workbook
	 * @param style
	 * @param font
	 * @param bgcsszbMap
	 * @return
	 */
	private <T> T buildExcle(T order, int type, int orderNo, int rowIndex, Map<String, ExcleCell> titleOrderMap,
			HSSFSheet sheet, HSSFWorkbook workbook, HashMap<String, HSSFCellStyle> sm, HSSFFont font, Integer dataType,
			Map<String, CostBgcsszb> bgcsszbMap) {
		HSSFRow orderRow = sheet.createRow(rowIndex);
		// 序号
		List<ExcleCell> orderCellList = new ArrayList<>();
		HSSFCellStyle cStyle = null;
		if (sm.containsKey("no")) {
			cStyle = sm.get("no");
		} else {
			cStyle = workbook.createCellStyle();
		}
		String oNo = String.valueOf(orderNo);
		ExcleCell orderInfo = null;
		if (dataType == 1) {// 分类
			orderInfo = new ExcleCell(oNo, "no", 97, 0, workbook, cStyle, font);
		} else {
			orderInfo = new ExcleCell(oNo, "no", 97, 0, workbook, cStyle, font);
		}
		orderCellList.add(orderInfo);

		String jsonStr = JSONObject.toJSONString(order, SerializerFeature.WriteMapNullValue);
		Map<String, Object> objMap = JSONObject.parseObject(jsonStr);
		CostInputQueryVo bean = (CostInputQueryVo) order;
		Integer xsws, dqfs;
		Boolean isnum = false;
		// 遍历对象map
		for (Entry<String, CostBgcsszb> entry : bgcsszbMap.entrySet()) {
			String property = entry.getKey();
			CostBgcsszb bgcsszb = entry.getValue();
			xsws = bgcsszb.getObjdis();// 小数位数
			if (xsws == null) {
				xsws = 0;
			}
			dqfs = bgcsszb.getObjalg();// 对齐方式
			if (dqfs == null) {
				dqfs = 0;
			}
			ExcleCell c = titleOrderMap.get(property);
			if (c == null) {
				continue;
			}
			Object value_ = objMap.get(property);
			String value;
			if (value_ != null) {
				value = value_.toString();
			} else {
				value = "";
			}
			HSSFCellStyle dcStyle = null;
			if (sm.containsKey(property)) {
				dcStyle = sm.get(property);
			} else {
				dcStyle = workbook.createCellStyle();
				if (dqfs == 2) {
					dcStyle.setAlignment(HorizontalAlignment.CENTER);// 居中
				} else if (dqfs == 1) {
					dcStyle.setAlignment(HorizontalAlignment.RIGHT);// 右对齐
				} else {
					dcStyle.setAlignment(HorizontalAlignment.LEFT);// 默认左对齐
				}
				if ("itemPrice".equals(property) || "dwcbde".equals(property) || "dhde".equals(property)
						|| "xhl".equals(property) || "dh".equals(property) || "dwcb".equals(property)
						|| "zcb".equals(property) || "dhcz".equals(property) || "dwcbcz".equals(property)
						|| "nh".equals(property) || "previousReadOut".equals(property) || "lastReadOut".equals(property)
						|| "dbxh".equals(property)) {
					// 数值类型的要使用小数位数
					StringBuffer sb = new StringBuffer();
					sb.append("###############0");
					if (xsws > 0) {
						sb.append(".");
						for (int i = 0; xsws > i; i++) {
							sb.append("0");
						}
					}
					HSSFDataFormat df = workbook.createDataFormat();
					dcStyle.setDataFormat(df.getFormat(sb.toString()));
				}
				sm.put(property, dcStyle);
			}
			if (type == 98 && "itemName".equals(property)) {// 分类
				value = "★" + value;
			}
			if (this.pm.judgeDouble(value)) {
				isnum = true;
			} else {
				isnum = false;
			}
			ExcleCell ec = new ExcleCell(value, property, type, c.getIndex() - 1, workbook, dcStyle, font);
			if (isnum) {
				ec.setIsnum(isnum);
				ec.setDvalue(Double.parseDouble(value));
			}
			orderCellList.add(ec);
			if ("itemName".equals(property) || "itemPrice".equals(property) || "dwcbde".equals(property)
					|| "dhde".equals(property) || "xhl".equals(property) || "dh".equals(property)
					|| "dwcb".equals(property) || "zcb".equals(property) || "dhcz".equals(property)
					|| "dwcbcz".equals(property) || "nh".equals(property)) {
				String classId = bean.getClassId();
				if (classId != null) {
					Integer rowspan = bean.getRowspan();
					if (rowspan == null)
						rowspan = 0;
					Integer colspan = bean.getColspan();
					if (colspan == null)
						colspan = 0;
					if (rowspan > 1 || colspan > 1) {
						sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex + rowspan - 1, c.getIndex() - 1,
								c.getIndex() - 1));
					}
				}
			}
		}
		for (ExcleCell orderCell : orderCellList) {
			HSSFCell cell = orderRow.createCell(orderCell.getIndex());
			isnum = orderCell.getIsnum();
			if (isnum == null) {
				isnum = false;
			}
			if (isnum) {
				cell.setCellValue(orderCell.getDvalue());
			} else {
				cell.setCellValue(orderCell.getValue());
			}
			cell.setCellStyle(orderCell.getStyle());
		}

		return null;
	}

	/**
	 * 设置列宽
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	private void columnWidth(int colNum, HSSFSheet sheet) {
		for (int i = 1; i < colNum; i++) {
			sheet.setColumnWidth(i, 20 * 256);// 设置第一列的宽度
		}
	}

	/**
	 * 构建标题
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	private void buildTitle(int rowIndex, HSSFSheet sheet, List<ExcleCell> titleCellOrder) {
		HSSFRow row = sheet.createRow(rowIndex);
		// 设置首行标题
		for (ExcleCell title : titleCellOrder) {
			HSSFCell cell = row.createCell(title.getIndex());
			cell.setCellValue(title.getValue());
			cell.setCellStyle(title.getStyle());
		}
	}

	/**
	 * 构建订单表头
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 * @param bgcsszbMap
	 */
	private List<ExcleCell> buildStandbyTitle(List<ExcleCell> titleCellOrder, HSSFWorkbook workbook,
			HSSFCellStyle style, HSSFFont font, Map<String, CostBgcsszb> bgcsszbMap) {
		// 获取表头信息进行编制
		int index = 0;
		titleCellOrder.add(new ExcleCell("序号", "no", 9, 0, workbook, style, font));
		for (Entry<String, CostBgcsszb> entry : bgcsszbMap.entrySet()) {
			String key = entry.getKey();
			if (bgcsszbMap.containsKey(key)) {
				Integer tmused = bgcsszbMap.get(key).getTmused();
				if (tmused == 1) {
					String value = bgcsszbMap.get(key).getColumnshowName();
					titleCellOrder.add(new ExcleCell(value, key, 9, ++index, workbook, style, font));// 1
				}
			}
		}
		return titleCellOrder;
	}

	/**
	 * 构建订单表头
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 * @param bgcsszbMap
	 */
	private List<ExcleCell> buildStandbyProductTitle(List<ExcleCell> titleCellPro, HSSFWorkbook workbook,
			HSSFCellStyle style, HSSFFont font, Map<String, CostBgcsszb> bgcsszbMap) {
		int index = 1;
		titleCellPro.add(new ExcleCell("序号", "no", 99, index, workbook, style, font));
		for (Entry<String, CostBgcsszb> entry : bgcsszbMap.entrySet()) {
			String key = entry.getKey();
			if (bgcsszbMap.containsKey(key)) {
				Integer tmused = bgcsszbMap.get(key).getTmused();
				if (tmused == 1) {
					String value = bgcsszbMap.get(key).getColumnshowName();
					titleCellPro.add(new ExcleCell(value, key, 99, ++index, workbook, style, font));// 1
				}
			}
		}
		return titleCellPro;
	}

	/**
	 * 内表头
	 * 
	 * @param titleCellPro
	 * @param workbook
	 * @param style
	 * @param font
	 * @param bgcsszbMap     外表头
	 * @param costBgcsszbMap 内表头
	 * @param rowIndex
	 * @param reportType
	 * @return
	 */
	private List<ExcleCell> buildStandbyNtitle(List<ExcleCell> titleCellPro, HSSFWorkbook workbook, HSSFCellStyle style,
			HSSFFont font, Map<String, CostBgcsszb> bgcsszbMap,
			LinkedHashMap<String, CostBgcsszbInternal> costBgcsszbMap, Integer rowIndex, String reportType) {
		int index = 0;
		Integer rowIndex_ = rowIndex - 2;
		titleCellPro.add(new ExcleCell(rowIndex_.toString(), "no", 99, index, workbook, style, font));
		for (Entry<String, CostBgcsszb> entry : bgcsszbMap.entrySet()) {
			String key = entry.getKey();
			if (bgcsszbMap.containsKey(key)) {
				try {
					Integer tmused = costBgcsszbMap.get(key).getTmused();
					if (tmused == 1) {
						String value = costBgcsszbMap.get(key).getColumnshowName();
						titleCellPro.add(new ExcleCell(value, key, 99, ++index, workbook, style, font));// 1
					} else {
						titleCellPro.add(new ExcleCell("", key, 99, ++index, workbook, style, font));// 2
					}
				} catch (Exception e) {// 内表头没有显示，默认给null
					titleCellPro.add(new ExcleCell("", key, 99, ++index, workbook, style, font));// 2
				}
			}
		}
		return titleCellPro;
	}

	/**
	 * 数据格式化
	 * 
	 * @param digit
	 * @param double_
	 * @return
	 */
	private String getDigit(Integer digit, Double double_) {
		StringBuffer formats = new StringBuffer("0");
		if (digit > 0) {
			formats.append(".");
			for (int i = 0; i < digit; i++) {
				formats.append("0");
			}
		}
		DecimalFormat df1 = new DecimalFormat(formats.toString());
		return df1.format(double_);
	}

	@Override
	public List<CostInputQueryVo> getBriefAnalysis(CostReportQueryDto dto, Integer contentType) {
		// 查询简要分析
		dto.setContentType(contentType);
		List<CostReportAnalysis> analysis = costReportAnalysisService.getData(dto);
		List<CostInputQueryVo> costInputQueryVos = new ArrayList<CostInputQueryVo>();
		for (CostReportAnalysis costReportAnalysis : analysis) {
			CostInputQueryVo vo = new CostInputQueryVo();
			if (contentType == 0) {
				String analysisContent = costReportAnalysis.getAnalysisContent();
				if (analysisContent == null) {
					analysisContent = "";
				}
				vo.setItemName(analysisContent);
				costInputQueryVos.add(vo);
			} else if (contentType == 1) {
				String analysisReason = costReportAnalysis.getAnalysisReason();
				if (analysisReason == null) {
					analysisReason = "";
				}
				vo.setItemName(analysisReason);
				vo.setDataType(99);
				costInputQueryVos.add(vo);
			}
		}
		return costInputQueryVos;
	}

	/**
	 * 返回一个字段的显示值
	 * 
	 * @param columnCode   字段名
	 * @param mapTitleTemp 表头信息
	 * @return
	 */
	private String getInternalTitle(String columnCode, LinkedHashMap<String, CostBgcsszbInternal> mapTitleTemp) {
		String result = "";
		if (mapTitleTemp != null && mapTitleTemp.containsKey(columnCode)) {
			if (mapTitleTemp.get(columnCode).getTmused() != null && mapTitleTemp.get(columnCode).getTmused() == 1) {
				result = mapTitleTemp.get(columnCode).getColumnshowName() == null ? ""
						: mapTitleTemp.get(columnCode).getColumnshowName();
			}
		}
		return result;
	}

	@Override
	public Boolean saveCostTeamInfo(CostReportQueryDto dto) {
		Boolean key = false;
		dto.setContentType(0);// 手动
		String contentId = dto.getContentId();
		if (StringUtils.isEmpty(contentId)) {// 方案ID
			dto.setContentId("0");// 默认汇总方案
		}
		// 查询简要分析
		List<CostReportAnalysis> analysis = costReportAnalysisService.getData(dto);
		CostReportAnalysis costReportAnalysis = null;
		if (analysis.size() > 0) {
			costReportAnalysis = analysis.get(0);
			String analysisContent = dto.getAnalysisContent();
			costReportAnalysis.setAnalysisContent(analysisContent);
			entityService.update(costReportAnalysis);
			key = true;
		} else {
			costReportAnalysis = ObjUtils.copyTo(dto, CostReportAnalysis.class);
			costReportAnalysis.setId(TMUID.getUID());
			costReportAnalysis.setWriteDay(dto.getJzrq());
			costReportAnalysis.setProgramId(dto.getContentId());
			costReportAnalysis.setTeamId(dto.getMethodId());
			costReportAnalysis.setContentType(dto.getContentType());
			entityService.insert(costReportAnalysis);
			key = true;
		}
		return key;
	}
}
