package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.service.IToolService;
import com.yunhesoft.leanCosting.baseConfig.service.impl.ToolServiceImpl;
import com.yunhesoft.leanCosting.calcLogic.IGetPeriodForReportService;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamParamData;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostItemForWriteVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostSumMaryVo;
import com.yunhesoft.leanCosting.costReport.service.GetCostItemInfoService;
import com.yunhesoft.leanCosting.costReport.service.ICostChangeShiftsService;
import com.yunhesoft.leanCosting.costReport.service.ICostSumMaryService;
import com.yunhesoft.leanCosting.costReport.service.ICostWeeklyService;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class CostSumMaryServiceImpl implements ICostSumMaryService {

	@Autowired
	private EntityService entityService;

	@Autowired
	private GetCostItemInfoService getCostItemInfoService;

	@Autowired
	private IGetPeriodForReportService getPeriodForReportService;

	@Autowired
	private IToolService toolService;
	
	@Autowired
	private ICostWeeklyService costWeeklyService;
	
	@Autowired
	private ICostChangeShiftsService costChangeShiftsService;
	
	@Override
	public List<CostSummaryInfo> getDataList(CostReportQueryDto dto) {
		Where where = Where.create();
		where.eq(CostSummaryInfo::getUnitId, dto.getUnitId());
		where.eq(CostSummaryInfo::getReportNo, dto.getReportNo());
		return entityService.queryData(CostSummaryInfo.class, where, null, null);
	}
	
	@Override
	public List<CostSummaryInfo> getInfoList(CostReportQueryDto dto) {
		Where where = Where.create();
		where.eq(CostSummaryInfo::getUnitId, dto.getUnitId());
		where.eq(CostSummaryInfo::getReportNo, dto.getReportNo());
		where.eq(CostSummaryInfo::getProgramId, dto.getContentId());
		where.eq(CostSummaryInfo::getTeamId, dto.getMethodId());
		return entityService.queryData(CostSummaryInfo.class, where, null, null);
	}

	@Override
	public List<CostSumMaryVo> getData(CostReportQueryDto dto) {
		List<CostSumMaryVo> vos = new ArrayList<CostSumMaryVo>();
		Integer bbxs,fyxm;
		String pid,itemid,ybid;
		//核算对象
		Costuint costuint = entityService.queryObjectById(Costuint.class, dto.getUnitId());
		String reportType = dto.getReportType();
		if("3".equals(reportType)) {
			//周报
			//内容
			Map<String, String> map = getPeriodForReportService.getWeekPeriod(dto.getUnitId(), dto.getYf(), dto.getZs());
			String selectDate = map.get("jzrq");
			// 截止日期
			dto.setJzrq(selectDate);
			// 项目数据
			Map<String, CostSummaryItemData> itemMap = toolService.getWeepCostSummaryItemDatas(dto);
			// 核算指标
			Map<String, CostSummaryParamData> paramMap = toolService.getWeepCostSummaryParamDatas(dto);
			// 查询项目数据
			List<CostItemForWriteVo> list = getCostItemInfoService.reportQueryInfo(dto.getUnitId(), selectDate,dto.getContentId(),"false");
			Map<String, CostItemForWriteVo> map2 = new LinkedHashMap<String, CostItemForWriteVo>();
			for (CostItemForWriteVo costItemForWriteVo : list) {
				String key = costItemForWriteVo.getItemId();
				if (!map2.containsKey(key)) {
					map2.put(key, costItemForWriteVo);
				}
			}
			// 核算对象
			String type = ToolServiceImpl.HSXMTYPE;
			Integer digit = costuint.getDigit();
			if (digit == null) {// 小数点保留默认3位
				digit = 3;
			}
			if(dto.getTypeDate()==1) {//使用外部部
				digit = null;
			}
			for (Entry<String, CostItemForWriteVo> entry : map2.entrySet()) {
				String key = entry.getKey();
				CostItemForWriteVo iteminfo = entry.getValue();
				bbxs=iteminfo.getIsShowInQuery();
				if (bbxs!=null) {
					if (bbxs==0) {
						continue;//报表不显示的项目
					}
				}
				// 数据
				CostSumMaryVo costSumMaryVo = new CostSumMaryVo();
				if (itemMap.containsKey(key)) {
					fyxm=iteminfo.getIsFeeItem();
					if (fyxm==null) {
						fyxm=0;
					}
					CostSummaryItemData vo = itemMap.get(key);
					String id = vo.getId();
					ObjUtils.copyTo(iteminfo, costSumMaryVo);
					costSumMaryVo.setId(id);
					costSumMaryVo.setType(type);
					// 标准单耗
					costSumMaryVo.setBaseUnitConsumption(vo.getBaseUnitConsumption());
					// 标准消耗
					costSumMaryVo.setBaseConsumption(vo.getBaseConsumption());
					// 单耗
					costSumMaryVo.setUnitConsumption(vo.getUnitConsumption());
					// 成本
					costSumMaryVo.setItemCost(vo.getItemCost());
					// 单位成本
					costSumMaryVo.setUnitCost(vo.getUnitCost());
					//能耗
					costSumMaryVo.setBaseCost(vo.getBaseCost());
					if (fyxm==1) {
						//费用项目不显示消耗量和单价
						costSumMaryVo.setXhl(null);
						costSumMaryVo.setItemPrice(null);
					} else {
						// 消耗量
						Double xhl,dj;
						xhl=vo.getWriteConsumption();
						if (xhl!=null) {
							costSumMaryVo.setXhl(xhl.toString());
						}
						// 单价
						dj=vo.getItemPrice();
						if (dj!=null) {
							costSumMaryVo.setItemPrice(dj.toString());
						}
					}
					vos.add(costSumMaryVo);
				}
			}
			
			// 查询模式-核算指标
			if (dto.isQueryKey()) {
				//
				String type_ = ToolServiceImpl.HSZBTYPE;
				for (Entry<String, CostItemForWriteVo> entry : map2.entrySet()) {
					String key = entry.getKey();
					CostItemForWriteVo costItemForWriteVo = entry.getValue();
					// 数据
					CostSumMaryVo costSumMaryVo = new CostSumMaryVo();
					if (paramMap.containsKey(key)) {
						CostSummaryParamData vo = paramMap.get(key);
						String id = vo.getId();
						ObjUtils.copyTo(costItemForWriteVo, costSumMaryVo);
						toolService.weekCostSumMaryVo(costSumMaryVo, vo, id, type_, digit);
						vos.add(costSumMaryVo);
					}
				}
			}
		}else if("2".equals(reportType)) {
			//交接班
			List<CostTeamInfo> costTeamInfoss = costChangeShiftsService.getCostTeamInfos(dto);
			if(costTeamInfoss.size()>0) {
				CostTeamInfo costTeamInfo = costTeamInfoss.get(0);//默认
				for (CostTeamInfo costTeamInfo1 : costTeamInfoss) {
					String programId = costTeamInfo1.getProgramId();
					if("0".equals(programId)) {//给汇总
						costTeamInfo = costTeamInfo1;
					}
				}
				
				pid=costTeamInfo.getId();
				dto.setPid(pid);
				//1.查询汇总表项目信息(交接班)
				LinkedHashMap<String, CostTeamItemData> itemMap = toolService.getChangeShiftsCostTeamItemDatas(pid);
				//2.查询核算指标数据(交接班)
				Map<String, CostTeamParamData> paramMap = toolService.getChangeShiftsCostTeamParamDatas(pid);
				//3.查询汇总表仪表信息(交接班)
				Map<String, List<CostTeamInstrumentData>> instrumentMap = toolService.getChangeShiftsCostTeamInstrumentData(pid);
				
				List<CostItemForWriteVo> list = getCostItemInfoService.reportQueryInfo(dto.getUnitId(), dto.getJzrq(),dto.getContentId(),"true");
				//对比数据：1.查询项目数据
				LinkedHashMap<String, CostItemForWriteVo> xmMap = new LinkedHashMap<String, CostItemForWriteVo>();
				//对比数据：1.查询仪表数据
				LinkedHashMap<String, CostItemForWriteVo> ybMap = new LinkedHashMap<String, CostItemForWriteVo>();
				if (list!=null) {
					for (CostItemForWriteVo costItemForWriteVo : list) {
						itemid = costItemForWriteVo.getItemId();
						if (StringUtils.isNotEmpty(itemid)) {
							xmMap.put(itemid, costItemForWriteVo);
						}
						ybid = costItemForWriteVo.getInstrumentId();
						if (StringUtils.isNotEmpty(ybid)) {
							ybMap.put(ybid, costItemForWriteVo);
						}
					}
				}
				
				// 1. 查询核算指标-仪表(交接班)
				String type = ToolServiceImpl.JJBHSYBTYPE;
				Map<String,List<CostSumMaryVo>> map = new LinkedHashMap<String, List<CostSumMaryVo>>(); 
				for (Entry<String, CostItemForWriteVo> entry : ybMap.entrySet()) {
					ybid = entry.getKey();
					if(StringUtils.isEmpty(ybid)) {
						continue;
					}
					CostItemForWriteVo ybinfo = entry.getValue();
					// 数据
					if (instrumentMap.containsKey(ybid)) {
						fyxm=ybinfo.getIsFeeItem();
						if (fyxm==null) {
							fyxm=0;
						}
						List<CostTeamInstrumentData> vos1 = instrumentMap.get(ybid);
						for (CostTeamInstrumentData vo : vos1) {
							String id = vo.getId();
							CostSumMaryVo costSumMaryVo = new CostSumMaryVo();
							ObjUtils.copyTo(ybinfo, costSumMaryVo);
							toolService.jjbCostInstrumentVo(costSumMaryVo, vo, id, type,fyxm);
							itemid = costSumMaryVo.getItemId();
							if(map.containsKey(itemid)) {//仪表添加Map
								map.get(itemid).add(costSumMaryVo);
							} else {
								List<CostSumMaryVo> costSumMaryVos = new ArrayList<CostSumMaryVo>();
								costSumMaryVos.add(costSumMaryVo);
								map.put(itemid, costSumMaryVos);
							}
						}
					}
				}
				// 2.核算指标数据(交接班)
				type = ToolServiceImpl.JJBHSXMTYPE;
				for (Entry<String, CostItemForWriteVo> entry : xmMap.entrySet()) {
					itemid = entry.getKey();
					if(StringUtils.isEmpty(itemid)) {
						continue;
					}
					CostItemForWriteVo iteminfo = entry.getValue();
					bbxs=iteminfo.getIsShowInQuery();
					if (bbxs!=null) {
						if (bbxs==0) {
							continue;//报表不显示的项目
						}
					}
					// 数据
					if (itemMap.containsKey(itemid)) {
						CostTeamItemData vo = itemMap.get(itemid);
						if (map.containsKey(itemid)) {
							List<CostSumMaryVo> costSumMaryVos = map.get(itemid);
							if(costSumMaryVos.size()>0) {
								fyxm=iteminfo.getIsFeeItem();
								if (fyxm==null) {
									fyxm=0;
								}
								for (int i = 1; i < costSumMaryVos.size(); i++) {
									costSumMaryVos.get(i).setItemName("");
								}
								//第一列需要继承项目数据
								CostSumMaryVo bean = costSumMaryVos.get(0);
								if (fyxm==1) {
									//费用科目没有单价和消耗量
									bean.setItemPrice(null);
									bean.setXhl(null);
								} else {
									bean.setItemPrice(String.valueOf(vo.getItemPrice()));
									bean.setXhl(String.valueOf(vo.getWriteConsumption()));
								}
								bean.setBaseUnitConsumption(vo.getBaseUnitConsumption());
								bean.setBaseConsumption(vo.getBaseConsumption());
								bean.setUnitConsumption(vo.getUnitConsumption());
								bean.setItemCost(vo.getItemCost());
								bean.setBaseCost(vo.getBaseCost());
								bean.setUnitCost(vo.getUnitCost());
								bean.setItemId(iteminfo.getItemId());
								bean.setItemName(iteminfo.getItemName());
								bean.setClassId(iteminfo.getClassId());
								bean.setClassName(iteminfo.getClassName());
							}
							vos.addAll(costSumMaryVos);
						}
					}
				}
				
				Integer digit = costuint.getDigit();
				if (digit == null) {// 小数点保留默认3位
					digit = 3;
				}
				// 3.查询模式-核算指标
				if (dto.isQueryKey()) {
					//
					String type_ = ToolServiceImpl.JJBHSZBTYPE;
					for (Entry<String, CostItemForWriteVo> entry : xmMap.entrySet()) {
						String key = entry.getKey();
						CostItemForWriteVo costItemForWriteVo = entry.getValue();
						// 数据
						CostSumMaryVo costSumMaryVo = new CostSumMaryVo();
						if (paramMap.containsKey(key)) {
							CostTeamParamData vo = paramMap.get(key);
							String id = vo.getId();
							ObjUtils.copyTo(costItemForWriteVo, costSumMaryVo);
							toolService.jjbCostParamVo(costSumMaryVo, vo, id, type_, digit);
							vos.add(costSumMaryVo);
						}
					}
				}
			}
		}
		
		Map<String, List<CostSumMaryVo>> dataMap = new LinkedHashMap<String, List<CostSumMaryVo>>();
		for (CostSumMaryVo costSumMaryVo : vos) {
			String classId = costSumMaryVo.getClassId();
			if(dataMap.containsKey(classId)) {
				dataMap.get(classId).add(costSumMaryVo);
			}else {
				List<CostSumMaryVo> costSumMaryVos = new ArrayList<CostSumMaryVo>();
				costSumMaryVos.add(costSumMaryVo);
				dataMap.put(classId, costSumMaryVos);
			}
		}

		// 整理数据
		List<CostSumMaryVo> vo_s = new ArrayList<CostSumMaryVo>();
		String className = "";
		for (Entry<String, List<CostSumMaryVo>> entry : dataMap.entrySet()) {
			List<CostSumMaryVo> vos1 = entry.getValue();
			for (CostSumMaryVo vo : vos1) {
				if (vo.getClassName() != null && className.equals(vo.getClassName())) {
					CostSumMaryVo costSumMaryVo = new CostSumMaryVo();
					costSumMaryVo.setClassName(vo.getClassName());
					vo.setClassName("");
				} else {
					className = vo.getClassName();
				}
				vo_s.add(vo);
			}
		}
		return vo_s;
	}

	@Override
	public String saveData(List<CostSumMaryVo> dto) {
		List<CostSummaryItemData> costSummaryItemDatas = new ArrayList<CostSummaryItemData>();
		for (CostSumMaryVo costSumMaryVo : dto) {
			String id = costSumMaryVo.getId();
			Double writeConsumption = Double.parseDouble(costSumMaryVo.getWriteConsumption());
			CostSummaryItemData costSummaryItemData = entityService.queryObjectById(CostSummaryItemData.class, id);
			if (costSummaryItemData != null) {
				costSummaryItemData.setWriteConsumption(writeConsumption);
				costSummaryItemDatas.add(costSummaryItemData);
			}
		}
		entityService.updateBatch(costSummaryItemDatas);
		return "";
	}

	/**
	 * 周报查询使用
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public List<CostSummaryItemData> getCostSummaryItemDatas(CostReportQueryDto dto) {
		if ("3".equals(dto.getReportType())) {// 1:周报，
			Map<String, String> map = getPeriodForReportService.getWeekPeriod(dto.getUnitId(), dto.getYf(), dto.getZs());
			String selectDate = map.get("jzrq");
			dto.setJzrq(selectDate);
		}else if ("2".equals(dto.getReportType())) {// 2:交接班
			
		}
		return costWeeklyService.getWeepCostSummaryItemDatas(dto);
	}

	
}
