package com.yunhesoft.leanCosting.costReport.service.impl;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.accountTools.entity.dto.DeviceConfQueryDto;
import com.yunhesoft.accountTools.service.IDeviceConfService;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.calcLogic.FetchRealTimeDataVo;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamRecordDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamParamData;
import com.yunhesoft.leanCosting.costReport.entity.vo.TeamRecordDataVo;
import com.yunhesoft.leanCosting.costReport.service.IGetTeamRecordService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostStipulateTime;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostItemInfoVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostKeyDeviceConfVo;
import com.yunhesoft.leanCosting.unitConf.service.IKeyDeviceService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormMeter;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.service.IRtdbService;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class GetTeamRecordServiceImpl implements IGetTeamRecordService {

	@Autowired
	UnitItemInfoService uiis;

	@Autowired
	IUnitMethodService iims;

	@Autowired
	private IRtdbService rtdbSrv;

	@Autowired
	EntityService entityService;

	@Autowired
	IKeyDeviceService kds;

	@Autowired
	IDeviceConfService idcs;

	@Override
	public List<TeamRecordDataVo> getSampleData(TeamRecordDto dto) {
		List<TeamRecordDataVo> rtn = new ArrayList<TeamRecordDataVo>();
		String ul = dto.getUnitIdList();
		String rq = dto.getWriteDay();
		if (StringUtils.isNotEmpty(ul) && StringUtils.isNotEmpty(rq)) {
			Integer js;
			String sTimePoint, ybid, ssyb,bb;
			List<Tag> tempData;
			String bcdm = dto.getShiftId();
			String sbsj = dto.getBeginTime();
			String xbsj = dto.getEndTime();
			String[] ua = ul.split(",");
			for (String x : ua) {
				// 循环核算对象，得到每个核算对象的采集点及采集数据
				HashMap<String, List<String>> hTimePoint = new HashMap<String, List<String>>();// 暂存规定时间的时间段
				List<String> yblist = null;
				HashMap<String, HashMap<String, CostStipulateTime>> gdsjm = this.getFixTimeConfig(x, rq);// 得到核算对象下的规定时间
				HashMap<String, Date> iTimePoint = new HashMap<String, Date>();// 仪表的规定时间
				List<Costunitsampledot> cydl = this.uiis.getSampleDot(x, rq, "");// 得到核算对象下所有的采集点
				if (cydl != null) {
					for (Costunitsampledot dot : cydl) {
						js = dot.getIsUseToRecordEvent();// 用于记事
						if (js == null) {
							js = 0;
						}
						if (js == 1) {
							ybid = dot.getId();
							ssyb = dot.getDatasource();
							if (StringUtils.isEmpty(ssyb)) {
								continue;
							}
							sTimePoint = this.getObjFixTime(bcdm, ybid, sbsj, xbsj, gdsjm, iTimePoint);// 得到时间点和时间段
							if (!"".equals(sTimePoint)) {// 有设置
								if (hTimePoint.containsKey(sTimePoint)) {// 时间点被暂存过了
									yblist = hTimePoint.get(sTimePoint);// 读取暂存数据
								} else {
									yblist = new ArrayList<String>();// 新建一个list暂存数据
								}
								if (!yblist.contains(ssyb)) {
									yblist.add(ssyb);// 向List里填入实时仪表，留作采集使用
								}
								hTimePoint.put(sTimePoint, yblist);
							}
						}
					}
					HashMap<String, List<FetchRealTimeDataVo>> sjm = new HashMap<String, List<FetchRealTimeDataVo>>();
					if (hTimePoint.size() > 0) {
						// 获取规定时间的数据
						for (Entry<String, List<String>> m : hTimePoint.entrySet()) {
							// 分时间点来采集数据
							sTimePoint = m.getKey();
							yblist = m.getValue();
							String[] arr = sTimePoint.split(",");// 得到起始和截止采集时间
							try {
								tempData = getTagData(arr[0], arr[1], yblist);
								if (tempData != null) {
									sjm.putAll(dealWithFetchDataByInstrument(tempData));
								}
							} catch (Exception e) {
								// 代理服务器返回异常，为错误赋值并结束采集
								log.error("", "获取采集点实时数据发生错误：" + yblist.toString());
							}
						}
					}
					for (Costunitsampledot dot : cydl) {
						js = dot.getIsUseToRecordEvent();// 用于记事
						if (js == null) {
							js = 0;
						}
						if (js == 1) {
							ybid = dot.getId();
							ssyb = dot.getDatasource();
							if (StringUtils.isEmpty(ssyb)) {
								bb="";
							}else {
								bb=ssyb.toUpperCase();
							}
							rtn.add(getFixTimeYBValue(x, ybid, dot.getName(), dot.getTagnumber(), bb,
									dot.getSdUnit(), iTimePoint, sjm));
						}
					}
				}
			}
		}
		return rtn;
	}

	@Override
	public List<TeamRecordDataVo> getCostData(TeamRecordDto dto) {
		List<TeamRecordDataVo> rtn = new ArrayList<TeamRecordDataVo>();
		String pid, yn, cm, ag;
		String ul = dto.getUnitIdList();
		String rq = dto.getWriteDay();
		if (StringUtils.isNotEmpty(ul) && StringUtils.isNotEmpty(rq)) {
			Double val;
			Integer js;
			String paramid, pname, pv;
			String bzdm = dto.getTeamId();
			String bcdm = dto.getShiftId();
			String[] ua = ul.split(",");
			for (String x : ua) {
				CostItemInfoVo infos = this.uiis.getUnitData(x, rq, "0");
				List<Costindicator> csl = infos.getIndicatorList();
				if (csl != null) {
					// 设置的公式
					HashMap<String, String> gsm = new HashMap<String, String>();
					List<CostItemFormula> gsl = infos.getFormulaList();
					if (gsl != null) {
						for (CostItemFormula gs : gsl) {
							pid = gs.getPid();
							yn = gs.getFormula();
							gsm.put(pid, yn);
						}
					}
					List<Costindicator> ll = new ArrayList<Costindicator>();
					for (Costindicator cs : csl) {
						js = cs.getIsUseToRecordEvent();// 用于记事
						if (js == null) {
							js = 0;
						}
						if (js == 1) {
							ll.add(cs);
						}
					}
					if (ll.size() > 0) {
						// 有班组记事内容才获取数据
						List<CostTeamParamData> sj = null;
						HashMap<String, String> sjm = new HashMap<String, String>();
						// 获取本班数据
						sj = this.getCurShiftData(x, rq, bcdm, bzdm);
						if (sj != null) {
							DecimalFormat df = new DecimalFormat("#.###");
							for (CostTeamParamData j : sj) {
								paramid = j.getParamId();
								val = j.getCalcVal();
								if (val == null) {
									sjm.put(paramid, "0");
								} else {
									sjm.put(paramid, df.format(val));
								}
							}
						}
						for (Costindicator cs : ll) {
							paramid = cs.getId();
							if (gsm.containsKey(paramid)) {
								yn = gsm.get(paramid);
							} else {
								yn = "";
							}
							if (StringUtils.isEmpty(yn)) {
								// 未设置公式，不获取数据
								cm = "t";
								ag = "f";
							} else {
								// 设置了公式，可获取
								cm = "f";
								ag = "t";
							}
							pname = cs.getCpname();
							TeamRecordDataVo p = new TeamRecordDataVo();
							p.setUnitId(x);
							p.setItemId(paramid);
							p.setItemName(pname);
							p.setTagNumber(pname);
							p.setItemUnit(cs.getItemunit());
							if (sjm.containsKey(paramid) && "t".equalsIgnoreCase(ag)) {
								// 有数据且配置了公式
								pv = sjm.get(paramid);
								p.setFetchValue(pv);
							}
							p.setAutoGet(ag);
							p.setCanModified(cm);
							rtn.add(p);
						}
					}
				}
			}
		}
		return rtn;
	}

	@Override
	public List<TeamRecordDataVo> getShiftData(TeamRecordDto dto) {
		List<TeamRecordDataVo> data = new ArrayList<TeamRecordDataVo>();
		List<TeamRecordDataVo> rtn = new ArrayList<TeamRecordDataVo>();
		data.addAll(getSampleData(dto));
		data.addAll(getCostData(dto));
		if (data != null) {
			String accountid = dto.getAccountId();
			rtn = this.filterObjM(accountid, 3, data);
		}
		return rtn;
	}

	@Override
	public List<TeamRecordDataVo> getDeviceData(TeamRecordDto dto) {
		List<TeamRecordDataVo> data = new ArrayList<TeamRecordDataVo>();
		String ul = dto.getUnitIdList();
		String rq = dto.getWriteDay();
		if (StringUtils.isNotEmpty(ul) && StringUtils.isNotEmpty(rq)) {
			String kdid, kdname, optval;
			String[] ua = ul.split(",");
			for (String x : ua) {
				List<CostKeyDeviceConfVo> kdl = this.kds.getUseToRecordEventList(x, rq);
				if (kdl != null) {
					// 得到最近一次是设备数据
					for (CostKeyDeviceConfVo kd : kdl) {
						kdid = kd.getId();
						kdname = kd.getDeviceName();
						optval = kd.getDeviceStatus();
						List<String> opta = new ArrayList<String>();
						if (StringUtils.isNotEmpty(optval)) {
							String[] oa = optval.split(",");
							for (String yn : oa) {
								if (StringUtils.isNotEmpty(yn)) {
									opta.add(yn);
								}
							}
						}
						TeamRecordDataVo p = new TeamRecordDataVo();
						p.setUnitId(x);
						p.setItemId(kdid);
						p.setItemName(kdname);
						p.setTagNumber(kdname);
						p.setOptionValue(opta);
						data.add(p);
					}
				}
			}
		}
		List<TeamRecordDataVo> rtn = new ArrayList<TeamRecordDataVo>();
		if (data != null) {
			String accountid = dto.getAccountId();
			rtn = this.filterObjM(accountid, 2, data);
		}
		return rtn;
	}

	/**
	 * @category 检索给定时间
	 * @param unitId 核算对象
	 * @param tbrq   检索日期
	 * @return
	 */
	private HashMap<String, HashMap<String, CostStipulateTime>> getFixTimeConfig(String unitId, String tbrq) {
		HashMap<String, HashMap<String, CostStipulateTime>> rtn = new HashMap<String, HashMap<String, CostStipulateTime>>();
		// 先得到给定日期,每个仪表的最后规定时间设置
		MethodQueryDto dto = new MethodQueryDto();
		dto.setUnitid(unitId);
		dto.setMaxBegintime(tbrq);
		List<CostStipulateTime> fixtimeconfig = this.iims.getCostStipulateTimeList(dto);
		if (fixtimeconfig != null) {
			String ybid, newdate, olddate, shiftId;
			for (CostStipulateTime x : fixtimeconfig) {
				ybid = x.getPid();
				shiftId = x.getShiftClassCode();
				if (rtn.containsKey(ybid)) {
					HashMap<String, CostStipulateTime> ybc = rtn.get(ybid);
					if (ybc.containsKey(shiftId)) {
						CostStipulateTime c = ybc.get(shiftId);
						newdate = x.getBegintime();
						olddate = c.getBegintime();
						if (DateTimeUtils.bjDate(DateTimeUtils.parseDate(newdate),
								DateTimeUtils.parseDate(olddate)) >= 0) {
							// 新日期比旧的晚，使用新日期的配置
							ybc.put(shiftId, x);
						}
					} else {
						ybc.put(shiftId, x);
					}
					rtn.put(ybid, ybc);
				} else {
					HashMap<String, CostStipulateTime> ybc = new HashMap<String, CostStipulateTime>();
					ybc.put(shiftId, x);
					rtn.put(ybid, ybc);
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 获取规定时间仪表的规定时间点和采集时间段
	 * @param wzdm 物资代码
	 * @param ybwh 仪表
	 * @param sbsj 上班时间
	 * @param xbsj 下班时间
	 * @param tbrq 填表日期
	 * @param bcdm 当班班次
	 * @return String 采集时间段，如果没有时间段认为是没有设置
	 */
	private String getObjFixTime(String shiftid, String ybid, String sbsj, String xbsj,
			HashMap<String, HashMap<String, CostStipulateTime>> timeConfig, HashMap<String, Date> iTimePoint) {
		String rtn = "";
		Date dTimePoint = null;
		if (timeConfig.containsKey(ybid)) {
			// 有设置，将设置转成时间段
			HashMap<String, CostStipulateTime> cm = timeConfig.get(ybid);
			if (cm.containsKey(shiftid)) {
				CostStipulateTime config = cm.get(shiftid);
				Integer t = config.getStipulateType();
				if (t == null) {
					t = 3;// 默认是下班之前
				}
				Integer gdsj = config.getStipulateTime();
				if (gdsj == null) {
					gdsj = 0;
				}
				switch (t) {
				case 1:
					// 上班时间之前
					dTimePoint = DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(sbsj), -(gdsj * 60));
					break;
				case 2:
					// 上班时间之后
					dTimePoint = DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(sbsj), gdsj * 60);
					break;
				case 3:
					// 下班时间之前
					dTimePoint = DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(xbsj), -(gdsj * 60));
					break;
				case 4:
					// 下班时间之后
					dTimePoint = DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(xbsj), gdsj * 60);
					break;
				default:
					// 没有设置
					break;
				}
				if (dTimePoint != null) {
					// 时间点不是空值
					Integer fw = config.getDeviationTime();
					if (fw == null) {
						fw = 0;
					}
					// 得到采集的起始时间和截止时间
					iTimePoint.put(ybid, dTimePoint);// 暂存规定时间点
					rtn = DateTimeUtils.formatDateTime(DateTimeUtils.doSecond(dTimePoint, -(fw * 60))) + ","
							+ DateTimeUtils.formatDateTime(DateTimeUtils.doSecond(dTimePoint, fw * 60));
				}
			} else {
				// 如果未配置固定时间，就使用下班时间，偏差范围为10分钟
				// 得到采集的起始时间和截止时间
				dTimePoint = DateTimeUtils.parseDateTime(xbsj);
				iTimePoint.put(ybid, dTimePoint);// 暂存规定时间点
				rtn = DateTimeUtils.formatDateTime(DateTimeUtils.doSecond(dTimePoint, -(10 * 60))) + ","
						+ DateTimeUtils.formatDateTime(DateTimeUtils.doSecond(dTimePoint, 10 * 60));
			}
		}
		return rtn;
	}

	/**
	 * @category 读取实时数据并分析数据
	 * @param beginTime 开始时间
	 * @param endTime   截止时间
	 * @param list      待采仪表的信息
	 * @return
	 */
	private List<Tag> getTagData(String beginTime, String endTime, List<String> list) {
		List<Tag> data;
		if (list.size() == 0) {
			return null;// 没有仪表时，返回空值
		} else {
			try {
				data = this.rtdbSrv.queryRtdbTagData(list, beginTime, endTime, 60);
			} catch (Exception e) {
				return null;// 代理服务器出错，返回空值
			}
		}
		return data;
	}

	/**
	 * @category 按照实时仪表对采样值进行整理
	 * @param vList //返回的采样结果
	 */
	private HashMap<String, List<FetchRealTimeDataVo>> dealWithFetchDataByInstrument(List<Tag> vList) {
		HashMap<String, List<FetchRealTimeDataVo>> sjm = new HashMap<String, List<FetchRealTimeDataVo>>();
		String sval;
		Object val;
		if (vList != null) {
			int cc1 = vList.size();
			for (int i = 0; cc1 > i; i++) {
				Tag jsonObject = vList.get(i);
				String tagCode = jsonObject.getTagCode();
				List<FetchRealTimeDataVo> sl = null;
				if (sjm.containsKey(tagCode)) {
					sl = sjm.get(tagCode);
				} else {
					sl = new ArrayList<FetchRealTimeDataVo>();
				}
				List<TagData> datas = jsonObject.getDatas();
				if (datas != null) {
					int cc2 = datas.size();
					for (int j = 0; cc2 > j; j++) {
						TagData each = datas.get(j);
						FetchRealTimeDataVo vo = new FetchRealTimeDataVo();
						vo.setTime(each.getDatetime());
						val = each.getValue();
						if (val == null) {
							sval = "";
						} else {
							sval = String.valueOf(val);
						}
						vo.setValue(sval);
						sl.add(vo);
					}
					sjm.put(tagCode, sl);
				}
			}
		}
		return sjm;
	}

	/**
	 * @category 获取规定时间的仪表采集值，一般是LIMS数据
	 * @param wzdm      物资代码
	 * @param ybwh      仪表位号
	 * @param realData  实时仪表
	 * @param timePoint 规定的时间点
	 * @return
	 */
	private TeamRecordDataVo getFixTimeYBValue(String unitid, String ybid, String cjd, String tagnumber,
			String realData, String unit, HashMap<String, Date> iTimePoint,
			HashMap<String, List<FetchRealTimeDataVo>> fetchData) {
		long minTimeValue = -100;
		long timeValue = 0;
		Date minTime = null;
		Date sj = null;
		String val = "0", cm, ag;
		if (StringUtils.isEmpty(realData)) {
			cm = "t";
			ag = "f";
		} else {
			cm = "f";
			ag = "t";
			if (fetchData.containsKey(realData)) {
				// 有 realData的采集数据
				Date timePoint;
				if (iTimePoint.containsKey(ybid)) {
					timePoint = iTimePoint.get(ybid);
					if (timePoint != null) {
						List<FetchRealTimeDataVo> data = fetchData.get(realData);
						for (FetchRealTimeDataVo m : data) {
							// 仪表的数据
							sj = DateTimeUtils.parseDateTime(m.getTime());
							// 采集时间
							timeValue = Math.abs(DateTimeUtils.diffSecond(sj, timePoint));
							// 采集与时间点的差值
							if (minTimeValue < 0 || minTimeValue > timeValue) {
								// 第一次或者差值比最小值还小
								val = m.getValue();
								minTime = sj;
								minTimeValue = timeValue;
							}
						}
					}
				}
			}
		}

		TeamRecordDataVo rtn = new TeamRecordDataVo();
		rtn.setUnitId(unitid);
		rtn.setItemId(ybid);
		rtn.setItemName(cjd);
		rtn.setTagNumber(tagnumber);
		rtn.setItemUnit(unit);
		rtn.setCanModified(cm);
		rtn.setAutoGet(ag);
		if (minTimeValue >= 0) {
			DecimalFormat df = new DecimalFormat("#.###");
			rtn.setFetchTime(DateTimeUtils.formatDateTime(minTime));
			if (val == null) {
				rtn.setFetchValue(val);
			} else {
				rtn.setFetchValue(df.format(Double.parseDouble(val)));
			}
		}
		return rtn;
	}

	private List<CostTeamParamData> getCurShiftData(String unitid, String rq, String shiftid, String teamid) {
		if (teamid == null && "".equals(teamid)) {
			teamid = "0";
		}
		if (shiftid == null && "".equals(shiftid)) {
			shiftid = "0";
		}
		Where where = Where.create();
		where.eq(CostTeamInfo::getWriteDay, rq);
		where.eq(CostTeamInfo::getUnitId, unitid);
		where.eq(CostTeamInfo::getProgramId, "0");
		where.eq(CostTeamInfo::getShiftId, shiftid);
		where.eq(CostTeamInfo::getTeamId, teamid);
		CostTeamInfo info = entityService.queryObject(CostTeamInfo.class, where, null);
		if (info != null) {
			String pid = info.getId();
			Where wherex = Where.create();
			wherex.eq(CostTeamParamData::getPid, pid);
			return this.entityService.queryList(CostTeamParamData.class, wherex);
		} else {
			return null;
		}
	}

	private List<CostTeamParamData> getPreShiftData(String unitid, String sbsj) {
		Where where = Where.create();
		where.eq(CostTeamInfo::getUnitId, unitid);
		where.eq(CostTeamInfo::getProgramId, "0");
		where.eq(CostTeamInfo::getBeginTime, sbsj);
		CostTeamInfo info = entityService.queryObject(CostTeamInfo.class, where, null);
		if (info != null) {
			String pid = info.getId();
			Where wherex = Where.create();
			wherex.eq(CostTeamParamData::getPid, pid);
			return this.entityService.queryList(CostTeamParamData.class, wherex);
		} else {
			return null;
		}
	}

	// 根据台账过滤核算对象下的指标
	private List<TeamRecordDataVo> filterObjM(String accountid, Integer typecode, List<TeamRecordDataVo> data) {
		List<TeamRecordDataVo> rtn = new ArrayList<TeamRecordDataVo>();
		if (StringUtils.isNotEmpty(accountid)) {
			// 自定义台账要遵循设置，如果没有设置内容，不返回记事内容
			DeviceConfQueryDto queryDto = new DeviceConfQueryDto();
			queryDto.setAccountid(accountid);
			queryDto.setTypeCode(typecode);
			List<TdsAccountFormMeter> fl = this.idcs.getTdsAccountFormMeterList(queryDto);
			if (fl != null && fl.size() > 0) {
				HashMap<String, String> fm = new HashMap<String, String>();
				for (TdsAccountFormMeter x : fl) {
					fm.put(x.getDevCode(), "1");
				}
				String itemid;
				for (TeamRecordDataVo dp : data) {
					itemid = dp.getItemId();
					if (fm.containsKey(itemid)) {
						rtn.add(dp);
					}
				}
			}
		} else {
			// 默认台账无过滤
			rtn.addAll(data);
		}
		return rtn;
	}
}
