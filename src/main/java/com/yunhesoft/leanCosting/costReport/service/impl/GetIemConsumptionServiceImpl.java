package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamProgramDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamItemData;
import com.yunhesoft.leanCosting.costReport.entity.vo.ItemConsumption;
import com.yunhesoft.leanCosting.costReport.entity.vo.ItemLedgerVo;
import com.yunhesoft.leanCosting.costReport.service.GetItemConsumptionService;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class GetIemConsumptionServiceImpl implements GetItemConsumptionService {

	@Autowired
	EntityService entityService;
	@Autowired
	UnitItemInfoService uiis;

	@Override
	public List<ItemConsumption> getUnitDayItemConsumption(String unitId, String queryDate, String programId,
			HashMap<String, Costitem> xm) {
		List<ItemConsumption> rtn = new ArrayList<ItemConsumption>();
		String sql = "";
		if (programId == null || "".equals(programId)) {
			programId = "0";
		}
		sql = "select ITEMID,sum(CONSUMPTION) sumValue,count(*) countValue,sum(ITEMCOST) ITEMCOST,sum(WORKINGHOUR) workinghour from COSTTEAMITEMDATA"
				+ " where UNITID=? and SUMMARYDAY=? and PROGRAMID=? group by ITEMID";
		List<Map<String, Object>> list = entityService.queryListMap(sql, unitId, queryDate, programId);
		if (list != null) {
			// 给ItemConsumption赋值并添加到rtn
			Double ljl, xmcb, xmdj, gzsj, sl, dj;
			Integer hzfs;
			String xmid;
			Object val;
			for (Map<String, Object> x : list) {
				xmid = (String) x.get("ITEMID");
				val = x.get("sumValue");
				if (val == null) {
					ljl = 0.0;
				} else {
					ljl = Double.parseDouble(val.toString());
				}
				val = x.get("countValue");
				if (val == null) {
					sl = 0.0;
				} else {
					sl = Double.parseDouble(val.toString());
				}
				val = x.get("ITEMCOST");
				if (val == null) {
					xmcb = 0.0;
				} else {
					xmcb = Double.parseDouble(val.toString());
				}
				val = x.get("workinghour");
				if (val == null) {
					gzsj = 0.0;
				} else {
					gzsj = Double.parseDouble(val.toString());
				}
				if (xm.containsKey(xmid)) {
					Costitem bx = xm.get(xmid);
					hzfs = bx.getSumtype();
					dj = bx.getItemprice();
				} else {
					hzfs = 0;
					dj = 0.0;
				}
				ItemConsumption b = new ItemConsumption();
				b.setItemId(xmid);
				if (ljl.compareTo(0.0) == 0) {
					xmdj = dj;
				} else {
					xmdj = xmcb / ljl;
				}
				b.setItemPrice(xmdj);
				b.setWorkingHour(gzsj);
				if (hzfs == null || hzfs == 0) {
					// 求和
					b.setConsumption(ljl);
				} else {
					// 求平均
					if (sl.compareTo(0.0) == 0) {
						b.setConsumption(0.0);
					} else {
						b.setConsumption(ljl / sl);
					}
				}
				rtn.add(b);
			}
		}
		return rtn;
	}

	@Override
	public List<ItemConsumption> getUnitPeriodItemConsumption(String unitId, String beginDate, String endDate,
			String programId, String teamid, HashMap<String, Costitem> xm) {
		List<ItemConsumption> rtn = new ArrayList<ItemConsumption>();
		if (unitId == null || "".equals(unitId)) {
			return rtn;
		}
		if (beginDate == null || "".equals(beginDate)) {
			return rtn;
		}
		if (endDate == null || "".equals(endDate)) {
			return rtn;
		}
		String sql = "";
		if (StringUtils.isNotEmpty(teamid) && !"0".equals(teamid)) {
			sql = "select ITEMID,sum(CONSUMPTION) sumValue,count(*) countValue,sum(ITEMCOST) ITEMCOST,sum(WORKINGHOUR) workinghour from COSTTEAMITEMDATA"
					+ " where UNITID=?";
			if (beginDate.indexOf(" ") >= 0) {
				// 时间类型
				sql = sql + " and BEGINTIME>=?";
			} else {
				sql = sql + " and WRITEDAY>=?";
			}
			if (endDate.indexOf(" ") >= 0) {
				// 时间类型
				sql = sql + " and ENDTIME<=?";
			} else {
				sql = sql + " and WRITEDAY<=?";
			}
		} else {
			sql = "select ITEMID,sum(CONSUMPTION) sumValue,count(*) countValue,sum(ITEMCOST) ITEMCOST,sum(WORKINGHOUR) workinghour from COSTSUMMARYITEMDATA"
					+ " where UNITID=? and WRITEDAY>=? and WRITEDAY<=? and REPORTTYPE='DayReport'";
			beginDate = beginDate.substring(0, 10);
			endDate = endDate.substring(0, 10);
		}
		List<Map<String, Object>> list = null;
		if (programId == null || "".equals(programId)) {
			programId = "0";
		}
		sql = sql + " and PROGRAMID=?";
		if (StringUtils.isNotEmpty(teamid) && !"0".equals(teamid)) {
			// 得到班组的项目量
			sql = sql + " and TEAMID=? group by ITEMID";
			list = entityService.queryListMap(sql, unitId, beginDate, endDate, programId, teamid);
		} else {
			// 得到核算对象的项目量
			sql = sql + " group by ITEMID";
			list = entityService.queryListMap(sql, unitId, beginDate, endDate, programId);
		}
		if (list != null) {
			// 给ItemConsumption赋值并添加到rtn
			Double ljl, xmcb, xmdj, gzsj, sl, dj;
			Integer hzfs;
			String xmid;
			Object val;
			for (Map<String, Object> x : list) {
				xmid = (String) x.get("ITEMID");
				val = x.get("sumValue");
				if (val == null) {
					ljl = 0.0;
				} else {
					ljl = Double.parseDouble(val.toString());
				}
				val = x.get("countValue");
				if (val == null) {
					sl = 0.0;
				} else {
					sl = Double.parseDouble(val.toString());
				}
				val = x.get("ITEMCOST");
				if (val == null) {
					xmcb = 0.0;
				} else {
					xmcb = Double.parseDouble(val.toString());
				}
				val = x.get("workinghour");
				if (val == null) {
					gzsj = 0.0;
				} else {
					gzsj = Double.parseDouble(val.toString());
				}
				if (xm.containsKey(xmid)) {
					Costitem bx = xm.get(xmid);
					hzfs = bx.getSumtype();
					dj = bx.getItemprice();
				} else {
					hzfs = 0;
					dj = 0.0;
				}
				ItemConsumption b = new ItemConsumption();
				b.setItemId(xmid);
				if (ljl.compareTo(0.0) == 0.0) {
					xmdj = dj;
				} else {
					xmdj = xmcb / ljl;
				}
				b.setItemPrice(xmdj);
				b.setWorkingHour(gzsj);
				if (hzfs == null || hzfs == 0) {
					// 求和
					b.setConsumption(ljl);
				} else {
					// 求平均
					if (sl.compareTo(0.0) == 0) {
						b.setConsumption(0.0);
					} else {
						b.setConsumption(ljl / sl);
					}
				}
				rtn.add(b);
			}
		}
		return rtn;
	}

	@Override
	public List<ItemConsumption> getUnitWeekItemConsumption(String unitId, String beginDate, String endDate,
			String programId, String teamid, HashMap<String, Costitem> xm) {
		List<ItemConsumption> rtn = new ArrayList<ItemConsumption>();
		if (unitId == null || "".equals(unitId)) {
			return rtn;
		}
		if (beginDate == null || "".equals(beginDate)) {
			return rtn;
		}
		if (endDate == null || "".equals(endDate)) {
			return rtn;
		}
		String sql = "";
		if (programId == null || "".equals(programId)) {
			programId = "0";
		}
		List<Map<String, Object>> list = null;
		if (StringUtils.isNotEmpty(teamid) && !"0".equals(teamid)) {
			// 班组使用交接班的记录（统计日期）
			sql = "select ITEMID,sum(CONSUMPTION) sumValue,count(*) countValue,sum(ITEMCOST) ITEMCOST,sum(WORKINGHOUR) workinghour from COSTTEAMITEMDATA"
					+ " where UNITID=? and SUMMARYDAY>=? and SUMMARYDAY<=? and TEAMID=? and PROGRAMID=? group by ITEMID";
			list = entityService.queryListMap(sql, unitId, beginDate.substring(0, 10), endDate.substring(0, 10), teamid,
					programId);
		} else {
			// 装置使用日报
			sql = "select ITEMID,sum(CONSUMPTION) sumValue,count(*) countValue,sum(ITEMCOST) ITEMCOST,sum(WORKINGHOUR) workinghour from COSTSUMMARYITEMDATA"
					+ " where UNITID=? and WRITEDAY>=? and WRITEDAY<=? and PROGRAMID=? and REPORTTYPE='DayReport' group by ITEMID";
			list = entityService.queryListMap(sql, unitId, beginDate.substring(0, 10), endDate.substring(0, 10),
					programId);
		}
		if (list != null) {
			// 给ItemConsumption赋值并添加到rtn
			Double ljl, xmcb, xmdj, gzsj, sl, dj;
			Integer hzfs;
			String xmid;
			Object val;
			for (Map<String, Object> x : list) {
				xmid = (String) x.get("ITEMID");
				val = x.get("sumValue");
				if (val == null) {
					ljl = 0.0;
				} else {
					ljl = Double.parseDouble(val.toString());
				}
				val = x.get("countValue");
				if (val == null) {
					sl = 0.0;
				} else {
					sl = Double.parseDouble(val.toString());
				}
				val = x.get("ITEMCOST");
				if (val == null) {
					xmcb = 0.0;
				} else {
					xmcb = Double.parseDouble(val.toString());
				}
				val = x.get("workinghour");
				if (val == null) {
					gzsj = 0.0;
				} else {
					gzsj = Double.parseDouble(val.toString());
				}
				if (xm.containsKey(xmid)) {
					Costitem bx = xm.get(xmid);
					hzfs = bx.getSumtype();
					dj = bx.getItemprice();
				} else {
					hzfs = 0;
					dj = 0.0;
				}
				ItemConsumption b = new ItemConsumption();
				b.setItemId(xmid);
				if (ljl.compareTo(0.0) == 0) {
					xmdj = dj;
				} else {
					xmdj = xmcb / ljl;
				}
				b.setItemPrice(xmdj);
				b.setWorkingHour(gzsj);
				if (hzfs == null || hzfs == 0) {
					// 求和
					b.setConsumption(ljl);
				} else {
					// 求平均
					if (sl.compareTo(0.0) == 0) {
						b.setConsumption(0.0);
					} else {
						b.setConsumption(ljl / sl);
					}
				}
				rtn.add(b);
			}
		}
		return rtn;
	}

	@Override
	public List<ItemConsumption> getTeamPeriodItemConsumption(String unitId, String beginDate, String endDate,
			String programId, HashMap<String, Costitem> xm) {
		List<ItemConsumption> rtn = new ArrayList<ItemConsumption>();
		if (unitId == null || "".equals(unitId)) {
			return rtn;
		}
		if (beginDate == null || "".equals(beginDate)) {
			return rtn;
		}
		if (endDate == null || "".equals(endDate)) {
			return rtn;
		}
		if (programId == null || "".equals(programId)) {
			programId = "0";
		}
		String sql = "";
		sql = "select ITEMID,TEAMID,sum(CONSUMPTION) sumValue,count(*) countValue,sum(ITEMCOST) ITEMCOST,sum(WORKINGHOUR) workinghour from COSTTEAMITEMDATA"
				+ " where UNITID=? ";
		if (beginDate.indexOf(" ") >= 0) {
			// 时间类型
			sql = sql + " and BEGINTIME>=? ";
		} else {
			sql = sql + " and WRITEDAY>=? ";
		}
		if (endDate.indexOf(" ") >= 0) {
			// 时间类型
			sql = sql + " and ENDTIME<=? ";
		} else {
			sql = sql + " and WRITEDAY<=? ";
		}
		List<Map<String, Object>> list = null;
		sql = sql + " and PROGRAMID=? group by ITEMID,TEAMID";
		list = entityService.queryListMap(sql, unitId, beginDate, endDate, programId);
		if (list != null) {
			// 给ItemConsumption赋值并添加到rtn
			Double ljl, xmcb, xmdj, gzsj, sl, dj;
			Integer hzfs;
			String xmid, teamid;
			Object val;
			for (Map<String, Object> x : list) {
				xmid = (String) x.get("ITEMID");
				teamid = (String) x.get("TEAMID");
				val = x.get("sumValue");
				if (val == null) {
					ljl = 0.0;
				} else {
					ljl = Double.parseDouble(val.toString());
				}
				val = x.get("countValue");
				if (val == null) {
					sl = 0.0;
				} else {
					sl = Double.parseDouble(val.toString());
				}
				val = x.get("ITEMCOST");
				if (val == null) {
					xmcb = 0.0;
				} else {
					xmcb = Double.parseDouble(val.toString());
				}
				val = x.get("workinghour");
				if (val == null) {
					gzsj = 0.0;
				} else {
					gzsj = Double.parseDouble(val.toString());
				}
				if (xm.containsKey(xmid)) {
					Costitem bx = xm.get(xmid);
					hzfs = bx.getSumtype();
					dj = bx.getItemprice();
				} else {
					hzfs = 0;
					dj = 0.0;
				}
				ItemConsumption b = new ItemConsumption();
				b.setItemId(xmid);
				b.setTeamId(teamid);
				if (ljl.compareTo(0.0) == 0) {
					xmdj = dj;
				} else {
					xmdj = xmcb / ljl;
				}
				b.setItemPrice(xmdj);
				b.setWorkingHour(gzsj);
				if (hzfs == null || hzfs == 0) {
					// 求和
					b.setConsumption(ljl);
				} else {
					// 求平均
					if (sl.compareTo(0.0) == 0) {
						b.setConsumption(0.0);
					} else {
						b.setConsumption(ljl / sl);
					}
				}
				rtn.add(b);
			}
		}
		return rtn;
	}

	@Override
	public List<ItemConsumption> getTeamProgramItemConsumption(String unitId, String queryDate, String programId,
			String shiftId, String teamId, HashMap<String, Costitem> xm) {
		List<ItemConsumption> rtn = new ArrayList<ItemConsumption>();
		if (programId == null || "".equals(programId)) {
			programId = "0";
		}
		String sql = "select ITEMID,sum(CONSUMPTION) sumValue,count(*) countValue,sum(ITEMCOST) ITEMCOST,sum(WORKINGHOUR) workinghour from COSTTEAMBATCHITEMDATA"
				+ " where UNITID=? and WRITEDAY=? and PROGRAMID=? and SHIFTID=? and TEAMID=? group by ITEMID";
		List<Map<String, Object>> list = entityService.queryListMap(sql, unitId, queryDate, programId, shiftId, teamId);
		if (list != null) {
			// 给ItemConsumption赋值并添加到rtn
			Double ljl, xmcb, xmdj, gzsj, sl, dj;
			Integer hzfs;
			String xmid;
			Object val;
			for (Map<String, Object> x : list) {
				xmid = (String) x.get("ITEMID");
				val = x.get("sumValue");
				if (val == null) {
					ljl = 0.0;
				} else {
					ljl = Double.parseDouble(val.toString());
				}
				val = x.get("countValue");
				if (val == null) {
					sl = 0.0;
				} else {
					sl = Double.parseDouble(val.toString());
				}
				val = x.get("ITEMCOST");
				if (val == null) {
					xmcb = 0.0;
				} else {
					xmcb = Double.parseDouble(val.toString());
				}
				val = x.get("workinghour");
				if (val == null) {
					gzsj = 0.0;
				} else {
					gzsj = Double.parseDouble(val.toString());
				}
				if (xm.containsKey(xmid)) {
					Costitem bx = xm.get(xmid);
					hzfs = bx.getSumtype();
					dj = bx.getItemprice();
				} else {
					hzfs = 0;
					dj = 0.0;
				}
				ItemConsumption b = new ItemConsumption();
				b.setItemId(xmid);
				if (ljl.compareTo(0.0) == 0) {
					xmdj = dj;
				} else {
					xmdj = xmcb / ljl;
				}
				b.setItemPrice(xmdj);
				b.setWorkingHour(gzsj);
				if (hzfs == null || hzfs == 0) {
					// 求和
					b.setConsumption(ljl);
				} else {
					// 求平均
					if (sl.compareTo(0.0) == 0) {
						b.setConsumption(0.0);
					} else {
						b.setConsumption(ljl / sl);
					}
				}
				rtn.add(b);
			}
		}
		return rtn;
	}

	@Override
	public List<ItemConsumption> getTeamItemConsumption(String unitId, String queryDate, String shiftId, String teamId,
			HashMap<String, Costitem> xm) {
		List<ItemConsumption> rtn = new ArrayList<ItemConsumption>();
		String sql = "select ITEMID,sum(CONSUMPTION) sumValue,count(*) countValue,sum(ITEMCOST) ITEMCOST,sum(WORKINGHOUR) workinghour from COSTTEAMBATCHITEMDATA"
				+ " where UNITID=? and WRITEDAY=? and SHIFTID=? and TEAMID=? and PROGRAMID='0' group by ITEMID";
		List<Map<String, Object>> list = entityService.queryListMap(sql, unitId, queryDate, shiftId, teamId);
		if (list != null) {
			// 给ItemConsumption赋值并添加到rtn
			Double ljl, xmcb, xmdj, gzsj, sl, dj;
			Integer hzfs;
			String xmid;
			Object val;
			for (Map<String, Object> x : list) {
				xmid = (String) x.get("ITEMID");
				val = x.get("sumValue");
				if (val == null) {
					ljl = 0.0;
				} else {
					ljl = Double.parseDouble(val.toString());
				}
				val = x.get("countValue");
				if (val == null) {
					sl = 0.0;
				} else {
					sl = Double.parseDouble(val.toString());
				}
				val = x.get("ITEMCOST");
				if (val == null) {
					xmcb = 0.0;
				} else {
					xmcb = Double.parseDouble(val.toString());
				}
				val = x.get("workinghour");
				if (val == null) {
					gzsj = 0.0;
				} else {
					gzsj = Double.parseDouble(val.toString());
				}
				ItemConsumption b = new ItemConsumption();
				b.setItemId(xmid);
				if (xm.containsKey(xmid)) {
					Costitem bx = xm.get(xmid);
					hzfs = bx.getSumtype();
					dj = bx.getItemprice();
				} else {
					hzfs = 0;
					dj = 0.0;
				}
				if (ljl.compareTo(0.0) == 0) {
					xmdj = dj;
				} else {
					xmdj = xmcb / ljl;
				}
				b.setItemPrice(xmdj);
				b.setWorkingHour(gzsj);
				if (hzfs == null || hzfs == 0) {
					// 求和
					b.setConsumption(ljl);
				} else {
					// 求平均
					if (sl.compareTo(0.0) == 0) {
						b.setConsumption(0.0);
					} else {
						b.setConsumption(ljl / sl);
					}
				}
				rtn.add(b);
			}
		}
		return rtn;
	}

	@Override
	public List<ItemConsumption> getSubUnitItemConsumption(String unitId, String queryDate, String shiftId,
			String teamId, String programId, HashMap<String, Costitem> xm) {
		List<ItemConsumption> rtn = new ArrayList<ItemConsumption>();
		List<Map<String, Object>> list = null;
		if ("0".equals(teamId)) {
			// 未传入班组，这时使用日报数据
			String sql = "select b.ITEMID,sum(a.CONSUMPTION) sumValue,count(*) countValue,sum(a.ITEMCOST) ITEMCOST,sum(a.WORKINGHOUR) workinghour"
					+ " from COSTSUMMARYITEMDATA a,COSTITEM b where a.ITEMID=b.ID"
					+ " and reporttype='DayReport' and a.UNITID in (select id from COSTUINT where pid=?) and writeday=? and PROGRAMID=?"
					+ " group by b.ITEMID";
			list = entityService.queryListMap(sql, unitId, queryDate, programId);
		} else {
			// 传入班组，这时使用交接班数据
			String sql = "select b.ITEMID,sum(CONSUMPTION) sumValue,count(*) countValue,sum(ITEMCOST) ITEMCOST,sum(WORKINGHOUR) workinghour"
					+ " from COSTTEAMITEMDATA a,COSTITEM b where a.ITEMID=b.ID"
					+ " and a.UNITID in (select id from COSTUINT where pid=?) and a.WRITEDAY=? and a.SHIFTID=? and a.PROGRAMID=?"
					+ " group by b.ITEMID";
			list = entityService.queryListMap(sql, unitId, queryDate, shiftId, programId);
		}

		if (list != null) {
			// 给ItemConsumption赋值并添加到rtn
			Double ljl, xmcb, xmdj, gzsj, sl, dj;
			Integer hzfs;
			String xmid;
			Object val;
			for (Map<String, Object> x : list) {
				xmid = (String) x.get("ITEMID");
				val = x.get("sumValue");
				if (val == null) {
					ljl = 0.0;
				} else {
					ljl = Double.parseDouble(val.toString());
				}
				val = x.get("countValue");
				if (val == null) {
					sl = 0.0;
				} else {
					sl = Double.parseDouble(val.toString());
				}
				val = x.get("ITEMCOST");
				if (val == null) {
					xmcb = 0.0;
				} else {
					xmcb = Double.parseDouble(val.toString());
				}
				val = x.get("workinghour");
				if (val == null) {
					gzsj = 0.0;
				} else {
					gzsj = Double.parseDouble(val.toString());
				}
				if (xm.containsKey(xmid)) {
					Costitem bx = xm.get(xmid);
					hzfs = bx.getSumtype();
					dj = bx.getItemprice();
				} else {
					hzfs = 0;
					dj = 0.0;
				}
				ItemConsumption b = new ItemConsumption();
				b.setItemId(xmid);
				if (ljl.compareTo(0.0) == 0) {
					xmdj = dj;
				} else {
					xmdj = xmcb / ljl;
				}
				b.setItemPrice(xmdj);
				b.setWorkingHour(gzsj);
				if (hzfs == null || hzfs == 0) {
					// 求和
					b.setConsumption(ljl);
				} else {
					// 求平均
					if (sl.compareTo(0.0) == 0) {
						b.setConsumption(0.0);
					} else {
						b.setConsumption(ljl / sl);
					}
				}
				rtn.add(b);
			}
		}
		return rtn;
	}

	@Override
	public HashMap<String, HashMap<String, ItemLedgerVo>> getItemLedgerData(TeamProgramDto dto) {
		HashMap<String, HashMap<String, ItemLedgerVo>> rtn = new HashMap<String, HashMap<String, ItemLedgerVo>>();
		if (dto != null) {
			String unitid = dto.getUnitId();// 核算对象ID
			if (StringUtils.isEmpty(unitid)) {
				return rtn;// 传入核算对象错误，直接返回
			}
			String teamid = dto.getTeamId();// 班组ID
			if (StringUtils.isEmpty(teamid)) {
				teamid = "0";
			}
			String faid = dto.getProgramId();// 方案ID
			if (StringUtils.isEmpty(faid)) {
				faid = "0";// 全部
			}
			String ksrq = dto.getShiftBeginTime();// 开始日期
			if (StringUtils.isEmpty(ksrq)) {
				return rtn;// 传入日期错误，直接返回
			}
			String jzrq = dto.getShiftEndTime();// 截止日期
			if (StringUtils.isEmpty(jzrq)) {
				return rtn;// 传入日期错误，直接返回
			}
			String flid = dto.getShiftId();// 分类ID
			if (StringUtils.isEmpty(flid)) {
				flid = "0";
			}
			// 得到项目信息，主要是为了得到项目分类ID
			String xmid, xmfl, xmmc, rq;
			Double drl, ljl, cs;
			HashMap<String, Costitem> im = new HashMap<String, Costitem>();
			List<Costitem> infos = this.uiis.getItem(unitid, jzrq);
			if (infos != null) {
				for (Costitem x : infos) {
					xmfl = x.getPid();
					if (StringUtils.isEmpty(xmfl)) {
						xmfl = "";
					}
					if (!"0".equals(flid)) {
						// 需要比对分类
						if (!xmfl.equals(flid)) {
							// 不同分类的不添加
							continue;
						}
					}
					im.put(x.getId(), x);
				}
			}
			// 得到数据
			HashMap<String, HashMap<String, Double>> ljm = new HashMap<String, HashMap<String, Double>>();
			if ("0".equals(teamid)) {
				// 从日报得到数据
				Where where = Where.create();
				where.eq(CostSummaryItemData::getUnitId, unitid);
				where.eq(CostSummaryItemData::getReportType, "DayReport");
				where.eq(CostSummaryItemData::getProgramId, faid);
				where.ge(CostSummaryItemData::getWriteDay, ksrq);
				where.le(CostSummaryItemData::getWriteDay, jzrq);
				List<CostSummaryItemData> list = entityService.queryList(CostSummaryItemData.class, where, null);
				if (list != null) {
					for (CostSummaryItemData yn : list) {
						rq = yn.getWriteDay();
						HashMap<String, ItemLedgerVo> xp = null;
						if (rtn.containsKey(rq)) {
							xp = rtn.get(rq);
						} else {
							xp = new HashMap<String, ItemLedgerVo>();
						}
						xmid = yn.getItemId();
						if (im.containsKey(xmid)) {
							Costitem nn = im.get(xmid);
							xmfl = nn.getPid();// 分类ID
							xmmc = nn.getItemname();// 项目名称
							if (StringUtils.isEmpty(xmmc)) {
								xmmc = "";
							}
							drl = yn.getWriteConsumption();
							if (drl == null) {
								drl = 0.0;
							}
							if (ljm.containsKey(xmid)) {
								HashMap<String, Double> ll = ljm.get(xmid);
								ljl = ll.get("l") + drl;
								cs = ll.get("s") + 1;
								ll.put("l", ljl);
								ll.put("s", cs);
								ljm.put(xmid, ll);
							} else {
								HashMap<String, Double> ll = new HashMap<String, Double>();
								ljl = drl;
								ll.put("l", ljl);
								ll.put("s", 1.0);
								ljm.put(xmid, ll);
							}
							ItemLedgerVo vo = new ItemLedgerVo();
							vo.setClassId(xmfl);
							vo.setItemId(xmid);
							vo.setItemName(xmmc);
							vo.setRq(rq);
							vo.setDayCount(drl);
							vo.setSumCount(ljl);
							xp.put(xmid, vo);
							rtn.put(rq, xp);
						}
					}
					if (ljm != null && ljm.size() > 0) {
						for (Entry<String, HashMap<String, Double>> yl : ljm.entrySet()) {
							xmid = yl.getKey();
							HashMap<String, Double> ll = yl.getValue();
							ljl = ll.get("l");
							cs = ll.get("s");
							// 累加
							rq = "累加";
							HashMap<String, ItemLedgerVo> xp1 = null;
							if (rtn.containsKey(rq)) {
								xp1 = rtn.get(rq);
							} else {
								xp1 = new HashMap<String, ItemLedgerVo>();
							}
							ItemLedgerVo vo1 = new ItemLedgerVo();
							vo1.setClassId("");
							vo1.setItemId(xmid);
							vo1.setItemName("");
							vo1.setRq(rq);
							vo1.setDayCount(ljl);
							vo1.setSumCount(null);
							xp1.put(xmid, vo1);
							rtn.put(rq, xp1);
							// 平均
							rq = "平均";
							HashMap<String, ItemLedgerVo> xp2 = null;
							if (rtn.containsKey(rq)) {
								xp2 = rtn.get(rq);
							} else {
								xp2 = new HashMap<String, ItemLedgerVo>();
							}
							ItemLedgerVo vo2 = new ItemLedgerVo();
							vo2.setClassId("");
							vo2.setItemId(xmid);
							vo2.setItemName("");
							vo2.setRq(rq);
							if (cs.compareTo(0.0) == 0) {
								vo2.setDayCount(null);
							} else {
								vo2.setDayCount(ljl / cs);
							}
							vo2.setSumCount(null);
							xp2.put(xmid, vo2);
							rtn.put(rq, xp2);
						}
					}
				}
			} else {
				// 从交接班得到数据
				Where where = Where.create();
				where.eq(CostTeamItemData::getUnitId, unitid);
				where.eq(CostTeamItemData::getProgramId, faid);
				where.eq(CostTeamItemData::getTeamId, teamid);
				where.ge(CostTeamItemData::getWriteDay, ksrq);
				where.le(CostTeamItemData::getWriteDay, jzrq);
				List<CostTeamItemData> list = entityService.queryList(CostTeamItemData.class, where, null);
				if (list != null) {
					for (CostTeamItemData yn : list) {
						rq = yn.getWriteDay();
						HashMap<String, ItemLedgerVo> xp = null;
						if (rtn.containsKey(rq)) {
							xp = rtn.get(rq);
						} else {
							xp = new HashMap<String, ItemLedgerVo>();
						}
						xmid = yn.getItemId();
						if (im.containsKey(xmid)) {
							Costitem nn = im.get(xmid);
							xmfl = nn.getPid();// 分类ID
							xmmc = nn.getItemname();// 项目名称
							if (StringUtils.isEmpty(xmmc)) {
								xmmc = "";
							}
							drl = yn.getWriteConsumption();
							if (drl == null) {
								drl = 0.0;
							}
							if (ljm.containsKey(xmid)) {
								HashMap<String, Double> ll = ljm.get(xmid);
								ljl = ll.get("l") + drl;
								cs = ll.get("s") + 1;
								ll.put("l", ljl);
								ll.put("s", cs);
								ljm.put(xmid, ll);
							} else {
								HashMap<String, Double> ll = new HashMap<String, Double>();
								ljl = drl;
								ll.put("l", ljl);
								ll.put("s", 1.0);
								ljm.put(xmid, ll);
							}
							ItemLedgerVo vo = new ItemLedgerVo();
							vo.setClassId(xmfl);
							vo.setItemId(xmid);
							vo.setItemName(xmmc);
							vo.setRq(rq);
							vo.setDayCount(drl);
							vo.setSumCount(ljl);
							xp.put(xmid, vo);
							rtn.put(rq, xp);
						}
					}
					if (ljm != null && ljm.size() > 0) {
						for (Entry<String, HashMap<String, Double>> yl : ljm.entrySet()) {
							xmid = yl.getKey();
							HashMap<String, Double> ll = yl.getValue();
							ljl = ll.get("l");
							cs = ll.get("s");
							// 累加
							rq = "累加";
							HashMap<String, ItemLedgerVo> xp1 = null;
							if (rtn.containsKey(rq)) {
								xp1 = rtn.get(rq);
							} else {
								xp1 = new HashMap<String, ItemLedgerVo>();
							}
							ItemLedgerVo vo1 = new ItemLedgerVo();
							vo1.setClassId("");
							vo1.setItemId(xmid);
							vo1.setItemName("");
							vo1.setRq(rq);
							vo1.setDayCount(ljl);
							vo1.setSumCount(null);
							xp1.put(xmid, vo1);
							rtn.put(rq, xp1);
							// 平均
							rq = "平均";
							HashMap<String, ItemLedgerVo> xp2 = null;
							if (rtn.containsKey(rq)) {
								xp2 = rtn.get(rq);
							} else {
								xp2 = new HashMap<String, ItemLedgerVo>();
							}
							ItemLedgerVo vo2 = new ItemLedgerVo();
							vo2.setClassId("");
							vo2.setItemId(xmid);
							vo2.setItemName("");
							vo2.setRq(rq);
							if (cs.compareTo(0.0) == 0) {
								vo2.setDayCount(null);
							} else {
								vo2.setDayCount(ljl / cs);
							}
							vo2.setSumCount(null);
							xp2.put(xmid, vo2);
							rtn.put(rq, xp2);
						}
					}
				}
			}
		}
		return rtn;
	}

	@Override
	public List<ItemConsumption> getUnitYearItemConsumption(String unitId, String beginMonth, String endMonth,
			String programId, HashMap<String, Costitem> xm) {
		List<ItemConsumption> rtn = new ArrayList<ItemConsumption>();
		if (unitId == null || "".equals(unitId)) {
			return rtn;
		}
		if (beginMonth == null || "".equals(beginMonth)) {
			return rtn;
		}
		if (endMonth == null || "".equals(endMonth)) {
			return rtn;
		}
		if (programId == null || "".equals(programId)) {
			programId = "0";
		}
		// 不包含截止月份
		String sql = "select ITEMID,sum(CONSUMPTION) sumValue,count(*) countValue,sum(WORKINGHOUR) workinghour from COSTSUMMARYITEMDATA"
				+ " where UNITID=? and WRITEDAY>=? and WRITEDAY<? and PROGRAMID=? and REPORTTYPE='MonthReport' group by ITEMID";
		List<Map<String, Object>> list = entityService.queryListMap(sql, unitId, beginMonth, endMonth, programId);
		if (list != null) {
			// 给ItemConsumption赋值并添加到rtn
			Double ljl, gzsj, sl;
			Integer hzfs;
			String xmid;
			Object val;
			for (Map<String, Object> x : list) {
				xmid = (String) x.get("ITEMID");
				val = x.get("sumValue");
				if (val == null) {
					ljl = 0.0;
				} else {
					ljl = Double.parseDouble(val.toString());
				}
				val = x.get("countValue");
				if (val == null) {
					sl = 0.0;
				} else {
					sl = Double.parseDouble(val.toString());
				}
				val = x.get("workinghour");
				if (val == null) {
					gzsj = 0.0;
				} else {
					gzsj = Double.parseDouble(val.toString());
				}
				if (xm.containsKey(xmid)) {
					Costitem bx = xm.get(xmid);
					hzfs = bx.getSumtype();
				} else {
					hzfs = 0;
				}
				ItemConsumption b = new ItemConsumption();
				b.setItemId(xmid);
				b.setWorkingHour(gzsj);
				if (hzfs == null || hzfs == 0) {
					// 求和
					b.setConsumption(ljl);
				} else {
					// 求平均
					if (sl.compareTo(0.0) == 0) {
						b.setConsumption(0.0);
					} else {
						b.setConsumption(ljl / sl);
					}
				}
				rtn.add(b);
			}
		}
		return rtn;
	}
}