package com.yunhesoft.leanCosting.costReport.service.impl;


import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportMethodQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ShiftInstrumentWriteQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ShiftInstrumentWriteSaveDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostItemForWriteVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostTeamInstrumentDataVo;
import com.yunhesoft.leanCosting.costReport.service.GetCostItemInfoService;
import com.yunhesoft.leanCosting.costReport.service.ICostReportMethodService;
import com.yunhesoft.leanCosting.costReport.service.IShiftInstrumentWriteService;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduPlanStart;
import com.yunhesoft.leanCosting.productSchedu.service.IZzRunStateService;
import com.yunhesoft.leanCosting.programConfig.entity.dto.ProgramQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.system.kernel.service.EntityService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 *	交接班仪表填写相关服务接口实现类
 * <AUTHOR>
 * @date 2023-10-17
 */
@Service
public class ShiftInstrumentWriteServiceImpl implements IShiftInstrumentWriteService {

	
	@Autowired
	private EntityService entityService;
		
	@Autowired
	private ICostReportMethodService methodService; //报表方法
	
	@Autowired
	private GetCostItemInfoService costItemService; //核算项目
	
	@Autowired
	private IProgramService programService; //方案
	
	@Autowired
	private IZzRunStateService runStateService; //运行状态
	
	
	/**
	 *	获取交接班仪表填写数据
	 * @param queryDto
	 * @return
	 */
	public List<CostTeamInstrumentDataVo> getShiftInstrumentWriteList(ShiftInstrumentWriteQueryDto queryDto) {
		//返回结果数据
		List<CostTeamInstrumentDataVo> result = new ArrayList<CostTeamInstrumentDataVo>();
		//返回的交接班数据
		List<CostTeamInfo> teamList = new ArrayList<CostTeamInfo>(); //交接班信息
		//按照方案id返回的仪表数据
		HashMap<String, List<CostTeamInstrumentDataVo>> dataMap = new HashMap<String, List<CostTeamInstrumentDataVo>>();
		//按照交接班id返回的仪表填写数据
		HashMap<String, HashMap<String, CostTeamInstrumentData>> setCostTeamInstrumentDataMap = new HashMap<String, HashMap<String,CostTeamInstrumentData>>();
		
		//操作的交接班数据
		List<CostTeamInfo> addTeamList = new ArrayList<CostTeamInfo>(); //新增
		List<CostTeamInfo> delTeamList = new ArrayList<CostTeamInfo>(); //删除
		
		if(StringUtils.isNotNull(queryDto)) {
			String mainId = queryDto.getMainId(); //主记录id
			if(StringUtils.isNotEmpty(mainId)) {
				//根据主记录id获取交接班信息
				CostTeamInfo mainObj = methodService.getCostTeamInfoById(mainId);
				if(StringUtils.isNotNull(mainObj)) {
					String unitId = mainObj.getUnitId(); //核算对象id
					String writeDay = mainObj.getWriteDay(); //填写日期
					String teamId = mainObj.getTeamId(); //班组id
					String shiftId = mainObj.getShiftId(); //班次id
					String summaryDay = mainObj.getSummaryDay(); //统计日期
					//String programId = mainObj.getProgramId(); //方案id
					String beginTime = mainObj.getBeginTime(); //开始时间
					String endTime = mainObj.getEndTime(); //结束时间

					if(StringUtils.isNotEmpty(unitId)&&StringUtils.isNotEmpty(writeDay)&&StringUtils.isNotEmpty(teamId)&&StringUtils.isNotEmpty(shiftId)) {
						//根据主记录的信息，获取交接班和仪表数据
						CostReportMethodQueryDto dto = new CostReportMethodQueryDto();
						dto.setUnitId(unitId);
						dto.setWriteDay(writeDay);
						dto.setTeamId(teamId);
						dto.setShiftId(shiftId);
						//获取已设置的交接班数据
						List<CostTeamInfo> setCostTeamInfoList = methodService.getCostTeamInfoList(dto);
						HashMap<String, CostTeamInfo> setCostTeamInfoMap = this.getCostTeamInfoMap(setCostTeamInfoList, mainId);
						
						//获取已设置的仪表填写数据
						List<CostTeamInstrumentData> setCostTeamInstrumentDataList = methodService.getCostTeamInstrumentDataList(dto);
						setCostTeamInstrumentDataMap = this.getCostTeamInstrumentDataMap(setCostTeamInstrumentDataList);
						
						//获取排产计划数据
						List<String> programIdList = new ArrayList<String>(); //方案id列表
						LinkedHashMap<String, List<ProductScheduPlanStart>> planMap = new LinkedHashMap<String, List<ProductScheduPlanStart>>();
						try {
							//planMap = runStateService.getUnitProgByksrqjzrq(unitId, beginTime, endTime);
							planMap = this.getSSSMap(unitId);
						}catch(Exception e) {
							planMap = null;
						}
						if(StringUtils.isNotEmpty(planMap)&&planMap.containsKey(unitId)) {
							List<ProductScheduPlanStart> planList = planMap.get(unitId);
							if(StringUtils.isNotEmpty(planList)) {
								List<String> hasList = new ArrayList<String>(); //已存在记录
								for (int i = 0; i < planList.size(); i++) {
									ProductScheduPlanStart planObj = planList.get(i);
									String programid_plan = planObj.getProgramid();
									String startdatetime_plan = planObj.getStartdatetime();
									String enddatetime_plan = planObj.getEnddatetime();
									String key_plan = programid_plan+"_"+startdatetime_plan+"_"+enddatetime_plan;
									if(hasList.contains(key_plan)) { //去掉重复记录
										continue;
									}else {
										hasList.add(key_plan);
									}
									CostTeamInfo teamObj = new CostTeamInfo();
									if(StringUtils.isNotEmpty(setCostTeamInfoMap)&&setCostTeamInfoMap.containsKey(key_plan)) { //存在
										teamObj = setCostTeamInfoMap.get(key_plan);
										setCostTeamInfoMap.remove(key_plan);
									}else { //不存在，新增
										teamObj = new CostTeamInfo();
										teamObj.setId(TMUID.getUID());
										teamObj.setUnitId(unitId);
										teamObj.setWriteDay(writeDay);
										teamObj.setProgramId(programid_plan);
										teamObj.setTeamId(teamId);
										teamObj.setShiftId(shiftId);
										teamObj.setBeginTime(startdatetime_plan);
										teamObj.setEndTime(enddatetime_plan);
										teamObj.setSummaryDay(summaryDay);
										teamObj.setRemark("");
										addTeamList.add(teamObj);
									}
									teamList.add(teamObj);
									if(!programIdList.contains(programid_plan)) {
										programIdList.add(programid_plan);
									}
								}
							}
						}else { //调用接口出现异常或返回空值，直接返回，不再继续执行
							return result;
						}
						//剩余记录删除
						if(StringUtils.isNotEmpty(setCostTeamInfoMap)) {
							Iterator<Entry<String, CostTeamInfo>> entryMap = setCostTeamInfoMap.entrySet().iterator();
							while(entryMap.hasNext()){
								Entry<String, CostTeamInfo> entry = entryMap.next();
								CostTeamInfo delObj = entry.getValue();
								delTeamList.add(delObj);
							}
						}
						//有交接班信息（方案id），查询分类、项目、仪表等数据
						if(StringUtils.isNotEmpty(programIdList)) {
							
							//获取方案名称
							Map<String, ProgramItem> programItemMap = new HashMap<String, ProgramItem>();
							ProgramQueryDto programDto = new ProgramQueryDto();
							if(programIdList.size()==1) {
								programDto.setId(programIdList.get(0));
							}else {
								programDto.setIdList(programIdList);
							}
							List<ProgramItem> programItemList = programService.getProgramItemList(programDto);
							if(StringUtils.isNotEmpty(programItemList)) {
								programItemMap = programItemList.stream().collect(Collectors.toMap(ProgramItem::getId, Function.identity()));
							}
							
							//遍历方案id，获取对应仪表数据
							for (int i = 0; i < programIdList.size(); i++) {
								String programIdStr = programIdList.get(i);
								if(StringUtils.isNotEmpty(programItemMap)&&programItemMap.containsKey(programIdStr)) {
									String programName = programItemMap.get(programIdStr).getPiName();
									
									// 获取仪表数据
									List<CostTeamInstrumentDataVo> dataList = new ArrayList<CostTeamInstrumentDataVo>();
									List<CostItemForWriteVo> instrumentList = costItemService.shiftWriteItemInfo(unitId, writeDay, programIdStr);
									if(StringUtils.isNotEmpty(instrumentList)) {
										for (int j = 0; j < instrumentList.size(); j++) {
											CostItemForWriteVo instrumentObj = instrumentList.get(j);
											CostTeamInstrumentDataVo vo = new CostTeamInstrumentDataVo();
											BeanUtils.copyProperties(instrumentObj, vo); //赋予返回对象
											vo.setProgramId(programIdStr);
											vo.setProgramName(programName);
											//vo.setPid(pid); //生成数据时，使用主记录的id
											vo.setId("");
											vo.setUnitId(unitId);
											vo.setWriteDay(writeDay);
											vo.setTeamId(teamId);
											vo.setShiftId(shiftId);
											vo.setPreviousReadOut(0d); //前表数
											vo.setPreviousReadTime(""); //前表读取时间
											vo.setLastReadOut(0d); //后表数
											vo.setLastReadTime(""); //后表读取时间
											vo.setCalcVal(0d); //仪表计算量
											vo.setWriteVal(0d); //仪表调整量
											dataList.add(vo);
										}
									}
									if(StringUtils.isNotEmpty(dataList)) {
										dataMap.put(programIdStr, dataList);
									}
								}
							}
						}
					}
				}
			}
		}
		String ret = "";
		if ("".equals(ret) && StringUtils.isNotEmpty(addTeamList)) {
			if (entityService.insertBatch(addTeamList) == 0) {
				ret = "添加失败（交接班信息）！";
			}else {
				//主记录新增成功后，新增对应的交接班仪表录入数据
				if(StringUtils.isNotEmpty(dataMap)) {
					List<CostTeamInstrumentData> addDataList = new ArrayList<CostTeamInstrumentData>();
					for (int i = 0; i < addTeamList.size(); i++) {
						CostTeamInfo addTeamObj = addTeamList.get(i);
						String id = addTeamObj.getId();
						String programId = addTeamObj.getProgramId();
						if(StringUtils.isNotEmpty(id)&&StringUtils.isNotEmpty(programId)&&dataMap.containsKey(programId)) {
							List<CostTeamInstrumentDataVo> dataList = dataMap.get(programId);
							if(StringUtils.isNotEmpty(dataList)) {
								for (int j = 0; j < dataList.size(); j++) {
									CostTeamInstrumentDataVo vo = dataList.get(j);
									
									CostTeamInstrumentData obj = new CostTeamInstrumentData();
									BeanUtils.copyProperties(vo, obj); //赋予返回对象
									obj.setId(TMUID.getUID());
									obj.setPid(id);
									addDataList.add(obj);
									
									//将新增的仪表填写数据放入Map中，组队显示数据用
									String instrumentId = obj.getInstrumentId();
									if(setCostTeamInstrumentDataMap.containsKey(id)) {
										HashMap<String, CostTeamInstrumentData> map = setCostTeamInstrumentDataMap.get(id);
										map.put(instrumentId, obj);
										setCostTeamInstrumentDataMap.put(id, map);
									}else {
										HashMap<String, CostTeamInstrumentData> map = new HashMap<String, CostTeamInstrumentData>();
										map.put(instrumentId, obj);
										setCostTeamInstrumentDataMap.put(id, map);
									}
								}
							}
						}
					}
					if(StringUtils.isNotEmpty(addDataList)) {
						if (entityService.insertBatch(addDataList, 500) == 0) {
							ret = "新增失败（交接班录入数据）！";
						}
					}
				}
			}
		}
		if ("".equals(ret) && StringUtils.isNotEmpty(delTeamList)) {
			if (entityService.deleteByIdBatch(delTeamList) == 0) {
				ret = "删除失败（交接班信息）！";
			}else {
				//主记录删除成功后，删除对应的交接班仪表录入数据
				List<String> delIdList = delTeamList.stream().filter(item -> StringUtils.isNotEmpty(item.getId())).map(item -> item.getId()).collect(Collectors.toList());
				if(StringUtils.isNotEmpty(delIdList)) {
					CostReportMethodQueryDto delDto = new CostReportMethodQueryDto();
					if(delIdList.size()==1) {
						delDto.setPId(delIdList.get(0));
					}else {
						delDto.setPIdList(delIdList);
					}
					List<CostTeamInstrumentData> teamInstrumentList = methodService.getCostTeamInstrumentDataList(delDto);
					if(StringUtils.isNotEmpty(teamInstrumentList)) {
						if (entityService.deleteByIdBatch(teamInstrumentList) == 0) {
							ret = "删除失败（交接班录入数据）！";
						}
					}
				}
			}
		}
		//组队返回数据
		if ("".equals(ret) && StringUtils.isNotEmpty(teamList) && StringUtils.isNotEmpty(dataMap)) {
			for (int i = 0; i < teamList.size(); i++) {
				CostTeamInfo teamObj = teamList.get(i);
				String id = teamObj.getId(); //交接班id
				String programId = teamObj.getProgramId(); //方案id
				if(dataMap.containsKey(programId)) {
					//仪表填写数据
					HashMap<String, CostTeamInstrumentData> setInstrumentDataMap = new HashMap<String, CostTeamInstrumentData>();
					if(StringUtils.isNotEmpty(setCostTeamInstrumentDataMap)&&setCostTeamInstrumentDataMap.containsKey(id)) {
						setInstrumentDataMap = setCostTeamInstrumentDataMap.get(id);
					}
					//仪表数据
					List<CostTeamInstrumentDataVo> instrumentDataList = dataMap.get(programId);
					if(StringUtils.isNotEmpty(instrumentDataList)) {
						int programNameCount = instrumentDataList.size(); //记录数
						String programIdOld = "";
						String classIdOld = "";
						String itemIdOld = "";
						//按照分类分组Map
						Map<String, List<CostTeamInstrumentDataVo>> instrumentClassCountMap = instrumentDataList.stream().collect(Collectors.groupingBy(CostTeamInstrumentDataVo::getClassId));
						//按照分类项目分组Map
						Map<String, List<CostTeamInstrumentDataVo>> classItemCountMap = new HashMap<String, List<CostTeamInstrumentDataVo>>();
						this.getDataMap(instrumentDataList, classItemCountMap);
						for (int j = 0; j < programNameCount; j++) {
							CostTeamInstrumentDataVo instrumentDataObj = instrumentDataList.get(j);
							instrumentDataObj.setPid(id);
							String instrumentId_o = instrumentDataObj.getInstrumentId();
							//仪表填写数据
							if(StringUtils.isNotEmpty(setInstrumentDataMap)&&setInstrumentDataMap.containsKey(instrumentId_o)) {
								CostTeamInstrumentData setInstrumentDataObj = setInstrumentDataMap.get(instrumentId_o);
								BeanUtils.copyProperties(setInstrumentDataObj, instrumentDataObj); //赋予返回对象
							}
							
							//设置其他显示属性值
							//  -- 方案名称合并
							String programId_o = instrumentDataObj.getProgramId();
							String programName_o = instrumentDataObj.getProgramName();
							instrumentDataObj.setProgramNameStr(programName_o);
							if(programIdOld.equals(programId_o)) {
								instrumentDataObj.setProgramName("");
								instrumentDataObj.setProgramNameCount(0);
							}else {
								instrumentDataObj.setProgramName(programName_o);
								instrumentDataObj.setProgramNameCount(programNameCount);
								programIdOld = programId_o;
							}
							//  -- 分类名称合并
							String classId_o = instrumentDataObj.getClassId();
							String className_o = instrumentDataObj.getClassName();
							instrumentDataObj.setClassNameStr(className_o);
							if(classIdOld.equals(classId_o)) {
								instrumentDataObj.setClassName("");
								instrumentDataObj.setClassNameCount(0);
							}else {
								instrumentDataObj.setClassName(className_o);
								int classNameCount = 1;
								if(StringUtils.isNotEmpty(instrumentClassCountMap)&&instrumentClassCountMap.containsKey(classId_o)) {
									classNameCount = instrumentClassCountMap.get(classId_o).size();
								}
								instrumentDataObj.setClassNameCount(classNameCount);
								classIdOld = classId_o;
							}
							//  -- 项目名称合并
							String classId_itemId_o = classId_o+"_"+instrumentDataObj.getItemId();
							String temName_o = instrumentDataObj.getItemName();
							instrumentDataObj.setItemNameStr(temName_o);
							if(itemIdOld.equals(classId_itemId_o)) {
								instrumentDataObj.setItemName("");
								instrumentDataObj.setItemNameCount(0);
							}else {
								instrumentDataObj.setItemName(temName_o);
								int itemNameCount = 1;
								if(StringUtils.isNotEmpty(classItemCountMap)&&classItemCountMap.containsKey(classId_itemId_o)) {
									itemNameCount = classItemCountMap.get(classId_itemId_o).size();
								}
								instrumentDataObj.setItemNameCount(itemNameCount);
								itemIdOld = classId_itemId_o;
							}
							result.add(instrumentDataObj);
						}
					}
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取交接班信息Map
	 * @param list
	 * @param mainId_not 刨除主记录id
	 * @return
	 */
	private HashMap<String, CostTeamInfo> getCostTeamInfoMap(List<CostTeamInfo> list,String mainId_not) {
		HashMap<String, CostTeamInfo> result = new HashMap<String, CostTeamInfo>();
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostTeamInfo obj = list.get(i);
				String id = obj.getId();
				if(StringUtils.isNotEmpty(mainId_not)&&mainId_not.equals(id)) { //刨除主记录id
					continue;
				}
				String programId = obj.getProgramId();
				String beginTime = obj.getBeginTime();
				String endTime = obj.getEndTime();
				String key = programId+"_"+beginTime+"_"+endTime;
				if(!result.containsKey(key)) {
					result.put(key, obj);
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取交接班仪表填写数据Map
	 * @param list
	 * @return
	 */
	private HashMap<String, HashMap<String, CostTeamInstrumentData>> getCostTeamInstrumentDataMap(List<CostTeamInstrumentData> list) {
		HashMap<String, HashMap<String, CostTeamInstrumentData>> result = new HashMap<String, HashMap<String, CostTeamInstrumentData>>();
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostTeamInstrumentData obj = list.get(i);
				String pid = obj.getPid();
				String instrumentId = obj.getInstrumentId();
				if(result.containsKey(pid)) {
					HashMap<String, CostTeamInstrumentData> map = result.get(pid);
					map.put(instrumentId, obj);
					result.put(pid, map);
				}else {
					HashMap<String, CostTeamInstrumentData> map = new HashMap<String, CostTeamInstrumentData>();
					map.put(instrumentId, obj);
					result.put(pid, map);
				}
			}
		}
		return result;
	}
	
	
	
	
	private LinkedHashMap<String, List<ProductScheduPlanStart>> getSSSMap(String unitId) {
		LinkedHashMap<String, List<ProductScheduPlanStart>> result = new LinkedHashMap<String, List<ProductScheduPlanStart>>();
		
		
		ProductScheduPlanStart obj2 = new ProductScheduPlanStart();
		obj2.setProgramid("ZZW8JTI700BC85URJC6094");
		obj2.setProgramname("高压");
		obj2.setStartdatetime("2023-10-10 00:00:00");
		obj2.setEnddatetime("2023-10-10 08:00:00");
		
		ProductScheduPlanStart obj1 = new ProductScheduPlanStart();
		obj1.setProgramid("ZZW8BP3E08FO3VJPT90403");
		obj1.setProgramname("常压");
		obj1.setStartdatetime("2023-10-10 08:00:00");
		obj1.setEnddatetime("2023-10-10 12:00:00");
		
		ProductScheduPlanStart obj3 = new ProductScheduPlanStart();
		obj3.setProgramid("ZZW8JTI700BC85URJC6094");
		obj3.setProgramname("高压");
		obj3.setStartdatetime("2023-10-10 12:00:00");
		obj3.setEnddatetime("2023-10-10 17:00:00");
		
		List<ProductScheduPlanStart> list1 = new ArrayList<ProductScheduPlanStart>();
		list1.add(obj2);
		list1.add(obj1);
		list1.add(obj3);
		
		result.put(unitId, list1);
		
		return result;
	}
	
	/**
	 *	获取数据Map
	 * @param list
	 * @param classItemMap
	 */
	private void getDataMap(List<CostTeamInstrumentDataVo> list,
		Map<String, List<CostTeamInstrumentDataVo>> classItemMap) {
		if(classItemMap==null) {
			classItemMap = new HashMap<String, List<CostTeamInstrumentDataVo>>();
		}
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				CostTeamInstrumentDataVo obj = list.get(i);
				String classId = obj.getClassId();
				String itemId = obj.getItemId();
				String key = classId+"_"+itemId;
				if(classItemMap.containsKey(key)) {
					List<CostTeamInstrumentDataVo> voList = classItemMap.get(key);
					voList.add(obj);
					classItemMap.put(key, voList);
				}else {
					List<CostTeamInstrumentDataVo> voList = new ArrayList<CostTeamInstrumentDataVo>();
					voList.add(obj);
					classItemMap.put(key, voList);
				}
			}
		}
	}
	
	
	/**
	 *	保存交接班仪表填写数据
	 * @param saveDto
	 * @return
	 */
	public String saveShiftInstrumentWriteData(ShiftInstrumentWriteSaveDto saveDto) {
		String result = "";
		List<CostTeamInstrumentData> addList = new ArrayList<CostTeamInstrumentData>();
		List<CostTeamInstrumentData> updList = new ArrayList<CostTeamInstrumentData>();
		List<CostTeamInstrumentData> delList = new ArrayList<CostTeamInstrumentData>();
		if (saveDto != null) {
			String mainId = saveDto.getMainId(); //主记录id
			List<CostTeamInstrumentDataVo> saveList = saveDto.getSaveInstrumentDataList(); //保存的仪表填写数据
			if(StringUtils.isNotEmpty(mainId)&&StringUtils.isNotEmpty(saveList)) {
				//根据主记录id获取交接班信息
				CostTeamInfo mainObj = methodService.getCostTeamInfoById(mainId);
				if(StringUtils.isNotNull(mainObj)) {
					String unitId = mainObj.getUnitId(); //核算对象id
					String writeDay = mainObj.getWriteDay(); //填写日期
					String teamId = mainObj.getTeamId(); //班组id
					String shiftId = mainObj.getShiftId(); //班次id
					if(StringUtils.isNotEmpty(unitId)&&StringUtils.isNotEmpty(writeDay)&&StringUtils.isNotEmpty(teamId)&&StringUtils.isNotEmpty(shiftId)) {
						//根据主记录的信息，获取交接班和仪表数据
						CostReportMethodQueryDto dto = new CostReportMethodQueryDto();
						dto.setUnitId(unitId);
						dto.setWriteDay(writeDay);
						dto.setTeamId(teamId);
						dto.setShiftId(shiftId);
						//获取已设置的仪表填写数据
						Map<String, CostTeamInstrumentData> dataMap = new HashMap<String, CostTeamInstrumentData>();
						List<CostTeamInstrumentData> dataList = methodService.getCostTeamInstrumentDataList(dto);
						if(StringUtils.isNotEmpty(dataList)) {
							dataMap = dataList.stream().collect(Collectors.toMap(CostTeamInstrumentData::getId, Function.identity()));
						}
						
						//遍历保存记录
						for (int i = 0; i < saveList.size(); i++) {
							CostTeamInstrumentDataVo saveObj = saveList.get(i);
							int rowFlag_save = saveObj.getFlag()==null?0:saveObj.getFlag();
							String id_save = saveObj.getId();
							if(rowFlag_save==-1) { //删除
								if(StringUtils.isNotEmpty(dataMap)&&StringUtils.isNotEmpty(id_save)&&dataMap.containsKey(id_save)) {
									CostTeamInstrumentData obj = dataMap.get(id_save);
									delList.add(obj);
								}
							}else { //新增或修改
								if(StringUtils.isNotEmpty(dataMap)&&StringUtils.isNotEmpty(id_save)&&dataMap.containsKey(id_save)) { //修改
									CostTeamInstrumentData obj = dataMap.get(id_save);
									BeanUtils.copyProperties(saveObj, obj); // 赋予返回对象
									updList.add(obj);
								}else { //新增
									CostTeamInstrumentData obj = new CostTeamInstrumentData();
									BeanUtils.copyProperties(saveObj, obj); // 赋予返回对象
									String id = obj.getId();
									if(StringUtils.isEmpty(id)) {
										obj.setId(TMUID.getUID());
									}
									addList.add(obj);
								}
							}
						}
					}
				}
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
			if (entityService.insertBatch(addList, 500) == 0) {
				result = "新增失败（交接班仪表填写）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
			if (entityService.updateBatch(updList, 500) == 0) {
				result = "修改失败（交接班仪表填写）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(delList)) {
			if (entityService.deleteByIdBatch(delList, 500) == 0) {
				result = "删除失败（交接班仪表填写）！";
			}
		}
		return result;
	}
	
	
}
