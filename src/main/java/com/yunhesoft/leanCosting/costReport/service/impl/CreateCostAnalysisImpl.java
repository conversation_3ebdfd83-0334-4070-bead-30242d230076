package com.yunhesoft.leanCosting.costReport.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.aviator.AviatorResult;
import com.yunhesoft.core.common.aviator.AviatorUtils;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.Maths;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.calcLogic.PublicMethods;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamReportInputDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostReportAnalysis;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostAnalysisVo;
import com.yunhesoft.leanCosting.costReport.service.ICreateCostAnalysis;
import com.yunhesoft.leanCosting.unitConf.entity.dto.BriefAnalysisQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.BriefAnalysisConf;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.vo.ClassVo;
import com.yunhesoft.leanCosting.unitConf.service.IBriefAnalysisService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class CreateCostAnalysisImpl implements ICreateCostAnalysis {

	private String _zbbl = "\\[.*?\\](\\.?\\w*)|\\<.*?\\>(\\.?\\w*)|\\{.*?\\}(\\.?(([\\u4e00-\\u9fa5]|\\w)+|\\w*))|\\[*\\w*[a-zA-Z_]+\\w*(\\.?\\w+)+\\]*";
	private PublicMethods pm = new PublicMethods();

	@Autowired
	EntityService entityService;
	@Autowired
	IBriefAnalysisService ibas;
	@Autowired
	UnitItemInfoService uiis;

	/**
	 * @category 删除旧的自动分析数据
	 * @param pid
	 * @return
	 */
	private boolean delOldAnalysis(String pid) {
		Where where = Where.create();
		where.eq(CostReportAnalysis::getPid, pid);
		where.eq(CostReportAnalysis::getContentType, 1);
		int bs = entityService.delete(CostReportAnalysis.class, where);
		if (bs < 0) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * @category 新增自动分析数据
	 * @param data
	 * @return
	 */
	private boolean insertTeamDayParamData(List<CostReportAnalysis> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	private List<BriefAnalysisConf> getAnalysisConfig(String unitid, String tbrq) {
		BriefAnalysisQueryDto queryDto = new BriefAnalysisQueryDto();
		queryDto.setUnitid(unitid);
		queryDto.setBegintime(tbrq);
		return ibas.getBriefAnalysisConfList(queryDto); // 查询简要分析设置
	}

	@Override
	public Boolean createAnalysis(TeamReportInputDto dto) {
		String pid = dto.getPid();
		String unitid = dto.getUnitId();
		String tbrq = dto.getWriteDay();
		String bzdm = dto.getTeamId();
		String bcdm = dto.getShiftId();
		String faid = dto.getProgramId();
		String bn = dto.getBatchNo();
		String bblx = "b01";// 目前只针对交接班
		String jzsj = dto.getEndtime();
		boolean result = true;
		if (StringUtils.isNotEmpty(unitid) && StringUtils.isNotEmpty(tbrq)) {
			// 传入数据正确
			List<CostReportAnalysis> insertList = new ArrayList<CostReportAnalysis>();// 简要分析新建的批处理list
			// 处理自动简要分析
			List<BriefAnalysisConf> acl = this.getAnalysisConfig(unitid, tbrq); // 查询简要分析项目
			if (acl != null) {// 查询到了项目
				HashMap<String, Costitem> im = dto.getItemm();
				HashMap<String, Costindicator> zbm = dto.getHszbm();
				HashMap<String, ClassVo> flm = this.uiis.getUnitProductClass(unitid, tbrq);
				HashMap<String, HashMap<String, CostAnalysisVo>> T_data = getCurData(dto.getPcxml(), dto.getPccsl(), im,
						zbm, flm);// 获得当班数据，用于与考核值进行比较
				HashMap<String, HashMap<String, CostAnalysisVo>> P_data = getHisData(unitid, bcdm, faid, jzsj, im, zbm,
						flm);// 获得前30天同班次的数据比较
				HashMap<String, HashMap<String, CostAnalysisVo>> Y_data = getHisData(unitid, "0", faid, jzsj, im, zbm,
						flm);// 获得前30天的数据
				String jldw = "";
				for (BriefAnalysisConf temp : acl) {// 处理项目,分析计算结果
					jldw = temp.getItemUnit();
					if (jldw == null) {
						jldw = "";
					}
					String fxid = temp.getId();
					String itemid = temp.getGoodsId();
					String bjnr = temp.getCompareContentCode();// 比较内容（编码：1、消耗量/产量；2、单耗/收率；3、单位成本）
					String bjxm = "";
					if ("1".equals(bjnr)) {
						bjnr = "消耗量/产量";
					} else if ("2".equals(bjnr)) {
						bjnr = "单耗/收率";
					} else if ("3".equals(bjnr)) {
						bjnr = "单位成本";
					}
					String bjfs = temp.getCompareTypeCode();// 比较类型(1：与上班比；2：与同班次比；3：与计划比)
					String bjxs = temp.getCompareMethodCode();// 比较方法字段【 0:差值；1：百分比】
					if (bjxs == null) {
						bjxs = "0";
					}
					String tjgs = temp.getShowFormula();
					String title = null;// 生成的描述
					HashMap<String, CostAnalysisVo> T_wzIndex = T_data.get(itemid);// 获取当班次物资的数据索引
					if (T_wzIndex != null) {// 找到了对应物资数据
						CostAnalysisVo T_cab = T_wzIndex.get(bjnr);// 获得索引对应的当班次数据bean
						if (T_cab != null) {// 通过索引查找到了数据
							double T_value = T_cab.getVal();// 当班数据
							double C_value = 0;// 比较班次数据
							String t = "[" + T_cab.getItemName() + "->" + T_cab.getDataType() + "";
							if (jldw != null && !"".equals(jldw)) {
								t = t + ",单位:" + jldw + ""; // 单位
							}
							t += "]";
							if (tjgs != null && !"".equals(tjgs.trim())) {
								Double khz = T_cab.getStipulateVal();
								if (khz == null) {
									khz = 0.0;
								}
								String jhzgs = temp.getPlanFormula();// 计算公式值
								if (jhzgs == null) {
									jhzgs = "0.0";
								}
								double jhz = analysisGs(jhzgs, 0.0,
										DateTimeUtils.getMonthDays(Integer.parseInt(tbrq.substring(0, 4)),
												Integer.parseInt(tbrq.substring(5, 7))),
										dto.getGzsc());// 获得当前班次的工作时间);
								HashMap<String, String> tj = this.calcFormula(tjgs, khz, jhz, T_value);
								if (tj != null) {
									String jg = tj.get("gsjg");
									String cz = tj.get("gs");
									if ("true".equals(jg)) {
										// 条件成立了，添加数据
										title = temp.getGoodsName() + "->" + bjxm + "  " + cz.substring(1);
									}
								}
							} else {
								if ("1".equals(bjfs)) {
									// 与对象30天的均值比
									HashMap<String, CostAnalysisVo> Y_wzIndex = Y_data.get(itemid);
									if (Y_wzIndex != null) {// 通过索引查找到了数据
										CostAnalysisVo Y_cab = Y_wzIndex.get(bjnr);// 获得索引对应的前一班次数据bean
										if (Y_cab != null) {// 通过索引查找到了数据
											C_value = Y_cab.getVal();// 获取数据
										}
									}
									double result_value = Maths.round(T_value - C_value, 6);// 计算比较量,保留6位小数
									if (result_value == 0) {// 等于0的情况为持平
										title = t + "与上班持平(本班量：" + Maths.format(T_value, "###0.000000") + ")";
									} else {
										String res_value = Maths.format(Maths.abs(result_value), "###0.000000");
										String s = "(本班量：" + Maths.format(T_value, "###0.000000") + "，上班量："
												+ Maths.format(C_value, "###0.000000") + ")";
										String ms = "";
										if ("2".equals(bjxs)) {// 求百分比
											if (C_value == 0) {
												ms = "上班量为0，无法计算百分比";
												res_value = "";
											} else {
												res_value = Maths.format((T_value - C_value) / C_value * 100, "###0.00")
														+ "%";
												if (result_value > 0) {// 大于0的情况为提高
													ms = "比上班提高了";
												} else if (result_value < 0) {// 小于0的情况为降低
													ms = "比上班降低了";
												}
											}
										} else {// 差值模式
											res_value = Maths.format(result_value, "###0.000000") + jldw;
											if (result_value > 0) {// 大于0的情况
												ms = "比上班增加了";
											} else if (result_value < 0) {// 小于0的情况
												ms = "比上班减少了";
											}
										}
										title = t + ms + res_value + s;
									}
								} else if ("2".equals(bjfs)) {
									// 与同班次比
									HashMap<String, CostAnalysisVo> P_wzIndex = P_data.get(itemid);// 获取前一班次物资的数据索引
									if (P_wzIndex != null) {// 通过索引查找到了数据
										CostAnalysisVo P_cab = P_wzIndex.get(bjnr);// 获得索引对应的前一班次数据bean
										if (P_cab != null) {// 通过索引查找到了数据
											C_value = P_cab.getVal();// 获取数据
										}
									}
									double result_value = Maths.round(T_value - C_value, 6);// 计算比较量,保留6位小数
									if (result_value == 0) {// 等于0的情况为持平
										title = t + "与同班次持平(本班量：" + Maths.format(T_value, "###0.000000") + ")";
									} else {
										String res_value = Maths.format(Maths.abs(result_value), "###0.000000");
										String s = "(本班量：" + Maths.format(T_value, "###0.000000") + "，同班次量："
												+ Maths.format(C_value, "###0.000000") + ")";
										String ms = "";
										if ("2".equals(bjxs)) {// 求百分比
											if (C_value == 0) {
												ms = "同班次量为0，无法计算百分比";
												res_value = "";
											} else {
												res_value = Maths.format((T_value - C_value) / C_value * 100, "###0.00")
														+ "%";
												if (result_value > 0) {// 大于0的情况为提高
													ms = "比同班次提高了";
												} else if (result_value < 0) {// 小于0的情况为降低
													ms = "比同班次降低了";
												}
											}
										} else {// 差值模式
											res_value = Maths.format(result_value, "###0.000000") + jldw;
											if (result_value > 0) {// 大于0的情况为提高
												ms = "比同班次增加了";
											} else if (result_value < 0) {// 小于0的情况为降低
												ms = "比同班次减少了";
											}
										}
										title = t + ms + res_value + s;
									}
								} else {// 与计划
									double jhl = 0;// 计划量
									String jhzgs = temp.getPlanFormula();
									if (jhzgs == null) {
										jhzgs = "0.0";
									}

									C_value = analysisGs(jhzgs, jhl,
											DateTimeUtils.getMonthDays(Integer.parseInt(tbrq.substring(0, 4)),
													Integer.parseInt(tbrq.substring(5, 7))),
											dto.getGzsc());// 获得当前班次的工作时间);//计算公式值
									double result_value = Maths.round(T_value - C_value, 6);// 计算比较量,保留6位小数
									if (result_value == 0) {// 等于0的情况为持平
										title = t + "与计划持平(本班量：" + Maths.format(T_value, "###0.000000") + ")";
									} else {
										String res_value = Maths.format(Math.abs(result_value), "###0.000000");
										String s = "(本班量：" + Maths.format(T_value, "###0.000000") + "，计划量："
												+ Maths.format(C_value, "###0.000000") + ")";
										String ms = "";
										if ("2".equals(bjxs)) {// 求百分比
											if (C_value == 0) {
												ms = "计划量为0，无法计算百分比";
												res_value = "";
											} else {
												res_value = Maths.format((T_value - C_value) / C_value * 100, "###0.00")
														+ "%";
												if (result_value > 0) {// 大于0的情况为提高
													ms = "比计划提高了";
												} else if (result_value < 0) {// 小于0的情况为降低
													ms = "比计划降低了";
												}
											}
										} else {// 差值模式
											res_value = Maths.format(result_value, "###0.000000") + jldw;
											if (result_value > 0) {// 大于0的情况为提高
												ms = "比计划增加了";
											} else if (result_value < 0) {// 小于0的情况为降低
												ms = "比计划减少了";
											}
										}
										title = t + ms + res_value + s;
									}
								}
							}
							if (title != null) {
								CostReportAnalysis bean = new CostReportAnalysis();
								bean.setPid(dto.getPid());
								bean.setUnitId(unitid);
								bean.setReportType(bblx);// 报表类型
								bean.setWriteDay(tbrq);
								bean.setBatchNo(bn);
								bean.setProgramId(faid);
								bean.setShiftId(bcdm);
								bean.setTeamId(bzdm);// 班次代码
								bean.setContentType(1);
								bean.setAnalysisItemId(fxid);
								bean.setAnalysisContent(title);// 更新比较描述
								insertList.add(bean);// 新建
							}
						} else {
							// 未查找到当班次数据，则无比较意义
						}
					} else {
						// 没物资就没数据，不处理
					}
				}
			}
			if (this.delOldAnalysis(pid)) {
				if (this.insertTeamDayParamData(insertList)) {
				} else {
					result = false;
				}
			} else {
				result = false;
			}
		}
		return result;
	}

	/**
	 * @category 获得装置下给定时间的班次的全部物资数据
	 * @param unitid 核算对象ID
	 * @param kssj   上班时间
	 * @param jzsj   下班时间
	 * @param bcdm   班次代码
	 * @param bn     批号
	 * @return
	 */
	private HashMap<String, HashMap<String, CostAnalysisVo>> getCurData(List<CostBatchItemData> pcxml,
			List<CostBatchParamData> pccsl, HashMap<String, Costitem> im, HashMap<String, Costindicator> zbm,
			HashMap<String, ClassVo> flm) {
		HashMap<String, HashMap<String, CostAnalysisVo>> result = new HashMap<String, HashMap<String, CostAnalysisVo>>();
		if (pcxml != null) {
			Double xhl, khxhl, xmcb, khcb, dh, khdh, dwcb;
			boolean iscp;
			String xmid, xmmc, jldw, flid;
			for (CostBatchItemData x : pcxml) {
				xmid = x.getItemId();
				if (im.containsKey(xmid)) {
					Costitem i = im.get(xmid);
					xmmc = i.getItemname();
					jldw = i.getItemunit();
					if (jldw == null) {
						jldw = "";
					}
					flid = i.getPid();
					if (flid == null) {
						flid = "";
					}
					if (flm.containsKey(flid)) {
						iscp = true;
					} else {
						iscp = false;
					}
					xhl = x.getConsumption();
					if (xhl == null) {
						xhl = 0.0;
					}
					khxhl = x.getBaseConsumption();
					if (khxhl == null) {
						khxhl = 0.0;
					}
					dh = x.getUnitConsumption();
					if (dh == null) {
						dh = 0.0;
					}
					khdh = x.getBaseUnitConsumption();
					if (khdh == null) {
						khdh = 0.0;
					}
					dwcb = x.getUnitCost();
					if (dwcb == null) {
						dwcb = 0.0;
					}
					xmcb = x.getItemCost();
					if (xmcb == null) {
						xmcb = 0.0;
					}
					khcb = x.getBaseCost();
					if (khcb == null) {
						khcb = 0.0;
					}
					HashMap<String, CostAnalysisVo> hm = new HashMap<String, CostAnalysisVo>();
					if (iscp) {
						hm.put("1", this.setVo("产量", xmmc, jldw, xhl, khxhl));
						hm.put("2", this.setVo("实际收率", xmmc, jldw, dh, khdh));
					} else {
						hm.put("1", this.setVo("耗量", xmmc, jldw, xhl, khxhl));
						hm.put("2", this.setVo("实际单耗", xmmc, jldw, dh, khdh));
					}
					hm.put("3", this.setVo("单位成本", xmmc, jldw, dwcb, 0.0));
					hm.put("4", this.setVo("总成本", xmmc, jldw, xmcb, khcb));
					result.put(xmid, hm);// 以物资代码作为key备用检索
				}
			}
		}

		if (pccsl != null) {
			Double val, khz;
			String xmid, jldw;
			for (CostBatchParamData x : pccsl) {
				xmid = x.getParamId();// 参数代码
				if (zbm.containsKey(xmid)) {
					Costindicator zb = zbm.get(xmid);
					jldw = zb.getItemunit();
					if (jldw == null) {
						jldw = "";
					}
					val = x.getCalcVal();
					if (val == null) {
						val = 0.0;
					}
					khz = x.getBaseVal();
					if (khz == null) {
						khz = 0.0;
					}
					HashMap<String, CostAnalysisVo> hm_sys = new HashMap<String, CostAnalysisVo>();
					hm_sys.put("1", this.setVo("核算指标", zb.getCpname(), jldw, val, khz));// 以参数代码作为key备用检索
					result.put(xmid, hm_sys);// 以物资代码作为key备用检索
				}
			}
		}
		return result;
	}

	/**
	 * @category 获得装置下给定时间的班次的全部物资数据
	 * @param unitid 核算对象ID
	 * @param kssj   上班时间
	 * @param jzsj   下班时间
	 * @param bcdm   班次代码
	 * @param bn     批号
	 * @return
	 */
	private HashMap<String, HashMap<String, CostAnalysisVo>> getHisData(String unitid, String bcdm, String faid,
			String jzsj, HashMap<String, Costitem> im, HashMap<String, Costindicator> zbm,
			HashMap<String, ClassVo> flm) {
		HashMap<String, HashMap<String, CostAnalysisVo>> result = new HashMap<String, HashMap<String, CostAnalysisVo>>();
		if (StringUtils.isNotEmpty(unitid) && StringUtils.isNotEmpty(jzsj) && StringUtils.isNotEmpty(jzsj)) {// 传入数据正确
			String kssj = DateTimeUtils.formatDateTime(DateTimeUtils.doDate(DateTimeUtils.parseDate(jzsj), -30))
					.substring(0, 10);
			String sql = "";
			List<Map<String, Object>> list = null;
			if (StringUtils.isEmpty(bcdm)) {
				sql = "select ITEMID,avg(CONSUMPTION) xhl,avg(UNITCONSUMPTION) dh,avg(UNITCOST) dwcb,avg(ITEMCOST) zcb from COSTTEAMITEMDATA"
						+ " where UNITID=? and PROGRAMID=? and BEGINTIME>=? and ENDTIME<=? group by ITEMID";
				list = entityService.queryListMap(sql, unitid, faid, kssj, jzsj);
			} else {
				sql = "select ITEMID,avg(CONSUMPTION) xhl,avg(UNITCONSUMPTION) dh,avg(UNITCOST) dwcb,avg(ITEMCOST) zcb from COSTTEAMITEMDATA"
						+ " where UNITID=? and PROGRAMID=? and BEGINTIME>=? and ENDTIME<=? and SHIFTID=? group by ITEMID";
				list = entityService.queryListMap(sql, unitid, faid, kssj, jzsj, bcdm);
			}
			if (list != null) {
				Double xhl, xmcb, dh, dwcb;
				boolean iscp;
				String xmid, xmmc, jldw, flid;
				Object val;
				for (Map<String, Object> x : list) {
					xmid = x.get("ITEMID").toString();
					if (im.containsKey(xmid)) {
						Costitem i = im.get(xmid);
						xmmc = i.getItemname();
						jldw = i.getItemunit();
						if (jldw == null) {
							jldw = "";
						}
						flid = i.getPid();
						if (flid == null) {
							flid = "";
						}
						if (flm.containsKey(flid)) {
							iscp = true;
						} else {
							iscp = false;
						}
						val = x.get("xhl");
						if (val == null) {
							xhl = 0.0;
						} else {
							xhl = Double.parseDouble(val.toString());
						}
						val = x.get("dh");
						if (val == null) {
							dh = 0.0;
						} else {
							dh = Double.parseDouble(val.toString());
						}
						val = x.get("dwcb");
						if (val == null) {
							dwcb = 0.0;
						} else {
							dwcb = Double.parseDouble(val.toString());
						}
						val = x.get("zcb");
						if (val == null) {
							xmcb = 0.0;
						} else {
							xmcb = Double.parseDouble(val.toString());
						}
						HashMap<String, CostAnalysisVo> hm = new HashMap<String, CostAnalysisVo>();// 根据数据生成索引HashMap
						if (iscp) {
							hm.put("1", this.setVo("产量", xmmc, jldw, xhl, 0.0));
							hm.put("2", this.setVo("实际收率", xmmc, jldw, dh, 0.0));
						} else {
							hm.put("1", this.setVo("耗量", xmmc, jldw, xhl, 0.0));
							hm.put("2", this.setVo("实际单耗", xmmc, jldw, dh, 0.0));
						}
						hm.put("3", this.setVo("单位成本", xmmc, jldw, dwcb, 0.0));
						hm.put("4", this.setVo("总成本", xmmc, jldw, xmcb, 0.0));
						result.put(xmid, hm);// 以物资代码作为key备用检索
					}
				}
			}
			List<Map<String, Object>> listx = null;
			if (StringUtils.isEmpty(bcdm)) {
				sql = "select PARAMID,avg(CALCVAL) val from COSTTEAMPARAMDATA"
						+ " where UNITID=? and PROGRAMID=? and BEGINTIME>=? and ENDTIME<=? group by PARAMID";
				listx = entityService.queryListMap(sql, unitid, faid, kssj, jzsj);
			} else {
				sql = "select PARAMID,avg(CALCVAL) val from COSTTEAMPARAMDATA"
						+ " where UNITID=? and PROGRAMID=? and BEGINTIME>=? and ENDTIME<=? and SHIFTID=? group by PARAMID";
				listx = entityService.queryListMap(sql, unitid, faid, kssj, jzsj, bcdm);
			}
			if (listx != null) {
				Double xmcb;
				String xmid, jldw;
				Object val;
				for (Map<String, Object> x : listx) {
					xmid = x.get("PARAMID").toString();// 参数代码
					if (zbm.containsKey(xmid)) {
						Costindicator zb = zbm.get(xmid);
						jldw = zb.getItemunit();
						if (jldw == null) {
							jldw = "";
						}
						val = x.get("val");
						if (val == null) {
							xmcb = 0.0;
						} else {
							xmcb = Double.parseDouble(val.toString());
						}
						HashMap<String, CostAnalysisVo> hm_sys = new HashMap<String, CostAnalysisVo>();
						hm_sys.put("1", this.setVo("核算指标", zb.getCpname(), jldw, xmcb, 0.0));// 以参数代码作为key备用检索
						result.put(xmid, hm_sys);// 以物资代码作为key备用检索
					}
				}
			}
		}
		return result;
	}

	private CostAnalysisVo setVo(String ms, String xmmc, String jldw, Double val, Double khz) {
		CostAnalysisVo b = new CostAnalysisVo();
		b.setItemName(xmmc);// 物资名称
		b.setDataType(ms);// 项目名称
		b.setItemUnit(jldw);// 计量单位
		b.setVal(val);// 项目值
		b.setStipulateVal(khz);
		return b;
	}

	/**
	 * 获得匹配的变量
	 * 
	 * @param str
	 * @param rex
	 * @return
	 */
	private List<String> getBl(String str, String rex) {
		List<String> list = new ArrayList<String>();
		if (rex == null ? true : rex.trim().length() == 0)
			rex = "([^\\/^\\+^\\-^\\*^ ^\\(^\\)^\\,^\\>,^\\=,^\\<]+\\.)+([A-Za-z]+)";
		Pattern pattern = Pattern.compile(rex);
		Matcher m = pattern.matcher(str);
		while (m.find()) {
			list.add(m.group());
		}
		return list;
	}

	/**
	 * 计算公式的值
	 * 
	 * @param gs     (String) 公式
	 * @param YJH    (double) 月计划量
	 * @param Mdays  (int) 当月天数
	 * @param BZGZSJ (double) 班组工作时间
	 * @return double 值,保留6位小数
	 */
	private double analysisGs(String gs, double YJH, int Mdays, double BZGZSJ) {
		double val;// 返回结果
		List<String> varList = this.getBl(gs, this._zbbl);
		gs = gs.replaceAll("[\\[\\]]", "");
		HashMap<String, Object> valueMap = new HashMap<String, Object>();
		for (String var : varList) { // 获取变量值并赋值
			if ("$".equals(var.substring(0, 1)) || "if".equals(var) || "round".equals(var)
					|| this.pm.judgeDouble(var)) {
				// 数据源不解析
				continue;
			}
			val = 0.0;
			if ("[每月计划量]".equals(var)) {
				val = YJH;
			} else if ("[本月天数]".equals(var)) {
				val = Mdays;
			} else if ("[当班班组工作时间]".equals(var)) {
				val = BZGZSJ;
			}
			valueMap.put(var.replaceAll("[\\[\\]]", ""), val);
		}
		return this.pm.convertDouble(this.calcFormula(gs, valueMap), 0.0);
	}

	private HashMap<String, String> calcFormula(String gs, Double khz, Double jhz, Double sjz) {
		double val;
		String jg, cz = "";
		List<String> tjlist = this.getBl(gs, _zbbl);
		gs = gs.replaceAll("[\\{\\}]", "");
		HashMap<String, Object> valueMap = new HashMap<String, Object>();
		for (String var : tjlist) {
			if ("$".equals(var.substring(0, 1)) || "if".equals(var) || "round".equals(var)
					|| this.pm.judgeDouble(var)) {
				// 数据源不解析
				continue;
			}
			val = 0.0;
			if ("{考核值}".equals(var)) {
				val = khz;
				cz = cz + ",考核值：" + Maths.format(khz, "###0.000000");
			} else if ("{比较值}".equals(var)) {
				val = jhz;
				cz = cz + ",比较值：" + Maths.format(jhz, "###0.000000");
			} else if ("{当班值}".equals(var)) {
				val = sjz;
				cz = cz + ",当班值：" + Maths.format(sjz, "###0.000000");
			}
			valueMap.put(var.replaceAll("[\\[\\]]", ""), val);
		}
		HashMap<String, String> rtn = new HashMap<String, String>();

		jg = this.calcFormula(gs, valueMap);
		rtn.put("gsjg", jg);
		cz = cz + ",差值：" + jg;
		rtn.put("gs", cz);
		return rtn;
	}

	private String calcFormula(String sgs, Map<String, Object> valueMap) {
		String calcResult = "";
		try {
			AviatorResult ar = AviatorUtils.execute(sgs, valueMap, null);
			calcResult = String.valueOf(ar.getResult());
		} catch (Exception e) {
			calcResult = "0";
		}
		return calcResult;
	}

}
