package com.yunhesoft.leanCosting.costReport.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "核算——批次信息表")
@Getter
@Setter
@Entity
@Table(name = "COSTBATCHINFO")
public class CostBatchInfo extends BaseEntity {

	private static final long serialVersionUID = 2112199167528435369L;

	@ApiModelProperty(value = "核算单元ID")
	@Column(name = "UNITID", length = 100)
	private String unitId;
	
	@ApiModelProperty(value = "填报日期")
	@Column(name = "WRITEDAY", length = 100)
	private String writeDay;

	@ApiModelProperty(value = "批号")
	@Column(name = "BATCHNO", length = 100)
	private String batchNo;

	@ApiModelProperty(value = "方案ID")
	@Column(name = "PROGRAMID", length = 100)
	private String programId;

	@ApiModelProperty(value = "开始时间")
	@Column(name = "BEGINTIME", length = 20)
	private String beginTime;// 格式yyyy-mm-dd hh:mi:ss

	@ApiModelProperty(value = "结束时间")
	@Column(name = "ENDTIME", length = 20)
	private String endTime;// 格式yyyy-mm-dd hh:mi:ss

	@ApiModelProperty(value = "备注")
	@Column(name = "REMARK", length = 4000)
	private String remark;

}
