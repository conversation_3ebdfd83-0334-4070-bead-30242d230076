package com.yunhesoft.leanCosting.costReport.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.catalina.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.costReport.entity.dto.BzyhzbDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.ComboDto;
import com.yunhesoft.leanCosting.costReport.service.BzyhzbService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * Controller
 * 
 * <AUTHOR>
 * @date 2022-05-17
 */
@Log4j2
@RestController
@Api(tags = "分时段报表信息管理")
@RequestMapping("/stepReport")
public class StepReportController {
	@Autowired
	public HttpServletRequest request;
    @Autowired
    private BzyhzbService bzyhzbService;

    /**
     * 查询分时段报表列表
     */
    @ApiOperation(value = "分时段报表 月份列表")
    @RequestMapping(value = "/reportList", method = RequestMethod.POST)
    public Res<BzyhzbDto> list(@RequestBody BzyhzbDto param) {
    	Res<BzyhzbDto> res = new Res<BzyhzbDto>();
    	if (param != null) {
    		User user = null;
        	HttpSession sess = request.getSession();
        	if(sess!=null && sess.getAttribute("user")!=null) {
        		user = (User)sess.getAttribute("user");
        		param.setZbr(user.getName());
        		//param.setFcdm(user.getSort());
        	}
            
    		res = bzyhzbService.selectList(param);// 检索数据
        }
    	
        return res;
    }
    
    /**
     * 查询分时段报表对象
     */
    @ApiOperation(value = "分时段报表对象")
    @RequestMapping(value = "/getReport", method = RequestMethod.POST)
    public Res<BzyhzbDto> getReport(@RequestBody BzyhzbDto param) {
    	Res<BzyhzbDto> res = new Res<BzyhzbDto>();
    	if (param != null) {
    		User user = null;
        	HttpSession sess = request.getSession();
        	if(sess!=null && sess.getAttribute("user")!=null) {
        		user = (User)sess.getAttribute("user");
        		//param.setFcdm(user.getSort());
        	}
            
    		res = bzyhzbService.queryCondition(param);// 检索数据
        }
    	
        return res;
    }
    
    /**
     * 保存
     * 
     * @param param 保存数据（BzyhzbDto）
     * 
     */
    @ApiOperation("保存")
    @RequestMapping(value = "/reportSaveData", method = RequestMethod.POST)
    public Res<BzyhzbDto> saveReplaceCharData(@RequestBody BzyhzbDto dto) {
    	User user = null;
    	HttpSession sess = request.getSession();
    	if(sess!=null && sess.getAttribute("user")!=null) {
    		user = (User)sess.getAttribute("user");
    		//dto.setFcdm(user.getSort());
    	}
    	
        return Res.OK(bzyhzbService.saveReplaceData(dto));
    }

    
    @ApiOperation(value = "获取班组列表")
    @RequestMapping(value = "/getClassList", method = RequestMethod.POST)
    public Res<List<ComboDto>> getBzList(@RequestBody BzyhzbDto param) {
    	Res<List<ComboDto>> res = new Res<List<ComboDto>>();
    	if (param != null) {
    		res = bzyhzbService.queryClassList(param);// 检索数据
        }
    	
        return res;
    }

}
