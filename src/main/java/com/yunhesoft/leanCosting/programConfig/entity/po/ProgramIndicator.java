package com.yunhesoft.leanCosting.programConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 方案库的指标表
 */
@Entity
@Setter
@Getter
@Table(name = "PROGRAMINDICATOR")
public class ProgramIndicator extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1659853700772533633L;

	/** 分类ID */
	@Column(name = "PID", length = 100)
	private String pId;

	/** 方案版本ID */
	@Column(name = "PVID", length = 100)
	private String pvId;

	/** 采集点名称 */
	@Column(name = "INAME", length = 200)
	private String iname;

	/** 注释 */
	@Column(name = "MEMO", length = 1000)
	private String memo;

	/** 调整上限 */
	@Column(name = "OPERATEUPLIMIT")
	private Double operateUpLimit;

	/** 调整下限 */
	@Column(name = "OPERATELOWLINIT")
	private Double operateLowLimit;

	/** 合格上限 */
	@Column(name = "KEYUPLIMIT")
	private Double keyUpLimit;

	/** 合格下限 */
	@Column(name = "KEYLOWLIMIT")
	private Double keyLowLimit;

	/** 1、使用；0、不使用 */
	@Column(name = "TMUSED")
	private Integer tmUsed;

	/** 排序 */
	@Column(name = "TMSORT")
	private Integer tmSort;

	/** 预案相同 */
	@Column(name = "SAMEPLAN")
	private Integer samePlan;

	/** 红线值 */
	@Column(name = "REDLIMIT")
	private Double redLimit;
	
	/** 目标值 */
	@Column(name = "TARGETVALUE")
	private Double targetValue;

}
