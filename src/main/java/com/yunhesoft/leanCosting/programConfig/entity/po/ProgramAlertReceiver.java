package com.yunhesoft.leanCosting.programConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 超限后的接收人表
 */
@Entity
@Setter
@Getter
@Table(name = "PROGRAMALERTRECEIVER")
public class ProgramAlertReceiver extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 方案版本ID */
	@Column(name = "PVID", length = 100)
	private String pvId;

	/** 预案ID */
	@Column(name = "PID", length = 100)
	private String pId;

	/** 人员ID或岗位ID */
	@Column(name = "PERSONID", length = 100)
	private String personId;

	/** 人员姓名或岗位名称 */
	@Column(name = "PERSONNAME", length = 100)
	private String personName;

	/** 接收人类型：默认是0。0 岗位；1 人员 */
	@Column(name = "PERSONTYPE", length = 100)
	private String personType;

	/** 排序 */
	@Column(name = "TMSORT")
	private Integer tmSort;

}
