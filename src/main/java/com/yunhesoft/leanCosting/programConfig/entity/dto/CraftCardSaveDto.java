package com.yunhesoft.leanCosting.programConfig.entity.dto;


import java.util.List;

import com.yunhesoft.leanCosting.programConfig.entity.vo.OperationCardFormVo;

import lombok.Getter;
import lombok.Setter;


/**
 *	工艺卡保存类
 */
@Setter
@Getter
public class CraftCardSaveDto {
	
	
	private String pvId; //方案版本ID
	
    private String editType; //del-删除；save-保存；
    
    private List<OperationCardFormVo> craftCardList; //工艺卡保存数据
    
    
}
