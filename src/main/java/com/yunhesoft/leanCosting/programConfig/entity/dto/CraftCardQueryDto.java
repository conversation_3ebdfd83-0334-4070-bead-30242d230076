package com.yunhesoft.leanCosting.programConfig.entity.dto;


import lombok.Getter;
import lombok.Setter;

import java.util.List;

import com.yunhesoft.core.common.dto.BaseQueryDto;


/**
 *	工艺卡查询类
 */
@Setter
@Getter
public class CraftCardQueryDto extends BaseQueryDto{
	

	private String pvId; //方案版本ID
	
	private List<String> pvIdList; //方案版本列表
	
	private String craftCardName; //工艺卡名称
	
	private String selType; //查询类型：默认表单；pic、图片；
	
}
