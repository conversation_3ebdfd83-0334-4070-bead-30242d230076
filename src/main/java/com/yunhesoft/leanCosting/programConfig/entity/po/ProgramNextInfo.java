package com.yunhesoft.leanCosting.programConfig.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 *	后续方案信息表
 */
@Entity
@Setter
@Getter
@Table(name = "PROGRAMNEXTINFO")
public class ProgramNextInfo extends BaseEntity {
	
    private static final long serialVersionUID = 1L;
    
    /** 父id（主方案的数据id） */
    @Column(name="PID", length=100)
    private String pId;
    
    /** 后续工序id */
    @Column(name="NEXTUNITID", length=100)
    private String nextUnitId;
    
    /** 后续工序名称 */
    @Column(name="NEXTUNITNAME", length=200)
    private String nextUnitName;
    
    /** 后续方案id */
    @Column(name="NEXTPROGRAMID", length=100)
    private String nextProgramId;
    
    /** 后续方案名称 */
    @Column(name="NEXTPROGRAMNAME", length=200)
    private String nextProgramName;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmSort;
    
}
