package com.yunhesoft.leanCosting.programConfig.service.impl;


import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.programConfig.entity.dto.CraftCardQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.dto.CraftCardSaveDto;
import com.yunhesoft.leanCosting.programConfig.entity.po.OperationCardForm;
import com.yunhesoft.leanCosting.programConfig.entity.vo.OperationCardFormVo;
import com.yunhesoft.leanCosting.programConfig.service.IProgramCraftCardService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.tmsf.form.entity.dto.SFFormQueryDto;
import com.yunhesoft.tmsf.form.entity.po.SFForm;
import com.yunhesoft.tmsf.form.service.IFormManageService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 *	工艺卡相关服务接口实现类
 * <AUTHOR>
 * @date 2023-08-10
 */
@Service
public class ProgramCraftCardServiceImpl implements IProgramCraftCardService {

	@Autowired
	private EntityService entityService;
	
	@Autowired
	private IFormManageService formService;
	
	
	/**
	 *	获取工艺卡可绑定表单列表
	 * @return
	 */
	@Override
	public List<SFForm> getCraftCardCanBindFormList(CraftCardQueryDto queryDto) {
		List<SFForm> result = new ArrayList<SFForm>();
		List<SFForm> formList = formService.queryFormInfoList(null, null);
		if(StringUtils.isNotEmpty(formList)) {
			Map<String, List<OperationCardForm>> bindMap = new HashMap<String, List<OperationCardForm>>();
			List<OperationCardForm> bindList = this.getOperationCardFormList(queryDto);
			if(StringUtils.isNotEmpty(bindList)) {
				bindMap = bindList.stream().collect(Collectors.groupingBy(OperationCardForm::getFormId,Collectors.toList()));
			}
			for (int i = 0; i < formList.size(); i++) {
				SFForm formObj = formList.get(i);
				String id = formObj.getId();
				if(StringUtils.isEmpty(bindMap)||!bindMap.containsKey(id)) {  //显示未绑定的记录
					result.add(formObj);
				}
			}
    	}
		return result;
	}
	
	
	/**
	 *	获取方案下的工艺卡
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<OperationCardForm> getOperationCardFormList(CraftCardQueryDto queryDto) {
		List<OperationCardForm> result = new ArrayList<OperationCardForm>();
		try {
			String pvId = ""; //方案版本ID
			List<String> pvIdList = null;
			if(StringUtils.isNotNull(queryDto)) {
				pvId = queryDto.getPvId();
				pvIdList = queryDto.getPvIdList();
			}
			//检索条件
			Where where = Where.create();
			where.eq(OperationCardForm::getTmUsed, 1);
			if(StringUtils.isNotEmpty(pvId)) {
				where.eq(OperationCardForm::getPvId, pvId);
			}
			if(StringUtils.isNotEmpty(pvIdList)) {
				where.in(OperationCardForm::getPvId, pvIdList.toArray());
			}
			//排序
			Order order = Order.create();
			order.orderByAsc(OperationCardForm::getTmSort);
			List<OperationCardForm> list = entityService.queryData(OperationCardForm.class, where, order, null);
			if(StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	
	/**
	 *	获取工艺卡对象
	 * @param id
	 * @return
	 */
	@Override
	public OperationCardForm getOperationCardFormObjById(String id) {
		OperationCardForm result = null;
		if(StringUtils.isNotEmpty(id)) {
			OperationCardForm queryObj = entityService.queryObjectById(OperationCardForm.class, id);
			if(StringUtils.isNotNull(queryObj)) {
				result = queryObj;
			}
		}
		return result;
	}
	
	
	/**
	 *	获取工艺卡数据（vo）
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<OperationCardFormVo> getCraftCardList(CraftCardQueryDto queryDto) {
		List<OperationCardFormVo> result = new ArrayList<OperationCardFormVo>();
		List<OperationCardForm> queryList = this.getOperationCardFormList(queryDto);
		if(StringUtils.isNotEmpty(queryList)) {
			String selType = ""; //查询类型：默认表单；pic、图片；
			if(StringUtils.isNotNull(queryDto)) {
				selType = queryDto.getSelType();
			}
			if(StringUtils.isNotEmpty(selType)&&"pic".equals(selType)) { //工艺卡图片
				for (int i = 0; i < queryList.size(); i++) {
					OperationCardForm obj = queryList.get(i);
					OperationCardFormVo vo = new OperationCardFormVo();
    				BeanUtils.copyProperties(obj, vo); //赋予返回对象
    				vo.setFormName(vo.getFormId());
    				result.add(vo);
				}
			}else { //默认工艺卡表单
				Map<String, SFForm> formMap = new HashMap<String, SFForm>();
				SFFormQueryDto formDto = new SFFormQueryDto();
				if(queryDto!=null) {
					formDto.setName(queryDto.getCraftCardName());
				}
				List<SFForm> formList = formService.queryFormInfoList(formDto, null);
				if(StringUtils.isNotEmpty(formList)) {
					formMap = formList.stream().collect(Collectors.toMap(SFForm::getId,Function.identity()));
	        	}
				for (int i = 0; i < queryList.size(); i++) {
					OperationCardForm obj = queryList.get(i);
					String formId = obj.getFormId();
					if(StringUtils.isNotEmpty(formId)&&StringUtils.isNotEmpty(formMap)&&formMap.containsKey(formId)) {
						SFForm formObj = formMap.get(formId);
						String formName = formObj.getName();
						OperationCardFormVo vo = new OperationCardFormVo();
	    				BeanUtils.copyProperties(obj, vo); //赋予返回对象
	    				vo.setFormName(formName);
	    				vo.setFormObj(formObj);
	    				result.add(vo);
					}
				}
			}
		}
		return result;
	}
	
	
	/**
	 *	保存工艺卡数据
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveCraftCardData(CraftCardSaveDto saveDto) {
		String result = "";
		List<OperationCardForm> addList = new ArrayList<OperationCardForm>();
       	List<OperationCardForm> updList = new ArrayList<OperationCardForm>();
		if (saveDto != null) {
			String editType = saveDto.getEditType();
			String pvId = saveDto.getPvId();
			List<OperationCardFormVo> saveList = saveDto.getCraftCardList();
            if (StringUtils.isNotEmpty(editType)&&StringUtils.isNotEmpty(pvId)&&StringUtils.isNotNull(saveList)) {
            	int maxNum = 0; //最大序号
            	Map<String, List<OperationCardForm>> bindMap = new HashMap<String, List<OperationCardForm>>();
            	Map<String, OperationCardForm> dataMap = new HashMap<String, OperationCardForm>();
            	CraftCardQueryDto queryDto = new CraftCardQueryDto();
            	queryDto.setPvId(pvId);
            	List<OperationCardForm> dataList = this.getOperationCardFormList(queryDto);
            	if(StringUtils.isNotEmpty(dataList)) {
    				bindMap = dataList.stream().collect(Collectors.groupingBy(OperationCardForm::getFormId,Collectors.toList()));
            		dataMap = dataList.stream().collect(Collectors.toMap(OperationCardForm::getId,Function.identity()));
            		Integer maxSort = dataList.get(dataList.size()-1).getTmSort();
            		if(maxSort!=null) {
            			maxNum = maxSort;
            		}
            	}
            	for (int i = 0; i < saveList.size(); i++) {
            		OperationCardForm saveObj = saveList.get(i);
            		String id_save = saveObj.getId();
            		if("save".equals(editType)) { //保存
            			if(StringUtils.isNotEmpty(dataMap)&&StringUtils.isNotEmpty(id_save)&&dataMap.containsKey(id_save)) { //修改
            				//修改时，不需要保存绑定关系
                		}else { //新增
                			String formId_save = saveObj.getFormId();
                			if(StringUtils.isEmpty(bindMap)||!bindMap.containsKey(formId_save)) {  //未绑定的记录可以新增
                				maxNum += 1;
                    			OperationCardForm dataObj = new OperationCardForm();
                				BeanUtils.copyProperties(saveObj, dataObj); //赋予返回对象
                				String id = dataObj.getId();
                				if(StringUtils.isEmpty(id)) {
                					id = TMUID.getUID();
                				}
                				dataObj.setId(id);
                				dataObj.setPvId(pvId);
                				dataObj.setTmUsed(1);
                				dataObj.setTmSort(maxNum);
                				addList.add(dataObj);
            				}
                		}
            		}else if("del".equals(editType)) { //删除
            			if(StringUtils.isNotEmpty(id_save)) {
            				OperationCardForm dataObj = this.getOperationCardFormObjById(id_save);
                        	if(StringUtils.isNotNull(dataObj)) {
                    			dataObj.setTmUsed(0);
                    			updList.add(dataObj);
                        	}
            			}
            		}
				}
            }
        }
		result = this.saveCraftCard(addList, updList, null);
		return result;
	}
	
	
	/**
	 *	保存工艺卡数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	private String saveCraftCard(List<OperationCardForm> addList,List<OperationCardForm> updList,List<OperationCardForm> delList) {
		String result = "";
		if ("".equals(result)&&StringUtils.isNotEmpty(addList)) {
        	if(entityService.insertBatch(addList)==0) {
        		result = "添加失败！";
        	}
        }
		if ("".equals(result)&&StringUtils.isNotEmpty(updList)) {
        	if(entityService.updateByIdBatch(updList)==0) {
        		result = "更新失败！";
        	}
        }
		if ("".equals(result)&&StringUtils.isNotEmpty(delList)) {
        	if(entityService.deleteByIdBatch(delList)==0) {
        		result = "删除失败！";
        	}
        }
		return result;
	}
	

	/**
	 *	版本改变后，更新工艺卡相关数据
	 * @param operType
	 * @param pvId
	 * @param newPvId
	 * @return
	 */
	@Override
	public String renewByVersionCraftCard(String operType,String pvId,String newPvId) {
		String result = "";
		if(StringUtils.isNotEmpty(operType)) {
			List<OperationCardForm> addList = new ArrayList<OperationCardForm>();
			List<OperationCardForm> updList = new ArrayList<OperationCardForm>();
			if("add".equals(operType)) { //新增
				if(StringUtils.isNotEmpty(pvId)&&StringUtils.isNotEmpty(newPvId)) {
					CraftCardQueryDto dto = new CraftCardQueryDto();
					dto.setPvId(pvId);
	            	List<OperationCardForm> dataList = this.getOperationCardFormList(dto);
					if(StringUtils.isNotEmpty(dataList)) {
						for (int i = 0; i < dataList.size(); i++) {
							OperationCardForm dataObj = dataList.get(i);
							dataObj.setId(TMUID.getUID());
							dataObj.setPvId(newPvId);
		    				addList.add(dataObj);
						}
					}
				}
			}else if("del".equals(operType)) { //删除
				if(StringUtils.isNotEmpty(pvId)) {
            		String[] pvIdArr = pvId.split(",");
            		List<String> pvIdList = Arrays.asList(pvIdArr);
					CraftCardQueryDto dto = new CraftCardQueryDto();
					dto.setPvIdList(pvIdList);
	            	List<OperationCardForm> dataList = this.getOperationCardFormList(dto);
					if(StringUtils.isNotEmpty(dataList)) {
						for (int i = 0; i < dataList.size(); i++) {
							OperationCardForm dataObj = dataList.get(i);
							dataObj.setTmUsed(0);
							updList.add(dataObj);
						}
					}
				}
			}
			result = this.saveCraftCard(addList, updList, null);
		}
		return result;		
	}
	
	
}
