package com.yunhesoft.leanCosting.programConfig.controller;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.programConfig.entity.dto.IdentifyQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.dto.IdentifySaveDto;
import com.yunhesoft.leanCosting.programConfig.entity.po.StoveProjTatOrg;
import com.yunhesoft.leanCosting.programConfig.service.IProgramIdentifyService;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@RestController
@RequestMapping("/leanCosting/programConfig/identify")
@Api(tags = "识别方案")
public class ProgramIdentifyController extends BaseRestController {

	
	@Autowired
	private IProgramIdentifyService identifyService;

	
	/**
	 *	获取识别方案数据
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/getIdentifyProgramList", method = RequestMethod.POST)
	@ApiOperation("获取识别方案数据")
	public Res<?> getIdentifyProgramList(@RequestBody IdentifyQueryDto queryDto) {
		Res<List<StoveProjTatOrg>> res = new Res<List<StoveProjTatOrg>>();
		List<StoveProjTatOrg> list = identifyService.getIdentifyProgramList(queryDto);
		res.setResult(list);
		return res;
	}
	
	
	/**
	 *	保存识别方案数据
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/saveIdentifyProgramData", method = RequestMethod.POST)
	@ApiOperation("保存识别方案数据")
	public Res<?> saveIdentifyProgramData(@RequestBody IdentifySaveDto saveDto) {
		return Res.OK(identifyService.saveIdentifyProgramData(saveDto));
	}
	
	
}
