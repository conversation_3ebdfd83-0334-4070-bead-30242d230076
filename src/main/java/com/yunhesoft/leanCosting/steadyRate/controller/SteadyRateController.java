package com.yunhesoft.leanCosting.steadyRate.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.ProductUnitProgShift;
import com.yunhesoft.leanCosting.rtdbShow.service.IRtdbShowService;
import com.yunhesoft.leanCosting.steadyRate.scheduler.SteadyRateScheduleJobHandler;
import com.yunhesoft.leanCosting.steadyRate.service.ISteadyRateServise;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;


@Api(tags = "平稳率接口")
@RestController
@RequestMapping("/leanCosting/steadyRate")
public class SteadyRateController extends BaseRestController {

	@Autowired
	private IRtdbShowService srv;

	@Autowired
	private UnitItemInfoService uiSrv;

	@Autowired
	private SteadyRateScheduleJobHandler steadyRateHandler;

	@Autowired
	private ISteadyRateServise steadyRateSrv;



//	@ApiOperation(value = "核算预警调度")
//	@RequestMapping(value = "costWarningScheduleJob", method = { RequestMethod.GET })
//	public Res<?> costWarningScheduleJob() throws Exception {
//		handler.tm4CommCostRtdbWarningScheduleJobHandler();
//		return Res.OK();
//	}


	@ApiOperation(value = "平稳率采集调度")
	@RequestMapping(value = "steadyRateScheduleJob", method = { RequestMethod.POST })
	public Res<?> steadyRateScheduleJob(@RequestBody(required = false) JSONObject param) throws Exception {
		String tenantId = Optional.ofNullable(param).map(item -> item.getString("tenantId")).orElse("");
		if (StringUtils.isNotEmpty(tenantId)) {
			steadyRateHandler.tm4SteadyRateScheduleJobHandler(tenantId);
		} else {
			steadyRateHandler.tm4SteadyRateScheduleJobHandler();
		}
		return Res.OK();
	}

	@ApiOperation(value = "平稳率采集补录")
	@RequestMapping(value = "reCalcShiftSteadyRate", method = { RequestMethod.POST })
	public Res<?> reCalcShiftSteadyRate(@RequestBody(required = false) List<ProductUnitProgShift> shifList) throws Exception {
		steadyRateSrv.reCalcShiftSteadyRate(null, shifList);
		return Res.OK();
	}

	@ApiOperation(value = "获取预警消息待办列表")
	@RequestMapping(value = "queryWarningMsgTodoDataList", method = { RequestMethod.POST })
	public Res<?> queryWarningMsgTodoDataList(@RequestBody(required = false) JSONObject param) throws Exception {
		int totoType = Optional.ofNullable(param).map(item -> item.getInteger("todoType")).orElse(0);
		return Res.OK(steadyRateSrv.queryWarningMsgTodoDataList(totoType));
	}


	@ApiOperation(value = "转发预警消息")
	@RequestMapping(value = "forwardWarningMsg", method = { RequestMethod.POST })
	public Res<?> forwardWarningMsg(@RequestBody JSONObject param) throws Exception {
		String infoId = param.getString("infoId");
		String msgId = param.getString("msgId");
		JSONArray userIdArray = param.getJSONArray("forwardReceiveUserArray");
		JSONArray userNameArray = param.getJSONArray("forwardReceiveUserNameArray");
		List<String> userIdList = userIdArray.toJavaList(String.class);
		List<String> userNameList = userNameArray.toJavaList(String.class);
		return Res.OK(steadyRateSrv.forwardWarningMsg(infoId, msgId, userIdList, userNameList));
	}

}
