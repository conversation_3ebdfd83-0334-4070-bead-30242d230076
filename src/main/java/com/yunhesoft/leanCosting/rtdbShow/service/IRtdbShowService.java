package com.yunhesoft.leanCosting.rtdbShow.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.leanCosting.rtdbShow.entity.dto.RtdbQueryDto;
import com.yunhesoft.rtdb.core.model.Tag;

import java.util.List;

public interface IRtdbShowService {

    JSONObject queryRtdbData(List<String> tagCodes, String stime, String etime, int interval);
    
    JSONArray queryRtdbTagCalcOverData(RtdbQueryDto dto);

    List<Tag> queryRtdbTagData(List<String> tagCodes, String stime, String etime, int interval);
}
