package com.yunhesoft.leanCosting.standby.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description: 产品管理实体
 * @date 2021/09/10
 */

@ApiModel(value = "产品管理")
@Entity
@Data
@ToString
@Table(name = "STANDBY_PRODUCT_CONTROL")
public class StandbyProductControl extends BaseEntity {

    @ApiModelProperty(value = "产品编号")
    @Column(name = "PRODUCTNO", length = 50)
    private String productNo;

    @ApiModelProperty(value = "产品名称")
    @Column(name = "PRODUCT", length = 200)
    private String product;

    @ApiModelProperty(value = "订单数量")
    @Column(name = "ORDERNUMBER")
    private Integer orderNumber;

    @ApiModelProperty(value = "产品单位")
    @Column(name = "UNIT", length = 50)
    private String unit;

    @ApiModelProperty(value = "物资描述")
    @Column(name = "MATERIALSDESCRIBE", length = 1000)
    private String materialsDescribe;

    @ApiModelProperty(value = "产品描述")
    @Column(name = "PRODUCTDESCRIBE", length = 1000)
    private String productDescribe;

    @ApiModelProperty(value = "材料牌号")
    @Column(name = "MATERIALSNO", length = 100)
    private String materialsNo;

    @ApiModelProperty(value = "工艺")
    @Column(name = "TECHNOLOGY", length = 500)
    private String technology;

    @ApiModelProperty(value = "实际尺寸")
    @Column(name = "MEASUREREAL", length = 50)
    private String measureReal;

    @ApiModelProperty(value = "大端DN")
    @Column(name = "BIGDN", length = 50)
    private String bigDn;

    @ApiModelProperty(value = "小端DN")
    @Column(name = "SMALLDN", length = 50)
    private String smallDn;

    @ApiModelProperty(value = "实际壁厚")
    @Column(name = "WALLTHICKNESSREAL", length = 50)
    private String wallThicknessReal;

    @ApiModelProperty(value = "大段SCH")
    @Column(name = "BIGSCH", length = 50)
    private String bigSch;

    @ApiModelProperty(value = "小端SCH")
    @Column(name = "SMALLSCH", length = 50)
    private String smallSch;

    @ApiModelProperty(value = "重量")
    @Column(name = "WEIGHT")
    private Double weight;

    @ApiModelProperty(value = "制造标准")
    @Column(name = "MANUFACTURINGSTANDARDS", length = 50)
    private String manufacturingStandards;

    @ApiModelProperty(value = "非标情况下公式")
    @Column(name = "FORMULA", length = 3000)
    private String formula;

    @ApiModelProperty(value = "产品别名")
    @Column(name = "PRODUCTBM", length = 50)
    private String productBm;

    @ApiModelProperty(value = "状态")
    @Column(name = "STATUS")
    private Integer status;

    @ApiModelProperty(value = "尺寸")
    @Column(name = "MEASURE", length = 50)
    private String measure;

    @ApiModelProperty(value = "壁厚")
    @Column(name = "WALLTHICKNESS", length = 50)
    private String wallThickness;

    @ApiModelProperty(value = "nps")
    @Column(name = "NPS", length = 100)
    private String nps;

    //利用反射  此字段的后续将不会出现在前台的下拉框中
    @ApiModelProperty(value = "主表id")
    @Column(name = "PID", length = 50)
    private String pid;

    @ApiModelProperty(value = "顾客单位")
    @Column(name = "CLIENT", length = 200)
    private String client;


    @ApiModelProperty(value = "交货日期")
    @Column(name = "DELIVERYDATE")
    private Date deliveryDate;

    @ApiModelProperty(value = "生产数量")
    @Column(name = "MANUFACTURENUMBER")
    private Integer manufactureNumber;



    @ApiModelProperty(value = "材料规格")
    @Column(name = "MATERIALSPECIFICATION", length = 50)
    private String materialSpecification;

    @ApiModelProperty(value = "材料编号")
    @Column(name = "MATERIALSSERIAL", length = 50)
    private String materialSserial;
    @ApiModelProperty(value = "审定人id")
    @Column(name = "AUDITID", length = 50)
    private String auditId;

    @ApiModelProperty(value = "审定人姓名")
    @Column(name = "AUDITNAME", length = 100)
    private String auditName;

    @ApiModelProperty(value = "审定人时间")
    @Column(name = "AUDITDATE")
    private Date auditDate;

    @ApiModelProperty(value = "编辑人id")
    @Column(name = "EDITORIALID", length = 50)
    private String editorIalId;

    @ApiModelProperty(value = "编辑人姓名")
    @Column(name = "EDITORIALNAME", length = 100)
    private String editorIalName;

    @ApiModelProperty(value = "编辑时间")
    @Column(name = "EDITORIALDATE")
    private Date editorIalDate;


    @ApiModelProperty(value = "是否有工艺卡")
    @Column(name = "ISTECHNOLOGY")
    private Integer isTechnology;

    @ApiModelProperty(value = "产品编码ID")
    @Column(name = "PRODUCTNUMBERID", length = 50)
    private String productNumberId;

    @ApiModelProperty(value = "是否使用")
    @Column(name = "USED")
    private Integer used;

    @ApiModelProperty(value = "排序")
    @Column(name = "SORT")
    private Integer sort;


    @ApiModelProperty(value = "是否已经制作工艺卡")
    @Column(name = "ISMAKECARD", length = 3000)
    private Integer isMakeCard;

    @Column(name = "CREATEPERSONNAME",length = 50)
    private String createPersonName;
    @Column(name = "UPDATEPERSONNAME",length = 50)
    private String updatePersonName;

    @ApiModelProperty(value = "项目库-项目id")
    @Column(name = "COSTLIBRARYITEMID",length = 50)
    private  String costlibraryitemId;
}
