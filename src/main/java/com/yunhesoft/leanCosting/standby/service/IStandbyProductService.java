package com.yunhesoft.leanCosting.standby.service;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.order.entity.dto.QueryProductDto;
import com.yunhesoft.leanCosting.order.entity.dto.SaveDto;
import com.yunhesoft.leanCosting.order.entity.dto.SearchOrderDto;
import com.yunhesoft.leanCosting.order.entity.dto.SearchProductDto;
import com.yunhesoft.leanCosting.order.entity.po.ProductControl;
import com.yunhesoft.leanCosting.standby.entity.dto.QueryStandbyProductDto;
import com.yunhesoft.leanCosting.standby.entity.dto.SaveStandbyDto;
import com.yunhesoft.leanCosting.standby.entity.dto.SearchStandbyDto;
import com.yunhesoft.leanCosting.standby.entity.po.StandbyProductControl;
import com.yunhesoft.system.kernel.service.model.Pagination;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

/**
 * @Description: 产品管理接口服务类
 * <AUTHOR>
 * @date 2021/09/13
 */
public interface IStandbyProductService {

	/**
	 * 保存产品
	 *
	 * @param
	 * @return
	 */
	Boolean saveStandbyProduct(SaveStandbyDto param);

	/**
	 * 查询订单下的产品.
	 *
	 * @param
	 * @return
	 */
	List<StandbyProductControl> selectStandbyProduct(String orderId, Pagination<?> page);

	/**
	 * 根据id查询产品
	 *
	 * <AUTHOR>
	 * @param
	 * @return
	 */
	StandbyProductControl selectStandbyProductById(String productId);

	List<StandbyProductControl> selectProduct(String orderId, Pagination<?> page, SearchProductDto params);

	List<StandbyProductControl> selectStandbyProduct(String orderId);

	/**
	 * 分页查询订单下的产品.
	 *
	 * @param
	 * @return
	 */

	Res<?> selectStandbyProductPage(QueryStandbyProductDto queryParam);

	/**
	 * 查询订单下有无产品.
	 *
	 * @param
	 * @return
	 */
	int haveStandbyProductByPid(String param);

	/**
	 * 查询订单id删除产品.
	 *
	 * @param
	 * @return
	 */
	int delStandbyProductByPid(String param);

	/**
	 * 获得全部茶品.
	 *
	 * @param
	 * @return
	 */
	List<StandbyProductControl> getAllPro();

	/**
	 * 更新产品.
	 *
	 * @param
	 * @return
	 */
	Boolean updateStandbyProduct(List<StandbyProductControl> upateList);

	/**
	 * 添加产品
	 *
	 * <AUTHOR>
	 * @param
	 * @return
	 */
	Boolean addStandbyProduct(List<StandbyProductControl> insertList);

	/**
	 * 更新状态
	 *
	 * @param
	 * @return
	 */
	Boolean updateStatus(String id, int status);

	/**
	 * 产品管理审核
	 *
	 * @param
	 * @return
	 */
	Boolean passStandbyProduct(SaveStandbyDto param);




    /**
	 * 获取产品信息
	 *
	 * @param
	 * @return
	 */
	Map<String, String> getStandbyProductInfo();

	/**
	 * 查询产品
	 * <AUTHOR>
	 * @return
	 * @params
	*/

    List<StandbyProductControl> queryStandbyProduct(SearchStandbyDto queryDto, Pagination<?> page);

	/**
	 * 导出excel
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	void exportExcel(String pid,SearchProductDto ssdto,HttpServletResponse response);

	/**
	 * 导入excel
	 *
	 * @return
	 * <AUTHOR>
	 * @throws Exception 
	 * @throws IOException
	 * @params
	 */
	Boolean importExcel(MultipartFile file,String productImprotPid) throws IOException, Exception;

	Integer getMaxSort();
}
