package com.yunhesoft.leanCosting.workDispatch.entity.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
@ApiModel(value = "核算批次当班查询条件")
@Getter
@Setter
public class CostBatchDto {
//	@ApiModelProperty(value = "数据ID")
//	private String id;
	@ApiModelProperty(value = "核算对象ID")
	private String unitId;
	@ApiModelProperty(value = "填写日期")
	private String writeDay;
}
