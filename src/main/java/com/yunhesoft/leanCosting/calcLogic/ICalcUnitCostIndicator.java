package com.yunhesoft.leanCosting.calcLogic;

import java.util.List;

import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaValueVo;

public interface ICalcUnitCostIndicator {

	/**
	 * @category 计算使用核算指标
	 * @param UID
	 * @param unitid
	 * @param kssj
	 * @param jzsj
	 * @param zbid
	 * @return
	 */
	public List<MtmFormulaValueVo> calc(String UID, String unitid, String kssj, String jzsj, String zbid,
			String formula, String ftype);

	/**
	 * @category 根据UID去除map的值
	 * @param UID
	 */
	public void removeMapVal(String UID);

}
