package com.yunhesoft.leanCosting.calcLogic;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.utils.StringUtils;
import com.yunhesoft.core.common.aviator.AviatorResult;
import com.yunhesoft.core.common.aviator.AviatorUtils;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.Maths;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamProgramDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamReportInputDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchOnDuty;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamBatchItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInfo;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.ShiftCostCalcTask;
import com.yunhesoft.leanCosting.costReport.entity.vo.ItemConsumption;
import com.yunhesoft.leanCosting.costReport.service.GetCostItemInfoService;
import com.yunhesoft.leanCosting.costReport.service.GetItemConsumptionService;
import com.yunhesoft.leanCosting.costReport.service.ICreateCostAnalysis;
import com.yunhesoft.leanCosting.productSchedu.service.IProductScheduPlanService;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramLibraryCostItem;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostItemInfoVo;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

import lombok.extern.log4j.Log4j2;

/**
 * 
 * <AUTHOR>
 * @category 工况核算：计算班组的交接班数据
 * 
 */
@Service
@Log4j2
public class CalcTeamProjectLogic implements ICalcTeamProjectLogic {

	private PublicMethods pm = new PublicMethods();
	private String _zbbl = "(\\w+\\.\\w+\\.?(\\w+)?)|(\\w+\\.([\\u4E00-\\u9FA5A-Za-z0-9_])+\\.\\w+)|([\\u4E00-\\u9FA5A-Za-z0-9_]+)";

	@Autowired
	GetItemConsumptionService gics;
	@Autowired
	UnitItemInfoService unitItemInfoService;
	@Autowired
	IUnitMethodService unitMethodService;
	@Autowired
	EntityService entityService;
	@Autowired
	IGetPeriodForReportService gettjsd;
	@Autowired
	GetCostItemInfoService gcii;
	@Autowired
	IProductScheduPlanService psps;
	@Autowired
	ICreateCostAnalysis icca;
	@Autowired
	IOPShiftData iopsd;
	@Autowired
	IAutoShiftCalcLogic iascc;

	/**
	 * @category 计算批次数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcPCData(TeamReportInputDto reportinfo) {
		// 先删除旧的批号数据
		if (this.deleteBatchInfo(reportinfo)) {
			if (initBatchInfo(reportinfo)) {
				return this.calcBatchData(reportinfo);
			} else {
				log.error("", "初始化批次信息出现错误，批次信息：" + JSON.toJSONString(reportinfo));
				return "批次初始化失败";
			}
		} else {
			log.error("", "删除旧的批次信息出现错误，批次信息：" + JSON.toJSONString(reportinfo));
			return "";
		}
	}

	/**
	 * @category 计算单表消耗，此处不保存数据，只进行计算
	 * @return
	 */
	@Override
	public List<CostBatchInstrumentData> calcYbbs(TeamReportInputDto reportinfo) {
		String tbrq = reportinfo.getWriteDay();
		String unitid = reportinfo.getUnitId();
		String kssj = reportinfo.getBegintime();
		String jzsj = reportinfo.getEndtime();
		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), tbrq.substring(0, 7), kssj, jzsj);
		Double gzsj = DateTimeUtils.diffSecond(DateTimeUtils.parseDateTime(kssj), DateTimeUtils.parseDateTime(jzsj))
				/ 3600.00;
		reportinfo.setGzsc(gzsj);
		this.init(reportinfo, tbrq, false);
		HashMap<String, Costinstrument> ym = reportinfo.getYbm();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, CostItemFormula> gm = reportinfo.getGsm();
		List<CostBatchInstrumentData> ybbs = reportinfo.getYbbs();
		Double val, jsz;
		String xmid, ybid, key, keyx, wzmc, ybmc;
		if (ybbs != null) {
			HashMap<String, String> vm = reportinfo.getValm();
			if (vm == null) {
				vm = new HashMap<String, String>();
				reportinfo.setValm(vm);
			}
			for (CostBatchInstrumentData x : ybbs) {
				ybid = x.getInstrumentId();// 仪表ID
				if (ym.containsKey(ybid)) {
					Costinstrument yb = ym.get(ybid);
					if (yb != null) {
						xmid = yb.getPid();// 项目ID
						key = new StringBuffer(unitid).append(".").append(ybid).toString();
						// 前表数
						keyx = new StringBuffer(key).append(".qbs").toString();
						val = x.getPreviousReadOut();
						if (val == null) {
							val = 0.0;
						}
						vm.put(keyx, String.valueOf(val));
						// 后表数
						keyx = new StringBuffer(key).append(".hbs").toString();
						val = x.getLastReadOut();
						if (val == null) {
							val = 0.0;
						}
						vm.put(keyx, String.valueOf(val));
					}
				}
			}
			for (CostBatchInstrumentData x : ybbs) {
				ybid = x.getInstrumentId();// 仪表ID
				ybmc = "";
				wzmc = "";
				xmid = "";
				if (ym.containsKey(ybid)) {
					Costinstrument yb = ym.get(ybid);
					if (yb != null) {
						xmid = yb.getPid();// 项目ID
						ybmc = yb.getName();
						wzmc = this.getItemName(xmid, im);
					}
				}
				key = new StringBuffer(ybid).append(".dbxh").toString();
				if (gm.containsKey(key)) {
					// 找到公式，需要计算单表消耗
					jsz = this.calcformula(key, reportinfo, ctsp, xmid, wzmc, ybid, ybmc, "");
					x.setCalcVal(jsz);
					x.setWriteVal(jsz);
				}
			}
		}
		return ybbs;
	}

	/**
	 * @category 批次计算完成后，计算班组的方案报表，按照班次和方案，如果批次跨班，没有表数
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcTeamDayProgramReport(TeamReportInputDto reportinfo) {
		String rtn = "";
		Double zsc = 0.0, bcsc, val;
		String kssj, jzsj, pcid, sb, sbsj, xbsj;
		String tbrq, tjrq, bzdm, bcdm, itemid;
		String bn = reportinfo.getBatchNo();
		String unitid = reportinfo.getUnitId();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		HashMap<String, TeamProgramDto> zbi = new HashMap<String, TeamProgramDto>();
		// 先清除原有批次相关的班组方案数据
		if (this.deleteTeamBatchItemData(unitid, bn, zbi)) {
			// 批次的当班信息
			List<CostBatchItemData> pcxml = reportinfo.getPcxml();
			List<CostBatchOnDuty> pcl = this.getBatchDutyInfo(unitid, bn);
			if (pcl != null) {
				List<CostTeamBatchItemData> bzpcxm = new ArrayList<CostTeamBatchItemData>();
				if (pcl.size() == 1) {
					// 仅有一条批次数据时，不用考虑按时长分配项目量，考虑直接将数据复制给班组方案数据
					CostBatchOnDuty x = pcl.get(0);
					pcid = x.getId();
					tbrq = x.getWriteDay();
					tjrq = x.getSummaryDay();
					bzdm = x.getTeamId();
					bcdm = x.getShiftId();
					sbsj = x.getShiftBegintime();
					xbsj = x.getShiftEndtime();
					sb = (new StringBuffer(tbrq).append(".").append(unitid).append(".").append(bzdm).append(".")
							.append(bcdm).append(".").append(faid)).toString();
					TeamProgramDto zb = null;
					if (!zbi.containsKey(sb)) {
						zb = new TeamProgramDto();
						zb.setUnitId(unitid);
						zb.setProgramId(faid);
						zb.setWriteDay(tbrq);
						zb.setTeamId(bzdm);
						zb.setShiftId(bcdm);
					} else {
						zb = zbi.get(sb);
					}
					zb.setShiftBeginTime(sbsj);
					zb.setShiftEndTime(xbsj);
					zb.setSummaryDay(x.getSummaryDay());
					for (CostBatchItemData y : pcxml) {
						CostTeamBatchItemData ctid = new CostTeamBatchItemData();
						itemid = y.getItemId();
						ctid.setId(TMUID.getUID());
						ctid.setPid(pcid);
						ctid.setUnitId(unitid);
						ctid.setWriteDay(tbrq);
						ctid.setSummaryDay(tjrq);
						ctid.setTeamId(bzdm);
						ctid.setShiftId(bcdm);
						ctid.setProgramId(faid);
						ctid.setBatchNo(bn);
						ctid.setItemId(itemid);
						ctid.setConsumption(y.getConsumption());
						ctid.setItemCost(y.getItemCost());
						ctid.setWorkingHour(y.getWorkingHour());
						ctid.setShiftBeginTime(sbsj);
						ctid.setShiftEndTime(xbsj);
						bzpcxm.add(ctid);
					}
					// 检索当班批次数据，如果还有记录说明还有其它批次的数据
					Where where = Where.create();
					where.eq(CostTeamBatchItemData::getUnitId, unitid);
					where.eq(CostTeamBatchItemData::getProgramId, faid);
					where.eq(CostTeamBatchItemData::getWriteDay, tbrq);
					where.eq(CostTeamBatchItemData::getTeamId, bzdm);
					where.eq(CostTeamBatchItemData::getShiftId, bcdm);
					List<CostTeamBatchItemData> hsl = entityService.queryList(CostTeamBatchItemData.class, where, null);
					if (hsl != null && hsl.size() > 0) {
						// 有其它批次的数据，无仪表数据
						zb.setNoybbs(true);
					} else {
						// 有仪表表数
						zb.setNoybbs(false);
					}
					zbi.put(sb, zb);
				} else {
					// 批次含有多个班次的数据时，要先划分项目消耗量
					// 划分当前计算批次的量
					HashMap<String, Double> gzscm = new HashMap<String, Double>();
					// 第一次得到总的时长和班组方案核算信息数据
					for (CostBatchOnDuty x : pcl) {
						pcid = x.getId();
						kssj = x.getBeginTime();
						jzsj = x.getEndTime();
						bcsc = DateTimeUtils.diffSecond(DateTimeUtils.parseDateTime(jzsj),
								DateTimeUtils.parseDateTime(kssj)) / 60.0;
						gzscm.put(pcid, bcsc);// 一个批次在一个班次内只能有一次
						zsc = zsc + bcsc;
					}
					if (zsc.compareTo(0.0) == 0) {
						// 如果总的时长是0，此时不计算班组的内容
						log.error("", "总时长是0，不计算班组数据。计算信息：" + JSON.toJSONString(reportinfo));
					} else {
						// 得到各当班班组的量
						for (CostBatchOnDuty x : pcl) {
							pcid = x.getId();
							tbrq = x.getWriteDay();
							tjrq = x.getSummaryDay();
							bzdm = x.getTeamId();
							bcdm = x.getShiftId();
							sbsj = x.getShiftBegintime();
							xbsj = x.getShiftEndtime();
							sb = (new StringBuffer(tbrq).append(".").append(unitid).append(".").append(bzdm).append(".")
									.append(bcdm).append(".").append(faid)).toString();
							TeamProgramDto zb = null;
							if (!zbi.containsKey(sb)) {
								zb = new TeamProgramDto();
								zb.setUnitId(unitid);
								zb.setProgramId(faid);
								zb.setWriteDay(tbrq);
								zb.setTeamId(bzdm);
								zb.setShiftId(bcdm);
							} else {
								zb = zbi.get(sb);
							}
							zb.setShiftBeginTime(sbsj);
							zb.setShiftEndTime(xbsj);
							zb.setSummaryDay(x.getSummaryDay());
							zb.setNoybbs(true);
							zbi.put(sb, zb);
							if (gzscm.containsKey(pcid)) {
								bcsc = gzscm.get(pcid);
							} else {
								bcsc = 0.0;
							}
							// 添加班组项目数据
							if (pcxml != null) {
								for (CostBatchItemData y : pcxml) {
									CostTeamBatchItemData ctid = new CostTeamBatchItemData();
									itemid = y.getItemId();
									ctid.setId(TMUID.getUID());
									ctid.setPid(pcid);
									ctid.setUnitId(unitid);
									ctid.setWriteDay(tbrq);
									ctid.setSummaryDay(tjrq);
									ctid.setTeamId(bzdm);
									ctid.setShiftId(bcdm);
									ctid.setProgramId(faid);
									ctid.setBatchNo(bn);
									ctid.setItemId(itemid);
									ctid.setShiftBeginTime(sbsj);
									ctid.setShiftEndTime(xbsj);
									val = y.getConsumption();
									if (val == null) {
										val = 0.0;
									}
									val = val * bcsc / zsc;
									ctid.setConsumption(val);
									val = y.getItemCost();
									if (val == null) {
										val = 0.0;
									}
									val = val * bcsc / zsc;
									ctid.setItemCost(val);
									ctid.setWorkingHour(bcsc);
									bzpcxm.add(ctid);
								}
							}
						}
					}
				}
				if (bzpcxm != null && bzpcxm.size() > 0) {
					// 有批次班组项目量，先保存，然后计算
					if (this.insertTeamBatchItemData(bzpcxm)) {
						if (zbi != null && zbi.size() > 0) {
							Boolean noybbs = false;
							// 计算当班方案，包含了方案日报、班组汇总、班组日报和核算对象日报
							for (Entry<String, TeamProgramDto> pp : zbi.entrySet()) {
								TeamProgramDto info = pp.getValue();
								bzdm = info.getTeamId();
								bcdm = info.getShiftId();
								tbrq = info.getWriteDay();
								tjrq = info.getSummaryDay();
								sbsj = info.getShiftBeginTime();
								xbsj = info.getShiftEndTime();
								noybbs = info.getNoybbs();
								if (noybbs == null) {
									noybbs = false;
								}
								// 班组方案的
								this.calcTeamProgramDayReport(unitid, faid, bzdm, bcdm, tbrq, tjrq, sbsj, xbsj,
										reportinfo, noybbs);
								if (!"0".equals(faid)) {
									// 无方案时，不再计算班组汇总的（不区分方案）
									this.calcTeamProgramDayReport(unitid, "0", bzdm, bcdm, tbrq, tjrq, sbsj, xbsj,
											reportinfo, noybbs);
								}
							}
						}
					} else {
						log.error("", "新增班组批次数据出现错误。批次信息：" + JSON.toJSONString(reportinfo));
						rtn = "新增班组批次数据出现错误。";
					}
				}
			}
		} else {
			log.error("", "删除旧的班组数据出现错误。批次信息：" + JSON.toJSONString(reportinfo));
			rtn = "删除旧的班组数据出现错误。";
		}
		return rtn;
	}

	private void calcTeamProgramDayReport(String unitid, String faid, String bzdm, String bcdm, String tbrq,
			String tjrq, String sbsj, String xbsj, TeamReportInputDto reportinfo, Boolean noybbs) {
		List<CostTeamInstrumentData> jjbybl = new ArrayList<CostTeamInstrumentData>();
		List<CostTeamItemData> jjbxml = new ArrayList<CostTeamItemData>();
		List<CostTeamParamData> jjbparaml = new ArrayList<CostTeamParamData>();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		HashMap<String, ProgramLibraryCostItem> fawzm = reportinfo.getFawzm();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		HashMap<String, String> vm = reportinfo.getValm();
		// 初始化计算类
		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), tbrq.substring(0, 7), tbrq, tbrq);
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		// 得到项目量
		List<ItemConsumption> bzxhl = null;
		if ("0".equals(faid)) {
			// 班组汇总的
			bzxhl = this.gics.getTeamItemConsumption(unitid, tbrq, bcdm, bzdm, im);
		} else {
			// 班组方案的
			bzxhl = this.gics.getTeamProgramItemConsumption(unitid, tbrq, faid, bcdm, bzdm, im);
		}

		if (bzxhl != null) {
			Double xhl, dj, jsz;
			String wzdm, key, keyx, val, pid, wzmc;
			// 旧的备注内容
			CostTeamInfo zbxx = this.getTeamDayCalcInfo(unitid, tbrq, faid, bzdm, bcdm);
			String oremark = "", oid;
			if (zbxx != null) {
				oremark = zbxx.getRemark();
				oid = zbxx.getId();
			} else {
				oid = "";
			}
			// 计算信息
			pid = TMUID.getUID();
			CostTeamInfo cti = new CostTeamInfo();
			cti.setId(pid);
			cti.setUnitId(unitid);
			cti.setWriteDay(tbrq);
			cti.setProgramId(faid);
			cti.setTeamId(bzdm);
			cti.setShiftId(bcdm);
			cti.setSummaryDay(tjrq);
			cti.setRemark(oremark);
			cti.setBeginTime(sbsj);
			cti.setEndTime(xbsj);
			if (noybbs == false) {
				// 直接将批次的仪表量作为交接班的仪表量
				HashMap<String, CostBatchInstrumentData> pcybm = reportinfo.getPcybm();
				if (pcybm != null) {
					for (Entry<String, CostBatchInstrumentData> x : pcybm.entrySet()) {
						CostBatchInstrumentData p = x.getValue();
						CostTeamInstrumentData dd = new CostTeamInstrumentData();
						dd.setId(TMUID.getUID());
						dd.setUnitId(unitid);
						dd.setPid(pid);
						dd.setWriteDay(tbrq);
						dd.setProgramId(faid);
						dd.setTeamId(bzdm);
						dd.setShiftId(bcdm);
						dd.setInstrumentId(p.getInstrumentId());
						dd.setPreviousReadOut(p.getPreviousReadOut());
						dd.setPreviousReadTime(p.getPreviousReadTime());
						dd.setLastReadOut(p.getLastReadOut());
						dd.setLastReadTime(p.getLastReadTime());
						dd.setWriteVal(p.getWriteVal());
						dd.setCalcVal(p.getCalcVal());
						dd.setShiftBeginTime(sbsj);
						dd.setShiftEndTime(xbsj);
						jjbybl.add(dd);
					}
				}
			}
			// 第一次循环将项目量添加到vm
			for (ItemConsumption x : bzxhl) {
				wzdm = x.getItemId();
				key = new StringBuffer(unitid).append(".").append(wzdm).toString();
				xhl = x.getConsumption();
				if (xhl == null) {
					xhl = 0.0;
				}
				val = String.valueOf(xhl);
				keyx = new StringBuffer(key).append(".xhl").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".cl").toString();
				vm.put(keyx, val);
				vm.put(wzdm, val);
				dj = x.getItemPrice();
				if (dj == null) {
					dj = 0.0;
				}
				val = String.valueOf(dj);
				keyx = new StringBuffer(key).append(".khdj").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".bzdj").toString();
				vm.put(keyx, val);
				reportinfo.setGzsc(x.getWorkingHour());
			}
			// 第二次循环将项目添加到List<CostSummaryItemData>
			Double dcyn;
			for (ItemConsumption x : bzxhl) {
				wzdm = x.getItemId();
				CostTeamItemData b = new CostTeamItemData();
				b.setId(TMUID.getUID());
				b.setPid(pid);
				b.setUnitId(unitid);
				b.setWriteDay(tbrq);
				b.setSummaryDay(tjrq);
				b.setProgramId(faid);
				b.setTeamId(bzdm);
				b.setShiftId(bcdm);
				b.setItemId(wzdm);
				b.setBeginTime(sbsj);
				b.setEndTime(xbsj);
				dcyn = x.getItemPrice();
				if (dcyn != null) {
					b.setItemPrice(Maths.round(dcyn, 2));
				}
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				b.setWorkingHour(x.getWorkingHour());
				xhl = x.getConsumption();
				b.setConsumption(xhl);
				b.setWriteConsumption(xhl);
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setItemCost(jsz);
				jjbxml.add(b);
			}
			// 计算核算指标
			if (csm != null) {
				String paramid;
				for (Entry<String, Costindicator> y : csm.entrySet()) {
					Costindicator param = y.getValue();
					if ("0".equals(faid)) {
						// 汇总模式下，指标定额从基础设置取
						dj = param.getStandardval();
					} else {
						// 不是汇总模式，要检验核算指标是否启用
						wzmc = param.getCpname();
						if (zbm.containsKey(wzmc)) {
							dj = zbm.get(wzmc).getBaseConsumption();
						} else {
							// 方案下未启用指标
							continue;
						}
					}
					paramid = param.getId();
					jsz = this.calcformula((new StringBuffer(paramid)).append(".param").toString(), reportinfo, ctsp,
							"", "", "", "", "");
					if ("0".equals(faid) && jsz.compareTo(0.0) == 0) {
						continue;
					}
					CostTeamParamData bean = new CostTeamParamData();
					bean.setId(TMUID.getUID());
					bean.setPid(pid);
					bean.setUnitId(unitid);
					bean.setWriteDay(tbrq);
					bean.setProgramId(faid);
					bean.setParamId(paramid);
					bean.setTeamId(bzdm);
					bean.setShiftId(bcdm);
					bean.setBaseVal(dj);
					bean.setCalcVal(jsz);
					bean.setWorkingHour(reportinfo.getGzsc());
					bean.setBeginTime(sbsj);
					bean.setEndTime(xbsj);
					jjbparaml.add(bean);
				}
			}
			String errinfo = "";
			if (this.iopsd.deleteTeamDayParamData(oid)) {
				if (this.iopsd.deleteTeamDayItemData(oid)) {
					if (this.iopsd.deleteTeamDayCalcInfo(oid)) {
						if (this.iopsd.insertTeamDayCalcInfo(cti)) {
							if (this.iopsd.insertTeamDayItemData(jjbxml)) {
								if (this.iopsd.insertTeamDayParamData(jjbparaml)) {
									if (this.iopsd.deleteTeamDayYbData(oid)) {
										if (jjbybl.size() > 0) {
											if (this.iopsd.insertTeamDayYbData(jjbybl)) {
												if (this.icca.createAnalysis(reportinfo)) {
													this.saveTask(reportinfo);
												} else {
													errinfo = "自动生成简要分析出现了错误";
												}
											} else {
												errinfo = "新增交接班仪表数据出现了错误";
											}
										}
									} else {
										errinfo = "删除交接班仪表数据出现了错误";
									}
									// 核算对象的日报：汇总还是方案的，由传入的faid决定
									TeamReportInputDto rbinfo = new TeamReportInputDto();
									rbinfo.setUnitId(unitid);
									rbinfo.setWriteDay(tjrq);
									rbinfo.setSummaryDay(tjrq);
									rbinfo.setOrgId("");
									this.iascc.checkDayData(rbinfo);
									// 计算方案周报
									HashMap<String, String> tjsd = gettjsd.getReportPeriod(unitid, tjrq, "3");
									if (tjsd != null) {
										// 周报：汇总还是方案的，由传入的faid决定
										String yf = tjsd.get("yf");
										String zs = tjsd.get("zs");
										// 核算对象：汇总还是方案的，由传入的faid决定
										TeamReportInputDto zbinfo = new TeamReportInputDto();
										zbinfo.setWriteDay(yf + "-" + zs);
										zbinfo.setUnitId(unitid);
										zbinfo.setBegintime(tjsd.get("ksrq"));
										zbinfo.setEndtime(tjsd.get("jzrq"));
										zbinfo.setOrgId("");
										this.iascc.checkWeekData(zbinfo);
									}
								} else {
									errinfo = "新增班组方案核算指标出现了错误";
								}
							} else {
								errinfo = "新增班组方案项目数据出现了错误";
							}
						} else {
							errinfo = "新增班组方案信息出现了错误";
						}
					} else {
						errinfo = "删除班组方案信息出现了错误";
					}
				} else {
					errinfo = "删除班组方案项目数据出现了错误";
				}
			} else {
				errinfo = "删除班组方案核算指标出现了错误";
			}
			if (!"".equals(errinfo)) {
				log.error("", errinfo + "。计算信息：" + JSON.toJSONString(reportinfo));
			}
		}
	}

	/**
	 * @category 从汇总的日报数据得到装置录入量
	 * @param pid
	 * @param data
	 */
	private void getDeviceWriteData(List<CostSummaryItemData> ol, List<ItemConsumption> data,
			HashMap<String, Costitem> xm) {
		HashMap<String, ItemConsumption> yysj = new HashMap<String, ItemConsumption>();
		for (ItemConsumption x : data) {
			yysj.put(x.getItemId(), x);
		}
		if (ol != null) {
			String wzdm;
			Integer ms;
			Double dcyn;
			for (CostSummaryItemData x : ol) {
				wzdm = x.getItemId();
				if (xm.containsKey(wzdm)) {
					ms = xm.get(wzdm).getMaterialSupply();
					if (ms == null) {
						ms = 1;
					}
					if (ms == 0) {
						if (yysj.containsKey(wzdm)) {
							// 已有数据，这时更新消耗量和单价
							ItemConsumption a = yysj.get(wzdm);
							dcyn = x.getConsumption();
							if (dcyn != null) {
								a.setConsumption(Maths.round(dcyn, 3));
							}
							dcyn = x.getItemPrice();
							if (dcyn != null) {
								a.setItemPrice(Maths.round(dcyn, 2));
							}
						} else {
							// 没有数据，新增
							ItemConsumption a = new ItemConsumption();
							a.setItemId(wzdm);
							dcyn = x.getConsumption();
							if (dcyn != null) {
								a.setConsumption(Maths.round(dcyn, 3));
							}
							dcyn = x.getItemPrice();
							if (dcyn != null) {
								a.setItemPrice(Maths.round(dcyn, 2));
							}
							yysj.put(wzdm, a);
						}
					}
				}
			}
		}
	}

	/**
	 * @category 交接班计算完成后，自动计算装置日报
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcDeviceDayReportAuto(TeamReportInputDto reportinfo) {
		String rtn = "";
		String tbrq = reportinfo.getWriteDay();
		String unitid = reportinfo.getUnitId();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		this.init(reportinfo, tbrq, false);
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		HashMap<String, ProgramLibraryCostItem> fawzm = reportinfo.getFawzm();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		// 旧的备注内容
		CostSummaryInfo zbxx = this.getDeviceDayCalcInfo(tbrq, unitid, faid, "");
		String oremark = "", oid;
		if (zbxx != null) {
			oremark = zbxx.getRemark();
			oid = zbxx.getId();
		} else {
			oid = "";
		}
		CostSummaryInfo n = new CostSummaryInfo();
		String pid = TMUID.getUID();
		n.setId(pid);
		n.setUnitId(unitid);
		n.setReportNo(tbrq);
		n.setProgramId(faid);
		n.setRemark(oremark);
		Where where = Where.create();
		where.eq(CostSummaryItemData::getPid, oid);
		List<CostSummaryItemData> oldxml = entityService.queryList(CostSummaryItemData.class, where, null);
		HashMap<String, CostSummaryItemData> oldxmm = new HashMap<String, CostSummaryItemData>();
		if (oldxml != null) {
			for (CostSummaryItemData x : oldxml) {
				oldxmm.put(x.getItemId(), x);
			}
		}

		List<CostSummaryItemData> xml = new ArrayList<CostSummaryItemData>();
		List<CostSummaryParamData> paraml = new ArrayList<CostSummaryParamData>();

		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), tbrq.substring(0, 7), tbrq, tbrq);
		Double dj, xhl, jsz;
		String wzdm, paramid, wzmc, key, keyx, val;
		// 从班组录入得到的数据
		List<ItemConsumption> xmhl = this.gics.getUnitDayItemConsumption(unitid, tbrq, faid, im);
		// 装置录入的数据：目前只能是汇总有装置录入
		if ("0".equals(faid)) {
			this.getDeviceWriteData(oldxml, xmhl, im);
		}
		if (xmhl != null) {
			// 第一次循环将项目量添加到vm
			for (ItemConsumption x : xmhl) {
				wzdm = x.getItemId();
				key = new StringBuffer(unitid).append(".").append(wzdm).toString();
				xhl = x.getConsumption();
				if (xhl == null) {
					xhl = 0.0;
				}
				val = String.valueOf(xhl);
				keyx = new StringBuffer(key).append(".xhl").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".cl").toString();
				vm.put(keyx, val);
				vm.put(wzdm, val);
				dj = x.getItemPrice();
				if (dj == null) {
					dj = 0.0;
				}
				val = String.valueOf(dj);
				keyx = new StringBuffer(key).append(".khdj").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".bzdj").toString();
				vm.put(keyx, val);
				reportinfo.setGzsc(x.getWorkingHour());
			}
			Double dcyn;
			// 第二次循环将项目添加到List<CostSummaryItemData>
			for (ItemConsumption x : xmhl) {
				wzdm = x.getItemId();
				CostSummaryItemData b = new CostSummaryItemData();
				if (oldxmm.containsKey(wzdm)) {
					b.setUpdateTime(oldxmm.get(wzdm).getUpdateTime());
				}
				b.setId(TMUID.getUID());
				b.setPid(pid);
				b.setUnitId(unitid);
				b.setProgramId(faid);
				b.setTeamId("0");
				b.setReportType("DayReport");
				b.setItemId(wzdm);
				dcyn = x.getItemPrice();
				if (dcyn != null) {
					b.setItemPrice(Maths.round(dcyn, 2));
				}
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				b.setWorkingHour(x.getWorkingHour());
				b.setWriteDay(tbrq);
				xhl = x.getConsumption();
				b.setConsumption(xhl);
				b.setWriteConsumption(xhl);
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setItemCost(jsz);
				xml.add(b);
			}
		}
		if (csm != null) {
			for (Entry<String, Costindicator> x : csm.entrySet()) {
				Costindicator param = x.getValue();
				if ("0".equals(faid)) {
					// 汇总模式，用基础设置
					dj = param.getStandardval();
				} else {
					// 方案下，检测启用状态
					wzmc = param.getCpname();
					if (zbm.containsKey(wzmc)) {
						dj = zbm.get(wzmc).getBaseConsumption();
					} else {
						continue;
					}
				}
				paramid = param.getId();
				key = (new StringBuffer(paramid)).append(".param").toString();
				jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
				if ("0".equals(faid) && jsz.compareTo(0.0) == 0) {
					continue;// 汇总模式下，指标计算值是0的项目不保存
				}
				CostSummaryParamData bean = new CostSummaryParamData();
				bean.setId(TMUID.getUID());
				bean.setPid(pid);
				bean.setUnitId(unitid);
				bean.setProgramId(faid);
				bean.setParamId(paramid);
				bean.setTeamId("0");
				bean.setReportType("DayReport");
				bean.setBaseVal(dj);
				bean.setCalcVal(jsz);
				bean.setWorkingHour(reportinfo.getGzsc());
				bean.setWriteDay(tbrq);
				paraml.add(bean);
			}
		}

		if (this.iopsd.delSumParam(unitid, tbrq, faid, "0")) {
			if (this.iopsd.delSumItem(unitid, tbrq, faid, "0")) {
				if (this.iopsd.delSumInfo(unitid, tbrq, faid, "0")) {
					if (this.iopsd.insertSummaryCalcInfo(n)) {
						if (this.iopsd.insertSummaryItemData(xml)) {
							if (this.iopsd.insertSummaryParamData(paraml)) {
								rtn = "";
							} else {
								rtn = "新增日报核算指标出现了错误";
							}
						} else {
							rtn = "新增日报项目数据出现了错误";
						}
					} else {
						rtn = "新增日报信息出现了错误";
					}
				} else {
					rtn = "删除日报信息出现了错误";
				}
			} else {
				rtn = "删除日报项目数据出现了错误";
			}
		} else {
			rtn = "删除日报核算指标出现了错误";
		}
		if (!"".equals(rtn)) {
			log.error("", rtn + "。计算信息：" + JSON.toJSONString(reportinfo));
		}
		return rtn;
	}

	/**
	 * @category 日报保存完成后，计算装置日报
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcDeviceDayReport(TeamReportInputDto reportinfo) {
		String rtn = "";
		String tbrq = reportinfo.getWriteDay();
		String unitid = reportinfo.getUnitId();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		this.init(reportinfo, tbrq, false);
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		HashMap<String, ProgramLibraryCostItem> fawzm = reportinfo.getFawzm();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		// 旧的备注内容
		CostSummaryInfo zbxx = this.getDeviceDayCalcInfo(tbrq, unitid, faid, "");
		String pid = "";
		if (zbxx != null) {
			pid = zbxx.getId();
		} else {
			log.error("", "保存装置日报时没有获取到制表信息。计算信息：" + JSON.toJSONString(reportinfo));
			return "保存装置日报时没有获取到制表信息";
		}
		List<CostSummaryParamData> paraml = new ArrayList<CostSummaryParamData>();
		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), tbrq.substring(0, 7), tbrq, tbrq);
		Double dj, xhl, jsz;
		String wzdm, paramid, wzmc, key, keyx, val;
		List<CostSummaryItemData> xmhl = this.getDeviceDayItemData(pid);
		if (xmhl != null) {
			// 第一次循环将项目量添加到vm
			for (CostSummaryItemData x : xmhl) {
				wzdm = x.getItemId();
				key = new StringBuffer(unitid).append(".").append(wzdm).toString();
				xhl = x.getConsumption();
				if (xhl == null) {
					xhl = 0.0;
				}
				val = String.valueOf(xhl);
				keyx = new StringBuffer(key).append(".xhl").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".cl").toString();
				vm.put(keyx, val);
				vm.put(wzdm, val);
				dj = x.getItemPrice();
				if (dj == null) {
					dj = 0.0;
				}
				val = String.valueOf(dj);
				keyx = new StringBuffer(key).append(".khdj").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".bzdj").toString();
				vm.put(keyx, val);
				reportinfo.setGzsc(x.getWorkingHour());
			}
			// 第二次循环将项目添加到List<CostSummaryItemData>
			for (CostSummaryItemData b : xmhl) {
				wzdm = b.getItemId();
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setItemCost(jsz);
			}
		}
		if (csm != null) {
			for (Entry<String, Costindicator> x : csm.entrySet()) {
				Costindicator param = x.getValue();
				if ("0".equals(faid)) {
					dj = param.getStandardval();
				} else {
					wzmc = param.getCpname();
					if (zbm.containsKey(wzmc)) {
						dj = zbm.get(wzmc).getBaseConsumption();
					} else {
						continue;
					}
				}
				paramid = param.getId();
				key = (new StringBuffer(paramid)).append(".param").toString();
				jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
				if ("0".equals(faid) && jsz.compareTo(0.0) == 0) {
					continue;// 汇总模式下，指标计算值是0的项目不保存
				}
				CostSummaryParamData bean = new CostSummaryParamData();
				bean.setId(TMUID.getUID());
				bean.setPid(pid);
				bean.setUnitId(unitid);
				bean.setProgramId(faid);
				bean.setTeamId("0");
				bean.setReportType("DayReport");
				bean.setParamId(paramid);
				bean.setBaseVal(dj);
				bean.setCalcVal(jsz);
				bean.setWorkingHour(reportinfo.getGzsc());
				bean.setWriteDay(tbrq);
				paraml.add(bean);
			}
		}

		if (this.iopsd.delSumParam(unitid, tbrq, faid, "0")) {
			if (this.updateDeviceDayItemData(xmhl)) {
				if (this.iopsd.insertSummaryParamData(paraml)) {
					// 计算方案周报
					HashMap<String, String> tjsd = gettjsd.getReportPeriod(unitid, tbrq, "3");
					if (tjsd != null) {
						String yf = tjsd.get("yf");
						String zs = tjsd.get("zs");
						TeamReportInputDto zbinfo = new TeamReportInputDto();
						zbinfo.setWriteDay(yf + "-" + zs);
						zbinfo.setUnitId(unitid);
						zbinfo.setBegintime(tjsd.get("ksrq"));
						zbinfo.setEndtime(tjsd.get("jzrq"));
						zbinfo.setOrgId("");
						this.iascc.checkWeekData(zbinfo);
					}
					rtn = "";
				} else {
					rtn = "新增日报核算指标出现了错误";
				}
			} else {
				rtn = "更新日报项目数据出现了错误";
			}
		} else {
			rtn = "删除日报核算指标出现了错误";
		}
		if (!"".equals(rtn)) {
			log.error("", rtn + "。计算信息：" + JSON.toJSONString(reportinfo));
		}
		return rtn;
	}

	/**
	 * @category 自动计算装置月报数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcDeviceMonthReportAuto(TeamReportInputDto reportinfo) {
		String rtn = "";
		String yf = reportinfo.getWriteDay();
		String unitid = reportinfo.getUnitId();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		String kssj = reportinfo.getBegintime();
		String jzsj = reportinfo.getEndtime();
		this.init(reportinfo, jzsj, false);
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		HashMap<String, ProgramLibraryCostItem> fawzm = reportinfo.getFawzm();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		// 旧的备注内容
		CostSummaryInfo zbxx = this.getDeviceDayCalcInfo(yf, unitid, faid, "");
		String oremark = "", oid;
		if (zbxx != null) {
			oremark = zbxx.getRemark();
			oid = zbxx.getId();
		} else {
			oid = "";
		}
		CostSummaryInfo n = new CostSummaryInfo();
		String pid = TMUID.getUID();
		n.setId(pid);
		n.setUnitId(unitid);
		n.setReportNo(yf);
		n.setProgramId(faid);
		n.setRemark(oremark);
		n.setBeginTime(kssj);
		n.setEndTime(jzsj);
		n.setReportDay(reportinfo.getSummaryDay());
		List<CostSummaryItemData> xml = new ArrayList<CostSummaryItemData>();
		List<CostSummaryParamData> paraml = new ArrayList<CostSummaryParamData>();
		HashMap<String, Double> nxmscm = new HashMap<String, Double>();
		HashMap<String, Double> nxmlm = new HashMap<String, Double>();
		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), yf, kssj, jzsj);
		Double dj, xhl, jsz, sc;
		String wzdm, paramid, wzmc, key, keyx, val;
		// 数据来源于装置日报
		List<ItemConsumption> xmhl = this.gics.getUnitWeekItemConsumption(unitid, kssj, jzsj, faid, "", im);
		if (xmhl != null) {
			// 第一次循环将项目量添加到vm
			for (ItemConsumption x : xmhl) {
				wzdm = x.getItemId();
				key = new StringBuffer(unitid).append(".").append(wzdm).toString();
				xhl = x.getConsumption();
				if (xhl == null) {
					xhl = 0.0;
				}
				nxmlm.put(wzdm, xhl);// 初始化年累计的本月值
				val = String.valueOf(xhl);
				keyx = new StringBuffer(key).append(".xhl").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".cl").toString();
				vm.put(keyx, val);
				vm.put(wzdm, val);
				dj = x.getItemPrice();
				if (dj == null) {
					dj = 0.0;
				}
				if (im.containsKey(wzdm)) {
					im.get(wzdm).setItemprice(dj);
				}
				val = String.valueOf(dj);
				keyx = new StringBuffer(key).append(".khdj").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".bzdj").toString();
				vm.put(keyx, val);
				sc = x.getWorkingHour();
				if (sc == null) {
					sc = 0.0;
				}
				if (sc.compareTo(0.0) != 0) {
					nxmscm.put(wzdm, sc);
					reportinfo.setGzsc(sc);
				}
			}
			Double dcyn;
			// 第二次循环将项目添加到List<CostSummaryItemData>
			for (ItemConsumption x : xmhl) {
				wzdm = x.getItemId();
				CostSummaryItemData b = new CostSummaryItemData();
				b.setId(TMUID.getUID());
				b.setPid(pid);
				b.setUnitId(unitid);
				b.setProgramId(faid);
				b.setReportType("MonthReport");
				b.setItemId(wzdm);
				dcyn = x.getItemPrice();
				if (dcyn != null) {
					b.setItemPrice(Maths.round(dcyn, 2));
				}
				b.setBeginTime(kssj);
				b.setEndTime(jzsj);
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				b.setWorkingHour(x.getWorkingHour());
				b.setWriteDay(yf);
				xhl = x.getConsumption();
				b.setConsumption(xhl);
				b.setWriteConsumption(xhl);
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setItemCost(jsz);
				xml.add(b);
			}
		}
		if (csm != null) {
			for (Entry<String, Costindicator> x : csm.entrySet()) {
				Costindicator param = x.getValue();
				if ("0".equals(faid)) {
					dj = param.getStandardval();
				} else {
					wzmc = param.getCpname();
					if (zbm.containsKey(wzmc)) {
						dj = zbm.get(wzmc).getBaseConsumption();
					} else {
						continue;
					}
				}
				paramid = param.getId();
				key = (new StringBuffer(paramid)).append(".param").toString();
				jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
				if ("0".equals(faid) && jsz.compareTo(0.0) == 0) {
					continue;// 汇总模式下，指标计算值是0的项目不保存
				}
				CostSummaryParamData bean = new CostSummaryParamData();
				bean.setId(TMUID.getUID());
				bean.setPid(pid);
				bean.setUnitId(unitid);
				bean.setProgramId(faid);
				bean.setParamId(paramid);
				bean.setReportType("MonthReport");
				bean.setBaseVal(dj);
				bean.setCalcVal(jsz);
				bean.setWorkingHour(reportinfo.getGzsc());
				bean.setBeginTime(kssj);
				bean.setEndTime(jzsj);
				bean.setWriteDay(yf);
				paraml.add(bean);
			}
		}
		// 年累计
		if (xml != null) {
			String nc = yf.substring(0, 4) + "-01";
			List<ItemConsumption> nxml = this.gics.getUnitYearItemConsumption(unitid, nc, yf, faid, im);// 不含当月
			if (nxml != null) {
				// 将项目量添加到vm
				for (ItemConsumption x : nxml) {
					wzdm = x.getItemId();
					key = new StringBuffer(unitid).append(".").append(wzdm).toString();
					xhl = x.getConsumption();
					if (xhl == null) {
						xhl = 0.0;
					}
					if (nxmlm.containsKey(wzdm)) {
						xhl = nxmlm.get(wzdm) + xhl;// 年累计加上当月
					}
					val = String.valueOf(xhl);
					keyx = new StringBuffer(key).append(".xhl").toString();
					vm.put(keyx, val);
					keyx = new StringBuffer(key).append(".cl").toString();
					vm.put(keyx, val);
					vm.put(wzdm, val);
					nxmlm.put(wzdm, xhl);
					sc = x.getWorkingHour();
					if (sc == null) {
						sc = 0.0;
					}
					if (nxmscm.containsKey(wzdm)) {
						sc = nxmscm.get(wzdm) + sc;// 年累计加上当月
					}
					if (sc.compareTo(0.0) != 0) {
						nxmscm.put(wzdm, sc);
						reportinfo.setGzsc(sc);
					}
				}
			}

			for (CostSummaryItemData x : xml) {
				wzdm = x.getItemId();
				x.setYWorkingHour(nxmscm.get(wzdm));
				x.setYConsumption(nxmlm.get(wzdm));
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm, "",
						"", "", "");
				x.setYBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm, "",
						"", "", "");
				x.setYBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, "",
						"", "", "");
				x.setYUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm, "",
						"", "", "");
				x.setYUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, "",
						"", "", "");
				x.setYItemCost(jsz);
			}
		}
		if (paraml != null) {
			for (CostSummaryParamData x : paraml) {
				paramid = x.getParamId();
				key = (new StringBuffer(paramid)).append(".param").toString();
				jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
				x.setYCalcVal(jsz);
				x.setYWorkingHour(reportinfo.getGzsc());
			}
		}

		if (this.iopsd.deleteSummaryParamData(oid)) {
			if (this.iopsd.deleteSummaryItemData(oid)) {
				if (this.iopsd.deleteSummaryInfo(oid)) {
					if (this.iopsd.insertSummaryCalcInfo(n)) {
						if (this.iopsd.insertSummaryItemData(xml)) {
							if (this.iopsd.insertSummaryParamData(paraml)) {
								rtn = "";
							} else {
								rtn = "新增装置月报核算指标出现了错误";
							}
						} else {
							rtn = "新增装置月报项目数据出现了错误";
						}
					} else {
						rtn = "新增装置月报信息出现了错误";
					}
				} else {
					rtn = "删除装置月报信息出现了错误";
				}
			} else {
				rtn = "删除装置月报项目数据出现了错误";
			}
		} else {
			rtn = "删除装置月报核算指标出现了错误";
		}
		if (!"".equals(rtn)) {
			log.error("", rtn + "。计算信息：" + JSON.toJSONString(reportinfo));
		}
		return rtn;
	}

	/**
	 * @category 计算装置月报数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcDeviceMonthReport(TeamReportInputDto reportinfo) {
		String rtn = "";
		String yf = reportinfo.getWriteDay();
		String unitid = reportinfo.getUnitId();
		String kssj = reportinfo.getBegintime();
		String jzsj = reportinfo.getEndtime();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		this.init(reportinfo, jzsj, false);
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		HashMap<String, ProgramLibraryCostItem> fawzm = reportinfo.getFawzm();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		// 旧的备注内容
		CostSummaryInfo zbxx = this.getDeviceDayCalcInfo(yf, unitid, faid, "");
		String pid = "";
		if (zbxx != null) {
			pid = zbxx.getId();
		} else {
			log.error("", "保存装置月报时没有获取到制表信息。计算信息：" + JSON.toJSONString(reportinfo));
			return "保存装置月报时没有获取到制表信息";
		}
		List<CostSummaryParamData> paraml = new ArrayList<CostSummaryParamData>();
		HashMap<String, Double> nxmscm = new HashMap<String, Double>();
		HashMap<String, Double> nxmlm = new HashMap<String, Double>();
		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), yf, kssj, jzsj);
		Double dj, xhl, jsz, sc;
		String wzdm, paramid, wzmc, key, keyx, val;
		List<CostSummaryItemData> xmhl = this.getDeviceDayItemData(pid);
		if (xmhl != null) {
			// 第一次循环将项目量添加到vm
			for (CostSummaryItemData x : xmhl) {
				wzdm = x.getItemId();
				key = new StringBuffer(unitid).append(".").append(wzdm).toString();
				xhl = x.getWriteConsumption();
				if (xhl == null) {
					xhl = 0.0;
				}
				nxmlm.put(wzdm, xhl);
				val = String.valueOf(xhl);
				keyx = new StringBuffer(key).append(".xhl").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".cl").toString();
				vm.put(keyx, val);
				vm.put(wzdm, val);
				dj = x.getItemPrice();
				if (dj == null) {
					dj = 0.0;
				}
				val = String.valueOf(dj);
				keyx = new StringBuffer(key).append(".khdj").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".bzdj").toString();
				vm.put(keyx, val);
				sc = x.getWorkingHour();
				if (sc == null) {
					sc = 0.0;
				}
				if (sc.compareTo(0.0) != 0) {
					nxmscm.put(wzdm, sc);
					reportinfo.setGzsc(sc);
				}
			}
			// 第二次循环将项目添加到List<CostSummaryItemData>
			for (CostSummaryItemData b : xmhl) {
				wzdm = b.getItemId();
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setItemCost(jsz);
			}
		}
		if (csm != null) {
			for (Entry<String, Costindicator> x : csm.entrySet()) {
				Costindicator param = x.getValue();
				if ("0".equals(faid)) {
					dj = param.getStandardval();
				} else {
					wzmc = param.getCpname();
					if (zbm.containsKey(wzmc)) {
						dj = zbm.get(wzmc).getBaseConsumption();
					} else {
						continue;
					}
				}
				paramid = param.getId();
				key = (new StringBuffer(paramid)).append(".param").toString();
				jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
				if ("0".equals(faid) && jsz.compareTo(0.0) == 0) {
					continue;// 汇总模式下，指标计算值是0的项目不保存
				}
				CostSummaryParamData bean = new CostSummaryParamData();
				bean.setId(TMUID.getUID());
				bean.setPid(pid);
				bean.setUnitId(unitid);
				bean.setProgramId(faid);
				bean.setParamId(paramid);
				bean.setReportType("MonthReport");
				bean.setBaseVal(dj);
				bean.setCalcVal(jsz);
				bean.setWorkingHour(reportinfo.getGzsc());
				bean.setWriteDay(yf);
				bean.setBeginTime(kssj);
				bean.setEndTime(jzsj);
				paraml.add(bean);
			}
		}
		// 年累计
		if (xmhl != null) {
			String nc = yf.substring(0, 4) + "-01";
			List<ItemConsumption> nxml = this.gics.getUnitYearItemConsumption(unitid, nc, yf, faid, im);// 不含当月
			if (nxml != null) {
				// 将项目量添加到vm
				for (ItemConsumption x : nxml) {
					wzdm = x.getItemId();
					key = new StringBuffer(unitid).append(".").append(wzdm).toString();
					xhl = x.getConsumption();
					if (xhl == null) {
						xhl = 0.0;
					}
					if (nxmlm.containsKey(wzdm)) {
						xhl = nxmlm.get(wzdm) + xhl;// 年累计加上当月
					}
					val = String.valueOf(xhl);
					keyx = new StringBuffer(key).append(".xhl").toString();
					vm.put(keyx, val);
					keyx = new StringBuffer(key).append(".cl").toString();
					vm.put(keyx, val);
					vm.put(wzdm, val);
					nxmlm.put(wzdm, xhl);
					sc = x.getWorkingHour();
					if (sc == null) {
						sc = 0.0;
					}
					if (nxmscm.containsKey(wzdm)) {
						sc = nxmscm.get(wzdm) + sc;// 年累计加上当月
					}
					if (sc.compareTo(0.0) != 0) {
						nxmscm.put(wzdm, sc);
						reportinfo.setGzsc(sc);
					}
				}
			}

			for (CostSummaryItemData x : xmhl) {
				wzdm = x.getItemId();
				x.setYWorkingHour(nxmscm.get(wzdm));
				x.setYConsumption(nxmlm.get(wzdm));
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm, "",
						"", "", "");
				x.setYBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm, "",
						"", "", "");
				x.setYBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, "",
						"", "", "");
				x.setYUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm, "",
						"", "", "");
				x.setYUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, "",
						"", "", "");
				x.setYItemCost(jsz);
			}
		}
		if (paraml != null) {
			for (CostSummaryParamData x : paraml) {
				paramid = x.getParamId();
				key = (new StringBuffer(paramid)).append(".param").toString();
				jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
				x.setYCalcVal(jsz);
				x.setYWorkingHour(reportinfo.getGzsc());
			}
		}

		if (this.iopsd.deleteSummaryParamData(pid)) {
			if (this.updateDeviceDayItemData(xmhl)) {
				if (this.iopsd.insertSummaryParamData(paraml)) {
					rtn = "";
				} else {
					rtn = "新增装置月报核算指标出现了错误";
				}
			} else {
				rtn = "更新装置月报项目数据出现了错误";
			}
		} else {
			rtn = "删除装置月报核算指标出现了错误";
		}
		if (!"".equals(rtn)) {
			log.error("", rtn + "。计算信息：" + JSON.toJSONString(reportinfo));
		}
		return rtn;
	}

	/**
	 * @category 自动计算班组月报数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcTeamMonthReportAuto(TeamReportInputDto reportinfo) {
		String rtn = "";
		String yf = reportinfo.getWriteDay();// yyyy-mm格式的月份
		String rn = yf.substring(0, 4) + yf.substring(5, 7) + "000000";
		String unitid = reportinfo.getUnitId();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		String kssj = reportinfo.getBegintime();
		String jzsj = reportinfo.getEndtime();
		this.init(reportinfo, jzsj, false);
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		MethodQueryDto queryDto = new MethodQueryDto();
		queryDto.setUnitid(unitid);
		queryDto.setObjType("org");
		List<Costunitoperator> tlist = unitMethodService.getCostunitoperatorList(queryDto);

		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		HashMap<String, ProgramLibraryCostItem> fawzm = reportinfo.getFawzm();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		// 旧的备注内容
		CostSummaryInfo zbxx = this.getDeviceDayCalcInfo(rn, unitid, faid, "");
		String oremark = "", oid;
		if (zbxx != null) {
			oremark = zbxx.getRemark();
			oid = zbxx.getId();
		} else {
			oid = "";
		}
		CostSummaryInfo n = new CostSummaryInfo();
		String pid = TMUID.getUID();
		n.setId(pid);
		n.setUnitId(unitid);
		n.setReportNo(rn);
		n.setProgramId(faid);
		n.setRemark(oremark);
		n.setBeginTime(kssj);
		n.setEndTime(jzsj);
		n.setReportDay(reportinfo.getSummaryDay());
		List<CostSummaryItemData> xml = new ArrayList<CostSummaryItemData>();
		List<CostSummaryParamData> paraml = new ArrayList<CostSummaryParamData>();

		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), yf, kssj, jzsj);
		HashMap<String, Double> dxhm = new HashMap<String, Double>();// 装置项目消耗
		HashMap<String, Double> cbm = new HashMap<String, Double>();// 装置项目总金额
		HashMap<String, Double> djm = new HashMap<String, Double>();// 装置项目单价
		HashMap<String, Double> sjm = new HashMap<String, Double>();// 工作时间
		Double dj, xhl, jsz, gzsj, zgzsj = 0.0;
		String wzdm, teamid, paramid, wzmc, key, keyx, val;
		HashMap<String, HashMap<String, String>> ydm = new HashMap<String, HashMap<String, String>>();
		// 数据来源于交接班
		List<ItemConsumption> xmhl = this.gics.getTeamPeriodItemConsumption(unitid, kssj, jzsj, faid, im);
		if (xmhl != null) {
			// 第一次循环将项目量添加到vm
			for (ItemConsumption x : xmhl) {
				wzdm = x.getItemId();
				teamid = x.getTeamId();
				if (ydm.containsKey(wzdm)) {
					ydm.get(wzdm).put(teamid, "1");
				} else {
					HashMap<String, String> dm = new HashMap<String, String>();
					dm.put(teamid, "1");
					ydm.put(wzdm, dm);
				}
				// 消耗量
				xhl = x.getConsumption();
				if (xhl == null) {
					xhl = 0.0;
				}
				if (dxhm.containsKey(wzdm)) {
					dxhm.put(wzdm, dxhm.get(wzdm) + xhl);
				} else {
					dxhm.put(wzdm, xhl);
				}
				val = String.valueOf(xhl);
				keyx = new StringBuffer(wzdm).append(".").append(teamid).append(".xhl").toString();
				vm.put(keyx, val);
				// 工作时间
				gzsj = x.getWorkingHour();
				if (gzsj == null) {
					gzsj = 0.0;
				}
				vm.put(teamid, String.valueOf(gzsj));
				sjm.put(teamid, gzsj);
				// 得到总的金额，用于后续计算项目单价
				dj = x.getItemPrice();
				if (dj == null) {
					dj = 0.0;
				}
				if (cbm.containsKey(wzdm)) {
					cbm.put(wzdm, cbm.get(wzdm) + dj * xhl);
				} else {
					cbm.put(wzdm, dj * xhl);
				}
			}
			// 装置总的工作时间
			for (Entry<String, Double> sj : sjm.entrySet()) {
				zgzsj = zgzsj + sj.getValue();
			}
			vm.put("0", String.valueOf(zgzsj));
			sjm.put("0", zgzsj);
			// 添加装置的量
			for (Entry<String, Double> xh : dxhm.entrySet()) {
				wzdm = xh.getKey();
				xhl = xh.getValue();
				val = String.valueOf(xhl);
				keyx = new StringBuffer(wzdm).append(".0.xhl").toString();
				vm.put(keyx, val);
				dj = 0.0;
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					dj = wz.getItemprice();
					if (dj == null) {
						dj = 0.0;
					}
				}
				// 单价，需要根据总成本和量重新计算
				if (cbm.containsKey(wzdm)) {
					jsz = cbm.get(wzdm);
					if (xhl.compareTo(0.0) != 0) {
						dj = Maths.round(jsz / xhl, 2);
					}
				}
				val = String.valueOf(dj);
				key = new StringBuffer(unitid).append(".").append(wzdm).toString();
				keyx = new StringBuffer(key).append(".khdj").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".bzdj").toString();
				vm.put(keyx, val);
				djm.put(wzdm, dj);
			}
			// 第二次循环将项目添加到List<CostSummaryItemData>
			Double dcyn;
			for (ItemConsumption x : xmhl) {
				wzdm = x.getItemId();
				teamid = x.getTeamId();
				CostSummaryItemData b = new CostSummaryItemData();
				b.setId(TMUID.getUID());
				b.setPid(pid);
				b.setUnitId(unitid);
				b.setProgramId(faid);
				b.setReportType("bzybReport");
				b.setItemId(wzdm);
				if (djm.containsKey(wzdm)) {
					dcyn = djm.get(wzdm);
					if (dcyn != null) {
						b.setItemPrice(Maths.round(dcyn, 2));
					}
				} else {
					b.setItemPrice(0.0);
				}
				b.setBeginTime(kssj);
				b.setEndTime(jzsj);
				b.setTeamId(teamid);
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				b.setWorkingHour(x.getWorkingHour());
				xhl = x.getConsumption();
				b.setConsumption(xhl);
				b.setWriteConsumption(xhl);
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", teamid);
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", teamid);
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", teamid);
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", teamid);
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", teamid);
				b.setItemCost(jsz);
				xml.add(b);
			}
			// 添加没有量的班组
			if (tlist != null) {
				for (Entry<String, Double> e : dxhm.entrySet()) {
					wzdm = e.getKey();
					if (ydm.containsKey(wzdm)) {
						HashMap<String, String> dm = ydm.get(wzdm);
						for (Costunitoperator tid : tlist) {
							teamid = tid.getObjid();
							if (!dm.containsKey(teamid)) {
								CostSummaryItemData b = new CostSummaryItemData();
								b.setId(TMUID.getUID());
								b.setPid(pid);
								b.setUnitId(unitid);
								b.setProgramId(faid);
								b.setReportType("bzybReport");
								b.setItemId(wzdm);
								if (djm.containsKey(wzdm)) {
									dcyn = djm.get(wzdm);
									if (dcyn != null) {
										b.setItemPrice(Maths.round(dcyn, 2));
									}
								} else {
									b.setItemPrice(0.0);
								}
								b.setBeginTime(kssj);
								b.setEndTime(jzsj);
								b.setTeamId(teamid);
								if (im.containsKey(wzdm)) {
									Costitem wz = im.get(wzdm);
									b.setBaseUnitConsumption(wz.getBaseConsumption());
									if (!"0".equals(faid)) {
										// 使用了方案，这时取方案内的标准单耗
										if (fawzm != null && fawzm.containsKey(wzdm)) {
											b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
										}
									}
								}
								b.setWorkingHour(0.0);
								b.setConsumption(0.0);
								b.setWriteConsumption(0.0);
								b.setBaseConsumption(0.0);
								b.setBaseCost(0.0);
								b.setUnitConsumption(0.0);
								b.setUnitCost(0.0);
								b.setItemCost(0.0);
								xml.add(b);
							}
						}
					}
				}
			}
			// 添加装置的项目量
			for (Entry<String, Double> e : dxhm.entrySet()) {
				wzdm = e.getKey();
				CostSummaryItemData b = new CostSummaryItemData();
				b.setId(TMUID.getUID());
				b.setPid(pid);
				b.setUnitId(unitid);
				b.setProgramId(faid);
				b.setReportType("bzybReport");
				b.setItemId(wzdm);
				if (djm.containsKey(wzdm)) {
					dcyn = djm.get(wzdm);
					if (dcyn != null) {
						b.setItemPrice(Maths.round(dcyn, 2));
					}
				} else {
					b.setItemPrice(0.0);
				}
				b.setBeginTime(kssj);
				b.setEndTime(jzsj);
				b.setTeamId("0");
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				if (sjm.containsKey("0")) {
					b.setWorkingHour(sjm.get("0"));
				} else {
					b.setWorkingHour(0.0);
				}
				xhl = e.getValue();
				b.setConsumption(xhl);
				b.setWriteConsumption(xhl);
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "0");
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "0");
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "0");
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "0");
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "0");
				b.setItemCost(jsz);
				xml.add(b);
			}
		}
		if (csm != null) {
			for (Entry<String, Double> sj : sjm.entrySet()) {
				teamid = sj.getKey();
				gzsj = sj.getValue();
				for (Entry<String, Costindicator> x : csm.entrySet()) {
					Costindicator param = x.getValue();
					if ("0".equals(faid)) {
						dj = param.getStandardval();
					} else {
						wzmc = param.getCpname();
						if (zbm.containsKey(wzmc)) {
							dj = zbm.get(wzmc).getBaseConsumption();
						} else {
							continue;
						}
					}
					paramid = param.getId();
					key = (new StringBuffer(paramid)).append(".param").toString();
					jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", teamid);
					if ("0".equals(faid) && jsz.compareTo(0.0) == 0) {
						continue;// 汇总模式下，指标计算值是0的项目不保存
					}
					CostSummaryParamData bean = new CostSummaryParamData();
					bean.setId(TMUID.getUID());
					bean.setPid(pid);
					bean.setUnitId(unitid);
					bean.setProgramId(faid);
					bean.setReportType("bzybReport");
					bean.setTeamId(teamid);
					bean.setParamId(paramid);
					bean.setBaseVal(dj);
					bean.setCalcVal(jsz);
					bean.setWorkingHour(gzsj);
					bean.setBeginTime(kssj);
					bean.setEndTime(jzsj);
					paraml.add(bean);
				}
			}
		}

		if (this.iopsd.deleteSummaryParamData(oid)) {
			if (this.iopsd.deleteSummaryItemData(oid)) {
				if (this.iopsd.deleteSummaryInfo(oid)) {
					if (this.iopsd.insertSummaryCalcInfo(n)) {
						if (this.iopsd.insertSummaryItemData(xml)) {
							if (this.iopsd.insertSummaryParamData(paraml)) {
								rtn = "";
							} else {
								rtn = "新增班组月报核算指标出现了错误";
							}
						} else {
							rtn = "新增班组月报项目数据出现了错误";
						}
					} else {
						rtn = "新增班组月报信息出现了错误";
					}
				} else {
					rtn = "删除班组月报信息出现了错误";
				}
			} else {
				rtn = "删除班组月报项目数据出现了错误";
			}
		} else {
			rtn = "删除班组月报核算指标出现了错误";
		}
		if (!"".equals(rtn)) {
			log.error("", rtn + "。计算信息：" + JSON.toJSONString(reportinfo));
		}
		return rtn;
	}

	/**
	 * @category 根据用户保存的量计算班组月报数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcTeamMonthReport(TeamReportInputDto reportinfo) {
		String rtn = "";
		String yf = reportinfo.getWriteDay();// yyyy-mm格式的月份
		String rn = yf.substring(0, 4) + yf.substring(5, 7) + "000000";
		String unitid = reportinfo.getUnitId();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		String kssj = reportinfo.getBegintime();
		String jzsj = reportinfo.getEndtime();
		this.init(reportinfo, jzsj, false);
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		HashMap<String, ProgramLibraryCostItem> fawzm = reportinfo.getFawzm();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		// 旧的备注内容
		CostSummaryInfo zbxx = this.getDeviceDayCalcInfo(rn, unitid, faid, "");
		String pid;
		if (zbxx != null) {
			pid = zbxx.getId();
		} else {
			log.error("", "保存装置周报时没有获取到制表信息。计算信息：" + JSON.toJSONString(reportinfo));
			return "保存装置周报时没有获取到制表信息";
		}

		List<CostSummaryItemData> xml = new ArrayList<CostSummaryItemData>();
		List<CostSummaryParamData> paraml = new ArrayList<CostSummaryParamData>();

		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), yf, kssj, jzsj);
		HashMap<String, Double> dxhm = new HashMap<String, Double>();// 装置项目消耗
		HashMap<String, Double> djm = new HashMap<String, Double>();// 装置项目单价
		HashMap<String, Double> sjm = new HashMap<String, Double>();// 工作时间
		Double dj, xhl, jsz, gzsj, zgzsj = 0.0;
		String wzdm, teamid, paramid, wzmc, key, keyx, val;
		// 数据来源于班组月汇总
		List<CostSummaryItemData> xmhl = this.getDeviceDayItemData(pid);
		if (xmhl != null) {
			// 第一次循环将项目量添加到vm
			for (CostSummaryItemData x : xmhl) {
				wzdm = x.getItemId();
				teamid = x.getTeamId();
				if ("0".equals(teamid)) {
					// 装置数据用于获取单价
				} else {
					// 班组数据用于添加消耗量
					dj = x.getItemPrice();
					if (dj == null) {
						dj = 0.0;
					}
					val = String.valueOf(dj);
					key = new StringBuffer(unitid).append(".").append(wzdm).toString();
					keyx = new StringBuffer(key).append(".khdj").toString();
					vm.put(keyx, val);
					keyx = new StringBuffer(key).append(".bzdj").toString();
					vm.put(keyx, val);
					djm.put(wzdm, dj);
					xhl = x.getConsumption();
					if (xhl == null) {
						xhl = 0.0;
					}
					if (dxhm.containsKey(wzdm)) {
						dxhm.put(wzdm, dxhm.get(wzdm) + xhl);
					} else {
						dxhm.put(wzdm, xhl);
					}
					val = String.valueOf(xhl);
					keyx = new StringBuffer(wzdm).append(".").append(teamid).append(".xhl").toString();
					vm.put(keyx, val);
					// 工作时间
					gzsj = x.getWorkingHour();
					if (gzsj == null) {
						gzsj = 0.0;
					}
					vm.put(teamid, String.valueOf(gzsj));
					sjm.put(teamid, gzsj);
				}
			}
			// 装置总的工作时间
			for (Entry<String, Double> sj : sjm.entrySet()) {
				zgzsj = zgzsj + sj.getValue();
			}
			vm.put("0", String.valueOf(zgzsj));
			sjm.put("0", zgzsj);
			// 添加装置的量
			for (Entry<String, Double> xh : dxhm.entrySet()) {
				wzdm = xh.getKey();
				xhl = xh.getValue();
				val = String.valueOf(xhl);
				keyx = new StringBuffer(wzdm).append(".0.xhl").toString();
				vm.put(keyx, val);
			}
			// 第二次循环将项目添加到List<CostSummaryItemData>
			for (CostSummaryItemData x : xmhl) {
				wzdm = x.getItemId();
				teamid = x.getTeamId();
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					x.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							x.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				if ("0".equals(teamid)) {
					// 装置的使用班组量合计
					xhl = 0.0;
					if (dxhm.containsKey(wzdm)) {
						xhl = dxhm.get(wzdm);
					}
				} else {
					xhl = x.getConsumption();
				}
				x.setConsumption(xhl);
				x.setWriteConsumption(xhl);
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", teamid);
				x.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", teamid);
				x.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", teamid);
				x.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", teamid);
				x.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", teamid);
				x.setItemCost(jsz);
				xml.add(x);
			}
			// 添加装置的项目量
			Double dcyn;
			for (Entry<String, Double> e : dxhm.entrySet()) {
				wzdm = e.getKey();
				CostSummaryItemData b = new CostSummaryItemData();
				b.setId(TMUID.getUID());
				b.setPid(pid);
				b.setUnitId(unitid);
				b.setProgramId(faid);
				b.setReportType("bzybReport");
				b.setItemId(wzdm);
				if (djm.containsKey(wzdm)) {
					dcyn = djm.get(wzdm);
					if (dcyn != null) {
						b.setItemPrice(Maths.round(dcyn, 2));
					}
				} else {
					b.setItemPrice(0.0);
				}
				b.setBeginTime(kssj);
				b.setEndTime(jzsj);
				b.setTeamId("0");
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				if (sjm.containsKey("0")) {
					b.setWorkingHour(sjm.get("0"));
				} else {
					b.setWorkingHour(0.0);
				}
				xhl = e.getValue();
				b.setConsumption(xhl);
				b.setWriteConsumption(xhl);
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "0");
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "0");
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "0");
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "0");
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "0");
				b.setItemCost(jsz);
				xml.add(b);
			}
		}
		if (csm != null) {
			for (Entry<String, Double> sj : sjm.entrySet()) {
				teamid = sj.getKey();
				gzsj = sj.getValue();
				for (Entry<String, Costindicator> x : csm.entrySet()) {
					Costindicator param = x.getValue();
					if ("0".equals(faid)) {
						dj = param.getStandardval();
					} else {
						wzmc = param.getCpname();
						if (zbm.containsKey(wzmc)) {
							dj = zbm.get(wzmc).getBaseConsumption();
						} else {
							continue;
						}
					}
					paramid = param.getId();
					key = (new StringBuffer(paramid)).append(".param").toString();
					jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", teamid);
					if ("0".equals(faid) && jsz.compareTo(0.0) == 0) {
						continue;// 汇总模式下，指标计算值是0的项目不保存
					}
					CostSummaryParamData bean = new CostSummaryParamData();
					bean.setId(TMUID.getUID());
					bean.setPid(pid);
					bean.setUnitId(unitid);
					bean.setProgramId(faid);
					bean.setReportType("bzybReport");
					bean.setTeamId(teamid);
					bean.setParamId(paramid);
					bean.setBaseVal(dj);
					bean.setCalcVal(jsz);
					bean.setWorkingHour(gzsj);
					bean.setBeginTime(kssj);
					bean.setEndTime(jzsj);
					paraml.add(bean);
				}
			}
		}

		if (this.iopsd.deleteSummaryParamData(pid)) {
			if (this.iopsd.deleteSummaryItemData(pid)) {
				if (this.iopsd.insertSummaryItemData(xml)) {
					if (this.iopsd.insertSummaryParamData(paraml)) {
						rtn = "";
					} else {
						rtn = "新增班组月报核算指标出现了错误";
					}
				} else {
					rtn = "新增班组月报项目数据出现了错误";
				}
			} else {
				rtn = "删除班组月报项目数据出现了错误";
			}
		} else {
			rtn = "删除班组月报核算指标出现了错误";
		}
		if (!"".equals(rtn)) {
			log.error("", rtn + "。计算信息：" + JSON.toJSONString(reportinfo));
		}
		return rtn;
	}

	/**
	 * @category 自动计算周报数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcWeekReportAuto(TeamReportInputDto reportinfo) {
		String rtn = "";
		String zs = reportinfo.getWriteDay();
		String unitid = reportinfo.getUnitId();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		String teamid = reportinfo.getTeamId();
		String kssj = reportinfo.getBegintime();
		String jzsj = reportinfo.getEndtime();
		this.init(reportinfo, jzsj, false);
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		HashMap<String, ProgramLibraryCostItem> fawzm = reportinfo.getFawzm();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		// 旧的备注内容
		CostSummaryInfo zbxx = this.getDeviceDayCalcInfo(zs, unitid, faid, teamid);
		String oremark = "";
		if (zbxx != null) {
			oremark = zbxx.getRemark();
		}
		CostSummaryInfo n = new CostSummaryInfo();
		String pid = TMUID.getUID();
		n.setId(pid);
		n.setUnitId(unitid);
		n.setTeamId(teamid);
		if (teamid == null) {
			teamid = "";
		}
		n.setReportNo(zs);
		n.setProgramId(faid);
		n.setRemark(oremark);
		n.setBeginTime(kssj);
		n.setEndTime(jzsj);
		List<CostSummaryItemData> xml = new ArrayList<CostSummaryItemData>();
		List<CostSummaryParamData> paraml = new ArrayList<CostSummaryParamData>();
		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), zs, kssj, jzsj);
		Double dj, xhl, jsz;
		String wzdm, paramid, wzmc, key, keyx, val;
		List<ItemConsumption> xmhl = this.gics.getUnitWeekItemConsumption(unitid, kssj, jzsj, faid, teamid, im);
		if (xmhl != null) {
			// 第一次循环将项目量添加到vm
			for (ItemConsumption x : xmhl) {
				wzdm = x.getItemId();
				key = new StringBuffer(unitid).append(".").append(wzdm).toString();
				xhl = x.getConsumption();
				if (xhl == null) {
					xhl = 0.0;
				}
				val = String.valueOf(xhl);
				keyx = new StringBuffer(key).append(".xhl").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".cl").toString();
				vm.put(keyx, val);
				vm.put(wzdm, val);
				dj = x.getItemPrice();
				if (dj == null) {
					dj = 0.0;
				}
				val = String.valueOf(dj);
				keyx = new StringBuffer(key).append(".khdj").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".bzdj").toString();
				vm.put(keyx, val);
				reportinfo.setGzsc(x.getWorkingHour());
			}
			// 第二次循环将项目添加到List<CostSummaryItemData>
			Double dcyn;
			for (ItemConsumption x : xmhl) {
				wzdm = x.getItemId();
				CostSummaryItemData b = new CostSummaryItemData();
				b.setId(TMUID.getUID());
				b.setPid(pid);
				b.setUnitId(unitid);
				b.setTeamId(teamid);
				b.setProgramId(faid);
				b.setReportType("WeekReport");
				b.setItemId(wzdm);
				dcyn = x.getItemPrice();
				if (dcyn != null) {
					b.setItemPrice(Maths.round(dcyn, 2));
				}
				b.setBeginTime(kssj);
				b.setEndTime(jzsj);
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				b.setWorkingHour(x.getWorkingHour());
				b.setWriteDay(zs);
				xhl = x.getConsumption();
				b.setConsumption(xhl);
				b.setWriteConsumption(xhl);
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setItemCost(jsz);
				xml.add(b);
			}
		}
		if (csm != null) {
			for (Entry<String, Costindicator> x : csm.entrySet()) {
				Costindicator param = x.getValue();
				if ("0".equals(faid)) {
					dj = param.getStandardval();
				} else {
					wzmc = param.getCpname();
					if (zbm.containsKey(wzmc)) {
						dj = zbm.get(wzmc).getBaseConsumption();
					} else {
						continue;
					}
				}
				paramid = param.getId();
				key = (new StringBuffer(paramid)).append(".param").toString();
				jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
				if ("0".equals(faid) && jsz.compareTo(0.0) == 0) {
					continue;// 汇总模式下，指标计算值是0的项目不保存
				}
				CostSummaryParamData bean = new CostSummaryParamData();
				bean.setId(TMUID.getUID());
				bean.setPid(pid);
				bean.setUnitId(unitid);
				bean.setTeamId(teamid);
				bean.setProgramId(faid);
				bean.setParamId(paramid);
				bean.setReportType("WeekReport");
				bean.setBaseVal(dj);
				bean.setCalcVal(jsz);
				bean.setWorkingHour(reportinfo.getGzsc());
				bean.setBeginTime(kssj);
				bean.setEndTime(jzsj);
				bean.setWriteDay(zs);
				paraml.add(bean);
			}
		}

		if (this.iopsd.delSumParam(unitid, zs, faid, teamid)) {
			if (this.iopsd.delSumItem(unitid, zs, faid, teamid)) {
				if (this.iopsd.delSumInfo(unitid, zs, faid, teamid)) {
					if (this.iopsd.insertSummaryCalcInfo(n)) {
						if (this.iopsd.insertSummaryItemData(xml)) {
							if (this.iopsd.insertSummaryParamData(paraml)) {
								rtn = "";
							} else {
								rtn = "新增周报核算指标出现了错误";
							}
						} else {
							rtn = "新增周报项目数据出现了错误";
						}
					} else {
						rtn = "新增周报信息出现了错误";
					}
				} else {
					rtn = "删除周报信息出现了错误";
				}
			} else {
				rtn = "删除周报项目数据出现了错误";
			}
		} else {
			rtn = "删除周报核算指标出现了错误";
		}
		if (!"".equals(rtn)) {
			log.error("", rtn + "。计算信息：" + JSON.toJSONString(reportinfo));
		}
		return rtn;
	}

	/**
	 * @category 计算周报数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcWeekReport(TeamReportInputDto reportinfo) {
		String rtn = "";
		String zs = reportinfo.getWriteDay();
		String unitid = reportinfo.getUnitId();
		String teamid = reportinfo.getTeamId();
		if (teamid == null) {
			teamid = "";
		}
		String kssj = reportinfo.getBegintime();
		String jzsj = reportinfo.getEndtime();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		this.init(reportinfo, jzsj, false);
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		HashMap<String, ProgramLibraryCostItem> fawzm = reportinfo.getFawzm();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		// 旧的备注内容
		CostSummaryInfo zbxx = this.getDeviceDayCalcInfo(zs, unitid, faid, teamid);
		String pid = "";
		if (zbxx != null) {
			pid = zbxx.getId();
		} else {
			log.error("", "保存装置周报时没有获取到制表信息。计算信息：" + JSON.toJSONString(reportinfo));
			return "保存装置周报时没有获取到制表信息";
		}
		List<CostSummaryParamData> paraml = new ArrayList<CostSummaryParamData>();
		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), zs, kssj, jzsj);
		Double dj, xhl, jsz;
		String wzdm, paramid, wzmc, key, keyx, val;
		List<CostSummaryItemData> xmhl = this.getDeviceDayItemData(pid);
		if (xmhl != null) {
			// 第一次循环将项目量添加到vm
			for (CostSummaryItemData x : xmhl) {
				wzdm = x.getItemId();
				key = new StringBuffer(unitid).append(".").append(wzdm).toString();
				xhl = x.getConsumption();
				if (xhl == null) {
					xhl = 0.0;
				}
				val = String.valueOf(xhl);
				keyx = new StringBuffer(key).append(".xhl").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".cl").toString();
				vm.put(keyx, val);
				vm.put(wzdm, val);
				dj = x.getItemPrice();
				if (dj == null) {
					dj = 0.0;
				}
				val = String.valueOf(dj);
				keyx = new StringBuffer(key).append(".khdj").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".bzdj").toString();
				vm.put(keyx, val);
				reportinfo.setGzsc(x.getWorkingHour());
			}
			// 第二次循环将项目添加到List<CostSummaryItemData>
			for (CostSummaryItemData b : xmhl) {
				wzdm = b.getItemId();
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setItemCost(jsz);
			}
		}
		if (csm != null) {
			for (Entry<String, Costindicator> x : csm.entrySet()) {
				Costindicator param = x.getValue();
				if ("0".equals(faid)) {
					dj = param.getStandardval();
				} else {
					wzmc = param.getCpname();
					if (zbm.containsKey(wzmc)) {
						dj = zbm.get(wzmc).getBaseConsumption();
					} else {
						continue;
					}
				}
				paramid = param.getId();
				key = (new StringBuffer(paramid)).append(".param").toString();
				jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
				if ("0".equals(faid) && jsz.compareTo(0.0) == 0) {
					continue;// 汇总模式下，指标计算值是0的项目不保存
				}
				CostSummaryParamData bean = new CostSummaryParamData();
				bean.setId(TMUID.getUID());
				bean.setPid(pid);
				bean.setUnitId(unitid);
				bean.setTeamId(teamid);
				bean.setProgramId(faid);
				bean.setParamId(paramid);
				bean.setReportType("WeekReport");
				bean.setBaseVal(dj);
				bean.setCalcVal(jsz);
				bean.setWorkingHour(reportinfo.getGzsc());
				bean.setWriteDay(zs);
				bean.setBeginTime(kssj);
				bean.setEndTime(jzsj);
				paraml.add(bean);
			}
		}

		if (this.iopsd.delSumParam(unitid, zs, faid, teamid)) {
			if (this.updateDeviceDayItemData(xmhl)) {
				if (this.iopsd.insertSummaryParamData(paraml)) {
					rtn = "";
				} else {
					rtn = "新增装置月报核算指标出现了错误";
				}
			} else {
				rtn = "更新装置月报项目数据出现了错误";
			}
		} else {
			rtn = "删除装置月报核算指标出现了错误";
		}
		if (!"".equals(rtn)) {
			log.error("", rtn + "。计算信息：" + JSON.toJSONString(reportinfo));
		}
		return rtn;
	}

	/**
	 * @category 自动计算季报数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcQuarterReportAuto(TeamReportInputDto reportinfo) {
		String rtn = "";
		return rtn;
	}

	/**
	 * @category 计算季报数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcQuarterReport(TeamReportInputDto reportinfo) {
		String rtn = "";
		return rtn;
	}

	/**
	 * @category 自动计算年报数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcYearReportAuto(TeamReportInputDto reportinfo) {
		String rtn = "";
		return rtn;
	}

	/**
	 * @category 计算年报数据
	 * @param reportinfo
	 * @return
	 */
	@Override
	public String calcYearReport(TeamReportInputDto reportinfo) {
		String rtn = "";
		return rtn;
	}

	@Override
	public String calcPeriodReport(TeamReportInputDto reportinfo) {
		String rtn = "";
		// 制表信息
		String oid = reportinfo.getPid();
		if (oid == null || "".equals(oid)) {
			rtn = "未正确传入制表信息的ID";
			log.error("", rtn + "。计算信息：" + JSON.toJSONString(reportinfo));
			return rtn;
		}
		String bh = reportinfo.getWriteDay();
		String unitid = reportinfo.getUnitId();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		String teamid = reportinfo.getTeamId();
		if (teamid == null) {
			teamid = "";
		}
		String kssj = reportinfo.getBegintime();
		String jzsj = reportinfo.getEndtime();
		this.init(reportinfo, jzsj, false);
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		HashMap<String, ProgramLibraryCostItem> fawzm = reportinfo.getFawzm();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		List<CostSummaryItemData> xml = new ArrayList<CostSummaryItemData>();
		List<CostSummaryParamData> paraml = new ArrayList<CostSummaryParamData>();
		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), bh, kssj, jzsj);
		Double dj, xhl, jsz;
		String wzdm, paramid, wzmc, key, keyx, val;
		List<ItemConsumption> xmhl = this.gics.getUnitPeriodItemConsumption(unitid, kssj, jzsj, faid, teamid, im);
		if (xmhl != null) {
			// 第一次循环将项目量添加到vm
			for (ItemConsumption x : xmhl) {
				wzdm = x.getItemId();
				key = new StringBuffer(unitid).append(".").append(wzdm).toString();
				xhl = x.getConsumption();
				if (xhl == null) {
					xhl = 0.0;
				}
				val = String.valueOf(xhl);
				keyx = new StringBuffer(key).append(".xhl").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".cl").toString();
				vm.put(keyx, val);
				vm.put(wzdm, val);
				dj = x.getItemPrice();
				if (dj == null) {
					dj = 0.0;
				}
				val = String.valueOf(Maths.round(dj, 2));
				keyx = new StringBuffer(key).append(".khdj").toString();
				vm.put(keyx, val);
				keyx = new StringBuffer(key).append(".bzdj").toString();
				vm.put(keyx, val);
				reportinfo.setGzsc(x.getWorkingHour());
			}
			// 第二次循环将项目添加到List<CostSummaryItemData>
			Double dcyn;
			for (ItemConsumption x : xmhl) {
				wzdm = x.getItemId();
				CostSummaryItemData b = new CostSummaryItemData();
				b.setId(TMUID.getUID());
				b.setPid(oid);
				b.setUnitId(unitid);
				b.setTeamId(teamid);
				b.setProgramId(faid);
				b.setReportType("PeriodReport");
				b.setItemId(wzdm);
				dcyn = x.getItemPrice();
				if (dcyn != null) {
					b.setItemPrice(Maths.round(dcyn, 2));
				}
				b.setBeginTime(kssj);
				b.setEndTime(jzsj);
				if (im.containsKey(wzdm)) {
					Costitem wz = im.get(wzdm);
					wzmc = wz.getItemname();
					b.setBaseUnitConsumption(wz.getBaseConsumption());
					if (!"0".equals(faid)) {
						// 使用了方案，这时取方案内的标准单耗
						if (fawzm != null && fawzm.containsKey(wzdm)) {
							b.setBaseUnitConsumption(fawzm.get(wzdm).getBaseConsumption());
						}
					}
				} else {
					wzmc = "";
				}
				b.setWorkingHour(x.getWorkingHour());
				b.setWriteDay(bh);
				xhl = x.getConsumption();
				b.setConsumption(xhl);
				b.setWriteConsumption(xhl);
				// 考核消耗量
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseConsumption(jsz);
				// 考核总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setBaseCost(jsz);
				// 单耗
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setUnitConsumption(jsz);
				// 单位成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm,
						wzmc, "", "", "");
				b.setUnitCost(jsz);
				// 总成本
				jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
						"", "", "");
				b.setItemCost(jsz);
				xml.add(b);
			}
		}
		if (csm != null) {
			for (Entry<String, Costindicator> x : csm.entrySet()) {
				Costindicator param = x.getValue();
				if ("0".equals(faid)) {
					dj = param.getStandardval();
				} else {
					wzmc = param.getCpname();
					if (zbm.containsKey(wzmc)) {
						dj = zbm.get(wzmc).getBaseConsumption();
					} else {
						continue;
					}
				}
				paramid = param.getId();
				key = (new StringBuffer(paramid)).append(".param").toString();
				jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
				if ("0".equals(faid) && jsz.compareTo(0.0) == 0) {
					continue;// 汇总模式下，指标计算值是0的项目不保存
				}
				CostSummaryParamData bean = new CostSummaryParamData();
				bean.setId(TMUID.getUID());
				bean.setPid(oid);
				bean.setUnitId(unitid);
				bean.setTeamId(teamid);
				bean.setProgramId(faid);
				bean.setReportType("PeriodReport");
				bean.setParamId(paramid);
				bean.setBaseVal(dj);
				bean.setCalcVal(jsz);
				bean.setWorkingHour(reportinfo.getGzsc());
				bean.setBeginTime(kssj);
				bean.setEndTime(jzsj);
				bean.setWriteDay(bh);
				paraml.add(bean);
			}
		}

		if (this.iopsd.deleteSummaryParamData(oid)) {
			if (this.iopsd.deleteSummaryItemData(oid)) {
				if (this.iopsd.insertSummaryItemData(xml)) {
					if (this.iopsd.insertSummaryParamData(paraml)) {
						rtn = "";
					} else {
						rtn = "新增分时段报表核算指标出现了错误";
					}
				} else {
					rtn = "新增分时段报表项目数据出现了错误";
				}
			} else {
				rtn = "删除分时段报表项目数据出现了错误";
			}
		} else {
			rtn = "删除分时段报表核算指标出现了错误";
		}
		if (!"".equals(rtn)) {
			log.error("", rtn + "。计算信息：" + JSON.toJSONString(reportinfo));
		}
		return rtn;
	}

	@Override
	public String calcBatchProgramData(TeamReportInputDto reportinfo) {
		String rtn = "";
		String unitid = reportinfo.getUnitId();
		String tbrq = reportinfo.getWriteDay();
		String teamid = reportinfo.getTeamId();
		String shiftid = reportinfo.getShiftId();
		// 重新计算
		List<CostBatchOnDuty> dl = this.iopsd.getBatchOnDuty(unitid, shiftid, teamid, tbrq);
		if (dl != null) {
			int count = dl.size();
			if (count > 0) {
				Double gzsj = 0.0;
				String pid, faid, fakssj, fajzsj, did, bz;
				String tjrq = dl.get(0).getSummaryDay();
				String sbsj = dl.get(0).getShiftBegintime();
				String xbsj = dl.get(0).getShiftEndtime();
				// 初始化计算信息
				CalcTDSParam ctsp = new CalcTDSParam("", tbrq.substring(0, 7), sbsj, xbsj);
				this.init(reportinfo, unitid, tbrq);// 初始化核算对象内成本设置
				// 班组数据
				HashMap<String, TeamProgramDto> tpm = new HashMap<String, TeamProgramDto>();
				HashMap<String, List<CostBatchInstrumentData>> tybbsm = new HashMap<String, List<CostBatchInstrumentData>>();
				HashMap<String, List<CostBatchItemData>> tobjm = new HashMap<String, List<CostBatchItemData>>();
				HashMap<String, List<CostBatchParamData>> tparamm = new HashMap<String, List<CostBatchParamData>>();
				// 方案设置
				HashMap<String, HashMap<String, ProgramLibraryCostItem>> faqywzm = new HashMap<String, HashMap<String, ProgramLibraryCostItem>>();// 启用的物资
				HashMap<String, HashMap<String, ProgramLibraryCostItem>> faqyzbm = new HashMap<String, HashMap<String, ProgramLibraryCostItem>>();// 启用的指标
				for (int i = 0; count > i; i++) {
					CostBatchOnDuty d = dl.get(i);
					did = d.getId();
					faid = d.getProgramId();
					if (StringUtils.isEmpty(faid)) {
						faid = "0";
					}
					fakssj = d.getBeginTime();
					fajzsj = d.getEndTime();
					if (fakssj == null || fajzsj == null) {
						continue;
					}
					pid = TMUID.getUID();
					gzsj = DateTimeUtils.diffSecond(DateTimeUtils.parseDateTime(fakssj),
							DateTimeUtils.parseDateTime(fajzsj)) / 3600.00;
					if (tpm.containsKey(faid)) {
						TeamProgramDto tp = tpm.get(faid);
						tp.setShiftEndTime(fajzsj);
						tp.setGzsc(tp.getGzsc() + gzsj);
					} else {
						TeamProgramDto tp = new TeamProgramDto();
						tp.setShiftBeginTime(fakssj);
						tp.setShiftEndTime(fajzsj);
						tp.setGzsc(gzsj);
						tpm.put(faid, tp);
					}
					// 已保存的仪表数据
					List<CostBatchInstrumentData> ybbs = this.getBatchInstrumentData(did);
					// 计算批次数据
					if (!"0".equals(faid)) {
						List<ProgramLibraryCostItem> plcil = this.unitItemInfoService.getProgramItem(tbrq, faid);
						HashMap<String, ProgramLibraryCostItem> qywzm = new HashMap<String, ProgramLibraryCostItem>();// 启用的物资
						HashMap<String, ProgramLibraryCostItem> qyzbm = new HashMap<String, ProgramLibraryCostItem>();// 启用的指标
						if (plcil != null) {
							Integer dtype;
							int pic = plcil.size();
							for (int pi = 0; pic > pi; pi++) {
								ProgramLibraryCostItem x = plcil.get(pi);
								dtype = x.getDataType();
								if (dtype == null || dtype == 0) {
									// 项目
									qywzm.put(x.getItemId(), x);
								} else {
									// 指标
									qyzbm.put(x.getName(), x);
								}
							}
							faqywzm.put(faid, qywzm);
							faqyzbm.put(faid, qyzbm);
						}
					}
					// 批次计算信息
					HashMap<String, String> vm = reportinfo.getValm();
					if (vm == null) {
						vm = new HashMap<String, String>();
						reportinfo.setValm(vm);
					}
					vm.clear();// 每个批次计算前初始化
					reportinfo.setGzsc(gzsj);
					this.calcYbbs(ybbs, reportinfo, unitid, tbrq, gzsj, fakssj, fajzsj, ctsp, vm);
					if (tybbsm.containsKey(faid)) {
						tybbsm.get(faid).addAll(ybbs);
					} else {
						tybbsm.put(faid, ybbs);
					}
					List<CostBatchItemData> objl = this.newBatchObj(unitid, pid, ybbs, reportinfo, fakssj, fajzsj, faid,
							tbrq, gzsj, faqywzm, ctsp, vm);
					if (!tobjm.containsKey(faid)) {
						tobjm.put(faid, objl);
					}
					List<CostBatchParamData> pl = this.newBatchParam(unitid, pid, reportinfo, fakssj, fajzsj, faid,
							tbrq, gzsj, faqyzbm, ctsp);
					if (!tparamm.containsKey(faid)) {
						tparamm.put(faid, pl);
					}
				}
				// 班次的方案计算
				HashMap<String, CostTeamInfo> tim = this.getOldTeamInfo(unitid, shiftid, teamid, tbrq);
				List<CostTeamInfo> til = new ArrayList<CostTeamInfo>();
				List<CostTeamInstrumentData> tybl = new ArrayList<CostTeamInstrumentData>();
				List<CostTeamItemData> txml = new ArrayList<CostTeamItemData>();
				List<CostTeamParamData> tzbl = new ArrayList<CostTeamParamData>();
				if (tpm != null && tpm.size() > 0) {
					for (Entry<String, TeamProgramDto> t : tpm.entrySet()) {
						faid = t.getKey();
						TeamProgramDto to = t.getValue();
						fakssj = to.getShiftBeginTime();
						fajzsj = to.getShiftEndTime();
						gzsj = to.getGzsc();
						if (tim.containsKey(faid)) {
							CostTeamInfo ot = tim.get(faid);
							pid = ot.getId();
							bz = ot.getRemark();
						} else {
							pid = TMUID.getUID();
							bz = "";
						}
						// 计算信息
						HashMap<String, String> vm = reportinfo.getValm();
						if (vm == null) {
							vm = new HashMap<String, String>();
							reportinfo.setValm(vm);
						}
						vm.clear();// 每个批次计算前初始化
						// 班次方案信息
						til.add(this.newTeamInfo(pid, fakssj, fajzsj, faid, unitid, tbrq, tjrq, teamid, shiftid, bz));
						if (tybbsm.containsKey(faid)) {
							tybl.addAll(this.newTeamYbbs(unitid, pid, sbsj, xbsj, faid, tbrq, teamid, shiftid,
									tybbsm.get(faid), vm));
						}
						if (tobjm.containsKey(faid)) {
							txml.addAll(this.newTeamObj(unitid, pid, teamid, shiftid, tobjm.get(faid), reportinfo,
									fakssj, fajzsj, faid, tbrq, tjrq, gzsj, ctsp, vm));
						}
						if (tparamm.containsKey(faid)) {
							tzbl.addAll(this.newTeamParam(unitid, pid, teamid, shiftid, reportinfo, fakssj, fajzsj,
									faid, tjrq, gzsj, tparamm.get(faid), ctsp));
						}
					}
					// 计算汇总
					if (!tpm.containsKey("0")) {
						// 已计算的内容不含汇总
						HashMap<String, String> vm = reportinfo.getValm();
						if (vm == null) {
							vm = new HashMap<String, String>();
							reportinfo.setValm(vm);
						}
						vm.clear();// 每个批次计算前初始化
						if (tim.containsKey("0")) {
							CostTeamInfo ot = tim.get("0");
							pid = ot.getId();
							bz = ot.getRemark();
						} else {
							pid = TMUID.getUID();
							bz = "";
						}
						gzsj = DateTimeUtils.diffSecond(DateTimeUtils.parseDateTime(sbsj),
								DateTimeUtils.parseDateTime(xbsj)) / 3600.00;
						reportinfo.setGzsc(gzsj);
						til.add(this.newTeamInfo(pid, sbsj, xbsj, "0", unitid, tbrq, tjrq, teamid, shiftid, bz));
						tybl.addAll(this.newTeamYbbs(unitid, pid, sbsj, xbsj, tbrq, teamid, shiftid, tybbsm, vm));
						txml.addAll(this.newTeamObj(unitid, pid, teamid, shiftid, txml, reportinfo, sbsj, xbsj, tbrq,
								tjrq, gzsj, ctsp, vm));
						tzbl.addAll(this.newTeamParam(unitid, pid, teamid, shiftid, reportinfo, sbsj, xbsj, tjrq, gzsj,
								ctsp));
					}
					// 保存班组数据
					if (this.iopsd.insertTeamInfo(til)) {
						if (this.iopsd.insertTeamDayYbData(tybl)) {
							if (this.iopsd.insertTeamDayItemData(txml)) {
								if (this.iopsd.insertTeamDayParamData(tzbl)) {

								}
							}
						}
					}
					// 日报、周报的计算任务
					Boolean yn = reportinfo.getIsBatchCalc();
					if (yn == null) {
						yn = false;
					}
					if (yn == false) {
						this.saveTask(reportinfo);// 批量计算不生成计算任务
					}

				}
			}
		}
		return rtn;
	}

	private void calcYbbs(List<CostBatchInstrumentData> ybbs, TeamReportInputDto reportinfo, String unitid, String rq,
			Double gzsj, String kssj, String jzsj, CalcTDSParam ctsp, HashMap<String, String> vm) {
		HashMap<String, Costinstrument> ym = reportinfo.getYbm();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		Double val, jsz;
		String xmid, ybid, key, keyx, wzmc, ybmc;
		if (ybbs != null) {
			for (CostBatchInstrumentData x : ybbs) {
				ybid = x.getInstrumentId();// 仪表ID
				if (ym.containsKey(ybid)) {
					Costinstrument yb = ym.get(ybid);
					if (yb != null) {
						key = new StringBuffer(unitid).append(".").append(ybid).toString();
						// 前表数
						keyx = new StringBuffer(key).append(".qbs").toString();
						val = x.getPreviousReadOut();
						if (val == null) {
							val = 0.0;
						}
						vm.put(keyx, String.valueOf(val));
						// 后表数
						keyx = new StringBuffer(key).append(".hbs").toString();
						val = x.getLastReadOut();
						if (val == null) {
							val = 0.0;
						}
						vm.put(keyx, String.valueOf(val));
					}
				}
			}
			for (CostBatchInstrumentData x : ybbs) {
				ybid = x.getInstrumentId();// 仪表ID
				ybmc = "";
				wzmc = "";
				xmid = "";
				if (ym.containsKey(ybid)) {
					Costinstrument yb = ym.get(ybid);
					if (yb != null) {
						xmid = yb.getPid();// 项目ID
						ybmc = yb.getName();
						wzmc = this.getItemName(xmid, im);
					}
				}
				key = new StringBuffer(ybid).append(".dbxh").toString();
				// 找到公式，需要计算单表消耗
				jsz = this.calcformula(key, reportinfo, ctsp, xmid, wzmc, ybid, ybmc, "");
				x.setCalcVal(jsz);
				x.setWriteVal(jsz);
			}
			this.iopsd.updateBatchYbbs(ybbs);
		}
	}

	/**
	 * @category 根据仪表表数得到项目
	 */
	private List<CostBatchItemData> newBatchObj(String unitid, String pid, List<CostBatchInstrumentData> ybbs,
			TeamReportInputDto reportinfo, String kssj, String jzsj, String faid, String rq, Double gzsj,
			HashMap<String, HashMap<String, ProgramLibraryCostItem>> faqywzm, CalcTDSParam ctsp,
			HashMap<String, String> vm) {
		List<CostBatchItemData> rtn = new ArrayList<CostBatchItemData>();
		Double khdh;
		String ybid, xmid;
		if (ybbs != null) {
			HashMap<String, ProgramLibraryCostItem> qywzm = null;
			if (faqywzm.containsKey(faid)) {
				qywzm = faqywzm.get(faid);// 从方案得到单耗
			}
			HashMap<String, Costitem> im = reportinfo.getItemm();
			HashMap<String, Costinstrument> ym = reportinfo.getYbm();
			HashMap<String, String> om = new HashMap<String, String>();
			for (CostBatchInstrumentData y : ybbs) {
				ybid = y.getInstrumentId();
				if (ym.containsKey(ybid)) {
					Costinstrument yb = ym.get(ybid);
					xmid = yb.getPid();
					if (im.containsKey(xmid)) {
						if (!om.containsKey(xmid)) {
							Costitem ci = im.get(xmid);
							khdh = ci.getBaseConsumption();
							if (qywzm != null) {
								if (qywzm.containsKey(xmid)) {
									ProgramLibraryCostItem pp = qywzm.get(xmid);
									khdh = pp.getBaseConsumption();
								}
							}
							om.put(xmid, "1");
							CostBatchItemData b = new CostBatchItemData();
							b.setId(TMUID.getUID());
							b.setPid(pid);
							b.setBeginTime(kssj);
							b.setEndTime(jzsj);
							b.setProgramId(faid);
							b.setUnitId(unitid);
							b.setWriteDay(rq);
							b.setWorkingHour(gzsj);
							b.setItemId(xmid);
							b.setBaseUnitConsumption(khdh);
							b.setItemprice(ci.getItemprice());
							rtn.add(b);
						}
					}
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 计算核算参数
	 */
	private List<CostBatchParamData> newBatchParam(String unitid, String pid, TeamReportInputDto reportinfo,
			String kssj, String jzsj, String faid, String rq, Double gzsj,
			HashMap<String, HashMap<String, ProgramLibraryCostItem>> faqyzbm, CalcTDSParam ctsp) {
		Integer sy;
		String paramid, parammc;
		Double sv;
		HashMap<String, ProgramLibraryCostItem> qyzbm = null;
		if (faqyzbm.containsKey(faid)) {
			qyzbm = faqyzbm.get(faid);
		}
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		List<CostBatchParamData> pccsl = new ArrayList<CostBatchParamData>();
		for (Entry<String, Costindicator> x : csm.entrySet()) {
			Costindicator param = x.getValue();
			paramid = param.getId();
			parammc = param.getCpname();
			sv = param.getStandardval();// 默认使用指标设置的标准值
			// 核算指标启用（用名称与方案对照）
			sy = 1;// 默认启用
			if (qyzbm != null) {
				if (qyzbm.containsKey(parammc)) {
					ProgramLibraryCostItem plci = qyzbm.get(parammc);
					if (plci != null) {
						sy = plci.getTmUsed();
						if (sy == null) {
							sy = 1;
						}
						sv = plci.getBaseConsumption();// 核定量
					}
				}
			}
			if (sy == 1) {
				CostBatchParamData bean = new CostBatchParamData();
				bean.setId(TMUID.getUID());
				bean.setPid(pid);
				bean.setUnitId(unitid);
				bean.setProgramId(faid);
				bean.setParamId(paramid);
				bean.setBaseVal(sv);
				bean.setWorkingHour(gzsj);
				bean.setBeginTime(kssj);
				bean.setEndTime(jzsj);
				bean.setWriteDay(rq);
				pccsl.add(bean);
			}
		}
		return pccsl;
	}

	/**
	 * @category 班次方案计算信息
	 */
	private CostTeamInfo newTeamInfo(String pid, String fakssj, String fajzsj, String faid, String unitid, String tbrq,
			String tjrq, String teamid, String shiftid, String bz) {
		CostTeamInfo b = new CostTeamInfo();
		b.setId(pid);
		b.setBeginTime(fakssj);
		b.setEndTime(fajzsj);
		b.setProgramId(faid);
		b.setUnitId(unitid);
		b.setWriteDay(tbrq);
		b.setTeamId(teamid);
		b.setShiftId(shiftid);
		b.setSummaryDay(tjrq);
		b.setRemark(bz);
		return b;
	}

	/**
	 * @category 班次方案仪表
	 */
	private List<CostTeamInstrumentData> newTeamYbbs(String unitid, String pid, String sbsj, String xbsj, String faid,
			String tbrq, String teamid, String shiftid, List<CostBatchInstrumentData> ybbs,
			HashMap<String, String> vm) {
		List<CostTeamInstrumentData> rtn = new ArrayList<CostTeamInstrumentData>();
		if (ybbs != null) {
			Double dval, ybl;
			String sval, ybid, keyx;
			HashMap<String, Double> zcm = new HashMap<String, Double>();
			for (CostBatchInstrumentData y : ybbs) {
				ybid = y.getInstrumentId();
				dval = y.getCalcVal();
				ybl = y.getWriteVal();
				if (ybl == null) {
					ybl = 0.0;
				}
				if (zcm.containsKey(ybid)) {// 仪表量直接加
					zcm.put(ybid, zcm.get(ybid) + ybl);
				} else {
					zcm.put(ybid, ybl);
				}
				CostTeamInstrumentData ti = new CostTeamInstrumentData();
				ti.setId(TMUID.getUID());
				ti.setPid(pid);
				ti.setUnitId(unitid);
				ti.setWriteDay(tbrq);
				ti.setProgramId(faid);
				ti.setTeamId(teamid);
				ti.setShiftId(shiftid);
				ti.setShiftBeginTime(sbsj);
				ti.setShiftEndTime(xbsj);
				ti.setInstrumentId(ybid);
				ti.setCalcVal(dval);
				ti.setWriteVal(ybl);
				ti.setPreviousReadOut(y.getPreviousReadOut());
				ti.setPreviousReadTime(y.getPreviousReadTime());
				ti.setLastReadOut(y.getLastReadOut());
				ti.setLastReadTime(y.getLastReadTime());
				rtn.add(ti);
			}
			for (Entry<String, Double> tt : zcm.entrySet()) {
				ybid = tt.getKey();
				dval = tt.getValue();
				if (dval == null) {
					sval = "0";
				} else {
					sval = String.valueOf(dval);
				}
				keyx = (new StringBuffer(unitid).append(".").append(ybid).append(".dbxhl")).toString();
				vm.put(keyx, sval);
				keyx = (new StringBuffer(ybid).append(".dbxhl")).toString();
				vm.put(keyx, sval);
			}
		}
		return rtn;
	}

	/**
	 * @category 班次仪表
	 */
	private List<CostTeamInstrumentData> newTeamYbbs(String unitid, String pid, String sbsj, String xbsj, String tbrq,
			String teamid, String shiftid, HashMap<String, List<CostBatchInstrumentData>> tybbsm,
			HashMap<String, String> vm) {
		List<CostTeamInstrumentData> rtn = new ArrayList<CostTeamInstrumentData>();
		if (tybbsm != null) {
			Double dval, ybl;
			String sval, ybid, keyx;
			HashMap<String, Double> zcm = new HashMap<String, Double>();
			for (Entry<String, List<CostBatchInstrumentData>> yn : tybbsm.entrySet()) {
				List<CostBatchInstrumentData> ybbs = yn.getValue();
				if (ybbs != null) {
					for (CostBatchInstrumentData y : ybbs) {
						ybid = y.getInstrumentId();
						dval = y.getCalcVal();
						ybl = y.getWriteVal();
						if (ybl == null) {
							ybl = 0.0;
						}
						if (zcm.containsKey(ybid)) {// 仪表量直接加
							zcm.put(ybid, zcm.get(ybid) + ybl);
						} else {
							zcm.put(ybid, ybl);
						}
						CostTeamInstrumentData ti = new CostTeamInstrumentData();
						ti.setId(TMUID.getUID());
						ti.setPid(pid);
						ti.setUnitId(unitid);
						ti.setWriteDay(tbrq);
						ti.setProgramId("0");
						ti.setTeamId(teamid);
						ti.setShiftId(shiftid);
						ti.setShiftBeginTime(sbsj);
						ti.setShiftEndTime(xbsj);
						ti.setInstrumentId(ybid);
						ti.setCalcVal(dval);
						ti.setWriteVal(ybl);
						ti.setPreviousReadOut(y.getPreviousReadOut());
						ti.setPreviousReadTime(y.getPreviousReadTime());
						ti.setLastReadOut(y.getLastReadOut());
						ti.setLastReadTime(y.getLastReadTime());
						rtn.add(ti);
					}
				}
			}
			for (Entry<String, Double> tt : zcm.entrySet()) {
				ybid = tt.getKey();
				Double t = tt.getValue();
				if (t == null) {
					sval = "0";
				} else {
					sval = String.valueOf(t);
				}
				keyx = (new StringBuffer(unitid).append(".").append(ybid).append(".dbxhl")).toString();
				vm.put(keyx, sval);
				keyx = (new StringBuffer(ybid).append(".dbxhl")).toString();
				vm.put(keyx, sval);
			}
		}
		return rtn;
	}

	/**
	 * @category 根据仪表表数得到项目
	 * @param unitid
	 * @param pid
	 * @param ybbs
	 * @param reportinfo
	 * @return
	 */
	private List<CostTeamItemData> newTeamObj(String unitid, String pid, String teamid, String shiftid,
			List<CostBatchItemData> objl, TeamReportInputDto reportinfo, String kssj, String jzsj, String faid,
			String tbrq, String tjrq, Double gzsj, CalcTDSParam ctsp, HashMap<String, String> vm) {
		List<CostTeamItemData> rtn = new ArrayList<CostTeamItemData>();
		Double dval;
		String sval, xmid, wzmc = "";
		if (objl != null) {
			for (CostBatchItemData y : objl) {
				xmid = y.getItemId();
				CostTeamItemData b = new CostTeamItemData();
				b.setId(TMUID.getUID());
				b.setPid(pid);
				b.setUnitId(unitid);
				b.setWriteDay(tbrq);
				b.setProgramId(faid);
				b.setTeamId(teamid);
				b.setShiftId(shiftid);
				b.setSummaryDay(tjrq);
				b.setItemId(xmid);
				b.setItemPrice(y.getItemprice());
				b.setBaseUnitConsumption(y.getBaseUnitConsumption());
				b.setWorkingHour(gzsj);
				b.setBeginTime(kssj);
				b.setEndTime(jzsj);
				dval = this.calcformula((new StringBuffer(xmid).append(".hxl")).toString(), reportinfo, ctsp, xmid,
						wzmc, "", "", "");
				sval = String.valueOf(dval);
				vm.put(unitid + "." + xmid + ".xhl", sval);
				vm.put(unitid + "." + xmid + ".cl", sval);
				vm.put(xmid, sval);
				b.setConsumption(dval);
				b.setWriteConsumption(dval);
				rtn.add(b);
			}
		}
		if (rtn != null) {
			for (CostTeamItemData m : rtn) {
				xmid = m.getItemId();
				// 考核消耗量
				dval = this.calcformula((new StringBuffer(xmid).append(".khxhl")).toString(), reportinfo, ctsp, xmid,
						wzmc, "", "", "");
				m.setBaseConsumption(dval);
				// 考核总成本
				dval = this.calcformula((new StringBuffer(xmid).append(".khzcb")).toString(), reportinfo, ctsp, xmid,
						wzmc, "", "", "");
				m.setBaseCost(dval);
				// 单耗
				dval = this.calcformula((new StringBuffer(xmid).append(".dh")).toString(), reportinfo, ctsp, xmid, wzmc,
						"", "", "");
				m.setUnitConsumption(dval);
				// 单位成本
				dval = this.calcformula((new StringBuffer(xmid).append(".dwcb")).toString(), reportinfo, ctsp, xmid,
						wzmc, "", "", "");
				m.setUnitCost(dval);
				// 总成本
				dval = this.calcformula((new StringBuffer(xmid).append(".zcb")).toString(), reportinfo, ctsp, xmid,
						wzmc, "", "", "");
				m.setItemCost(dval);
			}
		}
		return rtn;
	}

	private List<CostTeamItemData> newTeamObj(String unitid, String pid, String teamid, String shiftid,
			List<CostTeamItemData> objl, TeamReportInputDto reportinfo, String kssj, String jzsj, String tbrq,
			String tjrq, Double gzsj, CalcTDSParam ctsp, HashMap<String, String> vm) {
		List<CostTeamItemData> rtn = new ArrayList<CostTeamItemData>();
		Double dval, jsz, khdh;
		String sval, xmid, wzmc = "";
		HashMap<String, Costitem> im = reportinfo.getItemm();
		if (im == null) {
			im = new HashMap<String, Costitem>();
		}
		HashMap<String, CostTeamItemData> xmm = new HashMap<String, CostTeamItemData>();
		if (objl != null) {
			for (CostTeamItemData y : objl) {
				xmid = y.getItemId();
				if (im.containsKey(xmid)) {
					khdh = im.get(xmid).getBaseConsumption();// 汇总状态只使用项目的单耗定额
				} else {
					khdh = null;
				}
				dval = y.getConsumption();
				if (dval == null) {
					dval = 0.0;
				}
				jsz = y.getWriteConsumption();
				if (jsz == null) {
					jsz = 0.0;
				}
				if (xmm.containsKey(xmid)) {
					CostTeamItemData c = xmm.get(xmid);
					c.setConsumption(c.getConsumption() + dval);
					c.setWriteConsumption(c.getWriteConsumption() + jsz);
				} else {
					CostTeamItemData b = new CostTeamItemData();
					b.setId(TMUID.getUID());
					b.setPid(pid);
					b.setUnitId(unitid);
					b.setWriteDay(tbrq);
					b.setProgramId("0");
					b.setTeamId(teamid);
					b.setShiftId(shiftid);
					b.setSummaryDay(tjrq);
					b.setItemId(xmid);
					b.setItemPrice(y.getItemPrice());
					b.setBaseUnitConsumption(khdh);
					b.setWorkingHour(gzsj);
					b.setBeginTime(kssj);
					b.setEndTime(jzsj);
					b.setConsumption(dval);
					b.setWriteConsumption(jsz);
					xmm.put(xmid, b);
				}
			}
		}

		for (Entry<String, CostTeamItemData> nyc : xmm.entrySet()) {
			xmid = nyc.getKey();
			CostTeamItemData yc = nyc.getValue();
			dval = yc.getWriteConsumption();
			sval = String.valueOf(dval);
			vm.put(unitid + "." + xmid + ".xhl", sval);
			vm.put(unitid + "." + xmid + ".cl", sval);
			vm.put(xmid, sval);
			rtn.add(yc);
		}
		if (rtn != null) {
			for (CostTeamItemData m : rtn) {
				xmid = m.getItemId();
				// 考核消耗量
				dval = this.calcformula((new StringBuffer(xmid).append(".khxhl")).toString(), reportinfo, ctsp, xmid,
						wzmc, "", "", "");
				m.setBaseConsumption(dval);
				// 考核总成本
				dval = this.calcformula((new StringBuffer(xmid).append(".khzcb")).toString(), reportinfo, ctsp, xmid,
						wzmc, "", "", "");
				m.setBaseCost(dval);
				// 单耗
				dval = this.calcformula((new StringBuffer(xmid).append(".dh")).toString(), reportinfo, ctsp, xmid, wzmc,
						"", "", "");
				m.setUnitConsumption(dval);
				// 单位成本
				dval = this.calcformula((new StringBuffer(xmid).append(".dwcb")).toString(), reportinfo, ctsp, xmid,
						wzmc, "", "", "");
				m.setUnitCost(dval);
				// 总成本
				dval = this.calcformula((new StringBuffer(xmid).append(".zcb")).toString(), reportinfo, ctsp, xmid,
						wzmc, "", "", "");
				m.setItemCost(dval);
			}
		}
		return rtn;
	}

	/**
	 * @category 计算核算参数
	 * @return
	 */

	private List<CostTeamParamData> newTeamParam(String unitid, String pid, String teamid, String shiftid,
			TeamReportInputDto reportinfo, String kssj, String jzsj, String faid, String rq, Double gzsj,
			List<CostBatchParamData> pl, CalcTDSParam ctsp) {
		String key, paramid;
		Double jsz;
		List<CostTeamParamData> pccsl = new ArrayList<CostTeamParamData>();
		for (CostBatchParamData x : pl) {
			paramid = x.getParamId();
			key = (new StringBuffer(paramid)).append(".param").toString();
			CostTeamParamData bean = new CostTeamParamData();
			bean.setId(TMUID.getUID());
			bean.setPid(pid);
			bean.setUnitId(unitid);
			bean.setWriteDay(rq);
			bean.setProgramId(faid);
			bean.setTeamId(teamid);
			bean.setShiftId(shiftid);
			bean.setParamId(paramid);
			bean.setBaseVal(x.getBaseVal());
			bean.setWorkingHour(gzsj);
			bean.setBeginTime(kssj);
			bean.setEndTime(jzsj);
			jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
			bean.setCalcVal(jsz);
			pccsl.add(bean);
		}
		return pccsl;
	}

	/**
	 * @category 计算核算参数
	 */
	private List<CostTeamParamData> newTeamParam(String unitid, String pid, String teamid, String shiftid,
			TeamReportInputDto reportinfo, String sbsj, String xbsj, String rq, Double gzsj, CalcTDSParam ctsp) {
		String paramid, key;
		Double sv, jsz;
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		List<CostTeamParamData> pccsl = new ArrayList<CostTeamParamData>();
		for (Entry<String, Costindicator> x : csm.entrySet()) {
			Costindicator param = x.getValue();
			paramid = param.getId();
			sv = param.getStandardval();// 默认使用指标设置的标准值
			key = (new StringBuffer(paramid)).append(".param").toString();
			jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
			if (jsz.compareTo(0.0) != 0) {
				CostTeamParamData bean = new CostTeamParamData();
				bean.setId(TMUID.getUID());
				bean.setPid(pid);
				bean.setUnitId(unitid);
				bean.setWriteDay(rq);
				bean.setProgramId("0");
				bean.setTeamId(teamid);
				bean.setShiftId(shiftid);
				bean.setParamId(paramid);
				bean.setBaseVal(sv);
				bean.setWorkingHour(gzsj);
				bean.setBeginTime(sbsj);
				bean.setEndTime(xbsj);
				bean.setCalcVal(jsz);
				pccsl.add(bean);
			}
		}
		return pccsl;
	}

	/**
	 * @category 得到旧的交接班信息并删除旧的数据
	 * @return
	 */
	private HashMap<String, CostTeamInfo> getOldTeamInfo(String unitid, String shiftid, String teamid, String tbrq) {
		HashMap<String, CostTeamInfo> rtn = new HashMap<String, CostTeamInfo>();
		List<CostTeamInfo> il = this.iopsd.getTeamInfo(unitid, shiftid, teamid, tbrq);
		if (il != null) {
			int count = il.size();
			String faid;
			for (int i = 0; count > i; i++) {
				CostTeamInfo x = il.get(i);
				faid = x.getProgramId();
				rtn.put(faid, x);
			}
		}
		if (this.iopsd.deleteTeamParamData(unitid, shiftid, teamid, tbrq)) {
			if (this.iopsd.deleteTeamItemData(unitid, shiftid, teamid, tbrq)) {
				if (this.iopsd.deleteTeamInstrumentData(unitid, shiftid, teamid, tbrq)) {
					if (this.iopsd.deleteTeamInfo(unitid, shiftid, teamid, tbrq)) {

					}
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 初始化批次的计算信息
	 */
	private boolean initBatchInfo(TeamReportInputDto reportinfo) {
		boolean cancalc = false;
		boolean updatebs = false;
		String bnid;
		String unitid = reportinfo.getUnitId();
		String bn = reportinfo.getBatchNo();
		List<CostBatchOnDuty> pcl = this.getBatchDutyInfo(unitid, bn);
		if (pcl != null && pcl.size() > 0) {
			Integer bs = 0;
			String xkssj, xjzsj, did, pckssj = "", pcjzsj = "";
			List<String> pidl = new ArrayList<String>();
			for (CostBatchOnDuty x : pcl) {
				did = x.getId();
				if (did == null) {
					did = "";
				}
				pidl.add(did);
				bs = x.getIsEnd();
				if (bs != null && bs == 1) {
					cancalc = true;
				}
				xkssj = x.getBeginTime();
				if (xkssj == null) {
					continue;
				}
				xjzsj = x.getEndTime();
				if (xjzsj == null) {
					continue;
				}
				if (pckssj == null || "".equals(pckssj)) {
					// 初始化
					pckssj = xkssj;
				} else {
					// 当前班组的批次开始时间<整个批次的开始时间，使用当前班组的批次开始时间作为整个批次的开始时间
					if (DateTimeUtils.bjDate(DateTimeUtils.parseDateTime(pckssj),
							DateTimeUtils.parseDateTime(xkssj)) == 1) {
						pckssj = xkssj;
					}
				}
				if (pcjzsj == null || "".equals(pcjzsj)) {
					// 初始化
					pcjzsj = xjzsj;
				} else {
					// 当前班组的批次截止时间>整个批次的截止时间，使用当前班组的批次截止时间作为整个批次的截止时间
					if (DateTimeUtils.bjDate(DateTimeUtils.parseDateTime(xjzsj),
							DateTimeUtils.parseDateTime(pcjzsj)) == 1) {
						pcjzsj = xjzsj;
					}
				}
			}
			if (pidl != null && pidl.size() > 0) {
				String faid = reportinfo.getProgramId();
				if (faid == null || "".equals(faid)) {
					faid = "0";
				}
				String tbrq = reportinfo.getWriteDay();
				CostBatchInfo cbi = this.getBatchCalcInfo(unitid, bn);
				if (cbi == null) {
					cbi = new CostBatchInfo();
					bnid = TMUID.getUID();
					cbi.setId(bnid);
				} else {
					bnid = cbi.getId();
					updatebs = true;
				}
				reportinfo.setBnid(bnid);
				reportinfo.setPckssj(pckssj);
				reportinfo.setPcjzsj(pcjzsj);
				cbi.setUnitId(unitid);
				cbi.setWriteDay(tbrq);
				cbi.setBatchNo(bn);
				cbi.setProgramId(faid);
				cbi.setBeginTime(pckssj);
				cbi.setEndTime(pcjzsj);
				List<CostBatchInstrumentData> dutyybbs = this.getBatchDutyInstrumentData(pidl);
				String ybid;
				Double val, hbs, jg;
				HashMap<String, Double> yblm = new HashMap<String, Double>();
				HashMap<String, Double> ybcbm = new HashMap<String, Double>();
				HashMap<String, List<CostBatchInstrumentData>> ybbsm = new HashMap<String, List<CostBatchInstrumentData>>();
				HashMap<String, CostBatchInstrumentData> pcybm = new HashMap<String, CostBatchInstrumentData>();
				if (dutyybbs != null) {
					for (CostBatchInstrumentData yb : dutyybbs) {
						did = yb.getPid();
						if (did == null) {
							did = "";
						}
						// 仪表的单表消耗
						ybid = yb.getInstrumentId();
						val = yb.getCalcVal();
						if (val == null) {
							val = 0.0;
						}
						jg = this.pm.convertDouble(yb.getItemPrice(), 0.0);
						if (jg.compareTo(0.0) != 0) {
							if (ybcbm.containsKey(ybid)) {
								ybcbm.put(ybid, ybcbm.get(ybid) + val * jg);
							} else {
								ybcbm.put(ybid, val * jg);
							}
						}

						if (yblm.containsKey(ybid)) {
							yblm.put(ybid, yblm.get(ybid) + val);
						} else {
							yblm.put(ybid, val);
						}
						if (ybbsm.containsKey(did)) {
							ybbsm.get(did).add(yb);
						} else {
							List<CostBatchInstrumentData> pp = new ArrayList<CostBatchInstrumentData>();
							pp.add(yb);
							ybbsm.put(did, pp);
						}
					}
					// 循环得到整个批次的前后表数
					for (String xid : pidl) {
						if (ybbsm.containsKey(xid)) {
							List<CostBatchInstrumentData> pp = ybbsm.get(xid);
							for (CostBatchInstrumentData mkj : pp) {
								ybid = mkj.getInstrumentId();
								if (pcybm.containsKey(ybid)) {
									// 仅添加后表
									CostBatchInstrumentData qq = pcybm.get(ybid);
									hbs = mkj.getLastReadOut();
									if (hbs == null) {
										hbs = 0.0;
									}
									if (hbs.compareTo(0.0) != 0) {
										qq.setLastReadOut(hbs);
									}
								} else {
									// 第一次添加时，同时给定前后表和仪表单表消耗
									CostBatchInstrumentData yy = new CostBatchInstrumentData();
									yy.setId(TMUID.getUID());
									yy.setPid(bnid);
									yy.setUnitId(unitid);
									yy.setProgramId(faid);
									yy.setInstrumentId(ybid);
									yy.setPreviousReadOut(mkj.getPreviousReadOut());
									yy.setLastReadOut(mkj.getLastReadOut());
									if (yblm.containsKey(ybid)) {
										val = yblm.get(ybid);
									} else {
										val = 0.0;
									}
									yy.setWriteVal(val);
									yy.setCalcVal(val);
									if (ybcbm.containsKey(ybid)) {
										jg = ybcbm.get(ybid);
										yy.setItemPrice(String.valueOf(jg));// 仪表的总金额
									}
									pcybm.put(ybid, yy);
								}
							}
							reportinfo.setPcybm(pcybm);
						}
					}
				}
				boolean savebs;
				if (updatebs) {
					savebs = this.updateBatchCalcInfo(cbi);
				} else {
					savebs = this.iopsd.insertBatchCalcInfo(cbi);
				}
				if (savebs && cancalc) {
					return true;
				} else {
					return false;
				}
			}
		}
		return false;
	}

	/**
	 * @category 初始化批次的计算信息
	 */
	private boolean deleteBatchInfo(TeamReportInputDto reportinfo) {
		String unitid = reportinfo.getUnitId();
		String obn = reportinfo.getDelbn();// 待删除的批号
		String nbn = reportinfo.getBatchNo();// 新的批号
		if (nbn == null) {
			nbn = "";
		}
		if (obn == null || "".equals(obn)) {
			return true;// 未传入旧批号，这时不处理历史数据
		} else {
			CostBatchInfo cbi = this.getBatchCalcInfo(unitid, obn);
			if (cbi == null) {
				return true;// 没有批次号，认为已删除
			} else {
				boolean updateold = false;
				// 批次号有数据，再读取批次当班数据，如果有多条，认为批次还要保留
				List<CostBatchOnDuty> pcl = this.getBatchDutyInfo(unitid, obn);
				if (pcl != null && pcl.size() > 0) {
					updateold = true;
				}
				if (updateold) {
					// 批号数据有用，先重新计算旧数据
					if (nbn.equals(obn)) {
						// 新旧批号一致，此处不计算旧批号的，外面计算
						return true;
					} else {
						// 旧的批号数据需要处理
						boolean delbs = true;// 默认要处理班组数据
						Integer bs = 0;
						for (CostBatchOnDuty pc : pcl) {
							bs = pc.getIsEnd();
							if (bs == null) {
								bs = 0;
							}
							if (bs == 1) {
								delbs = false;// 不需要处理班组数据，处理在calcBatchData里进行
								TeamReportInputDto tri = new TeamReportInputDto();
								tri.setBatchNo(pc.getBatchNo());
								tri.setBegintime(pc.getBeginTime());
								tri.setEndtime(pc.getEndTime());
								tri.setUnitId(pc.getUnitId());
								tri.setUnitMc(pc.getUnitName());
								tri.setWriteDay(pc.getWriteDay());
								tri.setSummaryDay(pc.getSummaryDay());
								tri.setShiftBegintime(pc.getShiftBegintime());
								tri.setShiftEndtime(pc.getShiftEndtime());
								tri.setTeamId(pc.getTeamId());
								tri.setShiftId(pc.getShiftId());
								tri.setProgramId(pc.getProgramId());
								tri.setOrgId(reportinfo.getOrgId());
								if (initBatchInfo(tri)) {
									this.calcBatchData(tri);
									return true;
								} else {
									return false;
								}
							}
						}
						if (delbs) {
							// 批次数据未重新计算，此时还要去除批次的计算内容
							HashMap<String, TeamProgramDto> zbi = new HashMap<String, TeamProgramDto>();
							if (this.deleteTeamBatchItemData(unitid, obn, zbi)) {
								String bnid = cbi.getId();
								if (this.iopsd.deleteInstrumentData(bnid)) {
									if (this.iopsd.deleteItemData(bnid)) {
										if (this.iopsd.deleteParamData(bnid)) {
											if (this.iopsd.deleteBatchCalcInfo(bnid)) {
												if (zbi != null && zbi.size() > 0) {
													// 重新计算班组方案数据

												}
												return true;
											} else {
												log.error("", "删除旧的批次信息出现错误。批次信息：" + JSON.toJSONString(reportinfo));
												return false;
											}
										} else {
											log.error("", "删除旧的批次核算指标数据出现错误。批次信息：" + JSON.toJSONString(reportinfo));
											return false;
										}
									} else {
										log.error("", "删除旧的批次项目数据出现错误。批次信息：" + JSON.toJSONString(reportinfo));
										return false;
									}
								} else {
									log.error("", "删除旧的批次仪表数据出现错误。批次信息：" + JSON.toJSONString(reportinfo));
									return false;
								}
							} else {
								log.error("", "删除旧的班组数据出现错误。批次信息：" + JSON.toJSONString(reportinfo));
								return false;
							}
						} else {
							return true;// 已处理的直接返回，正常这步无用
						}
					}
				} else {
					// 直接删除旧的批号数据
					HashMap<String, TeamProgramDto> zbi = new HashMap<String, TeamProgramDto>();
					if (this.deleteTeamBatchItemData(unitid, obn, zbi)) {
						String bnid = cbi.getId();
						if (this.iopsd.deleteInstrumentData(bnid)) {
							if (this.iopsd.deleteItemData(bnid)) {
								if (this.iopsd.deleteParamData(bnid)) {
									if (this.iopsd.deleteBatchCalcInfo(bnid)) {
										return true;
									} else {
										log.error("", "删除旧的批次信息出现错误。批次信息：" + JSON.toJSONString(reportinfo));
										return false;
									}
								} else {
									log.error("", "删除旧的批次核算指标数据出现错误。批次信息：" + JSON.toJSONString(reportinfo));
									return false;
								}
							} else {
								log.error("", "删除旧的批次项目数据出现错误。批次信息：" + JSON.toJSONString(reportinfo));
								return false;
							}
						} else {
							log.error("", "删除旧的批次仪表数据出现错误。批次信息：" + JSON.toJSONString(reportinfo));
							return false;
						}
					} else {
						log.error("", "删除旧的班组数据出现错误。批次信息：" + JSON.toJSONString(reportinfo));
						return false;
					}
				}
			}
		}
	}

	private String calcBatchData(TeamReportInputDto reportinfo) {
		String rtn = "";
		String tbrq = reportinfo.getWriteDay();
		String bnid = reportinfo.getBnid();
		String unitid = reportinfo.getUnitId();
		String pckssj = reportinfo.getPckssj();
		String pcjzsj = reportinfo.getPcjzsj();

		Double gzsj = DateTimeUtils.diffSecond(DateTimeUtils.parseDateTime(pcjzsj), DateTimeUtils.parseDateTime(pckssj))
				/ 3600.00;
		reportinfo.setGzsc(gzsj);
		this.init(reportinfo, tbrq, true);
		CalcTDSParam ctsp = new CalcTDSParam(reportinfo.getOrgId(), tbrq.substring(0, 7), pckssj, pcjzsj);
		HashMap<String, CostBatchInstrumentData> pcybm = reportinfo.getPcybm();
		HashMap<String, Costinstrument> ym = reportinfo.getYbm();
		HashMap<String, String> byxm = reportinfo.getByxm();
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		vm.clear();
		HashMap<String, Double> xmjgm = reportinfo.getXmjg();
		if (xmjgm == null) {
			xmjgm = new HashMap<String, Double>();
		}
		HashMap<String, Double> xmlm = new HashMap<String, Double>();
		HashMap<String, Double> xmcbm = new HashMap<String, Double>();

		List<CostBatchInstrumentData> batchybbs = new ArrayList<CostBatchInstrumentData>();
		if (pcybm != null && pcybm.size() > 0) {
			Double val, cb;
			String ybid, xmid, key;
			for (Entry<String, CostBatchInstrumentData> x : pcybm.entrySet()) {
				CostBatchInstrumentData yb = x.getValue();
				ybid = yb.getInstrumentId();
				if (ym.containsKey(ybid)) {
					Costinstrument ybxx = ym.get(ybid);
					if (ybxx != null) {
						xmid = ybxx.getPid();
						if (byxm.containsKey(xmid)) {
							continue;
						}
						if (xmid == null) {
							continue;
						}
						val = yb.getCalcVal();
						if (val == null) {
							val = 0.0;
						}
						cb = this.pm.convertDouble(yb.getItemPrice(), 0.0);
						if (cb.compareTo(0.0) != 0) {
							if (xmlm.containsKey(xmid)) {
								xmlm.put(xmid, xmlm.get(xmid) + val);
							} else {
								xmlm.put(xmid, val);
							}
							if (xmcbm.containsKey(xmid)) {
								xmcbm.put(xmid, xmcbm.get(xmid) + cb);
							} else {
								xmcbm.put(xmid, cb);
							}
						}
						key = new StringBuffer(unitid).append(".").append(ybid).append(".dbxhl").toString();
						vm.put(key, String.valueOf(val));
						key = new StringBuffer(ybid).append(".dbxhl").toString();
						vm.put(key, String.valueOf(val));
					}
				}
				batchybbs.add(yb);
			}
		}
		if (xmcbm != null && xmlm != null) {
			Double val, cb;
			String xmid;
			for (Entry<String, Double> xv : xmlm.entrySet()) {
				xmid = xv.getKey();
				val = xv.getValue();
				if (val != null && val.compareTo(0.0) != 0) {
					if (xmcbm.containsKey(xmid)) {
						cb = xmcbm.get(xmid);
						xmjgm.put(xmid, cb / val);
					}
				}
			}
		}
		this.calcObj(reportinfo, ctsp);
		this.calcHsb(reportinfo, ctsp);
		if (this.iopsd.deleteInstrumentData(bnid)) {
			if (this.iopsd.deleteItemData(bnid)) {
				if (this.iopsd.deleteParamData(bnid)) {
					if (this.iopsd.insertInstrumentData(batchybbs)) {
						if (this.iopsd.insertItemData(reportinfo.getPcxml())) {
							if (this.iopsd.insertParamData(reportinfo.getPccsl())) {
								rtn = calcTeamDayProgramReport(reportinfo);
							} else {
								rtn = "批次计算在新增核算指标出现了错误";
							}
						} else {
							rtn = "批次计算在新增核算项目出现了错误";
						}
					} else {
						rtn = "批次计算在新增核算仪表出现了错误";
					}
				} else {
					rtn = "批次计算在删除核算指标出现了错误";
				}
			} else {
				rtn = "批次计算在删除核算项目出现了错误";
			}
		} else {
			rtn = "批次计算在删除核算仪表出现了错误";
		}
		if (!"".equals(rtn)) {
			log.error("", rtn + "。批次信息" + JSON.toJSONString(reportinfo));
		}
		return rtn;
	}

	/**
	 ** 交接班计算任务：由调度计算日报和周报
	 */
	private void saveTask(TeamReportInputDto info) {
		String pid = "", unitid;
		String dw, rq, bc, bz, key;
		// 得到日期已有的计算任务
		List<ShiftCostCalcTask> tbl = this.getCalcInfo(info);
		HashMap<String, ShiftCostCalcTask> tbm = new HashMap<String, ShiftCostCalcTask>();
		if (tbl != null) {
			int count = tbl.size();
			for (int i = 0; count > i; i++) {
				ShiftCostCalcTask scct = tbl.get(i);
				dw = scct.getUnitId();
				rq = scct.getWriteDay();
				bc = scct.getShiftId();
				bz = scct.getTeamId();
				key = (new StringBuffer(dw).append(".").append(rq).append(".").append(bc).append(".").append(bz))
						.toString();
				tbm.put(key, scct);
			}
		}
		unitid = info.getUnitId();
		rq = info.getWriteDay();
		bc = info.getShiftId();
		bz = info.getTeamId();
		// 当前核算对象的计算任务
		key = (new StringBuffer(unitid).append(".").append(rq).append(".").append(bc).append(".").append(bz))
				.toString();
		if (tbm.containsKey(key)) {
			ShiftCostCalcTask bean = tbm.get(key);
			bean.setCalcStatus(0);
			this.entityService.update(bean);
		} else {
			ShiftCostCalcTask bean = new ShiftCostCalcTask();
			bean.setId(TMUID.getUID());
			bean.setUnitId(unitid);
			bean.setWriteDay(rq);
			bean.setShiftId(bc);
			bean.setTeamId(bz);
			bean.setSummaryDay(info.getSummaryDay());
			bean.setBeginTime(info.getBegintime());
			bean.setEndTime(info.getEndtime());
			bean.setShiftBeginTime(info.getShiftBegintime());
			bean.setShiftEndTime(info.getShiftEndtime());
			bean.setCalcStatus(0);
			this.entityService.insert(bean);
		}
		// 当前核算对象父对象的计算任务
		HashMap<String, String> upm = info.getUnitPm();
		if (upm == null) {
			upm = new HashMap<String, String>();
		}
		if (upm.containsKey(unitid)) {
			pid = upm.get(unitid);
			if (pid != null && !"".equals(pid) && !"root".equals(pid)) {
				// 有父对象，添加计算任务
				this.saveParentTask(pid, info, upm, tbm);
			}
		}
	}

	private void saveParentTask(String unitid, TeamReportInputDto info, HashMap<String, String> upm,
			HashMap<String, ShiftCostCalcTask> tbm) {
		String rq = info.getWriteDay();
		String bc = info.getShiftId();
		String bz = info.getTeamId();
		String key = (new StringBuffer(unitid).append(".").append(rq).append(".").append(bc).append(".").append(bz))
				.toString();
		if (tbm.containsKey(key)) {
			ShiftCostCalcTask bean = tbm.get(key);
			bean.setCalcStatus(0);
			this.entityService.update(bean);
		} else {
			ShiftCostCalcTask bean = new ShiftCostCalcTask();
			bean.setId(TMUID.getUID());
			bean.setUnitId(unitid);
			bean.setWriteDay(rq);
			bean.setShiftId(bc);
			bean.setTeamId(bz);
			bean.setSummaryDay(info.getSummaryDay());
			bean.setBeginTime(info.getBegintime());
			bean.setEndTime(info.getEndtime());
			bean.setShiftBeginTime(info.getShiftBegintime());
			bean.setShiftEndTime(info.getShiftEndtime());
			bean.setCalcStatus(0);
			this.entityService.insert(bean);
		}
		// 当前核算对象父对象的计算任务
		if (upm.containsKey(unitid)) {
			String pid = upm.get(unitid);
			if (pid != null && !"".equals(pid) && !"root".equals(pid)) {
				// 有父对象，添加计算任务
				this.saveParentTask(pid, info, upm, tbm);
			}
		}
	}

	private CostBatchItemData setItemData(String wzdm, Double dj, Double khdh, String faid, String bnid, String tbrq,
			String pckssj, String pcjzsj, Double gzsc) {
		CostBatchItemData bean = new CostBatchItemData();
		bean.setId(TMUID.getUID());
		bean.setPid(bnid);
		bean.setWriteDay(tbrq);
		bean.setBeginTime(pckssj);
		bean.setEndTime(pcjzsj);
		bean.setWorkingHour(gzsc);
		bean.setItemId(wzdm);
		bean.setProgramId(faid);
		if (dj != null) {
			bean.setItemprice(Maths.round(dj, 2));
		}
		bean.setBaseUnitConsumption(khdh);
		return bean;
	}

	private void init(TeamReportInputDto reportinfo, String unitid, String rq) {
		// 得到租户内所有的核算对象
		HashMap<String, String> um = new HashMap<String, String>();
		HashMap<String, String> upm = new HashMap<String, String>();
		MethodQueryDto queryDto = new MethodQueryDto();
		queryDto.setProductive(0);
		List<Costuint> ul = this.unitMethodService.getCostuintList(queryDto);
		if (ul != null) {
			String pid, id;
			int count = ul.size();
			for (int i = 0; count > i; i++) {
				Costuint x = ul.get(i);
				id = x.getId();
				um.put(id, x.getName());
				pid = x.getPid();
				if (StringUtils.isNotEmpty(pid)) {
					upm.put(id, pid);
				}
			}
			if (um.containsKey(unitid)) {
				reportinfo.setUnitMc(um.get(unitid));
			}
		}
		reportinfo.setUnitm(um);
		reportinfo.setUnitPm(upm);
		// 得到方案下的使用项目
		String wzdm, ybwh, key1, key2;
		CostItemInfoVo infos = this.unitItemInfoService.getCostData(unitid, rq);
		// 分类信息
		HashMap<String, String> flm = new HashMap<String, String>();
		List<Costclass> fll = infos.getClassList();
		if (fll != null) {
			String fllx;
			int count = fll.size();
			for (int i = 0; count > i; i++) {
				Costclass cy = fll.get(i);
				fllx = cy.getCctype();
				if (fllx == null) {
					fllx = "";
				}
				flm.put(cy.getId(), fllx);
			}
		}
		// 项目仪表
		HashMap<String, Costinstrument> ym = new HashMap<String, Costinstrument>();
		List<Costinstrument> ybids = infos.getInstrumentList();
		if (ybids != null) {
			int count = ybids.size();
			for (int i = 0; count > i; i++) {
				Costinstrument x = ybids.get(i);
				ybwh = x.getId();
				ym.put(ybwh, x);
			}
		}
		reportinfo.setYbm(ym);
		HashMap<String, Double> itemjh = new HashMap<String, Double>();
		// 成本项目信息
		HashMap<String, Costitem> im = new HashMap<String, Costitem>();
		HashMap<String, String> bm = new HashMap<String, String>();
		List<CostBatchItemData> xml = new ArrayList<CostBatchItemData>();
		HashMap<String, List<String>> flxm = new HashMap<String, List<String>>();
		List<Costitem> xmids = infos.getItemList();
		if (xmids != null) {
			Double dj;
			Integer lrfs;
			String flid, fllx, erpcode;
			HashMap<String, Double> jgm = gcii.getItemPrice("priceConfig", rq);
			HashMap<String, Double> jhm = gcii.getItemPrice("costConfig", rq);
			int count = xmids.size();
			for (int i = 0; count > i; i++) {
				Costitem x = xmids.get(i);
				// 物料提供
				lrfs = x.getMaterialSupply();
				if (lrfs != null && lrfs == 1) {
					// 班组录入的项目才添加到批次数据
					wzdm = x.getId();
					erpcode = x.getErpcode();
					dj = x.getItemprice();
					flid = x.getPid();
					if (flid == null) {
						flid = "";
					}
					// 得到分类类型下的项目
					if (flm.containsKey(flid)) {
						fllx = flm.get(flid);
						if (flxm.containsKey(fllx)) {
							flxm.get(fllx).add(wzdm);
						} else {
							List<String> ml = new ArrayList<String>();
							ml.add(wzdm);
							flxm.put(fllx, ml);
						}
					}
					// 计划值
					if (jhm != null && jhm.containsKey(wzdm)) {
						itemjh.put(wzdm, jhm.get(wzdm));
					}
					// 从价格维护
					if (jgm != null) {
						if (jgm.containsKey(erpcode)) {
							dj = jgm.get(erpcode);
						}
					}
					if (dj != null) {
						x.setItemprice(Maths.round(dj, 2));
					}
					im.put(wzdm, x);
				}
			}
		}
		reportinfo.setXmjh(itemjh);
		reportinfo.setItemfl(flxm);
		reportinfo.setItemm(im);
		reportinfo.setByxm(bm);
		reportinfo.setPcxml(xml);

		// 核算指标
		HashMap<String, Costindicator> csm = new HashMap<String, Costindicator>();
		List<Costindicator> csl = infos.getIndicatorList();
		if (csl != null) {
			int count = csl.size();
			for (int i = 0; count > i; i++) {
				Costindicator x = csl.get(i);
				csm.put(x.getId(), x);
			}
		}
		reportinfo.setHszbm(csm);
		// 公式
		String gslx;
		HashMap<String, CostItemFormula> gm = new HashMap<String, CostItemFormula>();
		List<CostItemFormula> gsl = infos.getFormulaList();
		if (gsl != null) {
			int count = gsl.size();
			for (int i = 0; count > i; i++) {
				CostItemFormula x = gsl.get(i);
				key1 = x.getPid();
				gslx = x.getFType();
				key2 = (new StringBuffer(key1)).append(".").append(gslx).toString();
				gm.put(key2, x);
			}
		}
		reportinfo.setGsm(gm);
	}

	private void init(TeamReportInputDto reportinfo, String rq, boolean jjb) {
		Double gzsc = reportinfo.getGzsc();
		String unitid = reportinfo.getUnitId();
		String faid = reportinfo.getProgramId();
		if (faid == null || "".equals(faid)) {
			faid = "0";
		}
		String bnid = reportinfo.getBnid();
		String tbrq = reportinfo.getWriteDay();
		String pckssj = reportinfo.getPckssj();
		String pcjzsj = reportinfo.getPcjzsj();
		// 得到租户内所有的核算对象
		HashMap<String, String> um = new HashMap<String, String>();
		HashMap<String, String> upm = new HashMap<String, String>();
		MethodQueryDto queryDto = new MethodQueryDto();
		queryDto.setProductive(0);
		List<Costuint> ul = this.unitMethodService.getCostuintList(queryDto);
		if (ul != null) {
			String pid, id;
			int count = ul.size();
			for (int i = 0; count > i; i++) {
				Costuint x = ul.get(i);
				id = x.getId();
				um.put(id, x.getName());
				pid = x.getPid();
				if (StringUtils.isNotEmpty(pid)) {
					upm.put(id, pid);
				}
			}
			if (um.containsKey(unitid)) {
				reportinfo.setUnitMc(um.get(unitid));
			}
		}
		reportinfo.setUnitm(um);
		reportinfo.setUnitPm(upm);
		// 得到方案下的使用项目
		String wzdm, ybwh, key1, key2;
		HashMap<String, ProgramLibraryCostItem> qywzm = new HashMap<String, ProgramLibraryCostItem>();// 启用的物资
		HashMap<String, ProgramLibraryCostItem> qyzbm = new HashMap<String, ProgramLibraryCostItem>();// 启用的指标
		CostItemInfoVo infos = this.unitItemInfoService.getUnitData(unitid, rq, faid);
		List<ProgramLibraryCostItem> qyxml = infos.getPItemList();
		if (qyxml != null) {
			Integer dtype;
			int count = qyxml.size();
			for (int i = 0; count > i; i++) {
				ProgramLibraryCostItem x = qyxml.get(i);
				dtype = x.getDataType();
				if (dtype == null || dtype == 0) {
					// 项目
					qywzm.put(x.getItemId(), x);
				} else {
					// 指标
					qyzbm.put(x.getName(), x);
				}
			}
		}
		reportinfo.setFawzm(qywzm);
		reportinfo.setFazbm(qyzbm);
		// 分类信息
		HashMap<String, String> flm = new HashMap<String, String>();
		List<Costclass> fll = infos.getClassList();
		if (fll != null) {
			String fllx;
			int count = fll.size();
			for (int i = 0; count > i; i++) {
				Costclass cy = fll.get(i);
				fllx = cy.getCctype();
				if (fllx == null) {
					fllx = "";
				}
				flm.put(cy.getId(), fllx);
			}
		}
		// 项目仪表
		HashMap<String, Costinstrument> ym = new HashMap<String, Costinstrument>();
		List<Costinstrument> ybids = infos.getInstrumentList();
		if (ybids != null) {
			int count = ybids.size();
			for (int i = 0; count > i; i++) {
				Costinstrument x = ybids.get(i);
				ybwh = x.getId();
				ym.put(ybwh, x);
			}
		}
		reportinfo.setYbm(ym);
		HashMap<String, Double> itemjh = new HashMap<String, Double>();
		// 成本项目信息
		HashMap<String, Costitem> im = new HashMap<String, Costitem>();
		HashMap<String, String> bm = new HashMap<String, String>();
		List<CostBatchItemData> xml = new ArrayList<CostBatchItemData>();
		HashMap<String, List<String>> flxm = new HashMap<String, List<String>>();
		List<Costitem> xmids = infos.getItemList();
		if (xmids != null) {
			Double dj;
			Integer lrfs;
			String flid, fllx, erpcode;
			HashMap<String, Double> jgm = gcii.getItemPrice("priceConfig", rq);
			HashMap<String, Double> jhm = gcii.getItemPrice("costConfig", rq);
			int count = xmids.size();
			for (int i = 0; count > i; i++) {
				Costitem x = xmids.get(i);
				// 物料提供
				wzdm = x.getId();
				erpcode = x.getErpcode();
				dj = x.getItemprice();
				flid = x.getPid();
				if (flid == null) {
					flid = "";
				}
				// 得到分类类型下的项目
				if (flm.containsKey(flid)) {
					fllx = flm.get(flid);
					if (flxm.containsKey(fllx)) {
						flxm.get(fllx).add(wzdm);
					} else {
						List<String> ml = new ArrayList<String>();
						ml.add(wzdm);
						flxm.put(fllx, ml);
					}
				}
				// 计划值
				if (jhm != null && jhm.containsKey(wzdm)) {
					itemjh.put(wzdm, jhm.get(wzdm));
				}
				// 从价格维护
				if (jgm != null) {
					if (jgm.containsKey(erpcode)) {
						dj = jgm.get(erpcode);
					}
				}
				if (dj != null) {
					x.setItemprice(Maths.round(dj, 2));
				}
				if (qywzm.size() > 0) {
					// 使用了方案时仅添加方案下的项目
					if (qywzm.containsKey(wzdm)) {
						ProgramLibraryCostItem faxm = qywzm.get(wzdm);
						if (faxm.getTmUsed() != null && faxm.getTmUsed() == 0) {
							bm.put(wzdm, "1");
						} else {
							x.setBaseConsumption(faxm.getBaseConsumption());
							if (jjb) {
								lrfs = x.getMaterialSupply();
								if (lrfs != null && lrfs == 1) {
									// 班组录入的项目才添加到批次数据
									xml.add(this.setItemData(wzdm, dj, faxm.getBaseConsumption(), faid, bnid, tbrq,
											pckssj, pcjzsj, gzsc));
								}
							}
						}
					} else {
						if (jjb) {
							lrfs = x.getMaterialSupply();
							if (lrfs != null && lrfs == 1) {
								// 班组录入的项目才添加到批次数据
								xml.add(this.setItemData(wzdm, dj, x.getBaseConsumption(), faid, bnid, tbrq, pckssj,
										pcjzsj, gzsc));
							}
						}
					}
				} else {
					// 未使用方案
					if (jjb) {
						lrfs = x.getMaterialSupply();
						if (lrfs != null && lrfs == 1) {
							// 班组录入的项目才添加到批次数据
							xml.add(this.setItemData(wzdm, dj, x.getBaseConsumption(), faid, bnid, tbrq, pckssj, pcjzsj,
									gzsc));
						}
					}
				}
				im.put(wzdm, x);
			}
		}
		reportinfo.setXmjh(itemjh);
		reportinfo.setItemfl(flxm);
		reportinfo.setItemm(im);
		reportinfo.setByxm(bm);
		reportinfo.setPcxml(xml);

		// 核算指标
		HashMap<String, Costindicator> csm = new HashMap<String, Costindicator>();
		List<Costindicator> csl = infos.getIndicatorList();
		if (csl != null) {
			int count = csl.size();
			for (int i = 0; count > i; i++) {
				Costindicator x = csl.get(i);
				csm.put(x.getId(), x);
			}
		}
		reportinfo.setHszbm(csm);
		// 公式
		String gslx;
		HashMap<String, CostItemFormula> gm = new HashMap<String, CostItemFormula>();
		List<CostItemFormula> gsl = infos.getFormulaList();
		if (gsl != null) {
			int count = gsl.size();
			for (int i = 0; count > i; i++) {
				CostItemFormula x = gsl.get(i);
				key1 = x.getPid();
				gslx = x.getFType();
				key2 = (new StringBuffer(key1)).append(".").append(gslx).toString();
				gm.put(key2, x);
			}
		}
		reportinfo.setGsm(gm);
	}

	private String getItemName(String itemid, HashMap<String, Costitem> im) {
		String rtn;
		if (im.containsKey(itemid)) {
			Costitem wz = im.get(itemid);
			if (wz != null) {
				rtn = wz.getItemname();
			} else {
				rtn = "";
			}
		} else {
			rtn = "";
		}
		return rtn;
	}

	/**
	 * @category 计算项目数据
	 * @return
	 */

	private void calcObj(TeamReportInputDto reportinfo, CalcTDSParam ctsp) {
		String wzdm, wzmc = "", val, cpid;
		Double jsz;
		String unitid = reportinfo.getUnitId();
		cpid = reportinfo.getCpid();
		if (cpid == null) {
			cpid = "";
		}
		List<CostBatchItemData> pcxml = reportinfo.getPcxml();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Double> xmjgm = reportinfo.getXmjg();
		if (xmjgm == null) {
			xmjgm = new HashMap<String, Double>();
		}
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		// 先计算消耗量
		Double dcyn;
		for (CostBatchItemData x : pcxml) {
			wzdm = x.getItemId();
			wzmc = this.getItemName(wzdm, im);
			if (xmjgm != null && xmjgm.containsKey(wzdm)) {
				dcyn = xmjgm.get(wzdm);
				if (dcyn != null) {
					x.setItemprice(Maths.round(dcyn, 2));// 优先使用录入选择的价格（平均价格）
				}
			}
			jsz = this.calcformula((new StringBuffer(wzdm).append(".hxl")).toString(), reportinfo, ctsp, wzdm, wzmc, "",
					"", "");
			if (cpid.equals(wzdm)) {
				reportinfo.setCpVal(jsz);
			}
			val = String.valueOf(jsz);
			vm.put(unitid + "." + wzdm + ".xhl", val);
			vm.put(unitid + "." + wzdm + ".cl", val);
			vm.put(wzdm, val);
			x.setConsumption(jsz);
			x.setWriteConsumption(jsz);
		}
		// 计算其它公式
		for (CostBatchItemData x : pcxml) {
			wzdm = x.getItemId();
			wzmc = this.getItemName(wzdm, im);
			// 考核消耗量
			jsz = this.calcformula((new StringBuffer(wzdm).append(".khxhl")).toString(), reportinfo, ctsp, wzdm, wzmc,
					"", "", "");
			x.setBaseConsumption(jsz);
			// 考核总成本
			jsz = this.calcformula((new StringBuffer(wzdm).append(".khzcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
					"", "", "");
			x.setBaseCost(jsz);
			// 单耗
			jsz = this.calcformula((new StringBuffer(wzdm).append(".dh")).toString(), reportinfo, ctsp, wzdm, wzmc, "",
					"", "");
			x.setUnitConsumption(jsz);
			// 单位成本
			jsz = this.calcformula((new StringBuffer(wzdm).append(".dwcb")).toString(), reportinfo, ctsp, wzdm, wzmc,
					"", "", "");
			x.setUnitCost(jsz);
			// 总成本
			jsz = this.calcformula((new StringBuffer(wzdm).append(".zcb")).toString(), reportinfo, ctsp, wzdm, wzmc, "",
					"", "");
			x.setItemCost(jsz);
		}
	}

	/**
	 * @category 计算核算参数
	 * @return
	 */

	private void calcHsb(TeamReportInputDto reportinfo, CalcTDSParam ctsp) {
		Integer sy;
		String key, paramid, parammc;
		Double jsz, sv;
		Double gzsc = reportinfo.getGzsc();
		String bnid = reportinfo.getBnid();
		String faid = reportinfo.getProgramId();
		String tbrq = reportinfo.getWriteDay();
		String unitid = reportinfo.getUnitId();
		String pckssj = reportinfo.getPckssj();
		String pcjzsj = reportinfo.getPcjzsj();
		HashMap<String, ProgramLibraryCostItem> zbm = reportinfo.getFazbm();
		HashMap<String, Costindicator> csm = reportinfo.getHszbm();
		List<CostBatchParamData> pccsl = new ArrayList<CostBatchParamData>();
		for (Entry<String, Costindicator> x : csm.entrySet()) {
			Costindicator param = x.getValue();
			paramid = param.getId();
			parammc = param.getCpname();
			sv = param.getStandardval();// 默认使用指标设置的标准值
			// 核算指标启用（用名称与方案对照）
			sy = 1;// 默认启用
			if (zbm.containsKey(parammc)) {
				ProgramLibraryCostItem plci = zbm.get(parammc);
				if (plci != null) {
					sy = plci.getTmUsed();
					if (sy == null) {
						sy = 1;
					}
					sv = plci.getBaseConsumption();// 核定量
				}
			}
			if (sy == 1) {
				key = (new StringBuffer(paramid)).append(".param").toString();
				CostBatchParamData bean = new CostBatchParamData();
				bean.setId(TMUID.getUID());
				bean.setPid(bnid);
				bean.setUnitId(unitid);
				bean.setProgramId(faid);
				bean.setParamId(paramid);
				bean.setBaseVal(sv);
				jsz = this.calcformula(key, reportinfo, ctsp, "", "", "", "", "");
				bean.setCalcVal(jsz);
				bean.setWorkingHour(gzsc);
				bean.setBeginTime(pckssj);
				bean.setEndTime(pcjzsj);
				bean.setWriteDay(tbrq);
				pccsl.add(bean);
			}
		}
		reportinfo.setPccsl(pccsl);
	}

	/**
	 * 获得匹配的变量
	 * 
	 * @param str
	 * @param rex
	 * @return
	 */
	private List<String> getBl(String str, String rex) {
		List<String> list = new ArrayList<String>();
		if (rex == null ? true : rex.trim().length() == 0)
			rex = "([^\\/^\\+^\\-^\\*^ ^\\(^\\)^\\,^\\>,^\\=,^\\<]+\\.)+([A-Za-z]+)";
		Pattern pattern = Pattern.compile(rex);
		Matcher m = pattern.matcher(str);
		while (m.find()) {
			list.add(m.group());
		}
		return list;
	}

	/**
	 * @category 替换公式参数
	 * @param sgs
	 * @param jscs
	 * @return
	 */

	private String calcFormula(String sgs, TeamReportInputDto reportinfo, String csid, String xmid, String xmmc,
			String ybid, String ybmc, CalcTDSParam ctsp, String teamid) {
		if (StringUtils.isEmpty(sgs)) {
			return "0";
		}
		String calcResult = "0";
		String objid = reportinfo.getUnitId(), objmc = "";
		if (csid != null && !"".equals(csid)) {
			// 根据参数指定的核算对象查找数据
			objid = csid;
		}
		HashMap<String, String> um = reportinfo.getUnitm();
		if (um.containsKey(objid)) {
			objmc = um.get(objid);
		}
		Map<String, List<String>> resMap = AviatorUtils.getAllParams(sgs);
		// 所有变量列表（不包含数据源）
		List<String> varList = this.getBl(sgs, this._zbbl);
		sgs = sgs.replaceAll("[\\[\\]]", "");
		// 数据源列表（数据源别名，含$）
		List<String> tdsList = resMap.get("tds");
		// 数据源公式列表
		List<String> tdsFormulaList = resMap.get("tdsFormula");

		String dd = "0";
		Map<String, Object> valueMap = new HashMap<>();
		for (String var : varList) { // 获取变量值并赋值
			if ("$".equals(var.substring(0, 1)) || "if".equals(var) || "round".equals(var)
					|| this.pm.judgeDouble(var)) {
				// 数据源不解析、函数和常数不解析
				continue;
			}
			dd = this.getValue(var, reportinfo, ctsp, teamid, objid);
			valueMap.put(var.replaceAll("[\\[\\]]", ""), this.pm.convertDouble(dd, 0.0));
		}
		Map<String, Object> tdsValueMap = new HashMap<>();
		if (tdsList != null && tdsList.size() > 0) {
			TdsFormulaParamDTO p = new TdsFormulaParamDTO();
			p.setUnitId(objid);
			p.setUnitName(objmc);
			p.setKssj(reportinfo.getBegintime());
			p.setJzsj(reportinfo.getEndtime());
			p.setTbrq(reportinfo.getWriteDay());
			p.setShiftId(reportinfo.getShiftId());
			p.setShiftName("");
			p.setTeamId(teamid);
			p.setTeamName("");
			p.setTENANT_ID("");
			p.setXmid(xmid);
			p.setXmmc(xmmc);
			p.setYbid(ybid);
			p.setYbmc(ybmc);
			ctsp.TDSRetrieve(tdsList, p);
			for (String param : tdsFormulaList) {
				Object tdsValue = null;
				try {
					tdsValue = ctsp.replaceDsFormula(param, p);
				} catch (Exception e) {
				}
				tdsValueMap.put(param, tdsValue);
			}
		}
		try {
			AviatorResult ar = AviatorUtils.execute(sgs, valueMap, tdsValueMap);
			calcResult = String.valueOf(ar.getResult());
			// 计算结果 String jg=ar.getExpression();
		} catch (Exception e) {
			calcResult = "0";
		}
		return calcResult;
	}

	/**
	 * @category 获取分类项目量
	 * @param wzdm
	 * @param reportinfo
	 * @param teamid
	 * @return
	 */
	private String getFlXml(String fllx, HashMap<String, List<String>> flxm, String teamid,
			HashMap<String, String> vm) {
		Double lj = 0.0;
		List<String> xxl = flxm.get(fllx);
		if (xxl != null) {
			if ("".equals(teamid)) {
				for (String xx : xxl) {
					// 普通报表数据解析，直接用项目ID
					if (vm.containsKey(xx)) {
						lj = lj + pm.convertDouble(vm.get(xx), 0.0);
					}
				}
			} else {
				// 班组月汇总取消耗量，完全从此处取
				for (String xx : xxl) {
					String key = (new StringBuffer(xx).append(".").append(teamid).append(".xhl")).toString();
					if (vm.containsKey(key)) {
						lj = lj + pm.convertDouble(vm.get(key), 0.0);
					}
				}
			}
		}
		return String.valueOf(lj);
	}

	/**
	 * @category 获取分类的总成本
	 * @param fllx
	 * @param flxm
	 * @param teamid
	 * @param vm
	 * @param reportinfo
	 * @param ctsp
	 * @param unitid
	 * @return
	 */
	private String getFlZcb(String fllx, HashMap<String, List<String>> flxm, String teamid, HashMap<String, String> vm,
			TeamReportInputDto reportinfo, CalcTDSParam ctsp, String unitid) {
		Double lj = 0.0;
		String key, val;
		List<String> xxl = flxm.get(fllx);
		if (xxl != null) {
			HashMap<String, CostItemFormula> gsm = reportinfo.getGsm();
			if (gsm == null) {
				gsm = new HashMap<String, CostItemFormula>();
				reportinfo.setGsm(gsm);
			}
			for (String xx : xxl) {
				// 普通报表数据解析，直接用项目ID
				key = (new StringBuffer(xx).append(".zcb")).toString();
				if (gsm.containsKey(key)) {
					CostItemFormula gs = gsm.get(key);
					if (gs != null) {
						val = this.calcFormula(gs.getFormula(), reportinfo, unitid, "", "", "", "", ctsp, teamid);
						lj = lj + pm.convertDouble(val, 0.0);
					}
				}
			}
		}
		return String.valueOf(lj);
	}

	/**
	 * @category 获取分类的综合能耗
	 * @param fllx
	 * @param flxm
	 * @param teamid
	 * @param vm
	 * @param reportinfo
	 * @param ctsp
	 * @param unitid
	 * @return
	 */
	private String getFlZhnh(String fllx, HashMap<String, List<String>> flxm, String teamid, HashMap<String, String> vm,
			TeamReportInputDto reportinfo, CalcTDSParam ctsp, String unitid) {
		Double lj = 0.0, dv, nv;
		String key;
		List<String> xxl = flxm.get(fllx);
		if (xxl != null) {
			HashMap<String, CostItemFormula> gsm = reportinfo.getGsm();
			if (gsm == null) {
				gsm = new HashMap<String, CostItemFormula>();
				reportinfo.setGsm(gsm);
			}
			HashMap<String, Costitem> im = reportinfo.getItemm();
			for (String xx : xxl) {
				// 普通报表数据解析，直接用项目ID
				if (im.containsKey(xx)) {
					Costitem tr = im.get(xx);
					nv = tr.getEnergyfactor();// 能耗系数
					if (nv == null) {
						nv = 0.0;
					}
					key = (new StringBuffer(xx).append(".dh")).toString();
					if (gsm.containsKey(key)) {
						CostItemFormula gs = gsm.get(key);
						if (gs != null) {
							dv = this.pm.convertDouble(
									this.calcFormula(gs.getFormula(), reportinfo, unitid, "", "", "", "", ctsp, teamid),
									0.0);
							lj = lj + dv * nv;
						}
					}
				}
			}
		}
		return String.valueOf(lj);
	}

	private String getXmSubInfo(String wzdm, TeamReportInputDto reportinfo, CalcTDSParam ctsp, String unitid) {
		Double dv = 0.0;
		HashMap<String, Costitem> im = reportinfo.getItemm();
		if (im == null) {
			im = new HashMap<String, Costitem>();
			reportinfo.setItemm(im);
		}
		HashMap<String, String> armm = reportinfo.getAlrm();
		if (armm == null) {
			armm = new HashMap<String, String>();
			reportinfo.setAlrm(armm);
		}
		HashMap<String, Double> subl = reportinfo.getSubiteml();
		if (subl == null) {
			subl = new HashMap<String, Double>();
			reportinfo.setSubiteml(subl);
		}
		if (!armm.containsKey(unitid + ".SUBUNITCONSUMPTION")) {
			// 未检索过数据，检索数据
			List<ItemConsumption> dd = gics.getSubUnitItemConsumption(unitid, reportinfo.getWriteDay(),
					reportinfo.getShiftId(), reportinfo.getTeamId(), reportinfo.getProgramId(), im);
			if (dd != null) {
				int count = dd.size();
				for (int i = 0; count > i; i++) {
					ItemConsumption d = dd.get(i);
					subl.put(d.getItemId(), d.getConsumption());
				}
			}
			armm.put(unitid + ".SUBUNITCONSUMPTION", "1");
		}
		String itemid;
		if (im != null && im.containsKey(wzdm)) {
			itemid = im.get(wzdm).getItemid();
			if (subl.containsKey(itemid)) {
				dv = subl.get(itemid);
			}
		}
		if (dv == null) {
			dv = 0.0;
		}
		return String.valueOf(dv);
	}

	/**
	 * @category 取核算项目值
	 * @param wzdm       项目代码
	 * @param qzlx       取值类型
	 * @param reportinfo 制表信息
	 * @param teamid     班组代码
	 * @param ctsp       数据源解析
	 * @param unitid     核算对象ID
	 * @return
	 */
	private String getCostingData(String wzdm, String qzlx, TeamReportInputDto reportinfo, String teamid,
			CalcTDSParam ctsp, String unitid) {
		Double val = 0.0;
		String rtn = "0";
		HashMap<String, String> vm = reportinfo.getValm();
		if (vm == null) {
			vm = new HashMap<String, String>();
			reportinfo.setValm(vm);
		}
		HashMap<String, Costinstrument> ym = reportinfo.getYbm();
		HashMap<String, Costitem> im = reportinfo.getItemm();
		HashMap<String, Double> xmjgm = reportinfo.getXmjg();
		if (xmjgm == null) {
			xmjgm = new HashMap<String, Double>();
		}
		HashMap<String, Double> xmjh = reportinfo.getXmjh();
		if (xmjh == null) {
			xmjh = new HashMap<String, Double>();
		}
		if ("xhl".equals(qzlx) || "cl".equals(qzlx)) {
			HashMap<String, List<String>> flxm = reportinfo.getItemfl();
			if (flxm == null) {
				flxm = new HashMap<String, List<String>>();
			}
			if (flxm.containsKey(wzdm)) {
				rtn = this.getFlXml(wzdm, flxm, teamid, vm);
			} else {
				if ("".equals(teamid)) {
					// 普通报表数据解析，直接用项目ID
					if (vm.containsKey(wzdm)) {
						rtn = vm.get(wzdm);
					}
				} else {
					// 班组月汇总取消耗量，完全从此处取
					String key = (new StringBuffer(wzdm).append(".").append(teamid).append(".xhl")).toString();
					if (vm.containsKey(key)) {
						rtn = vm.get(key);
					}
				}
			}
		} else if ("zcb".equals(qzlx)) {
			// 只能是分类的
			HashMap<String, List<String>> flxm = reportinfo.getItemfl();
			if (flxm == null) {
				flxm = new HashMap<String, List<String>>();
			}
			if (flxm.containsKey(wzdm)) {
				rtn = this.getFlZcb(wzdm, flxm, teamid, vm, reportinfo, ctsp, unitid);
			}
		} else if ("zhnh".equals(qzlx)) {
			// 只能是分类的
			HashMap<String, List<String>> flxm = reportinfo.getItemfl();
			if (flxm == null) {
				flxm = new HashMap<String, List<String>>();
			}
			if (flxm.containsKey(wzdm)) {
				rtn = this.getFlZhnh(wzdm, flxm, teamid, vm, reportinfo, ctsp, unitid);
			}
		} else if ("xjxhl".equals(qzlx)) {
			// 只能是下级核算对象的消耗量
			rtn = this.getXmSubInfo(wzdm, reportinfo, ctsp, unitid);
		} else if ("dbxhl".equals(qzlx)) {
			if (vm.containsKey(wzdm)) {
				rtn = vm.get(wzdm);
			}
		} else {
			if ("ybxs".equals(qzlx) || "yblc".equals(qzlx)) {
				if (ym.containsKey(wzdm)) {
					Costinstrument tr = ym.get(wzdm);
					if ("ybxs".equals(qzlx)) {
						val = tr.getConversionfactor();
						if (val == null) {
							val = 1.0;
						}
						rtn = String.valueOf(val);
					} else if ("yblc".equals(qzlx)) {
						val = tr.getInstrumentRange();
						if (val == null) {
							val = 0.0;
						}
						rtn = String.valueOf(val);
					} else {
						rtn = "0";
					}
				}
			} else {
				// 成本项目的
				if (im.containsKey(wzdm)) {
					Costitem tr = im.get(wzdm);
					if ("nkhz".equals(qzlx)) {
						val = 0.0;
					} else if ("khzcb".equals(qzlx)) {
						if (xmjh.containsKey(wzdm)) {// 优先使用录入选择的价格（平均价格）
							val = xmjh.get(wzdm);
						} else {
							val = 0.0;
						}
					} else if ("dj".equals(qzlx) || "khdj".equals(qzlx) || "bzdj".equals(qzlx)) {
						if (xmjgm.containsKey(wzdm)) {// 优先使用录入选择的价格（平均价格）
							val = xmjgm.get(wzdm);
						} else {
							val = tr.getItemprice();
						}
					} else if ("cpsl".equals(qzlx) || "khdh".equals(qzlx)) {
						val = tr.getBaseConsumption();
					} else if ("hsxs".equals(qzlx)) {
						val = tr.getConversionfactor();
					} else if ("nhxs".equals(qzlx)) {
						val = tr.getEnergyfactor();
					} else if ("dkhz".equals(qzlx)) {
						val = tr.getApportionmentfactor();
					} else {
						val = 0.0;
					}
				}
				if (val == null) {
					rtn = "0";
				} else {
					rtn = String.valueOf(val);
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 取参数值
	 * @param var
	 * @return
	 */

	private String getValue(String var, TeamReportInputDto reportinfo, CalcTDSParam ctsp, String teamid,
			String unitid) {
		String key;
		String rtn = "0";
		if (this.pm.judgeDouble(var)) {
			rtn = var;// 常数，直接返回
		} else {
			HashMap<String, String> vm = reportinfo.getValm();
			if (vm == null) {
				vm = new HashMap<String, String>();
				reportinfo.setValm(vm);
			}
			HashMap<String, Costindicator> csm = reportinfo.getHszbm();
			if (csm == null) {
				csm = new HashMap<String, Costindicator>();
				reportinfo.setHszbm(csm);
			}
			HashMap<String, CostItemFormula> gm = reportinfo.getGsm();
			if (gm == null) {
				gm = new HashMap<String, CostItemFormula>();
				reportinfo.setGsm(gm);
			}
			if (vm.containsKey(var)) {
				rtn = vm.get(var);
			} else {
				String[] s = var.split("\\.");
				if (s.length == 1) {
					key = var.toLowerCase();
					// 内定参数、核算参数或内置函数（if,round等）
					if ("bzgzsj".equals(key) || "[工作时长]".equals(key)) {
						// 班组工作时间
						if ("".equals(teamid)) {
							// 普通的都是直接取
							rtn = String.valueOf(reportinfo.getGzsc());
						} else {
							// 目前只有班组月汇总表的工作时间是从vm取
							if (vm.containsKey(teamid)) {
								rtn = vm.get(teamid);
							}
						}
					} else if ("bzdbcs".equals(key)) {
						return "0";
					} else if ("mdays".equals(key)) {
						rtn = String.valueOf(this.pm.getMdays(reportinfo.getWriteDay()));
					} else if ("ydays".equals(key)) {
						rtn = String.valueOf(this.pm.getYdays(reportinfo.getWriteDay()));
					} else {
						// 未定义的直接返回
						if (csm.containsKey(var)) {
							key = new StringBuffer(var).append(".param").toString();
							if (gm.containsKey(key)) {
								// 核算参数
								CostItemFormula param = gm.get(key);
								rtn = this.calcFormula(param.getFormula(), reportinfo, unitid, "", "", "", "", ctsp,
										teamid);
							}
						} else {
							return var;
						}
					}
				} else if (s.length == 2) {
					key = new StringBuffer(s[1]).append(".param").toString();
					if (gm.containsKey(key)) {
						// 核算参数
						CostItemFormula param = gm.get(key);
						rtn = this.calcFormula(param.getFormula(), reportinfo, s[0], "", "", "", "", ctsp, teamid);
					}
				} else if (s.length == 3) {
					// 核算项目
					rtn = this.getCostingData(s[1], s[2], reportinfo, teamid, ctsp, unitid);
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 查找并计算成本项目公式返回Double
	 * @param findKey
	 * @return
	 */
	private Double calcformula(String findKey, TeamReportInputDto reportinfo, CalcTDSParam ctsp, String xmid,
			String xmmc, String ybid, String ybmc, String teamid) {
		Double jsz = 0.0;
		HashMap<String, CostItemFormula> gm = reportinfo.getGsm();
		if (gm.containsKey(findKey)) {
			CostItemFormula fb = gm.get(findKey);
			if (fb != null) {
				String gs = this.pm.determineFormula(fb.getFormula());
				String val = this.calcFormula(gs, reportinfo, "", xmid, xmmc, ybid, ybmc, ctsp, teamid);
				jsz = this.pm.convertDouble(val, 0.0);
			}
		}
		return jsz;
	}

	/**
	 * @category 得到批次的当班信息
	 * @param tbrq      填报日期
	 * @param unitid    核算对象ID
	 * @param programid 方案ID
	 * @param bn        批号
	 * @return
	 */
	private List<CostBatchOnDuty> getBatchDutyInfo(String unitid, String bn) {
		Where where = Where.create();
		where.eq(CostBatchOnDuty::getUnitId, unitid);
		where.eq(CostBatchOnDuty::getBatchNo, bn);
		Order order = Order.create();
		order.orderByAsc(CostBatchOnDuty::getBeginTime);
		return entityService.queryList(CostBatchOnDuty.class, where, order);
	}

	/**
	 * @category 得到批次的仪表数据
	 * @param idlist 当班信息的ID
	 * @return
	 */
	private List<CostBatchInstrumentData> getBatchDutyInstrumentData(List<String> idlist) {
		Where where = Where.create();
		where.in(CostBatchInstrumentData::getPid, idlist.toArray());
		return entityService.queryList(CostBatchInstrumentData.class, where, null);
	}

	private List<CostBatchInstrumentData> getBatchInstrumentData(String pid) {
		Where where = Where.create();
		where.eq(CostBatchInstrumentData::getPid, pid);
		return entityService.queryList(CostBatchInstrumentData.class, where, null);
	}

	/**
	 * @category 得到批次计算信息
	 * @param tbrq      填报日期
	 * @param unitid    核算对象ID
	 * @param programid 方案ID
	 * @param bn        批号
	 * @return
	 */
	private CostBatchInfo getBatchCalcInfo(String unitid, String bn) {
		Where where = Where.create();
		where.eq(CostBatchInfo::getUnitId, unitid);
		where.eq(CostBatchInfo::getBatchNo, bn);
		return entityService.queryObject(CostBatchInfo.class, where, null);
	}

	/**
	 * @category 更新批次计算信息
	 * @param data
	 * @return
	 */
	private boolean updateBatchCalcInfo(CostBatchInfo data) {
		boolean rtn = false;
		int bs = entityService.rawUpdateById(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	/**
	 * @category 删除核算对象批次的班组消耗量
	 * @param unitid
	 * @param bn
	 * @return
	 */
	private boolean deleteTeamBatchItemData(String unitid, String bn, HashMap<String, TeamProgramDto> tj) {
		boolean rtn = true;
		Integer bs;
		String tbrq, bzdm, bcdm, faid;
		HashMap<String, String> ys = new HashMap<String, String>();
		Where where = Where.create();
		where.eq(CostTeamBatchItemData::getUnitId, unitid);
		where.eq(CostTeamBatchItemData::getBatchNo, bn);
		List<CostTeamBatchItemData> hsl = entityService.queryList(CostTeamBatchItemData.class, where, null);
		if (hsl != null) {
			// 有历史数据，说明发生了修改，需要处理历史计算数据
			for (CostTeamBatchItemData x : hsl) {
				tbrq = x.getWriteDay();
				bzdm = x.getTeamId();
				bcdm = x.getShiftId();
				faid = x.getProgramId();
				String sb = (new StringBuffer(tbrq).append(".").append(unitid).append(".").append(bzdm).append(".")
						.append(bcdm).append(".").append(faid)).toString();
				if (ys.containsKey(sb)) {
					continue;
				} else {
					// 添加的map,同样的班次只添加一次
					ys.put(sb, "1");
					// 给计算使用的
					TeamProgramDto trid = new TeamProgramDto();
					trid.setUnitId(unitid);
					trid.setProgramId(faid);
					trid.setWriteDay(tbrq);
					trid.setTeamId(bzdm);
					trid.setShiftId(bcdm);
					trid.setShiftBeginTime(x.getShiftBeginTime());
					trid.setShiftEndTime(x.getShiftEndTime());
					trid.setSummaryDay(x.getSummaryDay());
					tj.put(sb, trid);// 添加到待计算列表
				}
			}
		}
		bs = entityService.deleteByIdBatch(hsl);
		if (bs < 0) {
			return false;
		}
		return rtn;
	}

	private CostTeamInfo getTeamDayCalcInfo(String unitid, String tbrq, String faid, String bzdm, String bcdm) {
		if (faid == null && "".equals(faid)) {
			faid = "0";
		}
		if (bzdm == null && "".equals(bzdm)) {
			bzdm = "0";
		}
		if (bcdm == null && "".equals(bcdm)) {
			bcdm = "0";
		}
		Where where = Where.create();
		where.eq(CostTeamInfo::getWriteDay, tbrq);
		where.eq(CostTeamInfo::getUnitId, unitid);
		where.eq(CostTeamInfo::getProgramId, faid);
		where.eq(CostTeamInfo::getShiftId, bcdm);
		where.eq(CostTeamInfo::getTeamId, bzdm);
		return entityService.queryObject(CostTeamInfo.class, where, null);
	}

	private List<ShiftCostCalcTask> getCalcInfo(TeamReportInputDto info) {
		Where where = Where.create();
		where.eq(ShiftCostCalcTask::getWriteDay, info.getWriteDay());
		return entityService.queryData(ShiftCostCalcTask.class, where, null, null);
	}

	/**
	 * @category 得到汇总表的制表信息
	 * @param reportno  编号：有日期、月份和月份+编号的形式
	 * @param unitid
	 * @param programid
	 * @return
	 */
	private CostSummaryInfo getDeviceDayCalcInfo(String reportno, String unitid, String faid, String teamid) {
		Where where = Where.create();
		where.eq(CostSummaryInfo::getReportNo, reportno);
		where.eq(CostSummaryInfo::getUnitId, unitid);
		where.eq(CostSummaryInfo::getProgramId, faid);
		if (teamid != null && !"".equals(teamid)) {
			where.eq(CostSummaryInfo::getTeamId, teamid);
		}
		return entityService.queryObject(CostSummaryInfo.class, where, null);
	}

	/**
	 * @category 保存班组批次数据
	 * @param data
	 * @return
	 */
	private boolean insertTeamBatchItemData(List<CostTeamBatchItemData> data) {
		boolean rtn = false;
		int bs = entityService.insertBatch(data);
		if (bs > 0) {// 保存成功
			rtn = true;
		}
		return rtn;
	}

	private List<CostSummaryItemData> getDeviceDayItemData(String pid) {
		Where where = Where.create();
		where.eq(CostSummaryItemData::getPid, pid);
		return entityService.queryList(CostSummaryItemData.class, where, null);
	}

	private boolean updateDeviceDayItemData(List<CostSummaryItemData> data) {
		int rs = entityService.updateByIdBatch(data);
		if (rs < 0) {
			return false;
		} else {
			return true;
		}
	}

}
