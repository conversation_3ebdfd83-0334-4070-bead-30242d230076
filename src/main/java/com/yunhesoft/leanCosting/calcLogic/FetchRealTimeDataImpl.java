package com.yunhesoft.leanCosting.calcLogic;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.core.common.aviator.AviatorResult;
import com.yunhesoft.core.common.aviator.AviatorUtils;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostStipulateTime;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostItemInfoVo;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.system.tds.service.IRtdbService;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class FetchRealTimeDataImpl implements IFetchRealTimeData {

	@Autowired
	UnitItemInfoService uiif;

	@Autowired
	IUnitMethodService iims;

	@Autowired
	private IRtdbService rtdbSrv;

	private PublicMethods pm = new PublicMethods();

	/** 获取交接班的实时数据：给前台获取数据使用 */
	@Override
	public String getShiftDataWithJson(FetchRealTimeDataDTO info) {
		String rtn = "";
		collectData(info);
		List<FetchRealTimeDataVo> pl = info.getPl();
		if (pl != null && pl.size() > 0) {
			return JSONArray.toJSONString(pl);
		}
		return rtn;
	}

	/** 获取交接班的实时数据：给后台获取数据使用 */
	@Override
	public HashMap<String, List<FetchRealTimeDataVo>> getShiftDataWithMap(FetchRealTimeDataDTO info) {
		HashMap<String, List<FetchRealTimeDataVo>> rtn = new HashMap<String, List<FetchRealTimeDataVo>>();
		collectData(info);
		List<FetchRealTimeDataVo> pl = info.getPl();
		if (pl != null) {
			String ybid;
			for (FetchRealTimeDataVo x : pl) {
				ybid = x.getInstrumentId();
				if (rtn.containsKey(ybid)) {
					rtn.get(ybid).add(x);
				} else {
					List<FetchRealTimeDataVo> l = new ArrayList<FetchRealTimeDataVo>();
					l.add(x);
					rtn.put(ybid, l);
				}
			}
		}
		return rtn;
	}

	/** 获取给定时间点的数据 */
	@Override
	public HashMap<String, FetchRealTimeDataVo> getTimeDataWithMap(FetchRealTimeDataDTO info) {
		HashMap<String, FetchRealTimeDataVo> rtn = new HashMap<String, FetchRealTimeDataVo>();

		return rtn;
	}

	/**
	 * @category 获得实时数采数据
	 * @param zzdm      装置
	 * @param tbrq      填表日期
	 * @param beginTime 开始时间
	 * @param endTime   结束时间
	 * @return
	 */

	private void collectData(FetchRealTimeDataDTO info) {
		String unitid = info.getUnitId();// 核算对象ID
		String tbrq = info.getTbrq();// 交接班日期
		String kssj = info.getKssj();// 开始时间
		String jzsj = info.getJzsj();// 截止时间
		String tenantId = info.getTENANT_ID();// 租户ID
		CostItemInfoVo cjvo = uiif.getFetchInfo(unitid, tbrq, tenantId);// 采集信息
		Date xdsj = DateTimeUtils.parseDateTime(jzsj);// 相对时间：下班时间 - 提前时间

		String rdb;
		Integer yblx;
		String taglist0 = "";
		String taglist4 = "";
		String ssyb = "";
		List<FetchRealTimeDataVo> ybl = cjvo.getFiList();
		List<FetchRealTimeDataVo> tdsList = new ArrayList<FetchRealTimeDataVo>();// 数据源类型的实时仪表
		if (ybl != null) {
			// 归纳仪表的采集类型
			for (FetchRealTimeDataVo x : ybl) {
				rdb = x.getSjly();
				if (StringUtils.isEmpty(rdb)) {
					rdb = "1";// 默认是influxdb
				}
				if ("2".equals(rdb)) {// 数据源公式类型，需要另外解析
					tdsList.add(x);// 添加到解析列表
				}
			}
			// 获取数据源仪表值
			List<FetchRealTimeDataVo> tdsData = collectTdsData(tdsList, info);
			if (tdsData != null && tdsData.size() > 0) {
				info.getPl().addAll(tdsData);// 将数据源仪表值添加到返回列表
			}
			Date dt = DateTimeUtils.getNowDate();// 当前时间
			Date dt1;
			Date dt2;
			String bt = "";
			if (DateTimeUtils.bjDate(xdsj, dt) == 1) {
				bt = DateTimeUtils.formatDateTime(dt);// 当前时间小于相对时间时，结束时间使用当前时间
			} else {
				bt = DateTimeUtils.formatDateTime(xdsj);// 使用相对时间
			}

			String st[] = new String[5];
			String et[] = new String[5];
			HashMap<String, List<String>> hTimePoint = new HashMap<String, List<String>>();// 暂存规定时间的时间段
			List<String> yblist = null;
			String sTimePoint = "";
			List<FetchRealTimeDataVo> tempList = new ArrayList<FetchRealTimeDataVo>();// 暂存待获取数据仪表的List
			// 1 累计仪表 2 规定时间 3 人工清零
			st[0] = "";
			st[4] = "";
			et[0] = "";
			et[4] = "";
			taglist0 = "";
			taglist4 = "";
			for (FetchRealTimeDataVo fi : ybl) {
				rdb = fi.getSjly();
				if (StringUtils.isEmpty(rdb)) {
					rdb = "1";// 默认是influxdb
				}
				if ("1".equals(rdb)) {
					yblx = fi.getYblx();
					if (yblx == null) {
						yblx = 1;
					}
					ssyb = fi.getTagNumber();// 实时仪表
					if (StringUtils.isEmpty(ssyb)) {
						continue;// 没有配置实时仪表的不获取
					}
					tempList.add(fi);
					if (yblx == 1) {
						// 累计仪表，只读取当前交班前10分钟的数据
						taglist0 = taglist0 + ssyb + ";";
						if ("".equals(st[0])) {
							// 判断时间，只读取相对时间前10分钟的数据
							dt2 = DateTimeUtils.parseDateTime(bt);
							dt1 = DateTimeUtils.doSecond(dt2, -600);
							st[0] = DateTimeUtils.formatDateTime(dt1);
							et[0] = DateTimeUtils.formatDateTime(dt2);
						}
					}
					if (yblx == 2) {
						// 规定时间
						sTimePoint = getObjFixTime(info, fi.getInstrumentId(), kssj, jzsj, tbrq, unitid, dt);// 得到时间点和时间段
						if (!"".equals(sTimePoint)) {// 有设置
							if (hTimePoint.containsKey(sTimePoint)) {// 时间点被暂存过了
								yblist = hTimePoint.get(sTimePoint);// 读取暂存数据
							} else {
								yblist = new ArrayList<String>();// 新建一个list暂存数据
							}
							if (!yblist.contains(ssyb)) {
								yblist.add(ssyb);// 向List里填入实时仪表，留作采集使用
							}
							hTimePoint.put(sTimePoint, yblist);
						}
					}
					if (yblx == 3) {
						// 人工清零仪表
						taglist4 = taglist4 + ssyb + ";";
						if ("".equals(st[4])) {
							String sjfw = "60";
							dt1 = DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(bt), -Integer.valueOf(sjfw) * 60);
							st[4] = DateTimeUtils.formatDateTime(dt1);
							et[4] = bt;
						}
					}
				}
			}
			List<Tag> tempData;
			if (!("".equals(taglist0))) {
				// 采集累计仪表与瞬时仪表
				try {
					tempData = getTagData(st[0], et[0], taglist0);
					if (tempData != null) {// 成功获取数据
						dealWithFetchDataByInstrument(tempData, info);
					}
				} catch (Exception e) {
					// 代理服务器返回异常，为错误赋值并结束采集
					info.setErrInfo(taglist0);
					return;
				}
			}
			if (!("".equals(taglist4))) {
				// 人工清零
				try {
					tempData = getTagData(st[4], et[4], taglist4);
					if (tempData != null) {// 成功获取数据
						dealWithFetchDataByInstrument(tempData, info);
					}
				} catch (Exception e) {
					// 代理服务器返回异常，为错误赋值并结束采集
					info.setErrInfo(taglist4);
					return;
				}
			}
			if (hTimePoint.size() > 0) {
				// 获取规定时间的数据
				for (Entry<String, List<String>> m : hTimePoint.entrySet()) {
					// 分时间点来采集数据
					sTimePoint = m.getKey();
					yblist = m.getValue();
					String[] arr = sTimePoint.split(",");// 得到起始和截止采集时间
					try {
						tempData = getTagData(arr[0], arr[1], yblist);
						if (tempData != null) {
							dealWithFetchDataByInstrument(tempData, info);
						}
					} catch (Exception e) {
						// 代理服务器返回异常，为错误赋值并结束采集
						info.setErrInfo(listToString(yblist, ","));
						return;
					}
				}
			}
			String ybid;
			HashMap<String, Date> iTimePoint = info.getITimePoint();
			HashMap<String, List<FetchRealTimeDataVo>> fetchData = info.getDm();
			if (fetchData.size() > 0 || iTimePoint.size() > 0) {
				// 根据仪表类型加工采集回来的数据
				for (FetchRealTimeDataVo tmp : tempList) {
					ybid = tmp.getInstrumentId();
					yblx = tmp.getYblx();// 获得当前仪表的类型
					if (yblx == null) {
						yblx = 1;
					}
					if (yblx == 1) {
						// 累计仪表
						FetchRealTimeDataVo d = this.getLJYBValue(ybid, tmp.getTagNumber().toUpperCase(), fetchData);
						if (d != null) {
							info.getPl().add(d);
						}
					} else if (yblx == 2) {
						// 规定时间
						if (iTimePoint.containsKey(ybid)) {
							FetchRealTimeDataVo Fdata = getFixTimeYBValue(ybid, tmp.getTagNumber().toUpperCase(),
									iTimePoint.get(ybid), fetchData);
							if (Fdata != null) {
								info.getPl().add(Fdata);
							}
						}
					} else if (yblx == 3) {
						// 人工清零仪表
						info.getPl().addAll(getQLYBValue(ybid, tmp.getTagNumber().toUpperCase(), fetchData));
					}
				}
			}
		}
	}

	/**
	 * 获取数据源类型的仪表数据
	 * 
	 * @category 获取数据源类型的仪表数据介
	 * @param zzdm      装置代码
	 * @param tbrq      提前
	 * @param beginTime 开始时间
	 * @param endTime   截止时间
	 * @param bcdm      班次代码
	 */
	private List<FetchRealTimeDataVo> collectTdsData(List<FetchRealTimeDataVo> tdsList, FetchRealTimeDataDTO info) {
		List<FetchRealTimeDataVo> result = new ArrayList<FetchRealTimeDataVo>();
		CalcTDSParam ctsp = new CalcTDSParam("", info.getTbrq(), info.getKssj(), info.getJzsj());
		if (tdsList != null) {
			for (FetchRealTimeDataVo temp : tdsList) {
				String realData = temp.getTagNumber();// 数据源公式
				Object defaultVal = this.calcFormula(realData, info, ctsp, temp.getItemId(), temp.getItemName(),
						temp.getInstrumentId(), temp.getInstrumentName());// 解析脚本
				result.add(setDataBean(temp.getInstrumentId(), realData, "", String.valueOf(defaultVal), "0.0"));
			}
		}
		return result;
	}

	private String calcFormula(String sgs, FetchRealTimeDataDTO info, CalcTDSParam ctsp, String xmid, String xmmc,
			String ybid, String ybmc) {
		String calcResult = "";
		String objid = info.getUnitId(), objmc = "";
		Map<String, List<String>> resMap = AviatorUtils.getAllParams(sgs);
		List<String> varList = resMap.get("var");
		// 所有变量列表（不包含数据源）
		List<String> tdsList = resMap.get("tds");
		// 数据源列表（数据源别名，含$）
		List<String> tdsFormulaList = resMap.get("tdsFormula");
		// 数据源公式列表
		Map<String, Object> valueMap = new HashMap<>();
		for (String var : varList) { // 获取变量值并赋值
			valueMap.put(var, 0.0);
		}
		Map<String, Object> tdsValueMap = new HashMap<>();
		if (tdsList != null && tdsList.size() > 0) {
			TdsFormulaParamDTO p = new TdsFormulaParamDTO();
			p.setUnitId(objid);
			p.setUnitName(objmc);
			p.setKssj(info.getKssj());
			p.setJzsj(info.getJzsj());
			p.setTbrq(info.getTbrq());
			p.setShiftId(info.getShiftId());
			p.setShiftName("");
			p.setTeamId(info.getTeamId());
			p.setTeamName("");
			p.setTENANT_ID("");
			p.setXmid(xmid);
			p.setXmmc(xmmc);
			p.setYbid(ybid);
			p.setYbmc(ybmc);
			ctsp.TDSRetrieve(tdsList, p);
			for (String param : tdsFormulaList) {
				Object tdsValue = null;
				try {
					p.setXmid(param);
					tdsValue = ctsp.replaceDsFormula(param, p);
				} catch (Exception e) {
				}
				tdsValueMap.put(param, tdsValue);
			}
		}
		try {
			AviatorResult ar = AviatorUtils.execute(sgs, valueMap, tdsValueMap);
			calcResult = String.valueOf(ar.getResult());
		} catch (Exception e) {
			calcResult = "0";
		}
		return calcResult;
	}

	/**
	 * @category 将以separator分割的字符串listText变成List<String>
	 * @param listText  要转换的字符串
	 * @param separator 使用的分割符
	 * @return List<String>
	 */
	private List<String> stringToList(String listText, String separator) {
		List<String> rtn = new ArrayList<String>();
		String[] arr = listText.split(separator);// 先变成数组
		for (String x : arr) {
			if (rtn.contains(x)) {
				continue;
			} else {
				rtn.add(x);
			}
		}
		return rtn;
	}

	/**
	 * @category 将List<String>变成以separator分割的字符串
	 * @param listText
	 * @param separator
	 * @return
	 */
	private String listToString(List<String> listText, String separator) {
		// 通过每次判断初始字符串是否为空来保证分割符“,”的合适
		String strNames = "";
		for (String strOne : listText) {
			if ("".equals(strNames)) {
				strNames = strOne;// 第一次
			} else {
				strNames = strNames + "," + strOne;

			}
		}
		return strNames;
	}

	/**
	 * @category 检索给定时间
	 * @param unitId 核算对象
	 * @param tbrq   检索日期
	 * @return
	 */
	private HashMap<String, HashMap<String, CostStipulateTime>> getFixTimeConfig(String unitId, String tbrq) {
		HashMap<String, HashMap<String, CostStipulateTime>> rtn = new HashMap<String, HashMap<String, CostStipulateTime>>();
		// 先得到给定日期,每个仪表的最后规定时间设置
		List<CostStipulateTime> fixtimeconfig = this.iims.getCostStipulateTimeListByRedis(unitId, tbrq);
		if (fixtimeconfig != null) {
			String ybid, newdate, olddate, shiftId;
			for (CostStipulateTime x : fixtimeconfig) {
				ybid = x.getPid();
				shiftId = x.getShiftClassCode();
				if (rtn.containsKey(ybid)) {
					HashMap<String, CostStipulateTime> ybc = rtn.get(ybid);
					if (ybc.containsKey(shiftId)) {
						CostStipulateTime c = ybc.get(shiftId);
						newdate = x.getBegintime();
						olddate = c.getBegintime();
						if (DateTimeUtils.bjDate(DateTimeUtils.parseDate(newdate),
								DateTimeUtils.parseDate(olddate)) >= 0) {
							// 新日期比旧的晚，使用新日期的配置
							ybc.put(shiftId, x);
						}
					} else {
						ybc.put(shiftId, x);
					}
					rtn.put(ybid, ybc);
				} else {
					HashMap<String, CostStipulateTime> ybc = new HashMap<String, CostStipulateTime>();
					ybc.put(shiftId, x);
					rtn.put(ybid, ybc);
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 获取规定时间仪表的规定时间点和采集时间段
	 * @param wzdm 物资代码
	 * @param ybwh 仪表
	 * @param sbsj 上班时间
	 * @param xbsj 下班时间
	 * @param tbrq 填表日期
	 * @param bcdm 当班班次
	 * @return String 采集时间段，如果没有时间段认为是没有设置
	 */
	private String getObjFixTime(FetchRealTimeDataDTO info, String ybid, String sbsj, String xbsj, String tbrq,
			String unitid, Date curtime) {
		String rtn = "";
		Date dTimePoint = null;
		String shiftid = info.getShiftId();
		Integer fixTime = info.getFixTime();
		HashMap<String, HashMap<String, CostStipulateTime>> timeConfig = info.getFixTimeConfig();
		if (timeConfig == null) {
			timeConfig = new HashMap<String, HashMap<String, CostStipulateTime>>();
		}
		if (fixTime == null) {
			fixTime = 0;
		}
		if (fixTime == 0) {
			// 没有检索过
			timeConfig = this.getFixTimeConfig(unitid, tbrq);
			info.setFixTimeConfig(timeConfig);
			info.setFixTime(1);// 检索一次后不再检索
		}
		if (timeConfig.containsKey(ybid)) {
			// 有设置，将设置转成时间段
			HashMap<String, CostStipulateTime> cm = timeConfig.get(ybid);
			if (cm.containsKey(shiftid)) {
				CostStipulateTime config = cm.get(shiftid);
				Integer t = config.getStipulateType();
				if (t == null) {
					t = 3;// 默认是下班之前
				}
				Integer gdsj = config.getStipulateTime();
				if (gdsj == null) {
					gdsj = 0;
				}
				switch (t) {
				case 1:
					// 上班时间之前
					dTimePoint = DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(sbsj), -(gdsj * 60));
					break;
				case 2:
					// 上班时间之后
					dTimePoint = DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(sbsj), gdsj * 60);
					break;
				case 3:
					// 下班时间之前
					dTimePoint = DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(xbsj), -(gdsj * 60));
					break;
				case 4:
					// 下班时间之后
					dTimePoint = DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(xbsj), gdsj * 60);
					break;
				default:
					// 没有设置
					break;
				}
				if (dTimePoint != null) {
					// 时间点不是空值
					Date bt;
					if (DateTimeUtils.bjDate(dTimePoint, curtime) == 1) {
						bt = curtime;// 当前时间小于规定时间时，规定时间使用当前时间
					} else {
						bt = dTimePoint;// 使用规定时间
					}
					Integer fw = config.getDeviationTime();
					if (fw == null) {
						fw = 0;
					}
					// 得到采集的起始时间和截止时间
					info.getITimePoint().put(ybid, bt);// 暂存规定时间点
					rtn = DateTimeUtils.formatDateTime(DateTimeUtils.doSecond(bt, -(fw * 60))) + ","
							+ DateTimeUtils.formatDateTime(DateTimeUtils.doSecond(bt, fw * 60));
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 读取实时数据并分析数据
	 * @param beginTime 开始时间
	 * @param endTime   截止时间
	 * @param list      待采仪表的信息
	 * @return
	 */
	private List<Tag> getTagData(String beginTime, String endTime, List<String> list) {
		List<Tag> data;
		if (list.size() == 0) {
			return null;// 没有仪表时，返回空值
		} else {
			try {
				log.info(list.stream().collect(Collectors.joining(",")));
				log.info(beginTime);
				log.info(endTime);
				data = this.rtdbSrv.queryRtdbTagData(list, beginTime, endTime, 60);
			} catch (Exception e) {
				return null;// 代理服务器出错，返回空值
			}
		}
		return data;
	}

	/**
	 * @category 读取实时数据并分析数据
	 * @param beginTime 开始时间
	 * @param endTime   截止时间
	 * @param yblist    待采仪表列表
	 * @return
	 */
	private List<Tag> getTagData(String beginTime, String endTime, String yblist) {
		List<Tag> data;
		/// 数组转成List<String>
		if (yblist == null) {
			return null;// 没有仪表时，返回空值
		} else {
			try {
				log.info(yblist);
				log.info(beginTime);
				log.info(endTime);
				List<String> list = stringToList(yblist, ";");// 把字符串转成List
				data = this.rtdbSrv.queryRtdbTagData(list, beginTime, endTime, 60);
			} catch (Exception e) {
				return null;// 代理服务器出错，返回空值
			}
		}
		return data;
	}

	/**
	 * @category 把处理后的采集数据暂存到DataCollectionBean
	 */
	private FetchRealTimeDataVo setDataBean(String ybid, String tagNumber, String sj, String val, String bysj) {
		FetchRealTimeDataVo rtn = new FetchRealTimeDataVo();
		rtn.setInstrumentId(ybid);
		rtn.setTagNumber(tagNumber);
		rtn.setTime(sj);
		rtn.setValue(val);
		rtn.setLastValue(bysj);
		return rtn;
	}

	/**
	 * @category 处理累计仪表
	 * @param wzdm
	 * @param ybwh     仪表
	 * @param realData 数采仪表（转成大写）
	 * @return DataCollectionBean
	 */
	private FetchRealTimeDataVo getLJYBValue(String ybwh, String realData,
			HashMap<String, List<FetchRealTimeDataVo>> fetchData) {
		String val = "0";
		String sj = null, sj2;
		// 得到仪表的累计值
		if (fetchData.containsKey(realData)) {
			// 有realData的采样值
			List<FetchRealTimeDataVo> data = fetchData.get(realData);
			if (data != null) {
				for (FetchRealTimeDataVo m : data) {
					// 循环读取采样值，获取时间最大值的数据
					sj2 = m.getTime();
					if (sj == null) {
						sj = sj2;
						val = m.getValue();
					} else {
						if (DateTimeUtils.bjDate(DateTimeUtils.parseDateTime(sj2),
								DateTimeUtils.parseDateTime(sj)) >= 0) {
							sj = sj2;
							val = m.getValue();
						}
					}
				}
			}
		}
		if (StringUtils.isNotEmpty(sj)) {
			return setDataBean(ybwh, realData, sj, val, "0.0");
		} else {
			return null;
		}
	}

	/**
	 * @category 获取清零仪表的数据
	 * @param wzdm
	 * @param ybwh
	 * @param realData （转成大写）
	 * @return
	 */
	private List<FetchRealTimeDataVo> getQLYBValue(String ybid, String realData,
			HashMap<String, List<FetchRealTimeDataVo>> fetchData) {
		Double val = null;
		Double val1 = 0.0;
		Date sj = null;
		Date dt1 = null;// 用于比较的时间
		Date pdt = null;// 上一采集点的时间
		String fval = null;
		List<FetchRealTimeDataVo> rtn = new ArrayList<FetchRealTimeDataVo>();
		// 得到仪表的累计值
		if (fetchData.containsKey(realData)) {
			// 有realData的采样值
			List<FetchRealTimeDataVo> data = fetchData.get(realData);
			if (data.size() > 0) {
				for (FetchRealTimeDataVo m : data) {
					// 循环读取采样值重新组织数据
					sj = DateTimeUtils.parseDateTime(m.getTime());
					fval = m.getValue();
					if (dt1 == null || Math.abs(DateTimeUtils.diffSecond(sj, dt1)) >= 360) {
						val1 = this.pm.convertDouble(fval, 0.0);
						if (val == null) {
							// 第一个采集数据
							val = val1;
							pdt = sj;
						}
						if (val > val1) {
							// 后表小于前表了，出现了人工清零的情况
							if (rtn.size() > 0) {
								rtn.add(setDataBean(ybid, realData, DateTimeUtils.formatDateTime(pdt),
										String.valueOf(val), "-20000"));// 后面的按照累次录入
							} else {
								rtn.add(setDataBean(ybid, realData, DateTimeUtils.formatDateTime(pdt),
										String.valueOf(val), "-10000"));// 第一次按正常处理
							}
							dt1 = pdt;
							val = null;
							continue;
						}
						val = val1;
						pdt = sj;
					}
				}
				if (dt1 == null) {
					// 未清零，正常处理
					rtn.add(setDataBean(ybid, realData, DateTimeUtils.formatDateTime(sj), fval, "-10000"));
				} else {
					if ((!dt1.equals(sj))) {
						// 结束时间不被记录两次
						if (rtn.size() > 0) {
							// 有人工清零
							rtn.add(setDataBean(ybid, realData, DateTimeUtils.formatDateTime(sj), fval, "-20000"));
						} else { // 未清零，正常处理
							rtn.add(setDataBean(ybid, realData, DateTimeUtils.formatDateTime(sj), fval, "-10000"));
						}
					}
				}
			}
		}
		return rtn;
	}

	/**
	 * @category 班累计值
	 * @param wzdm
	 * @param ybwh     仪表
	 * @param lslx     仪表流速类型
	 * @param realData 数采仪表
	 * @return DataCollectionBean
	 */
	/*
	 * private FetchRealTimeDataVo getSumYBValue(String wzdm, String ybwh, int lslx,
	 * String realData) { double val = 0.0; double fetchVal = 0.0; int lx = 3600;
	 * Date sj1 = null; Date sj2 = null; if (lslx == 2) { lx = 60; } // 流速仪表 if
	 * (fetchData.containsKey(realData)) { // 有realData的采样值 LinkedHashMap<Date,
	 * Double> data = fetchData.get(realData); if (data.size() > 0) { for
	 * (Map.Entry<Date, Double> m : data.entrySet()) { fetchVal = m.getValue(); if
	 * (sj1 == null) { sj1 = m.getKey(); } sj2 = m.getKey(); //
	 * cz=Dates.diffSec(sj1, sj2); val = val + fetchVal * lx; sj1 = sj2; } } }
	 * return setDataBean(wzdm, ybwh, Dates.formatDateTime(sj2), val, 0.0); }
	 */

	/**
	 * @category 班平均值
	 * @param wzdm
	 * @param ybwh     仪表
	 * @param realData 数采仪表
	 * @return DataCollectionBean
	 */
	/*
	 * private FetchRealTimeDataVo getAvgYBValue(String wzdm, String ybwh, String
	 * realData, String rel_ssyb, int timeInterval) { double val = 0.0; Date sj1 =
	 * null; int cjCount = 0; double fetchVal = 0.0; double cjVal = 0.0; String
	 * relVal = ""; double gsValue = 0.0; if (fetchData.containsKey(realData)) { //
	 * 有realData的采集数据 LinkedHashMap<Date, Double> self = fetchData.get(realData); if
	 * ("".equals(rel_ssyb)) { // 没有关联仪表，直接使用采集值求平均 for (Map.Entry<Date, Double> m :
	 * self.entrySet()) {// 循环仪表采集数据 cjVal = m.getValue(); sj1 = m.getKey();
	 * fetchVal = fetchVal + cjVal; cjCount = cjCount + 1; } } else { //
	 * 有关联仪表，需要判断一下是否满足条件然后求平均 List<String> ybList = new ArrayList<String>();//
	 * 得到公式中包含的仪表 if (ybFormula.containsKey(rel_ssyb)) { ybList =
	 * ybFormula.get(rel_ssyb); String gs = ""; String tempGs = ""; if
	 * (convertFormula.containsKey(rel_ssyb)) {// 得到已分析的公式 gs =
	 * convertFormula.get(rel_ssyb); } Date sj = null; int secondInterval =
	 * timeInterval * 60; TJSEngine tjs = new TJSEngine(); for (Map.Entry<Date,
	 * Double> m : self.entrySet()) {// 循环仪表采集数据 sj1 = m.getKey(); if (sj == null ||
	 * Dates.bjDate(sj1, sj) >= 0) { sj = Dates.doSecond(sj1, secondInterval); cjVal
	 * = m.getValue(); tempGs = gs.replace("fetchTime()", "'" +
	 * Dates.formatDateTime(sj1) + "'");// 替换公式里的采集时间 // 为公式参数赋值 for (String yb :
	 * ybList) {// 用采集数据替换公式内的仪表 if (fetchData.containsKey(yb)) { // 采样时间下有仪表数据
	 * LinkedHashMap<Date, Double> rel = fetchData.get(yb);// 公式仪表的数据 if
	 * (rel.containsKey(sj1)) { // 在时间点下有数据 relVal = String.valueOf(rel.get(sj1));
	 * tempGs = tempGs.replace(yb, relVal); } else { tempGs = tempGs.replace(yb,
	 * "0");// 没有采集值，按0处理 } } else { // 无数据 tempGs = tempGs.replace(yb, "0");//
	 * 没有采集值，按0处理 } } try { String valx = tjs.eval(tempGs)+"";// 得到公式值 if
	 * (Coms.judgeDouble(valx)) { gsValue=Double.parseDouble(valx); } } catch
	 * (ScriptException e) { e.printStackTrace(); System.out.println("获取实时数据错误1");
	 * gsValue = 0; } if (gsValue == 1) { // 公式满足条件，使用采样值 fetchVal = fetchVal +
	 * cjVal; cjCount = cjCount + 1; } } } } else { // 没有设置有效的关联时，按照没有设置关联处理 for
	 * (Map.Entry<Date, Double> m : self.entrySet()) {// 循环仪表采集数据 cjVal =
	 * m.getValue(); sj1 = m.getKey(); fetchVal = fetchVal + cjVal; cjCount =
	 * cjCount + 1; } } } } // 计算平均值 if (cjCount > 0) { val = fetchVal / cjCount; }
	 * return setDataBean(wzdm, ybwh, Dates.formatDateTime(sj1), val, 0.0); }
	 */

	/**
	 * @category 班最大值
	 * @param wzdm
	 * @param ybwh     仪表
	 * @param realData 数采仪表
	 * @return DataCollectionBean
	 */
	/*
	 * private FetchRealTimeDataVo getMaxYBValue(String wzdm, String ybwh, String
	 * realData) { Double val = null; double fetchVal = 0.0; Date sj = null; if
	 * (fetchData.containsKey(realData)) { // 有realData的采样值 LinkedHashMap<Date,
	 * Double> data = fetchData.get(realData); if (data.size() > 0) { for
	 * (Map.Entry<Date, Double> m : data.entrySet()) { fetchVal = m.getValue(); if
	 * (val == null || fetchVal > val) {// 第一次或采集值大于上一次的值 sj = m.getKey(); val =
	 * fetchVal; } } } } if (val == null) { return setDataBean(wzdm, ybwh, "", 0,
	 * 0.0); } else { return setDataBean(wzdm, ybwh, Dates.formatDateTime(sj), val,
	 * 0.0); } }
	 */

	/**
	 * @category 液位仪表
	 * @param wzdm
	 * @param ybwh     仪表
	 * @param realData 数采仪表
	 * @param data     数采数据
	 * @return DataCollectionBean
	 */
	/*
	 * private List<FetchRealTimeDataVo> getYWYBValue(String wzdm, String ybwh, int
	 * lslx, String realData) { List<FetchRealTimeDataVo> rtn = new
	 * ArrayList<FetchRealTimeDataVo>(); Double lVal = null; Double dVal = null;
	 * double val1 = 0; double val2 = 0; int zl = 0; int lx = 0;// 0 无变化 1 变化中 if
	 * (fetchData.containsKey(realData)) { // 有realData的采集数据 LinkedHashMap<Date,
	 * Double> data = fetchData.get(realData); int count = data.size(); for
	 * (Map.Entry<Date, Double> m : data.entrySet()) { zl = zl + 1; dVal =
	 * m.getValue(); if (lVal == null) { lVal = dVal; } if (lVal != dVal && zl <
	 * count) { lx = 1; val1 = lVal; } else { if (lx == 1) { val2 = dVal; //
	 * 没有仪表类型的仪表按照1处理 switch (lslx) { case 2: // 只记录下降液位变化 if (val2 < val1) {
	 * rtn.add(setDataBean(wzdm, ybwh, Dates.formatDateTime(m.getKey()), val1,
	 * val2)); } break; case 3: // 只记录上升液位变化 if (val2 > val1) {
	 * rtn.add(setDataBean(wzdm, ybwh, Dates.formatDateTime(m.getKey()), val1,
	 * val2)); } break; default: // 液位只要变化就记录 rtn.add(setDataBean(wzdm, ybwh,
	 * Dates.formatDateTime(m.getKey()), val1, val2)); break; } lx = 0;// 结束变化 }
	 * else { lx = 0;// 恢复变化之前 } } lVal = dVal; } } return rtn; }
	 */

	/**
	 * @category 循环仪表
	 * @param wzdm
	 * @param ybwh     仪表
	 * @param realData 数采仪表
	 * @return DataCollectionBean
	 */
	/*
	 * private List<FetchRealTimeDataVo> getCycleYBValue(String wzdm, String ybwh,
	 * String realData) { List<FetchRealTimeDataVo> rtn = new
	 * ArrayList<FetchRealTimeDataVo>(); Double uval = null; Double lval = null;
	 * double val = 0; double val1 = 0; Date sj = null; if
	 * (fetchData.containsKey(realData)) { // 有 realData的采集数据 LinkedHashMap<Date,
	 * Double> data = fetchData.get(realData); int count = data.size(); int zl = 0;
	 * for (Map.Entry<Date, Double> m : data.entrySet()) { zl = zl + 1; val =
	 * m.getValue(); sj = m.getKey(); if (lval == null) { // 初始化值 lval = val; uval =
	 * val; } if (val < uval || zl == count) { // 如果当前数据小于上一个数据，说明归零(错误判断未判断),或者循环到尾
	 * val1 = val1 + uval - lval; } else { uval = val; } } }
	 * rtn.add(setDataBean(wzdm, ybwh, Dates.formatDateTime(sj), val1, 0.0)); return
	 * rtn; }
	 */

	/**
	 * @category 获取规定时间的仪表采集值，一般是LIMS数据
	 * @param wzdm      物资代码
	 * @param ybwh      仪表位号
	 * @param realData  实时仪表（转成大写）
	 * @param timePoint 规定的时间点
	 * @return
	 */
	private FetchRealTimeDataVo getFixTimeYBValue(String ybid, String realData, Date timePoint,
			HashMap<String, List<FetchRealTimeDataVo>> fetchData) {
		long minTimeValue = -100;
		long timeValue = 0;
		Date minTime = null;
		Date sj = null;
		String val = "0";
		if (fetchData.containsKey(realData)) {
			// 有 realData的采集数据
			List<FetchRealTimeDataVo> data = fetchData.get(realData);
			for (FetchRealTimeDataVo m : data) {
				// 仪表的数据
				sj = DateTimeUtils.parseDateTime(m.getTime());
				// 采集时间
				timeValue = Math.abs(DateTimeUtils.diffSecond(sj, timePoint));
				// 采集与时间点的差值
				if (minTimeValue < 0 || minTimeValue > timeValue) {
					// 第一次或者差值比最小值还小
					val = m.getValue();
					minTime = sj;
					minTimeValue = timeValue;
				}
			}
		} else { // 没有数据时的处理方法
			// List<String> data = sql.getSBData(zzType, ptbrq, pbcdm, wzdm, ybwh);
			// if (data.size() > 0) {
			// 有返回数据
			// minTimeValue = 100;
			// val = Double.valueOf(data.get(0));
			// minTime = Dates.parseDateTime(data.get(1));
			// }
		}
		if (minTimeValue >= 0) {
			return setDataBean(ybid, realData, DateTimeUtils.formatDateTime(minTime), val, "0.0");
		} else {
			return null;
		}
	}

	/**
	 * @category 获得时间统计类型仪表的运行时间
	 * @param wzdm      物资代码
	 * @param ybwh      成本仪表
	 * @param realData  实时仪表
	 * @param beginTime 上班时间
	 * @param endTime   下班时间
	 * @return
	 */
	/*
	 * private FetchRealTimeDataVo getTimeValue(String wzdm, String ybwh, String
	 * realData, String beginTime, String endTime, int timeInterval) { double val =
	 * 0; TJSEngine tjs = new TJSEngine(); if (timeValue.containsKey(realData)) {
	 * val = timeValue.get(realData);// 已经统计过了，从timeValue直接取值 } else {
	 * HashMap<String, Double> ybValue = new HashMap<String, Double>(); Date sj =
	 * null; Date st = null; Date et = null; String tempgs = ""; Double gsValue =
	 * 0.0; Date fetchTime = null; String fetchValue = ""; Double fdsj = 0.0;
	 * boolean useTOU = false;// 是否使用分时电价 int secondInterval = timeInterval * 60;
	 * String gs = "";// 得到转换后的公式 if (convertFormula.containsKey(realData)) { gs =
	 * convertFormula.get(realData); if (gs.indexOf("fetchTime()") > 0) { useTOU =
	 * true;// 使用了分时电价 } } List<String> ybList = new ArrayList<String>();//
	 * 得到公式中包含的仪表 if (ybFormula.containsKey(realData)) { ybList =
	 * ybFormula.get(realData); } // 开始时间统计 for (Date time : timeList) { if (sj ==
	 * null || Dates.bjDate(time, sj) >= 0) { sj = Dates.doSecond(time,
	 * secondInterval); ybValue = fetchDataValue.get(time);// 获得时间点下的数据 fetchTime =
	 * null; if (ybValue.size() > 0) {// 时间点下有数据 tempgs = gs; for (String yb :
	 * ybList) {// 用采集数据替换公式内的仪表 if (ybValue.containsKey(yb)) { // 采样时间下有仪表数据
	 * fetchTime = time;// 至少有一个符合条件的仪表 fetchValue =
	 * String.valueOf(ybValue.get(yb));// 获得采集值 if (fetchValue == null ||
	 * "".equals(fetchValue)) { fetchValue = "0";// 采集数据是空值或空字符串时，按0处理 } tempgs =
	 * tempgs.replace(yb, fetchValue); } else { // 无数据 tempgs = tempgs.replace(yb,
	 * "0");// 没有采集值，按0处理 } } if (fetchTime != null || useTOU) { //
	 * 有一个符合条件的仪表或使用了分时电价,可以计算公式 tempgs = tempgs.replace("fetchTime()", "'" +
	 * Dates.formatDateTime(time) + "'");// 替换公式里的采集时间 try { String valx =
	 * tjs.eval(tempgs)+"";// 得到公式值 if (Coms.judgeDouble(valx)) {
	 * gsValue=Double.parseDouble(valx); } } catch (ScriptException e) { gsValue =
	 * 0.0; System.out.println("获取实时数据错误1"); e.printStackTrace(); } if (gsValue ==
	 * 1) { // 为开始时间赋值 if (et == null && st == null) { //
	 * 第一次，当开始时间和结束时间同时是空值时，使用上班时间做开始时间 st = Dates.parseDateTime(beginTime); } else
	 * if (st == null) { // 再次 st = time; } } else { // 计算运行时间， if (st == null && et
	 * == null) {// 开始时间和结束时间同时是空值时，使用上班时间做开始时间 st = Dates.parseDateTime(beginTime);
	 * } if (st != null) { et = time;// 得到结束时间 fdsj = Dates.diffHour(et, st); if
	 * (fdsj != null) { val = val + fdsj; } } st = null;// 清空开始时间 } } } } } if
	 * (gsValue == 1) {// 最后采集点的公式是1时，使用下班时间做结束时间 // 最后的开始时间为空时，需要计算开始时间到下班时间间的小时数
	 * if (st == null) { st = Dates.parseDateTime(beginTime);// 开始时间是空值时，使用上班时间 } et
	 * = Dates.parseDateTime(endTime);// 下班时间做结束时间 fdsj = Dates.diffHour(et, st); if
	 * (fdsj != null) { val = val + fdsj; } } timeValue.put(realData, val);//
	 * 暂存数据留作后续计算使用 } return setDataBean(wzdm, ybwh, "", val, 0.0);// 为返回列表赋值 }
	 */

	/**
	 * @category 获得时间统计使用的数采仪表
	 * @param ssyb 设置的时间统计公式
	 * @return List<String>
	 */
	/*
	 * private List<String> getSSYBFromFormula(String ssyb) { List<String> rtn = new
	 * ArrayList<String>(); String xyb = ""; if (!convertFormula.containsKey(ssyb))
	 * { // 没有转换过 String gs = ssyb.replace("vtimestart()", "1");// 将vtimestart()替换成1
	 * gs = gs.replace("vtimestop()", "0");// 将vtimestop()替换成0
	 * 
	 * // 正则得到真正的实时仪表（引号内的部分，单引号和双引号都可以） // String str1 =
	 * "['\"](.*?)['\"]";//为了适应峰平谷的统计将单引号和双引号换成了# String str1 = "[#](.*?)[#]";
	 * java.util.regex.Pattern p = Pattern.compile(str1); Matcher m = p.matcher(gs);
	 * boolean f = m.find(); while (f) { xyb = m.group(); xyb =
	 * xyb.replaceAll("[#]", "");// 替换实时仪表上的# if (!rtn.contains(xyb)) {
	 * rtn.add(xyb); } f = m.find(); } gs = gs.replaceAll("[#]", "");// 替换公式中的#
	 * convertFormula.put(ssyb, gs);// 暂存变换结果 }
	 * 
	 * return rtn; }
	 */

	/**
	 * @category 将采集的时间统计关联仪表数据整理暂存起来
	 * @param vlist 采集的数据
	 * @return HashMap<String,List<fetchDataBean>>
	 */
	/*
	 * private void dealWithFetchDataByTime(List<TagData> vlist) { String ssyb = "";
	 * String conf = ""; Integer sct = 0; Integer cct = 0; Object obj = 0.00; Double
	 * val = 0.00;
	 * 
	 * Date fetchTime = null; if (vlist != null) { for (TagData s : vlist) { ssyb =
	 * s.getTag().getTagCode(); if (ConfidenceThreshold.containsKey(ssyb)) { sct =
	 * ConfidenceThreshold.get(ssyb); } else { sct = 0; } conf = s.getConf(); cct =
	 * this.convertInteger(conf, 100); if (cct >= sct) { fetchTime =
	 * s.getTimeStamp(); obj = s.getValue(); val = this.convertDouble((String) obj,
	 * 0.00); // 采样时间列表 if (!timeList.contains(fetchTime)) {
	 * timeList.add(fetchTime); } // 用于提取第二个仪表指定时间点的值 if
	 * (fetchDataValue.containsKey(fetchTime)) {
	 * fetchDataValue.get(fetchTime).put(ssyb, val);// } else { HashMap<String,
	 * Double> tempMap = new HashMap<String, Double>(); tempMap.put(ssyb, val);
	 * fetchDataValue.put(fetchTime, tempMap); } } } } Collections.sort(timeList); }
	 */

	/**
	 * @category 按照实时仪表对采样值进行整理
	 * @param vList //返回的采样结果
	 */
	private void dealWithFetchDataByInstrument(List<Tag> vList, FetchRealTimeDataDTO info) {
		HashMap<String, List<FetchRealTimeDataVo>> sjm = info.getDm();
		String sval;
		Object val;
		if (vList != null) {
			int cc1 = vList.size();
			for (int i = 0; cc1 > i; i++) {
				Tag jsonObject = vList.get(i);
				String tagCode = jsonObject.getTagCode();// 关转成大写的了
				List<FetchRealTimeDataVo> sl = null;
				if (sjm.containsKey(tagCode)) {
					sl = sjm.get(tagCode);
				} else {
					sl = new ArrayList<FetchRealTimeDataVo>();
				}
				List<TagData> datas = jsonObject.getDatas();
				if (datas != null) {
					int cc2 = datas.size();
					for (int j = 0; cc2 > j; j++) {
						TagData each = datas.get(j);
						FetchRealTimeDataVo vo = new FetchRealTimeDataVo();
						vo.setTime(each.getDatetime());
						val = each.getValue();
						if (val == null) {
							sval = "";
						} else {
							sval = String.valueOf(val);
						}
						vo.setValue(sval);
						sl.add(vo);
					}
					sjm.put(tagCode, sl);
				}
			}
		}
	}

}
