package com.yunhesoft.leanCosting.calcLogic;

import java.util.HashMap;
import java.util.List;

public interface IFetchRealTimeData {

	/**
	 * @category 获取交接班的实时数据：给前台获取数据使用
	 * @param FetchRealTimeDataDTO
	 * @return Json
	 */
	public String getShiftDataWithJson(FetchRealTimeDataDTO info);

	/**
	 * @category 获取交接班的实时数据：给后台获取数据使用
	 * @param FetchRealTimeDataDTO
	 * @return HashMap<String,FetchRealTimeDataVo>
	 */
	public HashMap<String, List<FetchRealTimeDataVo>> getShiftDataWithMap(FetchRealTimeDataDTO info);

	/**
	 * @category 获取给定时间点的数据
	 * @param FetchRealTimeDataDTO
	 * @return HashMap<String,FetchRealTimeDataVo>
	 */
	public HashMap<String, FetchRealTimeDataVo> getTimeDataWithMap(FetchRealTimeDataDTO info);

}
