package com.yunhesoft.leanCosting.calcLogic;

import lombok.Data;

@Data
public class TdsFormulaParamDTO {

	/** 核算单元ID */
	private String unitId;

	/** 核算单元名称 */
	private String unitName;

	/** 核算单元外部系统名称 */
	private String outUnitName;

	/** 开始时间 */
	private String kssj;

	/** 截止时间 */
	private String jzsj;

	/** 填表日期 */
	private String tbrq;

	/** 班次ID */
	private String shiftId;

	/** 班次名称 */
	private String shiftName;

	/** 班组ID */
	private String teamId;

	/** 班组名称 */
	private String teamName;

	/** 租户ID */
	private String TENANT_ID;

	/** 项目ID */
	private String xmid;

	/** 项目名称 */
	private String xmmc;

	/** 仪表ID */
	private String ybid;

	/** 仪表名称 */
	private String ybmc;

}
