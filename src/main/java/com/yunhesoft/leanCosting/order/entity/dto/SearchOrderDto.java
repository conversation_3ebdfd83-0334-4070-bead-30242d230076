package com.yunhesoft.leanCosting.order.entity.dto;

import com.yunhesoft.core.common.dto.BaseQueryDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @Description: 用于包含搜索订单条件
 * <AUTHOR>
 * @date 2021/09/15
 */
@Data
public class SearchOrderDto extends BaseQueryDto {
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "顾客单位")
    private String client;

    @ApiModelProperty(value = "交货日期")
    private String deliveryDate;

    @ApiModelProperty(value = "计划号")
    private String planNo;

    @ApiModelProperty(value = "合同号")
    private String contractNo;

    @ApiModelProperty(value = "联系人")
    private String contact;

    @ApiModelProperty(value = "联系电话")
    private String tel;

    @ApiModelProperty(value = "邮件")
    private String email;

    @ApiModelProperty(value = "订货日期")
    private String orderDate;


    @ApiModelProperty(value = "运输方式")
    private String transportation;

    @ApiModelProperty(value = "确认方式")
    private String confirm;

    @ApiModelProperty(value = "是否加急")
    private Integer isUrgent;

    // 0未下发 1已下发 2已完成
    @ApiModelProperty(value = "订单状态")
    private Integer status;

    // 0没有 1有
    @ApiModelProperty(value = "是否有产品")
    private Integer isProduct;

    // 0不使用 1使用
    @ApiModelProperty(value = "是否使用")
    private Integer used;

    @ApiModelProperty(value = "创建人姓名")
    private String createPersonName;
    /** 月份 */
    private String month;
    private String product;
    private String productNo;
    private String productId;
    private int flag;
}
