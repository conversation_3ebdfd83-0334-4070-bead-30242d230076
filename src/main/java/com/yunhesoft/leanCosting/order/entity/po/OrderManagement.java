package com.yunhesoft.leanCosting.order.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Description: emi 订单管理实体
 * <AUTHOR>
 * @date 2021/09/10
 */
@ApiModel(value = "订单管理实体")
@Entity
@Data
@Table(name = "ORDER_MANAGEMENT")
public class OrderManagement extends BaseEntity {

    @ApiModelProperty(value = "订单编号")
    @Column(name = "ORDERNO", length = 50)
    private String orderNo;

    @ApiModelProperty(value = "顾客单位")
    @Column(name = "CLIENT", length = 200)
    private String client;

    @ApiModelProperty(value = "交货日期")
    @Column(name = "DELIVERYDATE")
    private Date deliveryDate;

    @ApiModelProperty(value = "计划号")
    @Column(name = "PLANNO", length = 200)
    private String planNo;

    @ApiModelProperty(value = "合同号")
    @Column(name = "CONTRACTNO", length = 200)
    private String contractNo;

    @ApiModelProperty(value = "联系人")
    @Column(name = "CONTACT", length = 100)
    private String contact;

    @ApiModelProperty(value = "联系电话")
    @Column(name = "TEL", length = 100)
    private String tel;

    @ApiModelProperty(value = "邮件")
    @Column(name = "EMAIL", length = 50)
    private String email;

    @ApiModelProperty(value = "订货日期")
    @Column(name = "ORDERDATE")
    private Date orderDate;


    @ApiModelProperty(value = "运输方式")
    @Column(name = "TRANSPORTATION", length = 500)
    private String transportation;

    @ApiModelProperty(value = "确认方式")
    @Column(name = "CONFIRM", length = 100)
    private String confirm;

    @ApiModelProperty(value = "是否加急")
    @Column(name = "ISURGENT")
    private Integer isUrgent;

    // 0未下发 1已下发 2已完成
    @ApiModelProperty(value = "订单状态")
    @Column(name = "STATUS")
    private Integer status;

    // 0没有 1有
    @ApiModelProperty(value = "是否有产品")
    @Column(name = "ISPRODUCT")
    private Integer isProduct;

    // 0不使用 1使用
    @ApiModelProperty(value = "是否使用")
    @Column(name = "USED")
    private Integer used;

    @Column(name = "CREATEPERSONNAME",length = 50)
    private String createPersonName;

    @Column(name = "UPDATEPERSONNAME",length = 50)
    private String updatePersonName;
}
