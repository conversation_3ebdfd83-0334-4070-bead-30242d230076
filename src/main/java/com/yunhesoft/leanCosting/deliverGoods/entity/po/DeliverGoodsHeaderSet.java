package com.yunhesoft.leanCosting.deliverGoods.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import io.swagger.annotations.ApiModel;

@ApiModel(value = "发货记录表头")
@Data
@Entity
@Table(name = "DELIVER_GOODS_HEADER_SET")
public class DeliverGoodsHeaderSet extends BaseEntity {

    @ApiModelProperty(value = "表头名称")
    @Column(name = "HEADER", length = 100)
    private String header;

    @ApiModelProperty(value = "别名")
    @Column(name = "ALIAS", length = 100)
    private String alias;

    @ApiModelProperty(value = "组件类型")
    @Column(name = "COMTYPE", length = 100)
    private String comType;

    @ApiModelProperty(value = "宽度")
    @Column(name = "WIDTH")
    private Integer width;

    @ApiModelProperty(value = "字段最大长度")
    @Column(name = "MAXLENGTH")
    private Integer maxlength;

    @ApiModelProperty(value = "对齐方式")
    @Column(name = "align", length = 100)
    private String align;

    @ApiModelProperty(value = "排序")
    @Column(name = "TMSORT", length = 100)
    private Integer tmsort;

    @ApiModelProperty(value = "是否必填")
    @Column(name = "ISMUST", length = 100)
    private Integer isMust;

    @ApiModelProperty(value = "排序")
    @Column(name = "ISSHOW", length = 100)
    private Integer isShow;

    @ApiModelProperty(value = "是否同时作为检索条件")
    @Column(name = "ISQUERY", length = 100)
    private Integer isQuery;
}
