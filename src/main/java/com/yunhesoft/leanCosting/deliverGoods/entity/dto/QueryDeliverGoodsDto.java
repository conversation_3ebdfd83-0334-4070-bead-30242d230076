package com.yunhesoft.leanCosting.deliverGoods.entity.dto;


import com.yunhesoft.leanCosting.order.entity.po.ProductControl;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * @Description: 查询发货记录
 * <AUTHOR>
 * @date 2023/8/4
 */
@Data
public class QueryDeliverGoodsDto {

	private int flag;
    private String projectName;
    private String sponsorOrg;
    private String productNo;
    private String productName;
    private Date deliverDate;
    List<ProductControl> productList;
    private Integer pageSize;
    private Integer pageNum;

    @ApiModelProperty(value = "产品ID")
    private String productId;

    @ApiModelProperty(value = "发货编号")
    private String deliverNo;

    @ApiModelProperty(value = "承办方联系人")
    private String sponsorPerson;

    @ApiModelProperty(value = "承办方联系电话")
    private String sponsorPhone;

    @ApiModelProperty(value = "弯管厂家")
    private String elbowOrg;

    @ApiModelProperty(value = "弯管厂家联系人")
    private String elbowPerson;

    @ApiModelProperty(value = "弯管厂家电话")
    private String elbowPhone;

    @ApiModelProperty(value = "货车联系人")
    private String orgCarPerson;

    @ApiModelProperty(value = "货车车牌号")
    private String orgCarNo;

    @ApiModelProperty(value = "货车联系电话")
    private String orgCarPhone;

    @ApiModelProperty(value = "数量")
    private Integer number;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "是否删除")
    private Integer tmUsed;

    @ApiModelProperty(value = "预留字段1")
    private String param1;

    @ApiModelProperty(value = "预留字段2")
    private String param2;

    @ApiModelProperty(value = "预留字段3")
    private String param3;

    @ApiModelProperty(value = "预留字段4")
    private String param4;

    @ApiModelProperty(value = "预留字段5")
    private String param5;

    @ApiModelProperty(value = "预留字段6")
    private String param6;

    @ApiModelProperty(value = "预留字段7")
    private String param7;

    @ApiModelProperty(value = "预留字段8")
    private String param8;

    @ApiModelProperty(value = "预留字段9")
    private String param9;

    @ApiModelProperty(value = "预留字段10")
    private String param10;
}
