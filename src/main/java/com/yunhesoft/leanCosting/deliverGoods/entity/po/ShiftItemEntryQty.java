package com.yunhesoft.leanCosting.deliverGoods.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "广东储运的班次入库量同步数据")
@Entity
@Setter
@Getter
@Table(name = "SHIFTITEMENTRYQTY")
public class ShiftItemEntryQty extends BaseEntity {

	private static final long serialVersionUID = 1683106346833398399L;

	@ApiModelProperty(value = "班次")
	@Column(name = "INTV", length = 100)
	private String intv;

	@ApiModelProperty(value = "日期")
	@Column(name = "FINISEDDATE", length = 100)
	private String finisedDate;

	@ApiModelProperty(value = "批号")
	@Column(name = "WORKNO", length = 100)
	private String workNo;

	@ApiModelProperty(value = "项目等级")
	@Column(name = "MATERIALCLASSNO", length = 100)
	private String materialClassNo;

	@ApiModelProperty(value = "计量单位")
	@Column(name = "UNITWEIGHT", length = 100)
	private String unitWeight;

	@ApiModelProperty(value = "项目名称")
	@Column(name = "MATERIALNAME", length = 100)
	private String materialName;

	@ApiModelProperty(value = "数量")
	@Column(name = "UNIT", length = 100)
	private String unit;

	@ApiModelProperty(value = "项目编码")
	@Column(name = "MATERIALCODE", length = 100)
	private String materialCode;

}
