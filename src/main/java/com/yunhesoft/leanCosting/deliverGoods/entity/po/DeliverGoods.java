package com.yunhesoft.leanCosting.deliverGoods.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@ApiModel(value = "发货记录")
@Data
@Entity
@Table(name = "DELIVER_GOODS")
public class DeliverGoods extends BaseEntity {

    @ApiModelProperty(value = "产品ID")
    @Column(name = "PRODUCT_ID", length = 100)
    private String productId;

    @ApiModelProperty(value = "项目名称")
    @Column(name = "PROJECT_NAME", length = 100)
    private String projectName;

    @ApiModelProperty(value = "发货编号")
    @Column(name = "DELIVER_NO", length = 100)
    private String deliverNo;

    @ApiModelProperty(value = "承办方")
    @Column(name = "SPONSOR_ORG", length = 100)
    private String sponsorOrg;

    @ApiModelProperty(value = "承办方联系人")
    @Column(name = "SPONSOR_PERSON", length = 100)
    private String sponsorPerson;

    @ApiModelProperty(value = "承办方联系电话")
    @Column(name = "SPONSOR_PHONE", length = 100)
    private String sponsorPhone;

    @ApiModelProperty(value = "弯管厂家")
    @Column(name = "ELBOW_ORG", length = 100)
    private String elbowOrg;

    @ApiModelProperty(value = "弯管厂家联系人")
    @Column(name = "ELBOW_PERSON", length = 100)
    private String elbowPerson;

    @ApiModelProperty(value = "弯管厂家电话")
    @Column(name = "ELBOW_PHONE", length = 100)
    private String elbowPhone;

    @ApiModelProperty(value = "货车联系人")
    @Column(name = "ORG_CAR_PERSON", length = 100)
    private String orgCarPerson;

    @ApiModelProperty(value = "货车车牌号")
    @Column(name = "ORG_CAR_NO", length = 100)
    private String orgCarNo;

    @ApiModelProperty(value = "货车联系电话")
    @Column(name = "ORG_CAR_PHONE", length = 100)
    private String orgCarPhone;


    @ApiModelProperty(value = "交货日期")
    @Column(name = "DELIVERY_DATE", length = 100)
    private Date deliverDate;

    @ApiModelProperty(value = "产品编号")
    @Column(name = "PRODUCT_NO", length = 100)
    private String productNo;

    @ApiModelProperty(value = "产品名称")
    @Column(name = "PRODUCT_NAME", length = 100)
    private String productName;

    @ApiModelProperty(value = "数量")
    @Column(name = "NUMBER", length = 100)
    private Integer number;

    @ApiModelProperty(value = "单位")
    @Column(name = "UNIT", length = 100)
    private String unit;

    @ApiModelProperty(value = "是否删除")
    @Column(name = "TMUSED", length = 100)
    private Integer tmUsed;

    @ApiModelProperty(value = "预留字段1")
    @Column(name = "PARAM1", length = 100)
    private String param1;

    @ApiModelProperty(value = "预留字段2")
    @Column(name = "PARAM2", length = 100)
    private String param2;

    @ApiModelProperty(value = "预留字段3")
    @Column(name = "PARAM3", length = 100)
    private String param3;

    @ApiModelProperty(value = "预留字段4")
    @Column(name = "PARAM4", length = 100)
    private String param4;

    @ApiModelProperty(value = "预留字段5")
    @Column(name = "PARAM5", length = 100)
    private String param5;

    @ApiModelProperty(value = "预留字段6")
    @Column(name = "PARAM6", length = 100)
    private String param6;

    @ApiModelProperty(value = "预留字段7")
    @Column(name = "PARAM7", length = 100)
    private String param7;

    @ApiModelProperty(value = "预留字段8")
    @Column(name = "PARAM8", length = 100)
    private String param8;

    @ApiModelProperty(value = "预留字段9")
    @Column(name = "PARAM9", length = 100)
    private String param9;

    @ApiModelProperty(value = "预留字段10")
    @Column(name = "PARAM10", length = 100)
    private String param10;
}
