package com.yunhesoft.leanCosting.deliverGoods.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "广东储运的项目库存同步数据")
@Entity
@Setter
@Getter
@Table(name = "ITEMSTOCKDAYLEDGER")
public class ItemStockDayLedger extends BaseEntity {

	private static final long serialVersionUID = -3255706626969527901L;

	@ApiModelProperty(value = "项目等级")
	@Column(name = "ITEMGRADE", length = 100)
	private String itemGrade;

	@ApiModelProperty(value = "项目编码")
	@Column(name = "ITEMCODE", length = 100)
	private String itemCode;

	@ApiModelProperty(value = "日期")
	@Column(name = "SENDDATE", length = 100)
	private String sendDate;

	@ApiModelProperty(value = "项目名称")
	@Column(name = "ITEMNAME", length = 100)
	private String itemName;

	@ApiModelProperty(value = "工号")
	@Column(name = "WORKNO", length = 100)
	private String workNo;

	@ApiModelProperty(value = "数量")
	@Column(name = "QTY", length = 100)
	private String qty;

	@ApiModelProperty(value = "计量单位")
	@Column(name = "UNIT", length = 100)
	private String unit;

	@ApiModelProperty(value = "批号")
	@Column(name = "ITEMBRAND", length = 100)
	private String itemBrand;

}
