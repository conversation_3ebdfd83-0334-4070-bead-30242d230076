package com.yunhesoft.leanCosting.deliverGoods.service.impl;

import cn.afterturn.easypoi.handler.impl.ExcelDataHandlerDefaultImpl;
import cn.afterturn.easypoi.util.PoiPublicUtil;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.leanCosting.deliverGoods.entity.po.DeliverGoodsHeaderSet;
import com.yunhesoft.leanCosting.deliverGoods.service.IDeliverGoodsService;

@Component(value="goodsImportHandler")
public class MapImportHandler extends ExcelDataHandlerDefaultImpl<Map<String, Object>> {

	private static List<DeliverGoodsHeaderSet> deliverGoodsHeadList = null;
	
	public void createDeliverGoodsHeadList(){
		deliverGoodsHeadList = SpringUtils.getBean(IDeliverGoodsService.class).getDeliverGoodsHeader();
	}
	
    @Override
    public void setMapValue(Map<String, Object> map, String originKey, Object value) {
        if (value instanceof Double) {
            map.put(getRealKey(originKey), PoiPublicUtil.doubleToString((Double) value));
        } else {
            map.put(getRealKey(originKey), value != null ? value.toString() : null);
        }
    }

    private String getRealKey(String originKey) {
		for(int i=0;i<deliverGoodsHeadList.size();i++) {
			if (originKey.equals(deliverGoodsHeadList.get(i).getHeader())) {
				return deliverGoodsHeadList.get(i).getAlias();
			}
		}
		if (originKey.equals("ID")) {
			 return "id";
			 }
		return originKey;
	}
}
