package com.yunhesoft.leanCosting.deliverGoods.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.deliverGoods.entity.dto.QueryDeliverGoodsDto;
import com.yunhesoft.leanCosting.deliverGoods.entity.po.DeliverGoods;
import com.yunhesoft.leanCosting.deliverGoods.entity.po.DeliverGoodsHeaderSet;
import com.yunhesoft.leanCosting.deliverGoods.service.IDeliverGoodsService;
import com.yunhesoft.leanCosting.order.entity.dto.SaveDto;
import com.yunhesoft.leanCosting.order.entity.po.ProductControl;

import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import io.swagger.annotations.ApiModelProperty;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.lang.reflect.Field;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 发货记录服务实现
 * <AUTHOR>
 * @date 2023/8/3
 */
@Service
public class DeliverGoodsServiceImpl implements IDeliverGoodsService {
    @Autowired
    private EntityService dao;
    
    @Autowired
    @Qualifier("goodsImportHandler")
    private MapImportHandler mapImportHandler;
    
    @Autowired
    private IDeliverGoodsService idgs;
    /**
     * 根据产品列表查询发货记录
     *
     * @param productList
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<DeliverGoods> getDelverGoods(List<ProductControl> productList, QueryDeliverGoodsDto param,Pagination page) throws Exception {
		if (StringUtils.isNotEmpty(productList)) {
			Map<String, ProductControl> productControlMap = productList.stream()
					.collect(Collectors.toMap(ProductControl::getId, Function.identity()));
			List<String> productId = productList.stream().map(i -> i.getId()).collect(Collectors.toList());
			if (StringUtils.isEmpty(productId)) {
				throw new Exception("未取得产品记录");
			}
			// 使用productId取得发货记录
			Where where = Where.create();
			where.eq(DeliverGoods::getTmUsed, 1);
			where.in(DeliverGoods::getProductId, productId.toArray());
			List<DeliverGoods> deliverGoods = dao.rawQueryListByWhere(DeliverGoods.class, where);
			List<String> haveProduct = deliverGoods.stream().map(i -> i.getProductId()).collect(Collectors.toList());
			List<String> noProduct = productId.stream().filter(i -> !haveProduct.contains(i))
					.collect(Collectors.toList());
			// 已经存在的新发货记录要与产品信息保持一致
			List<DeliverGoods> updateList = new ArrayList<>();
			for (String id : haveProduct) {
				ProductControl product = productControlMap.get(id);
				List<DeliverGoods> goods = deliverGoods.stream().filter(i -> i.getProductId().equals(id))
						.collect(Collectors.toList());
				// 维护最新产品信息
				Boolean updated = false;
				for (DeliverGoods good : goods) {
					if (ObjUtils.notEmpty(product.getClient())
							&& (!product.getClient().equals(good.getProjectName()))) {
						good.setProjectName(product.getClient());
						updated = true;
					}
					if (ObjUtils.notEmpty(product.getDeliveryDate())
							&& (!product.getDeliveryDate().equals(good.getDeliverDate()))) {
						good.setDeliverDate(product.getDeliveryDate());
						updated = true;
					}
					if (ObjUtils.notEmpty(product.getProductNo())
							&& (!product.getProductNo().equals(good.getProductNo()))) {
						good.setProductNo(product.getProductNo());
						updated = true;
					}
					if (ObjUtils.notEmpty(product.getProduct())
							&& (!product.getProduct().equals(good.getProductName()))) {
						good.setProductName(product.getProduct());
						updated = true;
					}
					if (ObjUtils.notEmpty(product.getOrderNumber())
							&& (!product.getOrderNumber().equals(good.getNumber()))) {
						good.setNumber(product.getOrderNumber());
						updated = true;
					}
					if (ObjUtils.notEmpty(product.getUnit()) && (!product.getUnit().equals(good.getUnit()))) {
						good.setUnit(product.getUnit());
						updated = true;
					}
					if (updated) {
						// 加入修改记录列表
						updateList.add(good);
					}
				}
			}
			List<DeliverGoods> insertList = new ArrayList<>();
			// 不包含的产品生成新发货记录
			for (String id : noProduct) {
				ProductControl product = productControlMap.get(id);
				DeliverGoods good = new DeliverGoods();
				good.setId(TMUID.getUID());
				good.setProductId(product.getId());
				good.setTmUsed(1);
				good.setProjectName(product.getClient());
				good.setDeliverDate(product.getDeliveryDate());
				good.setProductNo(product.getProductNo());
				good.setProductName(product.getProduct());
				good.setNumber(product.getOrderNumber());
				good.setUnit(product.getUnit());
				// 与记录list合并
				deliverGoods.add(good);
				// 加入添加记录列表
				insertList.add(good);
			}
			if (StringUtils.isNotEmpty(updateList)) {
				this.updateDeliverGoods(updateList);
			}
			if (StringUtils.isNotEmpty(insertList)) {
				this.insertDeliverGoods(insertList);
			}
			if (StringUtils.isNotEmpty(deliverGoods)) {
				// 不为空 排序按照产品编号正序
				deliverGoods = deliverGoods.stream().sorted(Comparator.comparing(DeliverGoods::getProductNo))
						.collect(Collectors.toList());
			}
			if (ObjUtils.notEmpty(param)) {
				if (StringUtils.isNotEmpty(param.getProjectName())) {
					deliverGoods = deliverGoods.stream()
							.filter(i -> StringUtils.isNotEmpty(i.getProjectName())
									&& i.getProjectName().contains(param.getProjectName()))
							.collect(Collectors.toList());
				}
				if (StringUtils.isNotEmpty(param.getSponsorOrg())) {
					deliverGoods = deliverGoods.stream().filter(i -> StringUtils.isNotEmpty(i.getSponsorOrg())
							&& i.getSponsorOrg().contains(param.getSponsorOrg())).collect(Collectors.toList());
				}
				if (StringUtils.isNotEmpty(param.getProductNo())) {
					deliverGoods = deliverGoods.stream().filter(i -> StringUtils.isNotEmpty(i.getProductNo())
							&& i.getProductNo().contains(param.getProductNo())).collect(Collectors.toList());
				}
				if (StringUtils.isNotEmpty(param.getProductName())) {
					deliverGoods = deliverGoods.stream()
							.filter(i -> StringUtils.isNotEmpty(i.getProductName())
									&& i.getProductName().contains(param.getProductName()))
							.collect(Collectors.toList());
				}
				if (ObjUtils.notEmpty(param.getDeliverDate())) {
					deliverGoods = deliverGoods.stream().filter(i -> ObjUtils.notEmpty(i.getDeliverDate())
							&& i.getDeliverDate().equals(param.getDeliverDate())).collect(Collectors.toList());
				}
			}
			return deliverGoods;
		} else {
			// 使用productId取得发货记录
			Where where = Where.create();
			where.eq(DeliverGoods::getTmUsed,1);
			if(StringUtils.isNotEmpty(param.getProjectName())) {
				where.like(DeliverGoods::getProjectName, param.getProjectName());
			}
			if(StringUtils.isNotEmpty(param.getDeliverNo())) {
				where.like(DeliverGoods::getDeliverNo, param.getDeliverNo());
			}
			if(StringUtils.isNotEmpty(param.getSponsorOrg())) {
				where.like(DeliverGoods::getSponsorOrg, param.getSponsorOrg());
			}
			if(StringUtils.isNotEmpty(param.getSponsorPerson())) {
				where.like(DeliverGoods::getSponsorPerson, param.getSponsorPerson());
			}
			if(StringUtils.isNotEmpty(param.getSponsorPhone())) {
				where.like(DeliverGoods::getSponsorPhone, param.getSponsorPhone());
			}
			if(StringUtils.isNotEmpty(param.getElbowOrg())) {
				where.like(DeliverGoods::getElbowOrg, param.getElbowOrg());
			}
			if(StringUtils.isNotEmpty(param.getElbowPerson())) {
				where.like(DeliverGoods::getElbowPerson, param.getElbowPerson());
			}
			if(StringUtils.isNotEmpty(param.getElbowPhone())) {
				where.like(DeliverGoods::getElbowPhone, param.getElbowPhone());
			}
			if(StringUtils.isNotEmpty(param.getOrgCarPerson())) {
				where.like(DeliverGoods::getOrgCarPerson, param.getOrgCarPerson());
			}
			if(StringUtils.isNotEmpty(param.getOrgCarNo())) {
				where.like(DeliverGoods::getOrgCarNo, param.getOrgCarNo());
			}
			if(StringUtils.isNotEmpty(param.getOrgCarPhone())) {
				where.like(DeliverGoods::getOrgCarPhone, param.getOrgCarPhone());
			}
			if(ObjUtils.notEmpty(param.getDeliverDate())) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				String format = sdf.format(param.getDeliverDate());
				where.like(DeliverGoods::getDeliverDate, format);
			}
			if(StringUtils.isNotEmpty(param.getProductNo())) {
				where.like(DeliverGoods::getProductNo, param.getProductNo());
			}
			if(StringUtils.isNotEmpty(param.getProductName())) {
				where.like(DeliverGoods::getProductName, param.getProductName());
			}
			Order order = Order.create();
			order.orderByDesc(DeliverGoods::getCreateTime);
            return dao.queryData(DeliverGoods.class, where,order,page);
		}
    }

    private Boolean insertDeliverGoods(List<DeliverGoods> insertList) {
        return dao.insertBatch(insertList)>0;
    }

    private Boolean updateDeliverGoods(List<DeliverGoods> updateList) {
        return dao.updateByIdBatch(updateList)>0;
    }

    /**
     * 保存发货记录
     *
     * @param param
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public Boolean saveDeliverGoods(SaveDto param) {
        Boolean flag = false;
        if(param!=null){
            List<DeliverGoods> insertList = new ArrayList<>();
            List<DeliverGoods> updateList = new ArrayList<>();
            List<DeliverGoods> deleteList = new ArrayList<>();
            if(StringUtils.isNotEmpty(param.getData())){
                JSONArray jsonArray = JSONArray.parseArray(param.getData());
                if(jsonArray!=null){
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject row = jsonArray.getJSONObject(i);
                        Integer rowFlag = row.getInteger("TDSROW_rowFlag");
                        DeliverGoods bean = row.toJavaObject(DeliverGoods.class);
                        if(rowFlag==null || rowFlag==0){
                            bean.setId(TMUID.getUID());
                            bean.setTmUsed(1);
                            insertList.add(bean);
                        }else if(rowFlag==1){
                            updateList.add(bean);
                        }else {
                            bean.setTmUsed(0);
                            deleteList.add(bean);
                        }
                    }
                }
            }
            if(StringUtils.isNotEmpty(insertList)){
                flag = this.insertDeliverGoods(insertList);
            }
            if(StringUtils.isNotEmpty(updateList)){
                flag = this.updateDeliverGoods(updateList);
            }
            if(StringUtils.isNotEmpty(deleteList)){
                flag = this.updateDeliverGoods(deleteList);
            }
        }
        return flag;
    }
    /**
     * 保存表头
     * <AUTHOR>
     * @return
     * @params
    */
    @Override
    public Boolean saveDeliverGoodsHeader(SaveDto param) {
        Boolean flag = false;
        if(param!=null){
            List<DeliverGoodsHeaderSet> insertList = new ArrayList<>();
            List<DeliverGoodsHeaderSet> updateList = new ArrayList<>();
            List<DeliverGoodsHeaderSet> deleteList = new ArrayList<>();
            if(StringUtils.isNotEmpty(param.getData())){
                JSONArray jsonArray = JSONArray.parseArray(param.getData());
                if(jsonArray!=null){
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject row = jsonArray.getJSONObject(i);
                        Integer rowFlag = row.getInteger("TDSROW_rowFlag");
                        DeliverGoodsHeaderSet bean = row.toJavaObject(DeliverGoodsHeaderSet.class);
                        if(rowFlag==null || rowFlag==0){
                            bean.setId(TMUID.getUID());
                            insertList.add(bean);
                        }else if(rowFlag==1){
                            updateList.add(bean);
                        }else {
                            deleteList.add(bean);
                        }
                    }
                }
            }
            if(StringUtils.isNotEmpty(insertList)){
                flag = dao.insertBatch(insertList)>0;
            }
            if(StringUtils.isNotEmpty(updateList)){
                flag = dao.updateByIdBatch(updateList)>0;
            }
            if(StringUtils.isNotEmpty(deleteList)){
                flag = dao.updateByIdBatch(deleteList)>0;
            }
        }
        return flag;
    }

    /**
     * 获取表头
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<DeliverGoodsHeaderSet> getDeliverGoodsHeader() {
        Where where = Where.create();
        Order order = Order.create();
        order.orderByAsc(DeliverGoodsHeaderSet::getTmsort);
        List<DeliverGoodsHeaderSet> goodsHeaderSets = dao.queryList(DeliverGoodsHeaderSet.class, where,order);
        if(goodsHeaderSets.isEmpty()){
            //空的时候触发初始化
            this.initHeader();
            goodsHeaderSets = dao.queryList(DeliverGoodsHeaderSet.class, where,order);
        }
        return goodsHeaderSets;
    }

    private void initHeader() {
        //反射初始化所有表头信息
        Class c = DeliverGoods.class;
        //获取全部属性
        Field[] declaredFields = c.getDeclaredFields();
        List<DeliverGoodsHeaderSet> insertList = new ArrayList<>();
        int index = 0;
        for (Field declaredField : declaredFields) {
            ApiModelProperty annotation = declaredField.getAnnotation(ApiModelProperty.class);
            //字段名称
            String name = annotation.value();
            //字段别名
            String alias = declaredField.getName();
            Class<?> type = declaredField.getType();
			if("tmUsed".equals(alias)){
				//不生成是否删除字段作为表头
				continue;
			}
            //对于不同的类型字段设置对齐方式
            //数字右对齐 字符串左对齐   默认宽度100 最大长度100 文本框和数字框
            DeliverGoodsHeaderSet bean = new DeliverGoodsHeaderSet();
            bean.setId(TMUID.getUID());
            bean.setHeader(name);
            bean.setAlias(alias);
            bean.setMaxlength(100);
            bean.setWidth(100);
            bean.setIsShow(1);
            bean.setIsMust(0);
            bean.setTmsort(index+1);
            if(type.getName().contains("Integer")){
                bean.setAlign("right");
                bean.setComType("numberfield");
            }else{
                bean.setAlign("left");
                bean.setComType("textfield");
            }
			if("productName".equals(alias)){
				bean.setIsQuery(1);
			}else{
				bean.setIsQuery(0);
			}
            insertList.add(bean);
            index++;
        }
        if(StringUtils.isNotEmpty(insertList)){
            dao.insertBatch(insertList);
        }
    }


    /**
     * 导出
     *
     * @param param
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public void exportDeliverGoods(QueryDeliverGoodsDto param, HttpServletResponse response) throws Exception {
        List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
        //加入一个id列
        ExcelExportEntity idcol = new ExcelExportEntity("ID","id");
        colList.add(idcol);
        //根据表头设置来填充列表
        List<DeliverGoodsHeaderSet> deliverGoodsHeader = this.getDeliverGoodsHeader();
        deliverGoodsHeader = deliverGoodsHeader.stream().filter(i->i.getIsShow()==1).collect(Collectors.toList());
        for (DeliverGoodsHeaderSet deliverGoodsHeaderSet : deliverGoodsHeader) {
            ExcelExportEntity col = new ExcelExportEntity(deliverGoodsHeaderSet.getHeader(),deliverGoodsHeaderSet.getAlias());
            //设定宽度
    		if(deliverGoodsHeaderSet.getWidth() != 0) {
    			col.setWidth((deliverGoodsHeaderSet.getWidth())/6.2);
    		}else {
    			col.setWidth(0);
    		}
            colList.add(col);
        }
        //获取数据
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        if(param.getFlag() == 1) {
        	List<DeliverGoods> delverGoods = this.getDelverGoods(param.getProductList(), param,null);
            for (DeliverGoods delverGood : delverGoods) {
                Map<String, Object> map = ObjUtils.convertToMap(delverGood);
				map.put("id",delverGood.getId());
				if(map.get("deliverDate")!=null){
					Date date = (Date) map.get("deliverDate");
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
					String dateStr = sdf.format(date);
					map.put("deliverDate",dateStr);
				}
                list.add(map);
            }
        }
        //文档输出
		ExportParams exportParams = new ExportParams("发货记录", "数据");
		exportParams.setStyle(DeliverGoodExcleStyle.class);
		Workbook workbook = ExcelExportUtil.exportExcel(exportParams, colList, list);
        //下载
        ExcelExport.downLoadExcel("发货记录",response,workbook);

    }

    /**
     * 导入
     *
     * @param param
     * @return
     * <AUTHOR>
     * @params
     */
	@Override
	public Boolean importDeliverGoods(MultipartFile file) throws IOException, Exception {
		mapImportHandler.createDeliverGoodsHeadList();
		//获取DeliverGoods表字段以及其对应的数据类型
		Class<?> cls = DeliverGoods.class;
		Field[] declaredFields = cls.getDeclaredFields();
		//把所有的字段以及对应的数据类型放到一个map中
        Map<String, String> goodsMap = new HashMap<String, String>();
        for (Field declaredField : declaredFields) {
            //获取字段
            String alias = declaredField.getName();
            Class<?> type = declaredField.getType();
            String dataType = type.getName();
            goodsMap.put(alias, dataType);
        }
		//必填项目列表
		List<DeliverGoodsHeaderSet> isMustList = idgs.getDeliverGoodsHeader();
		// 导入参数
		ImportParams params = new ImportParams();
		params.setTitleRows(1);
		params.setHeadRows(1);
		// 此处传入一个对象 这个对象继承于 ExcelDataHandlerDefaultImpl
		// 重写方法 用于映射数据 和 表头对应实体关系
		params.setDataHandler(new MapImportHandler());
 		List<Map<String, Object>> list = null;
 		//如果传入的文件连表头都没有的空文件，返回错误提示信息
 		try {
 			list = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, params);
 		} catch (Exception e) {
 			// TODO: handle exception
 			throw new Exception("整个文件为空！");
 		}
 		if(list.isEmpty()) {
 			return false;
 		}
 		List<DeliverGoods> insertList = new ArrayList<>();
        List<DeliverGoods> updateList = new ArrayList<>();
        //错误信息列表
        List<String> totalerrorList = new ArrayList<String>();
		Object flag = new String();
		for (int i = 0; i < list.size(); i++) {
			List<String> errorList = new ArrayList<String>();
			Map<String, Object> oMap = list.get(i);
			for (int j = 0; j < isMustList.size(); j++) {
				if (isMustList.get(j).getIsMust() == 1
						&& ObjUtils.isEmpty(oMap.get(isMustList.get(j).getAlias()))) {
					//添加消息
					errorList.add("第"+(i+1)+"行 "+isMustList.get(j).getHeader()+"不能为空！");
				}
			}
			for(Map.Entry<String, String> goodsEntry : goodsMap.entrySet()){
				//字段英文名
			    String alias = goodsEntry.getKey();
			    //字段的数据类型
			    String dataType = goodsEntry.getValue();
				//字段名称
				String headerName = new String();
				for(int k = 0;k<isMustList.size();k++) {
					if(alias.equals(isMustList.get(k).getAlias())) {
						headerName = isMustList.get(k).getHeader();
					}
				}
			    for(Map.Entry<String, Object> oEntry : oMap.entrySet()) {
			    	//传过来数据的值
			    	Object oMapValue = oEntry.getValue();
			    	if(alias.equals(oEntry.getKey())) {
			    		if(dataType.equals("java.lang.Integer") && oMapValue instanceof String) {
			    		//数据库中是int,excel传过来的值是String的情况
			    			String goodsFlag = oMapValue.toString();
			    			if(goodsFlag.matches("^[-\\+]?[\\d]*$")) {//判断字符串是否是整数，是的话将其转换成int类型
			    				Integer.valueOf(goodsFlag);
			    				oEntry.setValue(goodsFlag);
			    			}else{//其他类型直接添加报错信息 
			    				errorList.add("第"+(i+1)+"行"+headerName+"属性值应该为整数，请修改数据");
			    			}
			    		}else if(dataType.equals("java.lang.Integer") && oMapValue instanceof Double) {
			    		//数据库中是int,excel传过来的值是Double的情况
			    			errorList.add("第"+(i+1)+"行"+headerName+"属性值应该为整数，请修改数据");
			    		}else if(dataType.equals("java.lang.Integer") && oMapValue instanceof Date) {
			    		//数据库中是int,excel传过来的值是Date的情况
			    			errorList.add("第"+(i+1)+"行"+headerName+"属性值应该为整数，请修改数据");
			    		}else if(dataType.equals("java.util.Date") && oMapValue instanceof String) {
			   			//数据库中是Date的情况,excel传过来的值是String的情况
					   		String goodsFlag = oMapValue.toString();
			    			if(goodsFlag.matches("^(19|20)\\d{2}[-/\\\\.]([1-9]|0[1-9]|1[012])[-/\\\\.]([1-9]|[12]\\d|3[01])$")) {//判断字符串是否是日期格式，是的话将其转换成date类型
			    				SimpleDateFormat sdf = new SimpleDateFormat();
			    				sdf.applyPattern("yyyy-MM-dd");
			    				Date date = sdf.parse(goodsFlag);
			    				oEntry.setValue(date);
			    			}else{//其他类型直接添加报错信息 
			    				errorList.add("第"+(i+1)+"行"+headerName+"属性值是日期，请修改例如2000-10-10");
			    			}
			   			}else if(dataType.equals("java.util.Date") && oMapValue instanceof Integer) {
				   		//数据库中是Date的情况,excel传过来的值是Integer的情况
			   				errorList.add("第"+(i+1)+"行"+headerName+"属性值是日期，请修改例如2000-10-10");
				   		}else if(dataType.equals("java.util.Date") && oMapValue instanceof Double) {
					   	//数据库中是Date的情况,excel传过来的值是Double的情况
				   			errorList.add("第"+(i+1)+"行"+headerName+"属性值是日期，请修改例如2000-10-10");
				   		}
		    		}
			    }
			}
			if(StringUtils.isNotEmpty(errorList)){
				totalerrorList.addAll(errorList);
				continue;
			}
			DeliverGoods bean = ObjUtils.convertToObject(DeliverGoods.class, list.get(i));
			flag = oMap.get("id");
			if(flag == null) {
				bean.setTmUsed(1);
				bean.setId(TMUID.getUID());
				insertList.add(bean);
			}else {
				updateList.add(bean);
			}
		}
		//转成string类型，并添加html换行符
		String errorMessage = totalerrorList.stream().collect(Collectors.joining("<br/>"));
		if(StringUtils.isNotEmpty(errorMessage)) {
			throw new Exception(errorMessage);
		}
		if(StringUtils.isNotEmpty(insertList)){
			return this.insertDeliverGoods(insertList);
        }
        if(StringUtils.isNotEmpty(updateList)){
        	return this.updateDeliverGoods(updateList);
        }
        return null;
		
	}
}
