package com.yunhesoft.leanCosting.baseConfig.entity.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CostclassSaveDto {
	private String id;
	@ApiModelProperty(value="父分类ID")
	private String Pid;


	@ApiModelProperty(value="核算单元ID")
	private String Unitcode;


	@ApiModelProperty(value="名称")
	private String Ccname;


	@ApiModelProperty(value="注释")
	private String Memo;


	/** 无用 */
    private Integer tmused;
    
    /** 排序 */
    private Integer tmsort;

	@ApiModelProperty(value="分类类型")
	private String Cctype;


}
