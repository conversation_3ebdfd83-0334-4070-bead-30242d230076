package com.yunhesoft.leanCosting.baseConfig.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.baseConfig.service.ICostEnvironmentConfigServer;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/baseConfig/Config")
@Api(tags = "TAB页签切换配置")
public class CostEnvironmentConfigController {

	@Autowired
	private ICostEnvironmentConfigServer costEnvironmentConfigServer;
	
	
	@RequestMapping(value = "/getConfig", method = RequestMethod.POST)
	@ApiOperation("获得核算对象名称-数据库修改后名称")
	public Res<?> getConfig() {
		Res<String> res = new Res<String>();
		res.setResult(costEnvironmentConfigServer.getConfig());
		return res;
	}
	
	
	
}
