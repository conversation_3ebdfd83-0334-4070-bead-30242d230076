package com.yunhesoft.leanCosting.baseConfig.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.baseConfig.entity.po.ProjectType;
import com.yunhesoft.leanCosting.baseConfig.service.impl.CostProjectTypeSeriveceIpml;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/baseConfig/projectType")
@Api(tags = "成本项目分类")
public class CostProjectTypeController {
	
	@Autowired CostProjectTypeSeriveceIpml iservice;
	
	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation("获取数据")
	public Res<?> getData() {
		Res<List<ProjectType>> res = new Res<List<ProjectType>>();
		List<ProjectType> list =iservice.getData();
		res.setResult(list);
		return res;
	}
	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation("保存数据")
	public Res<?> saveData(@RequestBody List<ProjectType> bean) {
		String message = iservice.save(bean);
		Res<String> res = new Res<String>();
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}
	
	@RequestMapping(value = "/deleteData", method = RequestMethod.POST)
	@ApiOperation("保存数据")
	public Res<?> delData(@RequestBody List<ProjectType> bean) {
		String message = iservice.delete(bean);
		Res<String> res = new Res<String>();
		if (message.trim().length() > 0) {
			res.setSuccess(false);
			res.setMessage(message);
		} else {
			res.setSuccess(true);
		}
		return res;
	}
}
