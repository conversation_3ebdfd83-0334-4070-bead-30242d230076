package com.yunhesoft.leanCosting.baseConfig.service.impl;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.constants.UserConstants;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibParamSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibSampledotSaveDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.CostEnvironmentConfig;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibItem;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibParam;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibSampledot;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.baseConfig.service.IToolService;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostReportQueryDto;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostSummaryParamData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamInstrumentData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamItemData;
import com.yunhesoft.leanCosting.costReport.entity.po.CostTeamParamData;
import com.yunhesoft.leanCosting.costReport.entity.vo.CostSumMaryVo;
import com.yunhesoft.leanCosting.costReport.service.ICostChangeShiftsService;
import com.yunhesoft.leanCosting.costReport.service.ICostWeeklyService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostuintQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostItemFormula;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostMeteringUnit;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costindicator;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costinstrument;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costitem;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampleclass;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.entity.SysDictType;
import com.yunhesoft.system.tools.dict.service.ISysDictDataService;
import com.yunhesoft.system.tools.dict.service.ISysDictTypeService;

/**
 * 工具类
 * 
 * <AUTHOR>
 *
 */
@Service
public class ToolServiceImpl implements IToolService {
	@Autowired
	private ISysDictDataService sysDictDataService;
	@Autowired
	private ISysDictTypeService dictTypeService; // 数据字典类型服务
	@Autowired
	private ISysDictDataService dictDataService; // 数据字典数据服务
	@Autowired
	private EntityService entityService;
	@Autowired
	private ICostuintService costuintService;
	@Autowired
	private ICostChangeShiftsService costChangeShiftsService;
	@Autowired
	private ICostWeeklyService costWeeklyService;
	/**
	 * 周报
	 */
	// 核算项目
	public static String HSXMTYPE = "COSTSUMMARYITEMDATA";
	// 核算指标
	public static String HSZBTYPE = "COSTSUMMARYPARAMDATA";
	/**
	 * 交接班
	 */
	// 核算项目
	public static String JJBHSXMTYPE = "COSTTEAMITEMDATA";
	// 核算仪表
	public static String JJBHSYBTYPE = "COSTTEAMINSTRUMENTDATA";
	// 核算指标
	public static String JJBHSZBTYPE = "CostTeamParamData";
	/**
	 * 同步项目库
	 */
	private static final String ITEMSYNC = "itemsync";//产品
	private static final String ITEMFYXM = "itemfyxm";//费用

	/**
	 * 产品项目
	 * 
	 * @return
	 */
	@Override
	public CostEnvironmentConfig getItemsync() {
		CostEnvironmentConfig bean = null;
		Where where = Where.create();
		where.eq(CostEnvironmentConfig::getPageConfig, ToolServiceImpl.ITEMSYNC);
		where.eq(CostEnvironmentConfig::getConfigParam, ToolServiceImpl.ITEMSYNC);
		Order order = Order.create();
		order.orderByAsc(CostEnvironmentConfig::getTmSort);
		List<CostEnvironmentConfig> configs = entityService.queryList(CostEnvironmentConfig.class, where, order);
		if (configs.size() > 0) {
			bean = configs.get(0);
		} else {
			bean = new CostEnvironmentConfig();
			bean.setId(TMUID.getUID());
			bean.setPageConfig(ToolServiceImpl.ITEMSYNC);
			bean.setConfigParam(ToolServiceImpl.ITEMSYNC);
			bean.setIsVisible(1);
			bean.setParamName("产品");
			entityService.insert(bean);
		}
		if (bean != null) {
			if (bean.getIsVisible() == 1) {
				return bean;
			}
			return null;
		}
		return bean;
	}

	/**
	 * 费用项目
	 * 
	 * @return
	 */
	@Override
	public CostEnvironmentConfig getItemsyncFyItem() {
		CostEnvironmentConfig bean = null;
		Where where = Where.create();
		where.eq(CostEnvironmentConfig::getPageConfig, ToolServiceImpl.ITEMFYXM);
		where.eq(CostEnvironmentConfig::getConfigParam, ToolServiceImpl.ITEMFYXM);
		Order order = Order.create();
		order.orderByAsc(CostEnvironmentConfig::getTmSort);
		List<CostEnvironmentConfig> configs = entityService.queryList(CostEnvironmentConfig.class, where, order);
		if (configs.size() > 0) {
			bean = configs.get(0);
		} else {
			bean = new CostEnvironmentConfig();
			bean.setId(TMUID.getUID());
			bean.setPageConfig(ToolServiceImpl.ITEMFYXM);
			bean.setConfigParam(ToolServiceImpl.ITEMFYXM);
			bean.setIsVisible(1);
			bean.setParamName("费用");
			entityService.insert(bean);
		}
		if (bean != null) {
			if (bean.getIsVisible() == 1) {
				return bean;
			}
			return null;
		}
		return bean;
	}

	/**
	 * 通过dictType字段，查询数据字典
	 * 
	 * @param dictType
	 * @return
	 */
	@Override
	public List<SysDictData> getDataList(String dictType) {
		SysDictData dictData = new SysDictData();
		dictData.setDictType(dictType);// 项目使用
		List<SysDictData> datas = sysDictDataService.selectDictDataList(dictData);
		List<SysDictData> list = new ArrayList<SysDictData>();
		for (SysDictData sysDictData : datas) {
			String status = sysDictData.getStatus();
			if ("0".equals(status)) {
				list.add(sysDictData);
			}
		}
		return list;
	}

	@Override
	public Map<Integer, String> getWeep() {
		Map<Integer, String> map = new LinkedHashMap<Integer, String>();
		for (int i = 1; i <= 7; i++) {
			String val = "";
			if (i == 1) {
				val = "第一周";
			} else if (i == 2) {
				val = "第二周";
			} else if (i == 3) {
				val = "第三周";
			} else if (i == 4) {
				val = "第四周";
			} else if (i == 5) {
				val = "第五周";
			} else if (i == 6) {
				val = "第六周";
			} else if (i == 7) {
				val = "第七周";
			}
			map.put(i, val);
		}
		return map;
	}

	@Override
	public void weekCostSumMaryVo(CostSumMaryVo costSumMaryVo, CostSummaryParamData vo, String id, String type,
			Integer digit) {
		// 计算值
		Double calcVal = vo.getCalcVal();
		costSumMaryVo.setClassId("hszb");
		costSumMaryVo.setClassName("核算指标");
		costSumMaryVo.setId(id);
		costSumMaryVo.setType(type);
		Double value = calcVal;
		if (calcVal != null) {
			value = calcVal;
		}

		if (digit != null) {
			/**
			 * 保留小数位数
			 */
			String formats = "0.";
			for (int i = 0; i < digit; i++) {
				formats += "0";
			}
			// 消耗量
			DecimalFormat df1 = new DecimalFormat(formats);
			String str = df1.format(value);
			/**
			 * END
			 */
			// 消耗量
			costSumMaryVo.setWriteConsumption(str);
		} else {
			costSumMaryVo.setWriteConsumption(value.toString());
		}
	}

	@Override
	public void jjbCostParamVo(CostSumMaryVo costSumMaryVo, CostTeamParamData vo, String id, String type,
			Integer digit) {
		// 计算值
		Double calcVal = vo.getCalcVal();
		costSumMaryVo.setClassId("hszb");
		costSumMaryVo.setClassName("核算指标");
		costSumMaryVo.setId(id);
		costSumMaryVo.setType(type);
		Double value = calcVal;
		if (calcVal != null) {
			value = calcVal;
		}
		/**
		 * 保留小数位数
		 */
		String formats = "0.";
		for (int i = 0; i < digit; i++) {
			formats += "0";
		}
		// 消耗量
		DecimalFormat df1 = new DecimalFormat(formats);
		String str = df1.format(value);
		/**
		 * END
		 */
		// 消耗量
		costSumMaryVo.setXhl(str);
	}

	@Override
	public void jjbCostInstrumentVo(CostSumMaryVo costSumMaryVo, CostTeamInstrumentData vo, String id, String type,Integer fyxm) {
		costSumMaryVo.setId(id);
		costSumMaryVo.setType(type);
		if (fyxm==0) {
			costSumMaryVo.setDbxh(vo.getCalcVal());
			costSumMaryVo.setPreviousReadOut(vo.getPreviousReadOut());
			costSumMaryVo.setLastReadOut(vo.getLastReadOut());
		} else {
			costSumMaryVo.setDbxh(null);
			costSumMaryVo.setPreviousReadOut(null);
			costSumMaryVo.setLastReadOut(null);
		}
	}

	@Override
	public void initCostBeanCostInfo() {
		this.initDictInfo("leanCosting", "成本项目分类数据字典", "calculate_type");
		this.initDictInfo("leanCosting", "汇总方式数据字典", "sum_type");
		this.initDictInfo("leanCosting", "成本项目公式字典", "FTYPE");
		this.initDictInfo("leanCosting", "编辑项目公式字典", "UFTYPE");
		this.initDictInfo("leanCosting", "编辑仪表公式字典", "UINSTRTYPE");
		this.initDictInfo("leanCosting", "单位成本类型字典", "FMLUSECLASSTYPE");
		this.initDictInfo("leanCosting", "价格公式类型字典", "FORMULADATATYPE");
		this.initDictInfo("leanCosting", "核算项目分类类型字典", "COSTCLASSTYPE");
	}

	/**
	 * 初始化数据字典信息
	 */
	private void initDictInfo(String moduleCode, String dictName, String dictType) {
		// 字典类型
		SysDictType typeObj = new SysDictType();
		typeObj.setModuleCode(moduleCode);
		typeObj.setDictName(dictName);
		typeObj.setDictType(dictType);
		typeObj.setStatus("0");
		if (!UserConstants.NOT_UNIQUE.equals(dictTypeService.checkDictTypeUnique(typeObj))) {
			dictTypeService.insertDictType(typeObj);
		}
		// 字典数据（按照类型检索）
		List<SysDictData> queryList = dictTypeService.selectDictDataByType(dictType);
		if (StringUtils.isEmpty(queryList)) { // 无数据
			LinkedHashMap<String, String> dataMap = this.getDictInitDataMap(dictType); // 获取初始化数据
			LinkedHashMap<String, String> remarkMap = this.getDictInitRemarkMap(dictType); // 备注
			if (StringUtils.isNotEmpty(dataMap)) {
				Iterator<Entry<String, String>> entryMap = dataMap.entrySet().iterator();
				int num = 0;
				while (entryMap.hasNext()) {
					num += 1;
					Entry<String, String> entry = entryMap.next();
					SysDictData dataObj = new SysDictData();
					dataObj.setDictSort(Long.valueOf(num));
					dataObj.setDictLabel(entry.getValue());
					dataObj.setDictValue(entry.getKey());
					dataObj.setDictType(dictType);
					dataObj.setStatus("0");

					if (remarkMap.containsKey(dataObj.getDictValue())) {
						String remark = remarkMap.get(dataObj.getDictValue());
						dataObj.setRemark(remark);
					}
					dictDataService.insertDictData(dataObj);
				}
			}
		}
	}

	/**
	 * 获取数据字典初始化数据
	 * 
	 * @return
	 */
	private LinkedHashMap<String, String> getDictInitDataMap(String dictType) {
		LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
		if (StringUtils.isNotEmpty(dictType)) {
			if ("calculate_type".equals(dictType)) { // 成本项目分类数据字典
				map.put("rawMaterials", "原料");
				map.put("fuels", "燃动");
				map.put("costs", "费用");
				map.put("products", "产品");
				map.put("others", "其它");
			} else if ("sum_type".equals(dictType)) { // 汇总方式数据字典
				map.put("0", "累加");
				map.put("1", "均值");
			} else if ("FTYPE".equals(dictType)) { // 成本项目公式字典
				map.put("hxl", "消耗量");
				map.put("dh", "单耗");
				map.put("dwcb", "单位成本");
				map.put("zcb", "总成本");
				map.put("khxhl", "考核消耗量");
				map.put("khzcb", "考核成本");
			} else if ("UFTYPE".equals(dictType)) { // 编辑项目公式字典
				map.put("khdj", "考核单价");
				map.put("bzdj", "标准单价");
				map.put("khzcb", "年计划费用");
				map.put("khdh", "考核单耗");
				map.put("khdwcb", "考核单位成本");
				map.put("hsxs", "换算系数");
				map.put("dkhz", "分摊因子");
				map.put("nhxs", "能耗系数");
				map.put("xhl", "项目消耗量");
				map.put("xjxhl", "下级项目量");
			} else if ("UINSTRTYPE".equals(dictType)) { // 编辑仪表公式字典
				map.put("ybxs", "仪表系数");
				map.put("yblc", "仪表量程");
				map.put("qbs", "前表数");
				map.put("hbs", "后表数");
				map.put("dbxhl", "仪表消耗量");
			} else if ("FMLUSECLASSTYPE".equals(dictType)) { // 单位成本类型字典
				map.put("0", "计算原料的单位加工费");
				map.put("1", "计算产品的单位成本");
			} else if ("FORMULADATATYPE".equals(dictType)) { // 价格公式类型字典
				map.put("rawMaterialFormula", "中间原料价格公式");
				map.put("priceFormula", "中间产品价格公式");
			} else if ("COSTCLASSTYPE".equals(dictType)) { // 核算项目分类类型字典
				map.put("yl", "原料");
				map.put("fzcl", "辅助材料");
				map.put("rl", "燃料");
				map.put("dl", "动力");
				map.put("fy", "费用");
				map.put("fcp", "副产品");
				map.put("cp", "产品");
			}
		}
		return map;
	}

	/**
	 * 备注
	 * 
	 * @param dictType
	 * @return
	 */
	private LinkedHashMap<String, String> getDictInitRemarkMap(String dictType) {
		LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
		if (StringUtils.isNotEmpty(dictType)) {
			if ("FORMULADATATYPE".equals(dictType)) { // 价格公式类型字典
				map.put("rawMaterialFormula", "[上游装置原料单价]+[上游装置单位加工费])*[项目系数]')");
				map.put("priceFormula", "([下游装置原料单价]+[下游装置单位加工费])*[项目系数]')");
			} else if ("UFTYPE".equals(dictType)) { // 编辑项目公式字典
				map.put("xhl", "hxl");
			}
		}
		return map;
	}

	/**
	 * 交接班数据整理所属函数-属于整理函数
	 */
	/**
	 * 查询汇总表项目信息(交接班)
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public LinkedHashMap<String, CostTeamItemData> getChangeShiftsCostTeamItemDatas(String pid) {
		LinkedHashMap<String, CostTeamItemData> paramMap = new LinkedHashMap<String, CostTeamItemData>();
		String itemid;
		List<CostTeamItemData> lists = costChangeShiftsService.getChangeShiftsCostTeamItemDatas(pid);
		for (CostTeamItemData vo : lists) {
			itemid = vo.getItemId();
			if (!paramMap.containsKey(itemid)) {
				paramMap.put(itemid, vo);
			}
		}
		return paramMap;
	}

	/**
	 * 查询核算指标数据(交接班)
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public LinkedHashMap<String, CostTeamParamData> getChangeShiftsCostTeamParamDatas(String pid) {
		LinkedHashMap<String, CostTeamParamData> paramMap = new LinkedHashMap<String, CostTeamParamData>();
		String paramid;
		List<CostTeamParamData> lists = costChangeShiftsService.getChangeShiftsCostTeamParamDatas(pid);
		for (CostTeamParamData vo : lists) {
			paramid = vo.getParamId();
			if (!paramMap.containsKey(paramid)) {
				paramMap.put(paramid, vo);
			}
		}
		return paramMap;
	}

	/**
	 * 查询汇总表仪表信息(交接班)0
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public LinkedHashMap<String, List<CostTeamInstrumentData>> getChangeShiftsCostTeamInstrumentData(String pid) {
		LinkedHashMap<String, List<CostTeamInstrumentData>> paramMap = new LinkedHashMap<String, List<CostTeamInstrumentData>>();
		String ybid;
		List<CostTeamInstrumentData> lists = costChangeShiftsService.getChangeShiftsCostTeamInstrumentData(pid);
		for (CostTeamInstrumentData vo : lists) {
			ybid = vo.getInstrumentId();
			if (paramMap.containsKey(ybid)) {
				paramMap.get(ybid).add(vo);
			}else {
				List<CostTeamInstrumentData> costTeamInstrumentDatas = new ArrayList<CostTeamInstrumentData>();
				costTeamInstrumentDatas.add(vo);
				paramMap.put(ybid, costTeamInstrumentDatas);
			}
		}
		return paramMap;
	}

	/**
	 * 交接班数据整理所属函数-属于整理函数END
	 */

	/**
	 * 周报班数据整理所属函数-属于整理函数
	 */
	/**
	 * 核算项目
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public LinkedHashMap<String, CostSummaryItemData> getWeepCostSummaryItemDatas(CostReportQueryDto dto) {
		List<CostSummaryItemData> costSummaryItemDatas = costWeeklyService.getWeepCostSummaryItemDatas(dto);
		LinkedHashMap<String, CostSummaryItemData> itemMap = new LinkedHashMap<String, CostSummaryItemData>();
		for (CostSummaryItemData vo : costSummaryItemDatas) {
			String key = vo.getItemId();
			if (!itemMap.containsKey(key)) {
				itemMap.put(key, vo);
			}
		}
		return itemMap;
	}

	/**
	 * 核算指标
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public LinkedHashMap<String, CostSummaryParamData> getWeepCostSummaryParamDatas(CostReportQueryDto dto) {
		List<CostSummaryParamData> lists = costWeeklyService.getWeepCostSummaryParamDatas(dto);
		LinkedHashMap<String, CostSummaryParamData> itemMap = new LinkedHashMap<String, CostSummaryParamData>();
		for (CostSummaryParamData vo : lists) {
			String key = vo.getParamId();
			if (!itemMap.containsKey(key)) {
				itemMap.put(key, vo);
			}
		}
		return itemMap;
	}
	
	
	@Override
	public List<CostItemFormula> initCostItemFormula(Costclass costclass,Costuint costuint, Costitem costitem,List<Costinstrument> costinstruments,Devicetypelibrary devicetypelibrary,List<String> fTypes) {
		List<CostItemFormula> formulas = new ArrayList<CostItemFormula>();
		String unitcode = costitem.getUnitid();// 核算对象ID
		String beginTime = costitem.getBegintime();// 版本
		String id = costitem.getId();// 项目ID
		Integer fmluseclass = devicetypelibrary.getFmluseclass();// 设备类型库类型
		// a、根据核算对象的单元类型得到核算单元的计算方式：计算原料的单位加工费还是计算产品的单位成本
		// b、当计算方式是计算原料的单位加工费，找到核算单元下分类类型是原料的分类；是计算产品的单位成本时，找到核算单元下分类类型是产品的分类
		String formula_ = "";// 分类项目量合计
		String Cformula_ = "";// 分类项目量合计
		if (fmluseclass == 0) {// 计算原料的单位加工费
			formula_ = costuint.getId() + ".原料.xhl";
			Cformula_ = costuint.getName() + ".原料.消耗量";
		} else if (fmluseclass == 1) {// 计算产品的单位成本
			formula_ = costuint.getId() + ".产品.xhl";
			Cformula_ = costuint.getName() + ".产品.消耗量";
		}
		// 注意：当项目分类是产品时，作为分母的就是原料分类的项目量合计。
		String classType = costclass.getCctype();// 分类
		if ("产品".equals(classType)) {
			formula_ = costuint.getId() + ".原料.xhl";
			Cformula_ = costuint.getName() + ".原料.消耗量";
		}

		String costItemId = costuint.getId() + "." + costitem.getId() + ".";
		String costItemVal = costuint.getName() + "." + costitem.getItemname() + ".";
		// c、单耗的默认公式：项目消耗量/b步骤找到的分类项目量合计
		if(fTypes.contains("dh")) {
			CostItemFormula dhFormula = init(unitcode, beginTime, "dh", id);// 单耗
			StringBuffer dhformulas = new StringBuffer();
			StringBuffer cdhformulas = new StringBuffer();
			dhformulas.append(costItemId + "xhl/" + formula_);
			cdhformulas.append(costItemVal + "项目消耗量/" + Cformula_);
			dhFormula.setFormula(dhformulas.toString());
			dhFormula.setCFormula(cdhformulas.toString());
			dhFormula.setId(TMUID.getUID());
			formulas.add(dhFormula);
		}
		if(fTypes.contains("dwcb")) {
			// d、单位成本的默认公式：项目消耗量*项目单价/b步骤找到的分类项目量合计
			CostItemFormula dwcbFormula = init(unitcode, beginTime, "dwcb", id);// 单位成本
			StringBuffer dwcbformulas = new StringBuffer();
			StringBuffer cdwcbformulas = new StringBuffer();
			dwcbformulas.append(costItemId + "khdj*" + costItemId + "xhl/" + formula_);
			cdwcbformulas.append(costItemVal + "考核单价*" + costItemVal + "项目消耗量/" + Cformula_);
			dwcbFormula.setFormula(dwcbformulas.toString());
			dwcbFormula.setCFormula(cdwcbformulas.toString());
			dwcbFormula.setId(TMUID.getUID());
			formulas.add(dwcbFormula);
		}
		if(fTypes.contains("zcb")) {
			// e、总成本的默认公式：项目消耗量*项目单价
			CostItemFormula zcbFormula = init(unitcode, beginTime, "zcb", id);// 总成本
			StringBuffer zcbformulas = new StringBuffer();
			StringBuffer czcbformulas = new StringBuffer();
			zcbformulas.append(costItemId + "khdj*" + costItemId + "xhl");
			czcbformulas.append(costItemVal + "考核单价*" + costItemVal + "项目消耗量");
			zcbFormula.setFormula(zcbformulas.toString());
			zcbFormula.setCFormula(czcbformulas.toString());
			zcbFormula.setId(TMUID.getUID());
			formulas.add(zcbFormula);
		}
		if(fTypes.contains("khxhl")) {
			// f、考核消耗量的默认公式：项目的单耗定额*b步骤找到的分类项目量合计
			CostItemFormula khxhlFormula = init(unitcode, beginTime, "khxhl", id);// 考核消耗量
			StringBuffer khxhlformulas = new StringBuffer();
			StringBuffer ckhxhlformulas = new StringBuffer();
			khxhlformulas.append(costItemId + "khdh*" + formula_);
			ckhxhlformulas.append(costItemVal + "考核单耗*" + Cformula_);
			khxhlFormula.setFormula(khxhlformulas.toString());
			khxhlFormula.setCFormula(ckhxhlformulas.toString());
			khxhlFormula.setId(TMUID.getUID());
			formulas.add(khxhlFormula);
		}
		if(fTypes.contains("khzcb")) {
			// g、考核成本的默认公式：项目的单耗定额*b步骤找到的分类项目量合计*项目单价
			CostItemFormula khzcbformula = init(unitcode, beginTime, "khzcb", id);// 考核成本
			StringBuffer khzcbformulas = new StringBuffer();
			StringBuffer ckhzcbformulas = new StringBuffer();
			khzcbformulas.append(costItemId + "khdh*" + formula_ + "*" + costItemId + "khdj");
			ckhzcbformulas.append(costItemVal + "考核单耗*" + Cformula_ + "*" + costItemVal + "考核单价");
			khzcbformula.setFormula(khzcbformulas.toString());
			khzcbformula.setCFormula(ckhzcbformulas.toString());
			khzcbformula.setId(TMUID.getUID());
			formulas.add(khzcbformula);
		}
		if(fTypes.contains("hxl")) {
			if(costinstruments!=null) {
				CostItemFormula _formula = new CostItemFormula();
				_formula.setId(TMUID.getUID());
				_formula.setUnitId(unitcode);
				_formula.setBegintime(beginTime);
				_formula.setFType("hxl");
				_formula.setPid(costitem.getId());
				// 核算项目.项目名称
				String xmId = costuint.getId() + "." + costitem.getId() + ".";
				String xmName = costuint.getName() + "." + costitem.getItemname() + ".";
				// 查询所有
				String costItemId1 = "";
				String costItemVali = "";
				for (Costinstrument costinstrument : costinstruments) {
					// 核算对象名称.项目名称.
					costItemId1 += "+" + costuint.getId() + "." + costinstrument.getId() + ".dbxhl";
					costItemVali += "+" + costuint.getName() + "." + costinstrument.getName() + ".仪表消耗量";
				}
				if (costItemId1.length() > 0) {
					costItemId1 = costItemId1.substring(1);
					costItemVali = costItemVali.substring(1);
				}
				// *项目仪表【200万加氢精制.焦化馏份油.换算系数】
				costItemId1 = "(" + costItemId1 + ")*" + xmId + "hsxs";
				costItemVali = "(" + costItemVali + ")*" + xmName + "换算系数";
				_formula.setCFormula(costItemVali.toString());
				_formula.setFormula(costItemId1.toString());
				formulas.add(_formula);
			}
		}
		return formulas;
	}
	/**
	 * 初始化项目公式内容
	 * 
	 * @param unitcode
	 * @param beginTime
	 * @param fType
	 * @param id
	 * @return
	 */
	private CostItemFormula init(String unitcode, String beginTime, String fType, String id) {
		CostItemFormula formula = new CostItemFormula();
		formula.setUnitId(unitcode);
		formula.setFType(fType);
		formula.setBegintime(beginTime);
		formula.setPid(id);
		return formula;
	}


	/**
	 * 
	 */
	@Override
	public CostItemFormula initCostInstrumentFormula(Costuint costuint, Costinstrument costinstrument) {
		// 核算对象
		String unitcode = costinstrument.getUnitid();
		String beginTime = costinstrument.getBegintime();// 版本
		String id = costinstrument.getId();// 仪表ID
		// 准备数据已完成
		CostItemFormula formula = new CostItemFormula();
		formula.setId(TMUID.getUID());
		formula.setUnitId(unitcode);
		formula.setBegintime(beginTime);
		formula.setPid(id);
		formula.setFType("dbxh");
		// 核算对象名称.项目名称.
		String costItemId = costuint.getId() + "." + costinstrument.getId() + ".";
		String costItemVal = costuint.getName() + "." + costinstrument.getName() + ".";
		// 具体逻辑公式
		StringBuffer formulas = new StringBuffer();
		StringBuffer cformulas = new StringBuffer();
		cformulas.append("if(" + costItemVal + "后表数>=" + costItemVal + "前表数,(" + costItemVal + "后表数-" + costItemVal
				+ "前表数)*" + costItemVal + "仪表系数,");
		cformulas.append(
				"(" + costItemVal + "仪表量程+" + costItemVal + "后表数-" + costItemVal + "前表数)*" + costItemVal + "仪表系数)");
		formulas.append("if(" + costItemId + "hbs>=" + costItemId + "qbs,(" + costItemId + "hbs-" + costItemId + "qbs)*"
				+ costItemId + "ybxs,");
		formulas.append("(" + costItemId + "yblc+" + costItemId + "hbs-" + costItemId + "qbs)*" + costItemId + "ybxs)");
		formula.setCFormula(cformulas.toString());// 公式【ZZW6D9CI03QRS20XE70242.ZZW6D9CI03QRS28IWJ0266.xhl*ZZW6D9CI03QRS20XE70242.ZZW6D9CI03QRS28IWJ0266.bzdj】
		formula.setFormula(formulas.toString());// 公式翻译【200万加氢精制.焦化馏份油.项目消耗量*200万加氢精制.焦化馏份油.标准单价】
		return formula;
	}

	@Override
	public Map<String, List<CostItemFormula>> initCostItemFormula_(Map<String, Costitem> costitemMap,
			Map<String, Costuint> costuintMap, List<CostItemFormula> formulas) {
		Map<String, List<CostItemFormula>> formulaMap = new LinkedHashMap<String, List<CostItemFormula>>();
		for (CostItemFormula formula : formulas) {
			String unitcode = formula.getUnitId();
			String beginTime = formula.getBegintime();
			String pid = formula.getPid();
			String key = unitcode + "_" + beginTime + "_" + pid;
			List<CostItemFormula> listCostItemFormulas = new ArrayList<CostItemFormula>();
			if (formulaMap.containsKey(key)) {
				listCostItemFormulas = formulaMap.get(key);
			}
			listCostItemFormulas.add(formula);
			formulaMap.put(key, listCostItemFormulas);
		}

		/**
		 * Pid，仪表
		 */
		List<Costinstrument> costinstruments = costuintService.getCostinstrumentDatas(new CostuintQueryDto());
		Map<String, List<Costinstrument>> _pidCostinstrumentsMap = new LinkedHashMap<String, List<Costinstrument>>();
		for (Costinstrument costinstrument : costinstruments) {
			String pid = costinstrument.getPid();
			// Pid
			List<Costinstrument> listCostItemFormulas1 = new ArrayList<Costinstrument>();
			if (_pidCostinstrumentsMap.containsKey(pid)) {
				listCostItemFormulas1 = _pidCostinstrumentsMap.get(pid);
			}
			listCostItemFormulas1.add(costinstrument);
			_pidCostinstrumentsMap.put(pid, listCostItemFormulas1);
		}

		for (Entry<String, List<Costinstrument>> entry : _pidCostinstrumentsMap.entrySet()) {
			List<Costinstrument> list = entry.getValue();
			String key = entry.getKey();
//			System.out.println("pid:" + key);
//			System.out.println("value:" + list.size());
		}

		Map<String, List<CostItemFormula>> map = new LinkedHashMap<String, List<CostItemFormula>>();
		List<CostItemFormula> adds = new ArrayList<CostItemFormula>();
		List<CostItemFormula> updatas = new ArrayList<CostItemFormula>();
		for (Entry<String, Costitem> entry : costitemMap.entrySet()) {
			String key = entry.getKey();
			Costitem _costitem = entry.getValue();
			String costItemId = _costitem.getId();
			String costItemName = _costitem.getItemname();
			String unitcode = _costitem.getUnitid();
			String beginTime = _costitem.getBegintime();

			Costuint _costuint = costuintMap.get(entry.getValue().getUnitid());
			// 核算项目.项目名称
			String xmId = _costuint.getId() + "." + costItemId + ".";
			String xmName = _costuint.getName() + "." + costItemName + ".";
			String key_ = unitcode + "_" + beginTime + "_" + key;
			List<CostItemFormula> costItemFormulas = formulaMap.get(key_);
			CostItemFormula _formula = new CostItemFormula();
			if (costItemFormulas != null && costItemFormulas.size() > 0) {
				_formula = costItemFormulas.get(0);
				_formula.setUnitId(unitcode);
				_formula.setBegintime(beginTime);
				_formula.setFType("hxl");
				_formula.setPid(costItemId);
			} else {
				_formula.setUnitId(unitcode);
				_formula.setBegintime(beginTime);
				_formula.setFType("hxl");
				_formula.setPid(costItemId);
			}
			// 查询所有
			List<Costinstrument> _costinstruments = _pidCostinstrumentsMap.get(costItemId);
			String costItemId1 = "";
			String costItemVali = "";
			for (Costinstrument costinstrument : _costinstruments) {
				// 核算对象名称.项目名称.
				costItemId1 += "+" + _costuint.getId() + "." + costinstrument.getId() + ".dbxhl";
				costItemVali += "+" + _costuint.getName() + "." + costinstrument.getName() + ".仪表消耗量";
			}
			if (costItemId1.length() > 0) {
				costItemId1 = costItemId1.substring(1);
				costItemVali = costItemVali.substring(1);
			}
			// *项目仪表【200万加氢精制.焦化馏份油.换算系数】
			costItemId1 = "(" + costItemId1 + ")*" + xmId + "hsxs";
			costItemVali = "(" + costItemVali + ")*" + xmName + "换算系数";
			_formula.setCFormula(costItemVali.toString());
			_formula.setFormula(costItemId1.toString());

			if (_formula.getId() == null) {
				_formula.setId(TMUID.getUID());
				adds.add(_formula);
			} else {
				updatas.add(_formula);
			}
			if(adds.size()>0) {
				map.put("insert", adds);
			}
			if(updatas.size()>0) {
				map.put("updata", updatas);
			}
		}
		return map;
	}

	/**
	 * 单元类型-复制
	 */
	/**
	 * 费用项目-项目分类
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @param ccType
	 * @return
	 */
	@Override
	public Costclass getCostclass(String maxVersion, String unitCode) {
		Where where = Where.create();
		where.eq(Costclass::getTmused, 1);
		where.eq(Costclass::getBegintime, maxVersion);
		where.eq(Costclass::getUnitid, unitCode);
		Order order = Order.create();
		order.orderByAsc(Costclass::getTmsort);
		List<Costclass> indexList = entityService.queryList(Costclass.class, where, order);
		Costclass sampleclass = null;
		int max = 0;
		for (Costclass bean : indexList) {
			String name = bean.getCctype();
			int sort = bean.getTmsort();
			if(max>sort) {
				max = sort;
			}
			if("费用".equals(name)) {
				sampleclass = bean;
			}
		}
		if(sampleclass==null) {//没有控制指标名称，需要新增数据
			sampleclass = new Costclass();
			sampleclass.setBegintime(maxVersion);
			sampleclass.setCctype("费用");
			sampleclass.setCcname("费用");
			sampleclass.setUnitid(unitCode);
			sampleclass.setTmused(1);
			sampleclass.setTmsort(max++);
			sampleclass.setPid("root");
			sampleclass.setId(TMUID.getUID());
			entityService.insert(sampleclass);
		}
		return sampleclass;
	}
	
	/**
	 * 获得-项目基础数据
	 * @param maxVersion
	 * @param unitCode
	 * @param classId
	 * @return
	 */
	@Override
	public Map<String, Costitem> getCostitems(String maxVersion, String unitCode,String classId){
		Where wherei = Where.create();
		wherei.eq(Costitem::getTmused, 1);
		wherei.eq(Costitem::getBegintime, maxVersion);
		wherei.eq(Costitem::getUnitid, unitCode);
		wherei.eq(Costitem::getPid, classId);
		Order orderi = Order.create();
		orderi.orderByAsc(Costitem::getTmsort);
		List<Costitem> indexList = entityService.queryList(Costitem.class, wherei, orderi);
		List<Costitem> indexLists = new ArrayList<Costitem>();
		for (Costitem costitem : indexList) {
			String deviceTypelibItemId = costitem.getDeviceTypelibItemId();
			if(deviceTypelibItemId!=null&&deviceTypelibItemId.trim().length()>0) {
				indexLists.add(costitem);
			}
		}
		Map<String, Costitem> dataMap = indexLists.stream()
				.collect(Collectors.toMap(Costitem::getDeviceTypelibItemId, Function.identity()));
		return dataMap;
	}
	
	/**
	 * 保存项目基础数据
	 * @param maxVersion
	 * @param unitCode
	 * @param list
	 * @param dataMap
	 * @param dataMap_
	 * @return
	 */
	@Override
	public String saveCostindicatorMap(String maxVersion, String unitCode, List<DeviceTypelibItem> list,
			Map<String, Costitem> dataMap, Map<String, CostMeteringUnit> dataMap_) {
		String result = "";
		List<Costitem> addList = new ArrayList<Costitem>();
		List<Costitem> updataList = new ArrayList<Costitem>();
		for (DeviceTypelibItem bean : list) {
			Costitem vo = ObjUtils.copyTo(bean, Costitem.class);
			String id = vo.getId();
			if (dataMap.containsKey(id)) {
				vo = dataMap.get(id);
				vo.setId(TMUID.getUID());
				vo.setDeviceTypelibItemId(bean.getId());
				updataList.add(vo);
			} else {
				vo.setBegintime(maxVersion);
				vo.setTmused(bean.getTmused());
				vo.setUnitid(unitCode);
				vo.setId(TMUID.getUID());
				vo.setDeviceTypelibItemId(bean.getId());
				if (dataMap_.containsKey(vo.getItemunit())) {
					vo.setItemunit(dataMap_.get(vo.getItemunit()).getMuName());
				}
				addList.add(vo);
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(addList)) {// 新增核算对象
			if (entityService.insertBatch(addList) == 0) {
				result = "添加失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updataList)) {// TODO:修改会有问题//更新核算对象
			if (entityService.updateByIdBatch(updataList) == 0) {
				result = "更新失败！";
			}
		}
		return result;
	}

	/**
	 * 核算指标
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @return
	 */
	@Override
	public Map<String, Costindicator> getCostindicatorMap(String maxVersion, String unitCode) {
		Where where = Where.create();
		where.eq(Costindicator::getTmused, 1);
		where.eq(Costindicator::getBegintime, maxVersion);
		where.eq(Costindicator::getUnitid, unitCode);
//		where.notNull(Costindicator::getDeviceTypelibParamId);
		Order orderi = Order.create();
		orderi.orderByAsc(Costindicator::getTmsort);
		List<Costindicator> indexList = entityService.queryList(Costindicator.class, where, orderi);
		Map<String, Costindicator> dataMap = new LinkedHashMap<String, Costindicator>();
		if(indexList.size()>0) {
			dataMap = indexList.stream()
					.collect(Collectors.toMap(Costindicator::getDeviceTypelibParamId, Function.identity()));
		}
		return dataMap; 
	}
	
	/**
	 * 核算指标-保存
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @param list
	 * @param dataMap
	 * @param dataMap_
	 * @return
	 */
	@Override
	public String saveCostindicator(String maxVersion, String unitCode, List<DeviceTypelibParam> list, Map<String, CostMeteringUnit> dataMap_) {
		Where where = Where.create();
		where.eq(Costindicator::getTmused, 1);
		where.eq(Costindicator::getBegintime, maxVersion);
		where.eq(Costindicator::getUnitid, unitCode);
		Order order = Order.create();
		List<Costindicator> indexList = entityService.queryList(Costindicator.class, where, order);
		Map<String, Costindicator> dataMap = new LinkedHashMap<String, Costindicator>();
		for (Costindicator costindicator : indexList) {
			String deviceTypelibParamId = costindicator.getDeviceTypelibParamId();
			if(deviceTypelibParamId!=null) {
				dataMap.put(deviceTypelibParamId, costindicator);
			}
		}
		List<DeviceTypelibParam> lists = repeatCostindicator(list, unitCode, maxVersion);
		String result = "";
		if(lists.size()>0) {
			List<Costindicator> addList = new ArrayList<Costindicator>();
			List<Costindicator> updataList = new ArrayList<Costindicator>();
			for (DeviceTypelibParam bean : list) {
				Costindicator vo = ObjUtils.copyTo(bean, Costindicator.class);
				String id = vo.getId();
				if (dataMap.containsKey(id)) {
					vo = dataMap.get(id);
					vo.setId(TMUID.getUID());
					vo.setDeviceTypelibParamId(bean.getId());
					updataList.add(vo);
				} else {
					vo.setBegintime(maxVersion);
					vo.setTmused(bean.getTmused());
					vo.setUnitid(unitCode);
					vo.setId(TMUID.getUID());
					vo.setDeviceTypelibParamId(bean.getId());
					if (dataMap_.containsKey(vo.getItemunit())) {
						vo.setItemunit(dataMap_.get(vo.getItemunit()).getMuName());
					}
					addList.add(vo);
				}
			}
			if ("".equals(result) && StringUtils.isNotEmpty(addList)) {// 新增核算对象
				if (entityService.insertBatch(addList) == 0) {
					result = "添加失败！";
				}
			}
			if ("".equals(result) && StringUtils.isNotEmpty(updataList)) {// TODO:修改会有问题//更新核算对象
				if (entityService.updateByIdBatch(updataList) == 0) {
					result = "更新失败！";
				}
			}
		}
			
		return result;
	}
	
	
	
	private List<DeviceTypelibParam> repeatCostindicator(List<DeviceTypelibParam> beans,String unitCode,String maxVersion) {
		List<DeviceTypelibParam> lists = new ArrayList<DeviceTypelibParam>();
		List<Costindicator> list2 = getCostindicator(unitCode);
		List<String> ids = new ArrayList<String>();
		List<String> names = new ArrayList<String>();
		for (Costindicator costunitsampledot : list2) {
			String deviceTypelibSampledotId = costunitsampledot.getDeviceTypelibParamId();
			String name = costunitsampledot.getCpname();
			if(deviceTypelibSampledotId!=null) {
				if(!ids.contains(deviceTypelibSampledotId)) {
					ids.add(deviceTypelibSampledotId);
				}
			}
			if(name!=null) {
				if(!names.contains(name)) {
					names.add(name);
				}
			}
		}
		for (DeviceTypelibParam bean : beans) {
			String id = bean.getId();
			String name = bean.getCpname();
			if(!(ids.contains(id)||names.contains(name))) {
				lists.add(bean);
			}
		}
		return lists;
		
//		
//		List<DeviceTypelibParam> lists = new ArrayList<DeviceTypelibParam>();
//		Map<String, DeviceTypelibParam> idMap = new LinkedHashMap<String, DeviceTypelibParam>();
//		Map<String, String> nameMap = new LinkedHashMap<String, String>();
//		for (DeviceTypelibParam saveDto : beans) {
//			String cpname = saveDto.getCpname();
//			String id = saveDto.getId();
//			nameMap.put(cpname, id);
//			idMap.put(id, saveDto);
//		}
//		List<Costunitsampledot> list2 = getCostunitsampledots(maxVersion,unitCode);
//		for (Costunitsampledot bean : list2) {
//			String ccname = bean.getName();
//			String eviceTypelibSampledotId = bean.getDeviceTypelibSampledotId();
//			if (!nameMap.containsKey(ccname)) {
//				if (!idMap.containsKey(eviceTypelibSampledotId)) {
//					DeviceTypelibParam beanVo = idMap.get(eviceTypelibSampledotId);
//					lists.add(beanVo);
//				}
//			}
//		}
//		return lists;
	}

	
	
	private List<Costindicator> getCostindicator(String unitcode) {
		Where wherei = Where.create();
		wherei.eq(Costindicator::getTmused, 1);
		wherei.eq(Costindicator::getUnitid, unitcode);
		Order orderi = Order.create();
		return entityService.queryList(Costindicator.class, wherei, orderi);
	}

	/**
	 * 采集点-分类
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @return
	 */
	@Override
	public Costunitsampleclass getCostunitsampleclass(String maxVersion, String unitCode) {
		Where where = Where.create();
		where.eq(Costunitsampleclass::getTmused, 1);
		where.eq(Costunitsampleclass::getBegintime, maxVersion);
		where.eq(Costunitsampleclass::getUnitid, unitCode);
		Order order = Order.create();
		order.orderByAsc(Costunitsampleclass::getTmsort);
		List<Costunitsampleclass> indexList = entityService.queryList(Costunitsampleclass.class, where, order);
		Costunitsampleclass sampleclass = null;
		int max = 0;
		for (Costunitsampleclass bean : indexList) {
			String name = bean.getName();
			int sort = bean.getTmsort();
			if(max>sort) {
				max = sort;
			}
			if("控制指标".equals(name)) {
				sampleclass = bean;
			}
		}
		if(sampleclass==null) {//没有控制指标名称，需要新增数据
			sampleclass = new Costunitsampleclass();
			sampleclass.setBegintime(maxVersion);
			sampleclass.setName("控制指标");
			sampleclass.setUnitid(unitCode);
			sampleclass.setTmused(1);
			sampleclass.setTmsort(max++);
			sampleclass.setPid("root");
			sampleclass.setId(TMUID.getUID());
			entityService.insert(sampleclass);
		}
		return sampleclass;
	}

	/**
	 * 采集点数据
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @return
	 */
	@Override
	public List<Costunitsampledot> getCostunitsampledots(String maxVersion,String unitCode) {
		Costunitsampleclass class_ = getCostunitsampleclass(maxVersion,unitCode);
		Where wherei = Where.create();
		wherei.eq(Costunitsampledot::getTmused, 1);
		wherei.eq(Costunitsampledot::getBegintime, maxVersion);
		wherei.eq(Costunitsampledot::getUnitid, unitCode);
		wherei.eq(Costunitsampledot::getPid, class_.getId());
		Order orderi = Order.create();
		return entityService.queryList(Costunitsampledot.class, wherei, orderi);
	}

	/**
	 * 采集点数据保存
	 * 
	 * @param maxVersion
	 * @param unitCode
	 * @param dotId
	 * @param maxtmsort
	 * @param list
	 * @param dataMap
	 * @param dataMap_
	 * @return
	 */
	@Override
	public String saveCostunitsampledot(String maxVersion, String unitCode, String dotId, int maxtmsort,
			List<DeviceTypelibSampledot> list,Map<String, CostMeteringUnit> dataMap_) {
		Where where = Where.create();
		where.eq(Costunitsampledot::getTmused, 1);
		where.eq(Costunitsampledot::getBegintime, maxVersion);
		where.eq(Costunitsampledot::getUnitid, unitCode);
		Order order = Order.create();
		List<Costunitsampledot> indexList = entityService.queryList(Costunitsampledot.class, where, order);
		Map<String, Costunitsampledot> dataMap = new LinkedHashMap<String, Costunitsampledot>();
		for (Costunitsampledot costunitsampledot : indexList) {
			String deviceTypelibParamId = costunitsampledot.getDeviceTypelibSampledotId();
			if(deviceTypelibParamId!=null) {
				dataMap.put(deviceTypelibParamId, costunitsampledot);
			}
		}
		
		List<DeviceTypelibSampledot> lists = repeatCostunitsampledot(list, unitCode, maxVersion);
		String result = "";
		if(lists.size()>0) {
			List<Costunitsampledot> addList = new ArrayList<Costunitsampledot>();
			List<Costunitsampledot> updataList = new ArrayList<Costunitsampledot>();
			for (DeviceTypelibSampledot bean : list) {
				Costunitsampledot vo = ObjUtils.copyTo(bean, Costunitsampledot.class);
				String id = vo.getId();
				if (dataMap.containsKey(id)) {
					vo = dataMap.get(id);
					vo.setId(TMUID.getUID());
					vo.setDeviceTypelibSampledotId(bean.getId());
					updataList.add(vo);
				} else {
					vo.setBegintime(maxVersion);
					vo.setTmused(bean.getTmused());
					vo.setUnitid(unitCode);
					vo.setId(TMUID.getUID());
					vo.setDeviceTypelibSampledotId(bean.getId());
					vo.setCtype("2");
					vo.setSourceype("3");
					vo.setTagnumber(vo.getName());
					vo.setDatasource(vo.getName());// 实时仪表
					vo.setTmsort(maxtmsort++);
					vo.setPid(dotId);
					if (dataMap_.containsKey(vo.getSdUnit())) {
						vo.setSdUnit(dataMap_.get(vo.getSdUnit()).getMuName());
					}
					addList.add(vo);
				}
			}
			if ("".equals(result) && StringUtils.isNotEmpty(addList)) {// 新增核算对象
				if (entityService.insertBatch(addList) == 0) {
					result = "添加失败！";
				}
			}
			if ("".equals(result) && StringUtils.isNotEmpty(updataList)) {
				if (entityService.updateByIdBatch(updataList) == 0) {
					result = "更新失败！";
				}
			}
		}
		return result;
	}

	private List<DeviceTypelibSampledot> repeatCostunitsampledot(List<DeviceTypelibSampledot> beans, String unitCode,
			String maxVersion) {
		List<DeviceTypelibSampledot> lists = new ArrayList<DeviceTypelibSampledot>();
		List<Costunitsampledot> list2 = getCostunitsampledots(maxVersion,unitCode);
		List<String> ids = new ArrayList<String>();
		List<String> names = new ArrayList<String>();
		for (Costunitsampledot costunitsampledot : list2) {
			String deviceTypelibSampledotId = costunitsampledot.getDeviceTypelibSampledotId();
			String name = costunitsampledot.getName();
			if(deviceTypelibSampledotId!=null) {
				if(!ids.contains(deviceTypelibSampledotId)) {
					ids.add(deviceTypelibSampledotId);
				}
			}
			if(name!=null) {
				if(!names.contains(name)) {
					names.add(name);
				}
			}
		}
		for (DeviceTypelibSampledot bean : beans) {
			String id = bean.getId();
			String name = bean.getName();
			if(!(ids.contains(id)||names.contains(name))) {
				lists.add(bean);
			}
		}
		return lists;
	}

	@Override
	public String repeatParamData(List<DeviceTypelibParamSaveDto> beans) {
		String Unitcode = "";	
		Map<String, String> idMap = new LinkedHashMap<String, String>();
		for (DeviceTypelibParamSaveDto saveDto : beans) {
			Unitcode = saveDto.getPid();
			String cpname = saveDto.getCpname();
			String id = saveDto.getId();
			if (idMap.containsKey(cpname)) {
				return "[" + cpname + "]名称重复";
			} else {
				idMap.put(cpname, id);
			}
		}
		List<DeviceTypelibParam> list2 = getDeviceTypelibParam(Unitcode);
		for (DeviceTypelibParam bean1 : list2) {
			String ccname = bean1.getCpname();
			String id = bean1.getId();
			if (idMap.containsKey(ccname)) {
				String _id = idMap.get(ccname);
				if (!_id.equals(id)) {// id不等
					return "[" + ccname + "]名称重复";
				}
			} else {
				idMap.put(ccname, id);
			}
		}
		return "";
	}

	private List<DeviceTypelibParam> getDeviceTypelibParam(String unitCode) {
		Where wherei = Where.create();
		wherei.eq(DeviceTypelibParam::getTmused, 1);
		wherei.eq(DeviceTypelibParam::getPid, unitCode);
		Order orderi = Order.create();
		return entityService.queryList(DeviceTypelibParam.class, wherei, orderi);
	}
	
	public String repeatSampledotData(List<DeviceTypelibSampledotSaveDto> beans) {
		String Unitcode = "";	
		Map<String, String> idMap = new LinkedHashMap<String, String>();
		for (DeviceTypelibSampledotSaveDto saveDto : beans) {
			Unitcode = saveDto.getPid();
			String cpname = saveDto.getName();
			String id = saveDto.getId();
			if (idMap.containsKey(cpname)) {
				return "[" + cpname + "]名称重复";
			} else {
				idMap.put(cpname, id);
			}
		}
		List<DeviceTypelibSampledot> list2 = getSampledotParam(Unitcode);
		for (DeviceTypelibSampledot bean1 : list2) {
			String ccname = bean1.getName();
			String id = bean1.getId();
			if (idMap.containsKey(ccname)) {
				String _id = idMap.get(ccname);
				if (!_id.equals(id)) {// id不等
					return "[" + ccname + "]名称重复";
				}
			} else {
				idMap.put(ccname, id);
			}
		}
		return "";
	}

	private List<DeviceTypelibSampledot> getSampledotParam(String unitcode) {
		Where wherei = Where.create();
		wherei.eq(DeviceTypelibSampledot::getTmused, 1);
		wherei.eq(DeviceTypelibSampledot::getPid, unitcode);
		Order orderi = Order.create();
		return entityService.queryList(DeviceTypelibSampledot.class, wherei, orderi);
	}

}
