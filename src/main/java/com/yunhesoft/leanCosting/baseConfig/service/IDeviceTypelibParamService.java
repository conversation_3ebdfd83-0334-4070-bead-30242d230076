package com.yunhesoft.leanCosting.baseConfig.service;

import java.util.List;

import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibParam;

public interface IDeviceTypelibParamService {
	/**
	 * 通过PID查询
	 * @return
	 */
	public List<DeviceTypelibParam> getItemData(DeviceTypelibDto dto);
	
	/**
	 * 保存数据
	 * 
	 * @param inserts
	 * @param updatas
	 * @param dels
	 * @return
	 */
	public String saveData(List<DeviceTypelibParam> addList, List<DeviceTypelibParam> updList,
			List<DeviceTypelibParam> delList);

	/**
	 * 复制到-核算对象-核算指标
	 * @param dto
	 * @return
	 */
	public String copyDeviceTypelib(DeviceTypelibDto dto);
}
