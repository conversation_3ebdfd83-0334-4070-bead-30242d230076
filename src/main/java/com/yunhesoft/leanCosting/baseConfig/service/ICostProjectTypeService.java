package com.yunhesoft.leanCosting.baseConfig.service;

import java.util.HashMap;
import java.util.List;

import com.yunhesoft.leanCosting.baseConfig.entity.po.ProjectType;

public interface ICostProjectTypeService {

	/**
	 * 删除数据
	 * 
	 * @param bean
	 * @return
	 */
	public String delete(List<ProjectType> bean);

	/**
	 * 获取数据
	 * 
	 * @return
	 */

	public List<ProjectType> getData();

	/**
	 * 根据id获取数据
	 * 
	 * @param id
	 * @return
	 */
	public ProjectType getBean(String id);

	/**
	 * 保存数据
	 * 
	 * @param bean
	 * @return
	 */
	public String save(List<ProjectType> bean);

	/**
	 * 更新数据
	 * 
	 * @param bean
	 * @return
	 */
	public int update(List<ProjectType> bean);

	/**
	 * @category 得到租户内运行状态的Map
	 * @return
	 */
	public HashMap<String, ProjectType> getProjectTypeM();
}
