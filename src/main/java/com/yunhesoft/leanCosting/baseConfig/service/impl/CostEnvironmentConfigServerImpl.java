package com.yunhesoft.leanCosting.baseConfig.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.CostEnvironmentConfig;
import com.yunhesoft.leanCosting.baseConfig.service.ICostEnvironmentConfigServer;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;


/**
 * 查询Tab页签数据
 * <AUTHOR>
 *
 */
@Service
public class CostEnvironmentConfigServerImpl implements ICostEnvironmentConfigServer {

	@Autowired
	private EntityService entityService;
	
	@Autowired
	private ICostToolService costToolService;
	
	@Override
	public CostEnvironmentConfig betData(String conFigName) {
		Where where = Where.create();
		if(!StringUtils.isEmpty(conFigName)) {
			where.eq(CostEnvironmentConfig::getConfigParam, conFigName);
		}
		Order order = Order.create();
		List<CostEnvironmentConfig> configs = entityService.queryList(CostEnvironmentConfig.class, where, order);
		if(configs.size()>0) {
			return configs.get(0);
		}
		return null;
	}

	@Override
	public String getConfig() {
		//初始化查询
		List<CostEnvironmentConfig> configs = costToolService.getCostEnvironmentConfigList("basicSetting", null);
		for (CostEnvironmentConfig costEnvironmentConfig : configs) {
			if("costObj_tab".equals(costEnvironmentConfig.getConfigParam())) {
				return costEnvironmentConfig.getParamName();
			}
		}
//		CostEnvironmentConfig bean = betData("costObj_tab");
		return null;
	}

}
