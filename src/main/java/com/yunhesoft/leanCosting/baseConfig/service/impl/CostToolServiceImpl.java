package com.yunhesoft.leanCosting.baseConfig.service.impl;

import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedCaseInsensitiveMap;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.CostEnvironmentConfig;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Devicetypelibrary;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryclassTreeVo;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryclassVo;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostlibraryitemVo;
import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostreportExcelVo;
import com.yunhesoft.leanCosting.baseConfig.service.ICostDeviceTypeService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostToolService;
import com.yunhesoft.leanCosting.baseConfig.service.ICostlibraryitemService;
import com.yunhesoft.leanCosting.baseConfig.service.IToolService;
import com.yunhesoft.leanCosting.calcLogic.PublicMethods;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBgcsszb;
import com.yunhesoft.leanCosting.order.entity.vo.ExcleCell;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostuintVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.TreeVo;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;

@Service
public class CostToolServiceImpl implements ICostToolService {

	@Autowired
	private ICostlibraryitemService costlibraryitemService;

	@Autowired
	private EntityService entityService;

	@Autowired
	private ICostDeviceTypeService costDeviceTypeService;

	@Autowired
	private IToolService toolService;

	@Autowired
	private ICostToolService costToolService;

	private PublicMethods pm = new PublicMethods();

	@Override
	public List<CostlibraryclassTreeVo> costlibraryclassTreeVo(List<CostlibraryclassTreeVo> list, Integer objTpye) {
		List<CostlibraryclassTreeVo> _list = new ArrayList<CostlibraryclassTreeVo>();
		// 查询
		for (CostlibraryclassTreeVo costlibraryclassVo : list) {
			if (costlibraryclassVo.getPid() == null || costlibraryclassVo.getPid().trim().length() == 0) {
				costlibraryclassVo.setPid("");
				_list.add(costlibraryclassVo);
			}
		}

		Map<String, List<CostlibraryclassTreeVo>> map = list.stream()
				.collect(Collectors.groupingBy(CostlibraryclassTreeVo::getPid));
		for (CostlibraryclassTreeVo costlibraryclassVo : _list) {
			String id = costlibraryclassVo.getId();
			if (map.containsKey(id)) {
				List<CostlibraryclassTreeVo> children = map.get(id);
				if (children != null) {
					List<CostlibraryclassTreeVo> treeVos = costlibraryclassVo.getChildren();
					if (treeVos != null && treeVos.size() > 0) {
						children.addAll(treeVos);
					}
					costlibraryclassVo.setChildren(children);
					getCostlibraryclassTreeVoData(children, map);
				}
			}
		}
		classTreeVo(_list, null);
		return _list;
	}

	private void classTreeVo(List<CostlibraryclassTreeVo> treeVos, String content) {
		for (CostlibraryclassTreeVo treeVo : treeVos) {
			List<CostlibraryclassTreeVo> list = treeVo.getChildren();
			String name = treeVo.getCcname();
			if (content == null) {
				content = "";
			}
			treeVo.setQueryContent(content + "&" + name);
			if (list != null) {
				classTreeVo(list, treeVo.getQueryContent());
			}
		}
	}

	/**
	 * 
	 * @param _list
	 * @param map
	 * @return
	 */
	private List<CostlibraryclassTreeVo> getCostlibraryclassTreeVoData(List<CostlibraryclassTreeVo> _list,
			Map<String, List<CostlibraryclassTreeVo>> map) {
		for (CostlibraryclassTreeVo costlibraryclassVo : _list) {
			String id = costlibraryclassVo.getId();
			if (map.containsKey(id)) {
				List<CostlibraryclassTreeVo> children = map.get(id);
				if (children != null) {
					List<CostlibraryclassTreeVo> treeVos = costlibraryclassVo.getChildren();
					if (treeVos != null && treeVos.size() > 0) {
						children.addAll(treeVos);
					}
					costlibraryclassVo.setChildren(children);
					getCostlibraryclassTreeVoData(children, map);
				}
			}
		}
		return _list;
	}

	@Override
	public List<CostlibraryclassTreeVo> costClassraryitemFyxmTreeVo(List<CostlibraryclassVo> _list) {
		List<CostlibraryclassTreeVo> list = new ArrayList<CostlibraryclassTreeVo>();
		List<CostlibraryclassTreeVo> newList = new ArrayList<CostlibraryclassTreeVo>();
		for (CostlibraryclassVo costlibraryclassVo : _list) {
			CostlibraryclassTreeVo treeVo = new CostlibraryclassTreeVo();
			ObjUtils.copyTo(costlibraryclassVo, treeVo);
			list.add(treeVo);
		}
		// 查询
		list = costlibraryclassTreeVo(list, 0);

		CostEnvironmentConfig config = toolService.getItemsync();
		String paramName = config.getParamName();
		for (CostlibraryclassTreeVo beanVo : list) {
			String Ccname = beanVo.getCcname();
			if (Ccname.equals(paramName)) {
				newList.add(beanVo);
			}
		}
		return newList;
	}

	@Override
	public List<CostlibraryclassTreeVo> costlibraryitemTreeVo(List<CostlibraryclassVo> _list) {
		List<CostlibraryitemVo> volist = costlibraryitemService.getData(null);
		// 查询全部记录
		List<CostlibraryclassTreeVo> costlibraryclassTreeVos = new ArrayList<CostlibraryclassTreeVo>();
		for (CostlibraryitemVo costlibraryitemVo : volist) {
			CostlibraryclassTreeVo treeVo = new CostlibraryclassTreeVo();
			ObjUtils.copyTo(costlibraryitemVo, treeVo);
			// 类型
			treeVo.setObjType(1);
			if (treeVo.getPid() == null) {
				treeVo.setPid("");
			}
			costlibraryclassTreeVos.add(treeVo);
		}
		Map<String, List<CostlibraryclassTreeVo>> map = costlibraryclassTreeVos.stream()
				.collect(Collectors.groupingBy(CostlibraryclassTreeVo::getPid));

		List<CostlibraryclassTreeVo> list = new ArrayList<CostlibraryclassTreeVo>();
		// 合并到分类中
		for (CostlibraryclassVo costlibraryclassVo : _list) {
			CostlibraryclassTreeVo treeVo = new CostlibraryclassTreeVo();
			ObjUtils.copyTo(costlibraryclassVo, treeVo);
			String id = treeVo.getId();
			if (map.containsKey(id)) {
				List<CostlibraryclassTreeVo> children = map.get(id);
				if (children != null) {
					treeVo.setChildren(children);
				}
			}
			treeVo.setObjType(0);
			list.add(treeVo);
		}

		classTreeVo(list, null);
		return list;
	}

	@Override
	public int maxSprt(Class<?> _class) {
		Integer sort = 0;
		try {
			// 类注解
			Annotation[] annotation = _class.getAnnotations();
			String tableName = _class.getName();
			for (int i = 0; i < annotation.length; i++) {
				if (annotation[i].toString().indexOf("@javax.persistence.Table") > -1) {
					String val = annotation[i].toString();
					val = val.substring(val.indexOf("@javax.persistence.Table") + 1, val.length() - 1).trim();
					String[] _vals = val.split(",");
					for (String _val : _vals) {
						if (_val.trim().indexOf("name") > -1) {
							tableName = _val.split("=")[1];
							break;
						}
					}
				}
			}
			String sql = " select max(TMsort) as sort from  " + tableName;
			SqlRowSet result = entityService.rawQuery(sql.toString());
			if (result != null) {
				while (result.next()) {
					// 2023-07-11不需要判断状态
					sort = result.getInt("sort");
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
		}
		return sort + 1;
	}

	@Override
	public List<TreeVo> queryContent(List<TreeVo> treeVos) {
		costToolService(treeVos, null, null);
		return treeVos;
	}

	/**
	 * 迭代输出
	 * 
	 * @param treeVos
	 * @param content
	 */
	private void costToolService(List<TreeVo> treeVos, String content, String queryTree) {
		for (TreeVo treeVo : treeVos) {
			List<TreeVo> list = treeVo.getChildren();
			String name = treeVo.getName();
			if (content == null) {
				treeVo.setQueryContent(name);
			} else {
				treeVo.setQueryContent(content + "." + name);
			}
			String id = treeVo.getId();
			if (queryTree == null) {
				treeVo.setQueryTree(id);
			} else {
				if ("hxl".equals(id) || "dh".equals(id) || "dwcb".equals(id) || "zcb".equals(id) || "khxhl".equals(id)
						|| "khzcb".equals(id)) {
					treeVo.setQueryTree(queryTree);
				} else {
					treeVo.setQueryTree(queryTree + "." + id);
				}
				treeVo.setId(queryTree);
			}
//			System.out.println(treeVo.getQueryTree());
			if (list != null) {
				costToolService(list, treeVo.getQueryContent(), treeVo.getQueryTree());
			}
		}
	}

	@Override
	public List<TreeVo> queryContentBm(List<TreeVo> treeVos) {
		costToolServiceBm(treeVos, null, null);
		return treeVos;
	}

	/**
	 * 迭代输出
	 * 
	 * @param treeVos
	 * @param content
	 */
	private void costToolServiceBm(List<TreeVo> treeVos, String content, String queryTree) {
		for (TreeVo treeVo : treeVos) {
			List<TreeVo> list = treeVo.getChildren();
			String name = treeVo.getName();
			if (content == null) {
				treeVo.setQueryContent(name);
			} else {
				treeVo.setQueryContent(content + "." + name);
			}
			String id = treeVo.getId();
			if (queryTree == null) {
				treeVo.setQueryTree(id);
			} else {
				treeVo.setQueryTree(queryTree + "." + id);
			}
			treeVo.setId(treeVo.getQueryTree());
			if (list != null) {
				costToolServiceBm(list, treeVo.getQueryContent(), treeVo.getQueryTree());
			}
		}
	}

	@Override
	public void CostitemVo(CostitemVo rootNode) {
		List<CostitemVo> costitemVos = rootNode.getChildren();
		costitemVo(costitemVos, null);
		rootNode.setQueryContent(rootNode.getNameStr());
		rootNode.setChildren(costitemVos);

	}

	private void costitemVo(List<CostitemVo> treeVos, String content) {
		if (treeVos != null) {
			for (CostitemVo treeVo : treeVos) {
				List<CostitemVo> list = treeVo.getChildren();
				String name = treeVo.getNameStr();
				if (content == null) {
					content = "";
				}
				treeVo.setQueryContent(content + "&" + name);
				if (list != null) {
					costitemVo(list, treeVo.getQueryContent());
				}
			}
		}
	}

	@Override
	public List<CostlibraryclassTreeVo> costlibraryTreeExpansion(List<CostlibraryclassTreeVo> costlibraryclassTreeVos,
			Integer treeExpansion) {
		return costitemVo(costlibraryclassTreeVos, 0, treeExpansion);
	}

	private List<CostlibraryclassTreeVo> costitemVo(List<CostlibraryclassTreeVo> treeVos, int content, int end) {
		List<CostlibraryclassTreeVo> costlibraryclassTreeVos = new ArrayList<CostlibraryclassTreeVo>();
		if (content == end) {// 序号相等时
			return costlibraryclassTreeVos;
		}
		for (CostlibraryclassTreeVo costitemVo : treeVos) {
			costlibraryclassTreeVos.add(costitemVo);
		}
		for (CostlibraryclassTreeVo costitemVo : treeVos) {
			List<CostlibraryclassTreeVo> vos = costitemVo.getChildren();
			if (vos != null && vos.size() > 0) {
				List<CostlibraryclassTreeVo> vos_ = costitemVo(vos, content + 1, end);
				costlibraryclassTreeVos.addAll(vos_);
			}
		}
		return costlibraryclassTreeVos;

	}

	@Override
	public List<CostuintVo> costuintTreeExpansion(List<CostuintVo> listVo, Integer treeExpansion) {
		// TODO Auto-generated method stub
		return costUnitVo(listVo, 0, treeExpansion);
	}

	private List<CostuintVo> costUnitVo(List<CostuintVo> treeVos, int content, int end) {
		List<CostuintVo> costuintVos = new ArrayList<CostuintVo>();
		if (content == end) {// 序号相等时
			return costuintVos;
		}
		for (CostuintVo costitemVo : treeVos) {
			costuintVos.add(costitemVo);
		}
		for (CostuintVo costitemVo : treeVos) {
			List<CostuintVo> vos = costitemVo.getChildren();
			if (vos != null && vos.size() > 0) {
				List<CostuintVo> vos_ = costUnitVo(vos, content + 1, end);
				costuintVos.addAll(vos_);
			}
		}
		return costuintVos;

	}

	/**
	 * 获取核算环境配置数据
	 * 
	 * @param pageConfig        页面设置
	 * @param configParam_query 配置参数
	 * @return
	 */
	@Override
	public List<CostEnvironmentConfig> getCostEnvironmentConfigList(String pageConfig, String configParam_query) {
		List<CostEnvironmentConfig> result = new ArrayList<CostEnvironmentConfig>();
		List<CostEnvironmentConfig> list = new ArrayList<CostEnvironmentConfig>();
		List<CostEnvironmentConfig> addList = new ArrayList<CostEnvironmentConfig>();
		List<CostEnvironmentConfig> delList = new ArrayList<CostEnvironmentConfig>();
		if (StringUtils.isNotEmpty(pageConfig)) {
			Map<String, CostEnvironmentConfig> map = new HashMap<String, CostEnvironmentConfig>();
			List<CostEnvironmentConfig> queryList = new ArrayList<CostEnvironmentConfig>();
			try {
				// 检索条件
				Where where = Where.create();
				where.eq(CostEnvironmentConfig::getPageConfig, pageConfig);
				// 排序
				Order order = Order.create();
				order.orderByAsc(CostEnvironmentConfig::getTmSort);
				List<CostEnvironmentConfig> dataList = entityService.queryData(CostEnvironmentConfig.class, where,
						order, null);
				if (StringUtils.isNotEmpty(dataList)) {
					queryList = dataList;
				}
			} catch (Exception e) {
				queryList = null;
			}
			if (queryList != null) {
				if (queryList.size() > 0) {
					if (pageConfig.startsWith("param_") || "R3DBConfig".equals(pageConfig)) { // 已存在的参数，不用校验比对，直接返回；
						result.addAll(queryList);
						return result;
					} else {
						for (int i = 0; i < queryList.size(); i++) {
							CostEnvironmentConfig queryObj = queryList.get(i);
							String configParam = queryObj.getConfigParam();
							if (!map.containsKey(configParam)) {
								map.put(configParam, queryObj);
								list.add(queryObj);
							}
						}
					}
				}
				// 默认
				List<CostEnvironmentConfig> initList = this.initCostEnvironmentConfigList(pageConfig);
				if (StringUtils.isNotEmpty(initList)) {
					for (int i = 0; i < initList.size(); i++) {
						CostEnvironmentConfig initObj = initList.get(i);
						String configParam = initObj.getConfigParam();
						if (StringUtils.isNotEmpty(map) && map.containsKey(configParam)) {
							map.remove(configParam);
						} else {
							initObj.setId(TMUID.getUID());
							addList.add(initObj);
							list.add(initObj);
						}
					}
					if (StringUtils.isNotEmpty(map)) {
						Iterator<Map.Entry<String, CostEnvironmentConfig>> entry = map.entrySet().iterator();
						while (entry.hasNext()) {
							Map.Entry<String, CostEnvironmentConfig> entryMap = entry.next();
							CostEnvironmentConfig delObj = entryMap.getValue();
							delList.add(delObj);
							list.remove(delObj);
						}
					}
				}

				String ret = "";
				if ("".equals(ret) && StringUtils.isNotEmpty(addList)) {
					if (entityService.insertBatch(addList) == 0) {
						ret = "添加失败（核算环境配置）！";
					}
				}
				if ("".equals(ret) && StringUtils.isNotEmpty(delList)) {
					if (entityService.deleteByIdBatch(delList) == 0) {
						ret = "删除失败（核算环境配置）！";
					}
				}
				if ("".equals(ret) && StringUtils.isNotEmpty(list)) {
					for (int i = 0; i < list.size(); i++) {
						CostEnvironmentConfig obj = list.get(i);
						int isVisible = obj.getIsVisible() == null ? 0 : obj.getIsVisible();
						if (isVisible == 1) { // 显示
							if (StringUtils.isNotEmpty(configParam_query)) {
								String configParam = obj.getConfigParam();
								if (!configParam_query.equals(configParam)) {
									continue;
								}
							}
							result.add(obj);
						}
					}
				}
			}
		}
		return result;
	}

	/**
	 * 获取核算环境配置初始化数据
	 * 
	 * @param pageConfig
	 * @return
	 */
	private List<CostEnvironmentConfig> initCostEnvironmentConfigList(String pageConfig) {
		List<CostEnvironmentConfig> result = new ArrayList<CostEnvironmentConfig>();
		if (StringUtils.isNotEmpty(pageConfig)) {
			if ("unitConfig".equals(pageConfig)) { // 核算对象设置
				CostEnvironmentConfig sampledotConf_tab = new CostEnvironmentConfig();
				sampledotConf_tab.setPageConfig(pageConfig);
				sampledotConf_tab.setConfigParam("sampledotConf_tab");
				sampledotConf_tab.setParamName("采集点管理");
				sampledotConf_tab.setIsVisible(1);
				sampledotConf_tab.setTmSort(1);
				result.add(sampledotConf_tab);

				CostEnvironmentConfig unitConf_tab = new CostEnvironmentConfig();
				unitConf_tab.setPageConfig(pageConfig);
				unitConf_tab.setConfigParam("unitConf_tab");
				unitConf_tab.setParamName("核算项目设置");
				unitConf_tab.setIsVisible(1);
				unitConf_tab.setTmSort(2);
				result.add(unitConf_tab);

				CostEnvironmentConfig costFormula_tab = new CostEnvironmentConfig();
				costFormula_tab.setPageConfig(pageConfig);
				costFormula_tab.setConfigParam("costFormula_tab");
				costFormula_tab.setParamName("公式设置");
				costFormula_tab.setIsVisible(1);
				costFormula_tab.setTmSort(3);
				result.add(costFormula_tab);

				CostEnvironmentConfig programConfigMain_tab = new CostEnvironmentConfig();
				programConfigMain_tab.setPageConfig(pageConfig);
				programConfigMain_tab.setConfigParam("programConfigMain_tab");
				programConfigMain_tab.setParamName("方案管理");
				programConfigMain_tab.setIsVisible(1);
				programConfigMain_tab.setTmSort(4);
				result.add(programConfigMain_tab);

				CostEnvironmentConfig briefAnalysisConf_tab = new CostEnvironmentConfig();
				briefAnalysisConf_tab.setPageConfig(pageConfig);
				briefAnalysisConf_tab.setConfigParam("briefAnalysisConf_tab");
				briefAnalysisConf_tab.setParamName("简要分析设置");
				briefAnalysisConf_tab.setIsVisible(1);
				briefAnalysisConf_tab.setTmSort(5);
				result.add(briefAnalysisConf_tab);

				CostEnvironmentConfig keyDeviceConf_tab = new CostEnvironmentConfig();
				keyDeviceConf_tab.setPageConfig(pageConfig);
				keyDeviceConf_tab.setConfigParam("keyDeviceConf_tab");
				keyDeviceConf_tab.setParamName("关键设备设置");
				keyDeviceConf_tab.setIsVisible(1);
				keyDeviceConf_tab.setTmSort(6);
				result.add(keyDeviceConf_tab);
			} else if ("programConfig".equals(pageConfig)) { // 方案管理
				CostEnvironmentConfig identifyProgramTab = new CostEnvironmentConfig();
				identifyProgramTab.setPageConfig(pageConfig);
				identifyProgramTab.setConfigParam("identifyProgramTab");
				identifyProgramTab.setParamName("识别方案");
				identifyProgramTab.setIsVisible(1);
				identifyProgramTab.setTmSort(1);
				result.add(identifyProgramTab);

				CostEnvironmentConfig programCostingTab = new CostEnvironmentConfig();
				programCostingTab.setPageConfig(pageConfig);
				programCostingTab.setConfigParam("programCostingTab");
				programCostingTab.setParamName("核算方案");
				programCostingTab.setIsVisible(1);
				programCostingTab.setTmSort(2);
				result.add(programCostingTab);

				CostEnvironmentConfig indexProgramTab = new CostEnvironmentConfig();
				indexProgramTab.setPageConfig(pageConfig);
				indexProgramTab.setConfigParam("indexProgramTab");
				indexProgramTab.setParamName("指标方案");
				indexProgramTab.setIsVisible(1);
				indexProgramTab.setTmSort(3);
				result.add(indexProgramTab);

				CostEnvironmentConfig craftCardTab = new CostEnvironmentConfig();
				craftCardTab.setPageConfig(pageConfig);
				craftCardTab.setConfigParam("craftCardTab");
				craftCardTab.setParamName("工艺卡");
				craftCardTab.setIsVisible(1);
				craftCardTab.setTmSort(4);
				result.add(craftCardTab);

				CostEnvironmentConfig leanLedgerTab = new CostEnvironmentConfig();
				leanLedgerTab.setPageConfig(pageConfig);
				leanLedgerTab.setConfigParam("leanLedgerTab");
				leanLedgerTab.setParamName("精益台账");
				leanLedgerTab.setIsVisible(1);
				leanLedgerTab.setTmSort(5);
				result.add(leanLedgerTab);
			} else if ("basicSetting".equals(pageConfig)) { // 基础设置
				CostEnvironmentConfig deviceTypeTab = new CostEnvironmentConfig();
				deviceTypeTab.setPageConfig(pageConfig);
				deviceTypeTab.setConfigParam("deviceType_tab");
				deviceTypeTab.setParamName("单元类型");
				deviceTypeTab.setIsVisible(1);
				deviceTypeTab.setTmSort(1);
				deviceTypeTab.setTreeExpansion(1);
				result.add(deviceTypeTab);

				CostEnvironmentConfig costObjTab = new CostEnvironmentConfig();
				costObjTab.setPageConfig(pageConfig);
				costObjTab.setConfigParam("costObj_tab");
				costObjTab.setParamName("核算对象");
				costObjTab.setIsVisible(1);
				costObjTab.setTmSort(2);
				costObjTab.setTreeExpansion(1);
				result.add(costObjTab);

				CostEnvironmentConfig projectTypeTab = new CostEnvironmentConfig();
				projectTypeTab.setPageConfig(pageConfig);
				projectTypeTab.setConfigParam("projectType_tab");
				projectTypeTab.setParamName("运行状态");
				projectTypeTab.setIsVisible(1);
				projectTypeTab.setTmSort(3);
				result.add(projectTypeTab);

				CostEnvironmentConfig costlibraryitemTab = new CostEnvironmentConfig();
				costlibraryitemTab.setPageConfig(pageConfig);
				costlibraryitemTab.setConfigParam("costlibraryitem_tab");
				costlibraryitemTab.setParamName("项目库");
				costlibraryitemTab.setIsVisible(1);
				costlibraryitemTab.setTmSort(4);
				costlibraryitemTab.setTreeExpansion(1);
				result.add(costlibraryitemTab);
			} else if ("param_briefAnalysis_planFormula".equals(pageConfig)) { // （参数）简要分析计划量
				CostEnvironmentConfig param1 = new CostEnvironmentConfig();
				param1.setPageConfig(pageConfig);
				param1.setConfigParam("[每月计划量]");
				param1.setParamName("每月计划量");
				param1.setIsVisible(1);
				param1.setTmSort(1);
				result.add(param1);

				CostEnvironmentConfig param2 = new CostEnvironmentConfig();
				param2.setPageConfig(pageConfig);
				param2.setConfigParam("[本月天数]");
				param2.setParamName("本月天数");
				param2.setIsVisible(1);
				param2.setTmSort(2);
				result.add(param2);

				CostEnvironmentConfig param3 = new CostEnvironmentConfig();
				param3.setPageConfig(pageConfig);
				param3.setConfigParam("[当班班组工作时间]");
				param3.setParamName("当班班组工作时间");
				param3.setIsVisible(1);
				param3.setTmSort(3);
				result.add(param3);

			} else if ("param_briefAnalysis_showFormula".equals(pageConfig)) { // （参数）简要分析显示条件
				CostEnvironmentConfig param1 = new CostEnvironmentConfig();
				param1.setPageConfig(pageConfig);
				param1.setConfigParam("{实际值}");
				param1.setParamName("实际值");
				param1.setIsVisible(1);
				param1.setTmSort(1);
				result.add(param1);

				CostEnvironmentConfig param2 = new CostEnvironmentConfig();
				param2.setPageConfig(pageConfig);
				param2.setConfigParam("{比较值}");
				param2.setParamName("比较值");
				param2.setIsVisible(1);
				param2.setTmSort(2);
				result.add(param2);

				CostEnvironmentConfig param3 = new CostEnvironmentConfig();
				param3.setPageConfig(pageConfig);
				param3.setConfigParam("{考核值}");
				param3.setParamName("考核值");
				param3.setIsVisible(1);
				param3.setTmSort(3);
				result.add(param3);

			} else if ("priceManage".equals(pageConfig)) { // 价格管理
				CostEnvironmentConfig priceConfigTab = new CostEnvironmentConfig();
				priceConfigTab.setPageConfig(pageConfig);
				priceConfigTab.setConfigParam("priceConfigTab");
				priceConfigTab.setParamName("价格维护");
				priceConfigTab.setIsVisible(1);
				priceConfigTab.setTmSort(1);
				result.add(priceConfigTab);

				CostEnvironmentConfig costConfigTab = new CostEnvironmentConfig();
				costConfigTab.setPageConfig(pageConfig);
				costConfigTab.setConfigParam("costConfigTab");
				costConfigTab.setParamName("费用维护");
				costConfigTab.setIsVisible(1);
				costConfigTab.setTmSort(2);
				result.add(costConfigTab);

				CostEnvironmentConfig processPriceTab = new CostEnvironmentConfig();
				processPriceTab.setPageConfig(pageConfig);
				processPriceTab.setConfigParam("processPriceTab");
				processPriceTab.setParamName("单位加工费");
				processPriceTab.setIsVisible(1);
				processPriceTab.setTmSort(3);
				result.add(processPriceTab);

				CostEnvironmentConfig priceRatioTab = new CostEnvironmentConfig();
				priceRatioTab.setPageConfig(pageConfig);
				priceRatioTab.setConfigParam("priceRatioTab");
				priceRatioTab.setParamName("单价系数");
				priceRatioTab.setIsVisible(1);
				priceRatioTab.setTmSort(4);
				result.add(priceRatioTab);

				CostEnvironmentConfig priceFormulaTab = new CostEnvironmentConfig();
				priceFormulaTab.setPageConfig(pageConfig);
				priceFormulaTab.setConfigParam("priceFormulaTab");
				priceFormulaTab.setParamName("价格公式");
				priceFormulaTab.setIsVisible(1);
				priceFormulaTab.setTmSort(5);
				result.add(priceFormulaTab);
			} else if ("R3DBConfig".equals(pageConfig)) { // R3DB配置
				CostEnvironmentConfig rdbid = new CostEnvironmentConfig();
				rdbid.setPageConfig(pageConfig);
				rdbid.setConfigParam("rdbid");
				rdbid.setParamName("21");
				rdbid.setIsVisible(1);
				rdbid.setTmSort(1);
				result.add(rdbid);

				CostEnvironmentConfig clientSn = new CostEnvironmentConfig();
				clientSn.setPageConfig(pageConfig);
				clientSn.setConfigParam("clientSn");
				clientSn.setParamName("200");
				clientSn.setIsVisible(1);
				clientSn.setTmSort(2);
				result.add(clientSn);

				CostEnvironmentConfig dataType = new CostEnvironmentConfig();
				dataType.setPageConfig(pageConfig);
				dataType.setConfigParam("dataType");
				dataType.setParamName("double");
				dataType.setIsVisible(1);
				dataType.setTmSort(3);
				result.add(dataType);

				CostEnvironmentConfig tagType = new CostEnvironmentConfig();
				tagType.setPageConfig(pageConfig);
				tagType.setConfigParam("tagType");
				tagType.setParamName("0");
				tagType.setIsVisible(1);
				tagType.setTmSort(4);
				result.add(tagType);
			} else if ("LIMS".equals(pageConfig)) { // LIMS配置
				CostEnvironmentConfig rdbid = new CostEnvironmentConfig();
				rdbid.setPageConfig(pageConfig);
				rdbid.setConfigParam("DaysBetweenSampleAndReleased");
				rdbid.setParamName("5");
				rdbid.setIsVisible(1);
				rdbid.setTmSort(1);
				result.add(rdbid);
			} else if ("param_priceManage".equals(pageConfig)) { // 价格管理配置参数
				CostEnvironmentConfig erpImport = new CostEnvironmentConfig();
				erpImport.setPageConfig(pageConfig);
				erpImport.setConfigParam("isUseErpCodeImport"); // 是否使用ERP编码导入项目
				erpImport.setParamName("0");
				erpImport.setIsVisible(1);
				erpImport.setTmSort(1);
				result.add(erpImport);
			}
		}
		return result;
	}

	/**
	 * 周报录入使用 END
	 */
	@Override
	public List<CostuintVo> getCopy(List<Costuint> _list) {
		Map<String, String> map = new LinkedCaseInsensitiveMap<String>();
		StringBuffer sql = new StringBuffer();
		String dbtype = entityService.getDatabaseType();
		if ("mysql".equalsIgnoreCase(dbtype)) {
			sql.append(
					" SELECT MAX(BEGINTIME) AS BEGINTIME,UNITID FROM COSTUNITVERSION WHERE IFNULL(UNITID,'') !='' GROUP BY UNITID");
		} else if ("oracle".equalsIgnoreCase(dbtype)) {
			sql.append(
					" SELECT MAX(BEGINTIME) AS BEGINTIME,UNITID FROM COSTUNITVERSION WHERE NVL(UNITID,'') !='' GROUP BY UNITID");
		} else {
			sql.append(
					" SELECT MAX(BEGINTIME) AS BEGINTIME,UNITID FROM COSTUNITVERSION WHERE (UNITID!='' or UNITID is not null) GROUP BY UNITID");
		}
		SqlRowSet result = entityService.rawQuery(sql.toString());
		if (result != null) {
			while (result.next()) {
				// 核算对象，最新的版本
				String begintime = result.getString("BEGINTIME");
				String unitid = result.getString("UNITID");
				map.put(unitid, begintime);
			}
		}

		// 单元类型
		List<Devicetypelibrary> typeList = costDeviceTypeService.getList(null);
		Map<String, Devicetypelibrary> deviceTypeMap = typeList.stream()
				.collect(Collectors.toMap(Devicetypelibrary::getId, Function.identity()));

		Map<String, String> orgIdMap = new LinkedHashMap<String, String>();
		List<CostuintVo> costuintVos = new ArrayList<CostuintVo>();
		for (Costuint costuint : _list) {
			CostuintVo treeVo = new CostuintVo();
			ObjUtils.copyTo(costuint, treeVo);
			if (map.containsKey(treeVo.getId())) {// 添加最新版本
				String begintime = map.get(treeVo.getId());
				treeVo.setBegintime(begintime);
			}
			// 机构id
			String orgId = treeVo.getOrgId();
			// 机构id
			if (orgId != null) {
				String orgName = "";
				if (orgIdMap.containsKey(orgId)) {
					orgName = orgIdMap.get(orgId);
				} else {
					SysOrg org = entityService.queryObjectById(SysOrg.class, orgId);
					if (org != null) {
						orgName = org.getOrgname();
					}
					orgIdMap.put(orgId, orgName);
				}
				treeVo.setOrgName(orgName);
			}
			if (deviceTypeMap.containsKey(treeVo.getUnittype())) {
				Devicetypelibrary costDeviceTypeVo = deviceTypeMap.get(treeVo.getUnittype());
				treeVo.setUnittypeName(costDeviceTypeVo.getDtname());
			}
//			if (treeVo.getPid() == null) {
//				treeVo.setPid("");
//				// 根节点
//				_costuintVos.add(treeVo);
//			}else if ("".equals(treeVo.getPid())) {
//				_costuintVos.add(treeVo);
//			}
			// 其他节点
			costuintVos.add(treeVo);
		}
		return costuintVos;
	}

	@Override
	public List<CostuintVo> treeProductive(List<Costuint> list, List<CostuintVo> costuintVos, Integer _productive,
			boolean IsmobileInput) {
		List<CostuintVo> _list = ObjUtils.copyToList(list, CostuintVo.class);
		List<String> yeziList = new ArrayList<String>();
		for (CostuintVo costuintVo : _list) {
			String id = costuintVo.getId();
			Integer productive = costuintVo.getProductive();
			if (productive == null)
				productive = 0;
			Integer mobileInput = costuintVo.getMobileInput();
			if (mobileInput == null)
				mobileInput = 0;

			if (productive == _productive) {
				if (IsmobileInput) {// 切叶子节点
					if (mobileInput == 1) {
						yeziList.add(id);
					}
				} else {
					yeziList.add(id);
				}
			}
		}

		for (int i = 0; i < costuintVos.size(); i++) {
			CostuintVo costuintVo = costuintVos.get(i);
			String id = costuintVo.getId();
			List<CostuintVo> _costuintVos = costuintVo.getChildren();
			if (_costuintVos != null && _costuintVos.size() > 0) {
				getCostTreeVoData(yeziList, _costuintVos);
			} else {
				if (!yeziList.contains(id)) {// 去掉叶子节点
					costuintVos.remove(costuintVo);
					i--;
				}
			}
		}

		return costuintVos;

	}

	private List<CostuintVo> getCostTreeVoData(List<String> yeziList, List<CostuintVo> costuintVos) {
		for (int i = 0; i < costuintVos.size(); i++) {
			CostuintVo costuintVo = costuintVos.get(i);
			String id = costuintVo.getId();
			List<CostuintVo> _costuintVos = costuintVo.getChildren();
			if (_costuintVos != null && _costuintVos.size() > 0) {
				getCostTreeVoData(yeziList, _costuintVos);
				if (_costuintVos.size() == 0) {
					costuintVos.remove(costuintVo);
					i--;
				} else {
					costuintVo.setProductive(-1);
				}
			} else {
				if (!yeziList.contains(id)) {
					costuintVos.remove(costuintVo);
					i--;
				}
			}
		}
		return costuintVos;
	}

	@Override
	public List<CostuintVo> FilterateProductive(List<Costuint> list, List<CostuintVo> costuintVos,
			Integer _productive) {
		List<CostuintVo> _list = ObjUtils.copyToList(list, CostuintVo.class);
		List<String> _yeziList = new ArrayList<String>();
		List<String> yeziList = new ArrayList<String>();
		//存Pid
		List<String> pidList = new ArrayList<String>();
		for (CostuintVo costuintVo : _list) {
			String id = costuintVo.getId();
			String pid = costuintVo.getPid();
			Integer productive = costuintVo.getProductive();
			if(productive != null) {
				if (productive == 1) {
					yeziList.add(id);
					if(!pidList.contains(pid)) {
						pidList.add(pid);
					}
				}
				if (productive == 1) {
					if(!_yeziList.contains(id)) {
						_yeziList.add(id);
					}
				}
			}
		}

		for (int i = 0; i < costuintVos.size(); i++) {
			CostuintVo costuintVo = costuintVos.get(i);
			String id = costuintVo.getId();
			if(pidList.contains(id)) {
				costuintVo.setProductive(2);
			}
			List<CostuintVo> _costuintVos = costuintVo.getChildren();
			if (_costuintVos != null && _costuintVos.size() > 0) {
				getLevel(yeziList,_costuintVos);
			} else {
				if (!yeziList.contains(id)) {// 去掉叶子节点
					costuintVos.remove(costuintVo);
					i--;
				}
			}
		}
		for (int i = 0; i < costuintVos.size(); i++) {
			CostuintVo costuintVo = costuintVos.get(i);
			List<CostuintVo> _costuintVos = costuintVo.getChildren();
			if (_costuintVos != null && _costuintVos.size() > 0) {
				getLevel_(yeziList,_costuintVos,pidList);
			}
		}
		
		return costuintVos;
	}
	
	
	private List<CostuintVo> getLevel_(List<String> yeziList, List<CostuintVo> costuintVos,List<String> pidList) {
		for (int i = 0; i < costuintVos.size(); i++) {
			CostuintVo costuintVo = costuintVos.get(i);
			String id = costuintVo.getId();
			if(pidList.contains(id)) {
				costuintVo.setProductive(2);
			}
			List<CostuintVo> _costuintVos = costuintVo.getChildren();
			if (yeziList.contains(id)) {
				costuintVos.remove(costuintVo);
				i--;
			}
			if (_costuintVos != null && _costuintVos.size() > 0) {
				getLevel_(yeziList, _costuintVos,pidList);
			}
		}
		return costuintVos;
	}

	private List<CostuintVo> getLevel(List<String> yeziList, List<CostuintVo> costuintVos) {
		for (int i = 0; i < costuintVos.size(); i++) {
			CostuintVo costuintVo = costuintVos.get(i);
			String id = costuintVo.getId();
			List<CostuintVo> _costuintVos = costuintVo.getChildren();
			if (_costuintVos != null && _costuintVos.size() > 0) {
				getLevel(yeziList, _costuintVos);
				if (_costuintVos.size() == 0) {
					costuintVos.remove(costuintVo);
					i--;
				} else {
					costuintVo.setProductive(-1);
				}
			} else {
				if (!yeziList.contains(id)) {
					costuintVos.remove(costuintVo);
					i--;
				}
			}
		}
		return costuintVos;
	}

	/**
	 * 整理类型
	 * 
	 * @param _list
	 * @return
	 */
	@Override
	public List<CostuintVo> tree(List<Costuint> _list, boolean b) {
		List<CostuintVo> costuintVos = getCopy(_list);
		List<CostuintVo> _costuintVos = new ArrayList<CostuintVo>();
		for (CostuintVo treeVo : costuintVos) {
			// 机构id
			if (treeVo.getPid() == null) {
				treeVo.setPid("");
				// 根节点
				treeVo.setProductive(-1);
				_costuintVos.add(treeVo);
			} else if ("".equals(treeVo.getPid())) {
				treeVo.setProductive(-1);
				_costuintVos.add(treeVo);
			}
		}
		Map<String, List<CostuintVo>> map = costuintVos.stream().collect(Collectors.groupingBy(CostuintVo::getPid));
		Date date = new Date();
		// 合并到分类中
		for (CostuintVo costlibraryclassVo : _costuintVos) {
			String id = costlibraryclassVo.getId();
			costlibraryclassVo.setLevel(0);
			if (map.containsKey(id)) {
				List<CostuintVo> children = map.get(id);
				if (children != null) {
					boolean key = false;// 有内容
					for (CostuintVo costuintVo : children) {
						Integer type = costuintVo.getType();
						if (type == null)
							type = 0;
						if (type == 1) {
							key = true;
							break;
						}
					}
					if (key) {// 有内容
						costlibraryclassVo.setContent(1);// 有内容
						costlibraryclassVo.setNrBtn(1);// 显示内容按钮
						costlibraryclassVo.setFlBtn(0);
					}
					if (b || !key) {
						costlibraryclassVo.setChildren(children);
					}
					getCostlibraryclassTreeVoData(children, map, b, 1);
				}
			} else {
				costlibraryclassVo.setFlBtn(1);// 显示分类按钮
				costlibraryclassVo.setNrBtn(1);// 显示内容按钮
			}
		}
		classTreeVo1(_costuintVos, null);
		Date date1 = new Date();
		System.out.println(date1.getTime() - date.getTime());
		return _costuintVos;
	}

	private List<CostuintVo> getCostlibraryclassTreeVoData(List<CostuintVo> _list, Map<String, List<CostuintVo>> map,
			boolean b, int level) {
		for (CostuintVo costlibraryclassVo : _list) {
			String id = costlibraryclassVo.getId();
			costlibraryclassVo.setLevel(level);
			if (map.containsKey(id)) {
				List<CostuintVo> children = map.get(id);
				if (children != null) {
					List<CostuintVo> treeVos = costlibraryclassVo.getChildren();
					if (treeVos != null && treeVos.size() > 0) {
						children.addAll(treeVos);
					}
					boolean key = false;// 有内容
					for (CostuintVo costuintVo : children) {
						Integer type = costuintVo.getType();
						if (type == null)
							type = 0;
						if (type == 1) {
							key = true;
							break;
						}
					}
					if (key) {// 有内容
						costlibraryclassVo.setContent(1);// 有内容
						costlibraryclassVo.setNrBtn(1);// 显示内容按钮
						costlibraryclassVo.setFlBtn(0);
					}
					if (b || !key) {
						costlibraryclassVo.setChildren(children);
					}
					getCostlibraryclassTreeVoData(children, map, b, level + 1);
				}
			}
		}
		return _list;

	}

	/**
	 * 树形整理
	 * 
	 * @param treeVos
	 * @param content
	 */
	private void classTreeVo1(List<CostuintVo> treeVos, String content) {
		for (CostuintVo treeVo : treeVos) {
			List<CostuintVo> list = treeVo.getChildren();
			String name = treeVo.getName();
			if (content == null) {
				content = "";
			}
			treeVo.setQueryContent(content + "&" + name);
			if (list != null) {
				list.sort(Comparator.comparing(CostuintVo::getTmSort));
				classTreeVo1(list, treeVo.getQueryContent());
				if (treeVo.getContent() == 0) {
					treeVo.setFlBtn(1);// 显示分类按钮
				}
			} else {
				if (treeVo.getContent() == 0) {
					treeVo.setFlBtn(1);// 显示分类按钮
					treeVo.setNrBtn(1);// 显示内容按钮
				}
			}
		}
	}

	@Override
	public HSSFWorkbook newWorkbook(CostreportExcelVo costreportExcelVo) {
		// 创建工作薄对象
		HSSFWorkbook workbook = new HSSFWorkbook();
		Map<String, CostBgcsszb> BgcsszbMap = costreportExcelVo.getBgcsszbMap();
		Map<String, JSONArray> datas = costreportExcelVo.getDatas();

		Map<String, String> remarks = costreportExcelVo.getRemarks();
		String titleName = costreportExcelVo.getTitleName();
		Map<String, String> ftitleNames = costreportExcelVo.getFtitleName();
		JSONArray column = costreportExcelVo.getColumn();
		int c1 = 0, c2 = 0, c3 = 0;
		String t1, t2, t3, tt1, tt2, tt3, tx;
		List<Map<String, CostBgcsszb>> list = new ArrayList<Map<String, CostBgcsszb>>();
		if (column != null) {
			c1 = column.size();
			if (c1 > 0) {
				Map<String, CostBgcsszb> newBgcsszbMap1 = new LinkedHashMap<String, CostBgcsszb>();
				Map<String, CostBgcsszb> newBgcsszbMap2 = new LinkedHashMap<String, CostBgcsszb>();
				Map<String, CostBgcsszb> newBgcsszbMap3 = new LinkedHashMap<String, CostBgcsszb>();
				// 第一层
				for (int i = 0; c1 > i; i++) {
					JSONObject row11 = column.getJSONObject(i);
					JSONObject ifrow = new JSONObject();
					ObjUtils.copyTo(row11, ifrow);
					tt1 = ifrow.getString("Header");
					t1 = ifrow.getString("Alias");
					int rowspan = 3;
					int colspan = 1;
					// 第二层
					if (ifrow.containsKey("child")) {
						JSONArray ifrowarray = ifrow.getJSONArray("child");
						c2 = ifrowarray.size();
						colspan = c2;
						rowspan = rowspan - 1;
						for (int i1 = 0; i1 < c2; i1++) {
							JSONObject row111 = ifrowarray.getJSONObject(i1);
							JSONObject ifrow1 = new JSONObject();
							ObjUtils.copyTo(row111, ifrow1);
							int rowspan1 = 2;
							int colspan1 = 1;
							tt2 = ifrow1.getString("Header");
							t2 = ifrow1.getString("Alias");
							// 第三层
							if (ifrow1.containsKey("child")) {
								JSONArray ifrowarray1 = ifrow1.getJSONArray("child");
								c3 = ifrowarray1.size();
								colspan = colspan + c3;
								colspan1 = c3;
								rowspan = rowspan - 1;
								rowspan1 = rowspan1 - 1;
								for (int i11 = 0; i11 < c3; i11++) {
									JSONObject row1111 = ifrowarray1.getJSONObject(i11);
									JSONObject ifrow11 = new JSONObject();
									ObjUtils.copyTo(row1111, ifrow11);
									tt3 = ifrow11.getString("Header");
									t3 = ifrow11.getString("Alias");
									tx = t3.replaceFirst(t2, "");
									Integer dq3 = 2, xs3 = 4;
									if (BgcsszbMap.containsKey(tx)) {
										CostBgcsszb bg = BgcsszbMap.get(tx);
										dq3 = bg.getObjalg();
										if (dq3 == null) {
											dq3 = 2;
										}
										xs3 = bg.getObjdis();
										if (xs3 == null) {
											xs3 = 4;
										}
									}
									CostBgcsszb bgcsszb111 = new CostBgcsszb();
									bgcsszb111.setRowspan(1);
									bgcsszb111.setColspan(1);
									bgcsszb111.setLeftColspan(3);
									bgcsszb111.setColumnshowName(tt3);
									bgcsszb111.setObjalg(dq3);
									bgcsszb111.setObjdis(xs3);
									bgcsszb111.setColumnCode(tx);
									newBgcsszbMap3.put(t3, bgcsszb111);
									if (ifrow11.containsKey("child")) {
										// 目前只有三层
										continue;
									}
								}
							}
							Integer dq2 = 2, xs2 = 4;
							tx = t2;
							if (BgcsszbMap.containsKey(t2)) {
								CostBgcsszb bg = BgcsszbMap.get(t2);
								dq2 = bg.getObjalg();
								if (dq2 == null) {
									dq2 = 2;
								}
								xs2 = bg.getObjdis();
								if (xs2 == null) {
									xs2 = 4;
								}
							} else {
								if ("dhslbj".equals(t2)) {
									tx = "dhcz";
									if (BgcsszbMap.containsKey("dhcz")) {
										CostBgcsszb bg = BgcsszbMap.get("dhcz");
										dq2 = bg.getObjalg();
										if (dq2 == null) {
											dq2 = 2;
										}
										xs2 = bg.getObjdis();
										if (xs2 == null) {
											xs2 = 4;
										}
									}
								} else {
									tx = t2.replaceFirst(t1, "");
									if (BgcsszbMap.containsKey(tx)) {
										CostBgcsszb bg = BgcsszbMap.get(tx);
										dq2 = bg.getObjalg();
										if (dq2 == null) {
											dq2 = 2;
										}
										xs2 = bg.getObjdis();
										if (xs2 == null) {
											xs2 = 4;
										}
									}
								}
							}
							CostBgcsszb bgcsszb11 = new CostBgcsszb();
							bgcsszb11.setRowspan(rowspan1);
							bgcsszb11.setColspan(colspan1);
							bgcsszb11.setLeftColspan(1);
							bgcsszb11.setColumnshowName(tt2);
							bgcsszb11.setObjalg(dq2);
							bgcsszb11.setObjdis(xs2);
							bgcsszb11.setColumnCode(tx);
							newBgcsszbMap2.put(t2, bgcsszb11);
						}
					}
					Integer dq1 = 2, xs1 = 4;
					if (BgcsszbMap.containsKey(t1)) {
						CostBgcsszb bg = BgcsszbMap.get(t1);
						dq1 = bg.getObjalg();
						if (dq1 == null) {
							dq1 = 2;
						}
						xs1 = bg.getObjdis();
						if (xs1 == null) {
							xs1 = 4;
						}
					}
					CostBgcsszb bgcsszb = new CostBgcsszb();
					bgcsszb.setRowspan(rowspan);
					if (!"itemName".equals(t1)) {
						bgcsszb.setRowspan(1);
					}
					bgcsszb.setColspan(colspan);
					if ("bysj".equals(t1)) {
						bgcsszb.setColspan(colspan - c2);
					}
					bgcsszb.setLeftColspan(0);
					bgcsszb.setColumnshowName(tt1);
					bgcsszb.setObjalg(dq1);
					bgcsszb.setObjdis(xs1);
					bgcsszb.setColumnCode(t1);
					newBgcsszbMap1.put(t1, bgcsszb);
				}
				list.add(newBgcsszbMap1);
				list.add(newBgcsszbMap2);
				list.add(newBgcsszbMap3);

				Map<String, List<String>> map2_3 = new LinkedHashMap<String, List<String>>();
				for (String key2 : newBgcsszbMap2.keySet()) {
					for (String key3 : newBgcsszbMap3.keySet()) {
						if (key3.indexOf(key2) > -1) {
							List<String> key2s = new ArrayList<String>();
							if (map2_3.containsKey(key2)) {
								key2s = map2_3.get(key2);
							}
							key2s.add(key3);
							map2_3.put(key2, key2s);
						}
					}
				}

				Map<String, CostBgcsszb> newBgcsszbMap_ = new LinkedHashMap<String, CostBgcsszb>();
				// 第一层
				newBgcsszbMap_.put("itemName", newBgcsszbMap1.get("itemName"));// 项目名称 固定
				// 第二层
				for (Entry<String, CostBgcsszb> entry : newBgcsszbMap2.entrySet()) {
					String key2 = entry.getKey();
					CostBgcsszb value2 = entry.getValue();
					if (map2_3.containsKey(key2)) {
						List<String> key3s = map2_3.get(key2);
						for (String key3 : key3s) {
							CostBgcsszb bgcsszb = newBgcsszbMap3.get(key3);
							newBgcsszbMap_.put(key3, bgcsszb);
						}
					} else {
						newBgcsszbMap_.put(key2, value2);
					}

				}
				BgcsszbMap = newBgcsszbMap_;
			}
		}

		int i1 = 0;
		if (datas.size() > 0) {
			for (Entry<String, JSONArray> entry : datas.entrySet()) {
				String sheetName = entry.getKey();
				JSONArray data = entry.getValue();
				// 获得副标题
				String ftitleName = ftitleNames.get(sheetName);
				// 获得简要分析
				String remark = remarks.get(sheetName);
				/**
				 * ----------------------------------
				 */
				// 创建表格样式
				HSSFCellStyle style = workbook.createCellStyle();
				HSSFFont font = workbook.createFont();
				// 准备订单表头
				List<ExcleCell> titleCellOrder = new ArrayList<>();
				this.buildStandbyTitle(titleCellOrder, workbook, style, font, BgcsszbMap);
				// 准备产品表头
				HSSFCellStyle style1 = workbook.createCellStyle();
				HSSFFont font1 = workbook.createFont();
				List<ExcleCell> titleCellPro = new ArrayList<>();

				// 数据Map删选
				this.buildStandbyProductTitle(titleCellPro, workbook, style1, font1, BgcsszbMap);
				Map<String, ExcleCell> titleProMap = titleCellPro.stream()
						.collect(Collectors.toMap(ExcleCell::getProperty, a -> a, (k1, k2) -> k1));
				// 开始构建excle
				// 创建sheet
				sheetName = costToolService.sheetName(sheetName);
				HSSFSheet sheet = workbook.getSheet(sheetName);
				if (sheet == null) {
					sheet = workbook.createSheet(sheetName);
				}
				// 单元格高度宽度
				this.columnWidth(titleCellOrder.size(), sheet);
				// 设置sheet内容
				// 创建行
				int rowIndex = 0;
				// 设置
				HSSFRow row = sheet.createRow(rowIndex);
				HSSFCell cell = row.createCell(0);
				// 标题
				cell.setCellValue(titleName);
				cell.setCellStyle(style);
				for (int i = 1; i < titleCellOrder.size(); i++) {
					HSSFCell cell1 = row.createCell(i);
					cell1.setCellValue("");
					cell1.setCellStyle(style);
				}
				if (ftitleName != null) {
					rowIndex++;
					sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titleCellOrder.size() - 1));
					HSSFRow row1 = sheet.createRow(rowIndex);
					HSSFCell cell1 = row1.createCell(0);
					// 副标题
					cell1.setCellValue(ftitleName);
					cell1.setCellStyle(style);
					for (int i = 1; i < titleCellOrder.size(); i++) {
						HSSFCell cell11 = row.createCell(i);
						cell11.setCellValue("");
						cell11.setCellStyle(style);
					}
				}
				sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, titleCellOrder.size() - 1));

				// 合并单元格
				if (list.size() > 0) {
					int xhnumber = rowIndex;
					List<String> keys = new ArrayList<String>();
					for (Map<String, CostBgcsszb> map : list) {
						int count3 = -1;
						String key3 = null;
						for (Entry<String, CostBgcsszb> entry1 : map.entrySet()) {
							key3 = entry1.getKey();
							break;
						}
						for (int i = 0; i < keys.size(); i++) {
							String key = keys.get(i);
							if (key3.indexOf(key) > -1) {
								count3 = i;
								break;
							}
						}
						if (count3 > 0) {
							count3 = count3 + 2;// 增加第一列合并单元和和序号列
						}
						// 表头显示
						rowIndex++;
						this.buildTitle(rowIndex, sheet, map, style, count3, BgcsszbMap.size());
						// 记录本行记录
						keys.clear();
						for (Entry<String, CostBgcsszb> entry1 : map.entrySet()) {
							keys.add(entry1.getKey());
						}
					}
					sheet.addMergedRegion(new CellRangeAddress(xhnumber + 1, rowIndex, 0, 0));
				} else {
					// 不合并
					rowIndex++;
					this.buildTitle(rowIndex, sheet, titleCellOrder);
				}
				sheet.setActive(true);
				sheet.createFreezePane(2, (rowIndex + 1));
				HashMap<String, HSSFCellStyle> csm = new HashMap<String, HSSFCellStyle>();
				// 将订单信息设置
				int orderNo = 0;
				for (int i = 0; i < data.size(); i++) {
					++rowIndex;
					++orderNo;
					JSONObject row11 = data.getJSONObject(i);
					Integer type = 99;
					HSSFFont fontOrder = workbook.createFont();
					Integer dataType = 999;
					Map<String, Object> objMap = new LinkedHashMap<String, Object>();
					for (Entry<String, CostBgcsszb> entry1 : BgcsszbMap.entrySet()) {
						String key = entry1.getKey();
						if (row11.containsKey(key)) {
							String value = row11.getString(key);
							objMap.put(key, value);
						} else {
							objMap.put(key, "");
						}
					}
					if (row11.containsKey("ctype")) {
						String ctype = row11.getString("ctype");
						if ("fl".equals(ctype)) {
							dataType = 1;// 分类
							type = 98;
							this.buildExcle(objMap, type, orderNo, rowIndex, titleProMap, sheet, workbook, csm,
									fontOrder, dataType, BgcsszbMap);
						} else if ("nbt".equals(ctype)) {
							dataType = 4;// 内表头
							type = 99;
							// 内表头
							List<ExcleCell> ntitleCellOrder = new ArrayList<>();
							this.buildStandbyNtitle(ntitleCellOrder, workbook, style, font, BgcsszbMap, objMap,
									rowIndex);
							this.buildTitle(rowIndex, sheet, ntitleCellOrder);
						}
					} else {
						this.buildExcle(objMap, type, orderNo, rowIndex, titleProMap, sheet, workbook, csm, fontOrder,
								dataType, BgcsszbMap);
					}
				}

				// 增加简要分析
				if (remark != null && remark.trim().length() > 0) {
					rowIndex++;
					this.buildjyfx(workbook, style, font, rowIndex, orderNo, sheet, remark, titleCellOrder.size());
				}
				sheetName = costToolService.sheetName(sheetName);
				workbook.setSheetName(i1++, sheetName);// 设置sheet的Name
			}
		}
		return workbook;
	}

	// 增加简要分析
	private void buildjyfx(HSSFWorkbook workbook, HSSFCellStyle style, HSSFFont font, int rowIndex, int orderNo,
			HSSFSheet sheet, String analysisContent, int rowNum) {
		List<ExcleCell> titleCellOrder1 = new ArrayList<>();
		String oNo = String.valueOf(orderNo);
		// 获取表头信息进行编制
		// 第一列
		HSSFRow orderRow = sheet.createRow(rowIndex);
		HSSFCellStyle style_1 = workbook.createCellStyle();
		HSSFFont font_1 = workbook.createFont();
		titleCellOrder1.add(new ExcleCell(oNo, "no", 97, 0, workbook, style_1, font_1));
		HSSFCellStyle style_ = workbook.createCellStyle();
		style_.setAlignment(HorizontalAlignment.LEFT);
		HSSFFont font_ = workbook.createFont();
		titleCellOrder1.add(new ExcleCell("★简要分析", "itemName", 98, 1, workbook, style_, font_));
		titleCellOrder1.add(new ExcleCell(analysisContent, "itemName", 98, 2, workbook, style_, font_));
		for (ExcleCell orderCell : titleCellOrder1) {
			HSSFCell cell = orderRow.createCell(orderCell.getIndex());
			cell.setCellValue(orderCell.getValue());
			cell.setCellStyle(orderCell.getStyle());
		}
		for (int i = 3; i < rowNum; i++) {
			HSSFCell cell = orderRow.createCell(i);
			cell.setCellValue("");
			cell.setCellStyle(style_);
		}

		sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 2, rowNum - 1));
	}

	/**
	 * 内表头
	 * 
	 * @param titleCellPro
	 * @param workbook
	 * @param style
	 * @param font
	 * @param bgcsszbMap     外表头
	 * @param costBgcsszbMap 内表头
	 * @param rowIndex
	 * @param reportType
	 * @return
	 */
	private List<ExcleCell> buildStandbyNtitle(List<ExcleCell> titleCellPro, HSSFWorkbook workbook, HSSFCellStyle style,
			HSSFFont font, Map<String, CostBgcsszb> bgcsszbMap, Map<String, Object> objMap, Integer rowIndex) {
		int index = 0;
		Integer rowIndex_ = rowIndex - 2;
		titleCellPro.add(new ExcleCell(rowIndex_.toString(), "no", 99, index, workbook, style, font));
		for (Entry<String, Object> entry : objMap.entrySet()) {
			String key = entry.getKey();
			Object value_ = entry.getValue();
			String value;
			if (value_ != null) {
				value = value_.toString();
			} else {
				value = "";
			}
			titleCellPro.add(new ExcleCell(value, key, 99, ++index, workbook, style, font));// 1
		}
		return titleCellPro;
	}

	/**
	 * 构建订单表头
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 * @param bgcsszbMap
	 */
	private List<ExcleCell> buildStandbyTitle(List<ExcleCell> titleCellOrder, HSSFWorkbook workbook,
			HSSFCellStyle style, HSSFFont font, Map<String, CostBgcsszb> bgcsszbMap) {
		// 获取表头信息进行编制
		int index = 0;
		titleCellOrder.add(new ExcleCell("序号", "no", 9, 0, workbook, style, font));
		for (Entry<String, CostBgcsszb> entry : bgcsszbMap.entrySet()) {
			String key = entry.getKey();
			if (bgcsszbMap.containsKey(key)) {
//				Integer tmused = bgcsszbMap.get(key).getTmused();
//				if (tmused == 1) {
				String value = bgcsszbMap.get(key).getColumnshowName();
				titleCellOrder.add(new ExcleCell(value, key, 9, ++index, workbook, style, font));// 1
//				}
			}
		}
		return titleCellOrder;
	}

	/**
	 * 构建订单表头
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 * @param bgcsszbMap
	 */
	private List<ExcleCell> buildStandbyProductTitle(List<ExcleCell> titleCellPro, HSSFWorkbook workbook,
			HSSFCellStyle style, HSSFFont font, Map<String, CostBgcsszb> bgcsszbMap) {
		int index = 1;
		titleCellPro.add(new ExcleCell("序号", "no", 99, index, workbook, style, font));
		for (Entry<String, CostBgcsszb> entry : bgcsszbMap.entrySet()) {
			String key = entry.getKey();
			if (bgcsszbMap.containsKey(key)) {
//				Integer tmused = bgcsszbMap.get(key).getTmused();
//				if (tmused == 1) {
				String value = bgcsszbMap.get(key).getColumnshowName();
				titleCellPro.add(new ExcleCell(value, key, 99, ++index, workbook, style, font));// 1
//				}
			}
		}
		return titleCellPro;
	}

	/**
	 * 设置列宽
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	private void columnWidth(int colNum, HSSFSheet sheet) {
		for (int i = 1; i < colNum; i++) {
			sheet.setColumnWidth(i, 20 * 256);// 设置第一列的宽度
		}
	}

	private void buildTitle(int rowIndex, HSSFSheet sheet, Map<String, CostBgcsszb> map, HSSFCellStyle style,
			int count3, int maxCount) {
		HSSFRow row = sheet.createRow(rowIndex);
		// 设置首行标题
		int index = 0;
		HSSFCell cell = row.createCell(index);
		cell.setCellValue("序号");
		cell.setCellStyle(style);
		if (count3 > -1) {
			index = count3 + index;
		}
		int leftColspan = -1;
		int colspan = 1;
		int oldIndex = 0;
		for (Entry<String, CostBgcsszb> entry : map.entrySet()) {
			CostBgcsszb bgcsszb = entry.getValue();
			String columnshowName = bgcsszb.getColumnshowName();
			if (count3 > -1) {
				HSSFCell cell1 = row.createCell(index);
				cell1.setCellValue(columnshowName);
				cell1.setCellStyle(style);
				index++;
			} else {
				int rowspan = bgcsszb.getRowspan();
				index = index + colspan;
				if (leftColspan < 0) {
					leftColspan = bgcsszb.getLeftColspan();
					index = index + leftColspan;
				}
				if ((oldIndex + 1) < index) {
					for (int i = oldIndex + 1; i < index; i++) {
						HSSFCell cell1 = row.createCell(i);
						cell1.setCellValue(columnshowName);
						cell1.setCellStyle(style);
					}
				}
				HSSFCell cell1 = row.createCell(index);
				oldIndex = index;
				cell1.setCellValue(columnshowName);
				cell1.setCellStyle(style);
				//
				colspan = bgcsszb.getColspan();
				// 合并单元格
				if (rowIndex > 0 && (rowIndex + rowspan - 1) > 0 && index > 0 && (index + colspan - 1) > 0) {
					if (rowIndex < (rowIndex + rowspan - 1) || index < (index + colspan - 1)) {
						sheet.addMergedRegion(
								new CellRangeAddress(rowIndex, (rowIndex + rowspan - 1), index, (index + colspan - 1)));
					}
				}
			}
		}
		if (count3 > -1) {// 第三行补充框体
			for (int i = 0; i < count3; i++) {
				HSSFCell cell1 = row.createCell(i);
				cell1.setCellValue("");
				cell1.setCellStyle(style);
			}
			for (int i = index; i < maxCount; i++) {
				HSSFCell cell1 = row.createCell(i);
				cell1.setCellValue("");
				cell1.setCellStyle(style);
			}
		}
	}

	/**
	 * 构建标题
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	private void buildTitle(int rowIndex, HSSFSheet sheet, List<ExcleCell> titleCellOrder) {
		HSSFRow row = sheet.createRow(rowIndex);
		// 设置首行标题
		for (ExcleCell title : titleCellOrder) {
			HSSFCell cell = row.createCell(title.getIndex());
			cell.setCellValue(title.getValue());
			cell.setCellStyle(title.getStyle());
		}
	}

	/**
	 * 
	 * @param <T>
	 * @param order
	 * @param type
	 * @param orderNo
	 * @param rowIndex
	 * @param titleOrderMap
	 * @param sheet
	 * @param workbook
	 * @param style
	 * @param font
	 * @param bgcsszbMap
	 * @return
	 */
	private <T> T buildExcle(Map<String, Object> objMap, int type, int orderNo, int rowIndex,
			Map<String, ExcleCell> titleOrderMap, HSSFSheet sheet, HSSFWorkbook workbook,
			HashMap<String, HSSFCellStyle> sm, HSSFFont font, Integer dataType, Map<String, CostBgcsszb> bgcsszbMap) {
		HSSFRow orderRow = sheet.createRow(rowIndex);
		List<ExcleCell> orderCellList = new ArrayList<>();
		String oNo = String.valueOf(orderNo);// 序号
		HSSFCellStyle cStyle = null;
		if (sm.containsKey("no")) {
			cStyle = sm.get("no");
		} else {
			cStyle = workbook.createCellStyle();
		}
		ExcleCell orderInfo = null;
		if (dataType == 1) {// 分类
			orderInfo = new ExcleCell(oNo, "no", 97, 0, workbook, cStyle, font);
		} else {
			orderInfo = new ExcleCell(oNo, "no", 97, 0, workbook, cStyle, font);
		}
		orderCellList.add(orderInfo);
		Integer xsws, dqfs;
		Boolean isnum = false;
		String tx;
		for (Entry<String, Object> entry : objMap.entrySet()) {
			String property = entry.getKey();
			Object value_ = entry.getValue();
			String value;
			if (value_ != null) {
				value = value_.toString();
			} else {
				value = "";
			}
			if (bgcsszbMap.containsKey(property)) {
				CostBgcsszb bgcsszb = bgcsszbMap.get(property);
				xsws = bgcsszb.getObjdis();// 小数位数
				if (xsws == null) {
					xsws = 0;
				}
				dqfs = bgcsszb.getObjalg();// 对齐方式
				if (dqfs == null) {
					dqfs = 0;
				}
				tx = bgcsszb.getColumnCode();
				if (StringUtils.isEmpty(tx)) {
					tx = property;
				}
				HSSFCellStyle dcStyle = null;
				if (sm.containsKey(property)) {
					dcStyle = sm.get(property);
				} else {
					dcStyle = workbook.createCellStyle();
					if (dqfs == 2) {
						dcStyle.setAlignment(HorizontalAlignment.CENTER);// 居中
					} else if (dqfs == 1) {
						dcStyle.setAlignment(HorizontalAlignment.RIGHT);// 右对齐
					} else {
						dcStyle.setAlignment(HorizontalAlignment.LEFT);// 默认左对齐
					}
					if ("itemPrice".equals(tx) || "dwcbde".equals(tx) || "dhde".equals(tx) || "xhl".equals(tx)
							|| "dh".equals(tx) || "dwcb".equals(tx) || "zcb".equals(tx) || "dhcz".equals(tx)
							|| "dwcbcz".equals(tx) || "nh".equals(tx) || "previousReadOut".equals(tx)
							|| "lastReadOut".equals(tx) || "dbxh".equals(tx)) {
						// 数值类型的要使用小数位数
						StringBuffer sb = new StringBuffer();
						sb.append("###############0");
						if (xsws > 0) {
							sb.append(".");
							for (int i = 0; xsws > i; i++) {
								sb.append("0");
							}
						}
						HSSFDataFormat df = workbook.createDataFormat();
						dcStyle.setDataFormat(df.getFormat(sb.toString()));
					}
					sm.put(property, dcStyle);
				}
				if (type == 98 && "itemName".equals(property)) {// 分类
					value = "★" + value;
				}
				if (this.pm.judgeDouble(value)) {
					isnum = true;
				} else {
					isnum = false;
				}
				ExcleCell c = titleOrderMap.get(property);
				if (c == null) {
					continue;
				}
				ExcleCell ec = new ExcleCell(value, property, type, c.getIndex() - 1, workbook, dcStyle, font);
				if (isnum) {
					ec.setIsnum(isnum);
					ec.setDvalue(Double.parseDouble(value));
				}
				orderCellList.add(ec);
			} else {
				continue;// 没有表头设置的列，是无效的
			}
		}
		for (ExcleCell orderCell : orderCellList) {
			HSSFCell cell = orderRow.createCell(orderCell.getIndex());
			isnum = orderCell.getIsnum();
			if (isnum == null) {
				isnum = false;
			}
			if (isnum) {
				cell.setCellValue(orderCell.getDvalue());
			} else {
				cell.setCellValue(orderCell.getValue());
			}
			cell.setCellStyle(orderCell.getStyle());
		}

		return null;
	}

	/**
	 * excel sheetName名称过滤字符
	 * 
	 * @param contentName
	 * @return
	 */
	@Override
	public String sheetName(String contentName) {
		contentName = contentName.replaceAll("\\*", "_");
		contentName = contentName.replaceAll("\\/", "_");
		contentName = contentName.replaceAll("\\[", "_");
		contentName = contentName.replaceAll("\\]", "_");
		contentName = contentName.replaceAll("/", "_");
		return contentName;
	}
}
