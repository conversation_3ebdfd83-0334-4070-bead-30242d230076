package com.yunhesoft.leanCosting.baseConfig.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.dto.deviceTypelib.DeviceTypelibDto;
import com.yunhesoft.leanCosting.baseConfig.entity.po.DeviceTypelibSampledot;
import com.yunhesoft.leanCosting.baseConfig.service.IDeviceTypelibSampledotService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class DeviceTypelibSampledotServiceImpl implements IDeviceTypelibSampledotService {

	@Autowired
	private EntityService entityService;
	
	@Override
	public List<DeviceTypelibSampledot> getItemData(DeviceTypelibDto dto) {
		Where where = Where.create();
		if (!StringUtils.isEmpty(dto.getPid())) {
			where.eq(DeviceTypelibSampledot::getPid, dto.getPid());
		}
		where.eq(DeviceTypelibSampledot::getTmused, 1);
		Order order = Order.create();
		return entityService.queryList(DeviceTypelibSampledot.class, where, order);
	}

	@Override
	public String saveData(List<DeviceTypelibSampledot> addList, List<DeviceTypelibSampledot> updList,
			List<DeviceTypelibSampledot> delList) {
		String result = "";
		if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
			if (entityService.insertBatch(addList) == 0) {
				result = "添加失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
			if (entityService.updateByIdBatch(updList) == 0) {
				result = "更新失败！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(delList)) {
			if (entityService.deleteByIdBatch(delList, 500) == 0) {
				result = "删除失败！";
			}
		}
		return result;
	}

}
