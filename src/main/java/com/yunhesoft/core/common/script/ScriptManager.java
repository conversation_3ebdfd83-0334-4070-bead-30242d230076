package com.yunhesoft.core.common.script;

import javax.script.Bindings;
import javax.script.ScriptContext;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

/**
 * js脚本解析器
 * 
 * <AUTHOR>
 *
 */
public class ScriptManager {

	private ScriptEngine se = null;

	/**
	 * 获得脚本解析引擎对象
	 * 
	 * @return
	 */
	public ScriptEngine getScriptEngine() {
		if (se == null) {
			se = initScriptEngine();
		}
		return se;
	}

	/**
	 * @category 初始化公式解析引擎
	 */
	public ScriptEngine initScriptEngine() {
		ScriptEngineManager sem = new ScriptEngineManager();
		return sem.getEngineByName("js");
	}

	/**
	 * 获取解析器的变量
	 * 
	 * @param se
	 * @param key
	 * @return
	 */
	public Object get(ScriptEngine se, String key) {
		if (se != null) {
			return se.get(key);
		} else {
			return null;
		}
	}

	/**
	 * 获取脚本解析器的变量
	 * 
	 * @param key
	 * @return
	 */
	public Object get(String key) {
		return this.get(this.getScriptEngine(), key);
	}

	/**
	 * 设置脚本中用到的变量
	 * 
	 * @category 设置公式中用到的变量
	 * @param varName  脚本中变量名称
	 * @param varValue 代码中的变量值
	 */
	public void put(ScriptEngine se, String varName, Object varValue) {
		if (se != null) {
			se.put(varName, varValue);
		}
	}

	/**
	 * 设置脚本中用到的变量
	 * 
	 * @category 设置公式中用到的变量
	 * @param varName  脚本中变量名称
	 * @param varValue 代码中的变量值
	 */
	public void put(String varName, Object varValue) {
		this.put(this.getScriptEngine(), varName, varValue);
	}

	/**
	 * 删除某个变量
	 */
	public void remove(ScriptEngine se, String key) {
		if (se != null) {
			Bindings nn = se.getBindings(ScriptContext.ENGINE_SCOPE);
			if (nn != null) {
				nn.remove(key);
			}
		}
	}

	/**
	 * 删除某个变量
	 */
	public void remove(String key) {
		this.remove(this.getScriptEngine(), key);
	}

	/**
	 * 清除脚本中的所有变量
	 */
	public void clear(ScriptEngine se) {
		if (se != null) {
			Bindings nn = se.getBindings(ScriptContext.ENGINE_SCOPE);
			if (nn != null) {
				nn.clear();
			}
		}
	}

	/**
	 * 清除脚本中的所有变量
	 */
	public void clear() {
		this.clear(this.getScriptEngine());
	}

	/**
	 * 解析并执行脚本
	 * 
	 * @category 解析并执行脚本
	 * @param scriptString 脚本字符串
	 * @return 计算结果
	 * @throws ScriptException 脚本执行错误信息
	 */
	public Object eval(ScriptEngine se, String scriptString) throws ScriptException {
		if (se == null) {
			return null;
		}
		if (scriptString == null || "".equals(scriptString.trim())) {
			return null;
		}
		Object obj = se.eval(scriptString);
		return parseNativeObject(obj);
	}

	/**
	 * 解析并执行脚本
	 * 
	 * @category 解析并执行脚本
	 * @param scriptString 脚本字符串
	 * @return 计算结果
	 * @throws ScriptException 脚本执行错误信息
	 */
	public Object eval(String scriptString) throws ScriptException {
		return eval(this.getScriptEngine(), scriptString);
	}

	/**
	 * 计算后的对象转换
	 * 
	 * @param obj
	 * @return
	 */
	private Object parseNativeObject(Object obj) {
		return ScriptEngineUtils.parseNativeObject(obj);
	}

}
