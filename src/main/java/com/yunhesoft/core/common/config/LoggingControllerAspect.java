package com.yunhesoft.core.common.config;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.http.HttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.ClientInfo;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.syslog.entity.dto.OperationLogAdd;
import com.yunhesoft.syslog.service.LogManageService;
import com.yunhesoft.system.kernel.config.SysUserHolder;

import lombok.extern.log4j.Log4j2;

/**
 * controller日志拦截并存储
 * 
 * <AUTHOR>
 *
 */
@Log4j2
@Aspect
@Component
public class LoggingControllerAspect {

	@Autowired
	private LogManageService logManageService;
	/** controller函数的进入时间 */
	private Long time;
	/** 日志数据 */
	private OperationLogAdd logrec;

	/** 白名单 */
	private static List<String> whiteList = Arrays.asList("/userOnline/userActive", "/menu/routers", "/auth/profile",
			"/auth/captcha-image", "/system/todo/getTodoList", "/system/todo/getTodoCount",
			"/system/todo/clearTodoCachedForUser", "/system/syslog/queryOperLog", "/system/syslog/exportFile",
			"/system/files/getFileStream", "/system/config/loginParams", "/system/config/app-title",
			"/system/config/isModuleRegister", "/system/config/helpUrl", "/system/tools/toolbarLink/getToolBarLinks",
			"/system/tools/diy/pagelib/getPageLibInfoByPageId", "/system/tools/diy/pagelib/getCurrentUserPages",
			"/itfc/conf/queryConfTpl", "/itfc/conf/querySysUrl", "/itfc/conf/query");

	/** 拦截哪个包下的controller类 */
	@Pointcut("within(*..controller..*)")
	public void logControllerMethods() {
	}

	/** 函数入口参数拦截 */
	@Before("logControllerMethods()")
	public void logRequestBefore(JoinPoint joinPoint) {
		time = System.currentTimeMillis();
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = attributes.getRequest();
		String uri = request.getRequestURI().replace(request.getContextPath(), "");
		if (this.isInWriteList(uri)) {// 白名单地址不拦截
			logrec = null;
			return;
		}
		try {
			String username = "";
			ClientInfo clientInfo = new ClientInfo(request);// 客户端信息
			SysUser user = SysUserHolder.getCurrentUser();
			if (user == null) {
				username = "未知用户";
				user = new SysUser();
			} else {
				username = user.getUserName();
			}
			user.setIpAddress(clientInfo.getIpAddress());
			user.setExplorerVer(clientInfo.getExplorerVer());
			user.setOSVer(clientInfo.getOSVer());

			/** 请求入口参数 */
			StringBuffer buff = new StringBuffer();
			Object[] args = joinPoint.getArgs();
			Method method = getMethod(joinPoint);
			String[] parameterNames = getParameterNames(method);
			for (int i = 0; i < args.length; i++) {
				Object argument = args[i];
				if (argument instanceof HttpServletResponse || argument instanceof HttpRequest) {
				} else {
					String parameterName = "";
					if (parameterNames.length >= i) {
						parameterName = "未知参数";
					} else {
						parameterName = parameterNames[i];
					}
					buff.append("\"").append(parameterName).append("\":");
					try {
						buff.append(JSON.toJSONString(argument));
					} catch (Exception e) {
						buff.append("\"对象实例解析json异常：类型(").append(argument.getClass()).append("\"");
					}
				}
			}
			if (buff.length() > 0) {
				buff.insert(0, "{");
				buff.append("}");
			}
			logrec = new OperationLogAdd();
			if (logrec != null) {
				logrec.setRequestUri(uri);
				logrec.setRequestType(request.getMethod());
				logrec.setRequestData(buff.toString());
				logrec.setId(TMUID.getUID());
				logrec.setLoginName(username);
			}
			// System.out.println("**add:" + logrec.getId());
			if (logrec != null) {
				if (logManageService == null) {
					logManageService = SpringUtils.getBean(LogManageService.class);
				}
				logManageService.saveOperLogAsync(logrec);
			}
			buff = null;
		} catch (Exception e) {
			log.error("", e);
		}
	}

	/**
	 * 判断地址是否在白名单
	 * 
	 * @param uri
	 * @return
	 */
	private boolean isInWriteList(String uri) {
		if (whiteList.contains(uri)) {
			return true;
		} else {
			return false;
		}
	}

	/** 函数的返回结果拦截 */
	@AfterReturning(pointcut = "logControllerMethods()", returning = "result")
	public void logRequestAfterReturning(JoinPoint joinPoint, Object result) {
		if (logrec != null) {
			logrec.setDueTimes((System.currentTimeMillis() - time) / 1000.00);// 执行时间
			if (logManageService == null) {
				logManageService = SpringUtils.getBean(LogManageService.class);
			}
			logManageService.saveOperLogAsync(logrec);
		}
	}

	private Method getMethod(JoinPoint joinPoint) {
		Method[] methods = joinPoint.getTarget().getClass().getDeclaredMethods();
		String methodName = joinPoint.getSignature().getName();
		for (Method method : methods) {
			if (method.getName().equals(methodName)) {
				return method;
			}
		}
		return null;
	}

	private String[] getParameterNames(Method method) {
		ParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
		return discoverer.getParameterNames(method);
	}

}
