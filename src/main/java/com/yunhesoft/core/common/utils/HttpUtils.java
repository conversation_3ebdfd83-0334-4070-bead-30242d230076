package com.yunhesoft.core.common.utils;

import java.net.ConnectException;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.Map;
import java.util.Set;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;


/**
 * TM3.5调用接口
 * <AUTHOR>
 *
 */
public class HttpUtils {

	
	/**
	 * TM4调用TM3.5接口数据方法
	 * @param DEBUG
	 * @param url		URL地址 可以接受get传参
	 * @param header	
	 * @param json
	 * @return
	 */
	public static String doPost(boolean DEBUG, String url, Map<String, String> header, String json) {
		// 编码类型
		String ENCODE_TYPE = "utf-8";
		String result = null;
		DefaultHttpClient client = new DefaultHttpClient();

		try {

			if (DEBUG) {
				System.out.println();
				System.out.println("----------------------------------------");
				System.out.println("com.yunhe.tools.InterfaceManage.doPost()");
				System.out.println("----------------------------------------");
				System.out.println("url（通信地址）：" + url);
			}

//					HttpPost post = new HttpPost("http://192.168.0.1:8088/tm3/system/org/sysOrg/getSysOrg");
			HttpPost post = new HttpPost(url);
			post.addHeader("Content-type", "application/json; charset=" + ENCODE_TYPE);

			StringEntity s = new StringEntity("{id:rood}", Charset.forName(ENCODE_TYPE));
			s.setContentEncoding(ENCODE_TYPE);
			s.setContentType("application/json"); // 发送json数据需要设置contentType

			// header
			if (header != null && header.size() > 0) {
				Set<String> headerKeys = header.keySet();

				if (DEBUG) {
					System.out.println();
					System.out.println("header：");
				}

				for (String hK : headerKeys) {
					post.addHeader(hK, header.get(hK));

					if (DEBUG) {
						System.out.println();
						System.out.println("key：" + hK);
						System.out.println("value：" + header.get(hK));
					}
				}
			}

			if (DEBUG) {
				System.out.println();
				System.out.println("请求字符串：" + json);
				System.out.println();
				System.out.println("json（body）：" + s);
			}

			// json body
			post.setEntity(s);

			// request
			HttpResponse res = client.execute(post);
			if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				// HttpEntity entity = res.getEntity();
				result = EntityUtils.toString(res.getEntity());// 返回json格式：
				// response = JSONObject.fromObject(result);

				if (DEBUG) {
					System.out.println();
					System.out.println("result（应答）：" + result);
				}
			} else {
				// result = "POST通信状态码：" + res.getStatusLine().getStatusCode() + "\n";
				// result += "应答信息：" + EntityUtils.toString(res.getEntity());

				System.out.println("\n【错误信息】【通信】【" + new Date() + "】：\n状态码：" + res.getStatusLine().getStatusCode()
						+ "\n" + "应答信息：" + EntityUtils.toString(res.getEntity()));
			}

			client.close();
		} catch (ConnectException e) {
			e.printStackTrace();
			client.close();
		} catch (Exception e) {
			e.printStackTrace();
			client.close();
		}

		return result;
	}
}
