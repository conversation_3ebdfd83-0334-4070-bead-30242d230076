package com.yunhesoft.core.common.utils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

/**
 * java 动态执行类函数
 *
 * <AUTHOR>
 * @since 2022.1.15
 */
@Data
@Log4j2
public class ClassExecute {

    // 执行类名
    private String className;
    // 执行实体对象
    private Object instanceObj;
    // 执行内容
    private String execContent;
    // 函数名称
    private String functionName;
    // 参数类型
    @SuppressWarnings("rawtypes")
    private Class[] argsClass = null;
    // 参数值
    private Object[] argsValue = null;

    /**
     * java 类型执行
     *
     * @param instanceObj 实体类对象
     * @param execContent 执行内容 getOecAuditTodo("oec",2), 注意： 执行内容中执行参数类型为 String
     *                    (不能是json字符串)和 int(不是 Integer)
     * @return
     */
    public Object execClass(Object instanceObj, String execContent) {
        this.initClassParams(execContent);
        this.instanceObj = instanceObj;
        return this.execClass(instanceObj, functionName, argsClass, argsValue);

    }

    /**
     * java 类型执行
     *
     * @param className   例如 ： logicsys.manager.todo.TodoApp
     * @param execContent 例如：getOecAuditTodo()
     * @return
     */
    public Object execClassByName(String className, String execContent) {
        this.initClassParams(execContent);
        this.className = className;
        return this.execClassByName(className, functionName, argsClass, argsValue);
    }

    /**
     * 执行java类函数
     *
     * @param className    类名称 ，例如 ： logicsys.manager.todo.TodoApp
     * @param functionName 执行函数名
     * @param paramList    执行的参数值
     * @return
     */
    @SuppressWarnings("rawtypes")
    public Object execClassByName(String className, String functionName, Class[] argsClass, Object[] argsValue) {
        Object value = null;
        try {
            Class<?> cls = this.getClazz(className);//Class.forName(className);
            try {
                this.instanceObj = cls.newInstance();// 实例化
                return execClass(cls, instanceObj, functionName, argsClass, argsValue);
            } catch (InstantiationException | IllegalAccessException e) {
                log.error("类实例化失败：" + className, e);
            }
        } catch (ClassNotFoundException e) {
            log.error("未找到相关类：" + className, e);
        }
        return value;
    }

    /**
     * 执行java类函数
     *
     * @param instanceObj  类实例化的对象
     * @param functionName 函数名
     * @param argsClass    参数值参数类型
     * @param argsValue    参数值
     * @return
     */
    @SuppressWarnings("rawtypes")
    public Object execClass(Object instanceObj, String functionName, Class[] argsClass, Object[] argsValue) {
        return this.execClass(null, instanceObj, functionName, argsClass, argsValue);
    }

    /**
     * 获取类对象
     *
     * @param className
     * @return
     */
    public Class<?> getClazz(String className) throws ClassNotFoundException {
        //String className = instanceObj.getClass().getName();
        this.className = className;
        return Class.forName(className);
    }

    /**
     * 执行java类函数
     *
     * @param clazz        实体例
     * @param instanceObj  类实例化的对象
     * @param functionName 函数名
     * @param argsClass    参数值参数类型
     * @param argsValue    参数值
     * @return
     */
    @SuppressWarnings("rawtypes")
    public Object execClass(Class<?> clazz, Object instanceObj, String functionName, Class[] argsClass,
                            Object[] argsValue) {
        Object value = null;
        if (instanceObj == null) {
            return null;
        }
        if (functionName == null || functionName.length() == 0) {
            return null;
        }
        if (clazz == null) {// 根据实体对象初始化类
            clazz = instanceObj.getClass();
        }
        Method method = null;
        try {
            if (argsClass != null && argsClass.length > 0) {
                method = clazz.getMethod(functionName, argsClass);
            } else {
                method = clazz.getMethod(functionName);
            }
        } catch (Exception e) {
            log.error("获取函数错误，class:[" + clazz.getName() + "],fun:[" + functionName + "]", e);
            return null;
        }
        if (method != null) {
            try {
                if (argsValue != null && argsValue.length > 0) {
                    value = method.invoke(instanceObj, argsValue); // 执行函数
                } else {
                    value = method.invoke(instanceObj);// 执行函数
                }
//				if (this.execContent != null) {
//					log.info("[执行内容]:" + this.execContent + "[执行结果]:" + (value == null ? "null" : value));
//				}
            } catch (IllegalArgumentException e) {
                // 反射执行该方法失败，参数不正确
                log.error("ClassExec：参数不正确[" + className + "." + functionName + "]"
                        + (this.execContent == null ? "" : this.execContent), e);
            } catch (IllegalAccessException e) {
                // 反射执行该方法失败，无法执行
                log.error("ClassExec：执行该方法失败[" + className + "." + functionName + "]"
                        + (this.execContent == null ? "" : this.execContent), e);
            } catch (InvocationTargetException e) {
                // 反射执行该方法失败，该方法本身抛出异常
                log.error("反射执行该方法失败:", e);
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return value;
    }

    /**
     * 初始化得到执行参数
     *
     * @param execContent
     */
    public void initClassParams(String execContent) {
        try {
            this.execContent = execContent;
            this.argsClass = null;
            this.argsValue = null;
            this.functionName = null;
            if (execContent != null && execContent.length() > 0) {
                int startindex = execContent.indexOf("(");
                int endindex = execContent.indexOf(")");
                if (startindex >= 0 && endindex >= 0) {
                    String funName = execContent.substring(0, startindex);
                    int i = funName.indexOf(".");
                    if (i >= 0) {
                        funName = funName.substring(i + 1);
                    }
                    this.setFunctionName(funName); // map.put("functionName", funName.trim()); // 函数名
                    String params = execContent.substring(startindex + 1, endindex).trim();
                    if (params.length() > 0) {
                        List<String> listParams = new ArrayList<String>();
                        params = params.replaceAll("'", "\"");
                        params = params.replaceAll(" ", "");
                        String[] ary = params.split(",");
                        String param = "";
                        boolean start = false;
                        for (String s : ary) {
                            if (s.startsWith("\"") && s.endsWith("\"")) {
                                listParams.add(s);
                                start = false;
                            } else if (s.startsWith("\"")) {
                                start = true;
                                param = s;
                            } else if (s.endsWith("\"")) {
                                param += "," + s;
                                listParams.add(param);
                                start = false;
                            } else {
                                if (start) {// 字符串的中间变量
                                    param += "," + s;
                                } else {
                                    listParams.add(s);
                                }
                            }
                        }
                        if (listParams.size() > 0) {
                            argsValue = new Object[listParams.size()];
                            argsClass = new Class[listParams.size()];
                            for (int n = 0; n < listParams.size(); n++) {
                                String s = listParams.get(n);
                                String clz = getType(s); // 数据类型
                                if ("int".equals(clz)) {// int
                                    argsClass[n] = int.class;
                                    try {
                                        argsValue[n] = Integer.parseInt(s);
                                    } catch (Exception e) {
                                        log.error("ClassExec：参数值类型转换错误，参数值[" + s + "]to Int", e);
                                    }
                                } else if ("bool".equals(clz)) {// boolean
                                    argsClass[n] = boolean.class;
                                    argsValue[n] = Boolean.parseBoolean(s);
                                } else if ("double".equals(clz)) {// double
                                    argsClass[n] = double.class;
                                    argsValue[n] = Double.parseDouble(s);
                                } else {// String
                                    if (s.startsWith("\"")) {
                                        s = s.substring(1);
                                    }
                                    if (s.endsWith("\"")) {
                                        s = s.substring(0, s.length() - 1);
                                    }
                                    argsClass[n] = String.class;
                                    argsValue[n] = String.valueOf(s);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
//		System.out.println();
    }

    /**
     * 获取数据类型
     *
     * @param s
     * @return
     */
    public String getType(String s) {
        String clz = "";
        if (s != null && s.length() > 0) {
            if (s.startsWith("\"") || s.endsWith("\"")) {
                clz = "str";
            } else if ("true".equalsIgnoreCase(s) || "false".equalsIgnoreCase(s)) {
                clz = "bool";
            } else if (isInteger(s)) {
                clz = "int";
            } else if (isDouble(s)) {
                clz = "double";
            } else {
                clz = "str";
            }
        } else {
            clz = "str";
        }
        return clz;
    }

    /**
     * 是否为int型
     *
     * @param str
     * @return
     */
    private boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    /**
     * 是否为double
     *
     * @param str
     * @return
     */
    private boolean isDouble(String str) {
        if (isInteger(str)) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

//	public static void main(String[] args) {
//		ClassExecute classExe = new ClassExecute();
//		classExe.initClassParams("user.getSys(1,'tt',true,2,3.5)");
//	}

}
