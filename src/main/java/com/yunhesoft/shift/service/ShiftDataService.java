package com.yunhesoft.shift.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.shift.shift.entity.po.ShiftData;

/**
 * 班次数据服务接口
 */
public interface ShiftDataService {

    /**
     * 获取当前班次信息
     * 
     * @param orgcode 机构代码
     * @param progid  班组代码
     * @return 当前班次信息
     */
    ShiftData getCurrentShift(String orgcode, String progid);

    /**
     * 获取上个班次信息
     * 
     * @param orgcode 机构代码
     * @param progid  班组代码
     * @return 上个班次信息
     */
    ShiftData getPreviousShift(String orgcode, String progid);

    /**
     * 获取上个班次的采集点数据
     * 
     * @param orgcode   机构代码
     * @param progid    班组代码
     * @param whereUnit 单位条件（可选）
     * @return 上个班次的采集点数据列表
     */
    List<Map<String, Object>> getPreviousShiftCollectData(String orgcode, String progid, String whereUnit);
}
