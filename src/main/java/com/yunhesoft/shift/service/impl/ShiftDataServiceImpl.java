package com.yunhesoft.shift.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.yunhesoft.shift.shift.entity.po.ShiftData;
import com.yunhesoft.shift.service.ShiftDataService;

import lombok.extern.slf4j.Slf4j;

/**
 * 班次数据服务实现类
 */
@Slf4j
@Service
public class ShiftDataServiceImpl implements ShiftDataService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取当前班次信息
     * 
     * @param orgcode 机构代码
     * @param progid  班组代码
     * @return 当前班次信息
     */
    @Override
    public ShiftData getCurrentShift(String orgcode, String progid) {
        String sql = "SELECT ORGCODE, MODELID, DBRQ, OBJID, OBJNAME, OBJTYPE, " +
                    "SHIFTCLASSID, SHIFTCLASSNAME, SBSJ, XBSJ, TJRQ, GZSJ, PROGID " +
                    "FROM SHIFT_DATA " +
                    "WHERE ORGCODE = ? AND PROGID = ? " +
                    "AND DBRQ = TO_CHAR(SYSDATE, 'YYYY-MM-DD') " +
                    "AND TO_CHAR(SYSDATE, 'HH24:MI:SS') BETWEEN SBSJ AND XBSJ " +
                    "AND ROWNUM = 1";
        
        try {
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, orgcode, progid);
            if (!results.isEmpty()) {
                return mapToShiftData(results.get(0));
            }
        } catch (Exception e) {
            log.error("获取当前班次失败: orgcode={}, progid={}", orgcode, progid, e);
        }
        return null;
    }

    /**
     * 获取上个班次信息
     * 
     * @param orgcode 机构代码
     * @param progid  班组代码
     * @return 上个班次信息
     */
    @Override
    public ShiftData getPreviousShift(String orgcode, String progid) {
        // 先获取当前班次
        ShiftData currentShift = getCurrentShift(orgcode, progid);
        if (currentShift == null) {
            log.warn("未找到当前班次，无法获取上个班次: orgcode={}, progid={}", orgcode, progid);
            return null;
        }

        String sql = "SELECT ORGCODE, MODELID, DBRQ, OBJID, OBJNAME, OBJTYPE, " +
                    "SHIFTCLASSID, SHIFTCLASSNAME, SBSJ, XBSJ, TJRQ, GZSJ, PROGID " +
                    "FROM SHIFT_DATA " +
                    "WHERE ORGCODE = ? AND PROGID = ? " +
                    "AND ((DBRQ = ? AND SBSJ < ?) " +
                    "OR (DBRQ = TO_CHAR(TO_DATE(?, 'YYYY-MM-DD') - 1, 'YYYY-MM-DD'))) " +
                    "ORDER BY DBRQ DESC, SBSJ DESC " +
                    "FETCH FIRST 1 ROWS ONLY";

        try {
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, 
                orgcode, progid, currentShift.getDbrq(), currentShift.getSbsj(), currentShift.getDbrq());
            
            if (!results.isEmpty()) {
                return mapToShiftData(results.get(0));
            }
        } catch (Exception e) {
            log.error("获取上个班次失败: orgcode={}, progid={}", orgcode, progid, e);
        }
        return null;
    }

    /**
     * 获取上个班次的采集点数据
     * 
     * @param orgcode 机构代码
     * @param progid  班组代码
     * @param whereUnit 单位条件（如果有额外的单位过滤条件）
     * @return 上个班次的采集点数据列表
     */
    @Override
    public List<Map<String, Object>> getPreviousShiftCollectData(String orgcode, String progid, String whereUnit) {
        // 获取上个班次信息
        ShiftData previousShift = getPreviousShift(orgcode, progid);
        if (previousShift == null) {
            log.warn("未找到上个班次，无法获取采集点数据: orgcode={}, progid={}", orgcode, progid);
            return null;
        }

        // 构建查询采集点数据的SQL
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT COLLECT_POINT_ID, IPT_ID, COLLECT_POINT_TEXT, COLLECT_POINT_VAL, ");
        sqlBuilder.append("INPUT_COMP_TYPE, INPUT_OPTIONS, INPUT_TIME, JOB_INPUT_TIME ");
        sqlBuilder.append("FROM ACCTOBJ_INPUTMX ");
        sqlBuilder.append("WHERE TMUSED = 1 ");
        sqlBuilder.append("AND EXISTS (");
        sqlBuilder.append("    SELECT ID FROM ACCTOBJ_INPUT ");
        sqlBuilder.append("    WHERE TMUSED = 1 ");
        
        // 如果有额外的单位条件，添加到查询中
        if (whereUnit != null && !whereUnit.trim().isEmpty()) {
            sqlBuilder.append("    AND ACCTOBJ_ID ").append(whereUnit);
        } else {
            sqlBuilder.append("    AND ACCTOBJ_ID = ? ");
        }
        
        sqlBuilder.append("    AND BCDM = ? ");
        sqlBuilder.append("    AND SBSJ = ? ");
        sqlBuilder.append("    AND TEAM_ID = ? ");
        sqlBuilder.append("    AND ID = ACCTOBJ_INPUTMX.IPT_ID");
        sqlBuilder.append(")");

        try {
            List<Map<String, Object>> results;
            if (whereUnit != null && !whereUnit.trim().isEmpty()) {
                // 如果有whereUnit条件，只传递班次相关参数
                results = jdbcTemplate.queryForList(sqlBuilder.toString(), 
                    previousShift.getShiftclassid(), 
                    previousShift.getSbsj(), 
                    previousShift.getProgid());
            } else {
                // 标准查询，包含OBJID参数
                results = jdbcTemplate.queryForList(sqlBuilder.toString(), 
                    previousShift.getObjid(),
                    previousShift.getShiftclassid(), 
                    previousShift.getSbsj(), 
                    previousShift.getProgid());
            }
            
            log.info("获取上个班次采集点数据成功: 班次={}, 数据条数={}", 
                previousShift.getShiftclassname(), results.size());
            return results;
            
        } catch (Exception e) {
            log.error("获取上个班次采集点数据失败: orgcode={}, progid={}", orgcode, progid, e);
            return null;
        }
    }

    /**
     * 将查询结果映射为ShiftData对象
     */
    private ShiftData mapToShiftData(Map<String, Object> row) {
        ShiftData shiftData = new ShiftData();
        shiftData.setOrgcode((String) row.get("ORGCODE"));
        shiftData.setModelid((String) row.get("MODELID"));
        shiftData.setDbrq((String) row.get("DBRQ"));
        shiftData.setObjid((String) row.get("OBJID"));
        shiftData.setObjname((String) row.get("OBJNAME"));
        shiftData.setObjtype((Integer) row.get("OBJTYPE"));
        shiftData.setShiftclassid((String) row.get("SHIFTCLASSID"));
        shiftData.setShiftclassname((String) row.get("SHIFTCLASSNAME"));
        shiftData.setSbsj((String) row.get("SBSJ"));
        shiftData.setXbsj((String) row.get("XBSJ"));
        shiftData.setTjrq((String) row.get("TJRQ"));
        shiftData.setGzsj((Double) row.get("GZSJ"));
        shiftData.setProgid((String) row.get("PROGID"));
        return shiftData;
    }
}
