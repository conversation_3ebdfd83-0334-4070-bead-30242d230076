package com.yunhesoft.report.exception;

/**
 * 自定义报表模块异常类
 * 作用：处理中途抛出自定义异常，在外层方法捕获此异常进行特殊处理
 */
public class ReportException extends Exception {
    public ReportException() {
        // 调用父类无参构造方法
        super();
    }

    public ReportException(String message) {
        // 调用父类有参构造方法
        super(message);
    }

    public ReportException(String message, Exception e) {
        // 调用父类有参构造方法
        super(message, e);
    }
}
