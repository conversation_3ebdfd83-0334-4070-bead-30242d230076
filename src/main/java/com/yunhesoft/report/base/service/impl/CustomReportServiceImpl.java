package com.yunhesoft.report.base.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.aviator.AviatorResult;
import com.yunhesoft.core.common.aviator.AviatorUtils;
import com.yunhesoft.core.common.utils.MD5Utils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.report.base.entity.dto.CustomReportDto;
import com.yunhesoft.report.base.entity.dto.CustomReportManageDto;
import com.yunhesoft.report.base.entity.dto.CustomReportQueryDto;
import com.yunhesoft.report.base.entity.po.ReportManage;
import com.yunhesoft.report.base.entity.po.ReportQuery;
import com.yunhesoft.report.base.entity.po.ReportRegion;
import com.yunhesoft.report.base.entity.vo.ReportCondsVo;
import com.yunhesoft.report.base.entity.vo.ReportEchartsDataVo;
import com.yunhesoft.report.base.entity.vo.ReportEchartsVo;
import com.yunhesoft.report.base.entity.vo.ReportManageVo;
import com.yunhesoft.report.base.entity.vo.ReportQueryVo;
import com.yunhesoft.report.base.service.ICustomReportService;
import com.yunhesoft.report.base.service.IReportManageService;
import com.yunhesoft.report.exception.ReportException;
import com.yunhesoft.report.tds.model.TDSReport;
import com.yunhesoft.report.utils.ReportConstant;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.model.IDataSource;
import com.yunhesoft.system.tds.model.TDataSourceManager;
import com.yunhesoft.system.tds.model.TOutPara;
import com.yunhesoft.system.tds.model.TRow;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tds.utils.TdsTools;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Service("CustomReportService")
public class CustomReportServiceImpl implements ICustomReportService {
    @Autowired
    IDataSourceService idsServ; //数据源接口服务
    @Autowired
    IReportManageService iRMServ; //报表配置管理服务
    @Autowired
    private RedisUtil redis; //Redis 缓存服务
    
    private String measureKey="度量";//度量查询条件的alias  ReportConstant.REPORT_CUSTOM_COLNAME_MEASURE 不能用，前台写死的度量

    private TDataSourceManager dsm = null;

    /**
     * 解析绑定数据源，返回报表统计结果集
     *
     * @param params 整合参数集
     */
    @Override
    public void getReportData(JSONObject params, TDSReport idsOut) {
        String reportId = params.getString("reportId"); //报表ID
        JSONArray queryConds = params.getJSONArray("queryConds"); //自定义报表查询条件
        Integer isRecount = params.getInteger("isRecount"); //重新统计标识
        
        long s0 = System.currentTimeMillis();

        try {
            //读取自定义报表相关配置
            long s1 = System.currentTimeMillis();
            CustomReportDto crDto = getReportConfig(reportId);
            Integer echartsShow = crDto.getReportManage().getEchartsShow();
            if(echartsShow!=null && echartsShow.intValue()==1) {//显示分析图
            	idsOut.setEchartsMode(true);
            }
            if(queryConds!=null && queryConds.size()>0 && StringUtils.isNotEmpty(crDto.getMeasureL())) {
            	for(int i=0,j=queryConds.size();i<j;i++) {
            		JSONObject obj = queryConds.getJSONObject(i);
            		if(measureKey.equals(obj.getString("alias"))) {//有度量条件
            			String defVal = obj.getString("defVal");
                    	if (StringUtils.isNotEmpty(defVal)) {//制定了度量
                    		StringBuffer val = new StringBuffer(defVal =","+defVal+",");
                    		List<ReportRegion> filterList = crDto.getMeasureL().stream().filter(item -> val.indexOf(","+item.getBindParamAlias()+",")>=0).collect(Collectors.toList());
                    		if (StringUtils.isNotEmpty(filterList)) {//过滤度量后，有值
                    			crDto.setMeasureL(filterList);
                    		}
                    	}else {
                    		//不指定的话，不进行度量过滤
                    	}
            		}
            	}
            }            
            long e1 = System.currentTimeMillis();
            log.info("「报表模块」「报表查询」：读取自定义报表相关配置完成（{} ms）", e1 - s1);
            //获取绑定数据源数据
            String tdsAlias = crDto.getReportManage().getTdsAlias(); //绑定数据源别名

            //如开启缓存，则获取 Redis 缓存数据
            Integer cacheResult = crDto.getReportManage().getCacheResult();
            boolean ifCache = false; //是否缓存结果
            boolean hasCacheResult = false; //是否存在缓存数据
            if (cacheResult != null && cacheResult == 1) {
                ifCache = true;

                if (isRecount == null || isRecount != 1) { //如果不重新统计，则获取缓存结果；如重新统计，则返回空缓存，重新处理查询统计
                    //获取缓存结果
                    hasCacheResult = getCacheResult(tdsAlias, queryConds, idsOut, crDto);
                }
            }

            if (!hasCacheResult) { //无缓存结果，重新处理生成结果数据
                //解析绑定数据源输入参数
                long s2 = System.currentTimeMillis();
                String inParaAlias = getInParaAlias(queryConds);
                long e2 = System.currentTimeMillis();
                log.debug("「报表模块」「报表查询」：解析绑定数据源输入参数完成（{} ms）", e2 - s2);

                //获取绑定数据源数据
                long s3 = System.currentTimeMillis();
                IDataSource ids = idsServ.getTDSData(tdsAlias, inParaAlias);
                long e3 = System.currentTimeMillis();
                log.info("「报表模块」「报表查询」：获取绑定数据源数据完成（{} ms）", e3 - s3);

                //二阶过滤：使用自定义查询条件（绑定输出参数）过滤数据源输出集
                long s4 = System.currentTimeMillis();
                idsDataFilter(ids, queryConds);
                long e4 = System.currentTimeMillis();
                log.debug("「报表模块」「报表查询」：过滤绑定数据源数据完成（{} ms）", e4 - s4);

                //报表统计
                long s5 = System.currentTimeMillis();
                reportStatistic(ids, crDto, idsOut);   // TDSReport
                long e5 = System.currentTimeMillis();
                log.debug("「报表模块」「报表查询」：报表统计完成（{} ms）", e5 - s5);

                if (ifCache) { //开启缓存
                    //缓存结果至 Redis
                    cacheReportResult(tdsAlias, queryConds, idsOut, crDto);
                } else { //关闭缓存
                    //清除缓存结果，避免关闭缓存并二次开启后，查询的是历史缓存数据（此时应根据新数据重新生成缓存）
                    clearReportResult(tdsAlias, queryConds, crDto);
                }
            }
        } catch (ReportException e) {
            log.error("「报表模块」「报表查询」：处理有误，错误信息：{}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("「报表模块」「报表查询」：处理有误", e);
        }

        long e0 = System.currentTimeMillis();
        log.info("「报表模块」「报表查询」：自定义报表查询统计完成（总用时：{} ms）", e0 - s0);
    }

    /**
     * 获取检索条件列表
     *
     * @param queryDto 检索条件参数
     * @return List<ReportQueryVo>
     */
    @SuppressWarnings("unchecked")
	@Override
    public List<ReportQueryVo> getReportQueryList(CustomReportQueryDto queryDto) {
        if (queryDto == null) {
            return null;
        }        
        Map<String,TdsinPara> tdsiInparaMap = null; //数据源输入参数map，用于读取数据源的配置
        Map<String,HashMap<String, Object>> tdsQueryDataMap = null;//加载好的数据源输入参数列表
        HashMap<String, IDataSource> idsMap = new HashMap<String,IDataSource>();//数据源缓存map
        //根据报表id获取条件集合
        List<ReportQuery> rQL = iRMServ.getReportQueryDataListByReportId(queryDto.getReportId());
        List<ReportQueryVo> rQVL = ObjUtils.convertToList(ReportQueryVo.class, rQL);
        for (ReportQueryVo reportQueryVo : rQVL) {
            if(measureKey.equals(reportQueryVo.getAlias())) {//度量的默认下拉框值
            	List<HashMap<String,String>> measureList = new ArrayList<HashMap<String,String>>();
            	List<ReportRegion> regionList = iRMServ.getReportRegionListByReportId(queryDto.getReportId());
            	if (StringUtils.isNotEmpty(regionList)) {
        		   for(ReportRegion temp:regionList) {
        			   if("measure".equals(temp.getRegion())) {
        				   HashMap<String,String> bean = new HashMap<String,String>();
        				   bean.put("key", temp.getBindParamAlias());
        				   bean.put("value", temp.getBindParam());
        				   measureList.add(bean);
        			   }
        		   }
            	}
            	reportQueryVo.setOptionVal(JSON.toJSONString(measureList));//[{'key':'ZQRTXQD2005CVZKB2S0193','value':'生产技术处'},{'key':'ZQUB0ALSB039LGC5DR1609','value':'热电厂'}]
            }else {
            	boolean isLoad = false;//是否已经加载了输入列参数
            	if(reportQueryVo.getReadTdsConfig()!=null && reportQueryVo.getReadTdsConfig().intValue()==1) {//读取数据源的输入参数配置
//            		if(!isLoad) {
//	            		if(tdsiInparaMap==null) {
//	            			ReportManage rmB = iRMServ.getReportManageById(queryDto.getReportId());
//	            			if(rmB!=null) {
//	            				List<TdsinPara> inparaList = idsServ.getListTdsInPara(rmB.getTdsAlias());
//	            				if (StringUtils.isNotEmpty(inparaList)) {
//	            					tdsiInparaMap = inparaList.stream().collect(Collectors.toMap(TdsinPara::getParaAlias,
//	            							obj -> obj, (key1, key2) -> key1));// 将list转换为map 重复键值时，第一个key不被第二个key覆盖	  
//	            				}
//	            				queryDto.setReport(rmB);
//	            			}
//	            			if(tdsiInparaMap==null) {
//	            				tdsiInparaMap = new HashMap<String,TdsinPara>();
//	            			}
//	            		}
//	            		TdsinPara inparam = tdsiInparaMap.get(reportQueryVo.getBindParamAlias());
//	            		if(inparam!=null) {
//	            			reportQueryVo.setAlias(inparam.getParaAlias());//复制别名，防止手动改了别名，导致联动出问题
//	            			reportQueryVo.setComponentType(inparam.getComponentType());//组件类型，目前仅支持部分组件，不是全支持
//	            			if("combo".equals(reportQueryVo.getComponentType())) {
//	            				try {
//	            					JSONObject comParams = JSON.parseObject(inparam.getComParams());
//	            					String multiple = comParams.getString("multiple");
//	            					if("1".equals(multiple)) {
//	            						reportQueryVo.setComponentType("lovcombo");//这里特殊，多选下拉框在报表里是单独的一个选项
//	            					}   				
//	            				}catch(Exception e) {
//	            					log.error(e);
//	            				}
//	            				List<HashMap<String,String>> comboList = new ArrayList<HashMap<String,String>>();       				
//	            				try {
//	            					Object keyVal = getDs().getScrictDefaultValueWithOutLoadTds(inparam.getDefaultKeyScript(), idsMap)+"\"\"''''";
//	            					if(keyVal!=null) {
//	            						String[] keyArr = getComboList(String.valueOf(keyVal));
//	            						if(keyArr!=null) {
//	            							for(String tempKey:keyArr) {
//	            								if(tempKey!=null) {//这里只能判断null，因为空白在这里有意义
//	            									HashMap<String,String> comboBean = new HashMap<String,String>();
//	            									comboBean.put("key", tempKey);
//	            									comboBean.put("value", tempKey);//默认value=key，如果key没有的话，就value=key
//	            									comboList.add(comboBean);
//	            								}
//	            							}
//	            						}
//	            					}
//	            				}catch(Exception e) {
//	            					log.error(e);
//	            				}	
//	            				try {
//	            					Object valueVal = getDs().getScrictDefaultValueWithOutLoadTds(inparam.getDefaultValueScript(), idsMap);
//	            					if(valueVal!=null) {
//	            						String[] valueArr = getComboList(String.valueOf(valueVal));
//	            						if(valueArr!=null) {
//	            							int k = comboList.size();
//	            							for(int i=0,j=valueArr.length;i<j;i++) {
//	            								if(i<k) {
//	            									HashMap<String,String>	comboBean = comboList.get(i);
//	            									comboBean.put("value", valueArr[i]);//设置显示值
//	            								}
//	            							}
//	            						}
//	            					}
//	            				}catch(Exception e) {
//	            					log.error(e);
//	            				}
//	            				reportQueryVo.setOptionVal(JSON.toJSONString(comboList));
//	            				if(comboList.size()>0) {
//	            					reportQueryVo.setDefVal(comboList.get(0).get("key"));//用第一个选项值作为默认值
//	            				}
//	            			}else {
//	            				try {
//	            					Object v = getDs().getScrictDefaultValueWithOutLoadTds(inparam.getDefaultKeyScript(), idsMap);
//	            					if(v!=null) {
//	            						reportQueryVo.setDefVal(String.valueOf(v));
//	            					}
//	            				}catch(Exception e) {
//	            					log.error(e);
//	            				}
//	            			}	
//	            			isLoad=true;//已加载了对应数据源的参数	
//	            		}
//            		}else {         	
            			if(tdsQueryDataMap==null) {
            				ReportManage rmB = iRMServ.getReportManageById(queryDto.getReportId());
            				if(rmB!=null) {
		            			tdsQueryDataMap=new HashMap<String,HashMap<String, Object>>();
		            			TdsQueryDto param = new TdsQueryDto();
		        				param.setTdsAlias(rmB.getTdsAlias());
		        				HashMap<String, Object> queryParam = idsServ.getTDSQuery(param);
		        				if(queryParam!=null) {
			        				if(queryParam.containsKey("data")) {
			        					List<HashMap<String, Object>> tdsQueryData = (List<HashMap<String, Object>>) queryParam.get("data");
			        					if(tdsQueryData!=null && tdsQueryData.size()>0) {
			        						tdsQueryDataMap=new HashMap<String,HashMap<String, Object>>();
			        						for(HashMap<String, Object> paramTemp:tdsQueryData) {
			        							tdsQueryDataMap.put(String.valueOf(paramTemp.get("name")), paramTemp);
			        						}
			        					}
			        				}
			        				if(queryParam.containsKey("relCount")) {
			        					try {
			        						int relCount = Integer.parseInt(String.valueOf(queryParam.get("relCount")));
			        						if(relCount>0) {
			        							queryDto.setReadLinked(true);//有数据源联动
			        						}
			        					}catch(Exception e) {
			        						log.error(e);
			        					}	
			        				}
		            				queryDto.setReport(rmB);
		        				}
		        				
		            		}
            				if(tdsQueryDataMap==null) {
            					tdsQueryDataMap=new HashMap<String,HashMap<String, Object>>();
            				}	
            			}
            			HashMap<String, Object> inparam = tdsQueryDataMap.get(reportQueryVo.getBindParamAlias());
	            		if(inparam!=null) {//{xtype=combo, label=机构, store=[{key=ZQRTXRC1R05CVZK86F0167, value=一车间2025-05}, {key=ZQRTXRC1R05CVZK86I0190, value=二车间2025-05}], valueField=key, mode=local, rawValue=一车间2025-05, comParams={"selectType":"1","idField":"","selectClearble":"","func":"vin","multiple":"1"}, fieldLabel=机构, name=orgCode, width=120, displayField=value, insertedit=true, value=ZQRTXRC1R05CVZK86F0167}
	            			reportQueryVo.setAlias(reportQueryVo.getBindParamAlias());//复制别名，防止手动改了别名，导致联动出问题
	            			reportQueryVo.setComponentType(String.valueOf(inparam.get("xtype")));//组件类型，目前仅支持部分组件，不是全支持
	            			if("combo".equals(reportQueryVo.getComponentType())) {
	            				try {
	            				    if (inparam.containsKey("comParams")) {
	            				    	String comParamsStr=String.valueOf(inparam.get("comParams"));
	            		            	if (StringUtils.isNotEmpty(comParamsStr)) {
			            					JSONObject comParams = JSON.parseObject(String.valueOf(inparam.get("comParams")));
			            					String multiple = comParams.getString("multiple");
			            					if("1".equals(multiple)) {
			            						reportQueryVo.setComponentType("lovcombo");//这里特殊，多选下拉框在报表里是单独的一个选项
			            					}   	
	            		            	}
	            				    }
	            				}catch(Exception e) {
	            					log.error(e);
	            				}
	            				if (inparam.containsKey("store")) {
	            					List<HashMap<String,Object>> comboList = (List<HashMap<String,Object>>)inparam.get("store");       		
	            					if (StringUtils.isNotEmpty(comboList)) {
	            						reportQueryVo.setOptionVal(JSON.toJSONString(comboList));
	            					}
	            				}
	            			}	            			
	            			reportQueryVo.setDefVal(String.valueOf(inparam.get("value")));           				
	            			isLoad=true;//已加载了对应数据源的参数	
	            		}
//            		}
            	}
            	if(!isLoad) {//没加载过输入列
                    String v = parseFormula(reportQueryVo.getDefVal(),idsMap); //公式解析
                    reportQueryVo.setDefVal(v);
            	}
            }
        	if(reportQueryVo.getDefVal()==null) {
        		reportQueryVo.setDefVal("");//确保默认值不为null
        	}
        }

        return rQVL;
    }
    
    /**
     * 获取检索条件列表(带重要配置信息)
     *
     * @param queryDto 自定义报表检索条件数据传输对象
     * @return ReportCondsVo
     */
    @Override
    public ReportCondsVo getReportQueryListWithCfg(CustomReportQueryDto queryDto) {
    	ReportCondsVo result = new ReportCondsVo();
    	result.setCondsList(getReportQueryList(queryDto));
    	ReportManage rmB = queryDto.getReport();//是否携带出来了该属性，如没有，则需要现查询
    	if(rmB==null) {
			rmB = iRMServ.getReportManageById(queryDto.getReportId());	
    	}
    	if(rmB!=null) {//找到了报表信息
    		result.setTdsAlias(rmB.getTdsAlias());//数据源别名
			if(rmB.getEchartsShow()!=null && rmB.getEchartsShow().intValue()==1) {
				result.setEchartsMode(true); 
			}else {
				result.setEchartsMode(false); 
			}
			boolean readLinked = false;
			if(queryDto.getReadLinked()!=null) {
				readLinked=queryDto.getReadLinked().booleanValue();
			}
			result.setLinkedMode(readLinked);//是否数据源条件联动
		}
    	return result;
    }
    
    private TDataSourceManager getDs() {
    	if(this.dsm==null) {
    		this.dsm = new TDataSourceManager();
    	}
    	return this.dsm;
    }
    /**
     * 获取下拉列表框内容
     * @category 获取下拉列表框内容
     * <AUTHOR> 
     * @param scrict 脚本
     * @return String[]
     */
    private String[] getComboList(String scrict) {
    	String[] result = null;
    	if(scrict!=null) {
    		String keyStr=scrict.trim().replaceAll("[\"\\'\\s]+","");//去除引号和空白字符
    		if(keyStr.startsWith("[") && keyStr.endsWith("]")) {//去掉外括号
    			keyStr=keyStr.substring(1,keyStr.length()-1);
    		}
    		result = keyStr.split(",");	
    	}
    	return result;
    }

    
    /**
     * 获取报表配置
     *
     * @param queryDto 自定义报表数据传输对象
     * @return ReportManageVo
     */
    public ReportManageVo getReportManage(CustomReportManageDto queryDto) {
        ReportManage rM = iRMServ.getReportManageById(queryDto.getReportId());
        return ObjUtils.copyTo(rM, ReportManageVo.class);
    }

    /**
     * 解析绑定数据源输入参数
     *
     * @param queryConds 自定义报表查询条件
     * @return String
     */
    private String getInParaAlias(JSONArray queryConds) throws ReportException {
        String inParaAlias = "";

        if (StringUtils.isEmpty(queryConds)) {
            return inParaAlias;
        }

        try {
            List<String> inParaList = new ArrayList<>();

            for (int i = 0; i < queryConds.size(); i++) {
                JSONObject queryCond = queryConds.getJSONObject(i);

                Integer bindType = queryCond.getInteger("bindType");
                if (bindType != null && bindType == 1) { //数据源入参（透传）
                    String bindParamAlias = queryCond.getString("bindParamAlias"); //绑定参数别名
                    if (StringUtils.isNotEmpty(bindParamAlias)) {
                        String defVal = queryCond.getString("defVal"); //默认值：数据源公式
                        if (defVal == null) {
                            defVal = "";
                        }
                        //TIPS：前端加载查询条件控件时解析公式，此处接收到的是计算结果值，无需再解析公式
                        inParaList.add(bindParamAlias + "=" + defVal);
                    }
                }
            }

            inParaAlias = String.join("|", inParaList.toArray(new String[0]));
        } catch (RuntimeException e) {
            throw new ReportException("解析绑定数据源输入参数有误", e);
        }

        return inParaAlias;
    }

    /**
     * 使用自定义查询条件（绑定输出参数）过滤数据源输出集
     *
     * @param ids        数据源
     * @param queryConds 检索条件
     */
    private void idsDataFilter(IDataSource ids, JSONArray queryConds) throws ReportException {
        //解析数据源自定义查询条件
        JSONArray filterParams = getIdsDataFilterParams(queryConds);
        if (StringUtils.isEmpty(filterParams)) {
            return;
        }

        //数据源公式计算结果缓存集合 Map 初始化
        //Map<String, String> formularValCacheMap = new HashMap<>(); //数据源公式计算结果缓存集合，key：公式，value：计算值
        //initFormularValCacheMap();

        //通过自定义查询条件过滤输出集，生成新数据集
        List<TRow> newList = ids.getDataStore().getTRows().stream()  //生成流
                .filter(b -> idsDataFilter(b, filterParams)) //过滤
                .collect(Collectors.toCollection(ArrayList<TRow>::new)); //流 -> List

        //更新数据源中的数据集
        ids.getDataStore().setTRows(newList);
    }

    /**
     * 组装过滤表达式，返回判定结果
     *
     * @param b  待过滤记录
     * @param ps 条件集
     * @return boolean
     */
    private boolean idsDataFilter(TRow b, JSONArray ps) {
        boolean ok = true;

        try {
            if (StringUtils.isNotEmpty(ps)) {
                for (int i = 0; i < ps.size(); i++) {
                    JSONObject p = ps.getJSONObject(i);

                    String bindParamAlias = p.getString("bindParamAlias"); //自定义查询条件绑定的数据源输出参数别名
                    Object srcVal = b.get(bindParamAlias);//源值（上游数据源输出集合记录值）
                    String operator = p.getString("operator"); //关系运算符
                    Object dstVal = p.getString("defVal"); //目标值（前端控件设定的默认值）
                    String datatype = p.getString("datatype"); //数据类型

                    if (TdsTools.PARAMTYPE_NUMBER.equals(datatype)) {
                        srcVal = TdsTools.castColumnValue(TdsTools.PARAMTYPE_NUMBER, srcVal);
                        dstVal = TdsTools.castColumnValue(TdsTools.PARAMTYPE_NUMBER, dstVal);
                        ok = compareBigDecimal((BigDecimal) srcVal, (BigDecimal) dstVal, operator);
                    } else {
                        srcVal = TdsTools.castColumnValue(TdsTools.PARAMTYPE_STRING, srcVal);
                        dstVal = TdsTools.castColumnValue(TdsTools.PARAMTYPE_STRING, dstVal);
                        //使用源值、目标值、关系运算符，组装表达式得到 true / false
                        ok = compareString((String) srcVal, (String) dstVal, operator);
                    }

                    if (!ok) {
                        break; //所有条件均为与（and）关系，则有一个是 false 就返回 false
                    }
                }
            }
        } catch (Exception e) {
            log.warn("「报表模块」「报表查询」：数据源结果集过滤有误", e);
            ok = false;
        }

        return ok;
    }

    /**
     * 使用源值、目标值、关系运算符，组装表达式得到 true / false
     *
     * @param sv  源值
     * @param dv  目标值
     * @param opr 关系运算符
     * @return boolean
     */
    private boolean compareString(String sv, String dv, String opr) {
        boolean ok;

        switch (opr) {
            case "等于":
                ok = sv.contentEquals(dv);
                break;
            case "不等于":
                ok = !sv.contentEquals(dv);
                break;
            case "包含":
                ok = sv.contains(dv);
                break;
            case "不包含":
                ok = !sv.contains(dv);
                break;
            case "大于":
                ok = TdsTools.stringCompare(sv, dv) > 0;
                break;
            case "大于等于":
                ok = TdsTools.stringCompare(sv, dv) >= 0;
                break;
            case "小于":
                ok = TdsTools.stringCompare(sv, dv) < 0;
                break;
            case "小于等于":
                ok = TdsTools.stringCompare(sv, dv) <= 0;
                break;
            case "为空":
                ok = StringUtils.isEmpty(sv);
                break;
            case "不为空":
                ok = StringUtils.isNotEmpty(sv);
                break;
            default:
                ok = false;
                break;
        }

        return ok;
    }

    private boolean compareBigDecimal(BigDecimal sv, BigDecimal dv, String opr) {
        boolean ok;

        //TIPS：数值无法比较 "包含"、"不包含"、"为空"、"不为空"
        switch (opr) {
            case "等于":
                ok = sv.compareTo(dv) == 0;
                break;
            case "不等于":
                ok = sv.compareTo(dv) != 0;
                break;
            case "大于":
                ok = sv.compareTo(dv) > 0;
                break;
            case "大于等于":
                ok = sv.compareTo(dv) >= 0;
                break;
            case "小于":
                ok = sv.compareTo(dv) < 0;
                break;
            case "小于等于":
                ok = sv.compareTo(dv) <= 0;
                break;
            default:
                ok = false;
                break;
        }

        return ok;
    }

    /**
     * 解析公式计算求值
     *
     * @param formula 公式
     * @return String
     */
    private String parseFormula(String formula,HashMap<String, IDataSource> idsMap) {
        String v = "";

        //参考说明 - 使用 AviatorScript5.0 进行公式解析：http://www.mytm3.com:8181/docs/tm4doc//384
        //获取公式中的变量
        Map<String, List<String>> paramMap = AviatorUtils.getAllParams(formula);
        List<String> tdsFormulaList = paramMap.get("tdsFormula"); // 数据源公式列表

        //数据源变量赋值
        Map<String, Object> tdsValueMap = new HashMap<>();
        for (String param : tdsFormulaList) {
            Object scriptValue;
            try {
                String tdsFormula = param;
                if (param.startsWith("$_.")) {
                    tdsFormula = param.substring(3);
                }
                //解析数据源公式
                
//                scriptValue = idsServ.getScriptValue(tdsFormula, null, null);//不能用这个，这个没加载自定义函数 比如getMonth()
                scriptValue = getDs().getScrictDefaultValueWithOutLoadTds(tdsFormula, idsMap);
                if (scriptValue == null) {
                    scriptValue = 0;
                }
            } catch (Exception e) {
                scriptValue = 0;
            }

            //key格式：$system.get(0).getf('数量')
            tdsValueMap.put(param, scriptValue);
        }

        //公式计算
        AviatorResult result = AviatorUtils.execute(formula, null, tdsValueMap);
        if (result.getResult() != null) {
            v = result.getResult().toString(); //计算结果
        }

        return v;
    }

    /**
     * 解析数据源自定义查询条件
     *
     * @param queryConds 自定义报表查询条件
     * @return JSONArray
     */
    private JSONArray getIdsDataFilterParams(JSONArray queryConds) throws ReportException {
        JSONArray filterParams = new JSONArray();

        if (StringUtils.isEmpty(queryConds)) {
            return filterParams;
        }

        try {
            for (int i = 0; i < queryConds.size(); i++) {
                JSONObject queryCond = queryConds.getJSONObject(i);

                Integer bindType = queryCond.getInteger("bindType");
                if (bindType != null && bindType == 2) { //数据源出参（过滤）
                    filterParams.add(queryCond);
                }
            }
        } catch (RuntimeException e) {
            throw new ReportException("解析数据源自定义查询条件有误", e);
        }

        return filterParams;
    }

    /**
     * 读取自定义报表相关配置
     *
     * @param reportId 报表id
     */
    private CustomReportDto getReportConfig(String reportId) throws ReportException {
        CustomReportDto crDto; //自定义报表数据传输对象

        try {
            //读取数据库加载报表配置
            ReportManage rmB = iRMServ.getReportManageById(reportId);
            List<ReportQuery> rqL = iRMServ.getReportQueryDataListByReportId(reportId);
            List<ReportRegion> rrL = iRMServ.getReportRegionListByReportId(reportId);

            crDto = new CustomReportDto();
            crDto.setReportManage(rmB);
            crDto.setReportQueryList(rqL);
            crDto.setReportRegionList(rrL);

            //校验报表配置是否完整
            String errorInfo = checkReportConfig(crDto);
            if (StringUtils.isNotEmpty(errorInfo)) {
                throw new ReportException("读取自定义报表区域配置有误（" + errorInfo + "，报表ID：" + reportId + "）");
            }
        } catch (ReportException e) {
            throw e;
        } catch (Exception e) {
            throw new ReportException("读取自定义报表相关配置有误", e);
        }

        return crDto;
    }

    /**
     * 校验报表配置是否完整
     *
     * @param crDto 自定义报表数据传输对象
     */
    private String checkReportConfig(CustomReportDto crDto) {
        ReportManage rmB = crDto.getReportManage();
        if (rmB == null) {
            return "报表数据不存在";
        }
        List<ReportRegion> rrL = crDto.getReportRegionList();
        if (ObjUtils.isEmpty(rrL)) {
            return "报表区域数据不存在";
        }
        List<ReportRegion> rowL = new ArrayList<>(); //行区记录列表
        List<ReportRegion> colL = new ArrayList<>(); //列区记录列表
        List<ReportRegion> measureL = new ArrayList<>(); //度量记录列表

        for (ReportRegion rr : rrL) {
            switch (rr.getRegion().toLowerCase()) {
                case "row":
                    rowL.add(rr);
                    break;
                case "col":
                    colL.add(rr);
                    break;
                case "measure":
                    measureL.add(rr);
                    break;
                default:
                    break;
            }
        }

        //暂存备用
        crDto.setColL(colL);
        crDto.setRowL(rowL);
        crDto.setMeasureL(measureL);

        //判断必须配置的数据是否为空
        if (ObjUtils.isEmpty(colL)) {
            return "报表列区记录不能为空";
        }
        if (ObjUtils.isEmpty(measureL)) {
            return "报表度量记录不能为空";
        }

        return "";
    }


    /**
     * 报表统计
     *
     * @param ids    绑定的数据源（数据来源）
     * @param crDto  自定义报表数据传输对象
     * @param idsOut 自定义报表查询数据源（数据出口）
     */
    private void reportStatistic(IDataSource ids, CustomReportDto crDto, TDSReport idsOut) throws ReportException {

        //统计值 Map，key：行区_列区_Σ度量组，value：[值1,值2,...]
        Map<String, ReportValue> valueMap = new HashMap<>();
        //行区动态记录列表
        List<List<ReportRegionValue>> rowELList = new ArrayList<>();
        //列区动态记录列表
        List<List<ReportRegionValue>> colELList = new ArrayList<>();

        List<ReportRegion> rowL = crDto.getRowL(); //行区记录列表
        List<ReportRegion> colL = crDto.getColL(); //列区记录列表
        List<ReportRegion> measureL = crDto.getMeasureL(); //度量记录列表
      
        
        //遍历数据集制作备用数据结构
        for (int i = 0; i < ids.getRowCount(); i++) {
            TRow tr = ids.get(i);
            //填充统计值 Map
            fillStatisticsDataStructure(tr, rowL, colL, measureL, valueMap, rowELList, colELList);
        }

        //整理行标题
        rowELList = rowELList.stream().distinct() //生成流并去重
                .sorted(this::compareReportRegionValueList) //行标题的每一行之间排序
                .collect(Collectors.toList());

        //整理列标题
        colELList = colELList.stream().distinct() //生成流并去重
                .sorted(this::compareReportRegionValueList) //列标题的每一列之间排序
                .collect(Collectors.toList());

        //计算统计值
        makeResultDataList(idsOut, rowELList, colELList, valueMap);

        //制作列表头
        makeResultTitleList(idsOut, rowL, measureL, colELList);
        
        if(idsOut.isEchartsMode()) {//分析图模式,根据返回结果生成分析图数据
            ReportRegion xAxis = null;//x轴列
            List<String> legendList = new ArrayList<String>();
            List<String> xAxisList = new ArrayList<String>();
        	if(StringUtils.isNotEmpty(rowL)) {
        		for(ReportRegion temp:rowL) {
        			if(temp.getAnalysisField()!=null && temp.getAnalysisField().intValue()==1) {//用于分析图
    					xAxis = temp;
    					break;
    				}
        		}
        		if(xAxis==null) {//没有设置某个列作为分析图的x轴列数据
        			xAxis = rowL.get(0);//默认取第一个
        		}
        	}
        	if(xAxis!=null) {//有X列
	        	if(idsOut.getRowCount()>0) {
	        		for (int i = 0,j=idsOut.getRowCount(); i < j; i++) {
	        			TRow tr = idsOut.get(i);
	    	            String xAxisStr = tr.getString(xAxis.getBindParam());
	    	            if(xAxisStr==null) {
	    	            	xAxisStr="";
	    	            }
	    	            if(!xAxisList.contains(xAxisStr)) {
	    	            	xAxisList.add(xAxisStr);//xAxis: {data: ['2012', '2013', '2014', '2015']}  
	    	            }
	    	        }
	        	}
	        	if(StringUtils.isNotEmpty(measureL)) {
	          	  for(ReportRegion temp:measureL) {//生成图例
	          		  legendList.add(temp.getBindParam());//legend: {data: ['Forest', 'Steppe', 'Desert', 'Wetland']}
	          	  }
	          	}
//	        	List<ReportEchartsVo> indexList = idsOut.getEchartsData();
	        	boolean isRowModel = true;//true行模式 false列模式
	        	if(StringUtils.isNotEmpty(colL)) {
	    		  for (ReportRegion temp : colL) { //列区配置
		              if (temp.getParamType() == 2) { //Σ度量组
		            	  isRowModel = false;
		              }
	    		  }
	        	}
	        	List<String> rowList = rowL.stream().map(item -> item.getBindParamAlias()).collect(Collectors.toList());//生成行列表
	        	if(isRowModel) {//行模式 	
	        		for(TOutPara temp:idsOut.getOutParaList()) {
	        			if(!rowList.contains(temp.getAlias())) {//去掉行区表头，只获取指标
	    	        		HashSet<String> dataKeyList = new HashSet<String>();//防止数据重复（这正常不会重复，但是如果选择错了（选不唯一的指标列），就可能重复，需要去除重复
	        				HashMap<String,ReportEchartsDataVo> legendMap = new HashMap<String,ReportEchartsDataVo>();
	    	        		ReportEchartsVo bean = this.createInedxBean(temp,temp.getName(), legendList, xAxisList, legendMap);//生成指标
	        				idsOut.getEchartsData().add(bean);//添加到返回列表
	        				for (int i = 0,j=idsOut.getRowCount(); i < j; i++) {
	    	        			TRow tr = idsOut.get(i);
	    	        			String legendValue = tr.getString(ReportConstant.REPORT_CUSTOM_COLNAME_MEASURE);//度量
	    	        			String key = tr.getString(xAxis.getBindParamAlias())+"_"+legendValue;//x列+度量
	    	        			if(!dataKeyList.contains(key)) {
	    	        				dataKeyList.add(key);
	    	        				ReportEchartsDataVo dataBean = legendMap.get(legendValue);//查找度量
	    	        				if(dataBean!=null) {
	    	        					dataBean.getData().add(tr.getDouble(temp.getAlias()));
	    	        				}
	    	        			}
	        				}	   
//	        				System.out.println(JSON.toJSONString(bean));
	        			}	
	        		}
	        	}else {//列模式
	        		HashMap<String,ReportEchartsVo> indexMap = new HashMap<String,ReportEchartsVo>();
	        		HashMap<String,HashMap<String,ReportEchartsDataVo>> indexLegendMap = new HashMap<String,HashMap<String,ReportEchartsDataVo>>();
	        		for(TOutPara temp:idsOut.getOutParaList()) {
	        			if(!rowList.contains(temp.getAlias())) {//去掉行区表头，只获取指标
	        				String[] key = getIndex(legendList,temp.getName());//获取指标和度量
	        				if(key!=null) {//获取到了指标和度量
	        					ReportEchartsVo bean  = indexMap.get(key[0]);
	        					HashMap<String,ReportEchartsDataVo> legendMap = indexLegendMap.get(key[0]);
	        					if(bean==null) {//还没有生成过数据
	    	        				legendMap = new HashMap<String,ReportEchartsDataVo>();
	    	    	        		bean = this.createInedxBean(temp,key[0],legendList, xAxisList, legendMap);//生成指标
	    	    	    			idsOut.getEchartsData().add(bean);//添加到返回列表
	    	    	    			indexMap.put(key[0], bean);
	    	    	    			indexLegendMap.put(key[0], legendMap);
	        					}
	        					for (int i = 0,j=idsOut.getRowCount(); i < j; i++) {
		    	        			TRow tr = idsOut.get(i);
		    	        			ReportEchartsDataVo dataBean = legendMap.get(key[1]);//查找度量
	    	        				if(dataBean!=null) {
	    	        					dataBean.getData().add(tr.getDouble(temp.getAlias()));
	    	        				}
		        				}	   
	        					
	        				}
	        			}	
	        		}
//	        		for(ReportEchartsVo bean:idsOut.getEchartsData()) {
//	        			System.out.println(JSON.toJSONString(bean));
//	        		}
	        	}
        	}
       }
    }
    /**
         * 获取指标和度量
     * @category 获取指标和度量
     * <AUTHOR> 
     * @param legendList 度量列表
     * @param colName 字段名
     * @return String[] [0]=指标名称 [1]=度量
     */
    private String[] getIndex(List<String> legendList,String colName) {
    	String[] result = null;
    	for(String temp:legendList) {
    		int index = colName.lastIndexOf("_"+temp);
    		if(index>0) {//这里不能等于0,否则无法获取指标
    			String indexName=colName.substring(0,index);
    			if(indexName.length()==index) {//判断是不是截取到了最尾巴的度量
    				result = new String[]{indexName,temp};
    				break;
    			}
    		}
    	}
    	return result;
    }
    /**
         * 创建指标分析数据
     * @category 创建指标分析数据
     * <AUTHOR> 
     * @param outBean
     * @param analysisName
     * @param legendList
     * @param xAxisList
     * @param legendMap
     * @return
     */
    private ReportEchartsVo createInedxBean(TOutPara outBean,String analysisName,List<String> legendList,List<String> xAxisList,HashMap<String,ReportEchartsDataVo> legendMap) {
    	ReportEchartsVo bean = new ReportEchartsVo();
		bean.setAnalysisName(analysisName);//分析内容
		bean.setLegend(legendList);//图例
		for(String tempLegend:legendList) {
			ReportEchartsDataVo legendBean = new ReportEchartsDataVo();
			legendBean.setName(tempLegend);
			legendMap.put(tempLegend, legendBean);
			bean.getSeries().add(legendBean);
		}
		bean.setXaxis(xAxisList);//x轴数据
		return bean;
    }
    /**
     * 制作列表头
     *
     * @param idsOut    输出数据源
     * @param rowL      行区配置
     * @param measureL  度量配置
     * @param colELList 列区标题列表
     */
    private void makeResultTitleList(IDataSource idsOut, List<ReportRegion> rowL, List<ReportRegion> measureL, List<List<ReportRegionValue>> colELList) {
        List<TOutPara> tOPL = new ArrayList<>();

        int colId = 0;
        List<String> spanFieldL = new ArrayList<>(); //行标题合并指定字段列表（指定字段值相同时才会合并，保障子单元格合并不会跨父单元格）
        boolean isRowMeasure = false; //Σ度量组 是否在行区
        IDataSource.DataType measureDataType = IDataSource.DataType.tdsDouble; //Σ度量组 如果在行区，列区所有输出参数的数据类型取度量值的数据类型（如果多行度量值数据类型不一致，取 tdsString）

        for (ReportRegion rR : rowL) { //行区配置
            String colName; //列名
            String colAlias; //列别名
            if (rR.getParamType() == 2) { //Σ度量组
                colName = ReportConstant.REPORT_CUSTOM_COLNAME_MEASURE;
                colAlias = ReportConstant.REPORT_CUSTOM_COLALIAS_MEASURE;
                isRowMeasure = true;
            } else { //数据源输出字段
                colName = rR.getBindParam();
                colAlias = rR.getBindParamAlias();
            }
            //填充输出参数
            TOutPara tP = new TOutPara(idsOut);
            tP.setID(colId);
            tP.setAlias(colAlias);
            tP.setName(colName);
            tP.setAlign(correctAlign(rR.getColAlign())); //对齐方式
            tP.setWidth(correctWidth(rR.getColWidth())); //列宽
            tP.setVisible(true);
            if (rR.getColFixed() != null && rR.getColFixed() == 1) {
                tP.setFixed("left");
            }

            if (rR.getDatatype().equals(TdsTools.PARAMTYPE_NUMBER)) { //数值型
                tP.setDataType(IDataSource.DataType.tdsDouble);
            } else { //字符型
                tP.setDataType(IDataSource.DataType.tdsString);
            }

            //指定前方行区配置字段进行合并（防止跨父类合并单元格）
            tP.setIsSpan(true);
            tP.setSpanType(2); //按照指定字段相同值合并
            if (spanFieldL.size() > 0) {
                tP.setSpanScript(String.join(",", spanFieldL)); //合并脚本格式：多个指定字段（输出参数别名）使用逗号分隔，例如：field1,field2
            }
            spanFieldL.add(colAlias); //存储当前行标题参数别名（用于子标题指定输出字段合并）

            tOPL.add(tP);

            colId++;
        }

        // 如果「Σ度量组」在行区，有几个度量，就取几行数据，如果数据类型混合（既有数值，又有字符串，则所有列区输出参数的数据类型为 tdsString，否则为 tdsDouble）
        // 如果「Σ度量组」在列区，取一行数据即可，每个列区输出参数数据类型与每个度量的数据类型一致
        TRow tR = null;
        List<TRow> tRows = idsOut.getDataStore().getTRows();
        if (!tRows.isEmpty()) {
            if (isRowMeasure) { //Σ度量组 在行区
                for (int i = 0; i < measureL.size(); i++) {
                    TRow tr = tRows.get(i);
                    Object v = tr.get(colId);

                    if (!(v instanceof BigDecimal)) { //非数值型
                        measureDataType = IDataSource.DataType.tdsString;
                        break;
                    }
                }
            } else { //Σ度量组 在列区
                tR = tRows.get(0);
            }
        }

        for (List<ReportRegionValue> cRRVL : colELList) { //列区动态数据
            List<String> colNameEL = new ArrayList<>();
            String colAlias;
            String colName;

            String align = "";
            Integer width = 50;
            for (ReportRegionValue rRV : cRRVL) {
                colNameEL.add(rRV.getValue().toString());
                //获取列数据的宽度与对齐方式（以最后一行的列数据为准，显示结构：前一行包含后一行）
                align = rRV.getColAlign();
                width = rRV.getColWidth();
            }
            colName = String.join("_", colNameEL);
            colAlias = "RPT" + MD5Utils.md5(colName); //建议别名尽量使用英文，避免因编码不一致产生中文乱码的问题
            if (StringUtils.isEmpty(colAlias)) {
                colAlias = colName;
            }

            //填充输出参数
            TOutPara tP = new TOutPara(idsOut);
            tP.setID(colId);
            tP.setAlias(colAlias);
            tP.setName(colName);
            tP.setAlign(correctAlign(align));
            tP.setWidth(correctWidth(width));
            tP.setVisible(true);

            if (isRowMeasure) { //Σ度量组 在行区
                tP.setDataType(measureDataType);
            } else { //Σ度量组 在列区
                if (tR != null) {
                    Object v = tR.get(colId);

                    if (v instanceof BigDecimal) { //数值型
                        tP.setDataType(IDataSource.DataType.tdsDouble);
                    } else { //字符型
                        tP.setDataType(IDataSource.DataType.tdsString);
                    }
                } else { //缺省：字符型
                    tP.setDataType(IDataSource.DataType.tdsString);
                }
            }

            tOPL.add(tP);

            colId++;
        }

        idsOut.setOutPara(tOPL);
    }

    /**
     * 校正对齐方式
     *
     * @param align 对齐方式
     * @return String
     */
    private String correctAlign(String align) {
        if (align == null) {
            return "center";
        } else {
            return align;
        }
    }

    /**
     * 校正宽度
     *
     * @param width 宽度
     * @return Integer
     */
    private Integer correctWidth(Integer width) {
        if (width == null || width < 50) {
            return 50;
        } else {
            return width;
        }
    }

    /**
     * 计算统计值，制作结果列表
     *
     * @param idsOut    输出数据源
     * @param rowELList 行区标题列表
     * @param colELList 列区标题列表
     * @param valueMap  值检索集合
     */
    private void makeResultDataList(IDataSource idsOut, List<List<ReportRegionValue>> rowELList, List<List<ReportRegionValue>> colELList, Map<String, ReportValue> valueMap) {
        List<TRow> dL = new ArrayList<>();

        if (rowELList.isEmpty() && !colELList.isEmpty()) { //无行标题记录
            TRow tR = new TRow(idsOut);

            //所有数据进行统计最终输出一行记录
            for (List<ReportRegionValue> cRRVL : colELList) {
                List<String> colAliasEL = new ArrayList<>();

                String datatype = ""; //度量数据类型

                for (ReportRegionValue rRV : cRRVL) {
                    colAliasEL.add(rRV.getValue().toString());

                    //获取度量数据类型
                    if (rRV.getIsMeaure()) {
                        datatype = rRV.getDatatype();
                    }
                }
                String colAlias = String.join("_", colAliasEL); //数据源输出参数别名

                //单元格统计值
                cellSummaryStatistics(valueMap, colAlias, tR, datatype);
            }
            dL.add(tR);
        } else {
            //按行标题动态数据，多行统计
            for (List<ReportRegionValue> rRRVL : rowELList) { //行区标题的每一行数据（方向：↓）
                TRow tR = new TRow(idsOut);
                List<String> rowAliasEL = new ArrayList<>();

                String datatype = ""; //度量数据类型

                for (ReportRegionValue rRV : rRRVL) { //行区标题一行中的每一个单元格（方向：→）
                    tR.addCol(rRV.getValue());
                    rowAliasEL.add(rRV.getValue().toString());

                    //获取度量数据类型
                    if (rRV.getIsMeaure()) {
                        datatype = rRV.getMeasureDatatype();
                    }
                }
                String rowAlias = String.join("_", rowAliasEL); //行动态数据下划线分隔，待组合查值集合的 Key

                //所有数据进行统计最终输出一行记录
                for (List<ReportRegionValue> cRRVL : colELList) { //列区标题的每一列数据（方向：→）
                    List<String> colAliasEL = new ArrayList<>();

                    for (ReportRegionValue cRV : cRRVL) { //列区标题的每一个单元格（方向：↓）
                        colAliasEL.add(cRV.getValue().toString());

                        //获取度量数据类型
                        if (cRV.getIsMeaure()) {
                            datatype = cRV.getMeasureDatatype();
                        }
                    }
                    String colAlias = String.join("_", colAliasEL); //数据源输出参数别名
                    String fullAlias = rowAlias + "_" + colAlias;

                    //单元格统计值
                    cellSummaryStatistics(valueMap, fullAlias, tR, datatype);
                }

                dL.add(tR);
            }
        }

        //向数据源注入数据集
        idsOut.getDataStore().setTRows(dL);
    }

    /**
     * 统计计算一个单元格的值
     *
     * @param valueMap 值缓存检索集合
     * @param key      检索键
     * @param tR       数据源输出的一行记录
     * @param datatype 度量数据类型
     */
    private void cellSummaryStatistics(Map<String, ReportValue> valueMap, String key, TRow tR, String datatype) {
        ReportValue rV = valueMap.get(key); //单元格值相关信息对象
        if (rV != null) {
            List<?> vL = rV.getValueList(); //单元格值列表（集合）
            ReportRegion m = rV.getMeasure(); //度量配置
            String sW = m.getStatisticWay(); //统计方式
            String dt = m.getDatatype(); //数据类型
            Integer scale = m.getColScale(); //保留小数位数

            Object v = summaryStatistics(vL, sW, dt, scale); //统计计算

            //最终值写入单元格
            //tR.set(colId, v);
            tR.addCol(v);
        } else {
            //行标题与列标题是从数据中统计出的动态数据，形成二维表时是交叉关系（全连接），每个交叉的单元格相当于交叉数据（可能在上游表中根本无此关系数据），此时给空字符串或0
            if (TdsTools.PARAMTYPE_NUMBER.equals(datatype)) {
                tR.addCol(BigDecimal.ZERO);
            } else {
                tR.addCol("");
            }
        }
    }

    /**
     * 汇总统计计算
     *
     * @param vL    值集合（待计算）
     * @param sW    统计方式 sum=合计值 max=最大值 min=最小值 avg=平均值 count=计数值
     * @param dt    数据类型 tdsString=字符型 tdsDouble=数值型
     * @param scale 保留小数位数
     * @return Object
     */
    @SuppressWarnings("unchecked")
    private Object summaryStatistics(List<?> vL, String sW, String dt, Integer scale) {
        Object v = null;

        if (sW == null) {
            sW = "";
        }

        switch (sW) {
            case "sum":
                if (TdsTools.PARAMTYPE_NUMBER.equals(dt)) {
                    v = ((List<BigDecimal>) vL).stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                } else if (TdsTools.PARAMTYPE_STRING.equals(dt)) {
                    v = BigDecimal.ZERO;
                }
                break;
            case "max":
                if (TdsTools.PARAMTYPE_NUMBER.equals(dt)) {
                    v = ((List<BigDecimal>) vL).stream().max(Comparator.comparing(v1 -> v1)).orElse(BigDecimal.ZERO);
                } else if (TdsTools.PARAMTYPE_STRING.equals(dt)) {
                    v = ((List<String>) vL).stream().max(Comparator.comparing(v1 -> v1)).orElse("");
                }
                break;
            case "min":
                if (TdsTools.PARAMTYPE_NUMBER.equals(dt)) {
                    v = ((List<BigDecimal>) vL).stream().min(Comparator.comparing(v1 -> v1)).orElse(BigDecimal.ZERO);
                } else if (TdsTools.PARAMTYPE_STRING.equals(dt)) {
                    v = ((List<String>) vL).stream().min(Comparator.comparing(v1 -> v1)).orElse("");
                }
                break;
            case "avg":
                if (TdsTools.PARAMTYPE_NUMBER.equals(dt)) {
                    v = ((List<BigDecimal>) vL).stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(vL.size()), 2, RoundingMode.HALF_UP);
                } else if (TdsTools.PARAMTYPE_STRING.equals(dt)) {
                    v = BigDecimal.ZERO;
                }
                break;
            case "count":
                v = new BigDecimal(vL.size());
                break;
            default:
                //无汇总方式取第一条
                if (vL.isEmpty()) {
                    if (TdsTools.PARAMTYPE_NUMBER.equals(dt)) {
                        v = BigDecimal.ZERO;
                    } else if (TdsTools.PARAMTYPE_STRING.equals(dt)) {
                        v = "";
                    }
                } else {
                    v = vL.get(0);
                }
                break;
        }

        //保留指定位小数
        if (v instanceof BigDecimal) {
            if (scale == null) {
                scale = 0;
            }
            v = ((BigDecimal) v).setScale(scale, RoundingMode.UP);
        }

        return v;
    }

    /**
     * 记录排序（按指定字段排序，如未指定则按名称排序）
     *
     * @param l1 当前记录
     * @param l2 下一条记录
     * @return int
     */
    private int compareReportRegionValueList(List<ReportRegionValue> l1, List<ReportRegionValue> l2) {
        int r = 0;

        //定义比较规则
        for (int i = 0; i < l1.size(); i++) {
            ReportRegionValue rRV1 = l1.get(i);
            ReportRegionValue rRV2 = l2.get(i);

            switch (rRV1.getOrderByWay()) {
                case "asc":
                    r = compareValue(rRV1.getValue(), rRV2.getValue(), rRV1.getDatatype());
                    break;
                case "desc":
                    r = compareValue(rRV2.getValue(), rRV1.getValue(), rRV1.getDatatype());
                    break;
                case "field_asc":
                    r = compareValue(rRV1.getSort(), rRV2.getSort(), rRV1.getOrderByDatatype());
                    break;
                case "field_desc":
                    r = compareValue(rRV2.getSort(), rRV1.getSort(), rRV1.getOrderByDatatype());
                    break;
                default:
                    break;
            }

            if (r != 0) {
                break;
            }
        }

        return r;
    }

    /**
     * 比较值大小
     *
     * @param sv1 原始值1
     * @param sv2 原始值2
     * @param dt  数据类型
     * @return int
     */
    private int compareValue(Object sv1, Object sv2, String dt) {
        int r;

        Object v1 = TdsTools.castColumnValue(dt, sv1);
        Object v2 = TdsTools.castColumnValue(dt, sv2);
        if (dt.equals(TdsTools.PARAMTYPE_NUMBER)) { //数值型
            r = ((BigDecimal) v1).compareTo((BigDecimal) v2);
        } else { //字符型
            //使用中文比较规则，否则中文字符排序有误
            r = TdsTools.stringCompare((String) v1, (String) v2);
        }

        return r;
    }

    /**
     * 填充统计相关临时数据结构
     *
     * @param tr       数据源单条记录
     * @param rowL     行区记录列表
     * @param colL     列区记录列表
     * @param measureL 度量记录列表
     */
    private void fillStatisticsDataStructure(TRow tr, List<ReportRegion> rowL, List<ReportRegion> colL, List<ReportRegion> measureL, Map<String, ReportValue> valueMap, List<List<ReportRegionValue>> rowELList, List<List<ReportRegionValue>> colELList) throws ReportException {
        for (int mI = 0; mI < measureL.size(); mI++) {
            ReportRegion measure = measureL.get(mI);

            List<String> kL = new ArrayList<>(); //统计值 Map Key 组合元素列表
            List<ReportRegionValue> rowEL = new ArrayList<>(); //行区元素列表
            List<ReportRegionValue> colEL = new ArrayList<>(); //列区元素列表

            //遍历行区绑定参数
            for (ReportRegion row : rowL) {
                Integer paramType = row.getParamType();
                String measureDataType = "";

                String prmAlias; //绑定数据源输出参数别名
                String kEl; //统计值 Map Key 组合元素（行标题）
                String align; //列对齐方式
                Integer width; //列宽

                if (paramType == 1) { //数据源输出参数
                    prmAlias = row.getBindParamAlias();
                    kEl = tr.getString(prmAlias); //使用数据源输出参数值
                    align = row.getColAlign();
                    width = row.getColWidth();
                } else if (paramType == 2) { //Σ度量组
                    kEl = measure.getBindParam(); //使用度量绑定数据源输出参数名称
                    measureDataType = measure.getDatatype(); //度量的数据类型
                    align = measure.getColAlign();
                    width = measure.getColWidth();
                } else {
                    throw new ReportException("报表统计参数类型有误（列区参数：" + row.getBindParam() + "，类型：" + paramType + "）");
                }

                kL.add(kEl);

                //制作行头的值
                ReportRegionValue rRV = makeReportRegionValue(row, kEl, tr, paramType, mI, measureDataType, align, width);
                rowEL.add(rRV);
            }
            //遍历列区绑定参数
            for (ReportRegion col : colL) {
                Integer paramType = col.getParamType();
                String measureDataType = "";

                String prmAlias; //绑定数据源输出参数别名
                String kEl; //统计值 Map Key 组合元素（列标题）
                String align; //列对齐方式
                Integer width; //列宽

                if (paramType == 1) { //数据源输出参数
                    prmAlias = col.getBindParamAlias();
                    kEl = tr.getString(prmAlias); //使用数据源输出参数值
                    align = col.getColAlign();
                    width = col.getColWidth();
                } else if (paramType == 2) { //Σ度量组
                    kEl = measure.getBindParam(); //使用度量绑定数据源输出参数名称
                    measureDataType = measure.getDatatype(); //度量的数据类型
                    align = measure.getColAlign();
                    width = measure.getColWidth();
                } else {
                    throw new ReportException("报表统计参数类型有误（行区参数：" + col.getBindParam() + "，类型：" + paramType + "）");
                }

                kL.add(kEl);

                //制作列头的值
                ReportRegionValue rRV = makeReportRegionValue(col, kEl, tr, paramType, mI, measureDataType, align, width);
                colEL.add(rRV);
            }

            //填充统计值 Map
            String key = String.join("_", kL); //统计值 Map Key
            Object value = tr.get(measure.getBindParamAlias()); //从输出集合中取出，注意数据类型（可能全都是 String 类型），需要按配置数据类型进行转换处理
            value = TdsTools.castColumnValue(measure.getDatatype(), value); //转换为有效值
            ReportValue rVB = valueMap.get(key);
            if (rVB == null) {
                rVB = new ReportValue();
                rVB.setMeasure(measure);
                List<Object> valueList = new ArrayList<>();
                rVB.setValueList(valueList);
                valueMap.put(key, rVB);
            }
            rVB.getValueList().add(value);

            //填充行区动态记录列表
            if (!rowEL.isEmpty()) {
                rowELList.add(rowEL);
            }
            if (!colEL.isEmpty()) {
                colELList.add(colEL);
            }
        }
    }

    /**
     * 制作行列区头部数据值
     *
     * @param rr              行列区配置
     * @param kEl             行列标题
     * @param tr              数据源输出集单条记录
     * @param pT              数据源输出参数 / Σ度量组
     * @param mI              度量配置记录的索引
     * @param measureDataType 度量的数据类型
     * @param align           列对齐方式
     * @param width           列宽
     * @return ReportRegionValue
     */
    private ReportRegionValue makeReportRegionValue(ReportRegion rr, String kEl, TRow tr, int pT, int mI, String measureDataType, String align, Integer width) {
        ReportRegionValue b = new ReportRegionValue();

        b.setValue(kEl); //值：外层已判断处理
        b.setColAlign(align);
        b.setColWidth(width);

        if (pT == 1) { //数据源输出参数
            b.setDatatype(rr.getDatatype()); //配置类型

            String orderByDatatype = rr.getOrderByDatatype();
            b.setOrderByDatatype(orderByDatatype);
            b.setOrderByWay(rr.getOrderByWay());
            String orderByFieldAlias = rr.getOrderByFieldAlias();
            if (StringUtils.isNotEmpty(orderByFieldAlias)) {
                //绑定排序字段值
                Object orderByFieldValue = tr.get(orderByFieldAlias); //数据源输出值
                Object effectiveValue = TdsTools.castColumnValue(orderByDatatype, orderByFieldValue); //根据数据类型转换有效值（处理NULL、转数值...）
                b.setSort(effectiveValue);
            }
            b.setIsMeaure(false);
        } else if (pT == 2) { //Σ度量组
            b.setDatatype(TdsTools.PARAMTYPE_STRING); //默认为字符串类型
            b.setOrderByWay("field_asc"); //按度量记录配置顺序
            b.setOrderByDatatype(TdsTools.PARAMTYPE_NUMBER);
            b.setSort(mI); //度量记录序号值，后续根据此值进行排序
            b.setIsMeaure(true);
            b.setMeasureDatatype(measureDataType); //度量值的数据类型（度量记录中配置的数据类型）
        }

        return b;
    }

    /**
     * 报表统计值
     */
    @Data
    private static class ReportValue {
        ReportRegion measure; //度量配置
        List<Object> valueList; //度量值列表
    }

    /**
     * 报表区域绑定参数动态值
     */
    @Data
    private static class ReportRegionValue {
        String orderByWay; //排序方式：asc=升序 desc=降序 field_asc=指定字段升序 field_desc=指定字段降序
        //按字段自身排序的相关属性
        String datatype; //单元格值的数据类型：tdsString=字符型 tdsDouble=数值型
        Object value; //数据源输出参数值（行列标题）
        //按其它字段排序的相关属性
        String orderByDatatype; //排序字段数据类型：tdsString=字符型 tdsDouble=数值型
        Object sort; //排序值
        Boolean isMeaure; //是否为度量
        String measureDatatype; //度量值的数据类型
        Integer colWidth; //列宽度
        String colAlign; //列对齐方式
    }

    /**
     * 缓存结果至 Redis
     *
     * @param tdsAlias   绑定数据源别名
     * @param queryConds 绑定数据源查询条件
     * @param idsOut     输出数据源
     * @param crDto      自定义报表数据传输对象
     */
    private void cacheReportResult(String tdsAlias, JSONArray queryConds, TDSReport idsOut, CustomReportDto crDto) {
        try {
            //缓存的 Key
            String key = getCacheKey(crDto.getReportManage().getId(), tdsAlias, queryConds);

            //获取结果数据
            List<TRow> rows = idsOut.getDataStore().getTRows();
            List<TOutPara> outParams = idsOut.getOutParaList();

            //转换输出参数列表（目的：去掉无用字段，节省空间，避免循环递归：TOutPara 内部嵌套了 ids 对象）
            JSONArray outParamsArr = new JSONArray();
            for (TOutPara tP : outParams) {
                JSONObject o = new JSONObject();

                o.put("iD", tP.getID());
                o.put("alias", tP.getAlias());
                o.put("name", tP.getName());
                o.put("align", tP.getAlign());
                o.put("width", tP.getWidth());
                o.put("visible", tP.getVisible());
                o.put("isSpan", tP.getIsSpan());
                o.put("spanType", tP.getSpanType());
                o.put("spanScript", tP.getSpanScript());
                o.put("dataType", tP.getDataType().toString());

                outParamsArr.add(o);
            }

            //整合结果数据
            JSONObject result = new JSONObject();
            result.put("rows", rows);
            result.put("outParams", outParamsArr);
            result.put("echartsData",idsOut.getEchartsData());
            //序列化
            String resultStr = JSONObject.toJSONString(result);

            //缓存结果至 Redis
            redis.set(key, resultStr);

            //设定过期时间
            long timeout = (long) (crDto.getReportManage().getCacheTime() * 3600);
            redis.expire(key, timeout, TimeUnit.SECONDS);

            log.info("「报表模块」「报表查询」：缓存报表结果完成（容量：{} KB / {} MB）", getStringSize(resultStr, "KB"), getStringSize(resultStr, "MB"));
        } catch (Exception e) {
            log.error("「报表模块」「报表查询」：缓存报表结果有误", e);
        }
    }

    /**
     * 清除缓存结果，避免关闭缓存并二次开启后，查询的是历史缓存数据（此时应根据新数据重新生成缓存）
     *
     * @param tdsAlias   绑定数据源别名
     * @param queryConds 绑定数据源查询条件
     * @param crDto      自定义报表数据传输对象
     */
    private void clearReportResult(String tdsAlias, JSONArray queryConds, CustomReportDto crDto) {
        try {
            //缓存的 Key
            String key = getCacheKey(crDto.getReportManage().getId(), tdsAlias, queryConds);

            if (redis.hasKey(key)) {
                redis.delete(key);
            }
        } catch (Exception e) {
            log.error("「报表模块」「报表查询」：清理报表缓存有误", e);
        }
    }

    /**
     * 获取报表结果缓存 Key
     *
     * @param reportId   报表id
     * @param tdsAlias   绑定数据源别名
     * @param queryConds 绑定数据源查询条件
     * @return String
     */
    private String getCacheKey(String reportId, String tdsAlias, JSONArray queryConds) {
        String queryCondsStr = JSONArray.toJSONString(queryConds);
        return ReportConstant.REPORT_MODULE + ":" + reportId + ":" + MD5Utils.md5(tdsAlias + "::" + queryCondsStr);
    }

    /**
     * 获取字符串容量大小
     *
     * @param s    字符串
     * @param type B / KB / MB /GB
     * @return double
     */
    private double getStringSize(String s, String type) {
        double size = 0;

        if (StringUtils.isNotEmpty(s)) {
            if (type == null) {
                type = "";
            }
            size = s.getBytes().length;

            switch (type.toUpperCase()) {
                case "B":
                    //size = s.getBytes().length
                    break;
                case "KB":
                    size /= 1024D;
                    break;
                case "MB":
                    size /= 1024D * 1024;
                    break;
                case "GB":
                    size /= 1024D * 1024 * 1024;
                    break;
                default: //B
                    break;
            }
        }

        return size;
    }

    /**
     * 获取缓存结果
     *
     * @param tdsAlias   绑定数据源别名
     * @param queryConds 绑定数据源查询条件
     * @param idsOut     输出数据源
     * @param crDto      自定义报表数据传输对象
     * @return boolean
     */
    private boolean getCacheResult(String tdsAlias, JSONArray queryConds, TDSReport idsOut, CustomReportDto crDto) {
        boolean hasCacheResult = false;

        try {
            //缓存的 Key
            String key = getCacheKey(crDto.getReportManage().getId(), tdsAlias, queryConds);

            //获取 Redis 缓存数据
            if (redis.hasKey(key)) {
                //获取缓存数据
                String resultStr = redis.getString(key);
                JSONObject result = JSONObject.parseObject(resultStr);

                //有缓存结果，使用缓存数据作为结果返回
                useCacheResult(result, idsOut);

                hasCacheResult = true;
            }
        } catch (Exception e) {
            log.error("「报表模块」「报表查询」：获取报表缓存数据有误", e);
        }

        return hasCacheResult;
    }

    /**
     * 使用缓存数据作为结果返回
     *
     * @param result 结果缓存数据
     * @param idsOut 自定义报表查询数据源（数据出口）
     */
    private void useCacheResult(JSONObject result, TDSReport idsOut) {
        JSONArray rowsArr = result.getJSONArray("rows");
        JSONArray outParamsArr = result.getJSONArray("outParams");

        List<TRow> rows = new ArrayList<>();
        List<TOutPara> outParams = new ArrayList<>();

        // 遍历数据，重新制作 TRow、TOutPara 对象列表
        for (int i = 0; i < rowsArr.size(); i++) {
            JSONObject o = rowsArr.getJSONObject(i);
            TRow row = new TRow(idsOut);

            //注入列值
            JSONArray values = o.getJSONArray("values");
            for (Object value : values) {
                row.addCol(value);
            }

            rows.add(row);
        }

        for (int i = 0; i < outParamsArr.size(); i++) {
            JSONObject o = outParamsArr.getJSONObject(i);

            TOutPara tP = new TOutPara(idsOut);
            tP.setID(o.getInteger("iD"));
            tP.setAlias(o.getString("alias"));
            tP.setName(o.getString("name"));
            tP.setAlign(o.getString("align"));
            tP.setWidth(o.getInteger("width")); //列宽
            tP.setVisible(o.getBoolean("visible"));

            if (TdsTools.PARAMTYPE_NUMBER.equals(o.getString("dataType"))) { //数值型
                tP.setDataType(IDataSource.DataType.tdsDouble);
            } else { //字符型
                tP.setDataType(IDataSource.DataType.tdsString);
            }

            //指定前方行区配置字段进行合并（防止跨父类合并单元格）
            tP.setIsSpan(o.getBoolean("isSpan"));
            tP.setSpanType(o.getInteger("spanType")); //按照指定字段相同值合并
            tP.setSpanScript(o.getString("spanScript")); //合并脚本格式：多个指定字段（输出参数别名）使用逗号分隔，例如：field1,field2

            outParams.add(tP);
        }

        //向数据源注入数据集
        idsOut.getDataStore().setTRows(rows);
        //向数据源注入输出参数集
        idsOut.setOutPara(outParams);
        
        if(result.containsKey("echartsData")) {//有分析图
        	JSONArray echartsData = result.getJSONArray("echartsData");
        	List<ReportEchartsVo> echartsDataList = new ArrayList<ReportEchartsVo>();
        	for (int i = 0,j=echartsData.size(); i <j ; i++) {		 
        		JSONObject o = echartsData.getJSONObject(i);
        		List<ReportEchartsDataVo> seriesList = new ArrayList<ReportEchartsDataVo>();
        		if(o.containsKey("series")) {
        			JSONArray series = o.getJSONArray("series");
        			o.remove("series");
        			for (int m = 0,n=series.size(); m <n ; m++) {
        				JSONObject s = series.getJSONObject(m);
        				ReportEchartsDataVo dataVo = new ReportEchartsDataVo();
        				ObjUtils.copy(dataVo, s);
        				seriesList.add(dataVo);
        			}
        		}
                ReportEchartsVo bean = new ReportEchartsVo();
                ObjUtils.copy(bean, o);
                bean.setSeries(seriesList);
                echartsDataList.add(bean);
            }
        	idsOut.setEchartsData(echartsDataList);      
        }

        log.info("「报表模块」「报表查询」：使用缓存数据作为统计结果");
    }
}
