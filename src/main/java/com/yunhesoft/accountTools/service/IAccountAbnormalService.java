package com.yunhesoft.accountTools.service;

import java.util.List;

import com.yunhesoft.accountTools.entity.dto.AbnormalParam;
import com.yunhesoft.accountTools.entity.vo.AbnormalDataVo;
import com.yunhesoft.accountTools.entity.vo.AbnormalObj;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;

public interface IAccountAbnormalService {
	 /**
	 * @category 获取异常按日期、核算对象、班次统计数据
	 * 
	 * @param dto
	 * @return
	 */
	public AbnormalObj getAabnormalTable(AbnormalParam param);
	
	/**
	 * @category 根据日期、核算对象、班次获取异常列表数据
	 * 
	 * @param dto
	 * @return
	 */
	public List<AbnormalDataVo> getAabnormalList(AbnormalParam param);
	
	/**
	 * @category 统计保存异常信息
	 * @param param
	 */
	public void countAabnormalInfo(AbnormalParam param);
	
	/**
	 * @category 获取核算对象一段时间的班次信息
	 * @param unitCode
	 * @param ksrq
	 * @param jzrq
	 * @return
	 */
	public List<ShiftForeignVo> getUnitRqShiftList(String unitCode, String ksrq, String jzrq);
}
