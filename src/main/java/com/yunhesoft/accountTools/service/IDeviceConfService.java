package com.yunhesoft.accountTools.service;


import java.util.List;

import com.yunhesoft.accountTools.entity.dto.DeviceConfQueryDto;
import com.yunhesoft.accountTools.entity.dto.DeviceConfSaveDto;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormMeter;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormMeterVo;


/**
 *	设备配置相关服务接口
 * <AUTHOR>
 * @date 2023-12-10
 */
public interface IDeviceConfService {
	
	/**
	 *	获取设备配置（单表）
	 * @param queryDto
	 * @return
	 */
	public List<TdsAccountFormMeter> getTdsAccountFormMeterList(DeviceConfQueryDto queryDto);
	
	/**
	 *	获取设备配置（附加其他属性）
	 * @param queryDto
	 * @return
	 */
	public List<TdsAccountFormMeterVo> loadDataTdsAccountFormMeter(DeviceConfQueryDto queryDto);

	/**
	 *	保存设备配置数据
	 * @param saveDto
	 * @return
	 */
	public String saveTdsAccountFormMeterData(DeviceConfSaveDto saveDto);

	/**
	 *	保存设备配置数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataTdsAccountFormMeter(List<TdsAccountFormMeter> addList, List<TdsAccountFormMeter> updList, List<TdsAccountFormMeter> delList);
	
}
