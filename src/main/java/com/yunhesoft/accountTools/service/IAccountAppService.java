package com.yunhesoft.accountTools.service;


import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.accountTools.entity.dto.AccountParam;
import com.yunhesoft.accountTools.entity.vo.AccountAppVo;
import com.yunhesoft.system.tds.entity.po.TdsAccountMarkinfo;
import com.yunhesoft.system.tools.formulaParam.entity.vo.TdsFormulaTreeVo;

public interface IAccountAppService {
	 
	/**
	 * 获取活动相关表单及数据表数据
	 * 
	 * @param dto
	 * @return
	 */
	public List<AccountAppVo> getAccountManageList(AccountParam dto);
	
	/**
     * 获取表格行数据
     *
     * @param params
     * @return
     */
	public AccountAppVo getAccountRowList(JSONObject params);
	
	/**
     * 保存表格行数据
     *
     * @param saveData@return
     */
	public AccountParam saveAccountRowList(JSONObject saveData);
	
	/**
	 * @category 公式设置用台账变量列表
	 * @param dto
	 * @return
	 */
	public List<TdsFormulaTreeVo> getAccountVarList(AccountParam dto);
	
	/**
	 * @category 公式解析
	 * @param dto
	 * @return
	 */
	public String parseFormula(AccountParam dto);
	/**
	 * @category 判断解析
	 * @param dto
	 * @return
	 */
	public String judgeParse(AccountParam dto);

    boolean isInsertData(AccountParam dto);

    TdsAccountMarkinfo getReasonAnalysis(JSONObject param);

	Boolean saveReasonAnalysis(TdsAccountMarkinfo param);
}
