package com.yunhesoft.accountTools.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.accountTools.entity.dto.AbnormalCountParam;
import com.yunhesoft.accountTools.entity.dto.AbnormalParam;
import com.yunhesoft.accountTools.entity.po.AccountAbnormalData;
import com.yunhesoft.accountTools.entity.po.AccountAbnormalDataItem;
import com.yunhesoft.accountTools.entity.po.AccountAbnormalForm;
import com.yunhesoft.accountTools.entity.vo.AbnormalDataVo;
import com.yunhesoft.accountTools.entity.vo.AbnormalObj;
import com.yunhesoft.accountTools.entity.vo.AbnormalShiftVo;
import com.yunhesoft.accountTools.service.IAccountAbnormalService;
import com.yunhesoft.accountTools.service.IAccountToolsService;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountForm;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormManage;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormVo;
import com.yunhesoft.system.applyConf.service.IAccountFormService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.entity.po.TdataSource;
import com.yunhesoft.system.tds.entity.po.TdsAccountData;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.tmsf.form.entity.po.SFForm;
import com.yunhesoft.tmsf.form.service.IFormManageService;
import com.yunhesoft.tmsf.form.service.impl.FormTdsHandlerFactory;
import com.yunhesoft.tmsf.form.service.impl.FormTdsHandlerService;


@Service
public class AccountAbnormalService implements IAccountAbnormalService {
	
	@Autowired
	EntityService srv;
	
	@Autowired
	ICostService costService;
	
	@Autowired
	private IAccountToolsService accountServ; // 台账服务类
	
	@Autowired
	private IAccountFormService aformServ; // 自定义表单服务类
	
	@Autowired
	private UnitItemInfoService unititeminfoservice;
	
	@Autowired
	private IFormManageService formServ;
	@Autowired
	private FormTdsHandlerFactory handlerFactory;	//工厂类
	
	@Autowired
	private IDataSourceService tdsSrv;	//数据源
	
	@Autowired
	private IUnitMethodService unitMeth;
	
	@Autowired
	IShiftService shift;
	
	private Boolean showLog = false;

	/**
	 * @category 获取异常按日期、核算对象、班次统计数据
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public AbnormalObj getAabnormalTable(AbnormalParam param) {
		
		AbnormalObj robj = new AbnormalObj();
		List<Map<String, String>> titleList = new ArrayList<Map<String,String>>();
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		robj.setTitleList(titleList);
		robj.setDataList(dataList);
		
//		Map<String, String> tmap0 = new HashMap<String, String>();
//		tmap0.put("label", "核算对象");
//		tmap0.put("prop", "unit");
//		titleList.add(tmap0);
		
		String ksrq = param.getKsrq();
		String jzrq = param.getJzrq();//ksrq="2024-01-01";jzrq="2024-01-15";
		Date d1 = DateTimeUtils.parseDate(ksrq);
		Date d2 = DateTimeUtils.parseDate(jzrq);
		Date td = d1;
		for (int i = 0,il=15; i < il; i++) {
			if(DateTimeUtils.bjDate(d2, td)>=0) {
				String rq = DateTimeUtils.formatDate(td).substring(0,10);
				
				Map<String, String> tmap = new HashMap<String, String>();
				tmap.put("label", rq);
				tmap.put("prop", "A"+i);
				titleList.add(tmap);
				
				td = DateTimeUtils.doDate(td, 1);
			}else {
				break;
			}
		}
		
		String orgIds = param.getOrgCode();
//		SysUser user = SysUserHolder.getCurrentUser();//测试用
//		orgIds = "ZZVE4VIF0122ZJVHUD3938";//user.getOrgId();//测试用
		
		List<Costuint> listCostuint = new ArrayList<Costuint>();
		if(orgIds!=null && orgIds.length()>0) {
			listCostuint = costService.getCostuintListOrgId(orgIds);
		}
		
		List<String> ulist = new ArrayList<String>();
		if(StringUtils.isNotEmpty(listCostuint)) {
			for (Iterator iter = listCostuint.iterator(); iter.hasNext();) {
				Costuint costuint = (Costuint) iter.next();
				if(!new Integer(1).equals(costuint.getLedgerEntry())) {
					iter.remove();
				}else {
					ulist.add(costuint.getId());
				}
			}
		}
		
		if(StringUtils.isNotEmpty(listCostuint)) {
			
			//获取异常统计数据
			Map<String, Integer> amap = getAabnormalCount(ulist, ksrq, jzrq);
			
			//获取录入数据
			Map<String, Integer> dmap = getDataCount(ulist, ksrq, jzrq);
			
			//机构对应表单列表
			
			
			//循环核算对象、日期获取班次，并整理输出数据
			for (Costuint unit : listCostuint) {
				String unitCode = unit.getId();
				
				Map<String, Object> rowmap = new LinkedHashMap<String, Object>();
				
				//第一列核算对象名
				rowmap.put("unitId", unit.getId());
				rowmap.put("unitName", unit.getName());
				
				MethodQueryDto qdto = new MethodQueryDto();
				qdto.setUnitid(unit.getId());// 核算对象ID
				qdto.setObjType("org");
				List<Costunitoperator> listCzjg = unitMeth.getCostunitoperatorList(qdto);
				List<String> listOrg = new ArrayList<String>();
				if (StringUtils.isNotEmpty(listCzjg)) {
					for (Costunitoperator temp : listCzjg) {
						listOrg.add(temp.getObjid());
					}
				}
				
				List<ShiftForeignVo> listShift = new ArrayList<ShiftForeignVo>();
				if(StringUtils.isNotEmpty(listOrg)) {
					listShift = shift.getShiftDataByksrqjzrq(listOrg, ksrq, jzrq);
				}
				
				for (Map<String, String> tmap : titleList) {
					String rq = tmap.get("label");
					String col = tmap.get("prop");
					
//					List<ShiftForeignVo> shiftList = unititeminfoservice.getShiftList(unitCode, rq);//当日班次列表
					
//					if(StringUtils.isEmpty(shiftList)) {
//						continue;
//					}
					
					List<AbnormalShiftVo> cellList = new ArrayList<AbnormalShiftVo>();
					
//					int pos = 0;//当日班次的位置
					for (ShiftForeignVo shift : listShift) {
						if(!rq.equals(shift.getTbrq())) {
							continue;
						}
						//上下班时间加日期，暂未知相关函数，暂时根据时间对比判断 TODO
						String sbsj = shift.getSbsj();
						String xbsj = shift.getXbsj();
//						if(sbsj.length() == 5 && xbsj.length() == 5) {//xx:xx
//							String sbrq = rq, xbrq = rq;
//							if(pos == 0 && sbsj.compareTo(xbsj) > 0 && !"00:00".equals(sbsj)) {
//								sbrq = DateTimeUtils.format(DateTimeUtils.doDate(DateTimeUtils.parseDate(rq), -1), "yyyy-MM-dd");
//							}else if(pos == shiftList.size()-1 && sbsj.compareTo(xbsj) > 0) {
//								xbrq = DateTimeUtils.format(DateTimeUtils.doDate(DateTimeUtils.parseDate(rq), 1), "yyyy-MM-dd");
//							}
//							
//							sbsj = sbrq+" "+sbsj+":00";
//							xbsj = xbrq+" "+xbsj+":00";
//						}
						
						AbnormalShiftVo vo = new AbnormalShiftVo();
						vo.setRq(rq);
						vo.setUnitCode(unitCode);
						vo.setUnitName(unit.getName());
						vo.setShiftCode(shift.getShiftClassCode());
						vo.setShiftName(shift.getShiftClassName());
						vo.setSbsjstr(sbsj);
						vo.setXbsjstr(xbsj);
						vo.setOrgCode(shift.getOrgCode());
						vo.setOrgName(shift.getOrgName());
						
						String key = rq+"_"+unitCode+"_"+shift.getShiftClassCode();
						if(amap.containsKey(key)) {
							vo.setHaveMark(true);
						}else if(!dmap.containsKey(key)) {
							vo.setNoData(true);
						}
						cellList.add(vo);
						
//						pos++;
					}
					rowmap.put(col, cellList);
				}
				dataList.add(rowmap);//行数据
			}
			
		}
		ulist = null;
		return robj;
	}

	private Map<String, Integer> getAabnormalCount(List<String> ulist, String ksrq, String jzrq) {
		
		Map<String, Integer> rmap = new HashMap<String, Integer>();
		
		Where where = Where.create();
		where.eq(AccountAbnormalForm::getTmused, 1);
		where.ge(AccountAbnormalForm::getRq, ksrq);
		where.le(AccountAbnormalForm::getRq, jzrq);
		where.in(AccountAbnormalForm::getUnitCode, ulist.toArray());
		where.notEmpty(AccountAbnormalForm::getTotalNum);
		where.gt(AccountAbnormalForm::getTotalNum, 0);
		List<AccountAbnormalForm> flist = srv.queryData(AccountAbnormalForm.class, where, null, null);
		
		if(StringUtils.isNotEmpty(flist)) {
			for (AccountAbnormalForm form : flist) {
				String key = form.getRq()+"_"+form.getUnitCode()+"_"+form.getShiftCode();
				if(rmap.containsKey(key)) {
					rmap.put(key, rmap.get(key)+form.getTotalNum());
				}else {
					rmap.put(key, form.getTotalNum());
				}
			}
		}
		
		return rmap;
	}
	
	private Map<String, Integer> getDataCount(List<String> ulist, String ksrq, String jzrq) {
		
		Map<String, Integer> rmap = new HashMap<String, Integer>();
		
		Where where = Where.create();
		where.eq(TdsAccountData::getTmused, 1);
		where.ge(TdsAccountData::getRq, ksrq);
		where.le(TdsAccountData::getRq, jzrq);
		where.in(TdsAccountData::getUnitcode, ulist.toArray());
		List<TdsAccountData> flist = srv.queryData(TdsAccountData.class, where, null, null);
		
		if(StringUtils.isNotEmpty(flist)) {
			for (TdsAccountData form : flist) {
				String key = form.getRq()+"_"+form.getUnitcode()+"_"+form.getClsno();
				if(rmap.containsKey(key)) {
					rmap.put(key, rmap.get(key)+1);
				}else {
					rmap.put(key, 1);
				}
			}
		}
		
		return rmap;
	}

	/**
	 * @category 根据日期、核算对象、班次获取异常列表数据
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public List<AbnormalDataVo> getAabnormalList(AbnormalParam param) {
		List<AbnormalDataVo> rlist = new ArrayList<AbnormalDataVo>();
		
		String rq = param.getRq();
		String unitCode = param.getUnitCode();
		String bc = param.getShiftCode();
		Pagination<?> page = param.getPage();
		
		Where wheref = Where.create();
		wheref.eq(AccountAbnormalForm::getTmused, 1);
		wheref.eq(AccountAbnormalForm::getRq, rq);
		wheref.eq(AccountAbnormalForm::getUnitCode, unitCode);
		wheref.eq(AccountAbnormalForm::getShiftCode, bc);
		List<AccountAbnormalForm> flist = srv.queryData(AccountAbnormalForm.class, wheref, null, null);
		
		if(StringUtils.isEmpty(flist)) {
			return rlist;
		}
		
		List<String> fidList = new ArrayList<String>();
		for (AccountAbnormalForm aform : flist) {
			fidList.add(aform.getId());
		}
		
		Where where = Where.create();
//		where.eq(AccountAbnormalData::getTmused, 1);
		where.in(AccountAbnormalData::getAbFormId, fidList.toArray());
		Order order = Order.create();
		order.order(AccountAbnormalData::getFormCode);
		order.order(AccountAbnormalData::getTmsort);
		List<AccountAbnormalData> list = srv.queryData(AccountAbnormalData.class, where, order, page);
		
		if(StringUtils.isNotEmpty(list)) {
			
			Where wherei = Where.create();
			wherei.eq(AccountAbnormalDataItem::getTmused, 1);
			wherei.in(AccountAbnormalDataItem::getAbFormId, fidList.toArray());
			Order orderi = Order.create();
			orderi.order(AccountAbnormalDataItem::getAbTime);
			List<AccountAbnormalDataItem> ilist = srv.queryData(AccountAbnormalDataItem.class, wherei, orderi, null);
			
			Map<String, List<AccountAbnormalDataItem>> imap = new HashMap<String, List<AccountAbnormalDataItem>>();
			for (AccountAbnormalDataItem item : ilist) {
				if(imap.containsKey(item.getDataId())) {
					imap.get(item.getDataId()).add(item);
				}else {
					List<AccountAbnormalDataItem> itemList = new ArrayList<AccountAbnormalDataItem>();
					itemList.add(item);
					imap.put(item.getDataId(), itemList);
				}
			}
			
			for (AccountAbnormalData data : list) {
				AbnormalDataVo vo = ObjUtils.copyTo(data, AbnormalDataVo.class);
				List<AccountAbnormalDataItem> itemList = imap.get(vo.getId());
				vo.setItemList(itemList);
				
				if(StringUtils.isNotEmpty(itemList)) {
					StringBuffer sb = new StringBuffer();
					if("tag".equals(vo.getAbItemId())) {
						for (AccountAbnormalDataItem item : itemList) {
							String limitStr = item.getAbLimitstr() == null ? "" : item.getAbLimitstr(); 
							String markInfo = item.getAbMarkinfo() == null ? "" : item.getAbMarkinfo(); 
							sb.append("<BR/>"+item.getAbTimeStr()+"( "+item.getAbVal()+" 【"+limitStr+"】"+markInfo+")");
						}
						String info = sb.length() > 5 ? sb.substring(5) : "";
						vo.setInfo(info);
					}else if("confirm".equals(vo.getAbItemId())) {
						for (AccountAbnormalDataItem item : itemList) {
							sb.append("<BR/>"+item.getAbTimeStr());
						}
						String info = sb.length() > 5 ? sb.substring(5) : "";
						vo.setInfo(info);
					}
				}
				
				rlist.add(vo);
			}
		}
		
		return rlist;
	}

	/**
	 * @category 统计保存异常信息
	 * @param param
	 */
	@Override
	public void countAabnormalInfo(AbnormalParam param) {
		
		Integer days = param.getCountDays();
		if(days == null || days < 0) {
			days = 1;
		}
		if(showLog) {
			System.out.println("调度异常统计开始：统计范围"+days);
		}
		
		String currRqsjStr = DateTimeUtils.getNowDateTimeStr();
		String currRqStr = currRqsjStr.substring(0, 10);
		currRqStr = DateTimeUtils.formatDate(DateTimeUtils.doDate(DateTimeUtils.parseDate(currRqStr),1),"yyyy-MM-dd");//可能存在当前当班统计日期是下一天，日期向后+1
		
//		String upRqStr = DateTimeUtils.formatDate(DateTimeUtils.doDate(DateTimeUtils.getNowDate(),-1),"yyyy-MM-dd");
		
		String ksrq = DateTimeUtils.formatDate(DateTimeUtils.doDate(DateTimeUtils.parseDate(currRqsjStr.substring(0, 10)),-days),"yyyy-MM-dd");//按当前日期开始往前推天数
		String jzrq = currRqStr;//截止日期为当前日期
		//获取核算对象列表
		CostDto cdto = new CostDto();
		cdto.setIsLedgerEntry(true);
		List<Costuint> unitList = costService.getData(cdto);
		
		if(StringUtils.isNotEmpty(unitList)) {
			for (Iterator iter = unitList.iterator(); iter.hasNext();) {
				Costuint unit = (Costuint) iter.next();
				if(!new Integer(1).equals(unit.getLedgerEntry())) {
					iter.remove();
				}
			}
		}
		if(showLog) {
			System.out.println("调度异常统计：核算对象个数信息："+unitList==null?null:unitList.size());
		}

//		List<SFForm> flist = accountServ.getAccountFormList(null);
//		
//		for (Iterator iter = flist.iterator(); iter.hasNext();) {
//			SFForm sfForm = (SFForm) iter.next();
//			if(!tidList.contains(sfForm.getId())) {
//				iter.remove();
//			}
//		}
		
		
		//获取自定义表单
		Map<String, TdsAccountFormVo> cmap = new HashMap<String, TdsAccountFormVo>();
		List<TdsAccountFormVo> clist = aformServ.getFormManageList(null);
		for (TdsAccountFormVo obj : clist) {
			cmap.put(obj.getId(), obj);
		}
		if(showLog) {
			System.out.println("调度异常统计：自定义表单个数："+clist==null?null:clist.size());
		}
		
		//获取已保存数据信息（根据核算对象获取昨天和今天的全部数据，备用，避免核算对象中循环获取）
		List<String> rqbetween = new ArrayList<String>();
		rqbetween.add(ksrq);
		rqbetween.add(jzrq);
		
		Where whereData = Where.create();
		whereData.eq(TdsAccountData::getTmused, 1);
		whereData.between(TdsAccountData::getWriteday, rqbetween.toArray());
		List<TdsAccountData> dataList = srv.queryData(TdsAccountData.class, whereData, null, null);
		if(showLog) {
			System.out.println("调度异常统计：已用数据个数："+(dataList==null?null:dataList.size()) + "时间段："+ksrq+"..."+jzrq);
		}
		
		Map<String, List<TdsAccountData>> dataMap = new HashMap<String, List<TdsAccountData>>();
		Map<String, List<String>> sameFormList = new HashMap<String, List<String>>();//错误数据去重，所有标识均相同，用表单去重
		for (TdsAccountData obj : dataList) {//暂不考虑多核算对象的情况
			String formId = obj.getCustomformcode()==null?obj.getFormid():obj.getCustomformcode();
			
			String key = obj.getWriteday()+"_"+obj.getUnitcode()+"_"+obj.getClsno();//日期 核算对象 班次
			if(dataMap.containsKey(key)) {
				List<String> slist = sameFormList.get(key);
				if(!slist.contains(formId)) {
					dataMap.get(key).add(obj);
				}
			}else {
				List<TdsAccountData> tlist = new ArrayList<TdsAccountData>();
				tlist.add(obj);
				dataMap.put(key, tlist);
				
				List<String> slist = new ArrayList<String>();
				slist.add(formId);
				sameFormList.put(key, slist);
			}
		}
		
		//获取异常数据，根据最后更新时间判断是否需要重新统计
		Where whereaf = Where.create();
		whereaf.eq(AccountAbnormalForm::getTmused, 1);
		whereaf.between(AccountAbnormalForm::getRq, rqbetween.toArray());
		List<AccountAbnormalForm> aflist = srv.queryData(AccountAbnormalForm.class, whereaf, null, null);
		Map<String, AccountAbnormalForm> afMap = new HashMap<String, AccountAbnormalForm>();
		for (AccountAbnormalForm obj : aflist) {
//			String key = obj.getRq()+"_"+obj.getUnitCode()+"_"+obj.getShiftCode()+"_"+obj.getFormCode();//日期 核算对象 班次 表单
//			afMap.put(key, obj);
			afMap.put(obj.getFormDataId(), obj);
		}
		if(showLog) {
			System.out.println("调度异常统计：已有异常统计数据个数："+(aflist==null?null:aflist.size()) + "时间段："+ksrq+"..."+jzrq);
		}
		
		Date nd = DateTimeUtils.getNowDate();
		
		//循环核算对象，找每个核算对上一个班次的信息，对比更新时间，判断是否需要调用统计
		if(StringUtils.isNotEmpty(unitList))
		for (Costuint unit : unitList) {
			
			String rq = currRqStr;
			ShiftForeignVo currShift = unititeminfoservice.getShiftInfo(unit.getId(), currRqsjStr);//当前当班班次
			if(currShift == null) {
				if(showLog) {
					System.out.println("调度异常统计1未获取到当班班次信息："+unit.getId()+" "+unit.getName()+" "+currRqsjStr);
				}
				continue;
			}
			rq = currShift.getTbrq();
			String bc = currShift.getShiftClassCode();
			
			int bcpos = -1;
			List<ShiftForeignVo> shiftList = unititeminfoservice.getShiftList(unit.getId(), currRqStr);//当日班次列表
			for (int i = 0,il=shiftList.size(); i < il; i++) {
				ShiftForeignVo vo = shiftList.get(i);
				if(vo.getShiftClassCode().equals(bc)) {
					bcpos = i;
					break;
				}
			}
			
			if(bcpos == -1) {//倒班错误数据修正处理
				if(showLog) {
					System.out.println("调度异常统计2倒班错误数据修正处理："+unit.getId()+" "+unit.getName()+" "+currRqsjStr+" "+bc);
				}
				for (int i = 0,il=shiftList.size(); i < il; i++) {
					ShiftForeignVo vo = shiftList.get(i);
					if(currShift.getSbsj().substring(11,16).equals(vo.getSbsj()) && currShift.getXbsj().substring(11,16).equals(vo.getXbsj())) {
						bcpos = i;
						break;
					}
				}
			}
			
			if(bcpos == -1) {
				if(showLog) {
					System.out.println("调度异常统计3当班班次未在当日班次列表中："+unit.getId()+" "+unit.getName()+" "+currRqsjStr+" "+bc);
				}
				continue;
			}
			
			String calrq = rq;//计算日期
//			ShiftForeignVo calShift = null;
//			if(bcpos == 0) {//对应第一个班次，需要获取上一日(相对于rq)最后一个班次
//				String uprq = DateTimeUtils.formatDate(DateTimeUtils.doDate(DateTimeUtils.parseDate(rq),-1),"yyyy-MM-dd");
//				List<ShiftForeignVo> upshiftList = unititeminfoservice.getShiftList(unit.getId(), uprq);//当日班次列表
//				if(StringUtils.isNotEmpty(upshiftList)) {
//					calShift = shiftList.get(upshiftList.size()-1);
//				}
//				calrq = uprq;
//			}else {
//				calShift = shiftList.get(bcpos-1);
//			}
			
//			if(calShift!=null) {
			List<ShiftForeignVo> bcList = getUnitRqShiftList(unit.getId(), ksrq, jzrq);
			if(StringUtils.isEmpty(bcList)) {
				if(showLog) {
					System.out.println("调度异常统计未获取到时间段班次信息："+unit.getId()+" "+unit.getName()+" "+ksrq+" "+jzrq);
				}
				continue;
			}
			//循环日期段所有班次信息
			for (ShiftForeignVo shift : bcList) {
				if(shift.getTbrq().compareTo(rq) > 0) {
					if(showLog) {
						System.out.println("调度异常统计未到日期不进行统计："+unit.getId()+" "+unit.getName()+" "+shift.getSbsj());
					}
					continue;
				}else if(shift.getTbrq().equals(rq)) {
					if(shift.getSbsj().compareTo(currShift.getSbsj()) >= 0) {
						if(showLog) {
							System.out.println("调度异常统计同日未到班次不进行统计："+unit.getId()+" "+unit.getName()+" "+shift.getSbsj());
						}
						continue;
					}
				}
				
				String calbc = shift.getShiftClassCode();//计算班次
				
				//判断数据信息
				String key = shift.getTbrq()+"_"+unit.getId()+"_"+calbc;
				List<TdsAccountData> data = dataMap.get(key); //可能包含多个表单数据，目前只判断并计算有保存数据的内容，如果未进入过表单，暂不统计计算
				if(StringUtils.isEmpty(data)) {
					if(showLog) {
						System.out.println("调度异常统计4未有相关保存主数据："+unit.getId()+" "+unit.getName()+" "+key);
					}
					continue;
				}
				
				List<AccountAbnormalForm> addList = new ArrayList<AccountAbnormalForm>();
				List<AccountAbnormalForm> updList = new ArrayList<AccountAbnormalForm>();
				
				List<AccountAbnormalData> addDataList = new ArrayList<AccountAbnormalData>();
				List<AccountAbnormalDataItem> addDataItemList = new ArrayList<AccountAbnormalDataItem>();
				List<String> delFormDataIds = new ArrayList<String>();//要删除的ACCOUNT_ABNORMAL_DATA的AB_FORM_ID
				
				for (TdsAccountData obj : data) {
					
					Boolean calMark = true;
					
					String formCode = obj.getFormid();//表单模板标识
					String dataId = obj.getDataid();//表单数据ID
					
					//获取异常最后更新时间，如果未统计过异常，直接进行统计，如果统计过，进行时间对比
					Date abnormalLastTime = null;
					AccountAbnormalForm abform = afMap.get(dataId);
					if(abform!=null) {
						abnormalLastTime = abform.getCreateTime();
						if(abform.getUpdateTime()!=null) {
							abnormalLastTime = abform.getUpdateTime();
						}
					}
					
					if(abnormalLastTime!=null) {
						//获取表单最后更新时间//TODO 获取表单最后更新时间
						Date formLastTime = getFormLastSaveTime(formCode, dataId);
						
						if(formLastTime == null) {//表单没有时间，不进行异常数据更新
							calMark = false;
						}else if(DateTimeUtils.bjDate(abnormalLastTime, formLastTime) == 1) {
							calMark = false;
						}
					}
					
					if(calMark) {//进行统计
						//获取表单用台账数据源信息，未有相关函数，先用配置，后期优化  TODO
						List<String> tdsalist = null;
						try {
							tdsalist = getFormTdsAlias(formCode);
						} catch (Exception e) {
							if(showLog) {
								System.out.println("调度异常统计5表单获取数据源信息错误："+unit.getId()+" "+unit.getName()+" "+key+"..."+e.getMessage());
							}
							continue;
						}
						//应用数据源暂走配置
						if(StringUtils.isEmpty(tdsalist)) {
							if(showLog) {
								System.out.println("调度异常统计6未有相关台账数据源："+unit.getId()+" "+unit.getName()+" "+key);
							}
							continue;
						}else {
							if(showLog) {
								System.out.println("调度异常统计数据源信息："+unit.getId()+" "+unit.getName()+" "+key+"..."+tdsalist.toString());
							}
						}
						
						AbnormalCountParam paramObj = new AbnormalCountParam();
						paramObj.setFormCode(formCode);
						paramObj.setDataId(dataId);
						paramObj.setRq(obj.getRq());
						paramObj.setUnitCode(unit.getId());//暂时不支持多核算对象 obj.setUnitcode()
						paramObj.setShiftCode(obj.getClsno());
						paramObj.setSbsj(obj.getSbsjstr());
						paramObj.setXbsj(obj.getXbsjstr());
						paramObj.setAccountId(obj.getCustomformcode());
						paramObj.setTdsAliasList(tdsalist);
						
						List<AbnormalDataVo> ablist = new AbnormalCountTdsAccount().countData(paramObj);//函数调用，后期完善
						
						//判断是否有数据，无数据直接添加；有统计数据，删除原统计结果，添加新数据
						AccountAbnormalForm aaform = afMap.get(dataId);
						if(aaform == null) {
							String tmuid = TMUID.getUID();
							addNewForm(obj, ablist, addList, nd, tmuid);
							addNewFormDate(obj, ablist, addDataList, addDataItemList, tmuid);
						}else {
							updForm(aaform, obj, ablist, updList, delFormDataIds, nd);
							addNewFormDate(obj, ablist, addDataList, addDataItemList, aaform.getId());
						}
					}
				}
				
				if(StringUtils.isNotEmpty(addList)) {
					srv.insertBatch(addList, 50);
				}
				if(StringUtils.isNotEmpty(updList)) {
					srv.updateBatch(updList);
				}
				if(StringUtils.isNotEmpty(delFormDataIds)) {
					Where where1 = Where.create();
					where1.in(AccountAbnormalData::getAbFormId, delFormDataIds.toArray());
					List<AccountAbnormalData> list1 = srv.queryData(AccountAbnormalData.class, where1, null, null);
					Where where2 = Where.create();
					where2.in(AccountAbnormalDataItem::getAbFormId, delFormDataIds.toArray());
					List<AccountAbnormalDataItem> list2 = srv.queryData(AccountAbnormalDataItem.class, where2, null, null);
					if(StringUtils.isNotEmpty(list1)) {
						srv.deleteByIdBatch(list1);
					}
					if(StringUtils.isNotEmpty(list2)) {
						srv.deleteByIdBatch(list2);
					}
					
				}
				if(StringUtils.isNotEmpty(addDataList)) {
					srv.insertBatch(addDataList);
				}
				if(StringUtils.isNotEmpty(addDataItemList)) {
					srv.insertBatch(addDataItemList);
				}
			}
		}
		
		
	}

	private List<String> getFormTdsAlias(String formCode) {
		JSONArray arr = formServ.getFormComponentJsonArray(formCode, "tdsEditTable");//(formCode, "");
		if(arr == null || arr.size()==0) {
			return null;
		}
		List<String> rlist = new ArrayList<String>();
//		arr.getJSONObject(0).getJSONObject("options").getString("tdsAlias")
		for (int i = 0,il=arr.size(); i < il; i++) {
			JSONObject jobj = arr.getJSONObject(i);
			if(jobj!=null) {
				JSONObject options = jobj.getJSONObject("options");
				if(options!=null) {
					String tdsAlias = options.getString("tdsAlias");
					if (StringUtils.isNotEmpty(tdsAlias)) {
						rlist.add(tdsAlias);
					}
				}
			}
		}
//		for (String ds : arr) {
//			TdataSource tds = tdsSrv.getTDataSource(ds);
//			if("TDSAccount".equalsIgnoreCase(tds.getTdsclassName())) {
//				rlist.add(ds);
//			}
//		}
		
		return rlist;
	}

	private void addNewFormDate(TdsAccountData obj, List<AbnormalDataVo> ablist, List<AccountAbnormalData> addDataList,
			List<AccountAbnormalDataItem> addDataItemList, String tmuid) {
		for (AbnormalDataVo vo : ablist) {
			AccountAbnormalData data = ObjUtils.copyTo(vo, AccountAbnormalData.class);
			data.setId(TMUID.getUID());
			data.setAbFormId(tmuid);
			data.setUnitCode(obj.getUnitcode());
			data.setUnitName(obj.getUnitname());
			data.setShiftCode(obj.getClsno());
			data.setShiftName(obj.getClsname());
			data.setSbsjstr(obj.getSbsjstr());
			data.setXbsjstr(obj.getXbsjstr());
			data.setOrgCode(obj.getOrgcode());
			data.setOrgName(obj.getOrgname());
			data.setFormTplCode(obj.getFormid());
			data.setCustomCode(obj.getCustomformcode());
			data.setCustomName(obj.getCustomformname());
			data.setFormCode(StringUtils.isEmpty(obj.getCustomformcode())?obj.getFormid():obj.getCustomformcode());
			data.setFormName(StringUtils.isEmpty(obj.getCustomformcode())?obj.getFormname():obj.getCustomformname());
			data.setFormDataId(obj.getDataid());
			
			addDataList.add(data);
			
			List<AccountAbnormalDataItem> itemList = vo.getItemList();
			if(StringUtils.isNotEmpty(itemList)) {
				for (AccountAbnormalDataItem itemvo : itemList) {
					AccountAbnormalDataItem item = ObjUtils.copyTo(itemvo, AccountAbnormalDataItem.class);
					item.setId(TMUID.getUID());
					item.setTmused(1);
					item.setAbFormId(tmuid);
					item.setDataId(data.getId());
					
					addDataItemList.add(item);
				}
			}
		}
	}

	private void updForm(AccountAbnormalForm aaform, TdsAccountData obj, List<AbnormalDataVo> ablist, List<AccountAbnormalForm> updList,
			List<String> delFormDataIds, Date nd) {
		aaform.setLastCountTime(nd);
		aaform.setTotalNum(ablist.size());
		
		updList.add(aaform);
		
		delFormDataIds.add(aaform.getId());
	}

	private void addNewForm(TdsAccountData obj, List<AbnormalDataVo> ablist, List<AccountAbnormalForm> addList, Date nd, String tmuid) {
		AccountAbnormalForm aaform = new AccountAbnormalForm();
		aaform.setId(tmuid);
		aaform.setRq(obj.getRq());
		aaform.setUnitCode(obj.getUnitcode());
		aaform.setUnitName(obj.getUnitname());
		aaform.setShiftCode(obj.getClsno());
		aaform.setShiftName(obj.getClsname());
		aaform.setSbsjstr(obj.getSbsjstr());
		aaform.setXbsjstr(obj.getXbsjstr());
		aaform.setOrgCode(obj.getOrgcode());
		aaform.setOrgName(obj.getOrgname());
		aaform.setFormTplCode(obj.getFormid());
		aaform.setCustomCode(obj.getCustomformcode());
		aaform.setCustomName(obj.getCustomformname());
		aaform.setFormCode(StringUtils.isEmpty(obj.getCustomformcode())?obj.getFormid():obj.getCustomformcode());
		aaform.setFormName(StringUtils.isEmpty(obj.getCustomformcode())?obj.getFormname():obj.getCustomformname());
		aaform.setFormDataId(obj.getDataid());
		aaform.setLastCountTime(nd);
		aaform.setEndTime(null);//暂时不记录截止时间
		aaform.setTotalNum(ablist.size());
		aaform.setTmused(1);
		
		addList.add(aaform);
	}
	
	//获取表单最后保存时间函数，暂时用 TODO
	private Date getFormLastSaveTime(String formId, String dataId) {
		SFForm sfForm = formServ.queryFormInfoById(formId);
		if (sfForm == null) {
			return null;
		}
		String tdsAlias = sfForm.getTableId();
		TdataSource tds = StringUtils.isEmpty(tdsAlias) ? null : tdsSrv.getTDataSource(tdsAlias);
		
		Map<String, Object> valueMap = new HashMap<>();
		List<TdsinPara> tdsInPara = new ArrayList<TdsinPara>();
		if("TDSMongoDB".equalsIgnoreCase(tds.getTdsclassName())) {
			valueMap.put("_id", dataId);//formId+":0:"+dataId
			TdsinPara inp = new TdsinPara();
			inp.setParaAlias("_id");
			inp.setParaName("标识");
			tdsInPara.add(inp);
		}else {
			valueMap.put("id", dataId);//formId+":0:"+dataId
			TdsinPara inp = new TdsinPara();
			inp.setParaAlias("id");
			inp.setParaName("标识");
			tdsInPara.add(inp);
		}
		
		Date ld = null;
		try {
			FormTdsHandlerService handler = handlerFactory.getInstance(tds.getTdsclassName());
			List data = handler.getTmsfFormTableData(sfForm, tds, tdsInPara, valueMap, null, null);
			if (StringUtils.isNotEmpty(data)) {
				
				Map<String, Object> obj = (Map<String, Object>) data.get(0);//有id，一般只有一条数据
				Object ctime = obj.get("CREATE_TIME");
				if(ctime!=null) {
					if(ctime instanceof Date) {
						ld = (Date)ctime;
					}else if(ctime instanceof String) {
						ld = DateTimeUtils.parseDate(ctime);
					}
				}
				Object utime = obj.get("UPDATE_TIME");//SqlServer
				if(utime!=null) {
					if(utime instanceof Date) {
						ld = (Date)utime;
					}else if(utime instanceof String) {
						ld = DateTimeUtils.parseDate(utime);
					}
				}
				Object utime2 = obj.get("updateTime");//MongoDB
				if(utime2!=null) {
					if(utime2 instanceof Date) {
						ld = (Date)utime2;
					}else if(utime2 instanceof String) {
						ld = DateTimeUtils.parseDate(utime2);
					}
				}
			}
		} catch (Exception e) {
			System.out.println("调度异常统计_获取表单最后更新时间错误"+formId+" "+formId+" "+e.getMessage());
		}
		return ld;
	}
	/**
	 * @category 获取核算对象一段时间的班次信息
	 * @param unitCode
	 * @param ksrq
	 * @param jzrq
	 * @return
	 */
	@Override
	public List<ShiftForeignVo> getUnitRqShiftList(String unitCode, String ksrq, String jzrq) {
		MethodQueryDto qdto = new MethodQueryDto();
		qdto.setUnitid(unitCode);// 核算对象ID
		qdto.setObjType("org");
		List<Costunitoperator> listCzjg = unitMeth.getCostunitoperatorList(qdto);
		List<String> listOrg = new ArrayList<String>();
		if (StringUtils.isNotEmpty(listCzjg)) {
			for (Costunitoperator temp : listCzjg) {
				listOrg.add(temp.getObjid());
			}
		}
		
		List<ShiftForeignVo> listShift = new ArrayList<ShiftForeignVo>();
		if(StringUtils.isNotEmpty(listOrg)) {
			listShift = shift.getShiftDataByksrqjzrq(listOrg, ksrq, jzrq);
		}
		return listShift;
	}
	
	private Map<String, List<String>> getOrgFormMap() {
		Map<String, List<String>> rmap = new HashMap<String, List<String>>();
		//默认表单
		List<SFForm> flist = accountServ.getAccountFormList(null);
		
		//机构自定义台账
		Map<String, List<TdsAccountForm>> cmap = getOrgCustomFormMap();
		
		for (String key : cmap.keySet()) {
			List<TdsAccountForm> list = cmap.get(key);
			for (TdsAccountForm cform : list) {
				String formid = cform.getFormCode();
				
			}
		}
		
		
		return rmap;
	}
	
	/**
	 * @category 获取机构管理的自定义表单
	 * @return
	 */
	private Map<String, List<TdsAccountForm>> getOrgCustomFormMap() {
		Map<String, List<TdsAccountForm>> rmap = new HashMap<String, List<TdsAccountForm>>();
		
		Where where = Where.create();
        where.eq(TdsAccountFormManage::getTmused, 1);
        where.eq(TdsAccountFormManage::getModeType, 3);//只查机构
        where.eq(TdsAccountFormManage::getManageBound, 1);//查班组应用
        where.and().lb();								//有录入或补录权限
        where.eq(TdsAccountFormManage::getAddMark, 1);
        where.or();
        where.eq(TdsAccountFormManage::getManageMark, 1);
        where.rb();
        List<TdsAccountFormManage> mlist = srv.queryData(TdsAccountFormManage.class, where, null, null);
        
        Where wheref = Where.create();
		wheref.in(TdsAccountForm::getTmused, 1);
		wheref.in(TdsAccountForm::getTypeCode, 1);
		wheref.notEmpty(TdsAccountForm::getFormCode);
        List<TdsAccountForm> flist = srv.queryData(TdsAccountForm.class, wheref, null, null);
        Map<String, TdsAccountForm> fmap = new HashMap<String, TdsAccountForm>();
        for (TdsAccountForm form : flist) {
        	String u = form.getUnitCode() == null ? null : form.getUnitCode().replace("\"", "").replace("[", "").replace("]", "");
        	if(StringUtils.isEmpty(u)) {
        		continue;
        	}
        	fmap.put(form.getId(), form);
		}
        
        for (TdsAccountFormManage fm : mlist) {
        	TdsAccountForm form = null;
			String accountId = fm.getAccountid();
			if(fmap.containsKey(accountId)) {
				String orgcode = fm.getManageCode();//目前只有机构
				
				if(rmap.containsKey(orgcode)) {
					rmap.get(orgcode).add(form);
				}else {
					List<TdsAccountForm> tlist = new ArrayList<TdsAccountForm>();
					tlist.add(form);
					rmap.put(orgcode, tlist);
				}
			}
		}
        
        return rmap;
	}
}
