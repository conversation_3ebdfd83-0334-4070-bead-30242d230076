package com.yunhesoft.accountTools.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.LinkedListMultimap;
import com.google.common.collect.ListMultimap;
import com.yunhesoft.accountTools.entity.dto.*;
import com.yunhesoft.accountTools.entity.po.DigitalLedger;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerExcel;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerExcelArea;
import com.yunhesoft.accountTools.entity.vo.ExcelData;
import com.yunhesoft.accountTools.entity.vo.ExcelVo;
import com.yunhesoft.accountTools.entity.vo.PotVo;
import com.yunhesoft.accountTools.service.IAccountConfigService;
import com.yunhesoft.accountTools.service.IAccountExcelService;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;
import com.yunhesoft.system.tools.sysConfig.entity.SysConfig;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;
import com.yunhesoft.tmsf.form.entity.po.SFForm;
import com.yunhesoft.tmsf.form.service.IFormManageService;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.poi.ss.util.CellReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Log4j2
@Service
public class AccountExcelServiceImpl implements IAccountExcelService {

    @Autowired
    EntityService srv;

    @Autowired
    private IFilesInfoService fileSrv;

    @Autowired
    private IAccountConfigService acSrv; // 台账相关服务

    /**
     * Excel模型初始化接口
     * 用于保存Excel解析相关数据，区域信息、结构信息
     *
     * @param dto
     * @return
     */
    @Override
    public ExcelVo getExcelInfo(ExcelParam param) {
        String belongId = param.getModelId();
        String mode = param.getModeCode();

        DigitalLedgerExcel obj = getExcelLedgerInfo(belongId);

        if (obj != null) {

            String rulesFileId = obj.getRulesFileId();
            String strutsFileId = obj.getStructsFileId();
            String parseFileId = obj.getParseFileId();
            String queryFileId = obj.getQueryFileId();

            String rulesJson = getFileJsonStr(rulesFileId);
            String strutsJson = getFileJsonStr(strutsFileId);
            String parseJson = null;
            String queryJson = null;

            if ("parse".equalsIgnoreCase(mode)) {
                parseJson = getFileJsonStr(parseFileId);
                queryJson = getFileJsonStr(queryFileId);
            }
            if ("query".equalsIgnoreCase(mode)) {
                if (StringUtils.isNotEmpty(queryFileId)) {
                    queryJson = getFileJsonStr(queryFileId);
                } else {
                    parseJson = getFileJsonStr(parseFileId);
                }
            }

            ExcelVo vo = new ExcelVo();
            ExcelMode em = new ExcelMode();
            vo.setModelInfo(em);
            em.setId(obj.getBelongId());
            em.setName(obj.getShowName());
            em.setRules(rulesJson);
            em.setStructs(strutsJson);
            em.setQuerys(obj.getQuerysInfo());
            vo.setParseJson(parseJson);
            vo.setQueryJson(queryJson);

            return vo;
        } else {
            //解析功能目前挂在表单上，使用表单名称
            SFForm sf = srv.queryObjectById(SFForm.class, belongId);
            String name = sf == null ? "" : sf.getName();

            ExcelVo vo = new ExcelVo();
            ExcelMode em = new ExcelMode();
            em.setId(belongId);
            em.setName(name);
            em.setQuerys("");
            em.setRules("");
            em.setStructs("");
            vo.setModelInfo(em);
            vo.setParseJson("");
            vo.setQueryJson("");

            return vo;
        }
    }

    //获取Excel解析保存信息
    private DigitalLedgerExcel getExcelLedgerInfo(String belongId) {
        Where where = Where.create().eq(DigitalLedgerExcel::getBelongId, belongId);
        List<DigitalLedgerExcel> list = srv.queryData(DigitalLedgerExcel.class, where, null, null);
        if (StringUtils.isNotEmpty(list)) {
            DigitalLedgerExcel obj = list.get(0);
            return obj;
        }
        return null;
    }

    /**
     * Excel模型获取核算对象采集点分类树型
     * 用于Excel解析时，同步采集点时，指定采集点分类
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, List<PotVo>> getExcelUnitPotClass(ExcelParam param) {
        List<PotVo> rlist = new ArrayList<PotVo>();
        Map<String, List<PotVo>> robj = new HashMap<String, List<PotVo>>();
        robj.put("data", rlist);
        /*
         * 获取表单及区域信息，查表，找对应的核算对象信息
         */
        Boolean details = param.getDetails();
        String belongId = param.getModelId();
        ExcelArea area = param.getArea();
        String areaId = area.getTid();
        //查询是否已保存过数据
        Where where = Where.create().eq(DigitalLedgerExcelArea::getBelongId, belongId).eq(DigitalLedgerExcelArea::getAreaId, areaId);
        List<DigitalLedgerExcelArea> list = srv.queryData(DigitalLedgerExcelArea.class, where, null, null);
        if (StringUtils.isNotEmpty(list)) {
            DigitalLedgerExcelArea obj = list.get(0);
            //调用接口函数获取分类信息
//			obj.getId();//此id应该与模型id一致, 封装rlist即可 details
            ExcelParam dto = new ExcelParam();
            dto.setModelId(obj.getId());
            dto.setDetails(details);
            rlist.addAll(acSrv.getExcelSampleClassTreeList(dto));
        }

        return robj;
    }

    /**
     * Excel模型同步解析结果采集点
     * 用于Excel解析时，更新采集点，内部根据区域生成核算对象
     *
     * @param dto
     * @return
     */
    @Override
    public List<String> saveExcelUnitPot(ExcelParam param) {
        List<String> rlist = new ArrayList<String>();

        String belongId = param.getModelId();
        ExcelArea excelarea = param.getArea();
        String areaId = excelarea.getTid();
        String areaName = excelarea.getName();
        String pid = param.getPid();
        List<ExcelDot> results = param.getResults();

        DigitalLedgerExcel einfo = getExcelData(belongId);
        //根据参数生成区域
        DigitalLedgerExcelArea area = this.generaterExcelArea(param,einfo);
        if (area != null) {
            String markId = area.getId();
            //封装采集点信息，传入接口 TODO
            if (StringUtils.isNotEmpty(results)) {
//				List<ExcelDot> list = new ArrayList<ExcelDot>();
//				List<ExcelDot> rlist = 接口参数modelId modelName classId list<ExcelDot> -- area.getId() areaName pid results

                ExcelParam dto = new ExcelParam();
                dto.setModelId(markId);
                dto.setName(areaName + einfo.getShowName());
                dto.setPid(pid);
                dto.setResults(results);
                dto.setBelongId(belongId);
                ExcelParam rv = acSrv.saveExcelSampleDot(dto);
                if (rv != null) {
                    List<ExcelDot> tlist = rv.getResults();

                    for (ExcelDot dot : tlist) {
                        rlist.add(new Integer(1).equals(dot.getOpResult()) ? "insert" : "update");
                    }
                } else {
                    for (ExcelDot dot : results) {
                        rlist.add("error");
                    }
                }
            }
        }

        return rlist;
    }

    /**
     * 根据解析参数生成区域
     * <AUTHOR>
     * @date 2025/4/23
     * @params getModelId 字段实际是表单id
     * @return
     *
    */

    @Override
    public DigitalLedgerExcelArea generaterExcelArea(ExcelParam param) {
        DigitalLedgerExcel einfo = getExcelData(param.getModelId());
        return this.generaterExcelArea(param,einfo);
    }
    @Override
    public DigitalLedgerExcelArea generaterExcelArea(ExcelParam param, DigitalLedgerExcel einfo) {
        String belongId = param.getModelId();
        ExcelArea excelarea = param.getArea();
        String areaId = excelarea.getTid();
        String areaName = excelarea.getName();
        String pid = param.getPid();
        List<ExcelDot> results = param.getResults();
        DigitalLedgerExcelArea area = null;
        if (einfo == null) {//没有数据，进行保存
            einfo = new DigitalLedgerExcel();
            einfo.setId(TMUID.getUID());
            einfo.setBelongId(belongId);
            einfo.setShowName("表单");//临时名称
            einfo.setTmused(1);
            srv.insert(einfo);

            area = new DigitalLedgerExcelArea();
            area.setId(TMUID.getUID());
            area.setExcelId(einfo.getId());
            area.setBelongId(belongId);
            area.setAreaId(areaId);
            area.setAreaName(areaName + einfo.getShowName());
            area.setTmused(1);
//			area.setStructsInfo(JSONArray.toJSONString(param.getResults()));//待研究

            srv.insert(area);

        } else {//有数据，进行更新

            area = getExcelArea(belongId, areaId);
            if (area != null) {
                area.setAreaName(areaName + einfo.getShowName());
//				area.setStructsInfo(JSONArray.toJSONString(param.getResults()));//待研究
                srv.updateById(area);
            } else {
                area = new DigitalLedgerExcelArea();
                area.setId(TMUID.getUID());
                area.setExcelId(einfo.getId());
                area.setBelongId(belongId);
                area.setAreaId(areaId);
                area.setAreaName(areaName + einfo.getShowName());
                area.setTmused(1);
//				area.setStructsInfo(JSONArray.toJSONString(param.getResults()));//待研究
                srv.insert(area);
            }

        }
        return area;
    }

    /**
     * Excel模型信息保存
     * 用于Excel解析时，保存整体模型信息，包括excel表格、解析区域、解析结构
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, Boolean> excelInfoSave(ExcelVo param) {
        Map<String, Boolean> rmap = new HashMap<String, Boolean>();
        Boolean result = true;

        ExcelMode em = param.getModelInfo();
        em = em == null ? new ExcelMode() : em;//避免获取参数出错
        String belongId = em.getId();
        String name = em.getName();
        String rules = em.getRules();
        String structs = em.getStructs();
        String querys = em.getQuerys();
        String parseJson = param.getParseJson();
        String queryJson = param.getQueryJson();

        DigitalLedgerExcel einfo = getExcelData(belongId);

        String rulesFileId = null;
        String structsFileId = null;
        String parseFileId = null;
        String queryFileId = null;
        if (einfo == null) {//没有数据，进行保存
            //转文件上传，保留文件ID
            if (StringUtils.isNotEmpty(rules)) {
                SysFilesInfo sf = saveJsonToFile(rules);
                if (sf != null) {
                    rulesFileId = sf.getId();
                }
            }

            if (StringUtils.isNotEmpty(structs)) {
                SysFilesInfo sf = saveJsonToFile(structs);
                if (sf != null) {
                    structsFileId = sf.getId();
                }
            }

            if (StringUtils.isNotEmpty(parseJson)) {
                SysFilesInfo sf = saveJsonToFile(parseJson);
                if (sf != null) {
                    parseFileId = sf.getId();
                }
            }
            if (StringUtils.isNotEmpty(queryJson)) {
                SysFilesInfo sf = saveJsonToFile(queryJson);
                if (sf != null) {
                    queryFileId = sf.getId();
                }
            }
            einfo = new DigitalLedgerExcel();
            einfo.setId(TMUID.getUID());
            einfo.setBelongId(belongId);
            einfo.setShowName(name);//名称
//			einfo.setRulesInfo(rules);
//			einfo.setStructsInfo(structs);
            einfo.setRulesFileId(rulesFileId);
            einfo.setStructsFileId(structsFileId);
            einfo.setQuerysInfo(querys);
            einfo.setParseFileId(parseFileId);
            einfo.setQueryFileId(queryFileId);
            einfo.setTmused(1);
            result = 1 == srv.insert(einfo);

        } else {//有数据，进行更新
            //从文件获取modelJson进行对比，如果不一样，重新进行上传
            rulesFileId = einfo.getRulesFileId();
            structsFileId = einfo.getStructsFileId();
            parseFileId = einfo.getParseFileId();
            queryFileId = einfo.getQueryFileId();

            Boolean rulesMark = false;
            Boolean structsMark = false;

            if (StringUtils.isNotEmpty(rules)) {//转文件上传，保留文件ID
                rulesMark = true;
                String json = getFileJsonStr(rulesFileId);
                if (StringUtils.isNotEmpty(json)) {
                    if (json.equals(rules)) {
                        rulesMark = false;
                    }
                }
                if (rulesMark) {
                    SysFilesInfo sf = saveJsonToFile(rules);
                    if (sf != null) {
                        rulesFileId = sf.getId();
                    }
                }
            }

            if (StringUtils.isNotEmpty(structs)) {//转文件上传，保留文件ID
                structsMark = true;
                String json = getFileJsonStr(structsFileId);
                if (StringUtils.isNotEmpty(json)) {
                    if (json.equals(structs)) {
                        structsMark = false;
                    }
                }
                if (structsMark) {
                    SysFilesInfo sf = saveJsonToFile(structs);
                    if (sf != null) {
                        structsFileId = sf.getId();
                    }
                }
            }

            Boolean parseMark = false;
            Boolean queryMark = false;
            if (StringUtils.isNotEmpty(parseJson)) {//转文件上传，保留文件ID
                parseMark = true;
                String json = getFileJsonStr(parseFileId);
                if (StringUtils.isNotEmpty(json)) {
                    if (json.equals(parseJson)) {
                        parseMark = false;
                    }
                }
                if (parseMark) {
                    SysFilesInfo sf = saveJsonToFile(parseJson);
                    if (sf != null) {
                        parseFileId = sf.getId();
                    }
                }
            }

            if (StringUtils.isNotEmpty(queryJson)) {//已有上传文件，进行对比，不同进行更新
                queryMark = true;
                String json = getFileJsonStr(queryFileId);
                if (StringUtils.isNotEmpty(json)) {
                    if (json.equals(queryJson)) {
                        queryMark = false;
                    }
                }
                if (queryMark) {
                    SysFilesInfo sf = saveJsonToFile(queryJson);
                    if (sf != null) {
                        queryFileId = sf.getId();
                    }
                }
            } else {//如果没有查询模型，清空
                if (StringUtils.isNotEmpty(queryFileId)) {
                    String json = getFileJsonStr(queryFileId);
                    if (StringUtils.isNotEmpty(json)) {
                        fileSrv.deleteFile(queryFileId);
                    }
                    queryFileId = null;
                }
            }

            einfo.setShowName(name);//名称
            einfo.setRulesInfo(rules);
//			einfo.setStructsInfo(structs);
//			einfo.setQuerysInfo(querys);
            einfo.setRulesFileId(rulesFileId);
            einfo.setStructsFileId(structsFileId);
            einfo.setParseFileId(parseFileId);
            einfo.setQueryFileId(queryFileId);
            einfo.setTmused(1);
            result = 1 == srv.updateById(einfo);
        }

//		if(fileId!=null) {
//			//本操作只保存配置信息，不新建台账模型及采集点
//		}

        rmap.put("data", result);//更新结果，有数据保存
        return rmap;
    }

    //查询并返回Excel信息表对象
    @Override
    public DigitalLedgerExcel getExcelData(String belongId) {
        Where where = Where.create().eq(DigitalLedgerExcel::getBelongId, belongId);
        List<DigitalLedgerExcel> list = srv.queryData(DigitalLedgerExcel.class, where, null, null);
        if (StringUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    //查询并返回Excel区域数据信息
    private DigitalLedgerExcelArea getExcelArea(String belongId, String tableInd) {
        Where where = Where.create().eq(DigitalLedgerExcelArea::getBelongId, belongId).eq(DigitalLedgerExcelArea::getAreaId, tableInd);
        List<DigitalLedgerExcelArea> list = srv.queryData(DigitalLedgerExcelArea.class, where, null, null);
        if (StringUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    //保存json字符串到文件中
    private SysFilesInfo saveJsonToFile(String modelJson) {
        try {
            Charset cs = StandardCharsets.UTF_8;
            InputStream is = new ByteArrayInputStream(modelJson.getBytes(cs));
            MultipartFile mf = getMultipartFile(is, "excel.json");
            SysFilesInfo obj = fileSrv.saveFiles(mf, "ledger");
            is.close();
            return obj;
        } catch (Exception e) {
            log.error("台账Excel保存失败：" + e.getMessage());
            return null;
        }
    }

    private String getFileJsonStr(String fileId) {
        if (StringUtils.isEmpty(fileId)) {
            return null;
        }
        String rv = null;
        InputStream is = null;
        try {
            is = fileSrv.getFileSteam(fileId);
            if (is != null) {
                ByteArrayOutputStream result = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = is.read(buffer)) != -1) {
                    result.write(buffer, 0, length);
                }
                rv = result.toString(StandardCharsets.UTF_8.name());
            }
        } catch (Exception e) {
            rv = null;
            log.error("获取Exceljson文件失败：" + e.getMessage());
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e1) {
                }
            }
        }


        return rv;
    }

    /**
     * @param inputStream
     * @param fileName
     * @return
     * @category 根据InputStream和名称获取MultipartFile对象
     */
    public MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
        FileItem fileItem = createFileItem(inputStream, fileName);
        //CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
        return new CommonsMultipartFile(fileItem);
    }

    /**
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return FileItem
     * @category FileItem类对象创建
     */
    public FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[10 * 1024 * 1024];
        OutputStream os = null;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        } catch (IOException e) {
            log.error("文件上传失败");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);

                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);
                }
            }
        }
        return item;
    }

    @Override
    public List<DigitalLedger> excleBindForm(String formId) {
        List<DigitalLedger> result = new ArrayList<>();
        //查询区域表
        Where where = Where.create();
        where.eq(DigitalLedgerExcelArea::getTmused, 1);
        where.eq(DigitalLedgerExcelArea::getBelongId, formId);
        List<DigitalLedgerExcelArea> digitalLedgerExcelAreas = srv.rawQueryListByWhere(DigitalLedgerExcelArea.class, where);
        if (StringUtils.isNotEmpty(digitalLedgerExcelAreas)) {
            for (DigitalLedgerExcelArea digitalLedgerExcelArea : digitalLedgerExcelAreas) {
                DigitalLedger bean = new DigitalLedger();
                bean.setFormId(formId);
                bean.setLedgerName(digitalLedgerExcelArea.getAreaName());
                bean.setLedgerModuleId(digitalLedgerExcelArea.getId());
                result.add(bean);
            }
        }
        return result;
    }

    @Autowired
    private IFormManageService formSrv;

    @Autowired
    private IDataSourceService tdsServ;

    @Data
    private static class ExcelCell {
        //值
        private String v;
        //行号
        private Integer r;
        //列号
        private Integer c;
    }

    @Override
    public ExcelVo excleQuery(ExcelQueryDataDto param) {
        String result = null;
        //获取表单组件信息
        Map<String, JSONArray> dataMapByLedgerModuleId = getExcelDataByForm(param);
        //根据表单取出excel的解析信息
        ExcelParam excelParam = new ExcelParam();
        excelParam.setModelId(param.getModelId());
        excelParam.setModeCode("parse");
        ExcelVo excelInfo = this.getExcelInfo(excelParam);
        //根据参数取出excel的json配置信息 queryJson 或者  parseJson
        String jsonStr = null;
        if (StringUtils.isNotEmpty(excelInfo.getQueryJson())) {
            jsonStr = excelInfo.getQueryJson();
        } else {
            jsonStr = excelInfo.getParseJson();
        }
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        //解析视图json信息
        JSONArray excelInfoList = JSONArray.parseArray(jsonStr);
        //excel结构信息
        ExcelMode modelInfo = excelInfo.getModelInfo();
        String structs = modelInfo.getStructs();
        JSONObject structsJson = JSONObject.parseObject(structs);
        //获取表格区域
        JSONArray areas = structsJson.getJSONArray("areas");
        ListMultimap<String, ExcelCell> groupExcelCellList = LinkedListMultimap.create();
        //循环每个表格区域
        for (int i = 0; i < areas.size(); i++) {
            JSONObject area = areas.getJSONObject(i);
            //获取表格方向
            String direction = area.getString("tableDir");
            String areaId = area.getString("tid");
            String sheetInd = area.getString("sheetInd");
            //获取表格区域头信息
            JSONArray areaHeader =
                    structsJson.getJSONArray("querys");
            //迭代器删除
            Iterator<JSONObject> iterator = areaHeader.iterator();
            String tid = area.getString("tid");
            CellReference ref = null;
            //用于暂存指定列数据的下一个填充位置
            ListMultimap<String, ExcelCell> colAliasAndCellLoc = LinkedListMultimap.create();
            Map<String, Integer> finishRowMap = new HashMap<>();
            Map<String, Integer> finishColMap = new HashMap<>();
            while (iterator.hasNext()) {
                JSONObject next = iterator.next();
                String infoTid = next.getString("tid");
                String colAlias = null;
                if (StringUtils.isNotEmpty(infoTid)) {
                    String[] split = infoTid.split("-");
                    colAlias = split[split.length - 1];
                }
                if (!tid.equals(next.getString("areaId"))) {
                    //非区域数据移除
                    iterator.remove();
                } else {
                    //对于当前区域的信息
                    //将excel的表头位置信息转化为 cellData 规格化数据
                    String cellReference = next.getString("range");
                    if (StringUtils.isNotEmpty(cellReference)) {
                        //根据单元格引用范围
                        //需要构建单元格范围矩阵 将单元格矩阵数据加入到 colAliasAndCellLoc
                        buildCellMatrix(cellReference, finishRowMap, colAlias, finishColMap, colAliasAndCellLoc);
                    }
                }
            }
            //通过tid 和表单id  获取区域、台账模型的id
            //通过excel区域获取到对应的台账模型
            DigitalLedgerExcelArea excelArea = this.getExcelArea(param.getModelId(), areaId);
            if (excelArea != null) {
                String id = excelArea.getId();
                // 通过模型的id获取 数据源数据
                JSONArray jsonArray = dataMapByLedgerModuleId.get(id);
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (int i1 = 0; i1 < jsonArray.size(); i1++) {
                        // 遍历数据行
                        JSONObject dataRow = jsonArray.getJSONObject(i1);
                        //行内数据信息
                        JSONObject renderData = new JSONObject();
                        String accountULInfoCol = dataRow.getString("_accountULInfoCol");
                        if (StringUtils.isNotEmpty(accountULInfoCol)) {
                            JSONObject info = JSONObject.parseObject(accountULInfoCol);
                            renderData = info.getJSONObject("data");
                        }
                        for (Map.Entry<String, Object> entry : dataRow.entrySet()) {
                            //遍历数据行里的列
                            String colKey = entry.getKey();
                            if("timeMarkCol".equals(colKey)){
                                //适配前后端对于时间列的不同描述
                                colKey = "TIME_POINT";
                            }
                            //获取当前填充位置
                            //当前填充位置可能为多个，多个单元格的值都是一个
                            List<ExcelCell> excelCells = colAliasAndCellLoc.get(colKey);
                            List<ExcelCell> excelCellList = new ArrayList<>();
                            if (StringUtils.isNotEmpty(excelCells)) {
                                for (ExcelCell excelCell : excelCells) {
                                    ExcelCell cell = new ExcelCell();
                                    cell.setR(excelCell.getR());
                                    cell.setC(excelCell.getC());
                                    cell.setV(excelCell.getV());
                                    excelCellList.add(cell);
                                }
                            }
                            Object v = entry.getValue();
                            String value = v != null ? String.valueOf(v) : "";
                            //查找渲染值映射
                            JSONObject renderObj = renderData.getJSONObject(colKey);
                            if (renderObj != null) {
                                if("4".equals(renderObj.getString("com"))){
                                    //下拉框时候需要进行键值对解析
                                    String ks = renderObj.getString("ks");
                                    String vs = renderObj.getString("vs");
                                    if(StringUtils.isNotEmpty(ks) && StringUtils.isNotEmpty(vs)){
                                        List<String> ksArr = Arrays.asList(ks.split(","));
                                        List<String> vsArr = Arrays.asList(vs.split(","));
                                        if(ksArr.contains(value)){
                                            value = vsArr.get(ksArr.indexOf(value));
                                        }
                                    }
                                }if("3".equals(renderObj.getString("com"))){
                                    //勾选框
                                    if("true".equals(value)){
                                        value = "是";
                                    }else{
                                        value = "否";
                                    }
                                }else {
                                    //直接渲染
                                    String txt = renderObj.getString("txt");
                                    if (StringUtils.isNotEmpty(txt)) {
                                        value = txt;
                                    }
                                }
                            }
                            buildeCellData(colKey,value, excelCellList, groupExcelCellList, sheetInd, excelCells,
                                    finishRowMap, direction, colAliasAndCellLoc, finishColMap);
                        }
                    }
                }
            }

        }
        //将数据行填充到 excelJson
        if (groupExcelCellList != null && groupExcelCellList.size() > 0) {
            for (int i = 0; i < excelInfoList.size(); i++) {
                JSONObject jsonObject = excelInfoList.getJSONObject(i);
                jsonObject.remove("data");
                JSONArray jsonArray = jsonObject.getJSONArray("celldata");
                String sheetInd = jsonObject.getString("index");
                List<ExcelCell> excelCells = groupExcelCellList.get(sheetInd);
                excelCells = Optional.ofNullable(excelCells).orElse(new ArrayList<>()).stream()
                        .filter(item -> StringUtils.isNotEmpty(item.getV()))
                        .collect(Collectors.toList());
                for (ExcelCell excelCell : excelCells) {
                    String jsonString = JSONObject.toJSONString(excelCell);
                    JSONObject cell = JSONObject.parseObject(jsonString);
                    jsonArray.add(cell);
                }

            }
            for (int i = 0; i < excelInfoList.size(); i++) {
                JSONObject jsonObject = excelInfoList.getJSONObject(i);
                JSONArray jsonArray = jsonObject.getJSONArray("celldata");
                Iterator<JSONObject> iterator = jsonArray.iterator();
                while (iterator.hasNext()) {
                    JSONObject next = iterator.next();
                    String val = next.getString("v");
                    if (StringUtils.isEmpty(val)) {
                        iterator.remove();
                    }
                }
            }
        }
        excelInfo.setQueryJson(excelInfoList.toJSONString());
        excelInfo.setParseJson(null);
        return excelInfo;
    }
    
    @Override
    public ExcelData excleQueryData(ExcelQueryDataDto param) {
        String belongId = param.getModelId();
        DigitalLedgerExcel obj = getExcelLedgerInfo(belongId);
        if(obj == null) return null;
        
        String rulesFileId = obj.getRulesFileId();
        String strutsFileId = obj.getStructsFileId();
        String rulesJson = getFileJsonStr(rulesFileId);
        String strutsJson = getFileJsonStr(strutsFileId);
        
        ExcelMode em = new ExcelMode();
        em.setId(obj.getBelongId());
        em.setName(obj.getShowName());
        em.setRules(rulesJson);
        em.setStructs(strutsJson);
        em.setQuerys(obj.getQuerysInfo());
        
        Map<String, JSONArray> dataMap = getExcelDataByForm(param);
        
    	ExcelData ed = new ExcelData();
    	ed.setModelInfo(em);
    	ed.setDataMap(dataMap);
    	return ed;
    }

    private static void buildeCellData(String colKey,String value, List<ExcelCell> excelCellList,
                                       ListMultimap<String, ExcelCell> groupExcelCellList, String sheetInd, List<ExcelCell> excelCells, Map<String, Integer> finishRowMap, String direction, ListMultimap<String, ExcelCell> colAliasAndCellLoc, Map<String, Integer> finishColMap) {
        if (StringUtils.isNotEmpty(excelCellList)) {
            //同一个数据的多个单元格 值为相同的
            for (ExcelCell excelCell : excelCellList) {
                if (excelCell != null && StringUtils.isNotEmpty(value)) {
                    //为单元格填充数据
                    excelCell.setV(String.valueOf(value));
                    groupExcelCellList.put(sheetInd, excelCell);
                }
                //延伸更新下一个数据的填充位置
                excelCells = extendsCellNextLoc(colKey, excelCells, finishRowMap, direction, colAliasAndCellLoc, finishColMap, excelCell);
            }
        }
    }

    private static List<ExcelCell> extendsCellNextLoc(String colKey, List<ExcelCell> excelCells, Map<String, Integer> finishRowMap, String direction, ListMultimap<String, ExcelCell> colAliasAndCellLoc, Map<String, Integer> finishColMap, ExcelCell excelCell) {
        //将新的填充位置保存 将旧位置删除
        excelCells = excelCells.stream()
                .filter(cell -> !Objects.equals(cell.getR(), excelCell.getR()) &&
                        !Objects.equals(cell.getC(), excelCell.getC()))
                .collect(Collectors.toList());
        if (Objects.equals(finishRowMap.get(colKey), excelCell.getR())) {
            //当前单元格是最后一行的数据
            //填充完毕后 更新下次填充位置
            //数据填充方向
            if ("h".equals(direction)) {
                ExcelCell newCell = new ExcelCell();
                newCell.setV("");
                //表头方向为横向  数据将会纵向填充
                newCell.setR(excelCell.getR() + 1);
                newCell.setC(excelCell.getC());
                finishRowMap.put(colKey,finishRowMap.get(colKey)+1);
                excelCells.add(newCell);
                colAliasAndCellLoc.replaceValues(colKey, excelCells);
            }
        }
        if (Objects.equals(finishColMap.get(colKey), excelCell.getC())) {
            //当前单元格是最后一列的数据
            if ("v".equals(direction)) {
                ExcelCell newCell = new ExcelCell();
                newCell.setV("");
                //表头方向为纵向  数据奖会横向填充
                newCell.setC(excelCell.getC() + 1);
                newCell.setR(excelCell.getR());
                finishColMap.put(colKey,finishColMap.get(colKey)+1);
                excelCells.add(newCell);
                colAliasAndCellLoc.replaceValues(colKey, excelCells);
            }
        }
        return excelCells;
    }

    private static void buildCellMatrix(String cellReference, Map<String, Integer> finishRowMap, String colAlias, Map<String, Integer> finishColMap, ListMultimap<String, ExcelCell> colAliasAndCellLoc) {
        String[] split = cellReference.split(":");
        //多个单元格 做单元格矩阵
        CellReference ref1 = new CellReference(split[0]);
        int row1 = ref1.getRow(); // 行号（从 0 开始）
        int col1 = ref1.getCol(); // 列号（从 0 开始）
        int finishRow = row1;
        int finishCol = col1;
        if (split.length > 1) {
            CellReference ref2 = new CellReference(split[1]);
            finishRow = ref2.getRow(); // 行号（从 0 开始）
            finishCol = ref2.getCol(); // 列号（从 0 开始）
        }
        finishRowMap.put(colAlias, finishRow);
        finishColMap.put(colAlias, finishCol);
        for (int i1 = 0; i1 < finishRow - row1 + 1; i1++)
            for (int i2 = 0; i2 < finishCol - col1 + 1; i2++) {
                ExcelCell excelCell = new ExcelCell();
                excelCell.setC(i2 + col1);
                excelCell.setR(i1 + row1);
                excelCell.setV("");
                colAliasAndCellLoc.put(colAlias, excelCell);
            }
    }

    private Map<String, JSONArray> getExcelDataByForm(ExcelQueryDataDto param) {
    	//子表ID，为空时获取当前表单下所有子表，否则只获取该子表数据
    	String filterAreaId = param.getAreaId();
        //获取表单组件信息
        JSONArray formAllComponents = formSrv.getFormComponentJsonArray(param.getModelId(), "tdsEditTable");
        //获取表单台账关系表
        //匹配输入参数正则
        String regex = "\\{(.*?)\\}";
        Pattern pattern = Pattern.compile(regex);
        Map<String, JSONArray> dataMapByLedgerModuleId = new HashMap<>();
        //读取表单配置获取台账数据源信息
        if (formAllComponents != null) {
            TdsQueryDto tdsParam = new TdsQueryDto();
            for (int i = 0; i < formAllComponents.size(); i++) {
                JSONObject component = formAllComponents.getJSONObject(i);
                String optionStr = component.getString("options");
                JSONObject options = JSONObject.parseObject(optionStr);
                //数据源别名
                tdsParam.setTdsAlias(options.getString("tdsAlias"));
                //输入参数
                String inParams = options.getString("tdsInParams");
                String ledgerModuleId = options.getString("ledgerModuleId");
                String initType = options.getString("initType");
                String name = options.getString("name");
                
                //如果已传入子表ID，则跳过其他子表，只获取该传入子表的数据集
                if(filterAreaId != null && !filterAreaId.equals(ledgerModuleId)) continue;
                
                String newInparam = inParamConvert(param, pattern, inParams, ledgerModuleId, initType, name);
                tdsParam.setInParaAlias(newInparam);
                tdsParam.setIsInitData(false);
                tdsParam.setShowRenderValue(false);
                tdsParam.setErrInfo(false);
                tdsParam.setPage(1);
                tdsParam.setPageSize(100);
                JSONArray tdsData = tdsServ.getTDSData(tdsParam);
                JSONObject jsonObject = tdsData.getJSONObject(0);
                JSONArray data = new JSONArray();
                data = jsonObject.getJSONArray("data");
                dataMapByLedgerModuleId.put(ledgerModuleId, data);
            }
        }
        return dataMapByLedgerModuleId;
    }

    public static String inParamConvert(ExcelQueryDataDto param, Pattern pattern, String inParams,
                                        String ledgerModuleId
            , String initType, String compoentId) {
        //解析输入参数将输入参数进行替换
        Matcher matcher = pattern.matcher(inParams);
        while (matcher.find()) {
            String group = matcher.group(1);
            String str = matcher.group(0);
            String value = param.getQuerys().getString(group);
            if (StringUtils.isEmpty(value)) {
                value = "";
            }
            inParams = inParams.replace(str, value);
        }
        //拼接其他参数
        StringBuilder sb = new StringBuilder(inParams);
        sb.append("|ledgerModuleId=" + (StringUtils.isNotEmpty(ledgerModuleId) ? ledgerModuleId : "") + "|");
        sb.append("ledgerInitType=" + (StringUtils.isNotEmpty(initType) ? initType : "") + "|");
        sb.append("ledgerComponentId=" + (StringUtils.isNotEmpty(compoentId) ? compoentId : "") + "|");
        String newInparam = sb.toString();
        return newInparam;
    }

    /**
     * 获取默认的台账数据源
     * <AUTHOR>
     * @date 2025/5/13
     * @params
     * @return 数据源别名
     *
    */
    @Autowired
    private ISysConfigService sysConfigServ;
    @Override
    public String getDefaultLedgerTds() {
        //查询系统参数
        SysConfig ledgerTds = sysConfigServ.getSysConfigByKey("ledgerTds");
        if(ledgerTds!=null){
            return ledgerTds.getConfigValue();
        }
        return "";
    }
}
