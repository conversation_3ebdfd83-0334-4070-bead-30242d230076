package com.yunhesoft.accountTools.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.util.DateUtil;
import com.yunhesoft.accountTools.entity.dto.AbnormalCountParam;
import com.yunhesoft.accountTools.entity.dto.AbnormalParam;
import com.yunhesoft.accountTools.entity.dto.AccountParam;
import com.yunhesoft.accountTools.entity.po.AccountAbnormalData;
import com.yunhesoft.accountTools.entity.po.AccountAbnormalDataItem;
import com.yunhesoft.accountTools.entity.po.AccountAbnormalForm;
import com.yunhesoft.accountTools.entity.vo.AbnormalDataVo;
import com.yunhesoft.accountTools.entity.vo.AbnormalObj;
import com.yunhesoft.accountTools.entity.vo.AbnormalShiftVo;
import com.yunhesoft.accountTools.entity.vo.ReturnObj;
import com.yunhesoft.accountTools.service.IAccountSchedService;
import com.yunhesoft.accountTools.service.IAccountToolsService;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountForm;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormManage;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormMeter;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormVo;
import com.yunhesoft.system.applyConf.service.IAccountFormService;
import com.yunhesoft.system.applyConf.service.IApplyConfService;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.mongodb.service.MongoDBService;
import com.yunhesoft.system.tds.entity.dto.TdsTagDto;
import com.yunhesoft.system.tds.entity.po.AccountAutoSaveTodo;
import com.yunhesoft.system.tds.entity.po.TdataSource;
import com.yunhesoft.system.tds.entity.po.TdsAccountConf;
import com.yunhesoft.system.tds.entity.po.TdsAccountData;
import com.yunhesoft.system.tds.entity.po.TdsAccountMeter;
import com.yunhesoft.system.tds.entity.po.TdsAccountTime;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.entity.vo.TdsAccountDataVo;
import com.yunhesoft.system.tds.model.TDSAccount;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tds.service.IRtdbService;
import com.yunhesoft.system.tds.service.impl.DataAccountTools;
import com.yunhesoft.tmsf.form.entity.dto.SFFormQueryDto;
import com.yunhesoft.tmsf.form.entity.po.SFForm;
import com.yunhesoft.tmsf.form.entity.vo.SFFormComponentBean;
import com.yunhesoft.tmsf.form.service.IFormManageService;
import com.yunhesoft.tmsf.form.service.impl.FormTdsHandlerFactory;
import com.yunhesoft.tmsf.form.service.impl.FormTdsHandlerService;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class AccountSchedService implements IAccountSchedService {
	
	@Autowired
	private EntityService srv;
	
	@Autowired
	private ICostService costService;//核算服务类
	
	@Autowired
	private IApplyConfService applySrv;
	
	@Autowired
	private IAccountToolsService accountServ; // 台账服务类
	
	@Autowired
    private IDataSourceAccountService dsaServ;
	
	@Autowired
	private IAccountFormService aformServ; // 自定义表单服务类
	
	@Autowired
    private IFormManageService formService;//表单服务类
	
	@Autowired
	private UnitItemInfoService unititeminfoservice;
	
	@Autowired
	private IFormManageService formServ;
	@Autowired
	private FormTdsHandlerFactory handlerFactory;	//工厂类
	
	@Autowired
	private IDataSourceService tdsSrv;	//数据源
	
	@Autowired
	private IUnitMethodService unitMeth;
	
	@Autowired
	private IShiftService shift;
	
	@Autowired
	private RedisUtil redis;
	
	@Autowired
	private IRtdbService rtdbSrv;
	
	@Autowired
	private MongoDBService mongoDBServ;
	
	@Autowired
	private AuthService auser;
	
	
	private final String accountKey = "TDS:ACCOUUNT:TITLE";//
	private final String accountNewTab = "ac";
	private String accountTypeId = "account";
//	private Boolean showLog = false;

	/**
	 * @category 获取异常按日期、核算对象、班次统计数据
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public Boolean autoSaveMeterData(AbnormalParam param) {
		
		String ksrq = DateTimeUtils.getNowDateStr();
		ksrq = DateTimeUtils.format(DateTimeUtils.doDate(DateTimeUtils.parseDate(ksrq), -1), DateTimeUtils.YYYY_MM_DD);//开始日期取上一日（可能存在当前是跨夜的情况）
		String jzrq = DateTimeUtils.getNowDateStr();//截止日期是当前日期
		
		Date nd = DateTimeUtils.getNowDate();
		
		List<Costuint> unitList = getAllCostunit();//所有核算对象
//		//计算当前时间所在班次实际应用的日期（理论上所有班次都一致），提取相关日期
//		String applyRq = null;
//		for (Costuint costuint : unitList) {
//			Boolean pause = false;
//			List<ShiftForeignVo> listShift = getUnitShiftList(costuint.getId(), ksrq, jzrq);
//			if(StringUtils.isNotEmpty(listShift)) {
//				for (ShiftForeignVo shift : listShift) {
//					String sbsj = shift.getSbsj();
//					String xbsj = shift.getXbsj();
//					if(DateTimeUtils.bjDate(DateTimeUtils.parseDateTime(sbsj), DateTimeUtils.parseDateTime(xbsj), nd)) {//当前时间在上下班内
//						String tbrq = shift.getTbrq();
//						applyRq = tbrq;
//						pause = true;
//						break;
//					}
//				}
//			}
//			if(pause) {
//				break;
//			}
//		}
		
		List<TdsAccountForm> customList = getCustomList();//自定义台账
		List<String> customUnitList = getCustomUnitList(customList);//整理同核算对象的自定义表单

		//所有台账用表单
		SFFormQueryDto formdto = new SFFormQueryDto();
		formdto.setCategory(this.accountTypeId);//台账分类
		List<SFForm> formList = formService.queryFormInfoList(formdto, null);
		
		//整理表单台账数据源相关配置
		Map<String, List<String>> amap = new HashMap<String, List<String>>();//台账表单
		Map<String, List> dsMap = new HashMap<String, List>();//所有台账用表单中的台账数据源
		getFormAccountTds(formList, amap, dsMap);
		
		
		//内部变量
		Map<String, Map<String, List<Date>>> rqCfMap = new HashMap<String, Map<String,List<Date>>>();//对应日期确认的数据
		Map<String, Map<String, Map<Date, List<String>>>> rqDaMap = new HashMap<String, Map<String, Map<Date, List<String>>>>();//对应日期仪表的数据
		Map<String, Map> mongoMap = new HashMap<String, Map>();//MongoDB仪表数据 key, obj
		Map<String, Map<String, AccountAutoSaveTodo>> todoMap = new HashMap<String, Map<String, AccountAutoSaveTodo>>();//已有待办数据
//		List<String> degreeList = new ArrayList<String>();

		//独立默认台账（按核算对象）
		for (Costuint unit : unitList) {
			String unitCode = unit.getId();
			if(customUnitList.contains(unitCode) && StringUtils.isNotEmpty(formList)) {
				continue;
			}
			
			List<AccountAutoSaveTodo> addList = null;//new ArrayList<AccountAutoSaveTodo>();
			List<AccountAutoSaveTodo> updList = null;//new ArrayList<AccountAutoSaveTodo>();
			
			
			//获取核算对象相关机构，并获取日期段班次信息
			List<ShiftForeignVo> listShift = getUnitShiftList(unitCode, ksrq, jzrq);
			if(listShift!=null)
			for (ShiftForeignVo shift : listShift) {
				//上下班时间加日期，暂未知相关函数，暂时根据时间对比判断
				String shiftId = shift.getShiftClassCode();
				String sbsj = shift.getSbsj();
				String xbsj = shift.getXbsj();
				String tbrq = shift.getTbrq();
				
				if(!DateTimeUtils.bjDate(DateTimeUtils.parseDateTime(sbsj), DateTimeUtils.parseDateTime(xbsj), nd)) {//当前时间在上下班直接内
					continue;
				}
				
				
				//循环所有台账数据源即可，不考虑表单关系
				for (String ds : dsMap.keySet()) {
					//获取并保存日期的确认数据
					String rqkey = tbrq+"_"+ds;
					Map<String, List<Date>> confMap = null;
					Map<String, Map<Date, List<String>>> dataMap = null;
					Map<String, AccountAutoSaveTodo> tMap = null;
					if(!rqCfMap.containsKey(rqkey)) {
						confMap = getConfirmMap(ds, tbrq);
						rqCfMap.put(rqkey, confMap);
					}else {
						confMap = rqCfMap.get(rqkey);
					}
					if(!rqDaMap.containsKey(rqkey)) {
						dataMap = getDataMap(ds, tbrq, mongoMap);
						rqDaMap.put(rqkey, dataMap);
					}else {
						dataMap = rqDaMap.get(rqkey);
					}
					if(!todoMap.containsKey(tbrq)) {
						tMap = getTodoMap(tbrq);
						todoMap.put(tbrq, tMap);
					}else {
						tMap = todoMap.get(tbrq);
					}
					
					//获取日期对应的数据，判断对应时间点是否有数据，如果无数据，进行写入，生成待办数据；否则补录内容，不再生成待办 TODO
					
					String key = tbrq+"_"+unitCode+"_"+shiftId+"__"+ds;
					
					List clist = dsMap.get(ds);
					if(clist!=null && clist.size() > 1) {
						TdsAccountConf conf = (TdsAccountConf)clist.get(0);
						TdsAccountTime timeConf = (TdsAccountTime)clist.get(1);
						if("2".equals(conf.getTagType())) {//采集点
							List<Date> datePosList = getPos(sbsj, xbsj, timeConf, nd);
							//判断，如果要获取的时间点都确认了，不再进行处理 TODO
							Boolean needGet = false;
							if(confMap!=null && confMap.size() > 0) {
								List<Date> cflist = confMap.get(key);
								if(StringUtils.isNotEmpty(cflist)) {
									for (Date d : datePosList) {
										if(!cflist.contains(d)) {
											needGet = true;
											break;
										}
									}
								}else {
									needGet = true;
								}
							}else {
								needGet = true;
							}
							if(StringUtils.isEmpty(datePosList)) {//如果没有要获取的时间点，不进行处理
								needGet = false;
							}
							//判断是否有新时间点的数据写入，如果有，生成关于此日期、核算对象、班次、表单或自定义表单的
							if(needGet) {
								getTagData(ds, shift, unitCode,  conf, timeConf, datePosList, null, dataMap, confMap, key, mongoMap, tMap, addList, updList);
							}
							
						}else {//lims
							//lims暂不涉及确认，不做获取判断
						}
						
					}
				}
				
			}
			//待办再研究
//			if(false && (StringUtils.isNotEmpty(addList) || StringUtils.isNotEmpty(updList))) {
//				saveTodoData(addList, updList);
//			}
		}
		//自定义台账
		for (TdsAccountForm obj : customList) {
			List<String> dsList = amap.get(obj.getFormCode());//自定义台账指定表单
			if(StringUtils.isNotEmpty(dsList)) {//判断有台账数据源
				String uc = obj.getUnitCode();
				if(uc != null) {
					List<AccountAutoSaveTodo> addList = null;//new ArrayList<AccountAutoSaveTodo>();
					List<AccountAutoSaveTodo> updList = null;//new ArrayList<AccountAutoSaveTodo>();
					
					String unitCode = uc.replace("[", "").replace("]", "").replace("\"", "").replace(" ", "");
					List<String> slist = Coms.StrToList(unitCode, ",");
					String ucode = slist.get(0);
					//获取核算对象相关机构，并获取日期段班次信息
					List<ShiftForeignVo> listShift = getUnitShiftList(ucode, ksrq, jzrq);
					for (ShiftForeignVo shift : listShift) {
						//上下班时间加日期，暂未知相关函数，暂时根据时间对比判断
						String shiftId = shift.getShiftClassCode();
						String sbsj = shift.getSbsj();
						String xbsj = shift.getXbsj();
						String tbrq = shift.getTbrq();
						
						if(!DateTimeUtils.bjDate(DateTimeUtils.parseDateTime(sbsj), DateTimeUtils.parseDateTime(xbsj), nd)) {//当前时间在上下班直接内
							continue;
						}
						
						for (String ds : dsList) {
							//获取并保存日期的确认数据
							String rqkey = tbrq+"_"+ds;
							Map<String, List<Date>> confMap = null;
							Map<String, Map<Date, List<String>>> dataMap = null;
							Map<String, AccountAutoSaveTodo> tMap = null;
							if(!rqCfMap.containsKey(rqkey)) {
								confMap = getConfirmMap(ds, tbrq);
								rqCfMap.put(rqkey, confMap);
							}else {
								confMap = rqCfMap.get(rqkey);
							}
							if(!rqDaMap.containsKey(rqkey)) {
								dataMap = getDataMap(ds, tbrq, mongoMap);
								rqDaMap.put(rqkey, dataMap);
							}else {
								dataMap = rqDaMap.get(rqkey);
							}
							if(!todoMap.containsKey(tbrq)) {
								tMap = getTodoMap(tbrq);
								todoMap.put(tbrq, tMap);
							}else {
								tMap = todoMap.get(tbrq);
							}
							String key = tbrq+"_"+unitCode+"_"+shiftId+"_"+obj.getId()+"_"+ds;
							
							List clist = dsMap.get(ds);
							if(clist!=null && clist.size() > 1) {
								TdsAccountConf conf = (TdsAccountConf)clist.get(0);
								TdsAccountTime timeConf = (TdsAccountTime)clist.get(1);
								if("2".equals(conf.getTagType())) {//采集点
									List<Date> datePosList = getPos(sbsj, xbsj, timeConf, nd);
									Boolean needGet = false;
									if(confMap!=null && confMap.size() > 0) {
										List<Date> cflist = confMap.get(key);
										if(StringUtils.isNotEmpty(cflist)) {
											for (Date d : datePosList) {
												if(!cflist.contains(d)) {
													needGet = true;
													break;
												}
											}
										}else {
											needGet = true;
										}
									}else {
										needGet = true;
									}
									if(StringUtils.isEmpty(datePosList)) {//如果没有要获取的时间点，不进行处理
										needGet = false;
									}
									//判断是否有新时间点的数据写入，如果有，生成关于此日期、核算对象、班次、表单或自定义表单的
									if(needGet) {
										getTagData(ds, shift, unitCode, conf, timeConf, datePosList, obj.getId(), dataMap, confMap, key, mongoMap, tMap, addList, updList);
									}
								}else {//lims
									//lims暂不涉及确认，不做获取判断
								}
								
							}
						}
						
					}
					//待办再研究
//					if(false && (StringUtils.isNotEmpty(addList) || StringUtils.isNotEmpty(updList))) {
//						saveTodoData(addList, updList);
//					}
				}
			}
		}
		
		return true;
	}
	/*
	 * 待办数据保存
	 */
	private void saveTodoData(List<AccountAutoSaveTodo> addList, List<AccountAutoSaveTodo> updList) {
		if(StringUtils.isNotEmpty(addList)) {
			srv.insertBatch(addList, 100);
		}
		if(StringUtils.isNotEmpty(updList)) {
			srv.updateByIdBatch(updList, 100);
		}
	}
	/*
	 * 获取已生成的待办数据
	 */
	private Map<String, AccountAutoSaveTodo> getTodoMap(String rq) {
		Map<String, AccountAutoSaveTodo> rmap = new HashMap<String, AccountAutoSaveTodo>();
		List<AccountAutoSaveTodo> list = getTodoData(rq, null, null, null, null);
		if(StringUtils.isNotEmpty(list)) {
			for (AccountAutoSaveTodo obj : list) {
				String key = rq+"_"+obj.getUnitCode()+"_"+obj.getShiftCode()+"_"+(obj.getCustomCode()==null?"":obj.getCustomCode());
				rmap.put(key, obj);
			}
		}
		return rmap;
	}
	/*
	 * 获取并整理已保存的MongoDB仪表数据
	 */
	private Map<String, Map<Date, List<String>>> getDataMap(String ds, String rq, Map<String, Map> mongoMap) {
		Map<String, Map<Date, List<String>>> rmap = new HashMap<String, Map<Date, List<String>>>();//key, time, tag
		List<Map> dataMap = getDataList(ds, rq);
		if(StringUtils.isNotEmpty(dataMap)) {
			for (Map map : dataMap) {//循环确认主数据
				String key = getMapStr(map, "rq")+"_"+getMapStr(map, "unitCode")+"_"+getMapStr(map, "bc")+"_"+getMapStr(map, "accountId")+"_"+ds;
				mongoMap.put(key, map);//单条数据
				Object cinfo = map.get("info");
				Map<Date, List<String>> dmap = new HashMap<Date, List<String>>();
				if(cinfo!=null) {
					List<Map> tlist = (List<Map>)cinfo;
					for (Map tmap : tlist) {
						String col = getMapStr(tmap, "col");
						String sj = getMapStr(tmap, "sj");
						if(sj.length() >= 10) {
							Date d = DateTimeUtils.parseDateTime(sj);
							if(dmap.containsKey(d)) {
								dmap.get(d).add(col);
							}else {
								List<String> _list = new ArrayList<String>();
								_list.add(col);
								dmap.put(d, _list);
							}
						}
					}
				}
				if(dmap.size() > 0) {
					rmap.put(key, dmap);
				}
			}
		}
		return rmap;
	}
	//获取仪表数据
	private List<Map> getDataList(String ds, String rq) {
		//仪表数据
		List<Map> cdata = dsaServ.getAccountNewData(accountNewTab+"_"+ds, null, rq, null, null, null);
		return cdata;
	}
	//获取数据
	private void getTagData(String ds, ShiftForeignVo shift, String unitCode, TdsAccountConf conf, TdsAccountTime timeConf, List<Date> datePosList, String accountId,
		Map<String, Map<Date, List<String>>> dataMap, Map<String, List<Date>> confMap, String key, Map<String, Map> mongoMap, Map<String, AccountAutoSaveTodo> tMap,
		List<AccountAutoSaveTodo> addList, List<AccountAutoSaveTodo> updList) {
		Date nd = DateTimeUtils.getNowDate();
//		String verrq = applySrv.getAaccuntVerRq(unitCode, shift.getTbrq());
		List<TdsAccountMeter> mlist = getMeterList(ds, unitCode, accountId, shift.getTbrq());
		
		if(StringUtils.isEmpty(mlist)) {
			return;
		}
		List<String> tagIds = new ArrayList<String>();//仪表id数组
		List<String> tagCodes = new ArrayList<String>();//仪表id数组
		Map<String, String> tagCodeIdMap = new HashMap<String, String>();//仪表位号与标识map
		Map<String, String> tagIdCodeMap = new HashMap<String, String>();//仪表位号与标识map
		setTagInfo(mlist, tagIds, tagCodes, tagCodeIdMap, tagIdCodeMap);
//		if(dataMap!=null) {
			
		List<Date> cflist = confMap.get(key);//已确认时间点
		Map<Date, List<String>> dmap = dataMap==null? null : dataMap.get(key);//时间仪表数据
		//与时间对比，进行数据获取；生成待办
		String st = null, et = null;
		int step = Coms.judgeLong(timeConf.getTimeStep()) ? Integer.parseInt(timeConf.getTimeStep()) : 120;
		Boolean getData = false;
		List<Date> getDateList = new ArrayList<Date>();
		for (Date dp : datePosList) {
			if(cflist!=null && cflist.contains(dp)) {//有确认，不再处理
			}else {//无确认，判断是否有时间点数据
				if(dmap!=null && dmap.containsKey(dp)) {//如果有时间点数据，看数据是否全，不全重新获取 TODO 暂不处理，后期优化
//						List<String> haveDataList = dmap.get(dp);
//						tagIds
				}else {//时间点无数据，进行获取并保存，写入或更新待办表数据
					getDateList.add(dp);
					if(st==null) {
						st = DateTimeUtils.formatDateTime(dp);
						et = st;
					}else {
						et = DateTimeUtils.formatDateTime(dp);
					}
					getData = true;
				}
				
			}
		}
		if(getData && st!=null && et!=null) {
			
			List<Tag> rdbtaglist = null;
			try {
//				rdbtaglist = rtdbSrv.queryRtdbTagData(tagCodes, st, et, step);
			} catch (Exception e) {
				rdbtaglist = null;
				log.error("台账获取仪表数据失败："+e.getMessage());
			}
			//test
			if("ZQF7SXAB507E41WFKR1262".equals(unitCode) || "ZQF7SXAB507E41WFMV1259".equals(unitCode) || "ZQF7SXAB507E41WFML1206".equals(unitCode)) {
				
				rdbtaglist = new ArrayList<Tag>();
				Tag t1 = new Tag();
				t1.setTagCode("DGS1_2103LIC3002.PV");
				List<TagData> datas1 = new ArrayList<TagData>();
				TagData d1 = new TagData();
				d1.setDatetime("2024-04-26 08:30:00");
				d1.setValue("911111");
				datas1.add(d1);
				t1.setDatas(datas1);
				
				Tag t2 = new Tag();
				t2.setTagCode("HYU1_7400GTW1005B.PV");
				List<TagData> datas2 = new ArrayList<TagData>();
				TagData d2 = new TagData();
				d2.setDatetime("2024-04-26 10:30:00");
				d2.setValue("93333");
				datas2.add(d2);
				t2.setDatas(datas2);
				
				Tag t3 = new Tag();
				t3.setTagCode("RAU1_2502BD60502.PV");
				List<TagData> datas3 = new ArrayList<TagData>();
				TagData d3 = new TagData();
				d3.setDatetime("2024-04-26 10:30:00");
				d3.setValue("944444");
				datas3.add(d3);
				t3.setDatas(datas3);
				
				rdbtaglist.add(t1);
				rdbtaglist.add(t2);
				rdbtaglist.add(t3);
			}
			
			
			if(StringUtils.isNotEmpty(rdbtaglist)) {
				Map<String, Map<Date, String>> map = new HashMap<String, Map<Date,String>>();
				
				Map<String, List<Map<String,Object>>> tagFaULmap = null;
				if(StringUtils.isNotEmpty(tagIds)) {
					List<String> unitIdList = Coms.StrToList(unitCode, ",");
					tagFaULmap = new HashMap<String, List<Map<String,Object>>>();//仪表对应方案的时间范围和上下限信息
					DataAccountTools dat = new DataAccountTools();
					dat.getProgramInfo(unitIdList, tagIds, getDateList, null, null, tagFaULmap);//暂时不考虑一个采集点出现在多个核算对象里的情况
//						getProgramInfo(unitIdList, colList);
				}
				
				for (Tag tag : rdbtaglist) {
					String tagCode = tag.getTagCode().toUpperCase();//转大写
					
					Map<Date, String> _map = new LinkedHashMap<Date, String>();
					map.put(tagCode, _map);
					
					//数采数据工序判断过滤，如果仪表停用，不获取采集数据
					List<Map<String, Object>> faData = null;
					if(tagFaULmap!=null && tagFaULmap.size() > 0) {
						String tagId = tagCodeIdMap.get(tagCode);
						if(StringUtils.isNotEmpty(tagId)) {
							faData = tagFaULmap.get(tagId);
						}
					}

					List<TagData> ilist = tag.getDatas();
					if(StringUtils.isNotEmpty(ilist)) {
						for (TagData da : ilist) {
							Date dt = DateUtil.parseDateTime(da.getDatetime());
							if(faData==null || faUseTag(faData, dt)) {//没有方案或者在使用范围内
								_map.put(dt, da.getValue()==null?"":String.valueOf(da.getValue()));
							}
						}
					}
				}
				
				if(map.size() > 0) {
					JSONArray insertData = new JSONArray();//插入数据
					for (String tagId : tagIds) {
						String tagCode = tagIdCodeMap.get(tagId);
						if(StringUtils.isNotEmpty(tagCode)) {
							Map<Date, String> sjmap = map.get(tagCode); 
							if(sjmap!=null)
							for (Date sj : getDateList) {
								String valstr = sjmap.get(sj);
								if(StringUtils.isNotEmpty(valstr)) {//插入数据
									JSONObject aobj = new JSONObject();
									aobj.put("col", tagId);//列名
									aobj.put("sj", DateTimeUtils.formatDateTime(sj));
									aobj.put("sjlong", sj.getTime());
									aobj.put("valstr", valstr);
									aobj.put("creTime", nd);
									aobj.put("updTime", null);
									aobj.put("updUserId", null);
									aobj.put("additionVal", null);//附加值
									aobj.put("edit", false);
									insertData.add(aobj);
								}
							}
						}
					}
					
					if(insertData.size() > 0) {
						Map mongoData = mongoMap.get(key);
						if(StringUtils.isNotEmpty(mongoData)) {//有新数据，针对新数据进行操作
							JSONObject tobj = new JSONObject(mongoData);
							JSONArray arr = tobj.getJSONArray("info");
							if(arr==null) {
								arr = new JSONArray();
							}
							arr.addAll(insertData);
							tobj.put("info", arr);
							mongoDBServ.updateById(accountNewTab+"_"+ds, tobj);
						}else {//没有主数据，进行添加
							JSONObject tobj = createMongoDbData("data", shift.getTbrq(), unitCode, shift.getShiftClassCode(), accountId, insertData);
							mongoDBServ.insert(accountNewTab+"_"+ds, tobj);
							
							//初始化确认数据
							JSONArray carr = new JSONArray();
							JSONObject cobj = createMongoDbData("confirm", shift.getTbrq(), unitCode, shift.getShiftClassCode(), accountId, carr);
							mongoDBServ.insert(accountNewTab+"_confirm_"+ds, cobj);
							
						}
						
//						//有插入数据操作，生成待办
//						String todoKey = key.replace("_"+ds, "");
//						if(tMap!=null && tMap.containsKey(todoKey)) {//更新待办数据
//							AccountAutoSaveTodo obj = tMap.get(todoKey);
//							obj.setTodoMark(1);
//							obj.setLastCountTime(nd);
//							updList.add(obj);
//						}else {//添加待办数据
//							AccountAutoSaveTodo obj = new AccountAutoSaveTodo();
//							obj.setId(TMUID.getUID());
//							obj.setRq(shift.getTbrq());
//							obj.setUnitCode(unitCode);
//							obj.setShiftCode(shift.getShiftClassCode());
//							obj.setTdsAlias(ds);
//							obj.setCustomCode(accountId);
//							obj.setSbsj(shift.getSbsj());
//							obj.setXbsj(shift.getXbsj());
//							obj.setLastCountTime(nd);
//							obj.setTodoMark(1);
//							obj.setTodoNum(0);
//							obj.setTmused(1);
//							addList.add(obj);
//						}
					}
				}
			}
		}
//		}else {//没有相关数据，获取数据；生成待办
//		}
		
	}
	private JSONObject createMongoDbData(String type, String rq, String unitCode, String shiftCode, String accountId, JSONArray data) {
		JSONObject tobj = new JSONObject();
		tobj.put("_id", TMUID.getUID());
		tobj.put("type", type);//
		tobj.put("unitCode", unitCode);//核算单元
		tobj.put("bc", shiftCode);//班次
		tobj.put("accountId", accountId);//accountId
		tobj.put("rq", rq);
		tobj.put("rqlong", DateTimeUtils.parseDateTime(rq).getTime());
		tobj.put("mode", 1);
		tobj.put("tmused", 1);
		tobj.put("info", data);
		return tobj;
	}
	/*
	 * 整理仪表数据
	 */
	private void setTagInfo(List<TdsAccountMeter> mlist, List<String> tagIds, List<String> tagCodes,
			Map<String, String> tagCodeIdMap, Map<String, String> tagIdCodeMap) {
		for (TdsAccountMeter obj : mlist) {
			tagIds.add(obj.getTagid());
			String tagCode = obj.getDatasource();
			if(StringUtils.isNotEmpty(tagCode) && !tagCodes.contains(tagCode)) {
				tagCodes.add(tagCode);
				tagCodeIdMap.put(tagCode, obj.getTagid());
			}
			if(StringUtils.isNotEmpty(tagCode)) {
				tagIdCodeMap.put(obj.getTagid(), tagCode);
			}
		}
		
	}
	private boolean faUseTag(List<Map<String, Object>> faData, Date dt) {//判断方案在各时间点是否使用
		Boolean flag = true;
		
		if(StringUtils.isNotEmpty(faData)) {
			String timeStr = DateTimeUtils.formatDateTime(dt);
			for (Map<String, Object> map : faData) {
				String srq = String.valueOf(map.get("srq"));
				String erq = String.valueOf(map.get("erq"));
				if(timeStr.compareTo(srq) >= 0 && timeStr.compareTo(erq) < 0) {//根据时间范围判断是否应用方案
					String usedstr = String.valueOf(map.get("used"));
					if(Coms.judgeDouble(usedstr) && Double.parseDouble(usedstr) == 0) {
						flag = false;
						break;
					}
				}
			}
		}
		return flag;
	}
//	private List<String> getTagIdList(List<TdsAccountMeter> mlist) {
//		List<String> rlist = new ArrayList<String>();
//		for (TdsAccountMeter obj : mlist) {
//			rlist.add(obj.getTagid());
//		}
//		return rlist;
//	}
//	private List<String> getTagCodeList(List<TdsAccountMeter> mlist) {
//		List<String> rlist = new ArrayList<String>();
//		for (TdsAccountMeter obj : mlist) {
//			String tagCode = obj.getDatasource();
//			if(StringUtils.isNotEmpty(tagCode) && !rlist.contains(tagCode)) {
//				rlist.add(tagCode);
//			}
//		}
//		return rlist;
//	}
	//获取仪表信息
	private List<TdsAccountMeter> getMeterList(String ds, String unitCode, String accountId, String rq) {
		List<TdsAccountMeter> mlist = new ArrayList<TdsAccountMeter>();
		List<String> unitlist = Coms.StrToList(unitCode, ",");
		if(unitlist.size() > 1) {
			//缓存读取数据
			String tagType = "2";
			String aid = accountId == null ? "" : accountId;
//			String verrq = applySrv.getAaccuntVerRq(unitCode, rq);
			String redisKey = unitCode+"_"+rq+"_"+aid+"_"+tagType;
			List<Map> rlist = redis.getMapValue(accountKey, redisKey);
			List<TdsAccountMeter> redisList = new ArrayList<TdsAccountMeter>();
			if(rlist!=null && rlist.size() > 0) {
				for (Map m : rlist) {
					redisList.add(ObjUtils.convertToObject(TdsAccountMeter.class, m));
				}
			}
			
			if(StringUtils.isNotEmpty(redisList)) {
				mlist = redisList;
			}else {
				List<TdsAccountMeter> tlist = new ArrayList<TdsAccountMeter>();
				for (String ucode : unitlist) {
					List<TdsAccountMeter> _list = null;
					List<String> tagIdList = new ArrayList<String>();
					if(StringUtils.isNotEmpty(accountId)) {
						String verrq = applySrv.getAaccuntVerRq(ucode, rq);
						List<TdsAccountFormMeter> fmlist = dsaServ.getFormMeterList(accountId, ucode, verrq, "2");
						if(StringUtils.isNotEmpty(fmlist)) {
							for (TdsAccountFormMeter fmeter : fmlist) {
								tagIdList.add(fmeter.getTagid());
							}
						}
					}
					if(StringUtils.isEmpty(tagIdList)) {//未设置获取全部仪表
						_list = getMeterList(ucode, rq, null);
					}else {
						_list = getMeterList(ucode, rq, tagIdList);
					}
					if(StringUtils.isNotEmpty(_list)) {
						tlist.addAll(_list);
					}
				}
				mlist = tlist;
			}
		}else {
			//缓存读取数据
			String tagType = "2";
			String aid = accountId == null ? "" : accountId;
			String verrq = applySrv.getAaccuntVerRq(unitCode, rq);
			String redisKey = unitCode+"_"+verrq+"_"+aid+"_"+tagType;
			
			List<Map> rlist = redis.getMapValue(accountKey, redisKey);
			List<TdsAccountMeter> redisList = new ArrayList<TdsAccountMeter>();
			if(rlist!=null && rlist.size() > 0) {
				for (Map m : rlist) {
					redisList.add(ObjUtils.convertToObject(TdsAccountMeter.class, m));
				}
			}
			
			if(StringUtils.isNotEmpty(redisList)) {
				mlist = redisList;
			}else {
				List<String> tagIdList = new ArrayList<String>();
				if(StringUtils.isNotEmpty(accountId)) {
					List<TdsAccountFormMeter> fmlist = dsaServ.getFormMeterList(accountId, unitCode, verrq, "2");
					if(StringUtils.isNotEmpty(fmlist)) {
						for (TdsAccountFormMeter fmeter : fmlist) {
							tagIdList.add(fmeter.getTagid());
						}
					}
				}
				if(StringUtils.isEmpty(tagIdList)) {//未设置获取全部仪表
					mlist = getMeterList(unitCode, rq, null);
					if(mlist == null) {
						mlist = new ArrayList<TdsAccountMeter>();
					}
				}else {
					mlist = getMeterList(unitCode, rq, tagIdList);
				}
				
				List<TdsTagDto> ttlist = new ArrayList<TdsTagDto>();
				for (TdsAccountMeter m : mlist) {
					ttlist.add(ObjUtils.copyTo(m, TdsTagDto.class));
				}
				redis.setMapValue(accountKey, redisKey, ttlist);
			}
		}
		return mlist;
	}
	//获取核算对象默认仪表列表，只取采集点，不取lims
	private List<TdsAccountMeter> getMeterList(String unitCode, String rq, List<String> tagIdList) {
		List<TdsAccountMeter> rlist = null;
		if(StringUtils.isEmpty(tagIdList)) {
			rlist = dsaServ.getDefaultAccountUnitTagList(unitCode, rq, "2");
		}else {
			rlist = dsaServ.getDefaultAccountUnitTagList(unitCode, rq, "2", tagIdList);//过滤仪表类型 平稳率、lims 
		}
		return rlist;
	}
	//获取确认数据map
	private Map<String, List<Date>> getConfirmMap(String ds, String rq) {
		Map<String, List<Date>> rmap = new HashMap<String, List<Date>>();
		List<Map> dataMap = getConfirmList(ds, rq);
		if(StringUtils.isNotEmpty(dataMap)) {
			for (Map map : dataMap) {//循环确认主数据
				List<Date> dlist = new ArrayList<Date>();
				String key = getMapStr(map, "rq")+"_"+getMapStr(map, "unitCode")+"_"+getMapStr(map, "bc")+"_"+getMapStr(map, "accountId")+"_"+ds;
				Object cinfo = map.get("info");
				if(cinfo!=null) {
					List<Map> cmap = (List<Map>)cinfo;
					for (Map dmap : cmap) {
						String sj = getMapStr(dmap, "sj");
						if(sj.length() >= 10) {
							Date d = DateTimeUtils.parseDateTime(sj);
							dlist.add(d);
						}
					}
				}
				if(StringUtils.isNotEmpty(dlist)) {
					rmap.put(key, dlist);
				}
			}
		}
		return rmap;
	}
	//获取确认数据
	private List<Map> getConfirmList(String ds, String rq) {
		//确认表数据
		List<Map> cdata = dsaServ.getAccountNewData(accountNewTab+"_confirm_"+ds, null, rq, null, null, null);
		return cdata;
	}
	/**
	 * @category 根据配置获取当前可获取的时间点
	 * @param sbsj
	 * @param xbsj
	 * @param timeConf
	 * @param nd
	 * @return
	 */
	private List<Date> getPos(String sbsj, String xbsj, TdsAccountTime timeConf, Date nd) {
		List<Date> rlist = new ArrayList<Date>();
		String step = timeConf.getTimeStep();
		Boolean haveStart = new Integer(1).equals(timeConf.getStartRound());
		Boolean haveEnd = new Integer(1).equals(timeConf.getEndRound());
		String startFix = timeConf.getStartFixed();
		String endFix = timeConf.getEndFixed();
		int istartfix = 0;
		int iendfix = 0;
		int istep = 120;
		if(Coms.judgeLong(step)) {
			istep = Integer.parseInt(step);
		}
		if(Coms.judgeLong(startFix)) {
			istartfix = Integer.parseInt(startFix);
		}
		if(Coms.judgeLong(endFix)) {
			iendfix = Integer.parseInt(endFix);
		}
		
		if(Coms.judgeLong(step)) {
			Date st = DateTimeUtils.parseDateTime(sbsj);
			Date et = DateTimeUtils.parseDateTime(xbsj);
			if(istartfix > 0) {
				st = DateTimeUtils.doMinute(st, istartfix);
			}
			if(iendfix > 0) {
				et = DateTimeUtils.doMinute(et, iendfix);
			}
			if(haveStart) {
				rlist.add(st);
			}
			Date tempDate = DateTimeUtils.doMinute(st, istep);
			for (int i = 0,il=50; i < il && DateTimeUtils.bjDate(et, tempDate) > 0; i++) {//时间对比，增加循环数，避免死循环
				if(DateTimeUtils.bjDate(tempDate, nd)>0) {
					break;
				}
				rlist.add((Date)tempDate.clone());
				tempDate = DateTimeUtils.doMinute(tempDate, istep);
			}
			
			if(DateTimeUtils.bjDate(tempDate, nd)<0 && haveEnd && DateTimeUtils.bjDate(et, tempDate) >= 0) {
				rlist.add((Date)tempDate.clone());
			}
		}
		return rlist;
	}

	/**
	 * @category 获取核算对象相关机构，并获取日期段班次信息
	 * @param unitCode
	 * @param ksrq
	 * @param jzrq
	 * @return
	 */
	private List<ShiftForeignVo> getUnitShiftList(String unitCode, String ksrq, String jzrq) {
		MethodQueryDto qdto = new MethodQueryDto();
		qdto.setUnitid(unitCode);// 核算对象ID
		qdto.setObjType("org");
		List<Costunitoperator> listCzjg = unitMeth.getCostunitoperatorList(qdto);
		List<String> listOrg = new ArrayList<String>();
		if (StringUtils.isNotEmpty(listCzjg)) {
			for (Costunitoperator temp : listCzjg) {
				listOrg.add(temp.getObjid());
			}
		}
		List<ShiftForeignVo> listShift = new ArrayList<ShiftForeignVo>();
		if(StringUtils.isNotEmpty(listOrg)) {
			listShift = shift.getShiftDataByksrqjzrq(listOrg, ksrq, jzrq);
		}
		return listShift;
	}

	/**
	 * @category 获取所有台账用的核算对象
	 * @return
	 */
	private List<Costuint> getAllCostunit() {
		//获取核算对象列表
		CostDto cdto = new CostDto();
		cdto.setIsLedgerEntry(true);
		List<Costuint> unitList = costService.getData(cdto);
		
		if(StringUtils.isNotEmpty(unitList)) {
			for (Iterator iter = unitList.iterator(); iter.hasNext();) {
				Costuint unit = (Costuint) iter.next();
				if(!new Integer(1).equals(unit.getLedgerEntry()) || !new Integer(1).equals(unit.getTmused())) {
					iter.remove();
				}
			}
		}
		
		return unitList;
	}
	/**
	 * @category 获取所有自定义台账
	 * @return
	 */
	private List<TdsAccountForm> getCustomList() {
		Where where = Where.create();
		where.eq(TdsAccountForm::getTmused, 1);
		where.notEmpty(TdsAccountForm::getFormCode);
		where.notEmpty(TdsAccountForm::getUnitCode);
		Order order = Order.create();
		order.order(TdsAccountForm::getUnitCode);
		List<TdsAccountForm> rlist = srv.queryData(TdsAccountForm.class, where, order, null);
		return rlist;
	}
	/**
	 * @category 整理同核算对象的自定义表单
	 */
	private List<String> getCustomUnitList(List<TdsAccountForm> customList) {
		List<String> rlist = new ArrayList<String>();
		for (TdsAccountForm obj : customList) {
			String us = obj.getUnitCode();
			if(us != null) {
				us = us.replace("[", "").replace("]", "").replace("\"", "").replace(" ", "");
				if(us.length() > 0) {
					List<String> slist = Coms.StrToList(us, ",");
					for (String s : slist) {
						if(!rlist.contains(s)) {
							rlist.add(s);
						}
					}
				}
			}
		}
		return rlist;
	}
	/**
	 * @category 根据表单id获取台账数据源相关信息
	 * @param formId
	 * @param amap Map<formId, dsList>
	 * @param dsMap Map<ds, List>
	 */
	private void getFormAccountTds(List<SFForm> formList, Map<String, List<String>> amap, Map<String, List> dsMap) {
		/*
		 * 循环表单，获取表单相关数据源台账，获取台账配置
		 */
		
//		Map<String, List> dsMap = new HashMap<String, List>();
		List<String> dlist = new ArrayList<String>();
		
		for (SFForm form : formList) {
			List<SFFormComponentBean> vlist = formService.getFormAllComp(form.getId(), null, true);
			String formId = form.getId();
			if(StringUtils.isNotEmpty(vlist)) {
				for (SFFormComponentBean bean : vlist) {//循环表单组件
					if("tds".equals(bean.getRenderType())) {//数据源组件
						String ds = bean.getFormComponent();//数据源别名
						//整理所有平稳率数据源信息
						if(!dsMap.containsKey(ds) && !dlist.contains(ds)) {//如果已处理过相关数据源，不再进行处理
							TdataSource tds = StringUtils.isEmpty(ds) ? null : tdsSrv.getTDataSource(ds);
							if(tds!=null && "TDSAccount".equalsIgnoreCase(tds.getTdsclassName())) {//台账数据源
								TdsAccountConf conf = dsaServ.getAccountConf(ds);//配置信息
								String tagType = conf.getTagType();
								if("2".equals(tagType)) {//只考虑平稳率时间点数据
									TdsAccountTime timeConf = dsaServ.getTimeConf(ds);//时间配置
									List tlist = new ArrayList(2);
									tlist.add(conf);
									tlist.add(timeConf);
									dsMap.put(ds, tlist);
								}
							}
						}
						//如果有平稳率数据源信息，进行表单模板保存
						List tlist = dsMap.get(ds);
						if(StringUtils.isNotEmpty(tlist)) {
							if(amap.containsKey(formId)) {
								amap.get(formId).add(ds);
							}else {
								List<String> dsList = new ArrayList<String>();
								dsList.add(ds);
								amap.put(formId,dsList);
							}
						}
						
						
//						if(dlist.contains(ds)) {
//							continue;
//						}
//						if(dsMap.containsKey(ds)) {
//							if(amap.containsKey(form.getId())) {
//								amap.get(form.getId()).add(ds);
//							}else {
//								List<String> dsList = new ArrayList<String>();
//								dsList.add(ds);
//								amap.put(form.getId(),dsList);
//							}
//						}else {
//							TdataSource tds = StringUtils.isEmpty(ds) ? null : tdsSrv.getTDataSource(ds);
//							if(tds!=null && "TDSAccount".equalsIgnoreCase(tds.getTdsclassName())) {//台账数据源
//								TdsAccountConf conf = dsaServ.getAccountConf(ds);//配置信息
//								String tagType = conf.getTagType();
//								if("2".equals(tagType)) {//只考虑平稳率时间点数据
//									TdsAccountTime timeConf = dsaServ.getTimeConf(ds);//时间配置
//									List tlist = new ArrayList(2);
//									tlist.add(conf);
//									tlist.add(timeConf);
//									dsMap.put(ds, tlist);
//									
//									if(amap.containsKey(form.getId())) {
//										amap.get(form.getId()).add(ds);
//									}else {
//										List<String> dsList = new ArrayList<String>();
//										dsList.add(ds);
//										amap.put(form.getId(),dsList);
//									}
//								}
//							}
//						}
						dlist.add(ds);
					}
				}
			}
		}
	}
	
	private String getMapStr(Map map, String key) {
		String rv = "";
		if(map != null) {
			Object obj = map.get(key);
			if(obj != null) {
				rv = String.valueOf(obj);
			}
		}
		return rv;
	}
	
	/********自动保存待办记录处理函数*******************************************************************************************************************/
	
	
	private Boolean saveAccountASData(List<AccountAutoSaveTodo> list) {
		Boolean flag = 1 == srv.insertBatch(list, 100);
		return flag;
	}
	/**
	 * @category 获取待办数据信息
	 * @param rq
	 * @param unitCode
	 * @param shiftCode
	 * @param accountId
	 * @return
	 */
	private List<AccountAutoSaveTodo> getTodoData(String rq, String unitCode, String shiftCode, String accountId, String ds) {
		List<AccountAutoSaveTodo> rlist = new ArrayList<AccountAutoSaveTodo>();
		Where where = Where.create();
		where.eq(AccountAutoSaveTodo::getTmused, 1);
		where.eq(AccountAutoSaveTodo::getRq, rq);
		if(StringUtils.isNotEmpty(unitCode)) {
			where.eq(AccountAutoSaveTodo::getUnitCode, unitCode);
		}
		if(StringUtils.isNotEmpty(shiftCode)) {
			where.eq(AccountAutoSaveTodo::getShiftCode, shiftCode);
		}
		if(StringUtils.isNotEmpty(accountId)) {
			where.eq(AccountAutoSaveTodo::getCustomCode, accountId);
		}
		if(StringUtils.isNotEmpty(ds)) {
			where.eq(AccountAutoSaveTodo::getTdsAlias, ds);
		}
		Order order = Order.create();
		order.orderByAsc(AccountAutoSaveTodo::getUnitCode).orderByAsc(AccountAutoSaveTodo::getShiftCode).orderByAsc(AccountAutoSaveTodo::getCustomCode);
		rlist = srv.queryData(AccountAutoSaveTodo.class, where, order, null);
		
		return rlist;
	}
	
	@Override
	public Integer getAccountTodoNum(String loginName) {
		Integer num = 0;
		
		SysUser sysUser = SysUserUtil.getCurrentUser();
		if(StringUtils.isNull(sysUser)) {
			sysUser=auser.getUserByLoginName(loginName);
		}
		boolean isAdmin = SysUserUtil.isAdmin();//管理员不显示待办
		if(isAdmin) {
			return num;
		}
		
		List<TdsAccountDataVo> list = getAccountTodoData();
		if(StringUtils.isNotEmpty(list)) {
			Map<String, Integer> todoMap = new HashMap<String, Integer>();
			String rq = list.get(0).getRq();
			String shiftCode = list.get(0).getClsno();
			List<AccountAutoSaveTodo> todoList = getTodoData(rq, null, shiftCode, null, null);
			if(StringUtils.isEmpty(todoList)) {
				return num;
			}else {
				for (AccountAutoSaveTodo todo : todoList) {
					String unitCode = todo.getUnitCode();
					String accountId = todo.getCustomCode()==null?"":todo.getCustomCode();
					todoMap.put(unitCode+"_"+accountId, new Integer(1).equals(todo.getTodoMark())?1:0);
				}
			}
			
			for (TdsAccountDataVo vo : list) {
				String unitCode = vo.getUnitcode();
				String accountId = vo.getCustomformcode()==null?"":vo.getCustomformcode();
				Integer c = todoMap.get(unitCode+"_"+accountId);
				if(c!=null) {
					num = num + c;
				}
			}
		}
		
		return num;
	}
	
	public List<TdsAccountDataVo> getAccountTodoData() {
		List<TdsAccountDataVo> rlist = new ArrayList<TdsAccountDataVo>();
		ReturnObj qxobj = accountServ.getAccountQx(null);
		String accountQx = qxobj.getQx();
		String unitCode = qxobj.getUnitCode();
		Boolean addMark = qxobj.getAddMark();
		Boolean manageMark = qxobj.getManageMark();
		Boolean searchMark = qxobj.getSearchMark();
		
		if(StringUtils.isEmpty(unitCode)) {
			return rlist;
		}
		String currDay = DateTimeUtils.getNowDateTimeStr();
		ShiftForeignVo shift = unititeminfoservice.getShiftInfo(unitCode, currDay);
		
		if(shift == null) {
			return rlist;
		}
		
		String rq = shift.getTbrq();
//		String currDate = currDay;
		String currShiftCode = shift.getShiftClassCode();
		
		SysUser user = SysUserHolder.getCurrentUser();
		AccountParam dto = new AccountParam();
		
		dto.setRq(rq);
		String orgCode = user.getOrgId();
		dto.setOrgCode(orgCode);
		
		dto.setQx(accountQx);
		dto.setAddMark(addMark);
		dto.setManageMark(manageMark);
		dto.setSearchMark(searchMark);
		dto.setShfitClassId(currShiftCode);
		dto.setUnitid(unitCode);
		
		List<ShiftForeignVo> slist = unititeminfoservice.getShiftList(unitCode,rq);
		List<Map<String, String>> paramList = new ArrayList<Map<String, String>>();
		for (ShiftForeignVo vo : slist) {
			Map<String, String> tmap = new HashMap<String, String>();
			tmap.put("orgCode", vo.getOrgCode());
			tmap.put("orgName", vo.getOrgName());
			tmap.put("empCode", vo.getEmpCode());
			tmap.put("shiftClassCode", vo.getShiftClassCode());
			tmap.put("shiftClassName", vo.getShiftClassName());
			tmap.put("tbrq", vo.getTbrq());
			tmap.put("sbsj", vo.getSbsj());
			tmap.put("xbsj", vo.getXbsj());
			paramList.add(tmap);
		}
		dto.setParamList(paramList);
		
		List<Map<String, String>> clsList = dto.getParamList();
		
		
		rlist = accountServ.getAccountManageList(dto);
		
		return rlist;
	}
	
	
}
