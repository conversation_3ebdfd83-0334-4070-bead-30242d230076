package com.yunhesoft.accountTools.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.util.DateUtil;
import com.yunhesoft.accountTools.entity.dto.AccountParam;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerForm;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerModule;
import com.yunhesoft.accountTools.entity.po.TdsAccountFormSfManage;
import com.yunhesoft.accountTools.entity.po.TdsAccountFormSt;
import com.yunhesoft.accountTools.entity.vo.AccountDataVo;
import com.yunhesoft.accountTools.entity.vo.ComboVo;
import com.yunhesoft.accountTools.entity.vo.ReturnObj;
import com.yunhesoft.accountTools.entity.vo.TdsAccountSTVo;
import com.yunhesoft.accountTools.service.IAccountAbnormalService;
import com.yunhesoft.accountTools.service.IAccountToolsService;
import com.yunhesoft.bzjs.entity.dto.BzjsConfirmStatusDto;
import com.yunhesoft.bzjs.entity.vo.BzjsConfirmStatusVo;
import com.yunhesoft.bzjs.service.IBzjsInputService;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.entity.po.JoblistPersonBind;
import com.yunhesoft.leanCosting.productSchedu.service.IProductScheduPlanService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.shift.shift.entity.dto.ShiftForeignDto;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.applyConf.entity.dto.ApplyParams;
import com.yunhesoft.system.applyConf.entity.po.AccoutAutosaveMark;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountForm;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormManage;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormVo;
import com.yunhesoft.system.applyConf.service.IAccountFormService;
import com.yunhesoft.system.applyConf.service.IApplyConfService;
import com.yunhesoft.system.employee.entity.po.SysEmployeeStation;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.mongodb.service.MongoDBService;
import com.yunhesoft.system.org.service.ISysOrgStationService;
import com.yunhesoft.system.tds.entity.po.*;
import com.yunhesoft.system.tds.entity.vo.TdsAccountDataVo;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tds.service.IRtdbService;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaTreeVo;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;
import com.yunhesoft.system.tools.files.service.ISysFileService;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;
import com.yunhesoft.system.tools.todo.entity.dto.ToDoApiDto;
import com.yunhesoft.system.tools.todo.entity.vo.ToDoApiVo;
import com.yunhesoft.system.tools.todo.service.TodoService;
import com.yunhesoft.tmsf.form.entity.dto.SFFormQueryDto;
import com.yunhesoft.tmsf.form.entity.po.SFForm;
import com.yunhesoft.tmsf.form.entity.po.SFFormCategory;
import com.yunhesoft.tmsf.form.entity.vo.SFFormComponentBean;
import com.yunhesoft.tmsf.form.service.IFormCategoryService;
import com.yunhesoft.tmsf.form.service.IFormManageService;
import com.yunhesoft.tmtools.TokenUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.imageio.ImageIO;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Log4j2
@Service
public class AccountToolsServiceImpl implements IAccountToolsService {

	@Autowired
	EntityService srv;

	@Autowired
	private IShiftService shiServ; // 倒班服务类

	@Autowired
    private IFormManageService formService;

	@Autowired
    private IFormCategoryService formCatgoryService;

	@Autowired
    private ICostService costService;

	@Autowired
	private IProductScheduPlanService prodSche;

//	@Autowired
//    private ISysOrgService orgSrv;

	@Autowired
    private IDataSourceAccountService accountServ;

	@Autowired
	private MongoDBService mongoDBServ;

	@Autowired
	private IApplyConfService applySrv;

	@Autowired
	private IAccountFormService aformSrv;

	@Autowired
	private UnitItemInfoService usrv; //

//	@Autowired
//	private UnitItemInfoService unititeminfoservice;

	@Autowired
	private IAccountAbnormalService abnormalSrv;

	@Autowired
	private IFilesInfoService fileSrv;

	@Autowired
	private IDataSourceService tdsSrv;	//数据源

	@Autowired
	private IRtdbService rtdbSrv;

	@Autowired
	private ISysConfigService sysConfigService;

	@Autowired
	private ISysFileService fs;

	@Autowired
	private TodoService todoSrv; // 待办服务

	@Autowired
	private IBzjsInputService bzjsSrv; // 班组记事服务
	@Autowired
	private ISysOrgStationService stationSrv; // 工位服务

	private String accountTypeId = "account";

	private final String accountTabName = "accountTab";

	private final String sameSuf = "_附加";
	private final String samePre = "sameadd_";
	private String noBound = "/";

	private String accountQx = null;
	private Boolean addMark = false;//录入
	private Boolean manageMark = false;//补录 管理
	private Boolean searchMark = false;//查询

	/**
	 * @category 根据日期机构获取班次列表
	 */
	@Override
	public List<ComboVo> getShiftClassList(AccountParam dto) {
		List<ComboVo> rlist = new ArrayList<ComboVo>();

		ShiftForeignDto sdto = ObjUtils.copyTo(dto, ShiftForeignDto.class);//orgCode nowDt
		if(StringUtils.isEmpty(sdto.getOrgCode())) {
			SysUser user = SysUserHolder.getCurrentUser();
			sdto.setOrgCode(user.getOrgId());
		}

		List<ShiftForeignVo> list = shiServ.getShiftClassList(sdto);
		if(StringUtils.isNotEmpty(list)) {
			String ndstr = DateTimeUtils.getNowDateStr().substring(0,10);
			String nextndstr = DateTimeUtils.formatDate(DateTimeUtils.doDate(DateTimeUtils.getNowDate(), 1)).substring(0,10);
			Date firstSb = null;//第一个时间
			for (ShiftForeignVo obj : list) {
				ComboVo vo = new ComboVo();
				vo.setLabel(obj.getShiftClassName());
				vo.setValue(obj.getShiftClassCode());
				if(firstSb == null && StringUtils.isNotEmpty(obj.getSbsj())) {
					firstSb = DateTimeUtils.parseDateTime(ndstr + " " + obj.getSbsj());
				}
				String sbsj = "", xbsj = "";
				if(StringUtils.isNotEmpty(obj.getSbsj()) && StringUtils.isNotEmpty(obj.getXbsj())) {
					Date sbdate = DateTimeUtils.parseDateTime(ndstr + " " + obj.getSbsj());
					Date xbdate = DateTimeUtils.parseDateTime(ndstr + " " + obj.getXbsj());
					if(DateTimeUtils.bjDate(firstSb, sbdate) == 1) {//跨夜，日期+1
						sbsj = nextndstr+ " " + obj.getSbsj();
					}else {
						sbsj = ndstr+ " " + obj.getSbsj();
					}

					if(DateTimeUtils.bjDate(firstSb, xbdate) == 1) {//跨夜，日期+1
						sbsj = nextndstr+ " " + obj.getXbsj();
					}else {
						sbsj = ndstr+ " " + obj.getXbsj();
					}
				}

				vo.setStartTimeStr(sbsj);
				vo.setEndTimeStr(xbsj);
				vo.setValue(obj.getShiftClassCode()+","+sbsj+","+xbsj+",");
				rlist.add(vo);
			}
		}

		return rlist;
	}

	/**
	 * @category 获取当前用户当班信息，若不当班则返回空
	 */
	@Override
	public ShiftForeignVo getShiftByDateTime(AccountParam dto) {
		SysUser user = SysUserHolder.getCurrentUser();

		String userId = null;//dto.getUserId();
		String orgCode = dto.getOrgCode();
		String date = dto.getNowDt();
		String currDate = DateTimeUtils.getNowDateTimeStr();
		Boolean usecurr = false;

		orgCode = StringUtils.isEmpty(orgCode) ? user.getOrgId() : orgCode;
		if(StringUtils.isEmpty(date) || date.length() < 19) {
			date = currDate;
			usecurr = true;
		}
//		date = StringUtils.isEmpty(date) ? DateTimeUtils.getNowDateTimeStr() : date;

//		String delayMin = sysConfigSrv.getSysConfig("ledger_class_input_delay");
		ApplyParams param = new ApplyParams();
		param.setApplyAlias("ledger_class_input_delay");
		String delayMin = applySrv.getApplyConfValue(param);

		ShiftForeignVo obj = shiServ.getShiftByDateTime(userId, orgCode, date);

		if(StringUtils.isEmpty(obj.getShiftClassCode()) && Coms.judgeLong(delayMin) && usecurr) {//当班判断时，加延时时间判断，可延时录入
			int dm = Integer.parseInt(delayMin);
			if(dm > 0) {
				String newdate = DateTimeUtils.formatDateTime(DateTimeUtils.doMinute(DateTimeUtils.parseDateTime(currDate), -dm));
				obj = shiServ.getShiftByDateTime(userId, orgCode, newdate);
			}
		}
		return obj;
	}

	@Override
	public ReturnObj getAccountQx(AccountParam dto) {
		accountQx = null;//null 无录入权限 1 班组 2 管理
		String unitCode = "";
		List<TdsAccountForm> flist = getCustomFormList();
		if(StringUtils.isNotEmpty(flist)) {
			String ustr = flist.get(0).getUnitCode();
			if(ustr!=null && ustr.length() > 0) {
				List<String> fulist = Coms.StrToList(ustr.replace("\"", "").replace("[", "").replace("]", ""), ",");
				if(StringUtils.isNotEmpty(fulist)) {
					unitCode = fulist.get(0);
				}
			}
		}else {
			SysUser user = SysUserHolder.getCurrentUser();
			String orgCode = user.getOrgId();
			String userId = user.getId();// 人员ID
			String postId = user.getPostId();
			postId = orgCode + "_" + postId;// 机构ID

			//获取核算对象列表，表单*核算对象显示/ 修改从个性表单配置读取数据，与此数据做兼容
			List<Costuint> ulist = costService.getCostuintListByOrgId(orgCode, 2, true);
			if(StringUtils.isNotEmpty(ulist)) {
				//可能有核算对象没设置管理权限的情况，很少，这里做下完善处理
//				unitCode = ulist.get(0).getId();
//				String qx = costService.userOrgIsManageOrg(unitCode, orgCode);
				String qx =  null;
				for (Costuint costuint : ulist) {
					if(StringUtils.isEmpty(unitCode)) {
						unitCode = costuint.getId();
					}
					qx = costService.userOrgIsManageOrg(unitCode, orgCode,userId,postId);
					System.out.println("默认台账权限："+qx+"核算对象："+unitCode+"机构代码："+orgCode);
					if(qx == null || "".equals(qx) || "other".equals(qx)) {
						continue;
					}else {
						if("manage".equals(qx)) {
							break;
						}
					}
				}
				if("manage".equals(qx)) {
					accountQx = "2";
					addMark = false;
					manageMark = true;
					searchMark = false;
				}else if("operate".equals(qx)) {
					accountQx = "1";
					addMark = true;
					manageMark = false;
					searchMark = false;
				}else {//other
//					accountQx = null;
					accountQx = "1";
					addMark = true;
					manageMark = false;
					searchMark = false;
				}
			}
		}
		ReturnObj robj = new ReturnObj();
		robj.setQx(accountQx);
		robj.setUnitCode(unitCode);
		robj.setAddMark(addMark);
		robj.setManageMark(manageMark);
		robj.setSearchMark(searchMark);

		return robj;
	}
	/**
	 * @category 判断当前用户的管辖权限
	 * @return
	 */
	public String getCurrUserAccountPerm() {
		accountQx = null;//null 无录入权限 1 班组 2 管理
		List<TdsAccountForm> flist = getCustomFormList();
		if(StringUtils.isNotEmpty(flist)) {
			return this.accountQx;
		}else {
			SysUser user = SysUserHolder.getCurrentUser();
			String orgCode = user.getOrgId();
			String userId = user.getId();// 人员ID
			String postId = user.getPostId();
			postId = orgCode + "_" + postId;// 机构ID
			//获取核算对象列表，表单*核算对象显示/ 修改从个性表单配置读取数据，与此数据做兼容
			List<Costuint> ulist = costService.getCostuintListByOrgId(orgCode, 2);
			if(StringUtils.isNotEmpty(ulist)) {
				String unitId = ulist.get(0).getId();
				String qx = costService.userOrgIsManageOrg(unitId, orgCode,userId,postId);
				if("manage".equals(qx)) {
					accountQx = "2";
				}else if("operate".equals(qx)) {
					accountQx = "1";
				}else {//other
//					accountQx = null;
				}
			}
			return accountQx;
		}
	}

	/**
	 * @category 根据日期返回当前人员可录入表单列表
	 * <p>
	 * 1、获取当前人员权限类别、班组或管理人员
	 * 2、获取默认可管理的表单和自定义表单信息
	 * 2、获取当前人所在机构管理的核算对象
	 * 3、表单和核算对象做交叉，排出录入列表
	 * 4、获取当前人员当班信息，取上下班时间，用于传参
	 * </p>
	 */
	@Override
	public List<TdsAccountDataVo> getAccountManageList(AccountParam dto) {

		aformSrv.initDefaultAccount(null);
//		initFormCls();
		TdsAccountForm defaultForm = getCustomDefaultFormList();

		SysUser user = SysUserHolder.getCurrentUser();
		List<TdsAccountDataVo> rlist = new ArrayList<TdsAccountDataVo>();

		String rq = dto.getRq();
		String orgCode = user.getOrgId();
		dto.setOrgCode(orgCode);

		String userPerm = dto.getQx();
		Boolean userAddMark = dto.getAddMark();
		Boolean userMmanageMark = dto.getManageMark();
		Boolean  userSearchMark = dto.getSearchMark();
		String shiftCode = dto.getShfitClassId();
		String randomUnitId = dto.getUnitid();

		userAddMark = userAddMark==null?false:userAddMark;
		userMmanageMark = userMmanageMark==null?false:userMmanageMark;
		userSearchMark = userSearchMark==null?true:userSearchMark;

		List<Map<String, String>> clsList = dto.getParamList();

		Date nowDate = DateTimeUtils.getNowDate();
//		Date nowDay = DateTimeUtils.getND();

		ShiftForeignVo vshift = usrv.getShiftInfo(randomUnitId, DateTimeUtils.formatDateTime(nowDate));

		Date lastDate = DateTimeUtils.parseDate(vshift.getXbsj().substring(0, 10));
		Date cd = DateTimeUtils.parseDate(rq);

		if(DateTimeUtils.bjDate(cd, lastDate) == 1) {//选择日期大于当前日期，不返回任何信息
			return rlist;
		}

		/*
		 * 获取当前人员关联的所有台账表单
		 * 获取当前人员关联的自定义台账
		 * 两个表单根据核算对象做交集，以自定义台账为优先
		 * 默认台账，根据机构判断身份（班组、管理），管理根据核算对象获取班次信息，列出台账数据
		 * 默认台账下，班组判断当前是否当班，获取数据
		 * 若有自定义台账，根据自定义台账获取身份（人员、岗位、机构顺序优先），剩余判断同上
		 */

		//推班次信息
		ShiftForeignVo shift = null;
		ShiftForeignVo currshift = null;//本班对应日期的倒班信息
		Boolean localshift = false;//是否本班组
		if("2".equals(userPerm)) {
			if(StringUtils.isEmpty(shiftCode)) {//管理身份，无班次，不显示数据
				return rlist;
			}else {
				//根据核算对象，日期和班次代码，获取当班数据
				List<ShiftForeignVo> shiftList = prodSche.getshiftList(randomUnitId, rq, shiftCode);
				if(StringUtils.isNotEmpty(shiftList)) {
					ShiftForeignVo _shift = shiftList.get(0);
					if(DateTimeUtils.bjDate(nowDate, DateTimeUtils.parseDateTime(_shift.getSbsj())) == 1) {//当前时间要大于上班时间，才可进行补录
						shift = _shift;
					}
				}
			}

			//权限判断
			if(userAddMark || userMmanageMark) {
				userAddMark = true;
				userMmanageMark = true;
				userSearchMark = true;
			}else {
				userSearchMark = true;
			}
		}else {
			if(StringUtils.isEmpty(clsList)) {
				return rlist;
			}else {
				for (Map<String, String> map : clsList) {
					String shiftClassCode = map.get("shiftClassCode");
//					String shiftClassName = map.get("shiftClassName");
					List<ShiftForeignVo> shiftList = prodSche.getshiftList(randomUnitId, rq, shiftClassCode);//usrv.getShift(randomUnitId, rq, shiftClassCode);//
					if(StringUtils.isNotEmpty(shiftList)) {
						ShiftForeignVo _shift = shiftList.get(0);
						if(orgCode!=null && orgCode.equals(_shift.getOrgCode())) {//同机构
							if(DateTimeUtils.bjDate(nowDate, DateTimeUtils.parseDateTime(_shift.getSbsj())) == 1) {//当前时间要大于上班时间，才可进行补录
								currshift = _shift;
							}
							break;
						}
					}
				}
				List<ShiftForeignVo> shiftList = prodSche.getshiftList(randomUnitId, rq, shiftCode);
				if(StringUtils.isNotEmpty(shiftList)) {
					ShiftForeignVo _shift = shiftList.get(0);
					if(DateTimeUtils.bjDate(nowDate, DateTimeUtils.parseDateTime(_shift.getSbsj())) == 1) {//当前时间要大于上班时间，才可进行显示
						shift = _shift;
					}
				}

				if(userAddMark || userMmanageMark) {
					userSearchMark = true;
				}
			}
		}

		if(shift == null) {
			return rlist;
		}
		//当前班组与本班班组相同
		if(currshift!=null && StringUtils.isNotEmpty(currshift.getOrgCode()) && currshift.getOrgCode().equals(shift.getOrgCode())) {
			localshift = true;
		}

		//获取核算对象列表，表单*核算对象显示/ 修改从个性表单配置读取数据，与此数据做兼容
		List<Costuint> ulist = costService.getCostuintListByOrgId(orgCode, 2, true);

		Collections.sort(ulist, (c1, c2) -> c1.getTmSort().compareTo(c2.getTmSort()));

		List<String> ucodelist = new ArrayList<String>();
		for (Costuint unit : ulist) {
			ucodelist.add(unit.getId());
		}
		//获取有效的自定义台账，用于去除默认台账
		List<TdsAccountForm> customList = getActiveCustomForm(ucodelist);

		//获取个性表单信息数据，先判断权限，如果管理人员，查各机构自己的自定义台账，如果是班组人员，如果本班组全查，如果是其他班组，查班组自己的自定义台账
		List<TdsAccountForm> flist = null;
		if("2".equals(userPerm)) {//管理人员按自定义设置为准
			flist = getCustomFormList();
		}else {//倒班人员查对应机构自定义台账
			flist = getCustomFormList(userPerm, localshift, shift.getOrgCode());
		}

		//获取当前人员管理的所有表单数据
		SFFormQueryDto formdto = new SFFormQueryDto();
		formdto.setCategory(this.accountTypeId);//台账分类
		formdto.setFilterByPerm(true);//个人相关的
		List<SFForm> formList = formService.queryFormInfoList(formdto, null);
		//获取默认表单，如果无设置，按原逻辑进行交叉显示
		if(defaultForm!=null) {
			String formName = defaultForm.getAccountName();
			String formid = defaultForm.getFormCode();
			if(StringUtils.isNotEmpty(formid)) {//有默认表单，只保留默认表单并赋予名称
				for (Iterator iter = formList.iterator(); iter.hasNext();) {
					SFForm sfForm = (SFForm) iter.next();
					if(formid.equals(sfForm.getId())) {
						sfForm.setName(formName);
					}else {
						iter.remove();
					}
				}
			}
		}

		if(StringUtils.isNotEmpty(customList)) {//如果有自定义表单，需要清除默认台账中核算对象相同的台账
			for (TdsAccountForm form : customList) {
				//同表单暂不清理
//				String formid = form.getFormCode();
//				for (SFForm f : formList) {
//					if(f.getId().equals(formid)) {
//						formList.remove(f);
//						break;
//					}
//				}
				List<String> fulist = Coms.StrToList(form.getUnitCode().replace("\"", "").replace("[", "").replace("]", ""), ",");
				if(StringUtils.isNotEmpty(fulist)) {
					for (String funitcode : fulist) {
						for (Costuint u : ulist) {
							if(u.getId().equals(funitcode)) {
								ulist.remove(u);
								break;
							}
						}
					}
				}
			}
		}

		//如果有自定义台账，或者有表单和对应核算对象数据，进行整理台账列表
		if((StringUtils.isNotEmpty(formList) && StringUtils.isNotEmpty(ulist)) || StringUtils.isNotEmpty(flist)) {

			String bcorg = shift.getOrgCode();//班次对应的机构代码
			//如果是管理人员，根据班次、日期、获取交接班时间

			//已保存数据
			Where where = Where.create();
			where.eq(TdsAccountData::getTmused, 1);
			where.eq(TdsAccountData::getRq, rq);
			where.eq(TdsAccountData::getClsno, shift.getShiftClassCode());
			where.eq(TdsAccountData::getOrgcode, bcorg);
			List<TdsAccountData> dataList = srv.queryList(TdsAccountData.class, where, null);
			Map<String, TdsAccountData> dataMap = new HashMap<String, TdsAccountData>();
			Map<String, TdsAccountData> dataFormMap = new HashMap<String, TdsAccountData>();
			if(StringUtils.isNotEmpty(dataList)) {
				for (TdsAccountData data : dataList) {
					dataMap.put(data.getFormid()+"_"+data.getUnitcode(), data);
					dataFormMap.put(data.getCustomformcode(), data);
				}
			}

//			//判断班组用户只有录入权限情况下，录入只显示在当班时间内
//			Boolean lrmark = false;
//			Boolean cxmark = false;
//			if(userMmanageMark) {//补录
//				lrmark = true;
//				cxmark = true;
//			}else if(userAddMark) {//当班录入
//				Date sbdate = DateTimeUtils.parseDateTime(shift.getSbsj());
//				Date xbdate = DateTimeUtils.parseDateTime(shift.getXbsj());
//
//				//下班推迟时间
//				ApplyParams param = new ApplyParams();
//				param.setApplyAlias("ledger_class_input_delay");
//				String delayMin = applySrv.getApplyConfValue(param);
//				if(Coms.judgeLong(delayMin)) {
//					int dm = Integer.parseInt(delayMin);
//					if(dm > 0) {
//						xbdate = DateTimeUtils.doMinute(xbdate, dm);
//					}
//				}
//				if(DateTimeUtils.bjDate(sbdate, xbdate, nowDate)) {//上下班时间内当班
//					lrmark = true;
//				}
//				cxmark = true;
//			}else if(userSearchMark) {//查询权限
//				cxmark = true;
//			}

			Boolean currShiftMark = false;//是否在当前当班时间内
			Date sbdate = DateTimeUtils.parseDateTime(shift.getSbsj());
			Date xbdate = DateTimeUtils.parseDateTime(shift.getXbsj());
			//下班推迟时间
			ApplyParams param = new ApplyParams();
			param.setApplyAlias("ledger_class_input_delay");
			String delayMin = applySrv.getApplyConfValue(param);
			if(Coms.judgeLong(delayMin)) {
				int dm = Integer.parseInt(delayMin);
				if(dm > 0) {
					xbdate = DateTimeUtils.doMinute(xbdate, dm);
				}
			}
			if(DateTimeUtils.bjDate(sbdate, xbdate, nowDate)) {//上下班时间内当班
				currShiftMark = true;
			}


			List<SysEmployeeStation> slist = stationSrv.getEmpStation(user.getId());
			Boolean haveUserStation = StringUtils.isNotEmpty(slist);//人员有工位
//			Boolean showDefaultTz = !haveUserStation || (haveUserStation && StringUtils.isEmpty(customList));

			//默认台账数据生成
			if(StringUtils.isNotEmpty(formList) && StringUtils.isNotEmpty(ulist)) {//循环表单
				//默认台账按登录人权限判断
				Boolean lrmark = false;
				Boolean cxmark = true;//默认都可查询
				if(userMmanageMark) {//补录
					if("2".equals(userPerm)) {
						lrmark = true;
//						cxmark = true;
					}else {
						if(orgCode!=null && orgCode.equals(shift.getOrgCode())) {
							lrmark = true;
//							cxmark = true;
						}
//						else {
//							cxmark = true;
//						}
					}
				}else if(userAddMark) {//当班录入，当班班次对应且在当班时间内
					if(currShiftMark && currshift!=null && StringUtils.isNotEmpty(currshift.getShiftClassCode()) && currshift.getShiftClassCode().equals(shift.getShiftClassCode())) {
						lrmark = true;
					}
//					cxmark = true;
				}
				//工位相关判断
				Map<String, TdsAccountFormSfManage> defaultMap = new HashMap<String, TdsAccountFormSfManage>();
				if(haveUserStation) {
					List<String> stationList = new ArrayList<String>();
					for (SysEmployeeStation sta : slist) {
						stationList.add(sta.getStationCode());
					}
					Map<String, Map<String, TdsAccountFormSfManage>> tmap = getDefaultBindMap(ucodelist, stationList);
					for (Costuint unit : ulist) {//循环核算单元
						Map<String, TdsAccountFormSfManage> fmap = tmap.get(unit.getId());
						for (SFForm form : formList) {
							if(fmap!=null && fmap.containsKey(form.getId())) {
								defaultMap.put(form.getId()+"_"+unit.getId(), fmap.get(form.getId()));
							}
						}
					}
				}

				for (SFForm form : formList) {
					for (Costuint unit : ulist) {//循环核算单元
						String key = form.getId()+"_"+unit.getId();
						if(haveUserStation) {//有个人工位，判断默认台账是否绑定对应工位，进行显示
							TdsAccountFormSfManage sfm = defaultMap.get(key);
							if(sfm!=null) {
								int level = getManageLevel(sfm);//绑定权限等级，判断是否可录入
								TdsAccountData data = dataMap.get(key);
								TdsAccountDataVo vo = null;

								Boolean lrbs = false;
								if(level > 2) {
									lrbs = true;//权限高
								}else if(level == 2 && localshift) {
									lrbs = true;//当班补录
								}else if(level == 1 && currShiftMark && localshift) {
									lrbs = true;//当班录入
								}

								if(data!=null) {
									vo = ObjUtils.copyTo(data, TdsAccountDataVo.class);
									vo.setRq(rq);
									vo.setFormname(form.getName());
									vo.setInputMark(lrbs);
									vo.setSearchMark(cxmark);
								}else {
									vo = new TdsAccountDataVo();
									vo.setRq(rq);
									vo.setFormid(form.getId());
									vo.setFormname(form.getName());
									vo.setTmused(0);
									vo.setDataid(TMUID.getUID());
									vo.setUnitcode(unit.getId());
									vo.setUnitname(unit.getName());
									vo.setOrgcode(shift.getOrgCode());
									vo.setOrgname(shift.getOrgName());
									vo.setSbsjstr(shift.getSbsj());
									vo.setXbsjstr(shift.getXbsj());
									vo.setWriteday(shift.getTjsj());
									vo.setClsno(shift.getShiftClassCode());
									vo.setClsname(shift.getShiftClassName());
									vo.setInputMark(lrbs);
									vo.setSearchMark(cxmark);
								}
								vo.setFormShowName(form.getName());
								rlist.add(vo);
							}
						}else {
							TdsAccountData data = dataMap.get(key);
							TdsAccountDataVo vo = null;
							if(data!=null) {
								vo = ObjUtils.copyTo(data, TdsAccountDataVo.class);
								vo.setRq(rq);
								vo.setFormname(form.getName());
								vo.setInputMark(lrmark);
								vo.setSearchMark(cxmark);
							}else {
								vo = new TdsAccountDataVo();
								vo.setRq(rq);
								vo.setFormid(form.getId());
								vo.setFormname(form.getName());
								vo.setTmused(0);
								vo.setDataid(TMUID.getUID());
								vo.setUnitcode(unit.getId());
								vo.setUnitname(unit.getName());
								vo.setOrgcode(shift.getOrgCode());
								vo.setOrgname(shift.getOrgName());
								vo.setSbsjstr(shift.getSbsj());
								vo.setXbsjstr(shift.getXbsj());
								vo.setWriteday(shift.getTjsj());
								vo.setClsno(shift.getShiftClassCode());
								vo.setClsname(shift.getShiftClassName());
								vo.setInputMark(lrmark);
								vo.setSearchMark(cxmark);
							}
							vo.setFormShowName(form.getName());
							rlist.add(vo);
						}


					}
				}
				defaultMap = null;
			}
			if(StringUtils.isNotEmpty(flist)) {
				for (TdsAccountForm form : flist) {
					//个性化按台账单独进行判断
					Boolean qxmark = "2".equals(form.getQx());
					Boolean lrmark = false;
					Boolean cxmark = true;//默认都可查询
					if(qxmark) {//管理人员 TODO 当前获取班组权限
						lrmark = new Integer(1).equals(form.getAddMark()) || new Integer(1).equals(form.getManageMark());
//						cxmark = true;
					}else {//当班人员，获取班次信息，不对应，不显示
						if(localshift) {
							if(new Integer(1).equals(form.getManageMark())) {//补录
								lrmark = true;
							}else if(new Integer(1).equals(form.getAddMark())) {//当班录入
								if(currShiftMark) {
									lrmark = true;
								}else {
									lrmark = false;
								}
							}
//							cxmark = true;
						}else {
//							cxmark = true;
						}
//						if(!orgCode.equals(shift.getOrgCode())) {//不同机构不显示
//							break;
//						}else {
//							if(new Integer(1).equals(form.getManageMark())) {//补录
//								lrmark = true;
//							}else if(new Integer(1).equals(form.getAddMark())) {//当班录入
//								if(currShiftMark) {
//									lrmark = true;
//								}else {
//									lrmark = false;
//								}
//							}
//							cxmark = true;
//						}
					}

					TdsAccountData data = dataFormMap.get(form.getId());
					TdsAccountDataVo vo = null;
					if(data!=null) {
						vo = ObjUtils.copyTo(data, TdsAccountDataVo.class);
						vo.setRq(rq);
						vo.setFormShowName(form.getAccountName());
						vo.setInputMark(lrmark);
						vo.setSearchMark(cxmark);
					}else {
						vo = new TdsAccountDataVo();
						vo.setRq(rq);
						vo.setFormid(form.getFormCode());
						vo.setFormname(form.getFormName());
						vo.setCustomformcode(form.getId());
						vo.setCustomformname(form.getAccountName());
						vo.setFormShowName(vo.getCustomformname());
						vo.setTmused(1);
						vo.setDataid(TMUID.getUID());
						vo.setUnitcode(form.getUnitCode().replace("\"", "").replace("[", "").replace("]", ""));
						vo.setUnitname(form.getUnitName());
						vo.setOrgcode(shift.getOrgCode());
						vo.setOrgname(shift.getOrgName());
						vo.setSbsjstr(shift.getSbsj());
						vo.setXbsjstr(shift.getXbsj());
						vo.setWriteday(shift.getTjsj());
						vo.setClsno(shift.getShiftClassCode());
						vo.setClsname(shift.getShiftClassName());
						vo.setInputMark(lrmark);
						vo.setSearchMark(cxmark);
					}
					rlist.add(vo);
				}
			}
		}

		return rlist;
	}

	private List<TdsAccountForm> getCustomFormList(String userPerm, Boolean localshift, String orgCode) {
		if(localshift) {
			return getCustomFormList();
		}else {
			SysUser currentUser = SysUserHolder.getCurrentUser();
	        String userId = currentUser.getId();
	        //判断当前用户是否已设置工位
	        List<String> stationList = new ArrayList<String>();
	        List<String> aidList = new ArrayList<String>();//有效的工位设置台账
	        List<String> aidAllList = new ArrayList<String>();//所有工位设置台账
	        Boolean haveStation = false;//自定义台账有工位
	        Boolean haveUserStation = false;//人员有工位
	        Map<String, TdsAccountFormManage> tmap = new HashMap<String, TdsAccountFormManage>();
	        List<SysEmployeeStation> slist = stationSrv.getEmpStation(userId);
	        //查所有设置了工位的自定义台账
	        Where wheret = Where.create();
	        wheret.eq(TdsAccountFormManage::getTmused, 1);
	        wheret.eq(TdsAccountFormManage::getModeType, 4);
	        List<TdsAccountFormManage> flist = srv.queryData(TdsAccountFormManage.class, wheret, null, null);

	        if(StringUtils.isNotEmpty(slist)) {
	        	haveUserStation = true;
	        	for (SysEmployeeStation obj : slist) {
	        		stationList.add(obj.getStationCode());
				}
//	        	//查所有设置了工位的自定义台账
//				Where wheret = Where.create();
//				wheret.eq(TdsAccountFormManage::getTmused, 1);
//				wheret.in(TdsAccountFormManage::getManageCode, stationList.toArray());
//	        	List<TdsAccountFormManage> flist = srv.queryData(TdsAccountFormManage.class, wheret, null, null);
	        	if(StringUtils.isNotEmpty(flist)) {
	        		haveStation = true;
	        		for (TdsAccountFormManage obj : flist) {
	        			if(stationList.contains(obj.getManageCode())) {
	        				if(new Integer(1).equals(obj.getAddMark()) || new Integer(1).equals(obj.getManageMark()) || new Integer(1).equals(obj.getSearchMark())) {//有效数据
	        					aidList.add(obj.getAccountid());
	        					tmap.put(obj.getAccountid(), obj);
	        				}
	        			}
	        			aidAllList.add(obj.getAccountid());
					}
	        	}
	        }else {
	        	for (TdsAccountFormManage obj : flist) {
        			aidAllList.add(obj.getAccountid());
				}
	        }

	        String postId = currentUser.getPostId();
	        String orgId = currentUser.getOrgId();
	        List<String> midList = new ArrayList<>();
	        if (StringUtils.isNotEmpty(userId)) {
	        	midList.add(userId);
	        }
	        if (StringUtils.isNotEmpty(orgId)) {
	        	midList.add(orgId);
	            //岗位
	            if (StringUtils.isNotEmpty(postId)) {
	            	midList.add(orgId + "_" + postId);
	            }
	        }

			List<TdsAccountForm> rlist = new ArrayList<TdsAccountForm>();
			Where where = Where.create();
	        where.eq(TdsAccountFormManage::getTmused, 1);
	        where.in(TdsAccountFormManage::getManageCode, midList.toArray());
	        where.in(TdsAccountFormManage::getModeType, "1", "2", "3");
	        where.and().lb();
	        where.eq(TdsAccountFormManage::getAddMark, 1);
	        where.or();
	        where.eq(TdsAccountFormManage::getManageMark, 1);
	        where.or();
	        where.eq(TdsAccountFormManage::getSearchMark, 1);
	        where.rb();
	        Order order = Order.create();
	        order.order(TdsAccountFormManage::getModeType);
	        List<TdsAccountFormManage> mlist = srv.queryData(TdsAccountFormManage.class, where, order, null);
	        if(StringUtils.isNotEmpty(mlist)) {
	        	List<String> idList = new ArrayList<>();
	        	Map<String, TdsAccountFormManage> mmap = new HashMap<String, TdsAccountFormManage>();
	        	if(!haveUserStation) {
	        		for (TdsAccountFormManage obj : mlist) {
	        			if(!idList.contains(obj.getAccountid())) {
//	        				if(haveStation && aidAllList.contains(obj.getAccountid())) {//如果有工位设置，去掉工位设置相关的台账
//	        					continue;
//	        				}
	        				idList.add(obj.getAccountid());
	        				mmap.put(obj.getAccountid(), obj);
	        			}
	        		}
	        	}
	        	if(haveUserStation && aidList.size() > 0) {//如果有工位设置，加上符合工位设置的台账
	        		for (String id : aidList) {
	        			if(!idList.contains(id)) {
	        				idList.add(id);
	            			mmap.put(id, tmap.get(id));
	        			}
					}
	        	}
	        	if(StringUtils.isNotEmpty(idList)) {
	        		Where wheref = Where.create();
	        		wheref.in(TdsAccountForm::getTmused, 1);
	        		wheref.in(TdsAccountForm::getTypeCode, 1);
	        		wheref.notEmpty(TdsAccountForm::getFormCode);
	        		wheref.in(TdsAccountForm::getId, idList.toArray());
	        		Order orderf = Order.create();
	                orderf.order(TdsAccountForm::getTmsort);
	                List<TdsAccountForm> list = srv.queryData(TdsAccountForm.class, wheref, orderf, null);
	        		if(StringUtils.isNotEmpty(list)) {
	        			for (TdsAccountForm form : list) {
	        				String unitCode = form.getUnitCode();
	        				unitCode = unitCode==null?"":unitCode.replace("\"", "").replace("[", "").replace("]", "");
	        				if("".equals(unitCode)) {
	        					continue;
	        				}
	        				TdsAccountFormManage obj = mmap.get(form.getId());
	        				if(obj!=null) {
	        					form.setQx(obj.getManageBound());
	        					form.setAddMark(obj.getAddMark());
	        					form.setManageMark(obj.getManageMark());
	        					form.setSearchMark(obj.getSearchMark());
	        				}
	        				rlist.add(form);
						}
	        		}
	        	}
	        }
			return rlist;
		}
	}

	private List<TdsAccountForm> getActiveCustomForm(List<String> unitlist) {

//		List<String> ucodelist = new ArrayList<String>();
//		for (Costuint unit : ulist) {
//			ucodelist.add(unit.getId());
//		}

		List<TdsAccountForm> rlist = new ArrayList<TdsAccountForm>();
		Where wheref = Where.create();
		wheref.in(TdsAccountForm::getTmused, 1);//启用
		wheref.in(TdsAccountForm::getTypeCode, 1);//自定义台账
		wheref.notEmpty(TdsAccountForm::getFormCode);//有表单设置
		Order orderf = Order.create();
        orderf.order(TdsAccountForm::getTmsort);
        List<TdsAccountForm> list = srv.queryData(TdsAccountForm.class, wheref, orderf, null);
		if(StringUtils.isNotEmpty(list)) {
			for (TdsAccountForm form : list) {
				String unitCode = form.getUnitCode();
				unitCode = unitCode==null?"":unitCode.replace("\"", "").replace("[", "").replace("]", "");
				if("".equals(unitCode)) {//有核算对象设置
					continue;
				}
				Boolean have = false;
				List<String> tlist = Coms.StrToList(unitCode, ",");
				for (String a : tlist) {
					for (String b : unitlist) {
						if(a.equals(b)) {
							have = true;
							break;
						}
					}
					if(have) {
						break;
					}
				}

				if(have) {
					rlist.add(form);
				}
			}
		}
		return rlist;
	}
	/**
	 * @category 获取自定义表单信息
	 * @return
	 */
	private List<TdsAccountForm> getCustomFormList() {
		SysUser currentUser = SysUserHolder.getCurrentUser();
        if (currentUser == null) {
            return null;
        }
        String userId = currentUser.getId();
        String postId = currentUser.getPostId();
        String orgId = currentUser.getOrgId();
//        boolean isAdmin = SysUserUtil.isAdmin(userId);
        List<String> idList = new ArrayList<>();
        if (StringUtils.isNotEmpty(userId)) {
            idList.add(userId);
        }
        if (StringUtils.isNotEmpty(orgId)) {
            idList.add(orgId);
//            //TODO 根据参数判断机构是否向上查找，先默认向下查询
//            ApplyParams param = new ApplyParams();
//    		param.setApplyAlias("form_manage_mode");
//    		String mode = applySrv.getApplyConfValue(param);
//            if ("1".equals(mode)) {
                //向下查找
//                List<String> parentOrgCode = orgSrv.getParentOrgCode(orgId);
//                if (StringUtils.isNotEmpty(parentOrgCode)) {
//                    idList.addAll(parentOrgCode);
//                }
//            }

            //岗位
            if (StringUtils.isNotEmpty(postId)) {
                idList.add(orgId + "_" + postId);
            }

        }
        if (StringUtils.isEmpty(idList)) {
            return null;
        }

        //判断当前用户是否已设置工位
        List<String> stationList = new ArrayList<String>();
        List<String> aidList = new ArrayList<String>();//有效的工位设置台账
        List<String> aidAllList = new ArrayList<String>();//所有工位设置台账
        Boolean haveStation = false;//有工位台账设置
        Boolean haveUserStation = false;//人员有工位
        Map<String, TdsAccountFormManage> tmap = new HashMap<String, TdsAccountFormManage>();
        List<SysEmployeeStation> slist = stationSrv.getEmpStation(userId);

        //查所有设置了工位的自定义台账
		Where wheret = Where.create();
		wheret.eq(TdsAccountFormManage::getTmused, 1);
		wheret.eq(TdsAccountFormManage::getModeType, 4);
//		wheret.in(TdsAccountFormManage::getManageCode, stationList.toArray());
    	List<TdsAccountFormManage> flist = srv.queryData(TdsAccountFormManage.class, wheret, null, null);

        if(StringUtils.isNotEmpty(slist)) {//人员有工位
        	haveUserStation = true;
        	for (SysEmployeeStation obj : slist) {
        		stationList.add(obj.getStationCode());
			}
        	//查所有设置了工位的自定义台账
//			Where wheret = Where.create();
//			wheret.eq(TdsAccountFormManage::getTmused, 1);
////			wheret.in(TdsAccountFormManage::getManageCode, stationList.toArray());
//        	List<TdsAccountFormManage> flist = srv.queryData(TdsAccountFormManage.class, wheret, null, null);
        	if(StringUtils.isNotEmpty(flist)) {//有工位台账设置
        		haveStation = true;
        		for (TdsAccountFormManage obj : flist) {
        			if(stationList.contains(obj.getManageCode())) {
        				if(new Integer(1).equals(obj.getAddMark()) || new Integer(1).equals(obj.getManageMark()) || new Integer(1).equals(obj.getSearchMark())) {//有效数据
        					aidList.add(obj.getAccountid());

        					//取最大的权限
        					TdsAccountFormManage tm = tmap.get(obj.getAccountid());
        					if(tm!=null) {
        						if("2".equals(obj.getManageBound())) {
        							tm.setManageBound("2");
        						}
        						if(new Integer(1).equals(obj.getAddMark())) {
        							tm.setAddMark(1);
        						}
        						if(new Integer(1).equals(obj.getManageMark())) {
        							tm.setManageMark(1);
        						}
        						if(new Integer(1).equals(obj.getSearchMark())) {
        							tm.setSearchMark(1);
        						}
        					}else {
        						tmap.put(obj.getAccountid(), obj);
        					}
        				}
        			}
        			aidAllList.add(obj.getAccountid());
				}
        	}
        }else {//人员无工位
        	if(StringUtils.isNotEmpty(flist)) {//有工位台账设置
        		haveStation = true;
        		for (TdsAccountFormManage obj : flist) {
        			aidAllList.add(obj.getAccountid());
				}
        	}
        }

        List<TdsAccountForm> rlist = new ArrayList<TdsAccountForm>();
        String qx = null;//1本机构(班组) 2本车间（管理）

        Where where = Where.create();
        where.eq(TdsAccountFormManage::getTmused, 1);
        where.in(TdsAccountFormManage::getManageCode, idList.toArray());
        where.in(TdsAccountFormManage::getModeType, "1", "2", "3");
        where.and().lb();
        where.eq(TdsAccountFormManage::getAddMark, 1);
        where.or();
        where.eq(TdsAccountFormManage::getManageMark, 1);
        where.or();
        where.eq(TdsAccountFormManage::getSearchMark, 1);
        where.rb();
        Order order = Order.create();
        order.order(TdsAccountFormManage::getModeType);
        List<TdsAccountFormManage> mlist = srv.queryData(TdsAccountFormManage.class, where, order, null);
        if(StringUtils.isNotEmpty(mlist)) {
        	idList.clear();
        	Map<String, TdsAccountFormManage> mmap = new HashMap<String, TdsAccountFormManage>();

        	if(!haveUserStation) {//如果没有人员工位设置，按原逻辑走
        		for (TdsAccountFormManage obj : mlist) {
        			if(!idList.contains(obj.getAccountid())) {
//        				if(haveStation && aidAllList.contains(obj.getAccountid())) {//如果有工位设置，去掉工位设置相关的台账
//        					continue;
//        				}
        				idList.add(obj.getAccountid());
        				mmap.put(obj.getAccountid(), obj);
//        			if(qx==null) {
//        				qx = obj.getManageBound();
//        				addMark = new Integer(1).equals(obj.getAddMark());
//        				manageMark = new Integer(1).equals(obj.getManageMark());
//        				searchMark = new Integer(1).equals(obj.getSearchMark());
//        			}else {
//        				if("2".equals(obj.getManageBound())) {
//        					qx = "2";
//        				}
//        			}
        			}
        		}
        	}
        	if(haveUserStation && aidList.size() > 0) {//如果有人员工位设置，只显示符合工位设置的台账
        		for (String id : aidList) {
        			if(!idList.contains(id)) {
        				idList.add(id);
            			mmap.put(id, tmap.get(id));
        			}
				}
        	}
        	if(StringUtils.isNotEmpty(idList)) {
        		Where wheref = Where.create();
        		wheref.in(TdsAccountForm::getTmused, 1);
        		wheref.in(TdsAccountForm::getTypeCode, 1);
        		wheref.notEmpty(TdsAccountForm::getFormCode);
        		wheref.in(TdsAccountForm::getId, idList.toArray());
        		Order orderf = Order.create();
                orderf.order(TdsAccountForm::getTmsort);
                List<TdsAccountForm> list = srv.queryData(TdsAccountForm.class, wheref, orderf, null);
        		if(StringUtils.isNotEmpty(list)) {
        			for (TdsAccountForm form : list) {
        				String unitCode = form.getUnitCode();
        				unitCode = unitCode==null?"":unitCode.replace("\"", "").replace("[", "").replace("]", "");
        				if("".equals(unitCode)) {
        					continue;
        				}
        				rlist.add(form);
        				TdsAccountFormManage obj = mmap.get(form.getId());
        				if(obj!=null) {
        					form.setQx(obj.getManageBound());
        					form.setAddMark(obj.getAddMark());
        					form.setManageMark(obj.getManageMark());
        					form.setSearchMark(obj.getSearchMark());

        					if(!addMark && new Integer(1).equals(obj.getAddMark())) {
        						addMark = true;
        					}
        					if(!manageMark && new Integer(1).equals(obj.getManageMark())) {
        						manageMark = true;
        					}
        					if(!searchMark && new Integer(1).equals(obj.getSearchMark())) {
        						searchMark = true;
        					}

        					if(qx==null) {
        						qx = obj.getManageBound();
//        						addMark = new Integer(1).equals(obj.getAddMark());
//                				manageMark = new Integer(1).equals(obj.getManageMark());
//                				searchMark = new Integer(1).equals(obj.getSearchMark());
        					}else if("2".equals(obj.getManageBound())) {
        						qx = "2";
            				}
        				}
					}
        		}
        	}
        }
        accountQx = qx;
		return rlist;
	}
	/**
	 * @category 获取默认台账表单设置
	 * @return
	 */
	private TdsAccountForm getCustomDefaultFormList() {
		TdsAccountForm defaultForm = null;

		Where wheref = Where.create();
		wheref.in(TdsAccountForm::getTypeCode, 0);
		List<TdsAccountForm> rlist = srv.queryData(TdsAccountForm.class, wheref, null, null);
		if(StringUtils.isNotEmpty(rlist)) {
			defaultForm = rlist.get(0);
		}else {
			//调用初始化默认表单函数，初始化后，关联表单为空

		}

		return defaultForm;
	}

	@Override
	public String saveAccountManageData(AccountParam dto) {
		String msg = "";
		TdsAccountData data = dto.getData();
		//具体时间另外保存
		if(data.getSbsj()==null && StringUtils.isNotEmpty(data.getSbsjstr())) {
			data.setSbsj(DateTimeUtils.parseDateTime(data.getSbsjstr()));
		}
		if(data.getXbsj()==null && StringUtils.isNotEmpty(data.getXbsjstr())) {
			data.setXbsj(DateTimeUtils.parseDateTime(data.getXbsjstr()));
		}

		if(StringUtils.isNotEmpty(data.getId())) {//更新
			TdsAccountData obj = srv.queryObjectById(TdsAccountData.class,  data.getId());
			if(obj!=null) {
				srv.updateById(data);
			}else {
				srv.insert(data);//未找到id数据，说明有问题，暂时做插入操作
			}
		}else {//插入
			data.setId(TMUID.getUID());
//			data.setId(data.getDataid());//保存dataid为主键有问题，多个表单相同日期班次
			data.setTmused(1);
			srv.insert(data);
		}

		return msg;
	}

	/**
	 * 获取所有台账录入数据
	 */
	@Override
	public List<TdsAccountDataVo> getAllAccountManageList(AccountParam dto) {

		initFormCls();
//		SysUser user = SysUserHolder.getCurrentUser();
		List<TdsAccountDataVo> rlist = new ArrayList<TdsAccountDataVo>();

		String rq = dto.getRq();
		String bc = dto.getShfitClassId();
		String unitid = dto.getUnitid();
		String unitname = dto.getUnitname();
//		String orgCode = StringUtils.isEmpty(dto.getOrgCode()) ? user.getOrgId() : dto.getOrgCode();
//		dto.setOrgCode(orgCode);

		//根据核算对象，日期和班次代码，获取当班数据
		ShiftForeignVo shift = null;
		List<ShiftForeignVo> shiftList = prodSche.getshiftList(unitid, rq, bc);
		if(StringUtils.isNotEmpty(shiftList)) {
			shift = shiftList.get(0);
		}else {
			return rlist;
		}

//		String dataId = null;

		//已保存过的信息
		Where where = Where.create();
		where.eq(TdsAccountData::getTmused, 1);
		where.eq(TdsAccountData::getRq, rq);
		where.eq(TdsAccountData::getUnitcode, unitid);
		where.eq(TdsAccountData::getClsno, bc);
		Order order = Order.create();
		order.order(TdsAccountData::getFormid);
		List<TdsAccountData> dataList = srv.queryList(TdsAccountData.class, where, order);

		Map<String, TdsAccountData> dmap = new HashMap<String, TdsAccountData>();
		for (TdsAccountData obj : dataList) {
			dmap.put(obj.getFormid(), obj);
//			dataId = obj.getDataid();
		}
		//当前人员可管理表单
		SFFormQueryDto formdto = new SFFormQueryDto();
		formdto.setCategory(this.accountTypeId);//台账分类
		formdto.setFilterByPerm(true);//个人相关的
		List<SFForm> formList = formService.queryFormInfoList(formdto, null);

//		dataId = dataId == null? TMUID.getUID() : dataId;
		//数据整合
		if(StringUtils.isNotEmpty(formList)) {
			for (SFForm form : formList) {
				TdsAccountDataVo robj = null;
				if(dmap.containsKey(form.getId())) {
					TdsAccountData data = dmap.get(form.getId());
					robj = ObjUtils.copyTo(data, TdsAccountDataVo.class);
				}else {
					robj = new TdsAccountDataVo();
					robj.setId("");
					robj.setDataid(TMUID.getUID());
					robj.setRq(shift.getTjsj());
					robj.setWriteday(shift.getTbrq());
					robj.setClsno(shift.getShiftClassCode());
					robj.setClsname(shift.getShiftClassName());
					robj.setUnitcode(unitid);
					robj.setUnitname(unitname);
					robj.setOrgcode(shift.getOrgCode());
					robj.setOrgname(shift.getOrgName());
					robj.setSbsjstr(shift.getSbsj());
					robj.setXbsjstr(shift.getXbsj());
					robj.setFormid(form.getId());
					robj.setFormname(form.getName());
				}

				rlist.add(robj);
			}
		}

		return rlist;
	}
	@Override
	public List<TdsAccountDataVo> saveAccountData(AccountParam dto) {
		List<TdsAccountDataVo> rlist = new ArrayList<TdsAccountDataVo>();
		TdsAccountData data = dto.getData();
		if(data!=null) {
			//检测dataId是否保存过，没保存过进行保存，否则不处理
			//查询已有数据
			Where where = Where.create();
			where.eq(TdsAccountData::getTmused, 1);
			where.eq(TdsAccountData::getDataid, data.getDataid());
			List<TdsAccountData> dataList = srv.queryList(TdsAccountData.class, where, null);
			if(StringUtils.isEmpty(dataList)) {
				data.setId(TMUID.getUID());
				data.setTmused(1);
				srv.insert(data);
			}
		}
		return rlist;
	}
	/*
	 * 初始化表单类别
	 */
	@Override
	public void initFormCls() {
		List<SFFormCategory> clist = formCatgoryService.getAllSubCategoryList(this.accountTypeId);
		if(StringUtils.isEmpty(clist)) {
			SFFormCategory category = new SFFormCategory();
			category.setCategoryName("台账应用类别");
			category.setCategoryValue(this.accountTypeId);
			formCatgoryService.saveFormCategory(category);
		}
	}

	/**
	 * 获取班长工作台账列表
	 * dataId: _this.id, 数据ID暂时没什么用
        rq: _this.summaryDay, 日期
        shfitClassId: _this.shiftId, 班次
        unitid: _this.unitId, 核算单元
        orgCode: _this.teamId, 机构代码
	 *
	 */
	@Override
	public List<TdsAccountDataVo> getClassAccountList(AccountParam dto) {

		aformSrv.initDefaultAccount(null);

		List<TdsAccountDataVo> rlist = new ArrayList<TdsAccountDataVo>();

		TdsAccountData data = dto.getData();
		String unitCode = data.getUnitcode();
		//当前用户岗位含有多个核算对象情况，将所有核算对象写入
//		SysUser user = SysUserHolder.getCurrentUser();
//		String postId = user.getPostId();
		//TODO 岗位获取核算对象列表
		//unitCode = '';
		List<String> unitlist = Coms.StrToList(unitCode, ",");//工作台带入的核算对象信息

		String qx = null;
		Boolean lrmark = false;
		SysUser user = SysUserHolder.getCurrentUser();
		String orgCode = user.getOrgId();
		String userId = user.getId();// 人员ID
		String postId = user.getPostId();
		postId = orgCode + "_" + postId;// 机构ID
		for (String unitId : unitlist) {
			qx = costService.userOrgIsManageOrg(unitId, orgCode,userId,postId);
//			System.out.println("工作台台账权限："+qx+"核算对象："+unitCode+"机构代码："+orgCode);
			if(qx == null || "".equals(qx)) {
				continue;
			}else {
				if("manage".equals(qx)) {
					break;
				}
			}
		}

		if("manage".equals(qx)) {
			lrmark = true;
		}else if("operate".equals(qx)) {//后期优化 TODO
			lrmark = true;
		}else {//other
			lrmark = false;
		}

		boolean isAdmin = SysUserUtil.isAdmin(user.getId());
		if (isAdmin) {
			lrmark = true;
		}

		//查询已有数据
		Where where = Where.create();
		where.eq(TdsAccountData::getTmused, 1);
		where.eq(TdsAccountData::getRq, data.getRq());
		where.eq(TdsAccountData::getClsno, data.getClsno());//可能出现两个部门（装置）用同一套班次情况，班次代码相同，无法区分数据
//		where.eq(TdsAccountData::getOrgcode, data.getOrgcode());//增加用班组标识做过滤
		if(unitlist.size() == 1) {
			where.like(TdsAccountData::getUnitcode, data.getUnitcode());//核算单元过滤
		}else {
			where.and().lb();
			for (int i = 0,il=unitlist.size(); i < il; i++) {
				String ucode = unitlist.get(i);
				if(i == 0) {
					where.like(TdsAccountData::getUnitcode, ucode);
				}else {
					where.and().like(TdsAccountData::getUnitcode, ucode);
				}
			}
			where.rb();
		}

		List<TdsAccountData> dataList = srv.queryList(TdsAccountData.class, where, null);
		Map<List<String>, TdsAccountData> hmap = new HashMap<List<String>, TdsAccountData>();
		Map<String, TdsAccountData> fmap = new HashMap<String, TdsAccountData>();
		if(StringUtils.isNotEmpty(dataList)) {//TODO 如何多核算对象合一（在一张表单），根据配置进行处理
			for (TdsAccountData obj : dataList) {
				List<String> _list = new ArrayList<String>();
				_list.add(obj.getFormid());
				_list.addAll(Coms.StrToList(obj.getUnitcode().replace("\"", "").replace("[", "").replace("]", ""), ","));
				hmap.put(_list, obj);

				fmap.put(obj.getCustomformcode(), obj);
			}
		}

//		TdsAccountForm defaultForm = getCustomDefaultFormList();
		//获取当前人员管理的所有表单数据
		SFFormQueryDto formdto = new SFFormQueryDto();
		formdto.setCategory(this.accountTypeId);//台账分类
		formdto.setFilterByPerm(true);//个人相关的
		List<SFForm> formList = formService.queryFormInfoList(formdto, null);
		//获取默认表单，如果无设置，按原逻辑进行交叉显示
//		if(defaultForm!=null) {
//			String formName = defaultForm.getAccountName();
//			String formid = defaultForm.getFormCode();
//			if(StringUtils.isNotEmpty(formid)) {//有默认表单，只保留默认表单并赋予名称
//				for (Iterator iter = formList.iterator(); iter.hasNext();) {
//					SFForm sfForm = (SFForm) iter.next();
//					if(formid.equals(sfForm.getId())) {
//						sfForm.setName(formName);
//					}else {
//						iter.remove();
//					}
//				}
//			}
//		}

		Boolean haveDefault = true;
		//获取个性表单信息数据
		accountQx = null;
		List<TdsAccountForm> customList = getActiveCustomForm(unitlist);
		List<TdsAccountForm> flist = getCustomFormList();
		if(isAdmin) {
			flist = customList;
		}
		if(StringUtils.isNotEmpty(flist)) {//如果有自定义表单，需要判断对应的核算对象是否在传入的核算对象中
			for (Iterator iter = flist.iterator(); iter.hasNext();) {
				TdsAccountForm form = (TdsAccountForm) iter.next();
				List<String> fulist = Coms.StrToList(form.getUnitCode().replace("\"", "").replace("[", "").replace("]", ""), ",");
				if(StringUtils.isNotEmpty(fulist)) {
					fulist.retainAll(unitlist);
					if(StringUtils.isNotEmpty(fulist)) {
						//保留自定义表单，目前工作台查单核算对象，有自定义台账，不显示默认台账
						haveDefault = false;
					}else {//核算对象对不上
						iter.remove();
					}
				}else {
					iter.remove();
				}
			}
		}

//		//班长工作台账暂不判断权限，都可以录入和查询，看后期是否可以做调整 TODO
//		if(StringUtils.isEmpty(flist)) {//无个性化表单，获取默认权限
//			ReturnObj qxobj = getAccountQx(null);//获取当前人员权限
//		}

		if(StringUtils.isNotEmpty(formList) || StringUtils.isNotEmpty(flist)) {

			List<SysEmployeeStation> slist = stationSrv.getEmpStation(user.getId());
			Boolean haveUserStation = StringUtils.isNotEmpty(slist);//人员有工位
//			Boolean showDefaultTz = !haveUserStation || (haveUserStation && StringUtils.isEmpty(customList));

			//有默认核算对象，循环表单
			if(haveDefault) {
				Map<String, TdsAccountFormSfManage> defaultMap = new HashMap<String, TdsAccountFormSfManage>();
				if(haveUserStation) {
					List<String> stationList = new ArrayList<String>();
					for (SysEmployeeStation sta : slist) {
						stationList.add(sta.getStationCode());
					}
					Map<String, Map<String, TdsAccountFormSfManage>> tmap = getDefaultBindMap(unitlist, stationList);
					for (String unit : unitlist) {//循环核算单元
						Map<String, TdsAccountFormSfManage> sfmap = tmap.get(unit);
						for (SFForm form : formList) {
							if(sfmap!=null && sfmap.containsKey(form.getId())) {
								defaultMap.put(form.getId()+"_"+unit, sfmap.get(form.getId()));
							}
						}
					}
				}


				for (SFForm form : formList) {
					if(haveUserStation) {
						String key = form.getId()+"_"+unitCode;//目前工作台账判断默认台账工位显示，只能判断一个核算对象
						TdsAccountFormSfManage sfm = defaultMap.get(key);
						if(sfm!=null) {
//							int level = getManageLevel(sfm);
							TdsAccountDataVo vo = null;//new TdsAccountDataVo();

							TdsAccountData tdata = null;
							List<String> fulist = new ArrayList<String>();
							fulist.add(form.getId());
							fulist.addAll(unitlist);
							for(List<String> hlist : hmap.keySet()) {
								if(hlist.containsAll(fulist) && fulist.containsAll(hlist)) {
									tdata = hmap.get(hlist);
									break;
								}
							}

							if(tdata!=null) {//工作台账对应核算对象的数据是否录入过
								vo = ObjUtils.copyTo(tdata, TdsAccountDataVo.class);
								vo.setFormname(form.getName());
								vo.setFormShowName(form.getName());
							}else {
								if(!lrmark) {//如果没有录入，只有查询权限，没录入不显示
									continue;
								}

								vo = ObjUtils.copyTo(data, TdsAccountDataVo.class);
								vo.setId("");
								vo.setDataid(TMUID.getUID());
								vo.setFormid(form.getId());
								vo.setFormname(form.getName());
								vo.setUnitcode(unitCode);//为添加数据的核算对象重新赋值
								vo.setFormShowName(form.getName());
							}
							vo.setInputMark(lrmark);
							vo.setSearchMark(true);

							rlist.add(vo);
						}
					}else {
						TdsAccountDataVo vo = null;//new TdsAccountDataVo();

						TdsAccountData tdata = null;
						List<String> fulist = new ArrayList<String>();
						fulist.add(form.getId());
						fulist.addAll(unitlist);
						for(List<String> hlist : hmap.keySet()) {
							if(hlist.containsAll(fulist) && fulist.containsAll(hlist)) {
								tdata = hmap.get(hlist);
								break;
							}
						}

						if(tdata!=null) {//工作台账对应核算对象的数据是否录入过
							vo = ObjUtils.copyTo(tdata, TdsAccountDataVo.class);
							vo.setFormname(form.getName());
							vo.setFormShowName(form.getName());
						}else {
							if(!lrmark) {//如果没有录入，只有查询权限，没录入不显示
								continue;
							}

							vo = ObjUtils.copyTo(data, TdsAccountDataVo.class);
							vo.setId("");
							vo.setDataid(TMUID.getUID());
							vo.setFormid(form.getId());
							vo.setFormname(form.getName());
							vo.setUnitcode(unitCode);//为添加数据的核算对象重新赋值
							vo.setFormShowName(form.getName());
						}
						vo.setInputMark(lrmark);
						vo.setSearchMark(true);

						rlist.add(vo);
					}
				}
				defaultMap = null;
			}

			//有个性化设置的表单，核算对象能对上，显示个性化表单，消除已有的对应表单和核算对象
			if(StringUtils.isNotEmpty(flist)) {
				for (TdsAccountForm form : flist) {
					String unitcode = form.getUnitCode().replace("\"", "").replace("[", "").replace("]", "");
					List<String> fulist = Coms.StrToList(unitcode, ",");
					if(StringUtils.isNotEmpty(fulist)) {
						if(fulist.containsAll(unitlist)) {
							TdsAccountDataVo vo = null;
							TdsAccountData tdata = fmap.get(form.getId());
							if(tdata!=null) {//工作台账对应核算对象的数据是否录入过
								vo = ObjUtils.copyTo(tdata, TdsAccountDataVo.class);
								vo.setFormShowName(tdata.getCustomformname());
							}else {
								if(!lrmark) {//如果没有录入，只有查询权限，没录入不显示
									continue;
								}
								vo = ObjUtils.copyTo(data, TdsAccountDataVo.class);
								vo.setId("");
								vo.setDataid(TMUID.getUID());
								vo.setFormid(form.getFormCode());
								vo.setFormname(form.getFormName());
								vo.setUnitcode(unitcode);//
								vo.setUnitname(form.getUnitName());//
								vo.setCustomformcode(form.getId());
								vo.setCustomformname(form.getAccountName());
								vo.setFormShowName(form.getAccountName());
							}
							vo.setInputMark(lrmark);
							vo.setSearchMark(true);
							rlist.add(vo);
						}
					}
				}
			}

		}

		return rlist;
	}

	/**
	 * 获取当前人员机构的核算对象列表
	 */
	@Override
	public List<Costuint> getCurrOrgUnitList(AccountParam dto) {

		SysUser user = SysUserHolder.getCurrentUser();
		String orgCode = user.getOrgId();

		//获取核算对象列表
		List<Costuint> ulist = costService.getCostuintListByOrgId(orgCode, 2, true);
		Collections.sort(ulist, (c1, c2) -> c1.getTmSort().compareTo(c2.getTmSort()));
		return ulist;
	}

	/**
	 * @category 清除台账数据
	 */
	@Override
	public Boolean clearTagData(AccountParam dto) {

		//获取数据源名称及参数值
		String tdsAlias = dto.getTdsAlias();
		String rq = null, unitCode = null, sbsj = null, xbsj = null;
		List<Map<String, String>> plist = dto.getParamList();
		if(StringUtils.isNotEmpty(plist)) {
			for (Map<String, String> map : plist) {
				if(map!=null) {
					String name = map.get("name");
					String value = map.get("value");
					if("rq".equals(name)) {
						rq = value;
					}else if("unitCode".equals(name)) {
						unitCode = value;
					}else if("bc".equals(name) && value!=null) {
						String[] ss = value.split(",");
						if(ss.length == 3) {
							sbsj = ss[1];
							xbsj = ss[2];
						}
					}
				}
			}
		}

		if(StringUtils.isEmpty(rq) || StringUtils.isEmpty(sbsj)) {
			return false;//参数不全
		}

		//获取台账数据源时间配置及仪表配置
		TdsAccountConf conf = accountServ.getAccountConf(tdsAlias);//配置信息
		TdsAccountTime timeConf = accountServ.getTimeConf(tdsAlias);//时间配置
		////只有一个核算对象情况
		List<TdsAccountMeter> mlist = accountServ.getAccountMeterList(tdsAlias, unitCode, null);//动态仪表配置
		if(StringUtils.isEmpty(mlist)) {//未设置获取全部仪表
			mlist = getDefaultMeter(tdsAlias, conf, rq, unitCode);
			if(mlist == null) {
				mlist = new ArrayList<TdsAccountMeter>();
			}
		}

		if(StringUtils.isNotEmpty(mlist)) {
			List<String> tagList = new ArrayList<String>();
			for (TdsAccountMeter obj : mlist) {
				if(StringUtils.isNotEmpty(obj.getDatasource())) {
					tagList.add(obj.getDatasource());
				}
			}
			//推时间范围
			Date startDate = DateTimeUtils.parseDateTime(sbsj);
			Date endDate = DateTimeUtils.parseDateTime(xbsj);
			if(conf.getBcDiff()!=null) {//上班时间偏差修正
				int bcdiff = conf.getBcDiff();
				startDate = DateTimeUtils.doMinute(startDate, bcdiff);
				endDate = DateTimeUtils.doMinute(endDate, bcdiff);
			}
			Boolean haveStart = new Integer(1).equals(timeConf.getStartRound());
			Boolean haveEnd = new Integer(1).equals(timeConf.getEndRound());

			//删除仪表、时间范围数据
			deleteMongoDbData(tdsAlias, tagList, startDate, endDate, haveStart, haveEnd);
		}

		return true;
	}

	private void deleteMongoDbData(String tdsAlias, List<String> tagList, Date startDate, Date endDate,
			Boolean haveStart, Boolean haveEnd) {
		Criteria criteria = new Criteria();
		criteria.and("dsAlias").is(this.accountTabName+"_"+tdsAlias);
		criteria.and("col").in(tagList);
		if(haveStart) {
			criteria.and("sjlong").gte(startDate);// 大于等于
		}else {
			criteria.and("sjlong").gt(startDate);
		}
		if(haveEnd) {
			criteria.and("sjlong").lte(endDate);// 小于等于
		}else {
			criteria.and("sjlong").lt(endDate);
		}
		Query query = new Query(criteria);
		List<Map> deleteList = mongoDBServ.find(query, Map.class, this.accountTabName+"_"+tdsAlias);
		mongoDBServ.deleteBatchById(deleteList);
	}

	/**
	 * @category 获取默认仪表配置数据通过sql获取默认仪表信息，目前已改为从当前日期版本的台账仪表数据库中获取默认仪表信息
	 * @param dsAlias
	 * @return
	 */
	private List<TdsAccountMeter> getDefaultMeter(String dsAlias, TdsAccountConf conf, String version, String unitCode) {
		SysUser user = SysUserHolder.getCurrentUser();
		List<TdsAccountMeter> rlist = null;
		Integer mode = 1;
		if(new Integer(2).equals(mode)) {//机构
			List<String> unitList = getOrgUnits(user.getOrgId());//获取机构对应核算单元列表
			if(StringUtils.isNotEmpty(unitList)) {
				//获取对应核算对象对应应用日期
				rlist = getOrgTag(unitList, conf, version);
			}
		}else if(new Integer(1).equals(mode)) {//核算对象
			rlist = getAccountTag(conf, version, unitCode);
		}
		return rlist;
	}

	//获取机构对应核算单元列表
	private List<String> getOrgUnits(String confCode) {
		List<String> unitList = new ArrayList<String>();
		String orgid = confCode;
		String sql = "select id as UNITID from COSTUINT where orgid = '"+orgid+"'"
				+ " union  select UNITID from COSTUNITMANAGER where objid = '"+orgid+"'"
				+ " union select UNITID from COSTUNITOPERATOR where objid = '"+orgid+"'";//语句
		List<Map<String, Object>> list = srv.queryListMap(sql, null);

		for (Map<String, Object> map : list) {
			String unitId = map.get("UNITID") == null?"":String.valueOf(map.get("UNITID"));
			if(unitId.length() > 0) {
				unitList.add(unitId);
			}
		}
		return unitList;
	}

	private List<TdsAccountMeter> getOrgTag(List<String> unitList, TdsAccountConf conf, String version) {
		List<TdsAccountMeter> rlist = new ArrayList<TdsAccountMeter>();
		for (String unitId : unitList) {
			List<TdsAccountMeter> list = null;
			if(conf.getTagType()!=null) {
				list = accountServ.getDefaultAccountUnitTagList(unitId, version, conf.getTagType());//过滤仪表类型 平稳率、lims
			}else {
				list = accountServ.getDefaultAccountUnitTagList(unitId, version);
			}
			if(StringUtils.isNotEmpty(list)) {
				rlist.addAll(list);
			}
		}
		return rlist;
	}

	//获取核算对象默认仪表列表
	private List<TdsAccountMeter> getAccountTag(TdsAccountConf conf, String version, String unitCode) {
		String unitId = unitCode;
		String rq = version;
		List<TdsAccountMeter> rlist = null;//
		if(conf.getTagType()!=null) {
			rlist = accountServ.getDefaultAccountUnitTagList(unitId, rq, String.valueOf(conf.getTagType()));//过滤仪表类型 平稳率、lims
		}else {
			rlist = accountServ.getDefaultAccountUnitTagList(unitId, rq);
		}
		return rlist;
	}

	/**
	 * @category 获取当班人员可录入台账列表数据
	 */
	@Override
	public List<TdsAccountDataVo> getCurrClassAccountList(AccountParam dto) {

		initFormCls();

		SysUser user = SysUserHolder.getCurrentUser();
		List<TdsAccountDataVo> rlist = new ArrayList<TdsAccountDataVo>();

		String rq = dto.getRq();
		String orgCode = StringUtils.isEmpty(dto.getOrgCode()) ? user.getOrgId() : dto.getOrgCode();
		dto.setOrgCode(orgCode);

		//倒班相关参数
		if(StringUtils.isNotEmpty(dto.getRq())) {
			dto.setNowDt(dto.getRq());
		}

		//获取当前用户所属班次信息，取上下班时间，TODO 后期存在延时录入情况，需要调整为根据当前用户当天班次获取对应上下班时间、
		ShiftForeignVo sf = getShiftByDateTime(dto);
		Integer clsmark = 0;
		String bc = "";
		if(sf!=null && sf.getTbrq()!=null && dto.getRq().equals(sf.getTbrq())) {
			clsmark = 1;
			bc = sf.getShiftClassCode();
		}
		if(StringUtils.isEmpty(bc)) {
			return rlist;
		}
		/*
		 * 获取当前人员关联的所有台账表单
		 * 获取当前参数查询台账保存数据，根据表单标识做对应是否保存过，赋值ID数据
		 *
		 */

		//获取当前人员管理的所有表单数据
		SFFormQueryDto formdto = new SFFormQueryDto();
		formdto.setCategory(this.accountTypeId);//台账分类
		formdto.setFilterByPerm(true);//个人相关的
		List<SFForm> formList = formService.queryFormInfoList(formdto, null);//台账表单

		//个人相关表单不为空，循环表单构建输出
		if(StringUtils.isNotEmpty(formList)) {

			//获取人员管理核算对象列表，按表单显示，核算对象获取列表字符串，根据人员ID、岗位、机构获取相关核算对象列表
			List<Costuint> ulist = costService.getCostuintListByOrgId(orgCode, user.getId(), user.getPostId(), 2);
			Collections.sort(ulist, (c1, c2) -> c1.getTmSort().compareTo(c2.getTmSort()));

			String unitCodes = "";
			String unitNames = "";
			List<String> unitlist = new ArrayList<String>();
			for (Costuint unit : ulist) {//TODO 排序在后期做处理
				unitCodes+=","+unit.getId();
				unitNames+=","+unit.getName();
				unitlist.add(unit.getId());
			}
			if(unitCodes.length() > 0) {
				unitCodes = unitCodes.substring(1);
				unitNames = unitNames.substring(1);
			}

			//已保存信息
			Where where = Where.create();
			where.eq(TdsAccountData::getTmused, 1);
			where.eq(TdsAccountData::getRq, rq);
			where.eq(TdsAccountData::getClsno, bc);
			where.eq(TdsAccountData::getOrgcode, orgCode);
			List<TdsAccountData> dataList = srv.queryList(TdsAccountData.class, where, null);
			Map<String, TdsAccountData> dataMap = new HashMap<String, TdsAccountData>();
			Map<List<String>, TdsAccountData> hmap = new HashMap<List<String>, TdsAccountData>();
			if(StringUtils.isNotEmpty(dataList)) {
				for (TdsAccountData data : dataList) {
					dataMap.put(data.getFormid()+"_"+data.getUnitcode(), data);//表单+核算对象 定位已保存过的数据 TODO 考虑多个核算对象情况

					List<String> _list = new ArrayList<String>();
					_list.add(data.getFormid());
					_list.addAll(Coms.StrToList(data.getUnitcode(), ","));
					hmap.put(_list, data);
				}
			}
			//循环表单
			if(StringUtils.isNotEmpty(formList)) {
				for (SFForm form : formList) {
					//判断是否含有这个表单数据
					TdsAccountData data = null;
					List<String> fulist = new ArrayList<String>();
					fulist.add(form.getId());
					fulist.addAll(unitlist);
					for(List<String> hlist : hmap.keySet()) {
						if(hlist.containsAll(fulist)) {
							data = hmap.get(hlist);
							break;
						}
					}

					TdsAccountDataVo vo = null;
					if(data!=null) {
						vo = ObjUtils.copyTo(data, TdsAccountDataVo.class);
						vo.setRq(rq);
						vo.setFormname(form.getName());
						vo.setClsmark(clsmark);
					}else {
						vo = new TdsAccountDataVo();
						vo.setRq(rq);
						vo.setFormid(form.getId());
						vo.setFormname(form.getName());
						vo.setTmused(0);
						vo.setClsmark(clsmark);
						vo.setDataid(TMUID.getUID());
						vo.setUnitcode(unitCodes);
						vo.setUnitname(unitNames);
						vo.setOrgcode(user.getOrgId());
						vo.setOrgname(user.getOrgName());

						if(clsmark == 1) {
							vo.setSbsjstr(sf.getSbsj());
							vo.setXbsjstr(sf.getXbsj());
							vo.setWriteday(sf.getTjsj());
							vo.setClsno(sf.getShiftClassCode());
							vo.setClsname(sf.getShiftClassName());
						}
					}
					rlist.add(vo);
				}
			}
		}


		return rlist;
	}

	@Override
	public String getShowUnitConf(AccountParam dto) {
		ApplyParams param = new ApplyParams();
		param.setApplyAlias("unit_show_mode");
		String unit_show_mode = applySrv.getApplyConfValue(param);
		return unit_show_mode;
	}

	@Override
	public List<SFForm> getAccountFormList(AccountParam dto) {
//		SysUser user = SysUserHolder.getCurrentUser();

		//获取当前人员管理的所有表单数据
		SFFormQueryDto formdto = new SFFormQueryDto();
		formdto.setCategory(this.accountTypeId);//台账分类
//		formdto.setFilterByPerm(true);//个人相关的
		List<SFForm> formList = formService.queryFormInfoList(formdto, null);
		return formList;
	}

	@Override
	public Boolean createUnitPerm(AccountParam dto) {
		List<TdsAccountFormVo> formList = dto.getFormList();
		if(StringUtils.isNotEmpty(formList)) {
			List<String> idList = new ArrayList<String>();
			Map<String, String> umap = new HashMap<String, String>();
			for (TdsAccountFormVo vo : formList) {
				String unitcode = vo.getUnitCode()==null?"":vo.getUnitCode().replace("[", "").replace("]", "").replace("]", "");
				if(unitcode.length() > 0) {
					idList.add(vo.getId());
					umap.put(vo.getId(), unitcode);
				}
			}
			if(StringUtils.isNotEmpty(idList)) {
				//获取表单权限配置，如果没有，进行初始化

				//过滤出对应表单id，根据map获取核算对象，获取管理信息，插入数据，同步管理范围名到表单表中
			}
		}
		return null;
	}

	@Override
	/**
	 * 台账数据源导出
	 */
	public void exportTdsAccountData(String dataId, JSONObject jobj) {
		//
		TdsAccountData data = srv.queryObjectById(TdsAccountData.class, dataId);
		if(data!=null && jobj!=null && StringUtils.isNotEmpty(data.getUnitcode())) {
			String dsAlias = jobj.getString("tdsAlias");
			if(StringUtils.isNotEmpty(dsAlias)) {
				String rq = data.getRq();
				String unitCode = data.getUnitcode();
				String bc = data.getClsno();
				String sbsj = data.getSbsjstr();
				String xbsj = data.getXbsjstr();
				String accountId = data.getCustomformcode();

				exportData(rq, unitCode, sbsj, xbsj, bc, accountId, dsAlias);
			}

		}

	}

	private void exportData(String rq, String unitCode, String sbsj, String xbsj, String bc, String accountId, String dsAlias) {
//		TdsAccountDto param = new TdsAccountDto();
//		param.setTdsAlias(dsAlias);
//		TdsAccountConf conf = accountServ.getAccountConf(dsAlias);//配置信息
//		TdsAccountTime timeConf = accountServ.getTimeConf(dsAlias);//时间配置
//		List<TdsAccountOutparamVo> plist = accountServ.getOutConf(param);//输出配置
//		List<TdsAccountMeter> mlist = null;
//
//		String trq = sbsj.substring(0, 10);
//		Date d = DateTimeUtils.parseDate(rq);
//		long versionLong = d.getTime();
//
//
//		List<String> unitlist = Coms.StrToList(unitCode, ",");
//		if(unitlist.size() > 1) {
//			List<TdsAccountMeter> tlist = new ArrayList<TdsAccountMeter>();
//			for (String ucode : unitlist) {
//				List<TdsAccountMeter> _list = null;
//				if(StringUtils.isNotEmpty(accountId)) {
//					List<TdsAccountFormMeter> fmlist = accountServ.getFormMeterList(accountId, ucode, trq);
//					if(StringUtils.isNotEmpty(fmlist)) {
//						_list = new ArrayList<TdsAccountMeter>();
//						for (TdsAccountFormMeter fmeter : fmlist) {
//							TdsAccountMeter am = ObjUtils.copyTo(fmeter, TdsAccountMeter.class);
//							_list.add(am);
//						}
//					}
//				}
//				if(StringUtils.isEmpty(_list)) {//未设置获取全部仪表
//					_list = getDefaultMeter(dsAlias, ucode, conf);
//				}
//				if(StringUtils.isNotEmpty(_list)) {
//					tlist.addAll(_list);
//				}
//
//			}
//			mlist = tlist;
//		}else {
//			if(StringUtils.isNotEmpty(accountId)) {
//				List<TdsAccountFormMeter> fmlist = accountServ.getFormMeterList(accountId, unitCode, trq);
//				if(StringUtils.isNotEmpty(fmlist)) {
//					mlist = new ArrayList<TdsAccountMeter>();
//					for (TdsAccountFormMeter fmeter : fmlist) {
//						TdsAccountMeter am = ObjUtils.copyTo(fmeter, TdsAccountMeter.class);
//						mlist.add(am);
//					}
//				}
//			}
//			if(StringUtils.isEmpty(mlist)) {//未设置获取全部仪表
//				mlist = getDefaultMeter(dsAlias, unitCode, conf);
//				if(mlist == null) {
//					mlist = new ArrayList<TdsAccountMeter>();
//				}
//			}
//		}
//
//		Map<String, TdsAccountMeter> tagIdObjMap = null;
//		Map<String, String> tagCodeIdMap = null;
//		List<String> colList = null;
//		if(StringUtils.isNotEmpty(mlist)) {
//			tagIdObjMap = new HashMap<String, TdsAccountMeter>();
//			tagCodeIdMap = new HashMap<String, String>();
//			colList = new ArrayList<String>();
//			for (TdsAccountMeter obj : mlist) {
//				tagIdObjMap.put(obj.getTagid(), obj);
//				if(StringUtils.isNotEmpty(obj.getDatasource()) && !tagCodeIdMap.containsKey(obj.getDatasource())) {
//					tagCodeIdMap.put(obj.getDatasource(), obj.getTagid());
//				}
//				colList.add(obj.getTagid());
//			}
//		}
//
//
//		//获取时间配置对应的时间点数据
//		List<Date> timeList = new ArrayList<Date>();
//		Map<Date, String> sjmap = null;//时间列表信息
//		List<String> se = new ArrayList<String>();
//		initTimeConfList(conf, timeConf, rq, sbsj, xbsj, timeList, sjmap, se);
//		//获取累计配置
//		List<TdsAccountCountConfVo> countList = new ArrayList<TdsAccountCountConfVo>();
//		Map<String, String> countMap = new HashMap<String, String>();
//		initCountInfo(mlist, tagIdObjMap, dsAlias, countList, rq, bc, countMap);
//		//根据方案获取仪表上下限，如果仪表有上下限超限标识，不用调用此方法，直接显示数据即可 TODO
//		Map<String, List<Map<String, Object>>> tagFaULmap = new HashMap<String, List<Map<String,Object>>>();
//		if(!"bound".equals(timeConf.getShowMode())) {//lims不取上下限
//			initTagUpLowLimit(unitlist, colList, timeList, tagFaULmap);
//		}
//		//根据时间和列信息，获取备注信息
//		String st = se.get(0);
//		String et = se.get(1);
//		Map<String, String> markMap = new HashMap<String, String>();//备注信息
//		initMarkInfo(dsAlias, st, et, colList, markMap);
//
//
//		//整理输出列
//		if(conf != null && StringUtils.isNotEmpty(plist)) {
//			int colCount = 0;//列计数
//			//获取数据
//			Map<String, Map<Date, String>> dataList = getMongoDBData(dsAlias, unitCode, accountId, sbsj, xbsj, timeConf);
//
//			//判断是否启用多表头显示，启用后按模型模式输出表头信息，否则只显示名称
//			Boolean manyTitleFlag = false;
//			TDataSourceManager dsm = new TDataSourceManager();//试试
//			TdstableInfo tInfo = dsm.getTDSTableInfo(dsAlias);
//			if (tInfo.getIsShowSpanTitle() != null && tInfo.getIsShowSpanTitle() == 1) {
//				manyTitleFlag = true;
//			}
//
//			List<String> mtmode = Coms.StrToList(conf.getTagTitleShowMode(), ",");
//			Map<String, String> unitmap = null;
//			if(mtmode.contains("cost")) {
//				unitmap = getUnitMap();
//			}
//
//			int row = 1;
//			List<String> showList = new ArrayList<String>();
//			List<String> aliasList = new ArrayList<String>();
//
//			List<TdsAccountOutparamVo> titleList = new ArrayList<TdsAccountOutparamVo>();
//			int titleLevel = 1;
//			TdsAccountOutparamVo timeCol = new TdsAccountOutparamVo();
//			TdsAccountOutparamVo confirmCol = new TdsAccountOutparamVo();
//
//			if(new Integer(1).equals(conf.getOutparamMode())) {//仪表为列----------------------------------------------------------------------
//
//				for (TdsAccountOutparamVo outparam : plist) {
//					String showName = outparam.getShowName();
//					if(new Integer(1).equals(outparam.getDynamicMark())) {//动态标识，动态扩展列
//						if(StringUtils.isNotEmpty(mlist)) {
//							for (TdsAccountMeter meter : mlist) {
//								Integer bit = getTagDegit(meter.getDecimalDegit(), conf.getMeterBit());
//								//重复处理
//								String tagNumber = meter.getTagnumber();
//								String tagName = meter.getShowName();
//								String yb = meter.getTagid();
//
//								if(StringUtils.isEmpty(tagName)) {
//									tagName = tagNumber;
//								}
//
//								aliasList.add(yb);
//
//								if(manyTitleFlag && mtmode.size() > 1) {//多层表头
//									String tname = "";
//									if(mtmode.contains("cost")) {
//										String unitcode = meter.getUnitCode() == null ? "" : meter.getUnitCode();
//										if(unitcode!=null && unitmap!=null) {
//											tname = unitmap.get(unitcode) == null ?  "" : unitmap.get(unitcode);
//										}else {
//											tname = "无";
//										}
//									}
//									if(mtmode.contains("zone")) {
//										tname += tname.length() > 0 ? "_" : "";
//										tname += meter.getBelongZone() == null ? "" : meter.getBelongZone();
//									}
//									if(mtmode.contains("dev")) {
//										tname += tname.length() > 0 ? "_" : "";
//										tname += meter.getBelongDev() == null ? "" : meter.getBelongDev();
//									}
//									if(mtmode.contains("tname")) {
//										tname += tname.length() > 0 ? "_" : "";
//										tname += meter.getBelongTag() == null ? "" : meter.getBelongTag();
//									}
//									if(mtmode.contains("name")) {
//										tname += tname.length() > 0 ? "_" : "";
//										tname += tagName == null ? "" : tagName.replace("_", "＿");
//									}
//									if(mtmode.contains("tag")) {
//										tname += tname.length() > 0 ? "_" : "";
//										tname += meter.getTagnumber() == null ? "" : meter.getTagnumber().replace("_", "＿");//表有可能有下划线，做处理
//									}
//									if(mtmode.contains("unit")) {
//										tname += tname.length() > 0 ? "_" : "";
//										tname += meter.getSdunit() == null ? "" : meter.getSdunit();
//									}
//									if(mtmode.contains("uplow")) {
//										String ul = "";
//										//无上下限，显示/
//										if(meter.getUpLimit()==null && meter.getLowerLimit()==null) {
//											ul = this.noBound;
//										}else if(meter.getUpLimit()!=null && meter.getLowerLimit()==null) {//有上限无下限≤上限
//											ul="≤";
//											ul+= meter.getUpLimit().toString();
//										}else if(meter.getUpLimit()==null && meter.getLowerLimit()!=null) {//有下限无上限≥下限
//											ul="≥";
//											ul+= meter.getLowerLimit().toString();
//										}else {//上下限都有
//											ul= meter.getLowerLimit().toString();
//											ul+="~";
//											ul+= meter.getUpLimit().toString();
//										}
//
//										tname += tname.length() > 0 ? "_" : "";
//										tname += ul;
//									}
//									tname = tname == null ? "" : tname;
//
//									tagName = "".equals(tname)? tagName.replace("_", "＿") : tname;
//								}
//
//								showList.add(tagName);
//
//								int lev = tagName.split("_").length;//表头层数
//								if(lev > titleLevel) {
//									titleLevel = lev;
//								}
//
//								Integer width = outparam.getWidth();
//								if(width==null || new Integer(0).equals(width)) {
//									width = meter.getWidth();
//								}
//								if(width==null || new Integer(0).equals(width)) {
//									width = 120;
//								}
//
//								TdsAccountOutparamVo top = new TdsAccountOutparamVo();
//								top.setShowName(tagName);
//								top.setAlias(yb);
//								top.setTmsort(row - 1);
//								top.setWidth(width);
//								top.setAlign(meter.getAlign());
//
//								top.setUpLimit(meter.getUpLimit());
//								top.setLowerLimit(meter.getLowerLimit());
//
//								titleList.add(top);
//								row++;
//							}
//						}
//					}else if(new Integer(1).equals(outparam.getTimeMark()) && new Integer(1).equals(outparam.getVisible())) {//时间列
//						showName = StringUtils.isNotEmpty(showName)? showName : "时间";
//						if(manyTitleFlag) {
//							ApplyParams dto = new ApplyParams();
//							dto.setApplyAlias("ledger_title_name");
//							String timeManyTitle = applySrv.getApplyConfValue(dto);
//
//							Map<String, String> smap = new HashMap<String, String>();
//							List<String> tl = Coms.StrToList(timeManyTitle, ";");
//							if(StringUtils.isNotEmpty(tl)) {
//								for (String str : tl) {
//									String[] s = str.split(",");
//									if(s.length > 1) {
//										smap.put(s[0], s[1]);
//									}
//								}
//							}
//							if(StringUtils.isNotEmpty(timeManyTitle) && smap.size() > 0) {
//
//								String tname = "";
//								if(mtmode.contains("cost") && smap.containsKey("cost")) {
//									tname = smap.get("cost");
//								}
//								if(mtmode.contains("zone") && smap.containsKey("zone")) {
//									tname += tname.length() > 0 ? "_" : "";
//									tname += smap.get("zone");
//								}
//								if(mtmode.contains("dev") && smap.containsKey("dev")) {
//									tname += tname.length() > 0 ? "_" : "";
//									tname += smap.get("dev");
//								}
//								if(mtmode.contains("tname") && smap.containsKey("tname")) {
//									tname += tname.length() > 0 ? "_" : "";
//									tname += smap.get("tname");
//								}
//								if(mtmode.contains("name") && smap.containsKey("name")) {
//									tname += tname.length() > 0 ? "_" : "";
//									tname += smap.get("name");
//								}
//								if(mtmode.contains("tag") && smap.containsKey("tag")) {
//									tname += tname.length() > 0 ? "_" : "";
//									tname += smap.get("tag");
//								}
//								if(mtmode.contains("unit") && smap.containsKey("unit")) {
//									tname += tname.length() > 0 ? "_" : "";
//									tname += smap.get("unit");
//								}
//								if(mtmode.contains("uplow") && smap.containsKey("uplow")) {
//									tname += tname.length() > 0 ? "_" : "";
//									tname += smap.get("uplow");
//								}
//								if(StringUtils.isNotEmpty(tname)) {
//									showName = tname;
//								}
//							}
//						}
//						int lev = showName.split("_").length;//表头层数
//						if(lev > titleLevel) {
//							titleLevel = lev;
//						}
//
//						timeCol.setShowName(showName);
//						timeCol.setAlias("timeMarkCol");
//						timeCol.setTmsort(row - 1);
//						timeCol.setWidth(outparam.getWidth());
//						timeCol.setAlign(outparam.getAlign());
//						timeCol.setFixed(outparam.getFixed());
//
//						if("left".equals(outparam.getFixed()) || "right".equals(outparam.getFixed())) {
//						}else {
//							titleList.add(timeCol);
//							row++;
//						}
//
//					}else if(StringUtils.isNotEmpty(outparam.getColType())) {//自定义列，目前有人员、机构，确认列
//
//						if("rowConfirm".equals(outparam.getColType()) && new Integer(1).equals(outparam.getVisible())) {//行确认类型
//							confirmCol.setShowName(showName);
//							confirmCol.setAlias("rowConfirm");
//							confirmCol.setTmsort(row - 1);
//							confirmCol.setWidth(outparam.getWidth());
//							confirmCol.setAlign("left");
//							confirmCol.setFixed(outparam.getFixed());
//
//							if("left".equals(outparam.getFixed()) || "right".equals(outparam.getFixed())) {
//							}else {
//								titleList.add(confirmCol);
//								row++;
//							}
//						}else {}//其他暂时不考虑
//
//					}else {}//指定列
//				}
//
//				//判断锁定列位置处理
//				if("left".equals(confirmCol.getFixed())) {
//					titleList.add(0, confirmCol);
//				}
//				if("left".equals(timeCol.getFixed())) {
//					titleList.add(0, timeCol);
//				}
//				if("right".equals(timeCol.getFixed())) {
//					titleList.add(timeCol);
//				}
//				if("right".equals(confirmCol.getFixed())) {
//					titleList.add(confirmCol);
//				}
//
//
//				row = 0;//行计数归0
//
//				//表头(含多层)输出处理
//				List<TdsAccountOutparamVo> outList = new ArrayList<TdsAccountOutparamVo>();
//				//表头整理，暂时没加入上下限信息
//				if(titleLevel > 1) {
//					for (TdsAccountOutparamVo obj : titleList) {//整理多层表头，信息转为数组进行记录
//						List<String> tlist = Coms.StrToList(obj.getShowName(), "_");
//						obj.setTlist(tlist);
//					}
//					List<List<Integer>> mergeList = new ArrayList<List<Integer>>();//合并信息记录
//					colCount = titleList.size();//列总数
//					for (int rowno = 0; rowno < titleLevel; rowno++) {//循环行层数
//
//						for (int i = 0; i < colCount; i++) {
//							TdsAccountOutparamVo obj = titleList.get(i);
//							List<String> tlist = obj.getTlist();
//							if("timeMarkCol".equals(obj.getAlias())) {//时间列无合并
//								TdsAccountOutparamVo out = ObjUtils.copyTo(obj, TdsAccountOutparamVo.class);
//								String val = tlist.size() > i ? tlist.get(i) : "时间";
//								out.setVal(val);
//								out.setIsTitle(true);
//								out.setRows(1);
//								out.setCols(1);
//								out.setRowPos(rowno);
//								out.setColPos(i);
//								outList.add(out);
//							}else if("rowConfirm".equals(obj.getAlias())) {//确认列合并
//								if(rowno == 0) {
//									TdsAccountOutparamVo out = ObjUtils.copyTo(obj, TdsAccountOutparamVo.class);
//									String val = obj.getShowName();
//									out.setVal(val);
//									out.setIsTitle(true);
//									out.setRows(1);
//									out.setCols(titleLevel);
//									out.setRowPos(rowno);
//									out.setColPos(i);
//									outList.add(out);
//
//									List<Integer> _list = new ArrayList<Integer>();//srow, erow, scol, ecol
//									_list.add(rowno);
//									_list.add(titleLevel - 1);
//									_list.add(i);
//									_list.add(i);
//									mergeList.add(_list);
//								}
//							}else {//仪表列
//								Boolean inCombine = false;
//								for (List<Integer> hlist : mergeList) {
//									int srow = hlist.get(0);
//									int erow = hlist.get(1);
//									int scol = hlist.get(2);
//									int ecol = hlist.get(3);
//									if(rowno >= srow && rowno <= erow && i >= scol && i<= ecol) {
//										inCombine = true;
//										break;
//									}
//								}
//
//								if(inCombine) {
//									continue;
//								}
//
//								//先判断列合并，无列合并，再判断行合并（下面是空字符），进入单元格判断时，判断是否在合并区域，如果在，直接跳过
//								int colnum = 0, rownum = 0;
//								String val = tlist.size() > i ? tlist.get(i) : "";
//								for (int j = i+1; j < colCount; j++) {
//									TdsAccountOutparamVo nextobj = titleList.get(j);
//									if("timeMarkCol".equals(nextobj.getAlias()) || "rowConfirm".equals(obj.getAlias())) {//时间、确认列直接终止
//										break;
//									}
//
//									List<String> tlist2 = nextobj.getTlist();
//									String nextval = tlist2.get(i) == null ? "" : tlist2.get(i);
//									if(val.equals(nextval)) {
//										colnum++;
//									}else {
//										break;
//									}
//								}
//
//								if(colnum == 0) {//无列合并，判断并处理行
//									for (int nextrowno = rowno; nextrowno < titleLevel; rowno++) {//循环行层数
//
//									}
//								}
//
//								TdsAccountOutparamVo out = ObjUtils.copyTo(obj, TdsAccountOutparamVo.class);
//								out.setVal(val);
//								out.setIsTitle(true);
//								out.setRows(1);
//								out.setCols(1);
//								outList.add(out);
//							}
//						}
//						row++;
//					}
//				}else {//单层表头直接输出行
//					for (TdsAccountOutparamVo obj : titleList) {
//						TdsAccountOutparamVo out = ObjUtils.copyTo(obj, TdsAccountOutparamVo.class);
//						out.setVal(obj.getShowName());
//						out.setIsTitle(true);
//						out.setRows(1);
//						out.setCols(1);
//
//						outList.add(out);
//					}
//					row++;
//				}
//
//				//数据整理
//				if("bound".equals(timeConf.getShowMode())) {//只显示一行数据
//					int rowId = 0;
//					Map<String, Object> rowRawMp = new HashMap<String, Object>();
//					String id = st+","+et;
//
//					for (int i = 0,il=titleList.size(); i < il; i++) {
//						TdsAccountOutparamVo op = titleList.get(i);
//						String alias = op.getAlias();
//						if(alias.startsWith(this.samePre)) {//别名相同增加前缀，获取数据时处理
//							alias = alias.replace(this.samePre, "");
//						}
//						String val = null;
//
//						if(alias.equals("ID")) {
//							val = id;
//						}else if(alias.equals("timeMarkCol")) {
//							val = st.substring(0, 16)+"~"+et.substring(0, 16);
//						}else {
//							val = "";
//							Map<Date, String> sjval = dataList.get(alias);
//							if(sjval!=null) {
//								for (Date dat : sjval.keySet()) {
//									if(val.length() > 0) {
//										val+=",";
//									}
//									String v = sjval.get(dat) == null?"":sjval.get(dat);
//									val = v;
//								}
//							}
//						}
//
//					}
//
//				}else {
//					if(StringUtils.isNotEmpty(sjmap)) {//整理数据成行
//						Map<Date, Map<String, String>> dataMap = new HashMap<Date, Map<String,String>>();
//						for (String tagId : dataList.keySet()) {
//							Map<Date, String> sjval = dataList.get(tagId);
//							if(sjval!=null) {
//								for (Date dt : sjval.keySet()) {
//									String val = sjval.get(dt);
//									if(dataMap.containsKey(dt)) {
//										dataMap.get(dt).put(tagId, val);
//									}else {
//										Map<String, String> _map = new HashMap<String, String>();
//										_map.put(tagId, val);
//										dataMap.put(dt, _map);
//									}
//								}
//							}
//						}
//
//						int rowId = 0;
//
//						String limitColor = conf.getOverLimitColor();
//
//						for (Date date : sjmap.keySet()) {//按时间成行，整理输出行
//							String dateStr = sjmap.get(date);
//							String timeStr = DateTimeUtils.formatDateTime(date);
//							Map<String, String> vmap = dataMap.get(date);
//
//							for (TdsAccountOutparamVo obj : titleList) {//循环列
//								String colid = obj.getAlias();
//
//								String val = null;
//
//								if(colid.equals("ID")) {//主键
//									val = timeStr;
//								}else if(colid.equals("timeMarkCol")) {//时间列
//									val = dateStr;
//								}else {
//									if(vmap!=null) {
//										val = vmap.get(colid);
//									}
//								}
//							}
//						}
//					}
//				}
//
//			}
//		}
	}

//	private List<TdsAccountMeter> getDefaultMeter(String rq, String unitCode, TdsAccountConf conf) {
//		List<TdsAccountMeter> rlist = null;//
//		if(conf.getTagType()!=null) {
//			rlist = accountServ.getDefaultAccountUnitTagList(unitCode, rq, String.valueOf(conf.getTagType()));//过滤仪表类型 平稳率、lims
//		}else {
//			rlist = accountServ.getDefaultAccountUnitTagList(unitCode, rq);
//		}
//		return rlist;
//	}
//
//	/**
//	 * @category 计算时间配置的时间列表
//	 */
//	private void initTimeConfList(TdsAccountConf conf, TdsAccountTime timeConf, String rq, String sbsj, String xbsj, List<Date> timeList, Map<Date, String> sjmap, List<String> se) {
//		//整理时间信息
//
//		String st = null, et = null;
//		int istep = 120;
//		if(timeConf!=null) {
//			Date startDate = null;
//			String startRq = null;
//			String startBind = timeConf.getStartBingDay();
//			String startFix = timeConf.getStartFixed();
//			Boolean haveStart = new Integer(1).equals(timeConf.getStartRound());
//			int istartfix = 0;
//
//			Date endDate = null;
//			String endRq = null;
//			String endBind = timeConf.getEndBingDay();
//			String endFix = timeConf.getEndFixed();
//			Boolean haveEnd = new Integer(1).equals(timeConf.getEndRound());
//			int iendfix = 0;
//			if(Coms.judgeLong(startFix)) {
//				istartfix = Integer.parseInt(startFix);
//			}
//			if(Coms.judgeLong(endFix)) {
//				iendfix = Integer.parseInt(endFix);
//			}
//			//开始时间获取
//			if(StringUtils.isNotEmpty(startBind)) {
//				//绑定台账内部绑定
//				if("day".equals(startBind) || "mon".equals(startBind)) {
//					startRq = rq;
//				}else if("bc".equals(startBind)) {
//					startRq = sbsj;
//				}
//			}
//			if(StringUtils.isEmpty(startRq)) {
//				if(istartfix != 0) {
//					startRq = DateTimeUtils.getNowDateStr().substring(0,10) + " " + timeConf.getStartBingTime()+":00";
//					startDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(startRq), istartfix);
//				}else {
//					startRq = DateTimeUtils.getNowDateStr().substring(0,10) + " " + timeConf.getStartBingTime()+":00";
//					startDate = DateTimeUtils.parseDateTime(startRq);
//				}
//			}else {
//				if("bc".equals(startBind)) {
//					startDate = DateTimeUtils.parseDateTime(startRq);
//					if(conf.getBcDiff()!=null) {//上班时间偏差修正
//						int bcdiff = conf.getBcDiff();
//						startDate = DateTimeUtils.doMinute(startDate, bcdiff);
//					}
//				}else {
//					if(istartfix != 0) {
//						startDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(startRq + " " + timeConf.getStartBingTime()+":00"), istartfix);
//					}else {
//						startDate = DateTimeUtils.parseDateTime(startRq + " " + timeConf.getStartBingTime()+":00");
//					}
//				}
//			}
//			//截止时间获取
//			if(StringUtils.isNotEmpty(endBind)) {
//				//绑定台账内部绑定
//				if("day".equals(endBind) || "mon".equals(endBind)) {
//					endRq = rq;
//				}else if("bc".equals(endBind)) {
//					endRq = xbsj;
//				}
//			}
//			if(StringUtils.isEmpty(endRq)) {
//				if(iendfix != 0) {
//					endRq = DateTimeUtils.getNowDateStr().substring(0,10) + " " + timeConf.getEndBingTime()+":00";
//					endDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(endRq), iendfix);
//				}else {
//					endRq = DateTimeUtils.getNowDateStr().substring(0,10) + " " + timeConf.getEndBingTime()+":00";
//					endDate = DateTimeUtils.parseDateTime(endRq);
//				}
//			}else {
//				if("bc".equals(endBind)) {
//					endDate = DateTimeUtils.parseDateTime(endRq);
//					if(conf.getBcDiff()!=null) {//下班时间偏差修正
//						int bcdiff = conf.getBcDiff();
//						endDate = DateTimeUtils.doMinute(endDate, bcdiff);
//					}
//				}else {
//					if(iendfix != 0) {
//						endDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(endRq + " " + timeConf.getEndBingTime()+":00"), iendfix);
//					}else {
//						endDate = DateTimeUtils.parseDateTime(endRq + " " + timeConf.getEndBingTime()+":00");
//					}
//				}
//			}
//
//			if(DateTimeUtils.bjDate(startDate, endDate) == 1) {//如果时间不对，不获取数据
//			}else {
//
//				Date tempDate = startDate;
//				String timeStep = timeConf.getTimeStep();
//				Integer step = 60;
//				if(Coms.judgeLong(timeStep)) {
//					step = Integer.parseInt(timeStep);
//				}
//				if(haveStart) {
//					timeList.add(startDate);
//					tempDate = DateTimeUtils.doMinute(tempDate, step);
//				}
//
//				for (int i = 0,il=50; i < il && DateTimeUtils.bjDate(endDate, tempDate) > 0; i++) {//时间对比，增加循环数，避免死循环
//					timeList.add((Date)tempDate.clone());
//					tempDate = DateTimeUtils.doMinute(tempDate, step);
//				}
//
//				if(DateTimeUtils.bjDate(tempDate, endDate) == 1) {
//					if(haveEnd || "bound".equals(timeConf.getShowMode())) {
//						if(!timeList.contains(endDate)) {
//							timeList.add(endDate);
//						}
//						if("bound".equals(timeConf.getShowMode())) {
//
//						}else if(timeList.size() > 1) {
//							//需要取最小公约数
//							long diffMin = Math.abs(DateTimeUtils.diffDate(endDate, tempDate, DateTimeUtils.MINITE));
//							int imin = Integer.parseInt(String.valueOf(diffMin));
//							if(imin == 0) {
//								istep = step;
//							}else {
//								istep = commonDivisor(60, imin);//计算最大公约数
//							}
//							st = DateTimeUtils.formatDateTime(timeList.get(0));
//							et = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
//						}
//					}
//				}else if(DateTimeUtils.bjDate(tempDate, endDate) == 0) {
//					//间隔固定
//					if(haveEnd || "bound".equals(timeConf.getShowMode())) {
//						timeList.add(endDate);
//					}
//				}
//
//				if(st == null && timeList.size() > 0) {
//					st = DateTimeUtils.formatDateTime(timeList.get(0));
//					et = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
//					istep = step;
//				}
//
//				if(StringUtils.isNotEmpty(timeList)) {
//					sjmap = new LinkedHashMap<Date, String>();
//					for (Date date : timeList) {
//						sjmap.put(date, DateTimeUtils.format(date, timeConf.getTimeFormat()));
//					}
//				}
//			}
//		}
//		se.add(st);
//		se.add(et);
//	}
//	/**
//	 * @category 初始化数据源行统计信息
//	 */
//	private void initCountInfo(List<TdsAccountMeter> mlist, Map<String, TdsAccountMeter> tagIdObjMap, String dsAlias, List<TdsAccountCountConfVo> countList,
//			String rq, String bc, Map<String, String> countMap) {
//		if(StringUtils.isNotEmpty(mlist)) {//目前先用仪表，后期需要增加自定义输出列的处理 TODO
//			List<String> tagIdList = new ArrayList<String>();
//			for(String tagId : tagIdObjMap.keySet()) {
//				tagIdList.add(tagId);
//			}
//			//根据仪表信息判断，是否有统计配置
//			countList = accountServ.getCountConfList(dsAlias, tagIdList);
//			//有统计信息，获取对应仪表统计数据 MongoDB
//			//判断有无班次信息shiftCode，查询相关班次对应日期数据；如果无班次，直接查日期统计数据
//			for (TdsAccountCountConfVo conf : countList) {
//				List<TdsAccountCountCol> clist = conf.getColList();
//				if(StringUtils.isNotEmpty(clist)) {
//					List<String> colIdList = new ArrayList<String>();
//					for (TdsAccountCountCol cc : clist) {
//						colIdList.add(cc.getColMark());
//					}
//					List<Map> jgmap = accountServ.getAccountCountData(this.accountTabName+"_"+dsAlias+"_count", dsAlias, conf.getId(), rq, bc, colIdList);
//					if(jgmap!=null) {
//						for (Map map : jgmap) {
//							String col = String.valueOf(map.get("col"));//仪表相当于COLID
//							String valstr = String.valueOf(map.get("valstr"));//结果
//							valstr = valstr == null? "" : valstr;
//							countMap.put(conf.getId()+"_"+col, valstr);
//						}
//					}
//				}
//			}
//		}
//	}
//	/**
//	 * @category 获取原因分析信息
//	 */
//	private void initMarkInfo(String dsAlias, String st, String et, List<String> colList, Map<String, String> markMap) {
//		if(StringUtils.isNotEmpty(colList)) {
//			List<TdsAccountMarkinfo> mlist = accountServ.getTdsAccountAllMarkData(dsAlias, st, et, colList);
//			if(StringUtils.isNotEmpty(mlist)) {
//				markMap = new HashMap<String, String>();
//				for (TdsAccountMarkinfo obj : mlist) {
//					markMap.put(obj.getMarkKey(), obj.getMarkInfo());
//				}
//			}
//		}
//	}
//	/**
//	 * @category 获取多方案上下限信息
//	 */
//	private void initTagUpLowLimit(List<String> unitIdList, List<String> colList, List<Date> timeList, Map<String, List<Map<String, Object>>> tagFaULmap) {
//
//		/* 获取所有核算对象列表
//		 * 循环核算对象列表，获取相关所有方案切换数据
//		 * 根据核算对象、方案切换时间、当前显示时间点，获取使用了哪些方案                                 -
//		 * 循环使用方案，根据当前采集点，获取方案仪表上下限，记录仪表对应方案的时间范围和上下限信息
//		 */
//
////		Map<String, List<Map<String, Object>>> jgmap = new HashMap<String, List<Map<String,Object>>>();//仪表对应方案的时间范围和上下限信息
//
//		StringBuffer sb = new StringBuffer();
//		for (String unitId : unitIdList) {
//			sb.append(",'");
//			sb.append(unitId);
//			sb.append("'");
//		}
//		if(sb.length() > 0) {
//			String unitstr = sb.substring(1);
//			String sql = "select STARTDATETIME,ENDDATETIME,PROGRAMID,UNITID from PRODUCTSCHEDU_PLAN_START where UNITID in("+unitstr+") order by UNITID,STARTDATETIME";
//			List<Map<String, Object>> plist = srv.queryListMap(sql, null);
//
//			Map<String, List<Map<String, String>>> umap = new HashMap<String, List<Map<String,String>>>();//核算对象所有方案信息
//
//			Map<String, List<Map<String, String>>> ufamap = new HashMap<String, List<Map<String,String>>>();//核算对象当前时间点范围内容使用的方案
//
//			//按核算对象，整理各核算对象的方案切换数据
//			for (Map<String, Object> map : plist) {
//				String unitId = String.valueOf(map.get("UNITID"));
//				if("null".equalsIgnoreCase(unitId)) {
//					continue;
//				}
//
//				String programId = String.valueOf(map.get("PROGRAMID"));
//				String srq = String.valueOf(map.get("STARTDATETIME"));
//				String erq = String.valueOf(map.get("ENDDATETIME"));
//
//				Map<String, String> tmap = new HashMap<String, String>();
//				tmap.put("srq", srq);
//				tmap.put("erq", erq);
//				tmap.put("programId", programId);
//
//				if(umap.containsKey(unitId)) {
//					umap.get(unitId).add(tmap);
//				}else {
//					List<Map<String, String>> tlist = new ArrayList<Map<String,String>>();
//					tlist.add(tmap);
//					umap.put(unitId, tlist);
//				}
//			}
//
//			for (String unitId : umap.keySet()) {//循环核算对象
//				List<Map<String, String>> list = umap.get(unitId);
//				if(StringUtils.isNotEmpty(list)) {
//					for (Map<String, String> map : list) {//循环核算对象方案
//						String srq = map.get("srq");
//						String erq = map.get("erq");
//						String programId = map.get("programId");
//
//						for(Date d : timeList) {//循环时间点
//							String dstr = DateTimeUtils.formatDateTime(d);
//							if(dstr.compareTo(srq) >= 0 && dstr.compareTo(erq) <= 0) {//根据时间范围判断是否应用方案
//
//								//记录所有核算对象对应的方案信息
//								if(ufamap.containsKey(unitId)) {
//									Map<String, String> famap = new HashMap<String, String>();
//									famap.put("srq", srq);
//									famap.put("erq", erq);
//									famap.put("programId", programId);
//									ufamap.get(unitId).add(famap);
//								}else {
//									List<Map<String, String>> falist = new ArrayList<Map<String,String>>();
//									Map<String, String> famap = new HashMap<String, String>();
//									famap.put("srq", srq);
//									famap.put("erq", erq);
//									famap.put("programId", programId);
//									falist.add(famap);
//									ufamap.put(unitId, falist);
//								}
//
//								break;
//							}
//						}
//					}
//				}
//			}
//
//			//按核算对象加载方案数据，并过滤除当前有的仪表
//			Map<String, Map<String, List<Double>>> hmap = new HashMap<String, Map<String,List<Double>>>();
//			for (String unitId : ufamap.keySet()) {
//				List<Map<String, String>> falist = ufamap.get(unitId);
//
//				for (Map<String, String> fmap : falist) {
//
//					String srq = fmap.get("srq");
//					String erq = fmap.get("erq");
//					String programId = fmap.get("programId");
//
//					String key = unitId+"_"+programId;
//
//					Map<String, List<Double>> map = null;
//					if(hmap.containsKey(key)) {
//						map = hmap.get(key);
//					}else {
//						map = getTagUplowData(unitId, srq, programId, colList);//获取方案相关仪表上下限
//						hmap.put(key, map);
//					}
//					if(map!=null && map.size() > 0) {
//						for (String tagId : map.keySet()) {
//							List<Double> ullist = map.get(tagId);
//
//							Map<String, Object> taginfo = new HashMap<String, Object>();
//							taginfo.put("srq", srq);
//							taginfo.put("erq", erq);
//							taginfo.put("up", ullist.get(0));
//							taginfo.put("low", ullist.get(1));
//
//							List<Map<String, Object>> tlist = tagFaULmap.get(tagId);
//							if(StringUtils.isNotEmpty(tlist)) {
//								tagFaULmap.get(tagId).add(taginfo);
//							}else {
//								List<Map<String, Object>> _list = new ArrayList<Map<String,Object>>();
//								_list.add(taginfo);
//								tagFaULmap.put(tagId, _list);
//							}
//						}
//					}
//				}
//			}
//
//		}
//	}
//
//	/**
//	 * @category 根据核算对象ID、日期、方案ID获取方案上下限信息
//	 */
//	private Map<String, List<Double>> getTagUplowData(String unitId, String rq, String programId, List<String> colList) {
//		Map<String, List<Double>> rmap = new HashMap<String, List<Double>>();
//
//		String faid = programId;
//		String sql = "select a.id,a.DATASOURCE,a.TAGNUMBER,b.KEYLOWLIMIT,b.KEYUPLIMIT,b.OPERATELOWLINIT,b.OPERATEUPLIMIT from " +
//				"(select id,BEGINTIME,CTYPE,DATASOURCE,NAME,PID,SOURCEYPE,TAGNUMBER,SDUNIT,VALRANGE,TMSORT from COSTUNITSAMPLEDOT " +
//				"	where UNITID='"+unitId+"' and BEGINTIME=(select max(BEGINTIME) beginTime from COSTUNITVERSION " +
//				"		where UNITID='"+unitId+"' and BEGINTIME<='"+rq+"') and TMUSED=1) a inner join " +
//				"(select id,iname,KEYUPLIMIT,KEYLOWLIMIT,OPERATEUPLIMIT,OPERATELOWLINIT from PROGRAMINDICATOR where PVID=(" +
//				"select id from PROGRAMVERSION where projectdataid='"+faid+"' and PVERSION=(" +
//				"select max(PVERSION) from PROGRAMVERSION where projectdataid='"+faid+"' and PVERSION<='"+rq+"')) and TMUSED=1) b on a.NAME=b.INAME";
//		List<Map<String, Object>> ullist = srv.queryListMap(sql, null);
//		if(StringUtils.isNotEmpty(ullist)) {
//			for (Map<String, Object> map : ullist) {
//				String tagId = String.valueOf(map.get("id"));
//				if(!colList.contains(tagId)) {
//					continue;
//				}
//				if(StringUtils.isNotEmpty(ullist)) {
//					List<Double> _list = new ArrayList<Double>(4);
//					String up1 = map.get("KEYUPLIMIT") == null? "":String.valueOf(map.get("KEYUPLIMIT"));
//					String low1 = map.get("KEYLOWLIMIT") == null? "":String.valueOf(map.get("KEYLOWLIMIT"));
//					String up2 = map.get("OPERATEUPLIMIT") == null? "":String.valueOf(map.get("OPERATEUPLIMIT"));
//					String low2 = map.get("OPERATELOWLINIT") == null? "":String.valueOf(map.get("OPERATELOWLINIT"));
//					if(Coms.judgeDouble(up1)) {
//						_list.add(Double.parseDouble(up1));
//					}else {
//						_list.add(null);
//					}
//					if(Coms.judgeDouble(low1)) {
//						_list.add(Double.parseDouble(low1));
//					}else {
//						_list.add(null);
//					}
//					if(Coms.judgeDouble(up2)) {
//						_list.add(Double.parseDouble(up2));
//					}else {
//						_list.add(null);
//					}
//					if(Coms.judgeDouble(low2)) {
//						_list.add(Double.parseDouble(low2));
//					}else {
//						_list.add(null);
//					}
//					rmap.put(tagId, _list);
//				}
//			}
//		}
//
//		return rmap;
//	}
//
//	/**
//	 * @category 获取已保存数据
//	 * @return
//	 */
//	private Map<String, Map<Date, String>> getMongoDBData(String dsAlias, String unitId, String accountId, String sbsj, String xbsj, TdsAccountTime timeConf) {//编写独立函数，进行调用 TODO
//
//		Map<String, Map<Date, String>> rmap = new LinkedHashMap<String, Map<Date,String>>();
//		//暂时按仪表为列的模式获取时间范围
//		List<Map> mdata = null;
//		if("bound".equals(timeConf.getShowMode())) {//时间段
//			long slong = DateTimeUtils.parseDateTime(sbsj).getTime();
//			mdata = accountServ.getAccountSaveData(this.accountTabName+"_"+dsAlias, dsAlias, slong, slong);
//		}else {
//			mdata = accountServ.getAccountSaveData(this.accountTabName+"_"+dsAlias, dsAlias, DateTimeUtils.parseDateTime(sbsj).getTime(), DateTimeUtils.parseDateTime(xbsj).getTime());
//		}
//		if(mdata!=null) {
//			mdata.sort(Comparator.comparing(o -> String.valueOf(o.get("val"))));
//
//			for (Map map : mdata) {
//				String col = String.valueOf(map.get("col"));//仪表相当于tagID
//				String tag = String.valueOf(map.get("tag"));
//				String sj = String.valueOf(map.get("sj"));
//				String aid = String.valueOf(map.get("accountId"));
//				String unitCode = String.valueOf(map.get("unitCode"));
//				if(StringUtils.isEmpty(sj)) {//无时间数据，不进行获取
//					continue;
//				}
//				String valstr =  String.valueOf(map.get("valstr"));
//				if("rowConfirm".equals(col)) {
//					if(unitId.equals(unitCode)) {
//						if(StringUtils.isEmpty(accountId)) {
//							if(aid.length() > 0 && !"null".equalsIgnoreCase(aid)) {
//								continue;
//							}
//							valstr = String.valueOf(map.get("creUserName"))+"("+valstr+")";
////							if("true".equals(String.valueOf(map.get("rowConfirmOver")))) {
////								valstr = "<div style='color:red;'>"+String.valueOf(map.get("creUserName"))+"("+valstr+")</div>";
////							}else {
////								valstr = "<div style='color:#1B9A49;'>"+String.valueOf(map.get("creUserName"))+"("+valstr+")</div>";
////							}
//						}else {
//							if(StringUtils.isNotEmpty(aid) && accountId.equals(aid)) {//没有保存自定义表单ID
//								valstr = String.valueOf(map.get("creUserName"))+"("+valstr+")";
////								if("true".equals(String.valueOf(map.get("rowConfirmOver")))) {
////									valstr = "<div style='color:red;'>"+String.valueOf(map.get("creUserName"))+"("+valstr+")</div>";
////								}else {
////									valstr = "<div style='color:#1B9A49;'>"+String.valueOf(map.get("creUserName"))+"("+valstr+")</div>";
////								}
//							}else {
//								continue;
//							}
//						}
//					}else {
//						continue;
//					}
//				}
//
//				if("bound".equals(timeConf.getShowMode())) {//时间段
//						Map<Date, String> _map = new HashMap<Date, String>();
//						_map.put(null, valstr);
//						rmap.put(col, _map);
////					}
//
////					if(StringUtils.isNotEmpty(tag) && isEdit) {
////						//标识记录已保存的数据，用于背景色显示,单独一行的分析仪表暂无用
////						if(saveDataList == null) {
////							saveDataList = new ArrayList<String>();
////						}
////						saveDataList.add(col);
////					}
////
////					if(mongoMap==null) {
////						mongoMap = new HashMap<String, Map>();//默认一行数据
////					}
////					mongoMap.put(tag, map);
//				}else {
//
//					if(rmap.containsKey(col)) {
//						rmap.get(col).put(DateTimeUtils.parseDateTime(sj), valstr);
//					}else {
//
//						Map<Date, String> _map = new HashMap<Date, String>();
//						_map.put(DateTimeUtils.parseDateTime(sj), valstr);
//						rmap.put(col, _map);
//					}
////					if(StringUtils.isNotEmpty(tag) && isEdit) {
////						//标识记录已保存的数据，用于背景色显示
////						if(saveDataList == null) {
////							saveDataList = new ArrayList<String>();
////						}
////						saveDataList.add(sj+"_"+col);//tag.replace(" ", "T")
////					}
//				}
//			}
//		}
//
//		return rmap;
//	}
	/**
	 * @category 取最大公约数
	 * @param step 数值1
	 * @param imin 数值2
	 * @return
	 */
	private int commonDivisor(int step, int imin) {
		if(step == imin) {
			return step;
		}
		int min = step < imin ? step : imin;
		int max = step > imin ? step : imin;
		int comDiv = max;
		int jg = 1;

		for (int i = min,il=0; i > il && comDiv != 0; i--) {
			comDiv = max % min;
			if(comDiv == 0) {
				jg = min;
				break;
			}else {
				max = min;
				min = comDiv;
			}
		}
		return jg;
	}
//
//	/**
//	 * @category 获取核算单元信息
//	 * @return
//	 */
//	private Map<String, String> getUnitMap() {
//		Map<String, String> rmap = new HashMap<String, String>();
//		String sql = "select ID, NAME from COSTUINT where TMUSED=1";
//		List<Map<String, Object>> list = srv.queryListMap(sql, null);
//		if(StringUtils.isNotEmpty(list)) {
//			for (Map<String, Object> map : list) {
//				String id = map.get("ID")==null? null : String.valueOf(map.get("ID"));
//				String name = map.get("NAME")==null? "" : String.valueOf(map.get("NAME"));
//				if(id != null) {
//					rmap.put(id, name);
//				}
//			}
//		}
//		return rmap;
//	}
//	//获取小数位数
//	private Integer getTagDegit(Integer decimalDegit, Integer meterBit) {
//		Integer rv = 2;
//		if(decimalDegit == null || decimalDegit < 0) {
//			rv = meterBit == null ? 2 : meterBit;
//		}else {
//			rv = decimalDegit;
//		}
//		return rv;
//	}

	@Override
	/**
	 * 批量同步台账仪表版本数据
	 */
	public void syncBatchTaginfo(List<Costunitsampledot> tlist, String unitCode, String op) {

		if(StringUtils.isEmpty(op)) {
			applySrv.syncBatchTagSort(unitCode);
		}else if(StringUtils.isNotEmpty(tlist)) {

			List<TdsAccountTag> tagList = new ArrayList<TdsAccountTag>();
			List<TdsAccountTag> delTagList = new ArrayList<TdsAccountTag>();

			if("add".equals(op) || "del".equals(op)) {//添加删除判断是否给台账使用
				for (Costunitsampledot dot : tlist) {
					if(new Integer(1).equals(dot.getIsShowLedger())) {
						TdsAccountTag tag = ObjUtils.copyTo(dot, TdsAccountTag.class);
						tag.setTagid(dot.getId());
						tag.setTagname(dot.getName());
						tag.setUnitCode(dot.getUnitid());
						tag.setUpLimit(dot.getIndexRangeUpper());
						tag.setLowerLimit(dot.getIndexRangeLower());
						tag.setDecimalDegit(dot.getPointCountLedger());
						tag.setTagpid(dot.getPid());
                        tag.setDeviceDefaultVal(dot.getDeviceDefaultVal());
                        tag.setDefaultVals(dot.getDefaultVals());
                        tag.setMultiSelectDisplayMode(dot.getMultiSelectDisplayMode());
                        tag.setCopyAddDefaultMode(dot.getCopyAddDefaultMode());

						tag.setAlign("right");
						tag.setEditMark(0);

						tagList.add(tag);
					}
				}
			}else {//更新仪表
				for (Costunitsampledot dot : tlist) {
					TdsAccountTag tag = ObjUtils.copyTo(dot, TdsAccountTag.class);
					tag.setTagid(dot.getId());
					tag.setTagname(dot.getName());
					tag.setUnitCode(dot.getUnitid());
					tag.setUpLimit(dot.getIndexRangeUpper());
					tag.setLowerLimit(dot.getIndexRangeLower());
					tag.setDecimalDegit(dot.getPointCountLedger());
					tag.setTagpid(dot.getPid());
                    tag.setDeviceDefaultVal(dot.getDeviceDefaultVal());
                    tag.setDefaultVals(dot.getDefaultVals());
                    tag.setMultiSelectDisplayMode(dot.getMultiSelectDisplayMode());
                    tag.setCopyAddDefaultMode(dot.getCopyAddDefaultMode());

//					tag.setAlign("right");
//					tag.setEditMark(0);
					if(new Integer(1).equals(dot.getIsShowLedger())) {
						tagList.add(tag);
					}else {
						delTagList.add(tag);
					}
				}
			}

			if(StringUtils.isNotEmpty(tagList)) {
				applySrv.syncBatchTaginfo(tagList, unitCode, op);
			}
			if(StringUtils.isNotEmpty(delTagList)) {
				applySrv.syncBatchTaginfo(delTagList, unitCode, "del");
			}
		}
	}

	/**
	 * @category 自动处理更新数据
	 * @param param
	 */
	public Boolean autoUpdAccount(AccountParam ap) {
		/*
		 * 根据更新时间及是生成文件时间，查询是否需要更新
		 * 重新生成文件并更新时间
		 */
		Date nd = DateTimeUtils.getNowDate();
		Date lastUpdTime = getLastUpdTime();//获取最后更新日期
		if(lastUpdTime!=null) {
//			String lastDtStr = DateTimeUtils.formatDateTime(lastUpdTime);
			List<Object> param = new ArrayList<Object>();
			param.add(lastUpdTime);
			param.add(nd);
			String sql = "select b.INPUT_TIME, b.BA_ID, b.sbsj, a.COLLECT_POINT_ID, a.COLLECT_POINT_VAL, a.COLLECT_POINT_TEXT, a.INPUT_COMP_TYPE,INPUT_OPTIONS, a.UPDATE_TIME " +
					"from ACCTOBJ_INPUTMX a left join ACCTOBJ_INPUT b on a.IPT_ID=b.ID " +
					"where a.UPDATE_TIME>? and b.xbsj<? and b.BA_ID is not null and b.sbsj is not null order by b.INPUT_TIME";
			try {
				List<Map<String, Object>> list = srv.queryListMap(sql, param.toArray());
				Date st = null, et = null, uut = null;
				Map<String, String> datamap = new HashMap<String, String>();

				List<String> ulist = new ArrayList<String>();
				StringBuffer sb = new StringBuffer();

				List<TdsAccountData> dlist = null;
				if(list.size() > 0) {
					for (Map<String, Object> map : list) {
						Date dt = getMapDate(map, "INPUT_TIME");
						Date ut = getMapDate(map, "UPDATE_TIME");
						String tagId = getMapString(map, "COLLECT_POINT_ID");
						String val = getMapString(map, "COLLECT_POINT_VAL");
//						String showTxt = getMapString(map, "COLLECT_POINT_TEXT");
//						String comType = getMapString(map, "INPUT_COMP_TYPE");
//						String comOptions = getMapString(map, "INPUT_OPTIONS");
						String unitid = getMapString(map, "BA_ID");
						Date sbsj = getMapDate(map, "sbsj");
						String sbsjstr = DateTimeUtils.formatDateTime(sbsj);

						String key = unitid+","+sbsjstr;
						if(!ulist.contains(key)) {
							ulist.add(key);
							sb.append(" or (UNITCODE='"+unitid+"' and SBSJSTR='"+sbsjstr+"')");
						}

						if(uut==null) {
							uut = ut;
						}else {
							if(DateTimeUtils.bjDate(ut, uut) == 1) {
								uut = ut;
							}
						}

						//赋值
						if(dt!=null) {
							datamap.put(tagId+"_"+DateTimeUtils.formatDateTime(dt) , val);

							if(st == null) {
								st = dt;
								et = dt;
							}else {
								if(DateTimeUtils.bjDate(st, dt) == 1) {
									st = dt;
								}
								if(DateTimeUtils.bjDate(dt, et) == 1) {
									et = dt;
								}
							}
						}
					}
				}
				if(st==null || et==null) {
					return false;
				}
				if(datamap.isEmpty()) {
					return false;
				}

				//查数据
				if(sb.length() > 0) {
					param.clear();
					param.add(DateTimeUtils.parseDateTime(DateTimeUtils.formatDate(uut)));
					sql = "select ID from TDS_ACCOUNT_DATA where tmused=1 and (imgtime is null or imgtime<?) and ("+sb.substring(4)+")";
					List<Map<String, Object>> alist = srv.queryListMap(sql, param.toArray());
					List<String> idlist = new ArrayList<String>();
					if(alist.size() > 0) {
						for (Map<String, Object> map : alist) {
							String id = getMapString(map, "ID");
							idlist.add(id);
						}

						Where where = Where.create();
						where.eq(TdsAccountData::getTmused, 1);
						where.in(TdsAccountData::getId, idlist.toArray());
						dlist = srv.queryData(TdsAccountData.class, where, null, null);
					}
				}

				//取MongoDB数据，进行更新
				String tdsAlias = null;
				List<Map> mdata = null;
				SFFormQueryDto formdto = new SFFormQueryDto();
				formdto.setCategory(this.accountTypeId);//台账分类
				List<SFForm> formList = formService.queryFormInfoList(formdto, null);
				if(StringUtils.isNotEmpty(formList)) {//有表单
					String formId = formList.get(0).getId();
					Map<String, List<List>> amap = new HashMap<String, List<List>>();
					getFormAccountTds(formId, amap);
					List tlist = amap.get(formId);
					if(tlist!=null) {
						List itemList = (List)tlist.get(0);//暂只做表单第一个数据源
						TdsAccountConf conf = (TdsAccountConf)itemList.get(0);
						TdsAccountTime timeConf = (TdsAccountTime)itemList.get(1);
						if("2".equals(conf.getTagType()) && ("point".equals(timeConf.getShowMode()) || "mobile".equals(timeConf.getShowMode()))) {
							tdsAlias = conf.getTdsalias();
							mdata = accountServ.getAccountSaveData(this.accountTabName+"_"+tdsAlias, tdsAlias, st.getTime(), et.getTime());
						}
					}
				}
				if(mdata!=null) {
					JSONArray updateData = new JSONArray();
					for (Map map : mdata) {
						String col = String.valueOf(map.get("col"));//仪表相当于tagID
						String sj = String.valueOf(map.get("sj"));

						String val = datamap.get(col+"_"+sj);
						if(val!=null) {
							map.put("valstr", val);
							JSONObject uobj = new JSONObject(map);
							updateData.add(uobj);
						}
					}
					if (updateData.size() > 0) {// 修改
						mongoDBServ.updateBatchById(accountTabName + "_" + tdsAlias, updateData);
					}
				}

				//生成图片
				if(StringUtils.isNotEmpty(dlist)) {
					List<TdsAccountData> updList = new ArrayList<TdsAccountData>();
					for (TdsAccountData data : dlist) {
						savePic(data);
						if(data.getImgid()!=null) {
							data.setImgtime(nd);
							updList.add(data);
						}
					}
					if(StringUtils.isNotEmpty(updList)) {
						srv.updateByIdBatch(updList, 100);
					}
				}
			} catch (Exception e) {
				log.error("调度更新台账数据错误："+e.getMessage());
//				e.printStackTrace();
			}
		}

		return true;
	}
	private Integer getMapInt(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if(obj == null) {
			return null;
		}else if(Coms.judgeLong(obj)) {
			String so = String.valueOf(obj);
			return Integer.parseInt(so);
		}

		return null;
	}
	private String getMapString(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if(obj == null) {
			return null;
		}else {
			return String.valueOf(obj);
		}
	}
	private Date getMapDate(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if(obj == null) {
			return null;
		}else if(obj instanceof java.util.Date){
			return (Date)obj;
		}

		return null;
	}

	/**
	 * @category 台账自动保存
	 * @param param
	 */
	@Override
	public void accountAutoSave(AccountParam param) {
		autoUpdAccount(param);
		/**
		 * 处理流程
		 * 1、根据最后保存时间和当前时间，获取需要处理的时间段
		 * 2、根据时间段，获取台账配置（自定义台账），获取需要生成的台账信息
		 * 3、获取台账相关的核算对象信息，与台账信息做交集处理，确定需要生成的台账（默认台账、自定义台账）
		 * 4、根据日期、核算对象，推出倒班班次信息
		 * 5、根据日期、班次循环，创建所有台账信息（读取已有信息，做判断处理，避免重复）
		 * 6、台账数据生成后，根据台账配置，自动保存仪表数据
		 * 7、同时，整理相关数据，提供对外接口，同步更新关联功能数据（班组记事等）-----
		 */
		Date currTime = DateTimeUtils.getNowDate();
		Date lastUpdTime = getLastUpdTime();//获取最后更新日期
		Date startDay = getStartDay(lastUpdTime);//自动保存开始日期

		if(startDay!=null) {
			Date endDay = DateTimeUtils.parseD(DateTimeUtils.formatDate(currTime, DateTimeUtils.DateFormat_YMD), DateTimeUtils.DateFormat_YMD);//自动保存截止日期
			Date updateTime = currTime;//最后更新时间

			List<Costuint> unitList = getAllCostunit();//所有核算对象

			List<TdsAccountForm> customList = getCustomList();//自定义台账
			List<String> customUnitList = getCustomUnitList(customList);

			List<TdsAccountData> dataList = getHaveData(startDay, endDay);
			Map<String, TdsAccountData> dataMap = getDataMap(dataList);

			String curTimeStr = DateTimeUtils.formatDateTime(currTime);


			SFFormQueryDto formdto = new SFFormQueryDto();
			formdto.setCategory(this.accountTypeId);//台账分类
			List<SFForm> formList = formService.queryFormInfoList(formdto, null);

			Boolean execFlag = true;

			ApplyParams ap = new ApplyParams();
			ap.setApplyAlias("account_apply_form_template");
			String tdsAlias = applySrv.getApplyConfValue(ap);
			Map<String, String> mdbmap = getMongoDBData(tdsAlias, startDay, DateTimeUtils.doDate(endDay, 1));//MongoDB已有数据

//			Date tempDay = startDay;
//			//循环日期
//			for (int i = 0; i < 50; i++) {//避免死循环
//				if(i > 0) {
//					tempDay = DateTimeUtils.doDate(tempDay, 1);
//				}
//				if(DateTimeUtils.bjDate(tempDay, endDay) == 1) {//大于截止日期自动终止
//					break;
//				}
//
			String srqstr = DateTimeUtils.formatDate(startDay, DateTimeUtils.DateFormat_YMD);
			String erqstr = DateTimeUtils.formatDate(endDay, DateTimeUtils.DateFormat_YMD);

			Map<String, List<List>> amap = new HashMap<String, List<List>>();//台账数据源临时变量

			//独立默认台账（按核算对象）
			for (Costuint unit : unitList) {
				String unitCode = unit.getId();
				if(customUnitList.contains(unitCode) && StringUtils.isNotEmpty(formList)) {
					continue;
				}
//				List<ShiftForeignVo> shiftList = unititeminfoservice.getShiftList(unitCode, rqstr);//当日班次列表
				List<ShiftForeignVo> bcList = abnormalSrv.getUnitRqShiftList(unitCode, srqstr, erqstr);

				execFlag = execFlag && createAccount(unitCode, unit.getName(), bcList, dataMap, curTimeStr, formList, mdbmap, amap);
			}
			//自定义台账
			for (TdsAccountForm obj : customList) {
				String uc = obj.getUnitCode();
				if(uc != null) {
					String unitCode = uc.replace("[", "").replace("]", "").replace("\"", "").replace(" ", "");
					List<String> slist = Coms.StrToList(unitCode, ",");
					String ucode = slist.get(0);
//					List<ShiftForeignVo> shiftList = unititeminfoservice.getShiftList(ucode, rqstr);//当日班次列表
					List<ShiftForeignVo> bcList = abnormalSrv.getUnitRqShiftList(ucode, srqstr, erqstr);

					execFlag = execFlag && createCustomAccount(obj, unitCode, obj.getUnitName(), bcList, dataMap, curTimeStr, mdbmap, amap);
				}
			}

//			}

			if(execFlag) {
				updateLastDate(null, updateTime);
			}
		}
	}
	/*
	 * 创建台账记录
	 */
	private Boolean createCustomAccount(TdsAccountForm obj, String unitCode, String unitName, List<ShiftForeignVo> bcList,
			Map<String, TdsAccountData> dataMap, String curTimeStr, Map<String, String> mdbmap, Map<String, List<List>> amap) {
		Boolean flag = true;
		if(StringUtils.isNotEmpty(bcList)) {

			String formid = "";

			List<TdsAccountData> addList = new ArrayList<TdsAccountData>();

			//循环日期段所有班次信息
			for (ShiftForeignVo shift : bcList) {
				if(shift.getXbsj().compareTo(curTimeStr) > 0) {//超过上班时间即还没下班，不保存
					break;
				}
				String bcdm = shift.getShiftClassCode();//班次
				String rq = shift.getTbrq();//日期

				//判断数据信息
				String key = rq+"_"+bcdm+"_"+obj.getId();
				if(dataMap.containsKey(key)) {
					continue;
				}else {
					TdsAccountData data = new TdsAccountData();
					data.setId(TMUID.getUID());
					data.setRq(shift.getTbrq());
					data.setDataid(data.getId());
					data.setFormid(formid);
					data.setCustomformcode(obj.getId());
					data.setCustomformname(obj.getAccountName());
					data.setClsno(bcdm);
					data.setClsname(shift.getShiftClassName());
					data.setOrgcode(shift.getOrgCode());
					data.setOrgname(shift.getOrgName());
					data.setUnitcode(unitCode);
					data.setUnitname(unitName);
					data.setSbsjstr(shift.getSbsj());
					data.setXbsjstr(shift.getXbsj());
					data.setSbsj(DateTimeUtils.parseDateTime(shift.getSbsj()));
					data.setXbsj(DateTimeUtils.parseDateTime(shift.getXbsj()));
					data.setTmused(1);
//					srv.insert(data);
					addList.add(data);

					getFormAccountTds(formid, amap);
					List<List> tlist = amap.get(formid);
					if(StringUtils.isNotEmpty(tlist)) {
						for (List itemList : tlist) {
							TdsAccountConf conf = (TdsAccountConf)itemList.get(0);
							TdsAccountTime timeConf = (TdsAccountTime)itemList.get(1);
							if("2".equals(conf.getTagType())) {//只保存时间点和获取的数据
								saveMongoDbData(rq, unitCode, bcdm, mdbmap, conf, timeConf, data);//保存仪表数据
							}
						}
					}
				}
			}

			if(StringUtils.isNotEmpty(addList)) {
				flag = 1 == srv.insertBatch(addList, 50);
			}
		}
		return flag;
	}

	/*
	 * 创建台账记录
	 */
	private Boolean createAccount(String unitCode, String unitName, List<ShiftForeignVo> bcList, Map<String, TdsAccountData> dataMap,
			String curTimeStr, List<SFForm> formList, Map<String, String> mdbmap, Map<String, List<List>> amap) {
		Boolean flag = true;
		if(StringUtils.isNotEmpty(bcList)) {

			List<TdsAccountData> addList = new ArrayList<TdsAccountData>();

			//循环日期段所有班次信息
			for (ShiftForeignVo shift : bcList) {
				if(shift.getXbsj().compareTo(curTimeStr) > 0) {//超过下班时间即还没下班，不保存
					break;
				}
				String bcdm = shift.getShiftClassCode();//班次
				String rq = shift.getTbrq();//日期

				//判断数据信息
				String key = rq+"_"+bcdm+"_"+unitCode;
				if(dataMap.containsKey(key)) {
					continue;
				}else {
					String formId = null;
//					String dataId = null;
					TdsAccountData tdata = null;
					for (SFForm form : formList) {
						TdsAccountData data = new TdsAccountData();
						data.setId(TMUID.getUID());
						data.setRq(shift.getTbrq());
						data.setDataid(data.getId());
						data.setFormid(form.getId());
						data.setClsno(bcdm);
						data.setClsname(shift.getShiftClassName());
						data.setOrgcode(shift.getOrgCode());
						data.setOrgname(shift.getOrgName());
						data.setUnitcode(unitCode);
						data.setUnitname(unitName);
						data.setSbsjstr(shift.getSbsj());
						data.setXbsjstr(shift.getXbsj());
						data.setSbsj(DateTimeUtils.parseDateTime(shift.getSbsj()));
						data.setXbsj(DateTimeUtils.parseDateTime(shift.getXbsj()));
						data.setTmused(1);
//					srv.insert(data);
						addList.add(data);

						if(formId == null) {
							tdata = data;
							formId = form.getId();
//							dataId = data.getDataid();
						}
					}

					getFormAccountTds(formId, amap);
					List<List> tlist = amap.get(formId);
					if(StringUtils.isNotEmpty(tlist)) {
						for (List itemList : tlist) {
							TdsAccountConf conf = (TdsAccountConf)itemList.get(0);
							TdsAccountTime timeConf = (TdsAccountTime)itemList.get(1);
							if("2".equals(conf.getTagType())) {//只保存时间点和获取的数据
								saveMongoDbData(rq, unitCode, bcdm, mdbmap, conf, timeConf, tdata);//保存仪表数据
							}
						}
					}
				}
			}

			if(StringUtils.isNotEmpty(addList)) {
				Date nd = DateTimeUtils.getNowDate();
				flag = 1==srv.insertBatch(addList, 50);

				saveFormData(addList);//保存表单数据

				List<TdsAccountData> updList = new ArrayList<TdsAccountData>();
				//生成图片
				for (TdsAccountData data : addList) {
					savePic(data);
					if(data.getImgid()!=null) {
						data.setImgtime(nd);
						updList.add(data);
					}
				}
				if(StringUtils.isNotEmpty(updList)) {
					srv.updateByIdBatch(updList, 100);
				}
			}
		}

		return flag;
	}

	private void saveFormData(List<TdsAccountData> addList) {

		for (TdsAccountData obj : addList) {
			if(StringUtils.isEmpty(obj.getFormid())) {
				continue;
			}
			Map<String, Object> formData = new HashMap<String, Object>();
			formData.put("rq", obj.getRq());
			formData.put("unitCode", obj.getUnitcode());
			formData.put("bc", obj.getClsno() + "," + obj.getSbsjstr() + "," + obj.getXbsjstr());
			formData.put("accountId", obj.getCustomformcode());
			formData.put("dataid", obj.getDataid());
			formData.put("id", obj.getDataid());
			formData.put("teamId", obj.getOrgcode());
			formData.put("teamName", obj.getOrgname());
			formData.put("shiftId", obj.getClsno());
			formData.put("shiftName", obj.getClsname());
			formData.put("writeDay", obj.getRq());//
			formData.put("summaryDay", obj.getRq());
			formData.put("unitId", obj.getUnitcode());
			formData.put("unitName", obj.getUnitname());
			formData.put("shiftBegintime", obj.getSbsjstr());
			formData.put("shiftEndtime", obj.getXbsjstr());
			formService.saveFormData(obj.getFormid(), obj.getDataid(), formData);
		}
	}

	/**
	 * @category 根据表单id获取台账数据源相关信息
	 * @param formId
	 * @param amap
	 */
	private void getFormAccountTds(String formId, Map<String, List<List>> amap) {

		if(!amap.containsKey(formId)) {
			//根据模板获取台账数据源信息，进行相关仪表、时间获取，进行保存
			List<String> dsList = new ArrayList<String>();
			SFForm form = formService.queryFormInfoById(formId);
			if(form!=null) {
				List<SFFormComponentBean> vlist = formService.getFormAllComp(formId, null, true);
				if(StringUtils.isNotEmpty(vlist)) {
					for (SFFormComponentBean bean : vlist) {
						if("tds".equals(bean.getRenderType())) {
							String ds = bean.getFormComponent();
							TdataSource tds = StringUtils.isEmpty(ds) ? null : tdsSrv.getTDataSource(ds);
							if("TDSAccount".equalsIgnoreCase(tds.getTdsclassName())) {//台账数据源
								dsList.add(ds);
							}
						}
					}
				}
			}

			if(StringUtils.isNotEmpty(dsList)) {
				List<List> clist = new ArrayList<List>();
				for (String tdsAlias : dsList) {
					//获取台账数据源时间配置及仪表配置
					TdsAccountConf conf = accountServ.getAccountConf(tdsAlias);//配置信息
					TdsAccountTime timeConf = accountServ.getTimeConf(tdsAlias);//时间配置
					List tlist = new ArrayList(2);
					tlist.add(conf);
					tlist.add(timeConf);
					clist.add(tlist);
				}
				amap.put(formId, clist);
			}else {
				amap.put(formId, null);
			}

		}
	}

	/**
	 * @category 保存MongoDB仪表数据
	 * @param rq
	 * @param unitCode
	 * @param bcdm
	 */
	private Boolean saveMongoDbData(String rq, String unitCode, String bcdm, Map<String, String> mdbmap, TdsAccountConf conf, TdsAccountTime timeConf, TdsAccountData data) {

		String tdsAlias = conf.getTdsalias();
		String formId = data.getFormid();
		String dataId = data.getDataid();
		//取仪表列表
		List<String> tagCodes = null;//TODO
		Map<String, TdsAccountMeter> tagIdObjMap = null;//仪表相关信息
		List<String> colList = new ArrayList<String>();//列信息
		List<TdsAccountMeter> tagList = getDefaultMeter(tdsAlias, conf, rq, unitCode);
		if(StringUtils.isNotEmpty(tagList)) {
			tagCodes = new ArrayList<String>();//仪表信息
			tagIdObjMap = new HashMap<String, TdsAccountMeter>();
			colList = new ArrayList<String>();
			for (TdsAccountMeter obj : tagList) {
				tagIdObjMap.put(obj.getTagid(), obj);
				colList.add(obj.getTagid());
				if(StringUtils.isNotEmpty(obj.getDatasource()) && !tagCodes.contains(obj.getDatasource())) {
					tagCodes.add(obj.getDatasource());
				}
			}
		}

		//取时间列表
		List<Date> timeList = new ArrayList<Date>();
		if("point".equals(timeConf.getShowMode())) {
			Map<Date, String> sjmap = new HashMap<Date, String>();
			List<String> se = new ArrayList<String>();
			getTimeList(conf, timeConf, rq, data.getSbsjstr(), data.getXbsjstr(), timeList, sjmap, se);
		}else if("mobile".equals(timeConf.getShowMode())) {//移动端采集

		}
		if(StringUtils.isEmpty(timeList)) {//无时间点终止
			return true;
		}
		//判断是否已有MongoDB数据，如果有就跳过，没有时创建并批量插入

		Date nd = DateTimeUtils.getNowDate();
		List<Date> dtlist = timeList;//时间点列表
		String st = DateTimeUtils.formatDateTime(timeList.get(0));
		String et = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
		int getstep = 60;//TODO
		JSONObject jsonObject = null;
		try {
			jsonObject = rtdbSrv.queryRtdbData(tagCodes, st, et, getstep);
		} catch (Exception e) {
			jsonObject = null;
			log.error("台账获取仪表数据失败："+e.getMessage());
		}

		Map<String, Map<Date, String>> map = new HashMap<String, Map<Date,String>>();
		if(jsonObject != null) {
			JSONArray meters = JSONArray.parseArray(jsonObject.getString("result"));
			if(meters!=null && meters.size() > 0) {
				for (int i = 0,il=meters.size(); i < il; i++) {
					JSONObject meterObj = JSONObject.parseObject(meters.getString(i));
					String tagCode = meterObj.getString("tagCode");

					Map<Date, String> _map = new LinkedHashMap<Date, String>();
					map.put(tagCode, _map);

					JSONArray array = JSONArray.parseArray(meterObj.getString("datas"));
					if(array!=null && array.size() > 0) {
						for (int j = 0,jl=array.size(); j < jl; j++) {
							JSONObject obj = JSONObject.parseObject(array.getString(j));
							String value = obj.getString("value");//数据不格式化保存，在显示函数中处理
							Date dt = DateUtil.parseDateTime(obj.getString("datetime"));
							_map.put(dt, value);
						}
					}
				}
			}
		}

		JSONArray insertData = new JSONArray();
		for (Date date : dtlist) {//时间点

			for (String tagId : colList) {
//			for (String tagId : map.keySet()) {
				if(mdbmap!=null && mdbmap.containsKey(tagId+"_"+DateTimeUtils.formatDateTime(date))) {//已有数据不再重复保存
					continue;
				}
				TdsAccountMeter tagObj = tagIdObjMap.get(tagId);
				String tagCode = tagObj==null?null:tagObj.getDatasource();//tagIdObjMap.get(tagId)==null?null:tagIdObjMap.get(tagId).getDatasource();
				Map<Date, String> _map = map.get(tagCode);//map.get(tagId);
				if(_map!=null && _map.containsKey(date)) {
					String val = _map.get(date);
					if(StringUtils.isNotEmpty(val)) {
						JSONObject aobj = new JSONObject();
						aobj.put("_id", TMUID.getUID());
						aobj.put("dsAlias", tdsAlias);
						aobj.put("formId", formId);
						aobj.put("dataId", dataId);
						aobj.put("col", tagId);//列名
						aobj.put("tag", tagCode);
						aobj.put("tagId", tagId);
						aobj.put("dateType", "tag");
						aobj.put("timetype", "point");
						aobj.put("rq", rq);
						aobj.put("rqlong", DateTimeUtils.parseDate(rq).getTime());
						aobj.put("sj", DateTimeUtils.formatDateTime(date));
						aobj.put("sjlong", date.getTime());
						aobj.put("val", val);
						aobj.put("valstr", val);
						aobj.put("creTime", nd);
						aobj.put("creUserId", 0);
						aobj.put("creUserName", "系统");
						aobj.put("updTime", null);
						aobj.put("updUserId", null);
						aobj.put("updUserName", null);
						aobj.put("additionVal", null);//附加值
						aobj.put("edit", false);
						aobj.put("tmused", 1);
						aobj.put("confirmDiff", 0);//确认差（秒）
						aobj.put("rowConfirmBound", null);//确认范围（分钟）
						aobj.put("rowConfirmOver", false);//确认超时
						insertData.add(aobj);

					}
				}
			}
		}
		if(insertData.size() > 0) {
			mongoDBServ.insertBatch(accountTabName+"_"+tdsAlias, insertData);
		}

		return true;
	}

	//获取核算对象默认仪表列表
	private List<TdsAccountMeter> getAccountTag(String rq, String unitId, String tagType) {
		List<TdsAccountMeter> rlist = null;//
		if(tagType!=null) {
			rlist = accountServ.getDefaultAccountUnitTagList(unitId, rq, tagType);//过滤仪表类型 平稳率、lims
		}else {
			rlist = accountServ.getDefaultAccountUnitTagList(unitId, rq);
		}
		return rlist;
	}

	/**
	 * @category 获取已有MongoDB数据
	 * @return
	 */
	private Map<String, String> getMongoDBData(String tdsAlias, Date st, Date et) {
		Map<String, String> rmap = new HashMap<String, String>();
		List<Map> mdata = accountServ.getAccountSaveData(this.accountTabName+"_"+tdsAlias, tdsAlias, st.getTime(), et.getTime());
		if(mdata!=null) {//自动保存只提取仪表数据
			for (Map map : mdata) {
				String col = String.valueOf(map.get("col"));//仪表相当于tagID
//				String tag = String.valueOf(map.get("tag"));
				String sj = String.valueOf(map.get("sj"));
//				String aid = String.valueOf(map.get("accountId"));
//				String unitCode = String.valueOf(map.get("unitCode"));
				String valstr =  String.valueOf(map.get("valstr"));
				if("rowConfirm".equals(col)) {
					continue;
				}
				rmap.put(col+"_"+sj, valstr);
			}
		}

		return rmap;
	}

	/*
	 * 获取已有数据map信息
	 * key rq_bc_unitcode rq_bc_accountId
	 */
	private Map<String, TdsAccountData> getDataMap(List<TdsAccountData> dataList) {
		Map<String, TdsAccountData> map = new HashMap<String, TdsAccountData>();
		for (TdsAccountData obj : dataList) {
			String accountId = obj.getCustomformcode();
			String rq = obj.getRq();
			String bc = obj.getClsno();
			String unitcode = obj.getUnitcode();
			if(StringUtils.isEmpty(accountId)) {
				map.put(rq+"_"+bc+"_"+unitcode, obj);
			}else {
				map.put(rq+"_"+bc+"_"+accountId, obj);
			}
		}
		return map;
	}

	/*
	 * 整理同核算对象的自定义表单
	 */
	private List<String> getCustomUnitList(List<TdsAccountForm> customList) {
		List<String> rlist = new ArrayList<String>();
		for (TdsAccountForm obj : customList) {
			String us = obj.getUnitCode();
			if(us != null) {
				us = us.replace("[", "").replace("]", "").replace("\"", "").replace(" ", "");
				if(us.length() > 0) {
					List<String> slist = Coms.StrToList(us, ",");
					for (String s : slist) {
						if(!rlist.contains(s)) {
							rlist.add(s);
						}
					}
				}
			}
		}
		return rlist;
	}

	/*
	 * 获取已有数据
	 */
	private List<TdsAccountData> getHaveData(Date startDay, Date endDay) {
		List<String> rqbetween = new ArrayList<String>();
		rqbetween.add(DateTimeUtils.formatDate(startDay, DateTimeUtils.DateFormat_YMD));
		rqbetween.add(DateTimeUtils.formatDate(endDay, DateTimeUtils.DateFormat_YMD));

		Where whereData = Where.create();
		whereData.eq(TdsAccountData::getTmused, 1);
		whereData.between(TdsAccountData::getRq, rqbetween.toArray());
		List<TdsAccountData> dataList = srv.queryData(TdsAccountData.class, whereData, null, null);
		return dataList;
	}

	/*
	 * 获取开始日期
	 */
	private Date getStartDay(Date lastUpdTime) {
		Date rd = null;
		if(lastUpdTime!=null) {
			rd = DateTimeUtils.parseD(DateTimeUtils.formatDate(lastUpdTime, DateTimeUtils.DateFormat_YMD), DateTimeUtils.DateFormat_YMD);
		}else {
			//获取开始使用录入功能的第一条数据的日期 TODO 使用初始化数据模式做第一次处理

		}
		return rd;
	}

	//
	private List<TdsAccountForm> getCustomList() {
		Where where = Where.create();
		where.eq(TdsAccountForm::getTmused, 1);
		where.notEmpty(TdsAccountForm::getFormCode);
		where.notEmpty(TdsAccountForm::getUnitCode);
		Order order = Order.create();
		order.order(TdsAccountForm::getUnitCode);
		List<TdsAccountForm> rlist = srv.queryData(TdsAccountForm.class, where, order, null);
		return rlist;
	}

	private List<Costuint> getAllCostunit() {
		//获取核算对象列表
		CostDto cdto = new CostDto();
		cdto.setIsLedgerEntry(true);
		List<Costuint> unitList = costService.getData(cdto);

		if(StringUtils.isNotEmpty(unitList)) {
			for (Iterator iter = unitList.iterator(); iter.hasNext();) {
				Costuint unit = (Costuint) iter.next();
				if(!new Integer(1).equals(unit.getLedgerEntry()) || !new Integer(1).equals(unit.getTmused())) {
					iter.remove();
				}
			}
		}

		return unitList;
	}

	/**
	 * 获取最后更新时间
	 * @return
	 */
	private Date getLastUpdTime() {
		Where where = Where.create();
		where.eq(AccoutAutosaveMark::getApplyType, "mongodb");
		List<AccoutAutosaveMark> list = srv.queryData(AccoutAutosaveMark.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			AccoutAutosaveMark obj = list.get(0);
			return obj.getUpdTime();
		}
		return null;
	}
	/**
	 * @category 更新最后更新时间
	 * @param type
	 * @param lastUpdDate
	 * @return
	 */
	private Boolean updateLastDate(String type, Date lastUpdDate) {
		Boolean flag = true;
		type = StringUtils.isEmpty(type) ? "mongodb" : "relation";
		Where where = Where.create();
		where.eq(AccoutAutosaveMark::getApplyType, "mongodb");
		List<AccoutAutosaveMark> list = srv.queryData(AccoutAutosaveMark.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			AccoutAutosaveMark obj = list.get(0);
			obj.setUpdTime(lastUpdDate);
			flag = 1==srv.updateById(obj);
		}else {
			AccoutAutosaveMark obj = new AccoutAutosaveMark();
			obj.setId(TMUID.getUID());
			obj.setApplyType(type);
			obj.setUpdTime(lastUpdDate);
			obj.setTmused(1);
			flag = 1==srv.insert(obj);
		}

		return flag;
	}

	/**
	 * @category 计算时间配置的时间列表
	 */
	private void getTimeList(TdsAccountConf conf, TdsAccountTime timeConf, String rq, String sbsj, String xbsj, List<Date> timeList, Map<Date, String> sjmap, List<String> se) {
		//整理时间信息

		String st = null, et = null;
		int istep = 120;
		if(timeConf!=null) {
			Date startDate = null;
			String startRq = null;
			String startBind = timeConf.getStartBingDay();
			String startFix = timeConf.getStartFixed();
			Boolean haveStart = new Integer(1).equals(timeConf.getStartRound());
			int istartfix = 0;

			Date endDate = null;
			String endRq = null;
			String endBind = timeConf.getEndBingDay();
			String endFix = timeConf.getEndFixed();
			Boolean haveEnd = new Integer(1).equals(timeConf.getEndRound());
			int iendfix = 0;
			if(Coms.judgeLong(startFix)) {
				istartfix = Integer.parseInt(startFix);
			}
			if(Coms.judgeLong(endFix)) {
				iendfix = Integer.parseInt(endFix);
			}
			//开始时间获取
			if(StringUtils.isNotEmpty(startBind)) {
				//绑定台账内部绑定
				if("day".equals(startBind) || "mon".equals(startBind)) {
					startRq = rq;
				}else if("bc".equals(startBind)) {
					startRq = sbsj;
				}
			}
			if(StringUtils.isEmpty(startRq)) {
				if(istartfix != 0) {
					startRq = DateTimeUtils.getNowDateStr().substring(0,10) + " " + timeConf.getStartBingTime()+":00";
					startDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(startRq), istartfix);
				}else {
					startRq = DateTimeUtils.getNowDateStr().substring(0,10) + " " + timeConf.getStartBingTime()+":00";
					startDate = DateTimeUtils.parseDateTime(startRq);
				}
			}else {
				if("bc".equals(startBind)) {
					startDate = DateTimeUtils.parseDateTime(startRq);
					if(conf.getBcDiff()!=null) {//上班时间偏差修正
						int bcdiff = conf.getBcDiff();
						startDate = DateTimeUtils.doMinute(startDate, bcdiff);
					}
				}else {
					if(istartfix != 0) {
						startDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(startRq + " " + timeConf.getStartBingTime()+":00"), istartfix);
					}else {
						startDate = DateTimeUtils.parseDateTime(startRq + " " + timeConf.getStartBingTime()+":00");
					}
				}
			}
			//截止时间获取
			if(StringUtils.isNotEmpty(endBind)) {
				//绑定台账内部绑定
				if("day".equals(endBind) || "mon".equals(endBind)) {
					endRq = rq;
				}else if("bc".equals(endBind)) {
					endRq = xbsj;
				}
			}
			if(StringUtils.isEmpty(endRq)) {
				if(iendfix != 0) {
					endRq = DateTimeUtils.getNowDateStr().substring(0,10) + " " + timeConf.getEndBingTime()+":00";
					endDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(endRq), iendfix);
				}else {
					endRq = DateTimeUtils.getNowDateStr().substring(0,10) + " " + timeConf.getEndBingTime()+":00";
					endDate = DateTimeUtils.parseDateTime(endRq);
				}
			}else {
				if("bc".equals(endBind)) {
					endDate = DateTimeUtils.parseDateTime(endRq);
					if(conf.getBcDiff()!=null) {//下班时间偏差修正
						int bcdiff = conf.getBcDiff();
						endDate = DateTimeUtils.doMinute(endDate, bcdiff);
					}
				}else {
					if(iendfix != 0) {
						endDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(endRq + " " + timeConf.getEndBingTime()+":00"), iendfix);
					}else {
						endDate = DateTimeUtils.parseDateTime(endRq + " " + timeConf.getEndBingTime()+":00");
					}
				}
			}

			if(DateTimeUtils.bjDate(startDate, endDate) == 1) {//如果时间不对，不获取数据
			}else {

				Date tempDate = startDate;
				String timeStep = timeConf.getTimeStep();
				Integer step = 60;
				if(Coms.judgeLong(timeStep)) {
					step = Integer.parseInt(timeStep);
				}
				if(haveStart) {
					timeList.add(startDate);
					tempDate = DateTimeUtils.doMinute(tempDate, step);
				}

				for (int i = 0,il=50; i < il && DateTimeUtils.bjDate(endDate, tempDate) > 0; i++) {//时间对比，增加循环数，避免死循环
					timeList.add((Date)tempDate.clone());
					tempDate = DateTimeUtils.doMinute(tempDate, step);
				}

				if(DateTimeUtils.bjDate(tempDate, endDate) == 1) {
					if(haveEnd || "bound".equals(timeConf.getShowMode())) {
						if(!timeList.contains(endDate)) {
							timeList.add(endDate);
						}
						if("bound".equals(timeConf.getShowMode())) {

						}else if(timeList.size() > 1) {
							//需要取最小公约数
							long diffMin = Math.abs(DateTimeUtils.diffDate(endDate, tempDate, DateTimeUtils.MINITE));
							int imin = Integer.parseInt(String.valueOf(diffMin));
							if(imin == 0) {
								istep = step;
							}else {
								istep = commonDivisor(60, imin);//计算最大公约数
							}
							st = DateTimeUtils.formatDateTime(timeList.get(0));
							et = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
						}
					}
				}else if(DateTimeUtils.bjDate(tempDate, endDate) == 0) {
					//间隔固定
					if(haveEnd || "bound".equals(timeConf.getShowMode())) {
						timeList.add(endDate);
					}
				}

				if(st == null && timeList.size() > 0) {
					st = DateTimeUtils.formatDateTime(timeList.get(0));
					et = DateTimeUtils.formatDateTime(timeList.get(timeList.size() - 1));
					istep = step;
				}

				if(StringUtils.isNotEmpty(timeList)) {
					sjmap = new LinkedHashMap<Date, String>();
					for (Date date : timeList) {
						sjmap.put(date, DateTimeUtils.format(date, timeConf.getTimeFormat()));
					}
				}
			}
		}
		se.add(st);
		se.add(et);
	}

	/**--------------------------------------------------------------------------------------------------------------------------------------------------------------------*/

	/*
	 * 保存图片
	 */
	private Boolean savePic(TdsAccountData data) {
		String formId = data.getFormid();
		String hosturl = sysConfigService.getSysConfig("TM4Url");
		String saveImgUrl = sysConfigService.getSysConfig("saveImgUrl");
		String saveImgExe = sysConfigService.getSysConfig("saveImgExe");
		String minImgWidth = sysConfigService.getSysConfig("minImgWidth");
		if(StringUtils.isNotEmpty(formId) && StringUtils.isNotEmpty(hosturl) && StringUtils.isNotEmpty(saveImgUrl) && StringUtils.isNotEmpty(saveImgExe)) {
			String dataId = data.getDataid();
			String addr = sysConfigService.getSysConfig("file_path");
			if (addr == null) {
				addr = "d:/tm4UploadFiles";
			}
			String param = " -params formId="+formId+"&dataId="+dataId;
			String host = " -host "+hosturl+"";
			String page = " -page "+saveImgUrl+"";
			String command = saveImgExe;//"D:\\html-screenshot";
    		String token = " -token "+TokenUtils.createToken("0", "administrator", "administrator", "");
    		String filepath = " -file "+addr+"/temp.jpg";

    		String fileaddr = addr+"/temp.jpg";

    		String params = command+host+page+filepath+param+token;

    		runExe(params);
    		//图片保存完成后，进行文件上传处理（生成缩略图），上传两张
    		File file = new File(fileaddr);
    		if(file.exists()) {//文件存在
    			int thumbWidth = 600;
    			if(Coms.judgeLong(minImgWidth)) {
    				thumbWidth = Integer.parseInt(minImgWidth);
    			}
    			String minImg = addr+"/tempmin.jpg";
    			File minfile = null;
    			//生成缩率图
    			try {
    				// 读取图片
					BufferedImage inputImage = ImageIO.read(file);
					// 计算缩放比例
					double scaleRatio = (double) thumbWidth / inputImage.getWidth();

					// 创建缩放后的图片
		            int targetWidth = thumbWidth;
		            int targetHeight = (int) (inputImage.getHeight() * scaleRatio);
		            BufferedImage outputImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);

		            // 缩放图片
		            AffineTransform transform = new AffineTransform();
		            transform.scale(scaleRatio, scaleRatio);
		            AffineTransformOp op = new AffineTransformOp(transform, AffineTransformOp.TYPE_BICUBIC);
		            outputImage = op.filter(inputImage, outputImage);

		            minfile = new File(minImg);
		            // 保存缩略图
		            ImageIO.write(outputImage, "jpg", minfile);
				} catch (Exception e) {
				}
    			if(minfile!=null && minfile.exists()) {
    				//上传缩略图
    				SysFilesInfo minsf = saveFile(minfile);
    				data.setImgmid(minsf.getId());
    				//上传原图
    				SysFilesInfo sf = saveFile(file);
    				data.setImgid(sf.getId());
    			}
    		}
		}
		return true;
	}

	/**
     * @category 运行exe
     * @return
     */
    private void runExe(String params) {
    	try {
//    		// 使用Runtime执行exe文件
            Process process = Runtime.getRuntime().exec(params);
            process.waitFor();
        } catch (IOException | InterruptedException e) {
        	log.error(e.getMessage());
        }
    }

    public SysFilesInfo saveFile(File file) {
    	FileInputStream inputStream = null;
    	SysFilesInfo finfo = null;
		try {
//			File file = new File("D:/xxxxxx.jpg");
			if(file.exists()) {
				inputStream = new FileInputStream(file);
				MultipartFile mf = getMultipartFile(inputStream, "temp.jpg");
				finfo = fileSrv.saveFilesImg(mf, "account");
			}
		} catch (Exception e) {
		} finally {
			if(inputStream!=null) {
				try {
					inputStream.close();
				} catch (IOException e) {
				}
			}
		}
		return finfo;
    }

    /**
     * @category 根据InputStream和名称获取MultipartFile对象
     * @param inputStream
     * @param fileName
     * @return
     */
    public MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
        FileItem fileItem = createFileItem(inputStream, fileName);
        //CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
        return new CommonsMultipartFile(fileItem);
    }

    /**
     * @category FileItem类对象创建
     * @param inputStream inputStream
     * @param fileName    fileName
     * @return FileItem
     */
    public FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[10 * 1024 * 1024];
        OutputStream os = null;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        } catch (IOException e) {
        	log.error("文件上传失败");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);

                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Stream close exception", e);
                }
            }
        }

        return item;
    }

	@Override
	public List<AccountDataVo> mobilImgList(AccountParam param) {
		List<AccountDataVo> rlist = new ArrayList<AccountDataVo>();

		String unitid = param.getUnitid();
		String rq = param.getRq();

		if(StringUtils.isNotEmpty(unitid) && StringUtils.isNotEmpty(rq)) {
			Where where = Where.create();
			where.eq(TdsAccountData::getTmused, 1);
			where.eq(TdsAccountData::getRq, rq);
			where.eq(TdsAccountData::getUnitcode, unitid);
			where.notEmpty(TdsAccountData::getImgmid);
			List<TdsAccountData> list = srv.queryData(TdsAccountData.class, where, null, null);

			if(StringUtils.isNotEmpty(list)) {
				for (TdsAccountData data : list) {
					AccountDataVo vo = ObjUtils.copyTo(data, AccountDataVo.class);
					if(StringUtils.isNotEmpty(vo.getImgmid())) {
						vo.setMinImg64(getImgBase64(vo.getImgid()));
					}
					rlist.add(vo);
				}
			}
		}


		return rlist;
	}
	//根据id获取数据，并返回图片base64编码
	private String getImgBase64(String imgid) {
		SysFilesInfo fileInfo = srv.queryObjectById(SysFilesInfo.class, imgid);
		if(fileInfo!=null) {
			try {
				InputStream fileSteam = fs.getFileSteam(fileInfo);
				byte[] bytes = IOUtils.toByteArray(fileSteam);
				String str = new String(Base64.getEncoder().encode(bytes), StandardCharsets.UTF_8);
				return "data:image/jpg;base64,"+str;
			} catch (Exception e) {
			}
		}
		return null;
	}

	@Override
	public String getMobilImg(AccountParam param) {
		String imgid = param.getImgid();
		if(StringUtils.isNotEmpty(imgid)) {
			return getImgBase64(imgid);
		}

		return null;
	}


	@Override
	public Integer getAccountConfirmTodoNum(String loginName) {
		Integer num = 0;

		SysUser sysUser = SysUserUtil.getCurrentUser();
		boolean isAdmin = SysUserUtil.isAdmin();//管理员不显示待办
		if(isAdmin) {
			return 0;
		}

		List<String> uidlist = new ArrayList<String>();
		SysUser user = SysUserHolder.getCurrentUser();
		String orgCode = user.getOrgId();
		List<Costuint> ulist = costService.getCostuintListByOrgId(orgCode, 2, true);
		if(StringUtils.isNotEmpty(ulist)) {
			for (Costuint obj : ulist) {
				uidlist.add(obj.getId());
			}
		}
		if(StringUtils.isEmpty(uidlist)) {
			return 0;
		}

		String userId = sysUser.getId();
		String postId = sysUser.getPostId();
		String roleId = StringUtils.isEmpty(sysUser.getRoles()) ? "" : Coms.listToString(sysUser.getRoles());

		//根据接口获取对应人员未确认的数据ID集合
		int days = 7;//查询范围天数，参数获取，默认7天

		ApplyParams p = new ApplyParams();
		p.setApplyAlias("account_confirm_todo_days");
		String daysStr = applySrv.getApplyConfValue(p);
		if(Coms.judgeLong(daysStr)) {
			int ti = Integer.parseInt(daysStr);
			if(ti > 0) {
				days = ti;
			}
		}
		Date nd = DateTimeUtils.getNowDate();
		Date st = DateTimeUtils.doDate(nd, -days);

		BzjsConfirmStatusDto bzjsdto = new BzjsConfirmStatusDto();
		bzjsdto.setStartDate(st);
		bzjsdto.setEndDate(nd);
		bzjsdto.setPersonId(userId);
		bzjsdto.setPostId(postId);
		bzjsdto.setRoleIds(roleId);
		List<BzjsConfirmStatusVo> list = bzjsSrv.getConfirmStatusInfo(bzjsdto);

		List<String> idList = new ArrayList<String>();
		for (BzjsConfirmStatusVo vo : list) {
			if(!idList.contains(vo.getFormDataId())) {
				idList.add(vo.getFormDataId());
			}
		}
		if(idList.size() > 0) {
			List<TdsAccountData> dataList = new ArrayList<TdsAccountData>();
			if(idList.size() > 600) {
				List<List<String>> tlist = new ArrayList<List<String>>();
				for (int i = 0,il=idList.size(); i < il; i += 600) {
					tlist.add(idList.subList(i, Math.min(i + 600, il)));
				}
				for (List<String> list2 : tlist) {
					Where where = Where.create().eq(TdsAccountData::getTmused, 1).in(TdsAccountData::getDataid, list2.toArray()).in(TdsAccountData::getUnitcode, uidlist.toArray());
					List<TdsAccountData> templist = srv.queryData(TdsAccountData.class, where, null, null);
					if(StringUtils.isNotEmpty(templist)) {
						dataList.addAll(templist);
					}
				}
			}else {
				Where where = Where.create().eq(TdsAccountData::getTmused, 1).in(TdsAccountData::getDataid, idList.toArray()).in(TdsAccountData::getUnitcode, uidlist.toArray());
				dataList = srv.queryData(TdsAccountData.class, where, null, null);
			}

			dataList = filterConfirm(dataList);

			num = dataList.size();
		}

		return num;
	}

	@Override
	public ToDoApiVo getAccountConfirmTodoNumByApi(ToDoApiDto dto) {
		ToDoApiVo vo = new ToDoApiVo();
		Integer todoCnt = getAccountConfirmTodoNum(dto.getLoginName());
		vo.setTodoCount(todoCnt);

		return vo;
	}

	@Override
	public List<TdsAccountDataVo> getAccountConfirmTodoList(AccountParam dto) {
		SysUser sysUser = SysUserUtil.getCurrentUser();
		boolean isAdmin = SysUserUtil.isAdmin();//管理员不显示待办
		if(isAdmin) {
			return new ArrayList<TdsAccountDataVo>();
		}

		String userId = sysUser.getId();
		String postId = sysUser.getPostId();
		String roleId = StringUtils.isEmpty(sysUser.getRoles()) ? "" : Coms.listToString(sysUser.getRoles());

		List<TdsAccountDataVo> rlist = new ArrayList<TdsAccountDataVo>();

		List<String> uidlist = new ArrayList<String>();
		SysUser user = SysUserHolder.getCurrentUser();
		String orgCode = user.getOrgId();
		List<Costuint> ulist = costService.getCostuintListByOrgId(orgCode, 2, true);
		if(StringUtils.isNotEmpty(ulist)) {
			for (Costuint obj : ulist) {
				uidlist.add(obj.getId());
			}
		}
		if(StringUtils.isEmpty(uidlist)) {
			return rlist;
		}

		//根据接口获取对应人员未确认的数据ID集合
		int days = 7;//查询范围天数，参数获取，默认7天

		ApplyParams p = new ApplyParams();
		p.setApplyAlias("account_confirm_todo_days");
		String daysStr = applySrv.getApplyConfValue(p);
		if(Coms.judgeLong(daysStr)) {
			int ti = Integer.parseInt(daysStr);
			if(ti > 0) {
				days = ti;
			}
		}

		Date nd = DateTimeUtils.getNowDate();
		Date st = DateTimeUtils.doDate(nd, -days);

		BzjsConfirmStatusDto bzjsdto = new BzjsConfirmStatusDto();
		bzjsdto.setStartDate(st);
		bzjsdto.setEndDate(nd);
		bzjsdto.setPersonId(userId);
		bzjsdto.setPostId(postId);
		bzjsdto.setRoleIds(roleId);
		List<BzjsConfirmStatusVo> list = bzjsSrv.getConfirmStatusInfo(bzjsdto);

		List<String> idList = new ArrayList<String>();
		for (BzjsConfirmStatusVo vo : list) {
			if(!idList.contains(vo.getFormDataId())) {
				idList.add(vo.getFormDataId());
			}
		}
		if(StringUtils.isNotEmpty(idList)) {
			List<TdsAccountData> dataList = new ArrayList<TdsAccountData>();
			if(idList.size() > 600) {
				List<List<String>> tlist = new ArrayList<List<String>>();
				for (int i = 0,il=idList.size(); i < il; i += 600) {
					tlist.add(idList.subList(i, Math.min(i + 600, il)));
				}
				for (List<String> list2 : tlist) {
					Where where = Where.create().eq(TdsAccountData::getTmused, 1).in(TdsAccountData::getDataid, list2.toArray()).in(TdsAccountData::getUnitcode, uidlist.toArray());
					Order order = Order.create(TdsAccountData::getRq, "DESC");
					List<TdsAccountData> templist = srv.queryData(TdsAccountData.class, where, order, null);
					if(StringUtils.isNotEmpty(templist)) {
						dataList.addAll(templist);
					}
				}
			}else {
				Where where = Where.create().eq(TdsAccountData::getTmused, 1).in(TdsAccountData::getDataid, idList.toArray()).in(TdsAccountData::getUnitcode, uidlist.toArray());
				Order order = Order.create(TdsAccountData::getRq, "DESC");
				dataList = srv.queryData(TdsAccountData.class, where, order, null);
			}

			dataList = filterConfirm(dataList);

			for (TdsAccountData obj : dataList) {
				TdsAccountDataVo vo = new TdsAccountDataVo();
				vo.setId(obj.getId());
				vo.setDataid(obj.getDataid());
				vo.setFormid(obj.getFormid());
				vo.setRq(obj.getRq());
				vo.setClsno(obj.getClsno());
				vo.setClsname(obj.getClsname());
				vo.setSbsjstr(obj.getSbsjstr());
				vo.setXbsjstr(obj.getXbsjstr());
				vo.setUnitcode(obj.getUnitcode());
				vo.setUnitname(obj.getUnitname());
				vo.setCustomformcode(obj.getCustomformcode());
				vo.setCustomformname(obj.getCustomformname());
				vo.setOrgcode(obj.getOrgcode());
				vo.setOrgname(obj.getOrgname());
				vo.setWriteday(obj.getWriteday());

				rlist.add(vo);
			}
		}

		return rlist;
	}

	@Override
	public void refreshAccountConfirmTodo(AccountParam dto) {
		todoSrv.clearTodoCached("tm4-form", "account_classConfirm");
	}
	/**
	 * @category 获取台账干部待确认key值数组 rq_unitcode_bc
	 * @return
	 */
	@Override
	public List<String> getAccountTodoConfirmList(String st, String et) {

		List<String> rlist = new ArrayList<String>();

		SysUser sysUser = SysUserUtil.getCurrentUser();
		boolean isAdmin = SysUserUtil.isAdmin();//管理员不显示待办
		if(isAdmin) {
			return rlist;
		}

		String userId = sysUser.getId();
		String postId = sysUser.getPostId();
		String roleId = StringUtils.isEmpty(sysUser.getRoles()) ? "" : Coms.listToString(sysUser.getRoles());

		Date sd = DateTimeUtils.parseDateTime(st+" 00:00:00");
		Date ed = DateTimeUtils.parseDateTime(et+" 23:59:59");

		BzjsConfirmStatusDto bzjsdto = new BzjsConfirmStatusDto();
		bzjsdto.setStartDate(sd);
		bzjsdto.setEndDate(ed);
		bzjsdto.setPersonId(userId);
		bzjsdto.setPostId(postId);
		bzjsdto.setRoleIds(roleId);
		List<BzjsConfirmStatusVo> list = bzjsSrv.getConfirmStatusInfo(bzjsdto);

//		if(StringUtils.isNotEmpty(list)) {
//			String ndstr = DateTimeUtils.getNowDateTimeStr();
//			for (BzjsConfirmStatusVo obj : list) {
//				if(obj.getXbsj().compareTo(ndstr) > 0) {//未下班不统计
//					continue;
//				}
//				String rq = obj.getInputDate();
//				String key = rq+"_"+obj.getAccntObjIds()+"_"+obj.getBcdm();
//				if(!rlist.contains(key)) {
//					rlist.add(key);
//				}
//			}
//		}

		List<String> idList = new ArrayList<String>();
		for (BzjsConfirmStatusVo vo : list) {
			if(!idList.contains(vo.getFormDataId())) {
				idList.add(vo.getFormDataId());
			}
		}
		if(StringUtils.isNotEmpty(idList)) {
			String ndstr = DateTimeUtils.getNowDateTimeStr();

			List<TdsAccountData> dataList = new ArrayList<TdsAccountData>();
			if(idList.size() > 600) {
				List<List<String>> tlist = new ArrayList<List<String>>();
				for (int i = 0,il=idList.size(); i < il; i += 600) {
					tlist.add(idList.subList(i, Math.min(i + 600, il)));
				}
				for (List<String> list2 : tlist) {
					Where where = Where.create().eq(TdsAccountData::getTmused, 1).in(TdsAccountData::getDataid, list2.toArray());
					Order order = Order.create(TdsAccountData::getRq, "DESC");
					List<TdsAccountData> templist = srv.queryData(TdsAccountData.class, where, order, null);
					if(StringUtils.isNotEmpty(templist)) {
						dataList.addAll(templist);
					}
				}
			}else {
				Where where = Where.create().eq(TdsAccountData::getTmused, 1).in(TdsAccountData::getDataid, idList.toArray());
				Order order = Order.create(TdsAccountData::getRq, "DESC");
				dataList = srv.queryData(TdsAccountData.class, where, order, null);
			}
			dataList = filterConfirm(dataList);

			rlist = new ArrayList<String>();
			for (TdsAccountData obj : dataList) {
				if(obj.getXbsjstr().compareTo(ndstr) > 0) {//未下班不统计
					continue;
				}
				String key = obj.getRq()+"_"+obj.getUnitcode()+"_"+obj.getClsno();
				if(!rlist.contains(key)) {
					rlist.add(key);
				}
			}


		}

		return rlist;
	}
	//过滤不需要确认的数据
	private List<TdsAccountData> filterConfirm(List<TdsAccountData> list) {
		List<String> alist = new ArrayList<String>();
		List<TdsAccountData> flist = null;
		for (TdsAccountData obj : list) {
			if(StringUtils.isNotEmpty(obj.getCustomformcode()) && !alist.contains(obj.getCustomformcode())) {//只查自定义台账
				alist.add(obj.getCustomformcode());
			}
		}

		if(StringUtils.isNotEmpty(alist)) {
			List<TdsAccountFormSt> stlist = getStData(alist);
			if(StringUtils.isNotEmpty(stlist)) {
				//按accountId整理数据
				Map<String, List<TdsAccountFormSt>> map = new HashMap<String, List<TdsAccountFormSt>>();
				for (TdsAccountFormSt obj : stlist) {
					if(map.containsKey(obj.getAccountid())) {
						map.get(obj.getAccountid()).add(obj);
					}else {
						List<TdsAccountFormSt> tlist = new ArrayList<TdsAccountFormSt>();
						tlist.add(obj);
						map.put(obj.getAccountid(), tlist);
					}
				}

				List<TdsAccountData> rlist = new ArrayList<TdsAccountData>();
				for (TdsAccountData obj : list) {
					String aid = obj.getCustomformcode();
					if(StringUtils.isEmpty(aid)) {
						rlist.add(obj);
					}else {
						List<TdsAccountFormSt> tlist = map.get(aid);
						if(StringUtils.isEmpty(tlist)) {
							rlist.add(obj);
						}else {
							if(!haveStop(obj, tlist)) {
								rlist.add(obj);
							}
						}
					}
				}
				return rlist;
			}else {
				return list;
			}
		}
		return list;
	}
	//判断时间段内是否有停止情况
	private boolean haveStop(TdsAccountData data, List<TdsAccountFormSt> tlist) {
		boolean haveStop = false;

		Date sbsj = DateTimeUtils.parseDateTime(data.getSbsjstr());
		Date xbsj = DateTimeUtils.parseDateTime(data.getXbsjstr());

		for (TdsAccountFormSt obj : tlist) {
			if(obj.getStartTime()==null) {//只有停止时间
				if(DateTimeUtils.bjDate(sbsj, obj.getStopTime()) == 1 || DateTimeUtils.bjDate(sbsj, xbsj, obj.getStopTime())) {//停止时间在上班之前或在上下班时间间
					haveStop = true;
					break;
				}
			}else {
				Date st = obj.getStopTime();//停止时间
				Date et = obj.getStartTime();//启动时间
				if(DateTimeUtils.bjDate(st, xbsj) == 1 || DateTimeUtils.bjDate(sbsj, et) == 1) {//停止时间在下班后 或 启动时间在上班前 略过
					continue;
				}
				//如果启停时间与上下班时间有交集，判断为包含停止情况
				if(DateTimeUtils.bjDate(sbsj, xbsj, st) || DateTimeUtils.bjDate(sbsj, xbsj, et) || DateTimeUtils.bjDate(st, et, sbsj) || DateTimeUtils.bjDate(st, et, xbsj)) {
					haveStop = true;
					break;
				}
			}
		}

		return haveStop;
	}
	//获取相关自定义台账启停时间
	private List<TdsAccountFormSt> getStData(List<String> idList) {
		List<TdsAccountFormSt> rlist = null;
		if(StringUtils.isNotEmpty(idList)) {
			Where where = Where.create();
			where.eq(TdsAccountFormSt::getTmused, 1);
			where.in(TdsAccountFormSt::getAccountid, idList.toArray());//
			Order order = Order.create();
			order.order(TdsAccountFormSt::getAccountid).orderByDesc(TdsAccountFormSt::getStopTime);
			List<TdsAccountFormSt> list = srv.queryData(TdsAccountFormSt.class, where, null, null);
			return list;
		}

		return rlist;
	}

	@Override
	public List<Costuint> getOrgUnitList(String orgCode) {
		List<Costuint> rlist = new ArrayList<Costuint>();
		List<Costuint> listCostuint = costService.getCostuintListOrgId(orgCode);
		if (listCostuint != null && listCostuint.size() > 0) {
			for (int i = 0; i < listCostuint.size(); i++) {
				Costuint e = listCostuint.get(i);
				int ledgerEntry = e.getLedgerEntry()==null?1:e.getLedgerEntry();
				if(ledgerEntry==1) {
					rlist.add(e);
				}
			}
		}
		return rlist;
	}

	@Override
	public List<TdsAccountFormVo> getAccountFormManageList(AccountParam dto) {
		List<TdsAccountFormVo> rlist = new ArrayList<TdsAccountFormVo>();

		aformSrv.initDefaultAccount(null);

		String orgCode = dto.getOrgCode();
		String unitCode = dto.getUnitid();

		Where where = Where.create();
		where.eq(TdsAccountForm::getTmused, 1);
		where.eq(TdsAccountForm::getTypeCode, 1);//只查自定义台账
		if(StringUtils.isEmpty(unitCode) || "0".equals(unitCode)) {
			if(StringUtils.isEmpty(orgCode) || "0".equals(orgCode)) {
				where.and().lb().eq(TdsAccountForm::getUnitCode, "").or().isEmpty(TdsAccountForm::getUnitCode).rb();
			}else {
				List<Costuint> clist = getOrgUnitList(orgCode);
				if(StringUtils.isEmpty(clist)) {
					where.eq(TdsAccountForm::getUnitCode, "0");
				}else {
					int c = 1;
					where.and().lb();
					for (Costuint cost : clist) {
						if(c > 1) {
							where.or();
						}
						where.like(TdsAccountForm::getUnitCode, cost.getId());
						c++;
					}
					where.rb();
				}
			}
		}else {
			where.like(TdsAccountForm::getUnitCode, unitCode);
		}
		Order order = Order.create();
		order.order(TdsAccountForm::getTmsort);
		order.order(TdsAccountForm::getId);
		List<TdsAccountForm> list = srv.queryData(TdsAccountForm.class, where, order, null);

		SFFormQueryDto formdto = new SFFormQueryDto();
		formdto.setCategory(this.accountTypeId);//台账分类
//		formdto.setFilterByPerm(false);//个人相关的
		List<SFForm> formList = formService.queryFormInfoList(formdto, null);
		Map<String, String> fmap = new HashMap<String, String>();
		for (SFForm obj : formList) {
			fmap.put(obj.getId(), obj.getName());
		}

		List<String> fidlist = new ArrayList<String>();

		if(StringUtils.isNotEmpty(list)) {
			for (TdsAccountForm obj : list) {
				TdsAccountFormVo vo = ObjUtils.copyTo(obj, TdsAccountFormVo.class);
				if(!new Integer(0).equals(obj.getTypeCode())) {
					vo.setTagConf("管理");
					vo.setManageConf("管理");
				}
				vo.setFormName(fmap.get(vo.getFormCode()));
				rlist.add(vo);
				if(!fidlist.contains(obj.getFormCode())) {
					fidlist.add(obj.getFormCode());
				}
			}
		}

//		if(StringUtils.isEmpty(orgCode) || "0".equals(orgCode)) {
//			return rlist;
//		}

		if(StringUtils.isNotEmpty(formList) && StringUtils.isEmpty(list)) {// && StringUtils.isEmpty(list)
			Map<String, String> defaultBindMap = getDefaultBindMap(fmap.keySet(), unitCode);

			for (SFForm form : formList) {
				if(!fidlist.contains(form.getId())) {
					TdsAccountFormVo vo = new TdsAccountFormVo();
					vo.setId(TMUID.getUID());
					vo.setTypeCode(2);
					vo.setAccountName(form.getName());
					vo.setFormName(form.getName());
					vo.setFormCode(form.getId());
					vo.setManageRound(defaultBindMap.get(form.getId()));//默认台账
					rlist.add(vo);
				}
			}
		}

		return rlist;
	}
	//获取默认台账工位绑定数据
	private Map<String, String> getDefaultBindMap(Set<String> keySet, String unitCode) {
		Map<String, String> rmap = new HashMap<String, String>();
		if(StringUtils.isEmpty(unitCode)) {
			return rmap;
		}
		try {
			// 检索条件
			Where where = Where.create();
			where.eq(TdsAccountFormSfManage::getTmused, 1);
			where.eq(TdsAccountFormSfManage::getUnitCode, unitCode);
			if (StringUtils.isNotEmpty(keySet)) {
				where.in(TdsAccountFormSfManage::getFormCode, keySet.toArray());
			}
			// 排序
			Order order = Order.create();
			order.orderByAsc(TdsAccountFormSfManage::getTmsort);
			List<TdsAccountFormSfManage> list = srv.queryData(TdsAccountFormSfManage.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				for (TdsAccountFormSfManage obj : list) {
					if(rmap.containsKey(obj.getFormCode())) {
						rmap.put(obj.getFormCode(), rmap.get(obj.getFormCode()) + "," + obj.getManageName());
					}else {
						rmap.put(obj.getFormCode(), obj.getManageName());
					}
				}
			}
		} catch (Exception e) {
		}

		return rmap;
	}

	//获取默认台账工位绑定数据
	private Map<String, Map<String, TdsAccountFormSfManage>> getDefaultBindMap(List<String> unitList, List<String> stationList) {
		Map<String, Map<String, TdsAccountFormSfManage>> rmap = new HashMap<String, Map<String, TdsAccountFormSfManage>>();
		try {
			// 检索条件
			Where where = Where.create();
			where.eq(TdsAccountFormSfManage::getTmused, 1);
			where.in(TdsAccountFormSfManage::getManageCode, stationList.toArray());
			where.in(TdsAccountFormSfManage::getUnitCode, unitList.toArray());
			where.notEmpty(TdsAccountFormSfManage::getFormCode);
			// 排序
			Order order = Order.create();
			order.orderByAsc(TdsAccountFormSfManage::getUnitCode);
			order.orderByAsc(TdsAccountFormSfManage::getTmsort);
			List<TdsAccountFormSfManage> list = srv.queryData(TdsAccountFormSfManage.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				for (TdsAccountFormSfManage obj : list) {
					if(rmap.containsKey(obj.getUnitCode())) {
						Map<String, TdsAccountFormSfManage> tmap = rmap.get(obj.getUnitCode());
						if(tmap.containsKey(obj.getFormCode())) {
							TdsAccountFormSfManage old = tmap.get(obj.getFormCode());
							int oldlevel = getManageLevel(old);
							int newlevel = getManageLevel(obj);
							if(newlevel > oldlevel) {
								tmap.put(obj.getFormCode(), obj);
							}
						}else {
							tmap.put(obj.getFormCode(), obj);
						}
					}else {
						Map<String, TdsAccountFormSfManage> tmap = new HashMap<String, TdsAccountFormSfManage>();
						tmap.put(obj.getFormCode(), obj);
						rmap.put(obj.getUnitCode(), tmap);
					}
				}
			}
		} catch (Exception e) {
		}

		return rmap;
	}

	private int getManageLevel(TdsAccountFormSfManage obj) {
		int rv = 0;//查询级
		if(new Integer(1).equals(obj.getSearchMark())) {
			rv = 0;
		}
		if(new Integer(1).equals(obj.getAddMark())) {
			rv = 1;
		}
		if(new Integer(1).equals(obj.getManageMark())) {
			rv = 2;
		}
		if(new Integer(2).equals(obj.getManageBound())) {
			rv = 9;
		}
		return rv;
	}

	@Override
	public TdsAccountData getAccountData(TdsAccountData data) {
		Where where = Where.create();
		where.eq(TdsAccountData::getTmused, 1);
		where.eq(TdsAccountData::getRq, data.getRq());
		where.eq(TdsAccountData::getClsno, data.getClsno());
		where.eq(TdsAccountData::getUnitcode, data.getUnitcode());
		if(StringUtils.isNotEmpty(data.getCustomformcode())) {
			where.eq(TdsAccountData::getCustomformcode, data.getCustomformcode());
		}
		List<TdsAccountData> dataList = srv.queryList(TdsAccountData.class, where, null);
		if(StringUtils.isNotEmpty(dataList)) {
			return dataList.get(0);
		}
		return null;
	}

	@Override
	public List<TdsAccountSTVo> getAccountSTList(AccountParam dto) {
		List<TdsAccountSTVo> rlist = new ArrayList<TdsAccountSTVo>();

//		String orgCode = dto.getOrgCode();
		String unitCode = dto.getUnitid();

		Where where = Where.create();
		where.eq(TdsAccountForm::getTmused, 1);
		where.eq(TdsAccountForm::getTypeCode, 1);//只查自定义台账
		if(StringUtils.isEmpty(unitCode)) {
			return rlist;
		}else {
			where.like(TdsAccountForm::getUnitCode, unitCode);
		}
		Order order = Order.create();
		order.order(TdsAccountForm::getTmsort);
		order.order(TdsAccountForm::getId);
		List<TdsAccountForm> list = srv.queryData(TdsAccountForm.class, where, order, null);

		if(StringUtils.isNotEmpty(list)) {

			SFFormQueryDto formdto = new SFFormQueryDto();
			formdto.setCategory(this.accountTypeId);//台账分类
			List<SFForm> formList = formService.queryFormInfoList(formdto, null);
			Map<String, String> fmap = new HashMap<String, String>();
			for (SFForm obj : formList) {
				fmap.put(obj.getId(), obj.getName());
			}

			List<String> idList = new ArrayList<String>();
			for (TdsAccountForm obj : list) {
				idList.add(obj.getId());
			}
			Map<String, TdsAccountFormSt> stmap = getAccountLastST(idList);

			if(StringUtils.isNotEmpty(list)) {
				for (TdsAccountForm obj : list) {
					TdsAccountSTVo vo = ObjUtils.copyTo(obj, TdsAccountSTVo.class);
					vo.setFormName(fmap.get(obj.getFormCode()));

					TdsAccountFormSt ast = stmap.get(obj.getId());
					if(ast==null || ast.getStartTime()!=null) {
						vo.setRunState("1");//运行
						if(ast!=null) {
							vo.setLastUpdTime(ast.getStartTimeStr());
						}
					}else {
						vo.setRunState("0");//停止
						if(ast!=null) {
							vo.setLastUpdTime(ast.getStopTimeStr());
						}
					}
					rlist.add(vo);
				}
			}
		}


//		if(StringUtils.isEmpty(orgCode) || "0".equals(orgCode)) {
//			return rlist;
//		}



		return rlist;
	}
	//获取最后操作记录
	private Map<String, TdsAccountFormSt> getAccountLastST(List<String> idList) {
		Map<String, TdsAccountFormSt> rmap = new HashMap<String, TdsAccountFormSt>();
		Where where = Where.create();
		where.eq(TdsAccountFormSt::getTmused, 1);
		where.in(TdsAccountFormSt::getAccountid, idList.toArray());//自定义台账
		Order order = Order.create();
		order.orderByDesc(TdsAccountFormSt::getStopTime);
		List<TdsAccountFormSt> list = srv.queryData(TdsAccountFormSt.class, where, order, null);
		for (TdsAccountFormSt obj : list) {
			if(!rmap.containsKey(obj.getAccountid())) {
				rmap.put(obj.getAccountid(), obj);
			}
		}

		return rmap;
	}

	@Override
	public List<TdsAccountFormSt> getSTList(AccountParam dto) {
		String id = dto.getId();
		Where where = Where.create();
		where.eq(TdsAccountFormSt::getTmused, 1);
		where.eq(TdsAccountFormSt::getAccountid, id);//自定义台账
		Order order = Order.create();
		order.orderByDesc(TdsAccountFormSt::getStopTime);
		List<TdsAccountFormSt> list = srv.queryData(TdsAccountFormSt.class, where, order, null);
		return list;
	}

	@Override
	public Boolean changeRunStatus(AccountParam dto) {
		Boolean flag = false;

		String id = dto.getId();//自定义台账ID
		String op = dto.getOp();//启停操作 启动1  停止其他
		String optime = dto.getOpTime();//启停时间

		TdsAccountFormSt currObj = null;
		List<TdsAccountFormSt> list = getSTList(dto);
		if(StringUtils.isNotEmpty(list)) {
			currObj = list.get(0);
			if(currObj.getStartTime()!=null) {
				currObj = null;
			}
		}

		SysUser user = SysUserHolder.getCurrentUser();
		if("1".equals(op)) {//启动
			if(currObj!=null) {
				currObj.setStartTimeStr(optime);
				currObj.setStartTime(DateTimeUtils.parseDateTime(optime));
				currObj.setStartOpTime(DateTimeUtils.getNowDate());
				currObj.setStartUserId(user.getId());
				currObj.setStartUserName(user.getRealName());
				flag = 1 == srv.updateById(currObj);
			}else {

			}
		}else {//停止
			if(currObj==null) {
				TdsAccountFormSt obj = new TdsAccountFormSt();
				obj.setId(TMUID.getUID());
				obj.setAccountid(id);
				obj.setStopTimeStr(optime);
				obj.setStopTime(DateTimeUtils.parseDateTime(optime));
				obj.setStopOpTime(DateTimeUtils.getNowDate());
				obj.setStopUserId(user.getId());
				obj.setStopUserName(user.getRealName());
				obj.setTmused(1);

				flag = 1 == srv.insert(obj);
			}else {

			}
		}
		if(flag) {//清除待办缓存
			refreshAccountConfirmTodo(null);
//			todoSrv.clearTodoCached("tm4-form", "account_classConfirm");
		}

		return flag;
	}

	@Override
	public Boolean deleteStData(AccountParam dto) {
		Boolean flag = true;
		String id = dto.getId();
		List<String> idList = Coms.StrToList(id, ",");
		if(StringUtils.isNotEmpty(idList)) {
			Where where = Where.create();
			where.eq(TdsAccountFormSt::getTmused, 1);
			where.in(TdsAccountFormSt::getId, idList.toArray());//
			List<TdsAccountFormSt> list = srv.queryData(TdsAccountFormSt.class, where, null, null);
			if(StringUtils.isNotEmpty(list)) {
				for (TdsAccountFormSt obj : list) {
					obj.setTmused(0);
				}
				flag = 1 == srv.updateBatch(list);
				if(flag) {
					refreshAccountConfirmTodo(null);
				}
			}
		}

		return flag;
	}

	@Override
	public String getAccountConfirmDays(AccountParam dto) {
		//根据接口获取对应人员未确认的数据ID集合
		int days = 7;//查询范围天数，参数获取，默认7天

		ApplyParams p = new ApplyParams();
		p.setApplyAlias("account_confirm_todo_days");
		String daysStr = applySrv.getApplyConfValue(p);
		if(Coms.judgeLong(daysStr)) {
			int ti = Integer.parseInt(daysStr);
			if(ti > 0) {
				days = ti;
			}
		}

		return days+"";
	}

	@Override
	public List<Map<String, String>> getAccountVarList(AccountParam dto) {

		/*
		 * 参数传入判断，设置是 活动 或 台账模型，提供的变量不同
		 * 活动：提供 台账.变量（默认为第一个台账），查询对应活动关联表单中的台账模型列表，提供对应变量（台账模型列表根据数据进行动态显示）
		 * 台账模型：仅提供台账变量，前缀为台账数据；变量为台账数据.变量
		 */
		String activeId = dto.getActiveId();
		String ledgerId = dto.getLedgerId();
		Boolean isActive = true;//是否是活动
//		List ledgerList = new ArrayList();//台账模型信息
		List<DigitalLedgerForm> activeFormList = null;
		if(StringUtils.isNotEmpty(ledgerId)) {
			isActive = false;
			activeFormList = getActiveFormList(activeId, ledgerId);
		}else {
			//根据活动获取所有台账模型信息
			activeFormList = getActiveFormList(activeId, null);
		}

		List<Map<String, String>> rlist = new ArrayList<Map<String,String>>();
		List<MtmFormulaTreeVo> result = new ArrayList<MtmFormulaTreeVo>();

//		List<MtmThemesVo> themesList = themesServ.getData(1);//获取正在使用的主题
//		for(MtmThemesVo temp:themesList) {
			MtmFormulaTreeVo themesBean = new MtmFormulaTreeVo();
//			themesBean.setFormulaName(temp.getThemesName());
//			themesBean.setFormulaCode(temp.getId());

			themesBean.setFormulaCode("tz");
			themesBean.setFormulaName("台账");
			themesBean.setIsLeaf(0);//非公式节点
			result.add(themesBean);//添加到根节点

			List<MtmFormulaTreeVo> varList = gsList("form", "表单");//gsList(temp.getId(), temp.getThemesName())
			themesBean.getChildren().addAll(varList);
			for(DigitalLedgerForm obj: activeFormList) {
				if(isActive) {
					List<MtmFormulaTreeVo> varList2 = gsList(obj.getFormId(), obj.getFormName());//表单
					themesBean.getChildren().addAll(varList2);
				}else {
					List<MtmFormulaTreeVo> varList2 = gsList("data", obj.getFormName());//模型
					themesBean.getChildren().addAll(varList2);
				}
			}

			List<MtmFormulaTreeVo> vlist = themesBean.getChildren();
			if(StringUtils.isNotEmpty(vlist)) {
				for (MtmFormulaTreeVo vo : vlist) {
					Map<String, String> map = new HashMap<String, String>();
					map.put("code", "tz."+vo.getFormulaCode());
					map.put("name", vo.getFormulaName());
					rlist.add(map);
				}
			}
//		}

	    return rlist;
	}
	private List<MtmFormulaTreeVo> gsList(String pid, String name) {
		List<MtmFormulaTreeVo> list = new ArrayList<MtmFormulaTreeVo>();
		list.add(newFormula(name+".总数量", pid+".total", name+".总数量", 1, 1));//
		list.add(newFormula(name+".已填写数量", pid+".writeNum", name+".已填写数量", 1, 2));//
		list.add(newFormula(name+".未填写数量", pid+".unwriteNum", name+".未填写数量", 1, 3));
		list.add(newFormula(name+".填写率", pid+".writeRate", name+".填写率", 1, 4));
		list.add(newFormula(name+".超限数量", pid+".overNum", name+".超限数量", 1, 5));
		list.add(newFormula(name+".超限率", pid+".overRate", name+".超限率", 1, 6));
		return list;
	}
	/*
	 * 公式封装
	 */
	private MtmFormulaTreeVo newFormula(String text, String code, String desc, Integer isLeaf, Integer tmSort) {
		MtmFormulaTreeVo formula = new MtmFormulaTreeVo();
		formula.setFormulaName(text);
		formula.setFormulaCode(code);
		formula.setFormulaDesc(desc);
		formula.setIsLeaf(isLeaf);//公式节点
		formula.setTmSort(tmSort);
		return formula;
	}
	/*
	 * 获取活动表单列表
	 */
	private List<DigitalLedgerForm> getActiveFormList(String activeId, String ledgerId) {
		List<DigitalLedgerForm> list = new ArrayList<DigitalLedgerForm>();
		if(StringUtils.isNotEmpty(ledgerId)) {
			DigitalLedgerModule ledger = srv.queryObjectById(DigitalLedgerModule.class, ledgerId);
			DigitalLedgerForm obj = new DigitalLedgerForm();
			obj.setFormName(ledger.getModuleName());

			list.add(obj);
		}else {
//			Where where = Where.create();
//			where.eq(DigitalLedgerForm::getTmused, 1);
//			where.eq(DigitalLedgerForm::getActivityId, activeId);
//			Order order = Order.create();
//			order.order(DigitalLedgerForm::getTmsort);
//			list = srv.queryData(DigitalLedgerForm.class, where, order, null);
			Where where = Where.create();
			where.eq(JoblistPersonBind::getTmused, 1);
			where.eq(JoblistPersonBind::getPid, activeId);
			where.eq(JoblistPersonBind::getBindtype, 4);
			List<JoblistPersonBind> tlist = srv.queryData(JoblistPersonBind.class, where, null, null);
			if(StringUtils.isNotEmpty(tlist)) {
				for (JoblistPersonBind job : tlist) {
					DigitalLedgerForm obj = new DigitalLedgerForm();
					obj.setFormId(job.getBindid());
					obj.setFormName(job.getBindname());

					list.add(obj);
				}

			}
		}
        return list;
	}


	public String parseFormula(AccountParam dto) {


		return null;
	}

}
