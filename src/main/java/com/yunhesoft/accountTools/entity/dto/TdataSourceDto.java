package com.yunhesoft.accountTools.entity.dto;

import com.yunhesoft.system.tds.entity.po.TdsinPara;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @Author: gzz
 * @Date: 2025/4/9 14:39
 **/
@Data
public class TdataSourceDto {


    private String id;

    // 数据源别名
    private String tdsalias;

    // 数据源名称
    private String tdsname;

    //输入参数信息
    List<TdsinPara> tdsinParaList;

}
