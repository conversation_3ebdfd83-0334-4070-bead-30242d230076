package com.yunhesoft.accountTools.entity.dto;


import java.util.List;

import com.yunhesoft.accountTools.entity.po.TdsAccountFormSfManage;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormManageVo;

import lombok.Getter;
import lombok.Setter;


/**
 *	机构岗位人员保存类
 */
@Setter
@Getter
public class OrgPostUserSaveDto {
	
	private String modeType; //类型：1-人；2-岗；3-机构；
	
	private String accountid; //台账id
	
    private String editType; //del-删除；save-保存；
    
    private List<TdsAccountFormManageVo> orgPostUserSaveList; //机构岗位人员保存数据
    
    private String unitCode;
	private String formCode;
	private String orgMark;
	private List<TdsAccountFormSfManage> defaultBindList;//默认台账表单绑定工位数据
}
