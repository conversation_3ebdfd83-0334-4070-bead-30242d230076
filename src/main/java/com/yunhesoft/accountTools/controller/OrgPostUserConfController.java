package com.yunhesoft.accountTools.controller;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.accountTools.entity.dto.OrgPostUserQueryDto;
import com.yunhesoft.accountTools.entity.dto.OrgPostUserSaveDto;
import com.yunhesoft.accountTools.entity.po.TdsAccountFormSfManage;
import com.yunhesoft.accountTools.service.IOrgPostUserConfService;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormManageVo;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@RestController
@RequestMapping("/accountTools/orgPostUserConf")
@Api(tags = "机构、岗位、人员配置")
public class OrgPostUserConfController extends BaseRestController {

	
	@Autowired
	private IOrgPostUserConfService orgPostUserService; // 服务类
	
	
	/**
	 *	获取机构岗位人员配置（附加其他属性）
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/loadDataTdsAccountFormManage", method = RequestMethod.POST)
	@ApiOperation("获取机构岗位人员配置")
	public Res<?> loadDataTdsAccountFormManage(@RequestBody OrgPostUserQueryDto queryDto) {
		Res<List<TdsAccountFormManageVo>> res = new Res<List<TdsAccountFormManageVo>>();
		List<TdsAccountFormManageVo> list = orgPostUserService.loadDataTdsAccountFormManage(queryDto);
		res.setResult(list);
		return res;
	}
	
	/**
	 *	保存机构岗位人员配置数据
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/saveTdsAccountFormManageData", method = RequestMethod.POST)
	@ApiOperation("保存机构岗位人员配置数据")
	public Res<?> saveTdsAccountFormManageData(@RequestBody OrgPostUserSaveDto saveDto) {
		return Res.OK(orgPostUserService.saveTdsAccountFormManageData(saveDto));
	}
	
	/**
	 *	获取机构岗位人员配置（附加其他属性）默认台账
	 * @param queryDto
	 * @return
	 */
	@RequestMapping(value = "/loadDataTdsAccountFormManageDefault", method = RequestMethod.POST)
	@ApiOperation("获取机构岗位人员配置")
	public Res<?> loadDataTdsAccountFormManageDefault(@RequestBody OrgPostUserQueryDto queryDto) {
		Res<List<TdsAccountFormSfManage>> res = new Res<List<TdsAccountFormSfManage>>();
		List<TdsAccountFormSfManage> list = orgPostUserService.loadDataTdsAccountFormManageDefault(queryDto);
		res.setResult(list);
		return res;
	}
	
	/**
	 *	保存机构岗位人员配置数据 默认台账
	 * @param saveDto
	 * @return
	 */
	@RequestMapping(value = "/saveTdsAccountFormManageDataDefault", method = RequestMethod.POST)
	@ApiOperation("保存机构岗位人员配置数据")
	public Res<?> saveTdsAccountFormManageDataDefault(@RequestBody OrgPostUserSaveDto saveDto) {
		return Res.OK(orgPostUserService.saveTdsAccountFormManageDataDefault(saveDto));
	}
}
