package com.yunhesoft.system.post.entity.vo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 单个岗位信息节点（岗位组件使用）
 * @category 单个岗位信息节点
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "单个岗位信息节点")
public class PostCompVo {
	/** 当前节点id */
	private String id;

	/** 父节点id */
	private String pid;

	/** 当前节点名称 */
	private String label;

	/** 是否有子节点 */
	private Boolean hasChildren;

	/** 是否有子节点（目前没用上，为空） */
	private List<PostCompVo> children;

	/** 角色记录ID */
	private String roleTmuid;

	/** 角色ID（外键,关联:sys_role.id） */
	private String roleid;

	/** 角色名称 */
	private String rolename;
}
