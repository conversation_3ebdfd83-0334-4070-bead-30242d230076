package com.yunhesoft.system.msg.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发消息 - 短信参数【预留】
 * 
 * <AUTHOR> 2023.02.09
 */
@Data
@ApiModel(description = "发消息-短信参数【预留】")
public class MsgObjectParamSMS {

	@ApiModelProperty(value = "模板类型", example = "", required = true, hidden = false)
	private String templateType;
	
	@ApiModelProperty(value = "模板参数", example = "")
	private String templateParam;

}
