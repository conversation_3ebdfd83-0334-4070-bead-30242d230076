package com.yunhesoft.system.msg.entity.po;

import java.io.Serializable;

import org.springframework.data.mongodb.core.mapping.Document;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 系统消息实体 for MongoDB
 * 
 * <AUTHOR>
 *
 */
@Data
@ApiModel("系统操作日志")
@Document(collection = "SysMsgInfo")
public class SysMsgInfo implements Serializable {

	private static final long serialVersionUID = 1L;
	// 主键id
	private String id;
	// 模块编码
	private String moduleCode;
	// 模块名称
	private String moduleName;
	// 发送人id
	private String senderId;
	// 发送人
	private String sender;
	// 消息标题
	private String title;
	// 消息内容
	private String content;

	// 操作地址url
	private String url;

	// 页面参数
	private String urlParams;

	// 操作时间
	private String createTime;
	// 创建时间戳
	private long createTimestamp;

	// 租户id
	private String tenant_id = "0";

	/**
	 * 过期时间 推荐使用expireAt模式 可以运行时配置过期时间 (ttl模式无法修改失效间隔配置)
	 */
	// @Indexed(expireAfterSeconds = 0)
	// private Date expireAt;

}
