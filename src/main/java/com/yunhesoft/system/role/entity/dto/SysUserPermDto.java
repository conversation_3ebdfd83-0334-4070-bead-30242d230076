package com.yunhesoft.system.role.entity.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 角色权限查询参数
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "人员权限查询参数", description = "人员权限查询参数")
public class SysUserPermDto {

	/** 人员id */
	@ApiModelProperty(value = "userid")
	@NotBlank(message = "userid不能为空")
	private String userid;

	/** 权限id */
	@ApiModelProperty(value = "permid")
	private String permid;

	/** 权限id */
	@ApiModelProperty(value = "permids")
	private List<String> permids;

}
