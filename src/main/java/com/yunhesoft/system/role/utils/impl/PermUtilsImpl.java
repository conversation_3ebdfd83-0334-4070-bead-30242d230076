package com.yunhesoft.system.role.utils.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.role.service.*;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.IEmployeeBasicOperationService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.menu.entity.po.SysMenu;
import com.yunhesoft.system.menu.service.ISysMenuLibService;
import com.yunhesoft.system.menu.service.SysMenuService;
import com.yunhesoft.system.role.entity.dto.EmpPermDto;
import com.yunhesoft.system.role.entity.dto.EmpRoleDto;
import com.yunhesoft.system.role.entity.dto.SysUserPermDto;
import com.yunhesoft.system.role.entity.dto.UserRoleDto;
import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.entity.po.SysRolePerm;
import com.yunhesoft.system.role.entity.po.SysUserPerm;
import com.yunhesoft.system.role.entity.po.SysUserRole;
import com.yunhesoft.system.role.utils.IPermUtils;

import lombok.extern.log4j.Log4j2;

/**
 * 权限工具类
 *
 * <AUTHOR>
 * @category 权限工具类
 * @date 2021/04/23
 */
@Service
@Log4j2
public class PermUtilsImpl implements IPermUtils {

    @Autowired
    private ISysUserRoleService sysUserRoleService; // 人员拥有角色

    @Autowired
    private ISysRolePermService sysRolePermService; // 角色拥有权限
    @Autowired
    private ISysMenuLibService iSysMenuLibService;

    @Autowired
    private ISysUserPermService sysUserPermService; // 人员拥有权限关系

    @Autowired
    private ISysRoleService sysRoleSer; // 角色信息

    @Autowired
    private SysMenuService menuService; // 菜单

    @Autowired
    private IEmployeeBasicOperationService empServ; // 人员
    //@Autowired
    //private SysMenuService sysMenuService;

    @Autowired
    private RedisUtil redis;

    @Autowired
    private ISysRoleLevelService roleLevelService;
    private EntityService dao;

    /**
     * 判断当前用户是否拥有权限
     *
     * @param permStr 权限字符串 (system:config:index:add)
     * @return
     */
    @Override
    public boolean isHasPerm(String permStr) {
        boolean result = false;
        if (permStr == null || permStr.length() <= 0) {
            result = false;
        } else {
            if (SysUserUtil.getCurrentUser() == null) {
                result = false;
            } else {
                List<String> list = SysUserUtil.getCurrentUser().getPermissions();
                if (StringUtils.isNotEmpty(list)) {
                    if (list.contains(permStr)) {
                        result = true;
                    } else {
                        result = false;
                    }
                } else {
                    result = false;
                }
            }
        }
        return result;

    }

    /**
     * 判断当前用户是否拥有权限(菜单根据不同模式创建的，比如主题菜单)
     *
     * @param tmSysType 参数类似 (tmSysType=类型id 主题菜单就是主题id)
     * @param permId    权限id (system:config:index:add)
     * @return
     */
    @Override
    public boolean isHasTypePerm(String tmSysType, String permId) {
        boolean result = false;
        if (StringUtils.isNotEmpty(tmSysType) && StringUtils.isNotEmpty(permId)) {// 获取到了参数
            SysUser user = SysUserUtil.getCurrentUser();
            if (user != null) {// 获取到了user
                if (SysUserUtil.isAdmin()) {// 如果是超级管理员 或 租户管理员，返回true
                    result = true;
                } else {
                    List<String> list = user.getPermissions();
                    if (StringUtils.isNotEmpty(list)) {// 获取到了人员权限列表
                        for (int i = 0; i < list.size(); i++) {
                            String rightListStr = list.get(i);
                            // assCenter:oec:assessIndex?tmSysType=ZZW32Y7303CPCY44P00125:addOec
                            String[] rightArr = rightListStr.split("\\?");// 将权限路径和参数及权限编码放到数组中
                            if (rightArr == null || rightArr.length < 2) {// 如果数组未获取到参数返回false
                                result = false;
                            } else {
                                String[] paramRight = rightArr[1].split("\\:");// 获取参数及权限
                                if (paramRight == null || paramRight.length < 2) {// 没有权限编码返回false
                                    result = false;
                                } else {
                                    String paramStr = paramRight[0];
                                    String rightStr = paramRight[1];
                                    if (paramStr.contains("tmSysType=" + tmSysType)
                                            && permId.equals(rightArr[0] + ":" + rightStr)) {// 参数中包含传来的参数值，并且按钮编码和传过来的按钮编码一至，返回true;
                                        result = true;
                                        break;
                                    }
                                }

                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 判断登录人员是否具有某个权限（全生命周期专用）
     *
     * @param tmSysType 参数类型
     * @param perms     权限编码
     * @return
     */
    @Override
    public boolean isHashPerm(String tmSysType, String perms) {
        boolean result = false;
        if (perms == null || perms.length() <= 0 || tmSysType == null || tmSysType.length() <= 0) {// 传过来的参数无䇅，返回false
            result = false;
        } else {
            if (SysUserUtil.getCurrentUser() == null) {// 未获取到登录用户 返回false
                result = false;
            } else {
                if (isAdmin(SysUserUtil.getCurrentUser().getId())) {// 如果是超级管理员 ，返回true
                    result = true;
                } else if (isTenantAdmin()) {// 租户管理员
                    result = true;
                } else {
                    List<String> list = SysUserUtil.getCurrentUser().getPermissions();
                    if (StringUtils.isNotEmpty(list)) {// 获取到了人员权限列表
                        for (int i = 0; i < list.size(); i++) {
                            String rightListStr = list.get(i);
                            // project:manage:pm:mtmProjAssess:mtmProjAssessRelease?abc=123&type=clwzgl:mtmAssessDel
                            String[] rightArr = rightListStr.split("\\?");// 将权限路径和参数及权限编码放到数组中
                            if (rightArr == null || rightArr.length < 2) {// 如果数组未获取到参数返回false
                                result = false;
                            } else {
                                String[] paramRight = rightArr[1].split("\\:");// 获取参数及权限
                                if (paramRight == null || paramRight.length < 2) {// 没有权限编码返回false
                                    result = false;
                                } else {
                                    String paramStr = paramRight[0];
                                    String rightStr = paramRight[1];
                                    if (paramStr.contains("tmSysType=" + tmSysType) && perms.equals(rightStr)) {// 参数中包含传来的参数值，并且按钮编码和传过来的按钮编码一至，返回true;
                                        result = true;
                                        break;
                                    }
                                }

                            }
                        }
                    } else {
                        result = false;
                    }
                }
            }
        }
        return result;
    }
    /**
     * 判断是否拥有按钮权限
     *
     * @param userid 用户id
     * @param permId 权限id
     * @return
     */
//	@Override
//	public boolean isHasPerm(String userId, String permId) {
//		boolean bln = false;
//		if (StringUtils.isNotEmpty(userId) && StringUtils.isNotEmpty(permId)) {
//			List<String> list = this.getPermissions(userId, permId);
//			if (StringUtils.isNotEmpty(list) && list.contains("*")) {// 超级管理员
//				return true;
//			} else {
//				if (list.contains(permId)) {
//					return true;
//				}
//			}
//		}
//		return bln;
//	}

    /**
     * 判断是否拥有权限
     *
     * @param userid   用户id
     * @param menuId   菜单id
     * @param actionId 按钮id
     * @return
     */
//	@Override
//	public boolean isHasPerm(SysUserPermDto params) {
//		return this.isHasPerm(params.getPermid());
//	}

    /**
     * 获取用户拥有的权限列表
     *
     * @param userId 用户id
     * @param type   M：菜单权限；B：按钮权限：ALL：全部
     * @return
     */
    @Override
    public Map<String, Object> getPerms(String userId, String type) {
        return this.getPerms("1", userId, type);
    }

    @Override
    public Map<String, Object> getPerms(String mode, String userId, String type) {
        Map<String, Object> rtnMap = new HashMap<String, Object>();
        rtnMap.put("userId", userId);
        if (isAdmin(userId)) {// 超级管理员
            rtnMap.put("isAdmin", true);
        } else {
            UserRoleDto userRoleDto = new UserRoleDto();
            userRoleDto.setUserid(userId);
            List<SysUserRole> listRoles = sysUserRoleService.getUserRole(userRoleDto); // 查询人拥有哪些角色
            if (this.isTenantAdmin(listRoles)) {// 租户管理员
                rtnMap.put("isTenantAdmin", true);
                List<String> listRoleId = new ArrayList<String>();
                if (listRoles != null) {
                    for (SysUserRole e : listRoles) {
                        listRoleId.add(e.getRoleid());
                    }
                }
                rtnMap.put("roles", listRoleId);
            } else {
                List<String> list = this.getPermissions(mode,userId, listRoles);
                if (StringUtils.isNotEmpty(list) && list.contains("*")) {// 超级管理员
                    rtnMap.put("isAdmin", true);
                } else {
                    rtnMap.put("isAdmin", false);
                    if ("M".equalsIgnoreCase(type) || "ALL".equalsIgnoreCase(type)) {// 菜单权限列表
                        rtnMap.put("menus", list);
                    }
                    if ("B".equalsIgnoreCase(type) || "ALL".equalsIgnoreCase(type)) {// 按钮权限列表
                        rtnMap.put("perms", this.getButtonPermissions(list));
                    }
                }
            }
        }
        return rtnMap;
    }

    /**
     * 获取用户拥有的权限列表
     *
     * @param objType 0角色标识  1人员标识
     * @param objId   对象id
     * @param type    M：菜单权限；B：按钮权限：ALL：全部
     * @return
     */
    @Override
    public List<SysMenu> getPermsByObjId(String objId, Integer objType, String type) {
        // 超级管理员
        if ("administrator".equals(objId)) {
            return menuService.menuList();
        } else {// SysRole role = ObjUtils.convertToObject(SysRole.class, mp);
            // List<String> rtnList = new ArrayList<String>();
            // Map<String, SysRole> mapRole = sysRoleSer.getSysRoleMap(); // 系统可以使用的角色列表
            if (objType == 0) {
                //角色权限及id
                SysRole sysRole = sysRoleSer.getRoleById(objId);// mapRole.get(objId);
                if (sysRole == null || objType == 0) { // 角色不可用
                    return null;
                }
                if (sysRole.getUsed() == null || sysRole.getIsUse() == null || sysRole.getUsed() == 0
                        || sysRole.getIsUse() == 0) {
                    return null;
                }
            }
            if (!StringUtils.isEmpty(objId)) {
                Map<String, String> menusId = new HashMap<>();
                // 所有菜单
                List<SysMenu> m = iSysMenuLibService.getMenuListByType(type);
                List<String> mid = m.stream().map(i -> i.getId()).collect(Collectors.toList());
                if (objType == 0) {
                    // 查找角色对应的权限列表
                    List<String> rl = new ArrayList<>();
                    rl.add(objId);
                    // 获取角色权限
                    List<SysRolePerm> listRoles = sysRolePermService.getRolePermByIds(rl, null);
                    // 角色权限的菜单id
                    // 角色
                    menusId = listRoles.stream().filter(i -> mid.contains(i.getPermid()))
                            .collect(Collectors.toMap(SysRolePerm::getPermid, SysRolePerm::getPermid));
                } else {
                    // 根据人员id查询对应权限
                    Map<String, Object> perms = this.getPerms(objId, type);

                    // 判断是否为租户管理员或者超级管理员,直接返回所有菜单
                    if ((perms.containsKey("isTenantAdmin") && true == (boolean) perms.get("isTenantAdmin"))
                            || (perms.containsKey("isAdmin") && true == (boolean) perms.get("isAdmin"))) {
                        return menuService.menuList();
                    }

                    if ("M".equals(type)) {
                        List<String> permList = (List<String>) perms.get("menus");
                        menusId = permList.stream().filter(i -> mid.contains(i))
                                .collect(Collectors.toMap(Function.identity(), Function.identity()));
                    }
                }
                List<SysMenu> menus = menuService.menuList();
                // 按照权限重构属性
                rebuildMenuTree(menus.get(0), menusId, type);
                // 删除空的分类
                menus.get(0).setChildren(menus.get(0).getChildren().stream()
                        .filter(i -> StringUtils.isNotEmpty(i.getChildren())).collect(Collectors.toList()));
                return menus;
            }
            return new ArrayList<>();
        }
    }

    /**
     * 按照权限 重构菜单
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private void rebuildMenuTree(SysMenu menus, Map<String, String> menusId, String type) {
        if (ObjUtils.notEmpty(menus)) {
            List<SysMenu> children = menus.getChildren();
            // List<SysMenu> hasPermMenu = new ArrayList<>();
            if (StringUtils.isNotEmpty(children)) {
                Iterator<SysMenu> iter = children.iterator();
                while (iter.hasNext()) {
                    SysMenu menu = iter.next();
                    // 此菜单不属于属于权限范围
                    if (type.equals(menu.getMenuType())) {
                        if (!menusId.containsKey(menu.getId())) {
                            iter.remove();
                        }
                    } else {
                        if ("0".equals(menu.getVisible())) {
                            iter.remove();
                        }
                        if (StringUtils.isNotEmpty(menu.getChildren())) {
                            rebuildMenuTree(menu, menusId, type);
                        }
                    }
                }
            }
        }
    }

    /**
     * 判断是否为超级管理员
     *
     * @param userId
     * @return
     */
    @Override
    public boolean isAdmin(String userId) {
        boolean bln = false;
        if ("admin".equalsIgnoreCase(userId) || "administrator".equalsIgnoreCase(userId)) {
            bln = true;
        }
        return bln;
    }

    /**
     * 获得按钮权限字符串
     *
     * @param list 用户拥有的所有权限id，包括菜单权限、按钮权限
     * @return
     */
    private List<String> getButtonPermissions(List<String> list) {
        List<String> listPerms = new ArrayList<String>();
        if (StringUtils.isNotEmpty(list)) {
            // Map<String, SysMenu> mapMenu = menuService.getMenuMap();// 菜单map
            Map<String, Map<String, Object>> mapMenu = menuService.getMenuMapFromRedis(null);
            if (StringUtils.isNotEmpty(mapMenu)) {
                for (String permId : list) {
                    String s = this.getPermStr(mapMenu, permId);// 根据id获得权限字符串
                    if (StringUtils.isNotEmpty(s) && !listPerms.contains(s)) {
                        listPerms.add(s);
                    }
                }
            }
        }
        return listPerms;
    }

    private SysMenu getMenu(Map<String, Map<String, Object>> mapMenu, String key) {
        if (mapMenu != null) {
            try {
                if (mapMenu.containsKey(key)) {
                    return ObjUtils.convertToObject(SysMenu.class, mapMenu.get(key));
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return null;
    }

    /**
     * 获得按钮的权限字符串
     *
     * @param mapMenu
     * @param permId
     * @return 权限字符串，例如 system:employee:add
     */
    private String getPermStr(Map<String, Map<String, Object>> mapMenu, String permId) {
        String s = "";
        SysMenu menu = this.getMenu(mapMenu, permId);// mapMenu.get(permId);
        if (menu != null) {
            if (SysMenu.TYPE_BUTTON.equalsIgnoreCase(menu.getMenuType())) {// 按钮权限
                if (menu.getDelFlag() == 0) {// 未删除
                    SysMenu pmenu = this.getMenu(mapMenu, menu.getPid());// mapMenu.get(menu.getPid());// 菜单信息
                    if (pmenu != null && pmenu.getDelFlag() == 0 && StringUtils.isNotEmpty(menu.getPerms())
                            // && StringUtils.isNotEmpty(pmenu.getComponent())) {
                            && StringUtils.isNotEmpty(pmenu.getPath())) {
                        // 根据菜单的组件路径生成权限字符串
                        if (pmenu.getPath().startsWith("/")) {
                            s = pmenu.getPath().substring(1);
                        } else {
                            s = pmenu.getPath();
                        }
                        s = s.replaceAll("\\/", ":") + ":" + menu.getPerms();
                        // s = pmenu.getComponent().replaceAll("\\/", ":") + ":" + menu.getPerms();
                    }
                }
            } else if (SysMenu.TYPE_MENU.equalsIgnoreCase(menu.getMenuType())) {// 菜单权限
                if (menu.getDelFlag() == 0 && StringUtils.isNotEmpty(menu.getPath())) {
                    // 根据菜单的组件路径生成权限字符串
                    s = menu.getPath();
                    if (s.startsWith("/")) {
                        s = s.substring(1);
                    }
                    s = s.replaceAll("\\/", ":");
                }
            }
        }
        return s;
    }

    /**
     * 获取用户拥有的权限列表
     *
     * @param userId 用户id
     * @param permId 权限id
     * @return
     */
    private List<String> getPermissions(String mode,String userId, List<SysUserRole> listRoles) {
        List<String> rtnList = new ArrayList<String>();
//		Map<String, SysRole> mapRole = sysRoleSer.getSysRoleMap(); // 系统可以使用的角色列表
//		if (StringUtils.isEmpty(mapRole)) { // 系统未初始化任何角色
//			return rtnList;
//		}
        // 读取基础数据
        UserRoleDto userRoleDto = new UserRoleDto();
        userRoleDto.setUserid(userId);
//		List<SysUserRole> listRoles = sysUserRoleService.getUserRole(userRoleDto); // 查询人拥有哪些角色
        if (listRoles == null) {
            listRoles = sysUserRoleService.getUserRole(userRoleDto); // 查询人拥有哪些角色
        }
        if (StringUtils.isEmpty(listRoles)) {// 未设置角色的给一个默认角色
            listRoles = sysUserRoleService.getDefaultUserRole(userId, -1);
        }
        List<SysRolePerm> listRolePerms = new ArrayList<SysRolePerm>();
        if (!StringUtils.isEmpty(listRoles)) {// 查找角色对应的权限列表
            List<String> roleIds = new ArrayList<String>();
            for (SysUserRole role : listRoles) {
                String roleid = role.getRoleid();
                SysRole sysRole = sysRoleSer.getRoleById(roleid);
//				SysRole sysRole = mapRole.get(roleid);
                if (sysRole == null) { // 角色不可用
                    continue;
                }
                if (sysRole.getIsAdmin() != null && sysRole.getIsAdmin() == 1) {// 超级管理员
                    rtnList.add("*");
                    return rtnList;
                }
                roleIds.add(roleid);
            }
            listRolePerms = sysRolePermService.getRolePermByIds(roleIds);
        }
        // 开始检索个人权限
        SysUserPermDto userPermDto = new SysUserPermDto();
        userPermDto.setUserid(userId);
//		if (StringUtils.isNotEmpty(permId)) { // 检索某一个菜单的数据
//			userPermDto.setPermid(permId);
//		}
        List<SysUserPerm> listUserPerm = sysUserPermService.getUserPerm(userPermDto);// 查询人拥有哪些权限
        if (StringUtils.isNotEmpty(listRolePerms) || StringUtils.isNotEmpty(listUserPerm)) {
            // 角色权限与个人权限求并集(listRolePerms 与 listUserPerm)
            /*
             * Map<String, SysMenu> mapMenu = null; if (listType != null) { mapMenu = new
             * HashMap<String, SysMenu>(); if (StringUtils.isNotEmpty(permId)) { SysMenu
             * menu = menuService.getMenu(permId); if (menu != null) { mapMenu.put(permId,
             * menu); } } else { mapMenu = menuService.getMenuMap(); } }
             */
            //获取权限菜单数据
            Map<String, Map<String, Object>> menuData = this.getMenuData();
            //获取当前人的最大角色
            SysRole currentUserRoleLevel = roleLevelService.getCurrentUserRoleLevel(userId);
            if (StringUtils.isNotEmpty(listRolePerms)) {// 角色权限
                //按照角色层级进行过滤
                listRolePerms = listRolePerms.stream().filter(item ->
                        menuService.getMenuByRoleLevel(item.getPermid(), currentUserRoleLevel, menuData)
                ).collect(Collectors.toList());
                for (SysRolePerm sysRolePerm : listRolePerms) {
                    this.setPermMap(rtnList, sysRolePerm.getPermid());
                }
            }
            if (StringUtils.isNotEmpty(listUserPerm)) {// 个人权限
                //按照角色层级进行过滤
                listUserPerm = listUserPerm.stream().filter(item ->
                        menuService.getMenuByRoleLevel(item.getPermid(), currentUserRoleLevel, menuData)
                ).collect(Collectors.toList());
                for (SysUserPerm userPerm : listUserPerm) {
                    this.setPermMap(rtnList, userPerm.getPermid());
                }
            }
        }
        if("2".equals(mode)){ //协作模式，需要过滤某些敏感菜单 by x.zhong 2025.06.25
            List<String> mList = menuService.getAccHideList();
            if(StringUtils.isNotEmpty(mList) && StringUtils.isNotEmpty(rtnList)) {// 角色权限
                List<String> menuList = new ArrayList<>();
                for (String s : rtnList) {
                    if(!mList.contains(s)){
                        menuList.add(s);
                    }
                }
                return menuList;
            }else{
                return rtnList;
            }
        }else{
            return rtnList;
        }
    }

    /**
     * 获取菜单数据
     *
     * @return
     * <AUTHOR>
     * @date 2025/2/11
     * @params
     */
    public Map<String, Map<String, Object>> getMenuData() {
        return menuService.getMenuMapFromRedis(null);
    }
    /**
     * 权限的id转换
     *
     *
     * @param sysMenu
     * @param listType All:全部；B：按钮；M：菜单
     * @return
     */
    /*
     * private String permIdConvert(SysMenu sysMenu, List<String> listType) { if
     * (sysMenu == null) { return null; } if (StringUtils.isEmpty(listType)) {
     * return sysMenu.getId(); } String menuType = sysMenu.getMenuType(); if
     * (listType.contains(menuType)) { return sysMenu.getId(); } return null; }
     */

    /**
     * 权限map赋值
     *
     * @param map
     * @param menuid
     * @param actionid
     */
    private void setPermMap(List<String> rtnList, String permid) {
        if (StringUtils.isEmpty(permid)) {
            return;
        }
        String id = permid;
        if (StringUtils.isNotEmpty(id) && !rtnList.contains(id)) {
            rtnList.add(id);
        }
    }

    /**
     * 根据权限id获取拥有的角色列表
     *
     * @param permid 权限id
     * @return
     */
    @Override
    public List<SysRole> getRoleListByPermid(String permid) {
        List<String> roleList = sysRolePermService.getRoleIds(permid);
        List<SysRole> list = sysRoleSer.getRoleByIds(roleList);
        return list;
    }

    /**
     * 根据权限id获取拥有的人员列表
     *
     * @param permid
     * @return
     */
    @Override
    public List<EmployeeVo> getEmpListByPermid(EmpPermDto dto) {
        List<String> listUser1 = sysUserPermService.getUserIds(dto.getPermid()); // 个人权限人员列表
        List<String> roleList = sysRolePermService.getRoleIds(dto.getPermid());// 权限对应的角色列
        List<String> listUser2 = sysUserRoleService.getUserIds(roleList);
        List<String> listUser = new ArrayList<String>();
        if (StringUtils.isNotEmpty(listUser1)) {
            listUser.addAll(listUser1);
        }
        if (StringUtils.isNotEmpty(listUser2)) {
            listUser.addAll(listUser2);
        }
        List<EmployeeVo> list = new ArrayList<EmployeeVo>();
        if (StringUtils.isNotEmpty(listUser)) {
            EmpParamDto paramDto = new EmpParamDto();
            String paramEmpid = StringUtils.join(listUser, ",");
            paramDto.setEmpname(dto.getEmpname());
            paramDto.setEmpid(paramEmpid);
            paramDto.setCurrent(dto.getPageNum());
            paramDto.setSize(dto.getPageSize());
            list = empServ.getAllEmployee(paramDto);
            if (StringUtils.isNotEmpty(list)) {
                dto.setRecordCount(list.get(0).getRecordCount().intValue());
            }
        }
        return list;
    }

    /**
     * 根据角色id获取拥有的人员列表
     *
     * @param permid
     * @return
     */
    @Override
    public List<EmployeeVo> getEmpListByRoleid(EmpRoleDto dto) {
        List<String> roleList = new ArrayList<String>();
        roleList.add(dto.getRoleid());
        List<String> listUser = sysUserRoleService.getUserIds(roleList);
        List<EmployeeVo> list = new ArrayList<EmployeeVo>();
        if (StringUtils.isNotEmpty(listUser)) {
            EmpParamDto paramDto = new EmpParamDto();

            String paramEmpid = StringUtils.join(listUser, ",");
            paramDto.setEmpname(dto.getEmpname());
            paramDto.setEmpid(paramEmpid);
            paramDto.setCurrent(dto.getPageNum());
            paramDto.setSize(dto.getPageSize());
            list = empServ.getAllEmployee(paramDto);
            if (StringUtils.isNotEmpty(list)) {
                dto.setRecordCount(list.get(0).getRecordCount().intValue());
            }
        }
        // List<EmployeeVo> list = empServ.getEmployee(listUser);
        return list;
    }

    @Override
    public boolean isTenantAdmin(List<SysUserRole> listRoles) {
        if (MultiTenantUtils.enalbe()) {
            if (StringUtils.isNotEmpty(listRoles)) {
                String rid = "admin:" + MultiTenantUtils.getTenantId();
                for (SysUserRole e : listRoles) {
                    if (e.getRoleid().equals(rid)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 判断是否为租户管理员
     *
     * @param userId
     * @return
     */
    @Override
    public boolean isTenantAdmin() {
        return isTenantAdmin(SysUserHolder.getCurrentUser());
    }

    @Override
    public boolean isTenantAdmin(SysUser user) {
        boolean bln = false;
        if (MultiTenantUtils.enalbe()) {
            if (user != null) {
                List<String> roleIdList = SysUserHolder.getCurrentUser().getRoles();
                if (StringUtils.isNotEmpty(roleIdList)) {
                    String rid = "admin:" + MultiTenantUtils.getTenantId();
                    if (roleIdList.contains(rid)) {
                        return true;
                    }
                }
            }
        }
        return bln;
    }

    /**
     * 根据权限id获取拥有的人员列表
     *
     * @param permid
     * @return
     */
    @Override
    public List<String> getEmpIdByPermid(List<String> permids) {
        // TODO Auto-generated method stub
        List<String> result = new ArrayList<String>();
        if (StringUtils.isNotEmpty(permids)) {
            boolean hasUser = false;
            HashSet<String> listUser = new HashSet<String>();
            List<String> listUser1 = sysUserPermService.getUserIds(permids); // 个人权限人员列表
            if (StringUtils.isNotEmpty(listUser1)) {
                listUser.addAll(listUser1);
                hasUser = true;
            }
            List<String> roleList = sysRolePermService.getRoleIds(permids);// 权限对应的角色列
            List<String> listUser2 = sysUserRoleService.getUserIds(roleList);
            if (StringUtils.isNotEmpty(listUser2)) {
                listUser.addAll(listUser2);
                hasUser = true;
            }
            if (hasUser) {
                result.addAll(listUser);//返回有权限的人员
            }
        }
        return result;
    }

}
