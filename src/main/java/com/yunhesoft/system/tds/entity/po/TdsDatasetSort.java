package com.yunhesoft.system.tds.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据集输出排序设置
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_DATASET_SORT")
public class TdsDatasetSort extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 数据源别名 */
	@Column(name = "TDSALIAS", length = 100)
	private String tdsAlias;

	/** 排序字段 */
	@Column(name = "ALIAS", length = 50)
	private String alias;

	/** 排序方式 */
	@Column(name = "SORTTYPE")
	private Integer sortType;// 0：降序，1：升序

	/** 排序顺序 */
	@Column(name = "TMSORT")
	private Integer tmSort;

}