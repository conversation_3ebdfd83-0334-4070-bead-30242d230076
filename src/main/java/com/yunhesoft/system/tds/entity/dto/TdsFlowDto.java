package com.yunhesoft.system.tds.entity.dto;

import java.util.List;

import com.yunhesoft.system.tds.entity.po.TdsFlow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "数据源工作流设置", description = "数据源工作流设置")
public class TdsFlowDto {
	/** 数据源别名 */
	@ApiModelProperty(value = "数据源别名")
	private String tdsAlias;

	// 需要保存的数据
	@ApiModelProperty(value = "需要保存的数据(json数组)")
	private List<TdsFlow> data;

}
