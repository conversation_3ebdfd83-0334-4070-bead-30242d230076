package com.yunhesoft.system.tds.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据源编的子表数据
 * 
 * @category 数据源绑定子表输入输出参数
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Entity
@Table(name = "TDS_BIND")
public class TdsBind  extends BaseEntity {
	
	private static final long serialVersionUID = 1L;
	// 子表数据源别名
	@Column(name = "C_TDSALIAS", length = 255)
	private String cTdsAlias;
	// 子表数据源输入参数id
	@Column(name = "C_TDSINPARAALIAS", length = 255)
	private String cTdsInParaAlias;
	// 子表数据源输入参数名称
	@Column(name = "C_TDSINPARANAME", length = 255)
	private String cTdsInParaName;
	// 父表数据源别名
	@Column(name = "P_TDSALIAS", length = 255)
	private String pTdsAlias;
	// 父表数据源输出参数id
	@Column(name = "P_TDSOUTPARAALIAS", length = 255)
	private String pTdsOutParaAlias;
	// 父表数据源输出参数名称
	@Column(name = "P_TDSOUTPARANAME", length = 255)
	private String pTdsOutParaName;
	// 序号
	@Column(name = "PARAID")
	private int paraid;
	
	
}
