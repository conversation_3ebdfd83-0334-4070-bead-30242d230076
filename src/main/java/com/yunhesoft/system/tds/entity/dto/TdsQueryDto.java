package com.yunhesoft.system.tds.entity.dto;

import java.util.List;

import com.yunhesoft.system.tds.entity.vo.TdsSearchMoreDataVo;
import com.yunhesoft.system.tds.entity.vo.TdsSortMoreDataVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据源查询条件
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(description = "数据源查询条件")
public class TdsQueryDto {

	/** 数据源别名 */
	@ApiModelProperty(required = true, value = "数据源别名")
	private String tdsAlias;

	/** 检索条件字符串 */
	@ApiModelProperty(value = "检索条件字符串 ,name=zx|yf=2001-01")
	private String inParaAlias; // name=zx|yf=2001-01

	/** 检索条件字符串 */
	@ApiModelProperty(value = "检索条件字符串显示值 ,name=zx|yf=2001-01")
	private String inParaRawValue; // name=zx|yf=2001-01

	/** 检索条件对象 */
	@ApiModelProperty(value = "检索条件对象")
	private String queryData;

	/** 第几页 */
	@ApiModelProperty(value = "第几页 ")
	private Integer page;

	/** 每页数量 */
	@ApiModelProperty(value = "每页数量 ")
	private Integer pageSize;

	@ApiModelProperty(value = "检索时是否每次执行初始化语句")
	Boolean isInitData = false;

	@ApiModelProperty(value = "是否显示渲染函数值")
	Boolean showRenderValue = false;

	/** 是否显示错误信息 */
	@ApiModelProperty(value = "是否显示错误信息 ")
	Boolean errInfo = false;

	@ApiModelProperty(value = "高级检索及排序数据")
	private String tdsSearchMoreTxt;

	@ApiModelProperty(value = "高级检索及排序数据参数")
	private List<TdsSearchMoreDataVo> tdsSearchMoreData;

	@ApiModelProperty(value = "高级检索排序数据")
	private List<TdsSortMoreDataVo> TdsSortMortData;



}
