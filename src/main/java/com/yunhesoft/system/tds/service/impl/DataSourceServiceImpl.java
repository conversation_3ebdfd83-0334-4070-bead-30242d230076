package com.yunhesoft.system.tds.service.impl;

import java.util.*;

import javax.transaction.Transactional;

import com.yunhesoft.system.kernel.utils.excel.TDSExportParams;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.service.ISysDictTypeService;
import com.yunhesoft.system.tools.printer.service.PrinterService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Base64;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.PinYinUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SqlDDLService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.SqlDDLColumnInfo;
import com.yunhesoft.system.kernel.service.model.TableColumnInfo;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.tds.entity.dto.TdsDataInfoDto;
import com.yunhesoft.system.tds.entity.dto.TdsDataQueryDto;
import com.yunhesoft.system.tds.entity.dto.TdsEditBindParamDto;
import com.yunhesoft.system.tds.entity.dto.TdsEditCustomBtnDto;
import com.yunhesoft.system.tds.entity.dto.TdsExportDto;
import com.yunhesoft.system.tds.entity.dto.TdsFlowDto;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.entity.po.TdataSource;
import com.yunhesoft.system.tds.entity.po.TdsBind;
import com.yunhesoft.system.tds.entity.po.TdsDatasetSort;
import com.yunhesoft.system.tds.entity.po.TdsEditCustomBtn;
import com.yunhesoft.system.tds.entity.po.TdsEditTdsBindParam;
import com.yunhesoft.system.tds.entity.po.TdsFlow;
import com.yunhesoft.system.tds.entity.po.TdsScriptEntity;
import com.yunhesoft.system.tds.entity.po.TdsSortData;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.entity.po.TdsoutPara;
import com.yunhesoft.system.tds.entity.po.TdstableInfo;
import com.yunhesoft.system.tds.entity.vo.TdataSourceVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountOutparamVo;
import com.yunhesoft.system.tds.entity.vo.TdsComboBox;
import com.yunhesoft.system.tds.entity.vo.TdsSearchMoreDataVo;
import com.yunhesoft.system.tds.entity.vo.TdsSortMoreDataVo;
import com.yunhesoft.system.tds.entity.vo.TdsTableColumn;
import com.yunhesoft.system.tds.model.IDataSource;
import com.yunhesoft.system.tds.model.TDSCached;
import com.yunhesoft.system.tds.model.TDSInParaView;
import com.yunhesoft.system.tds.model.TDataSourceManager;
import com.yunhesoft.system.tds.model.TOutPara;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;
import com.yunhesoft.system.tds.service.IDataSourceInParaRelService;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tds.utils.TdsTools;

import lombok.extern.log4j.Log4j2;

/**
 * 数据源数据库操作实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
public class DataSourceServiceImpl implements IDataSourceService {

    @Autowired
    private EntityService dao;

    // 初始化输出参数(初始化数据时使用)
    private List<TdsoutPara> listInitOut = new ArrayList<TdsoutPara>();

    @Autowired
    private IDataSourceInParaRelService inParamRelServ; // 输入参数联动关系

    @Autowired
    private IDataSourceAccountService accountServ; // 台账服务

    @Autowired
    private SqlDDLService ddlService;

    @Autowired
    private ISysDictTypeService sysDictTypeServ;

    @Autowired
    private PrinterService printerServ;// 打印配置

    /**
     * 获取数据源数据
     *
     * @param tdsAlias    数据源别名
     * @param inParaAlias 检索条件字符串 name=zhongx|age=18
     * @param page        第几页
     * @param pageSize    每页数量
     * @param errInfo     是否显示错误信息
     * @return
     */
    @Override
    public JSONArray getTDSData(TdsQueryDto param) {
        String queryStr = param.getInParaAlias();
        if (StringUtils.isEmpty(queryStr)) {
            queryStr = param.getQueryData();
        }
        String json = this.getTDSData(param.getTdsAlias(), queryStr, param.getPage(), param.getPageSize(),
                param.getIsInitData() != null ? param.getIsInitData() : false,
                param.getShowRenderValue() != null ? param.getShowRenderValue() : false,
                param.getErrInfo() != null ? param.getErrInfo() : false, param.getTdsSearchMoreTxt(),
                param.getTdsSearchMoreData(), param.getTdsSortMortData());
        JSONArray jary = new JSONArray();
        try {
            jary = JSONArray.parseArray(json);
        } catch (Exception e) {
            log.error("", e);
            log.info("数据源结果json转换出错：" + param.getTdsAlias());
            log.info(json);
        }
        return jary;
    }

    /**
     * 获取多个数据源数据
     */
    @Override
    public JSONArray getMutiTDSData(TdsQueryDto param) {
        JSONArray jary = new JSONArray();
        if (StringUtils.isNotEmpty(param.getTdsAlias())) {
            if (param.getTdsAlias().indexOf(",") > 0) {
                String[] alias = param.getTdsAlias().split(",");
                for (String dsAlias : alias) {
                    TdsQueryDto dto = new TdsQueryDto();
                    BeanUtils.copyProperties(param, dto);
                    dto.setTdsAlias(dsAlias);
                    JSONArray ary = getTDSData(dto);
                    if (ary != null) {
                        jary.addAll(ary);
                    }
                }
            } else {
                return getTDSData(param);
            }
        }
        return jary;
    }

    /**
     * 数据源检索条件数据
     */
    @Override
    public HashMap<String, Object> getTDSQuery(TdsQueryDto param) {
        TDSInParaView tdsView = new TDSInParaView();
        HashMap<String, Object> map = tdsView.getTdsQueryList(param.getTdsAlias(), param.getInParaAlias(),
                param.getInParaRawValue());
        return map;
    }

    /**
     * 获取数据源输出参数for外部查询
     *
     * @param param
     * @return
     */
    @Override
    public HashMap<String, List<TdsTableColumn>> getTdsOutParam(String tdsAlias) {
        HashMap<String, List<TdsTableColumn>> map = new HashMap<String, List<TdsTableColumn>>();
        if (StringUtils.isNotEmpty(tdsAlias)) {
            String[] alias = tdsAlias.split(",");
            TDataSourceManager dsm = new TDataSourceManager();
            for (String dsAlias : alias) {
                List<TOutPara> list = dsm.TdsoutParaToOutParaList(null, dsm.getOutParaList(dsAlias));
                if (list != null) {
                    List<TdsTableColumn> listCol = new ArrayList<TdsTableColumn>();
                    Map<String, TOutPara> mapCol = new HashMap<String, TOutPara>();
                    List<Integer> formulaList = new ArrayList<Integer>();
                    int n = -1;
                    for (TOutPara op : list) {
                        n++;
                        mapCol.put(op.getAlias(), op);
                        listCol.add(dsm.tdsOut2Column(op));
                        if (StringUtils.isNotEmpty(op.getReportFormula())) {
                            formulaList.add(n);
                        }
                    }
                    if (formulaList.size() > 0) {
                        for (Integer index : formulaList) {
                            listCol.set(index, dsm.tdsOut2Column(list.get(index), null, mapCol));
                        }
                    }
                    map.put(dsAlias, listCol);
                }
            }
        }
        return map;
    }

    /**
     * 获取数据源数据
     *
     * @param tdsAlias    数据源别名
     * @param inParaAlias 检索条件
     * @param page        第几页
     * @param pageSize    每页数量
     * @param errInfo     是否显示错误信息
     * @return
     */
    private String getTDSData(String tdsAlias, String inParaAlias, Integer page, Integer pageSize, boolean isInitData,
                              boolean showRenderValue, boolean errInfo, String tdsSearchMoreTxt,
                              List<TdsSearchMoreDataVo> tdsSearchMoreData, List<TdsSortMoreDataVo> TdsSortMortData) {
        JSONArray ary = new JSONArray();
        JSONObject obj = new JSONObject();
        obj.put("name", tdsAlias); // 数据源别名
        obj.put("mtype", "tds");
        obj.put("mcode", tdsAlias);
        // 分页信息
        if (pageSize != null && page != null && page > 0) {
            JSONObject pageObj = new JSONObject();
            pageObj.put("page", page);
            pageObj.put("pageSize", pageSize);
            obj.put("pageInfo", pageObj);
        }
        // 检索条件
        if (StringUtils.isNotEmpty(inParaAlias)) {// name=chris|age=19
            JSONArray qAry = null;
            boolean isJson = false;
            if (inParaAlias.indexOf("[") > -1 || inParaAlias.indexOf("{") > -1) {//
                isJson = true; // [{name:"yf",value:"2021-07"}]
            }
            try {
                qAry = JSONArray.parseArray(inParaAlias); // 检索条件转换为json对象
            } catch (Exception e) {
                isJson = false;
            }
            if (!isJson) {
                qAry = new JSONArray();
                String[] objs = inParaAlias.split("\\|");
                for (String string : objs) {
                    String[] vals = string.split("=");
                    JSONObject o = new JSONObject();
                    String k = vals[0];
                    String v = vals.length > 1 ? vals[1] : "";
                    o.put("name", k);
                    o.put("value", v);
                    qAry.add(o);
                }
            }
            obj.put("data", qAry);
        }
        ary.add(obj);
        TDataSourceManager dsm = new TDataSourceManager();
        // [{"name":"dxlsjy","para":"检索日期:2021-06-02 标签选择: 人员选择:
        // 机构选择:","mtype":"tds","mcode":"dxlsjy","pageInfo":{"page":1,"pageSize":50},"data":[{"name":"tbrq","xtype":"datefield","value":"2021-06-02","rawValue":"2021-06-02"},{"name":"bqxz","xtype":"pagetagfield","value":"","rawValue":""},{"name":"ryxz","xtype":"userfield","value":"","rawValue":""},{"name":"jgxz","xtype":"quartercombo","value":"","rawValue":""}]}]
        return dsm.getData(null, ary, "json", isInitData, showRenderValue, errInfo, tdsSearchMoreTxt, tdsSearchMoreData,
                TdsSortMortData,inParaAlias);
    }

    /**
     * 获取数据源
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    @Override
    public TdataSource getTDataSource(String tdsAlias) {
        Where where = Where.create();
        where.eq(TdataSource::getTdsalias, tdsAlias);
        return dao.queryObject(TdataSource.class, where);
    }

    /**
     * 通过ID获取数据源
     *
     * @param Id
     * @return
     */
    @Override
    public TdataSource getTDataSourceById(String Id) {
        return dao.queryObjectById(TdataSource.class, Id);
    }

    /**
     * 获取输入参数
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public List<TdsinPara> getTDSInPara(String tdsAlias) {
        Where where = Where.create();
        where.eq(TdsinPara::getTdsalias, tdsAlias);
        Order order = Order.create();
        order.orderByAsc(TdsinPara::getParaId);
        return dao.queryList(TdsinPara.class, where, order);
    }

    /**
     * 批量获取输入参数
     *
     * @return
     * <AUTHOR>
     * @params
     */

    @Override
    public List<TdsinPara> getTDSInParaMulti(String tdsAlias) {
        Where where = Where.create();
        if (!tdsAlias.contains(",")) {
            where.eq(TdsinPara::getTdsalias, tdsAlias);
        } else {
            List<String> list = new ArrayList<String>();
            String[] ids = tdsAlias.split(",");
            for (String s : ids) {
                list.add(s);
            }
            where.in(TdsinPara::getTdsalias, list.toArray());
        }
        Order order = Order.create();
        order.orderByAsc(TdsinPara::getParaId);
        return dao.queryList(TdsinPara.class, where, order);
    }

    /**
     * 获取数据源数量
     *
     * @return
     */
    private Long getTDataSourceCount() {
        Where where = Where.create();
        where.eq(TdataSource::getUsed, 1);
        return dao.queryCount(TdataSource.class, where);
    }

    /**
     * 获取输出参数
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public List<TdsoutPara> getTDSOutPara(String tdsAlias) {
        Where where = Where.create();
        where.eq(TdsoutPara::getTdsalias, tdsAlias);
        Order order = Order.create();
        order.orderByAsc(TdsinPara::getParaId);
        return dao.queryList(TdsoutPara.class, where, order);
    }

    /**
     * 获取报表信息
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public TdstableInfo getTDSTableInfo(String tdsAlias) {
        Where where = Where.create();
        where.eq(TdstableInfo::getTdsAlias, tdsAlias);
        return dao.queryObject(TdstableInfo.class, where);
    }

    /**
     * 添加数据源报表信息
     *
     * @param tdsTable
     * @return
     */
    public boolean insertTDSTableInfo(TdstableInfo tdsTable) {
        boolean bln = false;
        try {
            int i = dao.insert(tdsTable);
            bln = i > 0 ? true : false;
        } catch (Exception e) {
            log.error(e);
        }
        return bln;
    }

//	private boolean deleteTDSTableInfo(String tdsAlias) {
//		List<String> tdsAliasList = new ArrayList<String>();
//		tdsAliasList.add(tdsAlias);
//		return this.deleteTDSTableInfo(tdsAliasList);
//	}

    /**
     * 批量删除数据源报表配置
     *
     * @param tdsAliasList
     * @return
     */
    private boolean deleteTDSTableInfo(List<String> tdsAliasList) {
        boolean bln = false;
        if (StringUtils.isNotEmpty(tdsAliasList)) {
            Where where = Where.create();
            if (tdsAliasList.size() == 1) {
                where.eq(TdstableInfo::getTdsAlias, tdsAliasList.get(0));
            } else {
                where.in(TdstableInfo::getTdsAlias, tdsAliasList.toArray());
            }
            int i = dao.rawDeleteByWhere(TdstableInfo.class, where);
            bln = i > 0 ? true : false;
        }
        return bln;
    }

    @Override
    public boolean insertTDataSource(TdataSource bean) {
        int i = dao.insert(bean);
        return i > 0 ? true : false;
    }

    @Override
    public boolean insertTDSInPara(List<TdsinPara> list) {
        int i = dao.insertBatch(list, 500);
        return i > 0 ? true : false;
    }

    @Override
    public boolean insertTDSOutPara(List<TdsoutPara> list) {
        int i = dao.insertBatch(list, 500);
        return i > 0 ? true : false;
    }

    @Override
    public boolean updateTDataSource(TdataSource bean) {
        int i = dao.updateById(bean);
        return i > 0 ? true : false;
    }

    @Override
    public boolean updateTDSInPara(List<TdsinPara> list) {
        int i = dao.updateByIdBatch(list);
        return i > 0 ? true : false;
    }

    @Override
    public boolean updateTDSOutPara(List<TdsoutPara> list) {
        int i = dao.updateByIdBatch(list);
        return i > 0 ? true : false;
    }

    /**
     * 删除一个数据源
     */
    @Override
    public boolean deleteTDataSource(String tdsAlias) {
        TdataSource bean = this.getTDataSource(tdsAlias);
        if (bean == null) {
            return false;
        }
        if (bean.getIsSys() != null && bean.getIsSys() == 1) {
            log.info("系统数据源不能删除：" + bean.getTdsalias());
            return false;
        }
        List<TdataSource> tdataList = new ArrayList<TdataSource>();
        tdataList.add(bean);
        boolean bln = this.deleteTdsDatasources(tdataList);// 调用批量删除函数
//		int i = dao.deleteByColumn(TdataSource.class, TdataSource::getTdsalias, tdsAlias);
//		boolean bln = i > 0 ? true : false;
//		if (bln) {
//			this.deleteTDSInPara(tdsAlias, null);// 删除输入
//			this.deleteTDSOutPara(tdsAlias, null);// 删除输出
//			this.deleteTDSTableInfo(tdsAlias);// 删除报表信息
//			inParamRelServ.deleteInparaRelation(tdsAlias);// 删除输入参数联动关联关系
//			TDSCached cached = TDSCached.getInstance();// 删除缓存
//			cached.removeAllDs(tdsAlias);
//		}

        return bln;
    }

    /**
     * 批量删除输入参数
     *
     * @param tdsAliasList
     * @return
     */
    private boolean deleteTDSInPara(List<String> tdsAliasList) {
        boolean bln = false;
        if (StringUtils.isNotEmpty(tdsAliasList)) {
            Where where = Where.create();
            if (tdsAliasList.size() == 1) {
                where.eq(TdsinPara::getTdsalias, tdsAliasList.get(0));
            } else {
                where.in(TdsinPara::getTdsalias, tdsAliasList.toArray());
            }
            int i = dao.rawDeleteByWhere(TdsinPara.class, where);
            bln = i > 0 ? true : false;
        }
        return bln;
    }

    @Override
    public boolean deleteTDSInPara(String tdsAlias, String paraAlias) {
        Where where = Where.create();
        where.eq(TdsinPara::getTdsalias, tdsAlias);
        if (StringUtils.isNotEmpty(paraAlias)) {
            where.eq(TdsinPara::getParaAlias, paraAlias);
        }

        int i = dao.rawDeleteByWhere(TdsinPara.class, where);
        return i > 0 ? true : false;
    }

    /**
     * 批量删除输出参数
     *
     * @param tdsAliasList
     * @return
     */
    private boolean deleteTDSOutPara(List<String> tdsAliasList) {
        boolean bln = false;
        if (StringUtils.isNotEmpty(tdsAliasList)) {
            Where where = Where.create();
            if (tdsAliasList.size() == 1) {
                where.eq(TdsoutPara::getTdsalias, tdsAliasList.get(0));
            } else {
                where.in(TdsoutPara::getTdsalias, tdsAliasList.toArray());
            }
            int i = dao.rawDeleteByWhere(TdsoutPara.class, where);
            bln = i > 0 ? true : false;
        }
        return bln;
    }

    @Override
    public boolean deleteTDSOutPara(String tdsAlias, String paraAlias) {
        Where where = Where.create();
        where.eq(TdsoutPara::getTdsalias, tdsAlias);
        if (StringUtils.isNotEmpty(paraAlias)) {
            where.eq(TdsoutPara::getParaAlias, paraAlias);
        }
        int i = dao.rawDeleteByWhere(TdsoutPara.class, where);
        return i > 0 ? true : false;
    }

    /**
     * 获取数据源数据
     */
    @Override
    public IDataSource getTDSData(String tdsAlias, String inParaAlias) {
        TDataSourceManager dsm = new TDataSourceManager();
        IDataSource ids = dsm.getDataSource(tdsAlias, inParaAlias);
        if (ids != null) {
            if (!ids.getAutoLoad()) {
                ids.load();
            }
        }
        return ids;
    }

    /**
     * 初始化数据源数据
     */
    @Override
    public void initTDS() {
        TdataSource t = newTds();
        if (this.getTDataSourceById(t.getId()) == null) {
            if (insertTDataSource(t)) {
                listInitOut.clear();
                listInitOut.add(newTdsOut("员工姓名", "userName", "user.getRealName()"));
                listInitOut.add(newTdsOut("组员ID", "userId", "user.getId()"));
                listInitOut.add(newTdsOut("机构ID", "orgId", "user.getOrgId()"));
                listInitOut.add(newTdsOut("机构名称", "orgName", "user.getOrgName()"));
                listInitOut.add(newTdsOut("机构编码", "orgNumber", "user.getOrgNumber()"));
                listInitOut.add(newTdsOut("岗位ID", "postId", "user.getPostId()"));
                listInitOut.add(newTdsOut("角色", "roleName", "user.getRoleName()"));
                listInitOut.add(newTdsOut("工号", "staffNo", "user.getStaffNo()"));
                listInitOut.add(newTdsOut("手机号", "phone", "user.getPhone()"));
                listInitOut.add(newTdsOut("身份证号", "cardno", "user.getCardno()"));
                listInitOut.add(newTdsOut("登录名", "loginName", "user.getUserName()"));
                listInitOut.add(newTdsOut("所属车间ID", "workshopCode", "user.getWorkshopCode()"));
                listInitOut.add(newTdsOut("所属车间名称", "workshopName", "user.getWorkshopName()"));
                listInitOut.add(newTdsOut("所属车间编码", "workshopNumber", "user.getWorkshopOrgNumber()"));
                this.insertTDSOutPara(listInitOut);
            }
        }
        this.initSysInfoOutParam();
    }

    /**
     * 根据机构类型初始化系统信息输出参数
     */
    private void initSysInfoOutParam() {
        try {
            List<SysDictData> list = sysDictTypeServ.selectDictDataByType("sys_org_type");//从数据字段中获取机构类型
            String dsAlias = "SysInfo";
            if (StringUtils.isNotEmpty(list)) {
                List<TdsoutPara> listOut = this.getTDSOutPara(dsAlias);
                List<String> outAliasList = new ArrayList<>();
                for (TdsoutPara e : listOut) {
                    outAliasList.add(e.getParaAlias());
                }
                List<TdsoutPara> addList = new ArrayList<>();
                int sort = listOut.size() + 1;
                for (SysDictData e : list) {
                    String code = e.getDictValue();
                    String name = e.getDictLabel();
                    if (StringUtils.isEmpty(name) || StringUtils.isEmpty(code)) {
                        continue;
                    } else {
                        code = code.trim();
                        name = name.trim();
                    }
                    String outalias = code + "Code";
                    if (!outAliasList.contains(outalias)) {
                        addList.add(newTdsOut("所属" + name + "ID", outalias, "user.getParentOrgCode(\"" + code + "\")", sort++));
                        addList.add(newTdsOut("所属" + name + "名称", code + "Name", "user.getParentOrgName(\"" + code + "\")", sort++));
                    }
                }
                if (addList.size() > 0) {
                    this.insertTDSOutPara(addList);
                    TDSCached cached = TDSCached.getInstance();
                    cached.removeOutPara(dsAlias);//清除redis
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }

    }

    private TdsoutPara newTdsOut(String paraName, String paraAlias, String callFun) {
        return this.newTdsOut(paraName, paraAlias, callFun, -1);
    }

    private TdsoutPara newTdsOut(String paraName, String paraAlias, String callFun, Integer index) {
        TdsoutPara e = new TdsoutPara();
        e.setId("SysInfo_" + TMUID.getUID());
        e.setTdsalias("SysInfo");
        if (index == null || index == -1) {
            e.setParaId(listInitOut.size() + 1);
        } else {
            e.setParaId(index);
        }
        e.setParaName(paraName);
        e.setParaAlias(paraAlias);
        e.setDataType("tdsString");
        e.setIskey(0);
        e.setCallFun(callFun);
        e.setVisible(1);
        return e;
    }

    private TdataSource newTds() {
        TdataSource e = new TdataSource();
        e.setId("SysInfo");
        e.setTdsalias("SysInfo");
        e.setModuleCode("system");
        e.setTdsname("系统信息");
        e.setTdstype("SYS");
        e.setTdsclassName("TDSSystemInfo");
        e.setMemo("系统信息");
        e.setUsed(1);
        e.setAutoLoad(0);
        e.setTmSort(0);
        e.setIsSys(1);
        return e;
    }

    /**
     * 获得数据源导出Excel数据
     *
     * @param param pageSize = -1 导出模板
     * @return
     */
    @Override
    public JSONArray getTDSExportData(TdsExportDto param) {
        JSONArray tdsAry = null;
        param.setShowRenderValue(true); // 显示渲染函数值
        if (StringUtils.isNotEmpty(param.getTempType())) {// 导出模板
            String IDWidth = "200";// ID列的宽度
            if ("1".equals(param.getTempType())) {// 空模板,没有数据
                param.setPageSize(-1);
                param.setPage(1);
                IDWidth = "0";
            }
            tdsAry = this.getTDSData(param);
            if (tdsAry != null && tdsAry.size() > 0) {
                for (int i = 0; i < tdsAry.size(); i++) {
                    JSONObject tds = tdsAry.getJSONObject(i);
                    if (tds.getJSONObject("mobileCfg") != null) {
                        JSONObject repObj = tds.getJSONObject("mobileCfg").getJSONObject("datas");
                        if (repObj != null && repObj.get("isShowSums") != null) {// 合计行是否显示
                            repObj.put("isShowSums", 0);// 隐藏合计行
                        }
                    }
                    JSONObject colprop = tds.getJSONObject("props").getJSONObject("colprop");
                    JSONArray cols = tds.getJSONObject("props").getJSONArray("cols");
                    // boolean hasId = false;
                    String idCol = "";
                    for (int j = 0; j < cols.size(); j++) {
                        String col = cols.getString(j); // 字段
                        JSONObject colObj = colprop.getJSONObject(col);
                        colObj.put("isSpanCol", "false");
                        if ("ID".equals(col)) {// 主键列
                            colObj.put("hidden", false);
                            colObj.put("comType", "");
                            // colObj.put("width", "200");
                            colObj.put("width", IDWidth);
                            colObj.put("isKey", "true");
                            colObj.put("header", "ID");
                            colObj.put("alias", "ID");
                            idCol = col;
                        }
                    }
                    if (idCol.length() == 0) {// 无主键列，添加主键列
                        cols.add(0, "ID");
                        JSONObject colObj = new JSONObject();
                        colObj.put("comType", "");
                        colObj.put("hidden", false);
                        colObj.put("dataType", "tdsString");
                        colObj.put("isKey", "true");
                        colObj.put("defaultKeyScript", "");
                        colObj.put("align", "left");
                        colObj.put("lx", "0");
                        colObj.put("rendererFun", "");
                        colObj.put("defaultValueScript", "");
                        colObj.put("isGroupCol", "false");
                        colObj.put("isSumCol", "false");
                        colObj.put("overtip", "true");
                        colObj.put("width", IDWidth);
                        colObj.put("alias", "ID");
                        colObj.put("header", "ID");
                        colObj.put("alias", "ID");
                        colObj.put("reportFormula", "");
                        colprop.put("ID", colObj);
                    } else {
                        cols.remove(idCol);// 把ID列放在第1列
                        cols.add(0, idCol);
                    }
                }
            }
        } else {// 正常数据导出
            tdsAry = this.getTDSData(param);

            // 更新报表标题
            updateReportTitle(tdsAry, param);
        }
        return tdsAry;
    }

    /**
     * 更新报表标题
     *
     * @param tdsAry 数据源相关信息
     * @param param  前端传递参数
     */
    private void updateReportTitle(JSONArray tdsAry, TdsExportDto param) {
        if (tdsAry != null && !tdsAry.isEmpty()) {
            Map<String, Object> reportConfig = param.getReportConfig();
            if (reportConfig != null) {
                String title = (String) reportConfig.get("title");
                if (StringUtils.isNotEmpty(title)) {
                    for (int i = 0; i < tdsAry.size(); i++) {
                        JSONObject o = tdsAry.getJSONObject(i);
                        o.put("desc", title);
                    }
                }
            }
        }
    }

    /**
     * 解析脚本
     *
     * @param script 脚本
     * @return 解析后的值
     * @category <AUTHOR>
     */
    @Override
    public Object getScriptValue(String script, Map<String, String> mapInParasValues,
                                 HashMap<String, IDataSource> publicIdsMap) {
        TDataSourceManager dsm = new TDataSourceManager();
        return dsm.getScriptValue(script, mapInParasValues, publicIdsMap);
    }

    /**
     * 获取数据源明细
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public TdsDataInfoDto getTDatasource(String tdsAlias) {
        TdsDataInfoDto e = new TdsDataInfoDto();
        if (tdsAlias == null && "".equals(tdsAlias)) {
            return e;
        }

        try {
            JSONObject jsonObj = JSONObject.parseObject(tdsAlias);
            tdsAlias = jsonObj.getString("tdsAlias");
        } catch (Exception ex) {

        }
        e.setTdataSource(0);// 数据源基础信息状态
        e.setTdsinPara(0);// 数据源输入参数状态
        e.setTdsoutPara(0);// 数据源输出参数状态
        e.setTdstableInfo(0);// "数据源报表状态
        // 数据源基础信息
        TdataSource tdata = this.getTDataSource(tdsAlias);
        // 数据源输入参数
        List<TdsinPara> tdsin = this.getTDSInPara(tdsAlias);
        // "数据源输出参数
        List<TdsoutPara> tdsout = this.getTDSOutPara(tdsAlias);
        // 数据源报表信息
        TdstableInfo tdsTable = this.getTDSTableInfo(tdsAlias);
        e.setTdata(tdata);
        e.setTdsin(tdsin);
        e.setTdsout(tdsout);
        e.setTdsTable(tdsTable);

        return e;
    }

    /**
     * 获取数据源列表
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    @Override
    public TdataSourceVo getListTDataSource(TdsDataQueryDto tdd) {
        List<TdataSource> list = new ArrayList<TdataSource>();
        Where where = Where.create();
        // 模块或分类 moduleCode 模块，categoryId 分类
        if (tdd.getModule() != null && !"".equals(tdd.getModule())) {
            where.eq(TdataSource::getModuleCode, tdd.getModule());
//			where.eq(TdataSource::getModuleCode, tdd.getModule()).or().eq(TdataSource::getCreateBy, tdd.getModule());
        }
        // 数据源名称或别名 tdsname 名称 ，tdsalias 别名
        if (tdd.getTdsAlias() != null && !"".equals(tdd.getTdsAlias())) {
            where.like(TdataSource::getTdsname, tdd.getTdsAlias()).or().like(TdataSource::getTdsalias,
                    tdd.getTdsAlias());
        }
        Pagination<?> page = null;
        if (tdd.getPageSize() != null && tdd.getPageSize() > 0) {// 创建分页信息
            page = Pagination.create(tdd.getPage() == null ? 1 : tdd.getPage(), tdd.getPageSize());
        }
        Order order = Order.create();
        order.orderByDesc(TdataSource::getCreateTime);
        // order.orderByAsc(TdataSource::getTmSort);

        list = dao.queryData(TdataSource.class, where, order, page);
        TdataSourceVo bean = new TdataSourceVo();
        bean.setList(list);
        if (ObjUtils.notEmpty(page)) {
            bean.setTotal(page.getTotal());
        }
        return bean;
    }

    /**
     * 批量删除数据源
     *
     * @param tdataList
     * @return
     */
    @Override
    public boolean deleteTdsDatasources(List<TdataSource> tdataList) {
        boolean bool = false;
        if (tdataList != null && tdataList.size() > 0) {
            // 要删除的集合
            List<String> list = new ArrayList<String>();
            int num = tdataList.size();
            for (int i = 0; i < num; i++) {
                TdataSource e = tdataList.get(i);
                if (e.getIsSys() != null && e.getIsSys() == 1) {// 系统数据源不能删除
                    continue;
                    // log.info("系统数据源不能删除：" + e.getTdsalias());
                    // return false;
                }
                list.add(e.getTdsalias());
            }
            if (list.size() > 0) {
                // 删除 数据源主数据
                int i = 0;
                if (tdataList.size() == 1) {
                    i = dao.deleteIn(TdataSource.class, TdataSource::getTdsalias, list.get(0));
                } else {
                    i = dao.deleteIn(TdataSource.class, TdataSource::getTdsalias, list.toArray());
                }
                bool = i > 0 ? true : false;
                if (bool) {
                    // 删除成功后，重新排序
                    // updateTmSort(px); //重新排序先去掉，效率太低 by x.zhong 2022-06-15
                    // 删除输入参数
                    this.deleteTDSInPara(list);
//				// 删除输出参数
                    this.deleteTDSOutPara(list);
                    // 删除报表信息
                    this.deleteTDSTableInfo(list);
                    // 删除输入关联
                    this.inParamRelServ.deleteInparaRelation(list);

                    TDSCached cached = TDSCached.getInstance();
                    for (int j = 0; j < list.size(); j++) {
                        String dsAlias = list.get(j);
                        cached.removeAllDs(dsAlias);
                    }

                }
            }
        }
        return bool;
    }

    /**
     * 重新排序
     *
     * @param px
     */
    public void updateTmSort(int px) {
        List<TdataSource> listupdate = new ArrayList<TdataSource>();
        List<TdataSource> list = new ArrayList<TdataSource>();
        Where where = Where.create();
        where.gt(TdataSource::getTmSort, px);
        Order order = Order.create();
        order.orderByAsc(TdataSource::getTmSort);
        list = dao.queryList(TdataSource.class, where, order);

        if (list != null && list.size() > 0) {
            int mun = list.size();
            for (int i = 0; i < mun; i++) {
                TdataSource e = list.get(i);
                e.setTmSort(i + px);
                listupdate.add(e);
            }
            dao.updateByIdBatch(listupdate);
        }
    }

    private String decode(String str) {
        try {
            if (StringUtils.isEmpty(str)) {
                return str;
            } else {
                return new String(Base64.decode(str), "utf-8");
            }
        } catch (Exception e) {
            return str;
        }
    }

    /**
     * 保存除数据源
     *
     * @param TdsDataInfoDto 数据源所有数据
     * @return
     */
    @Override
    public boolean saveTdsDatasource(TdsDataInfoDto tdsData) {
        boolean bool = true;
        String dsAlias = "";
        if (tdsData != null) {
            // 数据源基础信息状态
            Integer tdataSource = tdsData.getTdataSource();
            // 数据源输入参数状态
            Integer tdsinPara = tdsData.getTdsinPara();
            // 数据源输出参数状态
            Integer tdsoutPara = tdsData.getTdsoutPara();
            // 数据源报表状态
            Integer tdstableInfo = tdsData.getTdstableInfo();
            // 数据源基础信息
            TdataSource tdata = tdsData.getTdata();
            dsAlias = tdata.getTdsalias() == null ? "" : tdata.getTdsalias();
            if (tdataSource > 0) {
                // 前台base64加密，后台base64解密
//	base64field: ["memo", "tdsCalScript", "tdsCanEditScript", "tdsInitSql", "tdsQuerySql", "script","tdsUpdateSql"],
                tdata.setMemo(decode(tdata.getMemo()));
                tdata.setTdsCalScript(decode(tdata.getTdsCalScript()));
                tdata.setTdsCanEditScript(decode(tdata.getTdsCanEditScript()));
                tdata.setTdsInitSql(decode(tdata.getTdsInitSql()));
                tdata.setTdsQuerySql(decode(tdata.getTdsQuerySql()));
                tdata.setScript(decode(tdata.getScript()));
                tdata.setTdsUpdateSql(decode(tdata.getTdsUpdateSql()));
                tdata.setTdsAfterEditScript(decode(tdata.getTdsAfterEditScript()));
                tdata.setExcelHeaderRows(tdata.getExcelHeaderRows());
                // 需要处理
                if (tdata.getId() != null && !"".equals(tdata.getId())) {// 修改
                    int i = dao.updateById(tdata);
                    bool = i > 0 ? true : false;
                } else {// 添加
                    tdata.setId(TMUID.getUID());
                    int i = dao.insert(tdata);
                    bool = i > 0 ? true : false;
                }
            }

            if (bool) {
                Map<String, String> mapin = new HashMap<String, String>();
                // 数据源输入参数
                List<TdsinPara> tdsin = tdsData.getTdsin();
                if (tdsinPara > 0) {
                    List<TdsinPara> listaddin = new ArrayList<TdsinPara>();
                    List<TdsinPara> listupin = new ArrayList<TdsinPara>();
                    int m = tdsin.size();
                    for (int i = 0; i < m; i++) {
                        TdsinPara tin = tdsin.get(i);
                        String key = tin.getParaAlias();// 输入参数别名
                        if (key == null) {
                            continue;
                        }
                        key = key.trim();
                        tin.setParaAlias(key);
                        if (StringUtils.isNotEmpty(tin.getParaName())) {
                            tin.setParaName(tin.getParaName().trim());
                        } else {
                            tin.setParaName(key);
                        }
                        if (mapin.containsKey(key)) {
                            // 有重复别名
                            return false;
                        } else {
                            mapin.put(key, "");
                        }
                        if (tin.getId() != null && !"".equals(tin.getId())) {
                            // 修改
                            listupin.add(tin);
                        } else {
                            // 添加
                            tin.setId(TMUID.getUID());
                            listaddin.add(tin);
                        }
                    }
                    if (listaddin != null && listaddin.size() > 0) {
                        int i = dao.insertBatch(listaddin);
                        bool = i > 0 ? true : false;
                    }
                    if (listupin != null && listupin.size() > 0) {
                        int i = dao.updateByIdBatch(listupin);
                        bool = i > 0 ? true : false;
                    }
                    if (!bool) {
                        return bool;
                    }
                }

                // 数据源输出参数
                List<TdsoutPara> tdsout = tdsData.getTdsout();
                if (tdsoutPara > 0) {
                    Map<String, String> mapout = new HashMap<String, String>();
                    List<TdsoutPara> listaddout = new ArrayList<TdsoutPara>();
                    List<TdsoutPara> listupout = new ArrayList<TdsoutPara>();
                    int m = tdsout.size();
                    for (int i = 0; i < m; i++) {
                        TdsoutPara tout = tdsout.get(i);
                        String key1 = tout.getParaAlias();// 输入参数别名

                        if (key1 == null) {
                            continue;
                        }
                        key1 = key1.trim();
                        tout.setParaAlias(key1);
                        if (StringUtils.isNotEmpty(tout.getParaName())) {
                            tout.setParaName(tout.getParaName().trim());
                        } else {
                            tout.setParaName(key1);
                        }
                        if (mapout.containsKey(key1)) {// 有重复别名
                            return false;
                        } else {
                            mapout.put(key1, "");
                        }
                        if (tout.getId() != null && !"".equals(tout.getId())) {
                            // 修改
                            listupout.add(tout);
                        } else {// 添加
                            tout.setId(TMUID.getUID());
                            listaddout.add(tout);
                        }
                    }
                    if (listaddout != null && listaddout.size() > 0) {
                        int i = dao.insertBatch(listaddout);
                        bool = i > 0 ? true : false;
                    }
                    if (listupout != null && listupout.size() > 0) {
                        int i = dao.updateByIdBatch(listupout);
                        bool = i > 0 ? true : false;
                    }
                    if (!bool) {
                        return bool;
                    }
                }
                // 数据源报表信息
                TdstableInfo tdsTable = tdsData.getTdsTable();
                if (tdstableInfo > 0) {
                    // 需要处理
                    if (tdsTable.getId() != null && !"".equals(tdsTable.getId())) {// 修改
                        int i = dao.updateById(tdsTable);
                        bool = i > 0 ? true : false;
                    } else {// 添加
                        TdstableInfo tdsTableData = this.getTDSTableInfo(dsAlias);

                        if (tdsTableData != null) {
                            tdsTable.setId(tdsTableData.getId());
                            tdsTable.setTdsAlias(dsAlias);
                            int i = dao.updateById(tdsTable);
                            bool = i > 0 ? true : false;
                        } else {
                            tdsTable.setId(TMUID.getUID());
                            tdsTable.setTdsAlias(dsAlias);
                            // int i = dao.insert(tdsTable);
                            // bool = i > 0 ? true : false;
                            bool = insertTDSTableInfo(tdsTable);
                        }
                    }
                    if (!bool) {
                        return bool;
                    }
                }
                // 保存排序信息
                if (StringUtils.isNotEmpty(tdsData.getSortdata())) {
                    this.savaSortData(dsAlias, tdsData.getSortdata());
                }
            }

        }
        if (bool) {
            TDSCached cached = TDSCached.getInstance();
            cached.removeAllDs(dsAlias);
        }
        return bool;
    }

    /**
     * 删除输入参数
     *
     * @param id
     * @return
     */
    @Override
    public boolean delTdsinPara(String id) {
        // 删除输入参数
        boolean bool = true;
        try {
            JSONObject jsonObj = JSONObject.parseObject(id);
            id = jsonObj.getString("id");
        } catch (Exception e) {

        }

        List<String> list = new ArrayList<String>();
        if (id != null && !"".equals(id)) {
            if (id.indexOf(",") < 0) {
                list.add(id);
            } else {
                String arr[] = id.split(",");
                for (int i = 0; i < arr.length; i++) {
                    list.add(arr[i]);
                }
            }
        } else {
            return false;
        }
        Where where = Where.create();
        where.in(TdsinPara::getId, list.toArray());
        List<TdsinPara> list0 = dao.queryList(TdsinPara.class, where);

        int i = dao.deleteIn(TdsinPara.class, TdsinPara::getId, list.toArray());
        bool = i > 0 ? true : false;
        if (bool) {
            TDSCached cached = TDSCached.getInstance();
            if (list0 != null && list0.size() > 0) {
                String dsAlias = list0.get(0).getTdsalias();
                cached.removeInPara(dsAlias);
            }
        }
        return bool;
    }

    /**
     * 删除输出参数
     *
     * @param id
     * @return
     */
    @Override
    public boolean delTdsoutPara(String id) {
        // 删除输出参数
        boolean bool = true;
        try {
            JSONObject jsonObj = JSONObject.parseObject(id);
            id = jsonObj.getString("id");
        } catch (Exception e) {

        }
        List<String> list = new ArrayList<String>();
        if (id != null && !"".equals(id)) {
            if (id.indexOf(",") < 0) {
                list.add(id);
            } else {
                String arr[] = id.split(",");
                for (int i = 0; i < arr.length; i++) {
                    list.add(arr[i]);
                }
            }
        } else {
            return false;
        }
        Where where = Where.create();
        where.in(TdsoutPara::getId, list.toArray());
        List<TdsoutPara> list0 = dao.queryList(TdsoutPara.class, where);
        int i = dao.deleteIn(TdsoutPara.class, TdsoutPara::getId, list.toArray());
        bool = i > 0 ? true : false;

        if (bool) {
            TDSCached cached = TDSCached.getInstance();
            if (list0 != null && list0.size() > 0) {
                String dsAlias = list0.get(0).getTdsalias();
                cached.removeOutPara(dsAlias);
            }
        }
        return bool;
    }

    /**
     * 判断数据源别名是否存在
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public boolean verificationTdsAlias(String tdsAlias) {
        boolean bool = true;
        if (tdsAlias != null && !"".equals(tdsAlias)) {
            try {
                JSONObject jsonObj = JSONObject.parseObject(tdsAlias);
                tdsAlias = jsonObj.getString("tdsAlias");
            } catch (Exception e) {

            }
            TdataSource bean = this.getTDataSource(tdsAlias);
            if (bean != null) {
                bool = false;
            }
        }
        return bool;
    }

    /**
     * 获取数据源列表
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    @Override
    public List<TdsComboBox> getListTdataSource(String tdsAlias) {
        List<TdsComboBox> listCb = new ArrayList<TdsComboBox>();
        try {
            JSONObject jsonObj = JSONObject.parseObject(tdsAlias);
            tdsAlias = jsonObj.getString("tdsAlias");
        } catch (Exception e) {

        }
        Where where = new Where();
        where.eq(TdataSource::getUsed, 1);
        // 检索参数--模糊检索数据源别名/数据源名称
        if (tdsAlias != null && !"".equals(tdsAlias)) {
            where.like(TdataSource::getTdsalias, tdsAlias).or().like(TdataSource::getTdsname, tdsAlias);
        }
        Order order = Order.create();
        order.orderByAsc(TdataSource::getTmSort);
        List<TdataSource> list = dao.queryList(TdataSource.class, where, order);
        if (list != null && list.size() > 0) {
            int m = list.size();
            for (int i = 0; i < m; i++) {
                TdataSource e = list.get(i);
                String id = e.getTdsalias();
                String name = e.getTdsname();
                TdsComboBox bean = new TdsComboBox();
                bean.setId(id);
                bean.setName(name);
                listCb.add(bean);
            }
        }

        return listCb;

    }

    /**
     * 获取数据源绑定子表数据源的参数
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    @Override
    public List<TdsBind> getListTdsBind(String data) {
        List<TdsBind> listb = new ArrayList<TdsBind>();
        String p_tdsAlias = "";// 主数据源
        String c_tdsAlias = "";// 子数据源
        JSONObject jsonObj = JSONObject.parseObject(data);
        if (jsonObj.containsKey("p_tdsAlias")) {
            p_tdsAlias = jsonObj.getString("p_tdsAlias");
        }
        if (jsonObj.containsKey("c_tdsAlias")) {
            c_tdsAlias = jsonObj.getString("c_tdsAlias");
        }

        if (p_tdsAlias == null || c_tdsAlias == null || "".equals(p_tdsAlias) || "".equals(c_tdsAlias)) {
            return listb;
        }

        Where where = new Where();
        Order order = Order.create();
        // 获取绑定表数据
        where.eq(TdsBind::getPTdsAlias, p_tdsAlias);
        order.orderByAsc(TdsBind::getParaid);
        Map<String, TdsBind> map = new HashMap<String, TdsBind>();
        List<TdsBind> listbind = dao.queryList(TdsBind.class, where, order);
        if (listbind != null && listbind.size() > 0) {
            int m = listbind.size();
            for (int i = 0; i < m; i++) {
                TdsBind e = listbind.get(i);
                String key = e.getCTdsInParaAlias();
                map.put(key, e);
            }
        }
        where = new Where();
        order = Order.create();
        // 获取子表的输入参数
        where.eq(TdsinPara::getTdsalias, c_tdsAlias);
        order.orderByAsc(TdsinPara::getParaId);
        List<TdsinPara> listinp = dao.queryList(TdsinPara.class, where, order);
        if (listinp != null && listinp.size() > 0) {
            int m = listinp.size();
            for (int i = 0; i < m; i++) {
                TdsinPara e = listinp.get(i);
                String paraalias = e.getParaAlias();// 输入参数别名
                String paraName = e.getParaName();// 输入参数名称

                TdsBind bean = new TdsBind();
                bean.setId(TMUID.getUID());
                bean.setParaid(i + 1);// 排序
                bean.setCTdsAlias(c_tdsAlias);// 子数据源别名
                bean.setCTdsInParaAlias(paraalias);// 子数据源输入参数别名
                bean.setCTdsInParaName(paraName);// 子数据源输入参数别名
                bean.setPTdsAlias(p_tdsAlias);// 主数据源别名
                bean.setPTdsOutParaAlias("");// 子数据源输出参数别名
                bean.setPTdsOutParaName("");// 子数据源输出参数别名
                if (map.containsKey(paraalias)) {
                    TdsBind tb = map.get(paraalias);
                    bean.setPTdsAlias(tb.getPTdsAlias());// 主数据源别名
                    bean.setPTdsOutParaAlias(tb.getPTdsOutParaAlias());// 子数据源输出参数别名
                    bean.setPTdsOutParaName(tb.getPTdsOutParaName());// 子数据源输出参数别名
                }
                listb.add(bean);
            }
        }

        if (listbind != null && listbind.size() > 0) {
            // 删除原绑定
            dao.deleteByIdBatch(listbind);
        }
        if (listb != null && listb.size() > 0) {
            // 保存新绑定
            int n = dao.insertBatch(listb);
            if (n > 0) {
                TDSCached cached = TDSCached.getInstance();
                cached.removeOutPara(p_tdsAlias);
            }
        }
        return listb;

    }

    /**
     * 保存绑定关系
     *
     * @param tdsinPara
     * @return
     */
    @Override
    public boolean saveTdsBind(List<TdsBind> tdsbind) {
        boolean bool = true;
        int i = dao.updateByIdBatch(tdsbind);
        bool = i > 0 ? true : false;
        if (bool) {
            if (tdsbind != null && tdsbind.size() > 0) {
                String dsAlias = tdsbind.get(0).getPTdsAlias();
                TDSCached cached = TDSCached.getInstance();
                cached.removeOutPara(dsAlias);
            }
        }
        return bool;
    }

    /**
     * 拷贝数据源输入参数
     *
     * @param data
     * @return
     */
    @Override
    public List<TdsinPara> getCopyTdsinPara(String data) {
        List<TdsinPara> list = new ArrayList<TdsinPara>();
        String p_tdsAlias = "";// 被复制数据源
        String c_tdsAlias = "";// 拷贝后的数据源
        JSONObject jsonObj = JSONObject.parseObject(data);
        if (jsonObj.containsKey("p_tdsAlias")) {
            p_tdsAlias = jsonObj.getString("p_tdsAlias");
        }
        if (jsonObj.containsKey("c_tdsAlias")) {
            c_tdsAlias = jsonObj.getString("c_tdsAlias");
        }

        if (p_tdsAlias == null || c_tdsAlias == null || "".equals(p_tdsAlias) || "".equals(c_tdsAlias)) {
            return list;
        }
        Where where = new Where();
        Order order = Order.create();
        // 获取绑定表数据
        where.eq(TdsinPara::getTdsalias, p_tdsAlias);
        order.orderByAsc(TdsinPara::getParaId);
        List<TdsinPara> listb = dao.queryList(TdsinPara.class, where, order);

        if (listb != null && listb.size() > 0) {
            where = Where.create();
            where.eq(TdsinPara::getTdsalias, c_tdsAlias);
            // 查询要复制的数据源输入参数
            List<TdsinPara> lista = dao.queryList(TdsinPara.class, where, order);
            if (lista != null && lista.size() > 0) {
                return list;
            } else {
                for (int i = 0; i < listb.size(); i++) {
                    TdsinPara e1 = listb.get(i);

                    TdsinPara e2 = new TdsinPara();
                    e2.setId(TMUID.getUID());
                    e2.setTdsalias(c_tdsAlias);

                    // 输入参数别名
                    e2.setParaAlias(e1.getParaAlias());
                    // 显示顺序
                    e2.setParaId(e1.getParaId());
                    // 参数名称
                    e2.setParaName(e1.getParaName());
                    // 数据类型（TM4目前未使用，默认值：tdsString）
                    e2.setDataType(e1.getDataType());
                    // 下拉框填充key值脚本
                    e2.setDefaultKeyScript(e1.getDefaultKeyScript());
                    // 下拉框添加显示值脚本
                    e2.setDefaultValueScript(e1.getDefaultValueScript());
                    // 组件类型
                    e2.setComponentType(e1.getComponentType());
                    // 是否显示 1：显示；0：隐藏
                    e2.setDisplay(e1.getDisplay());
                    // 备注
                    e2.setMemo(e1.getMemo());
                    // 下拉框是否支持模糊检索（TM4未使用）
                    e2.setIscanquery(e1.getIscanquery());
                    // 导入选项，常规列1，关键列2，不导入3（TM4未使用）
                    e2.setImportChoice(e1.getImportChoice());
                    // 输入是否代入录入项 for 数据源编辑
                    e2.setInsertEdit(e1.getInsertEdit());
                    // 控件显示宽度
                    e2.setWidth(e1.getWidth());
                    // 初始化默认值脚本（TM4预留，未使用）
                    e2.setInitValueScript(e1.getInitValueScript());

                    list.add(e2);

                }

                if (list != null && list.size() > 0) {
                    int n = dao.insertBatch(list);
                    if (n > 0) {
                        TDSCached cached = TDSCached.getInstance();
                        cached.removeInPara(c_tdsAlias);

                    }
                }
            }
        }
        return list;
    }

    /**
     * 生成数据源输出参数
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public List<TdsoutPara> getCreateTdsOutPara(String data) {
        List<TdsoutPara> list = new ArrayList<TdsoutPara>();
        String tdsAlias = "";// 拷贝后的数据源
        try {
            JSONObject jsonObj = JSONObject.parseObject(data);
            if (jsonObj.containsKey("tdsAlias")) {
                tdsAlias = jsonObj.getString("tdsAlias");
            }
        } catch (Exception e) {
            tdsAlias = data;
        }

        if (tdsAlias == null || "".equals(tdsAlias)) {
            return list;
        }
        TdataSource tdsBean = this.getTDataSource(tdsAlias);
        if (tdsBean == null) {
            return list;
        }
        Map<String, TdsoutPara> outMap = new HashMap<String, TdsoutPara>();
        List<TdsoutPara> outList = this.getListTdsOutPara(tdsAlias);
        if (StringUtils.isNotEmpty(outList)) {
            for (TdsoutPara e : outList) {
                outMap.put(e.getParaAlias().toLowerCase(), e);
            }
        }
        if ("TDSDataSource".equalsIgnoreCase(tdsBean.getTdsclassName())) {// 数据源作为数据来源模式
            String sourceAlias = tdsBean.getTdsQuerySql();
            if (StringUtils.isNotEmpty(sourceAlias)) {
                sourceAlias = sourceAlias.trim();
            }
            List<TdsoutPara> sourceList = this.getTDSOutPara(sourceAlias);
            if (StringUtils.isNotEmpty(sourceList)) {
                for (TdsoutPara e : sourceList) {
                    if (!outMap.containsKey(e.getParaAlias().toLowerCase())) {
                        e.setId(TMUID.getUID());
                        e.setTdsalias(tdsAlias);
                        list.add(e);
                    }
                }
            }
        } else {// 其他类型数据源
            TDataSourceManager dsm = new TDataSourceManager(SysUserHolder.getCurrentUser());
            IDataSource ids = dsm.getDataSource(tdsAlias, false);
            if (ids != null) {
                ids.load(1, 1);
                LinkedHashMap<String, TOutPara> rsMap = ids.getRsCols();
                if (rsMap != null) {
                    int i = 0;
                    for (String alias : rsMap.keySet()) {
                        i++;
                        if (!outMap.containsKey(alias)) {
                            TOutPara tOutPara = rsMap.get(alias);
                            TdsoutPara bean = new TdsoutPara();
                            bean.setId(TMUID.getUID());
                            // 数据源别名
                            bean.setTdsalias(tdsAlias);
                            // 输出参数别名
                            if ("ID".equalsIgnoreCase(alias)) {
                                bean.setParaAlias(alias.toUpperCase());
                            } else {
                                bean.setParaAlias(alias);
                            }
                            // 显示顺序序号
                            bean.setParaId(i);
                            // 参数名称
                            bean.setParaName(tOutPara.getAlias());
                            // 数据类型，默认tdsString
                            if (tOutPara.getDataType() != null) {
                                if ("tdsDouble".equals(tOutPara.getDataType().toString())) {
                                    bean.setDataType(tOutPara.getDataType().toString());
                                } else {
                                    bean.setDataType("tdsString");
                                }
                            } else {
                                bean.setDataType("tdsString");
                            }
                            // 是否显示，1：显示，0：隐藏
                            bean.setVisible(1);
                            // 控件宽度
                            bean.setWidth(tOutPara.getWidth());
                            // 对齐方式 center,left,right
                            bean.setAlign(tOutPara.getAlign());
                            bean.setIsSum(0);
                            // 是否分组（TM4未使用）
                            bean.setIsGroup(0);
                            // 是否合并显示
                            bean.setIsSpan(0);
                            // 组件类型 for 数据源编辑
                            // bean.setComType("textfield");
                            // 当内容过长被隐藏时显示 tooltip
                            bean.setOvertip(0);
                            // 列是否固定在左侧或者右侧，true 表示固定在左侧 string, boolean true, left, right
                            bean.setAutowidth(0);
                            // 输入限制最大长度
                            if (tOutPara.getMaxlength() != null && tOutPara.getMaxlength() > 100000000) {
                                bean.setMaxlength(100000000l);
                            } else {
                                bean.setMaxlength(tOutPara.getMaxlength());
                            }
                            list.add(bean);
                        }
                    }
                }
            }
        }
        if (list.size() > 0) {
            boolean bln = this.insertTDSOutPara(list);
            if (bln) {
                list = this.getListTdsOutPara(tdsAlias);
                TDSCached cached = TDSCached.getInstance();
                cached.removeOutPara(tdsAlias);
            }
        }
        return list;
    }

    /**
     * 生成数据源输出参数
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public List<TdsoutPara> getListTdsOutPara(String tdsAlias) {
        try {
            JSONObject jsonObj = JSONObject.parseObject(tdsAlias);
            tdsAlias = jsonObj.getString("tdsAlias");
        } catch (Exception e) {

        }
        return this.getTDSOutPara(tdsAlias);

    }

    /**
     * 数据源另存为
     *
     * @param tdsAlias
     * @return
     */
    @Transactional
    @Override
    public boolean saveAsTDatasource(String data) {
        boolean bool = true;
        String tdsAlias = "";// 数据源别名
        String newTdsAlias = "";// 新数据源别名
        String newTdsname = "";// 新数据源名称
        JSONObject jsonObj = JSONObject.parseObject(data);
        if (jsonObj.containsKey("tdsAlias")) {
            tdsAlias = jsonObj.getString("tdsAlias");
        }
        if (jsonObj.containsKey("newTdsAlias")) {
            newTdsAlias = jsonObj.getString("newTdsAlias");
        }
        if (jsonObj.containsKey("newTdsname")) {
            newTdsname = jsonObj.getString("newTdsname");
        }
        if (tdsAlias == null || newTdsAlias == null || newTdsname == null || "".equals(tdsAlias)
                || "".equals(newTdsAlias) || "".equals(newTdsname)) {
            return false;
        }
        TdataSource tdata = this.getTDataSource(tdsAlias);// 数据源基础信息
        if (tdata != null && tdata.getId() != null) {
            tdata.setId(TMUID.getUID());
            tdata.setTdsalias(newTdsAlias);
            tdata.setTdsname(newTdsname);
        }

        List<TdsinPara> tdsin = this.getTDSInPara(tdsAlias);
        List<TdsinPara> addin = new ArrayList<TdsinPara>();// 数据源输入参数
        if (tdsin != null && tdsin.size() > 0) {
            int m = tdsin.size();
            for (int i = 0; i < m; i++) {
                TdsinPara bean = tdsin.get(i);
                bean.setId(TMUID.getUID());
                bean.setTdsalias(newTdsAlias);
                addin.add(bean);
            }
        }

        List<TdsoutPara> addout = new ArrayList<TdsoutPara>();// "数据源输出参数
        List<TdsoutPara> tdsout = this.getTDSOutPara(tdsAlias);
        if (tdsout != null && tdsout.size() > 0) {
            int m = tdsout.size();
            for (int i = 0; i < m; i++) {
                TdsoutPara bean = tdsout.get(i);
                bean.setId(TMUID.getUID());
                bean.setTdsalias(newTdsAlias);
                addout.add(bean);
            }
        }

        TdstableInfo tdsTable = this.getTDSTableInfo(tdsAlias);// 数据源报表信息
        if (tdsTable != null && tdsTable.getId() != null) {
            tdsTable.setId(TMUID.getUID());
            tdsTable.setTdsAlias(newTdsAlias);
        }
        int i1 = dao.insert(tdata);
        bool = i1 > 0 ? true : false;
        if (bool) {
            if (StringUtils.isNotEmpty(addin)) {
                dao.insertBatch(addin);
            }
            if (StringUtils.isNotEmpty(addout)) {
                dao.insertBatch(addout);
            }
            if (tdsTable != null) {
                dao.insert(tdsTable);
            }
            // 输入参数联动关系另存
            inParamRelServ.saveasTdsInparaRelation(tdsAlias, newTdsAlias);

            accountServ.copyAccount(tdsAlias, newTdsAlias);
        }
        return bool;
    }

    /**
     * 新增 输入参数
     *
     * @param listIn
     * @param tdsAlias
     * @param paraAlias
     * @param paraName
     * @param defaultKeyScript
     * @param defaultValueScript
     */
    @Override
    public void newTdsIn(List<TdsinPara> listIn, String tdsAlias, String paraAlias, String paraName, String comType,
                         String defaultKeyScript, String defaultValueScript, Map<String, Object> paramsMap) {
        TdsinPara e = new TdsinPara();
        if (StringUtils.isNotEmpty(paramsMap)) {
            ObjUtils.copy(e, paramsMap);
        }
        e.setId(TMUID.getUID());
        e.setTdsalias(tdsAlias);
        if (listIn == null) {
            listIn = new ArrayList<TdsinPara>();
        }
        e.setParaId(listIn.size() + 1);
        e.setParaName(paraName);
        e.setParaAlias(paraAlias);
        e.setDataType("tdsString");
        if (e.getDisplay() == null) {
            e.setDisplay(1);
        }
        e.setComponentType(comType);
        e.setDefaultKeyScript(defaultKeyScript);
        e.setDefaultValueScript(defaultValueScript);
        listIn.add(e);
    }

    /**
     * 新建输出参数
     *
     * @param listOut
     * @param tdsAlias  数据源别名
     * @param paraAlias 输出参数别名
     * @param paraName  输出名称
     */
    @Override
    public void newTdsOut(List<TdsoutPara> listOut, String tdsAlias, String paraAlias, String paraName,
                          Map<String, Object> paramsMap) {
        TdsoutPara e = new TdsoutPara();
        if (StringUtils.isNotEmpty(paramsMap)) {
            ObjUtils.copy(e, paramsMap);
        }
        e.setId(TMUID.getUID());
        e.setTdsalias(tdsAlias);
        if (listOut == null) {
            listOut = new ArrayList<TdsoutPara>();
        }
        e.setParaId(listOut.size() + 1);
        e.setParaName(paraName);
        e.setParaAlias(paraAlias);
        e.setDataType("tdsString");
        e.setIskey(0);
        if (e.getVisible() == null) {
            e.setVisible(1);
        }
        listOut.add(e);
    }

    /**
     * 新建数据源
     *
     * @param moduleCode
     * @param tdsAlias
     * @param tdsName
     * @param className
     * @param sql
     * @return
     */
    @Override
    public TdataSource newTds(String moduleCode, String tdsAlias, String tdsName, String className, Integer isSys,
                              String sql) {
        TdataSource e = new TdataSource();
        e.setId(tdsAlias);
        e.setTdsalias(tdsAlias);
        e.setModuleCode(moduleCode);
        e.setTdsname(tdsName);
        e.setTdstype("SYS");
        e.setTdsclassName(className == null ? "TDSSQL" : className);
        e.setMemo(tdsName);
        e.setUsed(1);
        e.setAutoLoad(0);
        if (isSys != null) {
            e.setIsSys(isSys);
        } else {
            e.setIsSys(0);
        }
        Long sort = getTDataSourceCount();
        if (sort != null) {
            e.setTmSort(sort.intValue() + 1);
        } else {
            sort = 1L;
            e.setTmSort(1);
        }
        return e;
    }

    @Override
    public TdataSource newTds(String moduleCode, String tdsAlias, String tdsName, String className) {
        return this.newTds(moduleCode, tdsAlias, tdsName, className, 1, null);
    }

    /**
     * 获取数据源工作流程设置数据
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    public TdsFlow getTdsFlowData(String tdsAlias) {
        TdsFlow result = null;
        TDSCached cached = TDSCached.getInstance();
        result = cached.getTdsFlowInfo(tdsAlias);// redis中获取
        if (result == null) {
            Where where = Where.create();
            where.eq(TdsFlow::getTdsalias, tdsAlias);
            result = dao.queryObject(TdsFlow.class, where);
            if (result == null) {
                result = new TdsFlow();
                result.setId("");// 数据唯一主键
                result.setAllowcommitscript("");// 允许发起流程提交脚本
                result.setBusinessKey("");// 业务主键
                result.setBusiTemplateId("");// 工作流程编码
                result.setIsshowbatchstartbtn(0);// 是否显示批量发起按钮（1：显示，0：不显示）
                result.setIsshowoutcolumn(1);// 是否显示输出列（1：显示，0：不显示）
                result.setIsstartflow(0);// 是否启动工作流（1：启动，0：未启动）
                result.setTdsalias(tdsAlias);// 数据源别名
                result.setToPath("");// 流程启动后，自动跳转页面地址
                result.setRootBusinessKey("");// 根业务主键
            }
            cached.putTdsFlowInfo(tdsAlias, result); // 存入内存数据库
        }
        return result;
    }

    /**
     * 保存数据源工作流程设置数据
     *
     * @param dto
     * @return
     */
    @Transactional
    public boolean saveTdsFlowData(TdsFlowDto dto) {
        boolean bln = false;
        TDSCached cached = TDSCached.getInstance();
        List<TdsFlow> listData = dto.getData();
        List<TdsFlow> listAdd = new ArrayList<TdsFlow>();
        List<TdsFlow> listUp = new ArrayList<TdsFlow>();
        if (StringUtils.isNotEmpty(listData)) {
            for (TdsFlow temp : listData) {
                if (temp.getId() == null || temp.getId().length() <= 0) {
                    String tmuid = TMUID.getUID();
                    temp.setId(tmuid);
                    listAdd.add(temp);
                } else {
                    listUp.add(temp);
                }
            }
        }
        boolean blnAdd = false;
        if (StringUtils.isNotEmpty(listAdd)) {
            int row = dao.insertBatch(listAdd);
            blnAdd = row > 0 ? true : false;
            if (blnAdd) {
                cached.removeTdsFlowInfo(dto.getTdsAlias());
            }
        } else {
            blnAdd = true;
        }
        boolean blnUp = false;
        if (StringUtils.isNotEmpty(listUp)) {
            int row = dao.updateByIdBatch(listUp);
            blnUp = row > 0 ? true : false;
            if (blnUp) {
                cached.removeTdsFlowInfo(dto.getTdsAlias());
            }
        } else {
            blnUp = true;
        }
        if (blnAdd && blnUp) {
            bln = true;
        }
        return bln;
    }

    /**
     * 为某个表创建字段
     *
     * @param tableName      表名
     * @param listColumnName 字段名
     * @return
     */
    public boolean createTableColumn(String tableName, List<String> listColumnName) {
        StringBuffer strB = new StringBuffer();
        HashMap<String, TableColumnInfo> map = new HashMap<String, TableColumnInfo>();
        map = getTableColumn(tableName);
        if (StringUtils.isNotEmpty(listColumnName)) {
            for (int i = 0; i < listColumnName.size(); i++) {
                String colName = listColumnName.get(i);
                colName = colName.toUpperCase();
                if (map.containsKey(colName)) {// 字段已存在不允许创建

                } else {
                    String dbtype = dao.getDatabaseType();
                    if (dao.isOracle()) {// if ("oracle".equalsIgnoreCase(dbtype) || "dm".equalsIgnoreCase(dbtype)) {
                        strB.append("alter table " + tableName + " add " + colName + " varchar2(255);");
                    } else if ("sqlserver".equalsIgnoreCase(dbtype)) {
                        strB.append("alter table " + tableName + " add " + colName + " varchar(255);");
                    } else {
                        strB.append("alter table " + tableName + " add " + colName + " varchar(255);");
                    }
                }
            }
        }
        boolean blnRtn = false;
        if (strB != null && strB.length() > 0) {
            blnRtn = dao.execute(strB.toString());
        } else {
            blnRtn = true;
        }
        return blnRtn;
    }

    /**
     * 为某个表创建字段
     *
     * @param tableName      表名
     * @param listColumnName 字段名
     * @param value          字段值
     * @param where          条件
     * @return
     */
    public boolean updateTableColumn(String tableName, List<String> listColumnName, String value, String where) {
        StringBuffer strB = new StringBuffer();
        HashMap<String, TableColumnInfo> map = new HashMap<String, TableColumnInfo>();
        map = getTableColumn(tableName);
        if (StringUtils.isNotEmpty(listColumnName)) {
            for (int i = 0; i < listColumnName.size(); i++) {
                String colName = listColumnName.get(i);
                colName = colName.toUpperCase();
                if (map.containsKey(colName)) {// 字段已存在
                    String dbtype = dao.getDatabaseType();
                    if (dao.isOracle()) {// if ("oracle".equalsIgnoreCase(dbtype) || "dm".equalsIgnoreCase(dbtype)) {
                        strB.append(" update ");
                        strB.append(tableName);
                        strB.append(" set ");
                        strB.append(colName);
                        strB.append(" ='");
                        strB.append(value);
                        strB.append("' where ");
                        strB.append(where);
                        strB.append(";");
                    } else if ("sqlserver".equalsIgnoreCase(dbtype)) {
                        strB.append(" update ");
                        strB.append(tableName);
                        strB.append(" set ");
                        strB.append(colName);
                        strB.append(" ='");
                        strB.append(value);
                        strB.append("' where ");
                        strB.append(where);
                        strB.append(";");
                    } else {
                        strB.append(" update ");
                        strB.append(tableName);
                        strB.append(" set ");
                        strB.append(colName);
                        strB.append(" ='");
                        strB.append(value);
                        strB.append("' where ");
                        strB.append(where);
                        strB.append(";");
                    }
                }
            }
        }
        boolean blnRtn = false;
        if (strB != null && strB.length() > 0) {
            blnRtn = dao.execute(strB.toString());
        } else {
            blnRtn = true;
        }
        return blnRtn;
    }

    /**
     * 判断表字段是否已存，存在返回false，不允许重复创建
     *
     * @param tableName 表名
     * @param colName   字段名
     * @return
     */
    private HashMap<String, TableColumnInfo> getTableColumn(String tableName) {
        HashMap<String, TableColumnInfo> map = new HashMap<String, TableColumnInfo>();
        List<TableColumnInfo> listTableCol = dao.getTableColumnList(tableName);
        if (StringUtils.isNotEmpty(listTableCol)) {
            for (TableColumnInfo temp : listTableCol) {
                map.put(temp.getColumnName().toUpperCase(), temp);
            }
        }
        return map;
    }

    /**
     * 判断某个数据源字段是否存在
     *
     * @param tdsAlias   数据源别名
     * @param columnName 字段名
     * @return
     */
    @Override
    public boolean isExtTableColumnNameByTds(String tdsAlias, String columnName) {
        String tableName = "";
        boolean result = false;
        TdataSource tdsObj = getTDataSource(tdsAlias);
        if (tdsObj != null) {
            tableName = tdsObj.getDbTableName() == null ? "" : tdsObj.getDbTableName();
            if (tableName.length() > 0) {
                result = isExitTableColumnName(tableName, columnName);
            }
        }
        return result;
    }

    /**
     * 获取字段map
     *
     * @param tdsAlias
     * @return
     */
    private Map<String, TableColumnInfo> getTdsEditTableColumnMap(String tdsAlias) {
        Map<String, TableColumnInfo> map = new HashMap<String, TableColumnInfo>();
        TdataSource tdsObj = getTDataSource(tdsAlias);
        if (tdsObj != null) {
            String tableName = tdsObj.getDbTableName();
            if (StringUtils.isNotEmpty(tableName)) {
                map = getTableColumn(tableName);
            }
        }
        return map;
    }

    /**
     * 判断字段在表中是否存在
     *
     * @param columnMap  字段map
     * @param columnName 字段名
     * @return
     */
    private boolean isExitTableColumnName(Map<String, TableColumnInfo> columnMap, String columnName) {
        if (columnMap != null) {
            if (columnMap.containsKey(columnName.toUpperCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断字段在表中是否存在
     *
     * @param tableName  表名
     * @param columnName 字段名
     * @return
     */
    private boolean isExitTableColumnName(String tableName, String columnName) {
        return isExitTableColumnName(getTableColumn(tableName), columnName);
    }

    /**
     * 保存数据源编辑自定义按钮数据
     *
     * @param dto
     * @return
     */
    @Override
    public boolean saveCustomBtnData(TdsEditCustomBtnDto dto) {
        boolean result = true;
        if (dto != null) {
            List<TdsEditCustomBtn> list = dto.getData();
            if (StringUtils.isNotEmpty(list)) {
                SysUser user = SysUserHolder.getCurrentUser();
                List<TdsEditCustomBtn> listAdd = new ArrayList<TdsEditCustomBtn>();
                List<TdsEditCustomBtn> listUp = new ArrayList<TdsEditCustomBtn>();
                for (TdsEditCustomBtn temp : list) {
                    Integer rowFlag = temp.getRowFlag() == null ? 0 : temp.getRowFlag();
                    if (rowFlag == 1) {
                        String id = TMUID.getUID();
                        temp.setId(id);
                        temp.setCreateBy(user.getId());
                        temp.setCreateTime(new Date());
                        String btnName = temp.getBtnName();
                        String pattern = "[^(\\u4e00-\\u9fa5A-Za-z)]*";
                        String btnAlias = btnName.replaceAll(pattern, "");
                        btnAlias = PinYinUtils.ToPinyin(btnAlias);
                        if (temp.getBtnType() != 1) {
                            temp.setBtnAlias(btnAlias);
                        }
                        listAdd.add(temp);
                    } else if (rowFlag == -1) {
                        temp.setTmUsed(0);
                        temp.setUpdateBy(user.getId());
                        temp.setUpdateTime(new Date());
                        listUp.add(temp);
                    } else {
                        listUp.add(temp);
                    }
                }
                boolean blnAdd = false;
                boolean blnUp = false;
                if (StringUtils.isNotEmpty(listAdd)) {
                    int row = dao.insertBatch(listAdd);
                    blnAdd = row > 0 ? true : false;
                } else {
                    blnAdd = true;
                }
                if (StringUtils.isNotEmpty(listUp)) {
                    int row = dao.updateByIdBatch(listUp);
                    blnUp = row > 0 ? true : false;
                } else {
                    blnUp = true;
                }
                if (blnAdd && blnUp) {
                    result = true;
                } else {
                    result = false;
                }
            }
        }
        return result;
    }

    /**
     * 获取数据源编辑功能中的自定义按钮数据
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    @Override
    public List<TdsEditCustomBtn> getCustomBtnData(String tdsAlias) {
        List<TdsEditCustomBtn> list = new ArrayList<TdsEditCustomBtn>();
        List<TdsEditCustomBtn> result = new ArrayList<TdsEditCustomBtn>();
        Where where = Where.create();
        where.eq(TdsEditCustomBtn::getTdsAlias, tdsAlias);
        where.eq(TdsEditCustomBtn::getTmUsed, 1);
        Order order = Order.create();
        order.orderByAsc(TdsEditCustomBtn::getTmSort);
        list = dao.queryList(TdsEditCustomBtn.class, where, order);
        HashMap<String, TdsEditCustomBtn> map = new HashMap<String, TdsEditCustomBtn>();
        if (StringUtils.isNotEmpty(list)) {
            for (TdsEditCustomBtn temp : list) {
                map.put(temp.getBtnAlias(), temp);
            }
            result.addAll(list);
        }
        List<TdsEditCustomBtn> listAdd = new ArrayList<TdsEditCustomBtn>();
        if (map == null || !map.containsKey("btnAdd")) {
            TdsEditCustomBtn beanAdd = new TdsEditCustomBtn();
            beanAdd.setBtnAlias("btnAdd");
            beanAdd.setBtnCss("");
            beanAdd.setBtnName("添加");
            beanAdd.setBtnScript("");
            beanAdd.setBtnType(1);
            beanAdd.setId(TMUID.getUID());
            beanAdd.setIsShow(1);
            beanAdd.setRowFlag(1);
            beanAdd.setShowScript("");
            beanAdd.setTdsAlias(tdsAlias);
            beanAdd.setTmSort(1);
            beanAdd.setTmUsed(1);
            listAdd.add(beanAdd);
        }
        if (map == null || !map.containsKey("btnDel")) {
            TdsEditCustomBtn beanDel = new TdsEditCustomBtn();
            beanDel.setBtnAlias("btnDel");
            beanDel.setBtnCss("");
            beanDel.setBtnName("删除");
            beanDel.setBtnScript("");
            beanDel.setBtnType(1);
            beanDel.setId(TMUID.getUID());
            beanDel.setIsShow(1);
            beanDel.setRowFlag(1);
            beanDel.setShowScript("");
            beanDel.setTdsAlias(tdsAlias);
            beanDel.setTmSort(2);
            beanDel.setTmUsed(1);
            listAdd.add(beanDel);
        }
        if (map == null || !map.containsKey("btnClear")) {
            TdsEditCustomBtn btnClear = new TdsEditCustomBtn();
            btnClear.setBtnAlias("btnClear");
            btnClear.setBtnCss("");
            btnClear.setBtnName("清除");
            btnClear.setBtnScript("");
            btnClear.setBtnType(1);
            btnClear.setId(TMUID.getUID());
            btnClear.setIsShow(0);
            btnClear.setRowFlag(1);
            btnClear.setShowScript("");
            btnClear.setTdsAlias(tdsAlias);
            btnClear.setTmSort(3);
            btnClear.setTmUsed(1);
            listAdd.add(btnClear);
        }
        if (map == null || !map.containsKey("btnSave")) {
            TdsEditCustomBtn beanSave = new TdsEditCustomBtn();
            beanSave.setBtnAlias("btnSave");
            beanSave.setBtnCss("");
            beanSave.setBtnName("保存");
            beanSave.setBtnScript("");
            beanSave.setBtnType(1);
            beanSave.setId(TMUID.getUID());
            beanSave.setIsShow(1);
            beanSave.setRowFlag(1);
            beanSave.setShowScript("");
            beanSave.setTdsAlias(tdsAlias);
            beanSave.setTmSort(4);
            beanSave.setTmUsed(1);
            listAdd.add(beanSave);
        }
        if (map == null || !map.containsKey("btnImport")) {
            TdsEditCustomBtn beanImport = new TdsEditCustomBtn();
            beanImport.setBtnAlias("btnImport");
            beanImport.setBtnCss("");
            beanImport.setBtnName("导入");
            beanImport.setBtnScript("");
            beanImport.setBtnType(1);
            beanImport.setId(TMUID.getUID());
            beanImport.setIsShow(1);
            beanImport.setRowFlag(1);
            beanImport.setShowScript("");
            beanImport.setTdsAlias(tdsAlias);
            beanImport.setTmSort(5);
            beanImport.setTmUsed(1);
            listAdd.add(beanImport);
        }
        if (map == null || !map.containsKey("btnReExtract")) {
            TdsEditCustomBtn beanImport = new TdsEditCustomBtn();
            beanImport.setBtnAlias("btnReExtract");
            beanImport.setBtnCss("");
            beanImport.setBtnName("重新提取");
            beanImport.setBtnScript("");
            beanImport.setBtnType(1);
            beanImport.setId(TMUID.getUID());
            beanImport.setIsShow(0);
            beanImport.setRowFlag(1);
            beanImport.setShowScript("");
            beanImport.setTdsAlias(tdsAlias);
            beanImport.setTmSort(6);
            beanImport.setTmUsed(1);
            listAdd.add(beanImport);
        }
        if (StringUtils.isNotEmpty(listAdd)) {
            TdsEditCustomBtnDto dto = new TdsEditCustomBtnDto();
            dto.setData(listAdd);
            dto.setTdsAlias(tdsAlias);
            saveCustomBtnData(dto);
            for (TdsEditCustomBtn temp : listAdd) {
                temp.setRowFlag(0);
                result.add(temp);
            }
        }
        return result;
    }

    /**
     * 判断是否已绑定输入输出参数
     *
     * @param targetTdsAlias    源数据源别名
     * @param bindtdsparamalias 绑定数据源别名
     * @return
     */
    @Override
    public boolean editTdsBindParamIsBind(String targettdsalias, String bindtdsalias) {
        boolean result = false;

        List<TdsEditTdsBindParam> list = getEditTdsBindParam(targettdsalias, bindtdsalias, 0);
        if (StringUtils.isNotEmpty(list)) {
            result = true;
        }
        return result;
    }

    /**
     * 获取绑定输入输出参数数据
     *
     * @param targetTdsAlias    源数据源别名
     * @param bindtdsparamalias 绑定数据源别名
     * @param bindtdsparamtype  参数类型，0：全部，1：输入参数，2：输出参数
     * @return
     */
    public List<TdsEditTdsBindParam> getEditTdsBindParam(String targettdsalias, String bindtdsalias,
                                                         int bindtdsparamtype) {
        List<TdsEditTdsBindParam> result = new ArrayList<TdsEditTdsBindParam>();
        Where where = Where.create();
        where.eq(TdsEditTdsBindParam::getTargettdsalias, targettdsalias);
        if (bindtdsparamtype != 0) {
            where.eq(TdsEditTdsBindParam::getBindtdsparamtype, bindtdsparamtype);
        }
        Order order = Order.create();
        order.orderByAsc(TdsEditTdsBindParam::getTmsort);
        result = dao.queryList(TdsEditTdsBindParam.class, where, order);
        return result;
    }

    /**
     * 获取绑定输入输出参数数据
     *
     * @param targetTdsAlias    源数据源别名
     * @param bindtdsparamalias 绑定数据源别名
     * @param bindtdsparamtype  参数类型，0：全部，1：输入参数，2：输出参数
     * @param isInitData        是否进行初始化数据操作
     * @return
     */
    @Override
    public List<TdsEditTdsBindParam> getEditTdsBindParamData(String targettdsalias, String bindtdsalias,
                                                             int bindtdsparamtype, boolean isInitData) {
        TDataSourceManager dsm = new TDataSourceManager();
        List<TdsEditTdsBindParam> result = new ArrayList<TdsEditTdsBindParam>();
        List<TdsEditTdsBindParam> list = new ArrayList<TdsEditTdsBindParam>();
        list = getEditTdsBindParam(targettdsalias, bindtdsalias, bindtdsparamtype);
        HashMap<String, TdsEditTdsBindParam> mapParam = new HashMap<String, TdsEditTdsBindParam>();
        if (StringUtils.isNotEmpty(list)) {// 已有绑定的数据
            for (TdsEditTdsBindParam temp : list) {
                mapParam.put(temp.getTargettdsparamalias() + "_" + temp.getBindtdsparamtype().toString(), temp);
            }
        }
        if (bindtdsparamtype == 1 || bindtdsparamtype == 0) {
            List<TdsinPara> listTargetInPara = dsm.getInParaList(targettdsalias);// getTDSInPara(targettdsalias);
            HashMap<String, TdsinPara> mapTargetIn = new HashMap<String, TdsinPara>();
            if (StringUtils.isNotEmpty(listTargetInPara)) {
                for (TdsinPara temp : listTargetInPara) {
                    mapTargetIn.put(temp.getParaAlias(), temp);
                }
            }
            List<TdsinPara> listBindInPara = dsm.getInParaList(bindtdsalias);
            HashMap<String, TdsinPara> mapBindIn = new HashMap<String, TdsinPara>();
            if (StringUtils.isNotEmpty(listBindInPara)) {
                for (TdsinPara temp : listBindInPara) {
                    mapBindIn.put(temp.getParaAlias(), temp);
                }
            }
            if (StringUtils.isNotEmpty(listTargetInPara)) {
                for (TdsinPara temp : listTargetInPara) {
                    String paraAlias = temp.getParaAlias();
                    TdsEditTdsBindParam bean = new TdsEditTdsBindParam();
                    bean.setTargettdsalias(targettdsalias);
                    bean.setTargettdsparamalias(temp.getParaAlias());
                    bean.setTargettdsparamname(temp.getParaName() + "（" + temp.getParaAlias() + "）");
                    bean.setBindtdsparamtype(1);
                    if (mapParam != null) {// 做过绑定操作
                        if (mapParam.containsKey(paraAlias + "_1")) {// 获取到了绑定的参数别名
                            if (mapBindIn != null) {// 获取到了绑定数据源的输入参数
                                if (mapBindIn.containsKey(paraAlias)) {// 绑定数据源的输入参数存在
                                    bean.setBindtdsalias(bindtdsalias);
                                    bean.setBindtdsparamalias(mapBindIn.get(paraAlias).getParaAlias());
                                    bean.setBindtdsparamname(mapBindIn.get(paraAlias).getParaName() + "（"
                                            + mapBindIn.get(paraAlias).getParaAlias() + "）");
                                } else {// 绑定数据源的输入参数被删除
                                    bean.setBindtdsalias(mapParam.get(paraAlias + "_1").getBindtdsalias());
                                    bean.setBindtdsparamalias(mapParam.get(paraAlias + "_1").getBindtdsparamalias());
                                    bean.setBindtdsparamname("参数被删除");
                                    bean.setIsDel(1);
                                }
                            } else {// 绑定数据源的输入参数被删除
                                bean.setBindtdsalias(mapParam.get(paraAlias + "_1").getBindtdsalias());
                                bean.setBindtdsparamalias(mapParam.get(paraAlias + "_1").getBindtdsparamalias());
                                bean.setBindtdsparamname("参数被删除");
                                bean.setIsDel(1);
                            }
                        } else {
                            if (mapBindIn != null) {// 获取到了绑定数据源的输入参数
                                if (mapBindIn.containsKey(paraAlias)) {// 绑定数据源的输入参数存在
                                    bean.setBindtdsalias(bindtdsalias);
                                    bean.setBindtdsparamalias(mapBindIn.get(paraAlias).getParaAlias());
                                    bean.setBindtdsparamname(mapBindIn.get(paraAlias).getParaName() + "（"
                                            + mapBindIn.get(paraAlias).getParaAlias() + "）");
                                }
                            }
                        }
                    } else {
                        if (mapBindIn != null) {// 获取到了绑定数据源的输入参数
                            if (mapBindIn.containsKey(paraAlias)) {// 绑定数据源的输入参数存在
                                bean.setBindtdsalias(bindtdsalias);
                                bean.setBindtdsparamalias(mapBindIn.get(paraAlias).getParaAlias());
                                bean.setBindtdsparamname(mapBindIn.get(paraAlias).getParaName() + "（"
                                        + mapBindIn.get(paraAlias).getParaAlias() + "）");
                            }
                        }
                    }
                    result.add(bean);
                }
            }
        }
        if (bindtdsparamtype == 2 || bindtdsparamtype == 0) {
            Map<String, TableColumnInfo> columnMap = this.getTdsEditTableColumnMap(targettdsalias);
            List<TOutPara> listTargetOutPara = dsm.TdsoutParaToOutParaList(null, getTDSOutPara(targettdsalias)); // ;
            HashMap<String, TOutPara> mapTargetOut = new HashMap<String, TOutPara>();
            if (StringUtils.isNotEmpty(listTargetOutPara)) {
                for (TOutPara temp : listTargetOutPara) {
                    mapTargetOut.put(temp.getAlias(), temp);
                }
            }
            List<TOutPara> listBindOutPara = dsm.TdsoutParaToOutParaList(null, getTDSOutPara(bindtdsalias)); // ;getTDSOutPara(bindtdsalias);
            if (StringUtils.isEmpty(listBindOutPara)) {
                listBindOutPara = TdsTools.getOutParaList(bindtdsalias);// 获取dataset 模式输出参数
            }
            HashMap<String, TOutPara> mapBindOut = new HashMap<String, TOutPara>();
            if (StringUtils.isNotEmpty(listBindOutPara)) {
                for (TOutPara temp : listBindOutPara) {
                    mapBindOut.put(temp.getAlias(), temp);
                }
            }
            if (StringUtils.isNotEmpty(listTargetOutPara)) {
                for (TOutPara temp : listTargetOutPara) {
                    String paraAlias = temp.getAlias();
                    TdsEditTdsBindParam bean = new TdsEditTdsBindParam();
                    bean.setTargettdsalias(targettdsalias);
                    bean.setTargettdsparamalias(temp.getAlias());
                    bean.setTargettdsparamname(temp.getName() + "（" + temp.getAlias() + "）");
                    bean.setBindtdsparamtype(2);
                    if (mapParam != null) {// 做过绑定操作
                        if (mapParam.containsKey(paraAlias + "_2")) {// 获取到了绑定的参数别名
                            TdsEditTdsBindParam bindBean = mapParam.get(paraAlias + "_2");
                            if (bindBean.getIsInitData() == null) {
                                if (this.isExitTableColumnName(columnMap, bean.getTargettdsparamalias())) {
                                    // if (isExtTableColumnNameByTds(targettdsalias, bean.getTargettdsparamalias()))
                                    // {
//									bean.setIsInitData(1);
                                    bean.setIsInitData(bindBean.getIsInitData());
                                } else {
                                    bean.setIsInitData(0);
                                }
                            } else {
                                bean.setIsInitData(bindBean.getIsInitData());
                            }
                            if (bindBean.getBindtdsparamalias() != null
                                    && bindBean.getBindtdsparamalias().length() > 0) {
                                bean.setBindtdsalias(bindtdsalias);
                                bean.setBindtdsparamalias(bindBean.getBindtdsparamalias());
                                bean.setBindtdsparamname(bindBean.getBindtdsparamname());
                            }
//							if (mapBindOut != null) {// 获取到了绑定数据源的输入参数
//								if (mapBindOut.containsKey(paraAlias)) {// 绑定数据源的输入参数存在
//									bean.setBindtdsalias(bindtdsalias);
//									bean.setBindtdsparamalias(mapBindOut.get(paraAlias).getAlias());
//									bean.setBindtdsparamname(mapBindOut.get(paraAlias).getName() + "（"
//											+ mapBindOut.get(paraAlias).getAlias() + "）");
//								}
//							}
                            bean.setIsSaveOld(mapParam.get(paraAlias + "_2").getIsSaveOld());
                            bean.setIsKey(mapParam.get(paraAlias + "_2").getIsKey());
                        } else {
//							if (this.isExitTableColumnName(columnMap, bean.getTargettdsparamalias())) {
//								// if (isExtTableColumnNameByTds(targettdsalias, bean.getTargettdsparamalias()))
//								// {
//								bean.setIsInitData(1);
//							} else {
                            bean.setIsInitData(0);
//							}
                            if (mapBindOut != null) {// 获取到了绑定数据源的输入参数
                                if (mapBindOut.containsKey(paraAlias)) {// 绑定数据源的输入参数存在
                                    bean.setBindtdsalias(bindtdsalias);
                                    bean.setBindtdsparamalias(mapBindOut.get(paraAlias).getAlias());
                                    bean.setBindtdsparamname(mapBindOut.get(paraAlias).getName() + "（"
                                            + mapBindOut.get(paraAlias).getAlias() + "）");
                                }
                            }
                            bean.setIsSaveOld(0);
                        }
                    } else {
                        bean.setIsInitData(1);
                        if (mapBindOut != null) {// 获取到了绑定数据源的输入参数
                            if (mapBindOut.containsKey(paraAlias)) {// 绑定数据源的输入参数存在
                                bean.setBindtdsalias(bindtdsalias);
                                bean.setBindtdsparamalias(mapBindOut.get(paraAlias).getAlias());
                                bean.setBindtdsparamname(mapBindOut.get(paraAlias).getName() + "（"
                                        + mapBindOut.get(paraAlias).getAlias() + "）");
                            }
                        }
                        bean.setIsSaveOld(0);
                    }
                    result.add(bean);
                }
            }
        }
        return result;
    }

    /**
     * 清除绑定的数据源输入输出参数
     *
     * @param targetTdsAlias    源数据源别名
     * @param bindtdsparamalias 绑定数据源别名
     * @return
     */
    @Override
    public boolean clearEditTdsBindParam(String targettdsalias, String bindtdsalias) {
        boolean result = false;
        List<TdsEditTdsBindParam> list = getEditTdsBindParam(targettdsalias, bindtdsalias, 0);
        if (StringUtils.isNotEmpty(list)) {
            int row = dao.deleteByIdBatch(list);
            if (row > 0) {
                result = true;
            }
        }
        return result;
    }

    /**
     * 保存可编辑数据源绑定输入输出参数
     *
     * @param dto
     * @return
     */
    @Override
    public boolean saveEditTdsBindParam(TdsEditBindParamDto dto) {
        boolean result = true;
        if (dto != null) {
            List<TdsEditTdsBindParam> list = dto.getData();
            if (StringUtils.isNotEmpty(list)) {
                SysUser user = SysUserHolder.getCurrentUser();
                List<TdsEditTdsBindParam> listAdd = new ArrayList<TdsEditTdsBindParam>();
//				clearEditTdsBindParam(dto.getTargetTdsAlias(), dto.getBindTdsAlias());
//				 List<TdsEditTdsBindParam> listDgetEditTdsBindParam
                List<TdsEditTdsBindParam> listDel = new ArrayList<TdsEditTdsBindParam>();
                Where where = Where.create();
                where.eq(TdsEditTdsBindParam::getTargettdsalias, dto.getTargetTdsAlias());
                int bindtdsparamtype = list.get(0).getBindtdsparamtype();
                where.eq(TdsEditTdsBindParam::getBindtdsparamtype, bindtdsparamtype);
                listDel = dao.queryList(TdsEditTdsBindParam.class, where, null);
                dao.deleteByIdBatch(listDel);
                for (TdsEditTdsBindParam temp : list) {
                    String id = TMUID.getUID();
                    temp.setId(id);
                    temp.setCreateBy(user.getId());
                    temp.setCreateTime(new Date());
                    listAdd.add(temp);
                }
                if (StringUtils.isNotEmpty(listAdd)) {
                    int row = dao.insertBatch(listAdd);
                    result = row > 0 ? true : false;
                } else {
                    result = true;
                }
            }
        }
        return result;
    }

    /**
     * 生成数据源输出参数
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public List<TdsinPara> getListTdsInPara(String tdsAlias) {
        try {
            if (tdsAlias.indexOf("{") >= 0) {
                JSONObject jsonObj = JSONObject.parseObject(tdsAlias);
                tdsAlias = jsonObj.getString("tdsAlias");
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return this.getTDSInPara(tdsAlias);

    }

    /**
     * 获取数据集模式的排序信息
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    @Override
    public List<TdsDatasetSort> getTdsDatasetSort(String tdsAlias) {
        Where where = Where.create();
        where.eq(TdsDatasetSort::getTdsAlias, tdsAlias);
        Order order = Order.create();
        order.orderByAsc(TdsDatasetSort::getTmSort);
        return dao.queryList(TdsDatasetSort.class, where, order);
    }

    /**
     * 获取脚本 通过数据源别名
     *
     * @param tdsAlias
     * @return
     * <AUTHOR>
     * @params 数据源别名
     */
    @Override
    public TdsScriptEntity getScriptByTds(String tdsAlias) {
        // 通过数据源别名查询脚本
        Where where = Where.create();
        where.eq(TdsScriptEntity::getTdsAlias, tdsAlias);
        List<TdsScriptEntity> list = dao.rawQueryListByWhere(TdsScriptEntity.class, where);
        if (StringUtils.isNotEmpty(list)) {
            return list.get(0);
        } else {
            TdsScriptEntity tdsScriptEntity = new TdsScriptEntity();
            tdsScriptEntity.setTdsAlias(tdsAlias);
            return tdsScriptEntity;
        }
    }

    /**
     * 保存数据源别名
     *
     * @param tdsScriptEntity
     * @return
     * <AUTHOR>
     * @params 数据源别名
     */
    @Override
    public Boolean saveScriptByTds(TdsScriptEntity tdsScriptEntity) {
        // 为保证表格数据冗余 清理脚本为空的数据项
        Where where1 = Where.create();
        where1.isEmpty(TdsScriptEntity::getTdsCalScript);
        where1.isEmpty(TdsScriptEntity::getTdsAfterEditScript);
        where1.isEmpty(TdsScriptEntity::getTdsCanEditScript);
        where1.isEmpty(TdsScriptEntity::getTdsInitSql);
        where1.isEmpty(TdsScriptEntity::getTdsCellclickFun);
        dao.rawDeleteByWhere(TdsScriptEntity.class, where1);
        int msg = 0;
        // 保存数据源脚本
        Where where = Where.create();
        where.eq(TdsScriptEntity::getTdsAlias, tdsScriptEntity.getTdsAlias());
        List<TdsScriptEntity> list = dao.rawQueryListByWhere(TdsScriptEntity.class, where);
        if (StringUtils.isNotEmpty(list)) {
            // 已存在数据源脚本 更新保存
            tdsScriptEntity.setId(list.get(0).getId());
            msg = dao.updateById(tdsScriptEntity);

        } else {
            // 不存在数据源脚本
            tdsScriptEntity.setId(TMUID.getUID());
            msg = dao.insert(tdsScriptEntity);
        }
        return msg > 0;
    }

    /**
     * 获取能外部访问的数据源列表
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<TdataSource> getOutTds() {
        Where where = Where.create();
        where.eq(TdataSource::getUsed, 1);
        where.eq(TdataSource::getIsOutTds, 0);
        return dao.rawQueryListByWhere(TdataSource.class, where);
    }

    /**
     * 获取数据源数据
     */
    @Override
    public IDataSource getTDSData(String tdsAlias, JSONArray queryData) {
        TDataSourceManager dsm = new TDataSourceManager();
        IDataSource ids = dsm.getDataSource(tdsAlias, this.getInparaMap(queryData));
        if (ids != null) {
            if (!ids.getAutoLoad()) {
                ids.load();
            }
        }
        return ids;
    }

    /**
     * 获得检索条件map
     *
     * @param queryData
     * @return
     */
    private Map<String, String> getInparaMap(JSONArray queryData) {
        Map<String, String> inparaMap = new HashMap<String, String>();
        if (queryData != null) {
            for (int i = 0; i < queryData.size(); i++) {
                JSONObject obj = queryData.getJSONObject(i);
                inparaMap.put(obj.getString("name"), obj.getString("value"));
            }
        }
        return inparaMap;
    }

    /**
     * 获取数据源数据(数据格式为 List<Map<String,Object>>)
     *
     * @param tdsAlias        数据源别名
     * @param queryData       检索条
     * @param showRenderValue true:返回渲染值;false:返回原始值
     * @return
     */
    @Override
    public IDataSource getTdsDataList(String tdsAlias, JSONArray queryData, boolean showRenderValue) {
        TDataSourceManager dsm = new TDataSourceManager();
        IDataSource ids = dsm.getDataSource(tdsAlias, this.getInparaMap(queryData));
        if (ids != null) {
            ids.setShowRenderValue(showRenderValue);
            ids.setLoadDataList(true);// 只加载list数据
            if (!ids.getAutoLoad()) {
                ids.load();
            }
        }
        return ids;
    }

    /**
     * 保存排序信息
     *
     * @param tdsAlias
     * @param sortDataList
     * @return
     */
    @Override
    public Boolean savaSortData(String tdsAlias, List<TdsSortMoreDataVo> sortDataList) {
        try {
            Where where = Where.create();
            where.eq(TdsSortData::getTdsAlias, tdsAlias);
            List<TdsSortData> insertList = new ArrayList<TdsSortData>();
            if (StringUtils.isNotEmpty(sortDataList)) {
                int index = 0;
                for (TdsSortMoreDataVo e : sortDataList) {
                    if (e.getIsSort() != null && e.getIsSort().intValue() == 1) {
                        index++;
                        TdsSortData bean = new TdsSortData();
                        ObjUtils.copyTo(e, bean);
                        bean.setId(TMUID.getUID());
                        bean.setTdsAlias(tdsAlias);
                        if (bean.getSortType() == null) {
                            bean.setSortType(1);// 1:升序；0：降序
                        }
                        bean.setTmSort(index);
                        insertList.add(bean);
                    }
                }
            }
            dao.rawDeleteByWhere(TdsSortData.class, where);
            if (StringUtils.isNotEmpty(insertList)) {
                dao.insertBatch(insertList);
            }
        } catch (Exception e) {
            log.error("", e);
            return false;
        }
        return true;
    }

    /**
     * 获得数据源排序信息
     *
     * @param tdsAlias
     * @return
     */
    @Override
    public List<TdsSortData> getSortData(String tdsAlias) {
        try {
            Where where = Where.create();
            where.eq(TdsSortData::getTdsAlias, tdsAlias);
            Order order = Order.create();
            order.orderByAsc(TdsSortData::getTmSort);
            return dao.queryList(TdsSortData.class, where, order);
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }

    /**
     * 获得排序信息 for 前台
     */
    @Override
    public List<TdsSortMoreDataVo> getSortDataVo(String tdsAlias) {
        List<TdsSortMoreDataVo> rtnList = new ArrayList<TdsSortMoreDataVo>();
        TDataSourceManager dsm = new TDataSourceManager();
        List<TdsoutPara> outList = dsm.getOutParaList(tdsAlias);
        if (StringUtils.isNotEmpty(outList)) {
            List<TdsSortData> sortList = getSortData(tdsAlias);
            LinkedHashMap<String, TdsoutPara> opMap = new LinkedHashMap<String, TdsoutPara>();
            for (TdsoutPara op : outList) {
                opMap.put(op.getParaAlias(), op);
            }
            int sort = 0;
            List<String> paramList = new ArrayList<String>();
            if (StringUtils.isNotEmpty(sortList)) {
                for (TdsSortData sd : sortList) {
                    TdsoutPara op = opMap.get(sd.getParamAlias());
                    if (op != null) {
                        TdsSortMoreDataVo vo = new TdsSortMoreDataVo();
                        sort++;
                        vo.setIsSort(1);
                        vo.setParamAlias(op.getParaAlias());
                        vo.setParamName(op.getParaName());
                        vo.setSortType(sd.getSortType());
                        vo.setTmSort(sort);
                        paramList.add(vo.getParamAlias());
                        rtnList.add(vo);
                    }
                }
            }
            for (String alias : opMap.keySet()) {
                if (!paramList.contains(alias)) {
                    TdsoutPara op = opMap.get(alias);
                    sort++;
                    TdsSortMoreDataVo vo = new TdsSortMoreDataVo();
                    vo.setIsSort(0);
                    vo.setParamAlias(alias);
                    vo.setParamName(op.getParaName());
                    vo.setSortType(1);
                    vo.setTmSort(sort);
                    rtnList.add(vo);
                }
            }
        }
        return rtnList;
    }

    @Override
    public List<List<TdsAccountOutparamVo>> getExportTdsData(TdsExportDto param) {
        // TDataSourceManager dsm = new TDataSourceManager();
        // TdataSource ds = dsm.getTdataSource(param.getTdsAlias());
        JSONArray inpara = new JSONArray();
        String queryJson = param.getQueryData();
        if (StringUtils.isEmpty(queryJson)) {
            queryJson = param.getInParaAlias();// param.getQueryData();
            if (queryJson.indexOf("[") >= 0 && queryJson.indexOf("]") >= 0) {
                inpara = JSONArray.parseArray(queryJson);
            } else {
                String[] objs = queryJson.split("\\|");
                for (String string : objs) {
                    String[] vals = string.split("=");
                    JSONObject o = new JSONObject();
                    String k = vals[0];
                    String v = vals.length > 1 ? vals[1] : "";
                    o.put("name", k);
                    o.put("value", v);
                    inpara.add(o);
                }
            }
        } else {
            inpara = JSONArray.parseArray(queryJson);
        }
        IDataSource tds = getTdsDataList(param.getTdsAlias(), inpara, true);

        JSONArray tdsData = tds.getJsonWithoutData();
        List<Map<String, Object>> datas = tds.getDataList();
//		Boolean addIndex = true;

        JSONObject _tds = tdsData.getJSONObject(0);
        int headerLevelCount = ExcelExport.tdsSpanHeaderLevel(_tds); // 合并表头层级

        // System.out.println("titleJson");
        // System.out.println(_tds.toJSONString());
        /*
         * cols一个元素 "timeMarkCol": { "hidden": false, "dataType": "tdsString", "isKey":
         * "false", "dataStatusFlag": 0, "isSpanCol": "false", "isExportRender": 0,
         * "align": "center", "rendererFun": "", "isGroupCol": "false", "isShowAsNum":
         * 0, "required": false, "isSumCol": "", "overtip": true, "minwidth": "",
         * "showPlaceholder": false, "width": "120", "alias": "timeMarkCol",//列名
         * "header": "核算对象_名称_位号_范围",//显示内容 "reportFormula": "" }
         */
//		JSONArray cols = ExcelExport.getTdsCols(_tds); // 获取列信息，相当于排序
        JSONArray cols = null;

        if (headerLevelCount > 1) {
            cols = new JSONArray();
            JSONArray spanCols = _tds.getJSONObject("props").getJSONArray("colspan");
            for (int i = 0, il = spanCols.size(); i < il; i++) {
                JSONObject colObj = spanCols.getJSONObject(i);
                String hidden = colObj.getString("hidden");
                if ("true".equals(hidden)) {
                    continue;
                }
//				String header = colObj.getString("header");
                getColArr(cols, colObj, null);
            }
        } else {
            cols = ExcelExport.getTdsCols(_tds); // 获取列信息，相当于排序
        }

        List<List<TdsAccountOutparamVo>> outList = new ArrayList<List<TdsAccountOutparamVo>>();// 全部输出内容

        int row = 0;// , col = 0;
        // 整理表头信息
        List<TdsAccountOutparamVo> titleList = new ArrayList<TdsAccountOutparamVo>();
        for (int i = 0, il = cols.size(); i < il; i++) {
            JSONObject colObj = cols.getJSONObject(i);
            String hidden = colObj.getString("hidden");
            if ("true".equals(hidden)) {
                continue;
            }
            String header = colObj.getString("header");
            String alias = colObj.getString("alias");
            String align = colObj.getString("align");
            Integer width = colObj.getInteger("width");
            TdsAccountOutparamVo vo = new TdsAccountOutparamVo();

            vo.setAlias(alias);
            vo.setShowName(header);
            vo.setWidth(width);
            vo.setAlign(align);

            List<String> tlist = Coms.StrToList(vo.getShowName(), "_");
            vo.setTlist(tlist);

            titleList.add(vo);
        }

        List<List<Integer>> mergeList = new ArrayList<List<Integer>>();// 合并信息记录

        if (headerLevelCount > 1) {
            // 处理列合并输出信息
            int rowno = 0, colCount = titleList.size();// colno = 0,
            for (rowno = 0; rowno < headerLevelCount; rowno++) {// 循环行层数

                List<TdsAccountOutparamVo> rowList = new ArrayList<TdsAccountOutparamVo>();

                for (int i = 0; i < colCount; i++) {// 循环列
                    TdsAccountOutparamVo obj = titleList.get(i);
                    List<String> tlist = obj.getTlist();
                    if ("timeMarkCol".equals(obj.getAlias())) {// 时间列无合并
                        TdsAccountOutparamVo out = ObjUtils.copyTo(obj, TdsAccountOutparamVo.class);
                        String val = tlist.size() > rowno ? tlist.get(rowno) : "时间";
                        out.setVal(val);
                        out.setIsTitle(true);
                        out.setRows(1);
                        out.setCols(1);
                        out.setRowPos(rowno);
                        out.setColPos(i);
                        rowList.add(out);
                    } else if ("rowConfirm".equals(obj.getAlias())) {// 确认列合并
                        if (rowno == 0) {
                            TdsAccountOutparamVo out = ObjUtils.copyTo(obj, TdsAccountOutparamVo.class);
                            String val = obj.getShowName();
                            out.setVal(val);
                            out.setIsTitle(true);
                            out.setRows(headerLevelCount);
                            out.setCols(1);
                            out.setRowPos(rowno);
                            out.setColPos(i);
                            rowList.add(out);

                            List<Integer> _list = new ArrayList<Integer>();// srow, erow, scol, ecol
                            _list.add(rowno);
                            _list.add(headerLevelCount - 1);
                            _list.add(i);
                            _list.add(i);
                            mergeList.add(_list);
                        }
                    } else {// 仪表列(普通列)
                        Boolean inCombine = false;
                        for (List<Integer> hlist : mergeList) {
                            int srow = hlist.get(0);
                            int erow = hlist.get(1);
                            int scol = hlist.get(2);
                            int ecol = hlist.get(3);
                            if (rowno >= srow && rowno <= erow && i >= scol && i <= ecol) {
                                inCombine = true;
                                break;
                            }
                        }

                        if (inCombine) {
                            continue;
                        }

                        // 先判断列合并，无列合并，再判断行合并（下面是空字符），进入单元格判断时，判断是否在合并区域，如果在，直接跳过
                        int colnum = 1, rownum = 1;

                        String val = tlist.size() > rowno ? tlist.get(rowno) : "";// 本单元格值
                        if (tlist.size() == 1) {
                            TdsAccountOutparamVo out = ObjUtils.copyTo(obj, TdsAccountOutparamVo.class);
                            out.setVal(val);
                            out.setIsTitle(true);
                            out.setRows(headerLevelCount);
                            out.setCols(1);
                            out.setRowPos(rowno);
                            out.setColPos(i);
                            rowList.add(out);

                            List<Integer> _list = new ArrayList<Integer>();// srow, erow, scol, ecol
                            _list.add(rowno);
                            _list.add(headerLevelCount - 1);
                            _list.add(i);
                            _list.add(i);
                            mergeList.add(_list);
                        } else if (tlist.size() == rowno + 1 && tlist.size() < headerLevelCount) {// 如果列层数结束且小于整体层数，向下合并
                            TdsAccountOutparamVo out = ObjUtils.copyTo(obj, TdsAccountOutparamVo.class);
                            out.setVal(val);
                            out.setIsTitle(true);
                            out.setRows(headerLevelCount - rowno);
                            out.setCols(1);
                            out.setRowPos(rowno);
                            out.setColPos(i);
                            rowList.add(out);

                            List<Integer> _list = new ArrayList<Integer>();// srow, erow, scol, ecol
                            _list.add(rowno);
                            _list.add(headerLevelCount - 1);
                            _list.add(i);
                            _list.add(i);
                            mergeList.add(_list);
                        } else {

                            if (StringUtils.isNotEmpty(val) && !"/".equals(val)) {// 内容为空，不进行合并判断处理，单独显示
                                for (int j = i + 1; j < colCount; j++) {
                                    TdsAccountOutparamVo nextobj = titleList.get(j);
                                    if ("timeMarkCol".equals(nextobj.getAlias())
                                            || "rowConfirm".equals(nextobj.getAlias())) {// 时间、确认列直接终止
                                        break;
                                    }

                                    List<String> tlist2 = nextobj.getTlist();
                                    String nextval = tlist2 == null || tlist2.size() <= rowno
                                            || tlist2.get(rowno) == null ? "" : tlist2.get(rowno);
                                    if (val.equals(nextval) && StringUtils.isNotEmpty(nextval)) {
                                        colnum++;
                                    } else {
                                        break;
                                    }
                                }

                                if (colnum == 1 && rowno + 1 < headerLevelCount) {// 无列合并，判断并处理行
                                    for (int nextrowno = rowno + 1; nextrowno < headerLevelCount; nextrowno++) {// 循环行层数
                                        String nextval = tlist.size() > nextrowno ? tlist.get(nextrowno) : "";// 本单元格下行值
                                        if ("".equals(nextval)) {
                                            rownum++;
                                        } else {
                                            break;
                                        }
                                    }
                                }
                            }

                            if (rownum > 1 || colnum > 1) {
                                List<Integer> _list = new ArrayList<Integer>();// srow, erow, scol, ecol
                                _list.add(rowno);
                                _list.add(rowno + rownum - 1);
                                _list.add(i);
                                _list.add(i + colnum - 1);
                                mergeList.add(_list);
                            }

                            TdsAccountOutparamVo out = ObjUtils.copyTo(obj, TdsAccountOutparamVo.class);
                            out.setVal(val);
                            out.setIsTitle(true);
                            out.setRows(rownum);
                            out.setCols(colnum);
                            out.setRowPos(rowno);
                            out.setColPos(i);
                            rowList.add(out);
                        }

                    }
                }
                row++;

                outList.add(rowList);
            }
        } else {// 单层表头直接输出行
            List<TdsAccountOutparamVo> rowList = new ArrayList<TdsAccountOutparamVo>();
            int j = 0;
            for (TdsAccountOutparamVo obj : titleList) {
                TdsAccountOutparamVo out = ObjUtils.copyTo(obj, TdsAccountOutparamVo.class);
                out.setVal(obj.getShowName());
                out.setIsTitle(true);
                out.setRows(1);
                out.setCols(1);
                out.setRowPos(row);
                out.setColPos(j++);

                rowList.add(out);
            }
            row++;

            outList.add(rowList);
        }

        // 数据输出
        for (Map<String, Object> map : datas) {// 循环行数据
            // String id = String.valueOf(map.get("ID"));// 时间字符串
            String accountInfo = String.valueOf(map.get("_accountULInfoCol"));// 台账上下限及备注字符串

            Map<String, String> markMap = new HashMap<String, String>();// 备注信息map
            List<String> overList = new ArrayList<String>();// 超限list
            if (StringUtils.isNotEmpty(accountInfo) && !"null".equalsIgnoreCase(accountInfo)) {
                try {
                    JSONObject jobj = JSONObject.parseObject(accountInfo);
                    if (jobj != null && jobj.containsKey("data")) {
                        JSONObject dataobj = jobj.getJSONObject("data");
                        if (dataobj != null) {
                            for (Map.Entry<String, Object> _map : dataobj.entrySet()) {
                                String key = _map.getKey();
                                JSONObject vobj = dataobj.getJSONObject(key);
                                String mark = vobj.getString("info");
                                String overmark = vobj.getString("overmark");
                                if ("true".equals(overmark)) {
                                    overList.add(key);
                                }
                                if (StringUtils.isNotEmpty(mark)) {
                                    markMap.put(key, mark);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    System.out.println("台账数据源表单导出，附加信息解析失败" + e.getMessage());
                }
            }

            List<TdsAccountOutparamVo> rowList = new ArrayList<TdsAccountOutparamVo>();

            for (int j = 0, jl = titleList.size(); j < jl; j++) {// 对照表头列，按顺序输出，暂不处理数据合并
                TdsAccountOutparamVo title = titleList.get(j);
                String alias = title.getAlias();

                String val = map.get(alias) == null ? "" : String.valueOf(map.get(alias));

                TdsAccountOutparamVo out = ObjUtils.copyTo(title, TdsAccountOutparamVo.class);
                out.setVal(val);
                out.setIsTitle(true);
                out.setRows(1);
                out.setCols(1);
                out.setRowPos(row);
                out.setColPos(j);
                out.setIsTitle(false);

                if (overList.contains(alias)) {
                    out.setOverMark(true);
                }
                if (markMap.containsKey(alias)) {
                    out.setMarkInfo(markMap.get(alias));
                }

                rowList.add(out);
            }

            outList.add(rowList);

            row++;
        }

        return outList;
    }

    private void getColArr(JSONArray cols, JSONObject colObj, String header) {
        if (colObj.get("children") != null && colObj.getJSONArray("children").size() > 0) {
            JSONArray arr = colObj.getJSONArray("children");
            for (int i = 0, il = arr.size(); i < il; i++) {
                JSONObject obj = arr.getJSONObject(i);
                String _header = colObj.getString("header");
                String newHeader = header == null ? _header : header + "_" + _header;
                getColArr(cols, obj, newHeader);
            }
        } else {
            String _header = colObj.getString("header");
            String newHeader = header == null ? _header : header + "_" + _header;
            colObj.put("header", newHeader);
            cols.add(colObj);
        }
    }

    @Override
    public String tdsSqlDDl(String tdsAlias) {
        TdataSource tds = this.getTDataSource(tdsAlias);
        if (!"TDSSQL".equalsIgnoreCase(tds.getTdsclassName()) || tds.getAllowToSave() == null
                || tds.getAllowToSave() != 1) {
            // 只有sql类数据源，且可编辑的才自动维护表结构
            return "只有TDSSQL类型且可编辑的数据源才允许自动维护表结构";
        }
        String dbTableName = tds.getDbTableName();
        if (StringUtils.isEmpty(dbTableName)) {
            // 没有绑定表，无需维护
            return "没有绑定表，无需维护";
        }
        List<TdsoutPara> outParaList = this.getTDSOutPara(tdsAlias);
        if (StringUtils.isEmpty(outParaList)) {
            // 没有输出参数，无需维护
            return "没有输出参数，无需维护";
        }

        List<SqlDDLColumnInfo> columnList = getSqlDDLColumnInfos(outParaList);
        // 新增字段
        ddlService.tableDDLHandle(dbTableName, columnList, true);

        // 查询数据库中的字段
        Map<String, TableColumnInfo> oldColMap = new HashMap<>();
        List<TableColumnInfo> listTableCol = dao.getTableColumnList(dbTableName);
        if (StringUtils.isNotEmpty(listTableCol)) {
            for (TableColumnInfo temp : listTableCol) {
                oldColMap.put(temp.getColumnName().toUpperCase(), temp);
            }
        }

        // 检测是否有类型或长度不匹配的列，给前台提示
        List<String> msgList = new ArrayList<>();
        String databaseType = dao.getDatabaseType();
        for (SqlDDLColumnInfo col : columnList) {
            if ("ID".equalsIgnoreCase(col.getColumnName())) {
                // id列不检测
                continue;
            }
            TableColumnInfo tableColumnInfo = oldColMap.get(col.getColumnName().toUpperCase());
            if (tableColumnInfo == null) {
                // 找不到数据库中的列
                continue;
            }
            String dbColumnType = col.getDbColumnType();
            int dbColumnLength = col.getDbColumnLength();
            int dbColumnPrecision = col.getDbColumnPrecision();
            String newColumnType = SqlDDLService.colTypeMap.get(databaseType).get(dbColumnType);
            String columnType = newColumnType;
            if (newColumnType.contains("(?)")) {
                columnType = newColumnType.replace("?", String.valueOf(dbColumnLength));
            } else if (newColumnType.contains("(?,?)")) {
                columnType = newColumnType.replace("(?,?)", "(" + dbColumnLength + "," + dbColumnPrecision + ")");
            }

            String ntype = newColumnType.replaceAll("\\(.*?\\)", "").trim();
            String dataType = tableColumnInfo.getDataType();
            Long charMaxlength = tableColumnInfo.getCharMaxlength();
            if (!ntype.equals(dataType) || (dataType.contains("varchar") && columnType.contains("varchar")
                    && charMaxlength != dbColumnLength)) {
                // 类型或长度不一样
//				msgList.add("字段【"+col.getColumnName()+"】类型或长度不一致，请到数据库手动维护");
                msgList.add("【" + col.getColumnName() + "】");
            }
        }

        String res = StringUtils.isEmpty(msgList) ? ""
                : "参数别名" + StringUtils.join(msgList, "") + "对应的字段类型或长度不一致，请到数据库手动维护";
        return res;
    }


    /**
     * 输出参数变成数据库字段
     *
     * @param outParaList
     * @return
     */
    private static List<SqlDDLColumnInfo> getSqlDDLColumnInfos(List<TdsoutPara> outParaList) {
        List<SqlDDLColumnInfo> columnList = new ArrayList<>();
        for (TdsoutPara out : outParaList) {
            String paraAlias = out.getParaAlias();
            String dataType = out.getDataType();
            Long maxlength = out.getMaxlength();
            long columnLength;
            String columnType;
            int columnPrecision = 0;
            if ("tdsString".equalsIgnoreCase(dataType)) {
                columnType = "string";
                columnLength = maxlength == null ? 255 : maxlength * 2;
            } else if ("tdsDouble".equalsIgnoreCase(dataType)) {
                columnType = "double";
                columnLength = 18;
                columnPrecision = 6;
            } else {
                columnType = "string";
                columnLength = maxlength == null ? 255 : maxlength * 2;
            }
            SqlDDLColumnInfo col = new SqlDDLColumnInfo();
            col.setColumnName(paraAlias);
            col.setDbColumnType(columnType);
            col.setDbColumnLength((int) columnLength);
            col.setDbColumnPrecision(columnPrecision);
            columnList.add(col);
        }
        return columnList;
    }

    /**
     * 获得导出Excel参数
     *
     * @param param
     * @return
     */
    @Override
    public List<TDSExportParams> getExprotExcelParams(TdsExportDto param) {
        List<TDSExportParams> paramsList = new ArrayList<>();
        if (param.getIsLoadPrinter() != null && param.getIsLoadPrinter()) {// 导出excel读打印配置信息
            String moduleCode = "system.tds";
            String code = param.getTdsAlias();
            // 打印报表信息初始化
            Map<String, Object> reportConfig = param.getReportConfig();
            if (reportConfig != null) {
                String mc = (String) reportConfig.get("moduleCode"); // 报表模块编码
                String ri = (String) reportConfig.get("reportId"); // 报表id
                if (StringUtils.isNotEmpty(ri)) {
                    moduleCode = mc;
                    code = ri;
                }
            }
            param.setPrinterConfig(printerServ.getPrinterConfig(moduleCode, code));
            param.setPrinterColumn(printerServ.getPrinterColList(moduleCode, code));
            if (StringUtils.isEmpty(param.getPrinterConfig())) {
                param.setPrinterConfig(null);
            }
            if (StringUtils.isEmpty(param.getPrinterColumn())) {
                param.setPrinterColumn(null);
            }
        }
        if (StringUtils.isNotEmpty(param.getExportData())) {// 根据数据和配置进行导出
            //ExcelExport.exportExcel(param.getExportData(), param.getPrinterConfig(), param.getPrinterColumn(), response);
        } else {// 数据源导出
            TDataSourceManager dsm = new TDataSourceManager();
            TdataSource ds = dsm.getTdataSource(param.getTdsAlias());
            if (ds != null && "com.yunhesoft.report.tds.model.TDSReport".equalsIgnoreCase(ds.getTdsclassName())) {// 报表模式
                TDSExportParams tparams = new TDSExportParams();
                tparams.setQueryData(param.getQueryData());
                tparams.setTdsData(this.getTDSExportData(param));
                //tparams.setDatas("1".equals(param.getTempType()) ? null : tds.getDataList());
                tparams.setPrinterConfig(param.getPrinterConfig());
                tparams.setPrinterColumn(param.getPrinterColumn());
                tparams.setType(param.getTempType());
                tparams.setSheetName(null);
                tparams.setShowIdCol(param.getShowIdCol());
                tparams.setTdsClass("TDSReport");
                paramsList.add(tparams);
                //ExcelExport.tdsExportExcel(param.getQueryData(), this.getTDSExportData(param), param.getPrinterConfig(), param.getPrinterColumn(), param.getTempType(), response);
            } else {
                JSONArray inpara = new JSONArray();
                String queryJson = param.getQueryData();
                if (StringUtils.isEmpty(queryJson)) {
                    queryJson = param.getInParaAlias();// param.getQueryData();
                    if (queryJson.indexOf("[") >= 0 && queryJson.indexOf("]") >= 0) {
                        inpara = JSONArray.parseArray(queryJson);
                    } else {
                        String[] objs = queryJson.split("\\|");
                        for (String string : objs) {
                            String[] vals = string.split("=");
                            JSONObject o = new JSONObject();
                            String k = vals[0];
                            String v = vals.length > 1 ? vals[1] : "";
                            o.put("name", k);
                            o.put("value", v);
                            inpara.add(o);
                        }
                    }
                } else {
                    inpara = JSONArray.parseArray(queryJson);
                }
                String mutiSheetCols = ""; //多sheet页导出条件 test
                if (ds != null) {
                    mutiSheetCols = ds.getMultiSheetInPara();
                }
                if (StringUtils.isEmpty(mutiSheetCols)) {//单sheet页导出
                    IDataSource tds = this.getTdsDataList(param.getTdsAlias(), inpara, true);
                    paramsList.add(this.createExpParam(null, param, inpara, tds));
                } else {//多sheet页导出
                    for (int n = 0; n < inpara.size(); n++) {
                        JSONObject obj = inpara.getJSONObject(n);
                        if (obj.getString("name").equals(mutiSheetCols)) {
                            JSONArray store = obj.getJSONArray("store");
                            if (store != null && store.size() > 0) {
                                for (int i = 0; i < store.size(); i++) {//按照输入参数 下拉框备选值重新检索数据
                                    JSONObject o = store.getJSONObject(i);
                                    String sheet_name = o.getString("value");
                                    String key = o.getString("key");
                                    obj.put("value", key);
                                    obj.put("rawValue", sheet_name);
                                    IDataSource tds = this.getTdsDataList(param.getTdsAlias(), inpara, true);
                                    paramsList.add(this.createExpParam(sheet_name, param, inpara, tds));
                                }
                            }
                            break;
                        }
                    }
                }
            }
        }
        return paramsList;
    }

    private TDSExportParams createExpParam(String sheetName, TdsExportDto param, JSONArray inpara, IDataSource tds) {
        TDSExportParams tparams = new TDSExportParams();
        tparams.setQueryData(inpara.toJSONString());
        tparams.setTdsData(tds.getJsonWithoutData());
        tparams.setDatas("1".equals(param.getTempType()) ? null : tds.getDataList());
        tparams.setPrinterConfig(param.getPrinterConfig());
        tparams.setPrinterColumn(param.getPrinterColumn());
        tparams.setType(param.getTempType());
        tparams.setSheetName(sheetName);
        tparams.setShowIdCol(param.getShowIdCol());
        return tparams;
    }

}
