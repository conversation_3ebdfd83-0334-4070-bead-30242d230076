package com.yunhesoft.system.tds.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.aviator.AviatorUtils;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.kernel.utils.excel.TDSExportParams;
import com.yunhesoft.system.outPage.service.IOutSecurity;
import com.yunhesoft.system.tds.entity.dto.*;
import com.yunhesoft.system.tds.entity.po.*;
import com.yunhesoft.system.tds.entity.vo.TdataSourceVo;
import com.yunhesoft.system.tds.model.TDSSystemInfo;
import com.yunhesoft.system.tds.model.TDataSourceManager;
import com.yunhesoft.system.tds.service.*;
import com.yunhesoft.system.tds.utils.TdsTools;
import com.yunhesoft.system.tools.eval.model.CustomFun;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 数据源读取相关
 *
 * <AUTHOR>
 */
@Log4j2
@Api(tags = "数据源")
@RestController
@RequestMapping("/tds")
public class TDSController extends BaseRestController {

    @Autowired
    private IDataSourceService tdsServ; // 数据源服务

    @Autowired
    private EntityService entserv;

    @Autowired
    private IDataSourceInParaRelService tdsRelServ; // 数据源输入参数联动服务

    @Autowired
    private IDataSourceManagerService dsManagerServ; // 数据源服务


    @Autowired
    private IOutSecurity outSecurity;
    @Autowired
    private IDataSourceOcrService ocrService;

    @Autowired
    private IDataSourceEditService tdsEditServ; // 数据源修改服

    @ApiOperation(value = "查询数据源数据")
    @RequestMapping(value = "getTdsData", method = {RequestMethod.POST})
    public Res<?> getTdsData(@RequestBody TdsQueryDto param) {
        Res<?> res = outSecurity.filterTdsRequest(param.getTdsAlias());
        if (res != null) {
            return res;
        }
        boolean isInitData = param.getIsInitData() != null ? param.getIsInitData() : false;
        if (isInitData) {// 初始化数据
            TDataSourceManager tsm = new TDataSourceManager();
            TdataSource tds = tsm.getTdataSource(param.getTdsAlias());
            if (tds != null && tds.getAllowToSave() != null && tds.getAllowToSave() == 1 && tds.getIsLoadInit() != null && tds.getIsLoadInit() == 1) {
                Map<String, Object> map = tdsEditServ.reExtractData(param, tds);// 每次检索时重新提取初始化数据
                if (map.containsKey("data")) {
                    return Res.OK(map.get("data"));
                } else {
                    String err = map.get("error").toString();
                    if (StringUtils.isEmpty(err)) {
                        param.setIsInitData(false);
                    }
                }
            }
        }
        return Res.OK(tdsServ.getTDSData(param));
    }

    @ApiOperation(value = "查询多个数据源数据")
    @RequestMapping(value = "getMutiTDSData", method = {RequestMethod.POST})
    public Res<?> getMutiTDSData(@RequestBody TdsQueryDto param) {
        return Res.OK(tdsServ.getMutiTDSData(param));
    }

    @ApiOperation(value = "获取数据源检索条件数据")
    @RequestMapping(value = "getTdsQuery", method = {RequestMethod.POST})
    public Res<?> getTdsQuery(@RequestBody TdsQueryDto param) {
        Res<?> res = outSecurity.filterTdsRequest(param.getTdsAlias());
        if (res != null) {
            return res;
        }
        return Res.OK(tdsServ.getTDSQuery(param));
    }

    @ApiOperation(value = "获取数据源输出参数（支持查询多个）")
    @RequestMapping(value = "getTdsOutParam", method = {RequestMethod.POST})
    public Res<?> getTdsOutParam(@RequestBody TdsQueryDto param) {
        return Res.OK(tdsServ.getTdsOutParam(param.getTdsAlias()));
    }

    /**
     * 导出Excel
     *
     * @param param
     */
    @RequestMapping(value = "/toExcel", method = RequestMethod.POST)
    @ApiOperation(value = "数据源导出Excel")
    public void toExcel(@RequestBody TdsExportDto param) {
        List<TDSExportParams> paramsList = tdsServ.getExprotExcelParams(param);
        if (StringUtils.isNotEmpty(param.getExportData())) {// 根据数据和配置进行导出
            ExcelExport.exportExcel(param.getExportData(), param.getPrinterConfig(), param.getPrinterColumn(), response);
        } else {
            if (StringUtils.isNotEmpty(paramsList)) {
                if ("TDSReport".equals(paramsList.get(0).getTdsClass())) {
                    TDSExportParams exp = paramsList.get(0);
                    ExcelExport.tdsExportExcel(exp.getQueryData(), exp.getTdsData(), exp.getPrinterConfig(), exp.getPrinterColumn(), exp.getType(), response);
                } else {
                    ExcelExport.tdsExportExcel(paramsList, true, response, null);
                }
            }
        }
    }

    @ApiOperation(value = "数据源检索条件切换")
    @RequestMapping(value = "tdsQueryChange", method = {RequestMethod.POST})
    public Res<?> tdsQueryChange(@RequestBody TdsQueryChangeDto param) {
        return Res.OK(tdsRelServ.queryChange(param));
    }

    /**
     * 以下为数据源设置使用=============================
     */

    @ApiOperation(value = "添加数据源数据（科技局项目批量设置使用）")
    @RequestMapping(value = "addDataSource", method = {RequestMethod.POST})
    public Res<?> addDataSource(@RequestBody TdsAddDto param) {
        return Res.OK(dsManagerServ.AddTds(param));
    }

    @ApiOperation(value = "添加数据源数据")
    @RequestMapping(value = "insertTDataSource", method = {RequestMethod.POST})
    public Res<?> insertTDataSource(@RequestBody TdataSource tds) {
        return Res.OK(tdsServ.insertTDataSource(tds));
    }

    @ApiOperation(value = "获取数据源数据表数据")
    @RequestMapping(value = "getTDataSource", method = {RequestMethod.POST})
    public Res<?> getTDataSource(@RequestBody TdsQueryDto param) {
        return Res.OK(tdsServ.getTDataSource(param.getTdsAlias()));
    }

    @ApiOperation(value = "获取数据源基础数据")
    @RequestMapping(value = "getTDS/{tdsAlias}", method = {RequestMethod.POST})
    public Res<?> getTDS(@PathVariable("tdsAlias") String tdsAlias) {
        return Res.OK(tdsServ.getTDataSource(tdsAlias));
    }

    @ApiOperation(value = "修改数据源数据")
    @RequestMapping(value = "updateTDataSource", method = {RequestMethod.POST})
    public Res<?> updateTDataSource(@RequestBody TdataSource tds) {
        return Res.OK(tdsServ.updateTDataSource(tds));
    }

    @ApiOperation(value = "删除数据源数据")
    @RequestMapping(value = "deleteTDataSource", method = {RequestMethod.POST})
    public Res<?> deleteTDataSource(@RequestBody TdsQueryDto param) {
        return Res.OK(tdsServ.deleteTDataSource(param.getTdsAlias()));
    }

    @ApiOperation(value = "获取自定义函数列表")
    @RequestMapping(value = "getCustomFunList", method = {RequestMethod.POST})
    public Res<?> getCustomFunList() {
        return Res.OK(CustomFun.getList());
    }

    /*--------------------------------------------------------TM4新数据源设置----------------------------------------------------*/
    @ApiOperation(value = "获取所有数据源列表")
    @RequestMapping(value = "/getTdsDatasource", method = {RequestMethod.POST})
    public Res<?> getTdsDatasource(@RequestBody TdsDataQueryDto tdd) {
        TdataSourceVo bean = tdsServ.getListTDataSource(tdd);
        return Res.OK(bean.getList(), bean.getTotal());
    }

    @ApiOperation(value = "获取数据源明细")
    @RequestMapping(value = "/getTDatasource", method = {RequestMethod.POST})
    public Res<?> getTDatasource(@RequestBody String tdsAlias) {
        return Res.OK(tdsServ.getTDatasource(tdsAlias));
    }

    @ApiOperation(value = "批量删除数据源")
    @RequestMapping(value = "/delTdsDatasource", method = {RequestMethod.POST})
    public Res<?> deleteTdsDatasources(@RequestBody List<TdataSource> tdatasource) {
        return Res.OK(tdsServ.deleteTdsDatasources(tdatasource));
    }

    @ApiOperation(value = "保存数据源所有信息")
    @RequestMapping(value = "/saveTdsDatasource", method = {RequestMethod.POST})
    public Res<?> saveTdsDatasource(@RequestBody TdsDataInfoDto tdsData) {
        return Res.OK(tdsServ.saveTdsDatasource(tdsData));
    }

    @ApiOperation(value = "删除数据源输入参数")
    @RequestMapping(value = "/delTdsinPara", method = {RequestMethod.POST})
    public Res<?> delTdsinPara(@RequestBody String id) {
        return Res.OK(tdsServ.delTdsinPara(id));
    }

    @ApiOperation(value = "删除数据源输出参数")
    @RequestMapping(value = "/delTdsoutPara", method = {RequestMethod.POST})
    public Res<?> delTdsoutPara(@RequestBody String id) {
        return Res.OK(tdsServ.delTdsoutPara(id));
    }

    @ApiOperation(value = "校验数据源别名重复")
    @RequestMapping(value = "/verificationTdsAlias", method = {RequestMethod.POST})
    public Res<?> verificationTdsAlias(@RequestBody String tdsAlias) {
        return Res.OK(tdsServ.verificationTdsAlias(tdsAlias));
    }

    @ApiOperation(value = "获取数据库中所有的表")
    @RequestMapping(value = "/getTableList", method = {RequestMethod.POST})
    public Res<?> getTableList(@RequestBody String tableName) {
        if (tableName != null && !"".equals(tableName)) {
            JSONObject jsonObj = JSONObject.parseObject(tableName);
            tableName = jsonObj.getString("tableName");
        } else {
            tableName = "";
        }
        return Res.OK(entserv.getTableList(tableName));
    }

    @ApiOperation(value = "获取数据源列表")
    @RequestMapping(value = "/getListTdataSource", method = {RequestMethod.POST})
    public Res<?> getListTdataSource(@RequestBody String tdsAlias) {
        return Res.OK(tdsServ.getListTdataSource(tdsAlias));
    }

    @ApiOperation(value = "获取数据源输出参数")
    @RequestMapping(value = "/getListTdsOutPara", method = {RequestMethod.POST})
    public Res<?> getListTdsOutPara(@RequestBody String tdsAlias) {
        JSONObject jsonObject = JSONObject.parseObject(tdsAlias);
        Res<?> res = outSecurity.filterTdsRequest(jsonObject.getString("tdsAlias"));
        if (res != null) {
            return res;
        }
        return Res.OK(tdsServ.getListTdsOutPara(tdsAlias));
    }

    @ApiOperation(value = "获取子表输入参数输出参数绑定关系")
    @RequestMapping(value = "/getListTdsBind", method = {RequestMethod.POST})
    public Res<?> getListTdsBind(@RequestBody String data) {
        // data:{p_tdsAlias:'AAA', c_tdsAlias:'BBB'} ---p_tdsAlias 主数据源别名，c_tdsAlias
        // 子数据源别名
        return Res.OK(tdsServ.getListTdsBind(data));
    }

    @ApiOperation(value = "保存主子表输入参数输出参数绑定关系")
    @RequestMapping(value = "/saveTdsBind", method = {RequestMethod.POST})
    public Res<?> saveTdsBind(@RequestBody List<TdsBind> tdsbind) {
        return Res.OK(tdsServ.saveTdsBind(tdsbind));
    }

    @ApiOperation(value = "复制输入参数")
    @RequestMapping(value = "/getCopyTdsinPara", method = {RequestMethod.POST})
    public Res<?> getCopyTdsinPara(@RequestBody String data) {
        // data:{p_tdsAlias:'AAA', c_tdsAlias:'BBB'} ---p_tdsAlias 被复制数据源别名，c_tdsAlias
        // 复制后的数据源别名
        return Res.OK(tdsServ.getCopyTdsinPara(data));
    }

    @ApiOperation(value = "生成输出参数")
    @RequestMapping(value = "/getCreateTdsOutPara", method = {RequestMethod.POST})
    public Res<?> getCreateTdsOutPara(@RequestBody String data) {
        // data:{sqls:'AAA', tdsAlias:'BBB', id:'BBB'} --- sql语句， tdsAlias 数据源别名， id
        // 主记录id
        return Res.OK(tdsServ.getCreateTdsOutPara(data));
    }

    @ApiOperation(value = "输入参数脚本校验")
    @RequestMapping(value = "/checkInParaScript", method = {RequestMethod.POST})
    public Res<?> checkInParaScript(@RequestParam String script) {
        TDSSystemInfo tds = new TDSSystemInfo();
        Object result = null;
        Res<Object> resp = new Res<Object>();
        try {
            result = tds.checkInParaScript(script);
            resp.setSuccess(true);
            resp.setMessage("执行成功！");
            resp.setResult(result);
        } catch (Exception e) {
            resp.fail("执行失败，失败原因：" + e.toString());
        }
        return resp;
    }

    @ApiOperation(value = "数据源另存为")
    @RequestMapping(value = "/saveAsTDatasource", method = {RequestMethod.POST})
    public Res<?> saveAsTDatasource(@RequestBody String data) {
        // data:{tdsAlias:'原数据源别名', newTdsAlias:'新数据源别名',newTdsname:'新数据源名称'}
        // 子数据源别名
        return Res.OK(tdsServ.saveAsTDatasource(data));
    }

    @ApiOperation(value = "数据源输入参数联动树数据")
    @RequestMapping(value = "getInParaRelTreeData", method = {RequestMethod.POST})
    public Res<?> getInParaRelTreeData(@RequestParam("tdsAlias") String tdsAlias) {
        return Res.OK(tdsRelServ.getInParaRelTreeData(tdsAlias));
    }

    @ApiOperation(value = "数据源输入参数联动树节点拖拽")
    @RequestMapping(value = "inParaRelNodeDrop", method = {RequestMethod.POST})
    public Res<?> inParaRelNodeDrop(@RequestBody TdsRelNodeDrop dto) {
        return Res.OK(tdsRelServ.inParaRelNodeDrop(dto));
    }

    @ApiOperation(value = "获取数据源工作流程设置数据")
    @RequestMapping(value = "getTdsFlowData", method = {RequestMethod.POST})
    public Res<?> getTdsFlowData(@RequestParam("tdsAlias") String tdsAlias) {
        Res<TdsFlow> res = new Res<TdsFlow>();
        Res<?> res1 = outSecurity.filterTdsRequest(tdsAlias);
        if (res1 != null) {
            return res1;
        }
        return res.setResult(tdsServ.getTdsFlowData(tdsAlias));
    }

    @ApiOperation(value = "保存数据源工作流程设置数据")
    @RequestMapping(value = "saveTdsFlowData", method = {RequestMethod.POST})
    public Res<?> saveTdsFlowData(@RequestBody TdsFlowDto dto) {
        return Res.OK(tdsServ.saveTdsFlowData(dto));
    }

    @ApiOperation(value = "在某一个表内创建字段")
    @RequestMapping(value = "createTableColumn", method = {RequestMethod.POST})
    public Res<?> createTableColumn(@RequestBody CreateTableColumnDto dto) {
        return Res.OK(tdsServ.createTableColumn(dto.getTableName(), dto.getListColumnName()));
    }

    @ApiOperation(value = "在某一个表内更新字段")
    @RequestMapping(value = "updateTableColumn", method = {RequestMethod.POST})
    public Res<?> updateTableColumn(@RequestBody CreateTableColumnDto dto) {
        return Res.OK(tdsServ.updateTableColumn(dto.getTableName(), dto.getListColumnName(), dto.getValue(), dto.getWhere()));
    }

    @ApiOperation(value = "保存数据源编辑功能自定义按钮数据")
    @RequestMapping(value = "saveCustomBtnData", method = {RequestMethod.POST})
    public Res<?> saveCustomBtnData(@RequestBody TdsEditCustomBtnDto param) {
        return Res.OK(tdsServ.saveCustomBtnData(param));
    }

    @ApiOperation(value = "获取数据源编辑功能自定义按钮数据")
    @RequestMapping(value = "getCustomBtnData", method = {RequestMethod.POST})
    public Res<?> getCustomBtnData(@RequestParam String tdsAlias) {
        return Res.OK(tdsServ.getCustomBtnData(tdsAlias));
    }

    @ApiOperation(value = "获取数据源编辑功能自定义按钮数据")
    @RequestMapping(value = "getEditTdsBindParamData", method = {RequestMethod.POST})
    public Res<?> getEditTdsBindParamData(@RequestParam @ApiParam(value = "源数据源别名") String targettdsalias, @RequestParam @ApiParam(value = "绑定数据源别名") String bindtdsalias, @RequestParam @ApiParam(value = "参数类型 ") int bindtdsparamtype, @RequestParam @ApiParam(value = "是否进行初始化数据") boolean isInitData) {
        return Res.OK(tdsServ.getEditTdsBindParamData(targettdsalias, bindtdsalias, bindtdsparamtype, isInitData));
    }

    @ApiOperation(value = "获取数据源输出参数")
    @RequestMapping(value = "/getListTdsInPara", method = {RequestMethod.POST})
    public Res<?> getListTdsInPara(@RequestBody String tdsAlias) {
        return Res.OK(tdsServ.getListTdsInPara(tdsAlias));
    }

    @ApiOperation(value = "保存数据源编辑功能绑定输入输出参数")
    @RequestMapping(value = "saveEditTdsBindParam", method = {RequestMethod.POST})
    public Res<?> saveEditTdsBindParam(@RequestBody TdsEditBindParamDto param) {
        return Res.OK(tdsServ.saveEditTdsBindParam(param));
    }

    @ApiOperation(value = "判断是否已绑定输入输出参数")
    @RequestMapping(value = "editTdsBindParamIsBind", method = {RequestMethod.POST})
    public Res<?> editTdsBindParamIsBind(@RequestParam @ApiParam(value = "源数据源别名") String targettdsalias, @RequestParam @ApiParam(value = "绑定数据源别名") String bindtdsalias) {
        return Res.OK(tdsServ.editTdsBindParamIsBind(targettdsalias, bindtdsalias));
    }

    @ApiOperation(value = "清除绑定的数据源输入输出参数")
    @RequestMapping(value = "clearEditTdsBindParam", method = {RequestMethod.POST})
    public Res<?> clearEditTdsBindParam(@RequestParam @ApiParam(value = "源数据源别名") String targettdsalias, @RequestParam @ApiParam(value = "绑定数据源别名") String bindtdsalias) {
        return Res.OK(tdsServ.clearEditTdsBindParam(targettdsalias, bindtdsalias));
    }

    @ApiOperation(value = "列间公式")
    @RequestMapping(value = "/getFunList", method = {RequestMethod.POST})
    public Res<?> getFunList() {
        return Res.OK(AviatorUtils.getFunctionList());
    }

    /**
     * 获取数据源输出列信息
     *
     * @param tdsAlias 数据源别名(多个数据源逗号分隔，代码union 模式)
     * @return
     */
    @ApiOperation(value = "获取数据源输出列信息")
    @RequestMapping(value = "getOutColumn", method = {RequestMethod.POST})
    public Res<?> getOutColumn(@RequestParam @ApiParam(value = "数据源别名") String tdsAlias) {
        return Res.OK(TdsTools.getOutColumn(tdsAlias));
    }

    /**
     * 获取数据源输出列信息
     *
     * @param tdsAlias 数据源别名(多个数据源逗号分隔，代码union 模式)
     * @return
     */
    @ApiOperation(value = "获取数据源输出列信息转成数据源输出参数对象")
    @RequestMapping(value = "getOutColumnToOutParam", method = {RequestMethod.POST})
    public Res<?> getOutColumnToOutParam(@RequestParam @ApiParam(value = "数据源别名") String tdsAlias) {
        return Res.OK(TdsTools.getOutColumnToOutParam(tdsAlias));
    }

    @ApiOperation(value = "判断数据源编辑功能中字段是否存在")
    @RequestMapping(value = "isExtTableColumnNameByTds", method = {RequestMethod.POST})
    public Res<?> isExtTableColumnNameByTds(@RequestParam @ApiParam(value = "数据源别名") String tdsAlias, @RequestParam @ApiParam(value = "字段别名") String columnName) {
        return Res.OK(tdsServ.isExtTableColumnNameByTds(tdsAlias, columnName));
    }

    @ApiOperation(value = "查询脚本")
    @RequestMapping(value = "/getScriptByTds", method = {RequestMethod.POST})
    public Res<?> getScriptByTds(@RequestParam @ApiParam(value = "数据源别名") String tdsAlias) {
        return Res.OK(tdsServ.getScriptByTds(tdsAlias));
    }

    @ApiOperation(value = "保存脚本")
    @RequestMapping(value = "/saveScriptByTds", method = {RequestMethod.POST})
    public Res<?> saveScriptByTds(@RequestBody @ApiParam(value = "数据源脚本") TdsScriptEntity tdsScriptEntity) {
        return Res.OK(tdsServ.saveScriptByTds(tdsScriptEntity));
    }

    @ApiOperation(value = "查询识别配置")
    @RequestMapping(value = "/getOcrSet", method = {RequestMethod.POST})
    public Res<?> getOcrSet(@RequestParam @ApiParam(value = "数据源别名") String tdsAlias) {
        return Res.OK(ocrService.getOcrSet(tdsAlias));
    }

    @ApiOperation(value = "保存识别配置")
    @RequestMapping(value = "/saveOcrSet", method = {RequestMethod.POST})
    public Res<?> saveOcrSet(@RequestBody @ApiParam(value = "数据源脚本") TdsOcrSet tdsScriptEntity) {
        return Res.OK(ocrService.saveOcrSet(tdsScriptEntity));
    }

    @ApiOperation(value = "保存排序信息")
    @RequestMapping(value = "/saveSortData", method = {RequestMethod.POST})
    public Res<?> savaSortData(@RequestBody TdsSortSaveDto dto) {
        return Res.OK(tdsServ.savaSortData(dto.getTdsAlias(), dto.getSortDataList()));
    }

    @ApiOperation(value = "获取排序信息for前台")
    @RequestMapping(value = "/getSortDataVo", method = {RequestMethod.POST})
    public Res<?> getSortDataVo(@RequestParam @ApiParam(value = "数据源别名") String tdsAlias) {
        return Res.OK(tdsServ.getSortDataVo(tdsAlias));
    }

    /**
     * 导出数据源
     *
     * @param param
     */
    @RequestMapping(value = "/exportTdsManage", method = RequestMethod.POST)
    @ApiOperation(value = "数据源导出")
    public void exportTdsManage(@RequestBody TdsQueryDto param) {
        String tdsAlias = param.getTdsAlias();
        List<String> tdsAliasList = StringUtils.isEmpty(tdsAlias) ? null : Arrays.asList(tdsAlias.split(","));
        dsManagerServ.exportTdsManage(response, tdsAliasList);
    }

    @RequestMapping(value = "/tdsManageImportConfirm", method = RequestMethod.POST)
    @ApiOperation(value = "数据源导入确认")
    public Res<?> tdsManageImportConfirm(@RequestParam("file") MultipartFile file, @RequestParam(required = false, name = "params") JSONObject params) {
        return Res.OK(dsManagerServ.tdsManageImportComfirm(file, params));
    }

    /**
     * 导入数据源
     *
     * @param param
     */
    @RequestMapping(value = "/importTdsManage", method = RequestMethod.POST)
    @ApiOperation(value = "导入数据源")
    public Res<?> importTdsManage(@RequestParam("file") MultipartFile file, @RequestParam(required = false, name = "params") String params) {
//		String tdsAlias = param.getTdsAlias();
//		List<String> tdsAliasList = StringUtils.isEmpty(tdsAlias) ? null : Arrays.asList(tdsAlias.split(","));
        JSONObject paramObj = null;
        if (StringUtils.isNotEmpty(params)) {
            paramObj = JSON.parseObject(params);
        }
//		dsManagerServ
        return Res.OK(dsManagerServ.importTdsManage(file, paramObj));

    }

    @ApiOperation(value = "sql类型数据源维护表结构")
    @RequestMapping(value = "/tdsSqlDDl", method = {RequestMethod.POST})
    public Res<?> tdsSqlDDl(@RequestBody TdsQueryDto param) {
        return Res.OK(tdsServ.tdsSqlDDl(param.getTdsAlias()));
    }

    @ApiOperation(value = "数据源公式解析")
    @RequestMapping(value = "/parseScript", method = {RequestMethod.POST})
    public Res<?> parseScript(@RequestBody Map<String, Object> map) {
        TDSSystemInfo tds = new TDSSystemInfo();
        Object result = null;
        Res<Object> resp = new Res<Object>();
        try {
            String script = map.get("script") == null ? "" : String.valueOf(map.get("script"));
            result = tds.parseScript(script, map);
            resp.setSuccess(true);
            resp.setMessage("执行成功！");
            resp.setResult(result);
        } catch (Exception e) {
            resp.fail("执行失败，失败原因：" + e.toString());
        }
        return resp;
    }

//    @ApiOperation(value = "数据源公式解析eval")
//    @RequestMapping(value = "/tdsEval", method = {RequestMethod.POST})
//    public Res<?> tdsEval(@RequestParam(required = true, name = "tdsAlias") String tdsAlias, @RequestParam(required = false, name = "inpara") String inpara, @RequestParam(required = true, name = "params") String s) {
//        Object result = null;
//        Res<Object> resp = new Res<Object>();
//        try {
//            TDataSourceManager tsm = new TDataSourceManager();
//            Map<String, IDataSource> mapds = new HashMap<>();
//            IDataSource tds = tsm.getDataSource(tdsAlias, inpara);
//            tds.load();
//            mapds.put(tdsAlias, tds);
//            //$zxtdseval.find("zzdm='a001' and bzdm='b001' and zyid='c001'").getf("df")
//            result = tsm.execTds(s, mapds);
//            //result = tsm.evalTds(s, mapds, tdsFindParamsMap);
//            resp.setSuccess(true);
//            resp.setMessage("执行成功！");
//            resp.setResult(result);
//        } catch (Exception e) {
//            resp.fail("执行失败，失败原因：" + e.toString());
//        }
//        return resp;
//    }

    @ApiOperation(value = "公式解析测试")
    @RequestMapping(value = "/evalTest", method = {RequestMethod.POST})
    public Res<?> testEval(@RequestParam(required = false, name = "execCount") String count) {
        Object result = null;
        Res<Object> resp = new Res<Object>();
        try {
//            TDataSourceManager tsm = new TDataSourceManager();
//            int calcount = 5000;
//            if (count != null && !"".equals(count)) {
//                calcount = Integer.parseInt(count);
//            }
//            for (int i = 0; i < calcount; i++) {
//                String script = "100 * " + i + " + 30";
//                result = tsm.getScriptValue(script, null, null);
//                log.info("[" + i + "],script:" + script + ",result:" + result);
//            }
//            resp.setSuccess(true);
//            resp.setMessage("执行成功！");
//            resp.setResult(result);
        } catch (Exception e) {
            resp.fail("执行失败，失败原因：" + e.toString());
        }
        return resp;
    }

    @ApiOperation(value = "gc回收")
    @RequestMapping(value = "/gc", method = {RequestMethod.POST})
    public Res<?> gctst() {
        Object result = null;
        Res<Object> resp = new Res<Object>();
//        try {
//            System.gc();
//            resp.setSuccess(true);
//            resp.setMessage("执行成功！");
//            resp.setResult(result);
//        } catch (Exception e) {
//            resp.fail("执行失败，失败原因：" + e.toString());
//        }
        return resp;
    }

}
