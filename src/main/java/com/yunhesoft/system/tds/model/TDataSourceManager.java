package com.yunhesoft.system.tds.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.aviator.AviatorResult;
import com.yunhesoft.core.common.aviator.AviatorUtils;
import com.yunhesoft.core.common.script.ScriptEngineUtils;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.Maths;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.HtmlUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.tds.entity.dto.TdsTableInfoWithScript;
import com.yunhesoft.system.tds.entity.po.*;
import com.yunhesoft.system.tds.entity.vo.TdsSearchMoreChildData;
import com.yunhesoft.system.tds.entity.vo.TdsSearchMoreDataVo;
import com.yunhesoft.system.tds.entity.vo.TdsSortMoreDataVo;
import com.yunhesoft.system.tds.entity.vo.TdsTableColumn;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tds.utils.TdsTools;
import com.yunhesoft.system.tools.eval.CompiledScriptEngine;
import com.yunhesoft.system.tools.eval.TJSEngine;
import com.yunhesoft.system.tools.eval.model.CustomFun;
import lombok.extern.log4j.Log4j2;

import javax.script.ScriptException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 数据源管理器
 *
 * <AUTHOR>
 * @version 1.0 2010-3-1 <br>
 * 2.0 x.zhong 2021-06
 * @category 数据源管理器
 */
@Log4j2
public class TDataSourceManager implements Serializable {

    private static final long serialVersionUID = 7203256511826073805L;
    /**
     * 最后操作产生的错误信息
     */
    private String lastError;
    private Object sysObject;
    private boolean showUnCheckExcel = false;// Excel数据源是否未审核的数据，默认false
    private String dataLocatorForTDataSource = "";// 数据定位公式（例如
    // $a.find("a='b'").getf("c")
    // 会根据公式定位到具体数据，高亮显示）
    private boolean isNotPrintLog = false;// 不打印日志

    private TDSCached tdsCached = TDSCached.getInstance();

    private static TJSEngine checkJse = null;
    private static TJSEngine localJse = null;

    // 预编译脚本解析引擎
    private Map<String, CompiledScriptEngine> evalMap = new HashMap<String, CompiledScriptEngine>();

    public TDataSourceManager() {
        this.sysObject = null;
    }

    public TDataSourceManager(Object sysObject) {
        this.sysObject = sysObject;
    }

    public boolean isNotPrintLog() {
        return isNotPrintLog;
    }

    public void setNotPrintLog(boolean isNotPrintLog) {
        this.isNotPrintLog = isNotPrintLog;
    }

    public boolean isShowUnCheckExcel() {
        return showUnCheckExcel;
    }

    public void setShowUnCheckExcel(boolean showUnCheckExcel) {
        this.showUnCheckExcel = showUnCheckExcel;
    }

    public String getDataLocatorForTDataSource() {
        return dataLocatorForTDataSource;
    }

    public void setDataLocatorForTDataSource(String dataLocatorForTDataSource) {
        this.dataLocatorForTDataSource = dataLocatorForTDataSource;
    }

    public String getLastError() {
        return lastError;
    }

    public Object getSysObject() {
        return this.sysObject;
    }

    //可解析的自定义函数列表
    private static List<String> customFunList = null;

    private CompiledScriptEngine cse = null;

    /**
     * 根据别名返回一个数据源
     *
     * @param dsAlias 数据源别名
     * @return 数据源对象
     * @category 根据别名返回一个数据源
     */
    public IDataSource getDataSource(String dsAlias) {
        return getDataSource(dsAlias, true, sysObject);
    }

    public IDataSource getDataSource(String dsAlias, boolean autoLoad/* =true */) {
        return getDataSource(dsAlias, autoLoad, sysObject);
    }

    public IDataSource getDataSource(Class<?> cls) {
        try {
            return (IDataSource) cls.newInstance();
        } catch (InstantiationException e) {
            log.error("", e);
        } catch (IllegalAccessException e) {
            log.error("", e);
        }
        return null;
    }

    /**
     * 获得数据源数据库信息
     *
     * @param dsAlias 数据源别名
     * @return
     */
    public TdataSource getTdataSource(String dsAlias) {
        TdataSource tds = null;
        try {
            tds = tdsCached.getDs(dsAlias); // 从内存中获取
            if (tds == null) {
                tds = SpringUtils.getBean(IDataSourceService.class).getTDataSource(dsAlias);
                tdsCached.putDs(dsAlias, tds);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return tds;
    }

    public String getData(IDataSource ds, JSONArray dsArray, String type, boolean isInitData, boolean showRenderValue, boolean errInfo, String tdsSearchMoreTxt, List<TdsSearchMoreDataVo> tdsSearchMoreData, List<TdsSortMoreDataVo> tdsSortMoreData,String inParaAliasFront) {
        String strRtn = "";
        // JSONArray dsArray = JSONArray.parseArray(jsonData);//
        // JSONArray.fromObject(jsonData);
        for (int i = 0; i < dsArray.size(); i++) {
            JSONObject dsObj = dsArray.getJSONObject(i);// JSONObject.fromObject(dsArray.get(i));
            String dsCode = dsObj.getString("name");// 数据源别名
            String filter = "";
            if (dsObj.get("filter") != null) {
                filter = dsObj.getString("filter");
            }
            if (ds == null) {
                ds = this.getDataSource(dsCode);
                ds.setInParaAliasFront(inParaAliasFront);
            }
            if (ds != null) {
                if (filter != null && filter.length() > 0) {
                    // ds.filter(filter);
                }
                int page = 1;
                int pageSize = 0;
                JSONObject pageInfo = dsObj.getJSONObject("pageInfo");// 分页信息
                if (pageInfo != null) {
                    page = pageInfo.getInteger("page");// 当前页
                    pageSize = pageInfo.getInteger("pageSize");// 每页记录数
                }
                JSONArray dataArray = dsObj.getJSONArray("data");
                if (dataArray != null && dataArray.size() > 0) {
                    for (int j = 0; j < dataArray.size(); j++) {
                        JSONObject dataObj = dataArray.getJSONObject(j); // JSONObject.fromObject(dataArray.get(j));
                        String paraAlias = dataObj.getString("name");// 参数
                        Object oValue = dataObj.get("value"); // 参数值
                        ds.setInParaByAlias(paraAlias, oValue); // 输入参数
                    }
                }
                if (type != null && ("TDSStaticData".equals(type))) {
                } else {// 获取动态数据
                    ds.setShowRenderValue(showRenderValue);// 是否显示渲染函数执行后的值
                    ds.setLoadInitData(isInitData);// 每次加载是否执行初始化语句
                    loadDsData(ds, filter, page, pageSize, tdsSearchMoreTxt, tdsSearchMoreData, tdsSortMoreData);// 加载数据源
                    String strData = "";
                    if ("jsonData".equals(type)) {// 返回纯数据模式
                        strRtn = ds.getJsonData();
                    } else {
                        strData = ds.getJson(errInfo);
                        strRtn += strData;
                        if (i == (dsArray.size() - 1)) {
                            strRtn = "[" + strRtn + "]";
                        } else {
                            strRtn += ",";
                        }
                    }
                }
            }
        }
        return strRtn;
    }

    /**
     * 获得输入参数list
     *
     * @param dsAlias 数据源别名
     * @return
     */
    public List<TdsinPara> getInParaList(String dsAlias) {
        List<TdsinPara> list = null;
        try {
            list = tdsCached.getInPara(dsAlias);// 从内存中获取
            if (list == null) {
                list = SpringUtils.getBean(IDataSourceService.class).getTDSInPara(dsAlias);
                tdsCached.putInPara(dsAlias, list);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return list;
    }

    /**
     * 获得输出参数list
     *
     * @param dsAlias 数据源别名
     * @return
     */
    public List<TdsoutPara> getOutParaList(String dsAlias) {
        List<TdsoutPara> list = null;
        try {
            list = tdsCached.getOutPara(dsAlias);
            if (list == null) {
                list = SpringUtils.getBean(IDataSourceService.class).getTDSOutPara(dsAlias);
                tdsCached.putOutPara(dsAlias, list);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return list;
    }

    /**
     * 增量输入参数
     *
     * @param inParaAlias
     * @return
     */
    private Map<String, String> getMapInParas(String inParaAlias) {
        Map<String, String> map = null;
        try {
            if (inParaAlias != null && inParaAlias.length() > 0) {
                // 传入的条件
                String[] aryStr = inParaAlias.split("\\|");
                // 生成条件json字符串
                map = new HashMap<String, String>();
                for (int i = 0; i < aryStr.length; i++) {
                    String[] aryPara = (aryStr[i] + "=a").split("=");
                    if (aryPara[0] != null && aryPara[0].length() > 0) {
                        map.put(aryPara[0], aryPara[1]);
                    }
                }

            }
        } catch (Exception e) {
            log.error("", e);
        }
        return map;
    }

    /**
     * 根据别名返回一个数据源 <br>
     * 对于需要传入系统参数的数据源 *
     *
     * @param dsAlias     别名
     * @param inParaAlias 数据源输入参数
     * @return
     * @category 根据别名返回一个数据源
     */
    public IDataSource getDataSource(String dsAlias, String inParaAlias) {
        return this.getDataSource(dsAlias, getMapInParas(inParaAlias));
    }

    /**
     * 加载数据源
     *
     * @param dsAlias      数据源别名
     * @param inparaMap    检索条件map
     * @param loadDataList 返回值是否为 List<Map<String,Object>>
     * @return
     */
    public IDataSource getDataSource(String dsAlias, Map<String, String> inparaMap) {
        if (dsAlias == null || "".equals(dsAlias)) {
            return null;
        }
        lastError = "";
        IDataSource ids = null;
        try {
            ids = getDataSource(getTdataSource(dsAlias), true);
            if (ids != null) {
                ids.initParent(this);
                if (sysObject == null) {
                    ids.init(SysUserHolder.getCurrentUser());
                } else {
                    ids.init(sysObject);
                }
                ids.parseInParaDefaultValue();
                if (inparaMap != null) {
                    for (String key : inparaMap.keySet()) {
                        ids.setInParaByAlias(key, inparaMap.get(key));// 设置输入参数
                    }
                }
                if (ids.getAutoLoad()) {
                    // ids.setNowAutoLoad(true); // 正在调用自动自动加载数据源函数
                    ids.load(); // 调用自动加载数据源函数
                    // ids.setNowAutoLoad(false); // 已完成调用自动自动加载数据源函数
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return ids;
    }

    /**
     * 根据别名返回一个数据源 <br>
     * 对于需要传入系统参数的数据源 *
     *
     * @param dsAlias     别名
     * @param autoLoad    是否自动加载
     * @param initInfoObj 数据源初始化必须的系统参数，如系统用户信息等
     * @return
     * @category 根据别名返回一个数据源
     */
    private IDataSource getDataSource(String dsAlias, boolean autoLoad, Object initInfoObj) {
        if (dsAlias == null || "".equals(dsAlias)) return null;
        lastError = "";
        if (initInfoObj == null) {
            initInfoObj = SysUserHolder.getCurrentUser(); // 默认初始化当前用户信息
        }
//        log.info("获取数据源："+dsAlias);
        IDataSource ids = null;
        try {
            TdataSource tds = getTdataSource(dsAlias);
            ids = getDataSource(tds, true);
            if (ids != null) {
                // ids.setBpmMode(bpmMode);
                // ids.setTemplateId(templateId);
                ids.setCTdsAlias(tds.getCTdsAlias());
                ids.setCTdsName(tds.getCTdsName());
                ids.initParent(this);
                ids.init(initInfoObj);
                ids.parseInParaDefaultValue();
                if (ids.getAutoLoad() && autoLoad) {// zouhao 2013.09.24日添加
                    // ids.setNowAutoLoad(true); // 正在调用自动自动加载数据源函数
//                    log.info(dsAlias+",ids.load()");
                    ids.load(); // 调用自动加载数据源函数
                    // ids.setNowAutoLoad(false); // 已完成调用自动自动加载数据源函数
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return ids;
    }

    /**
     * 获得class全路径
     *
     * @param className
     * @return
     */
    private String getTdsClassName(String className) {
        if (StringUtils.isEmpty(className)) {
            className = "TDSSQL";
        }
        if (className.indexOf(".") < 0) {
            className = "com.yunhesoft.system.tds.model." + className;
        } else {
            if (!className.startsWith("com.yunhesoft.")) {
                className = "com.yunhesoft." + className;
            }
        }
        return className;
    }

    /**
     * 动态生成数据源类对象
     *
     * @param tds 数据库中数据源数据
     * @return 数据源对象接口
     * @category 动态生成数据源类对象
     */
    public IDataSource getDataSource(TdataSource tds, boolean hasPara) {
        lastError = "";
        if (tds == null) return null;
        IDataSource ids = null;
        String className = getTdsClassName(tds.getTdsclassName());
        try {
            Class<?> cls = Class.forName(className);
            ids = (IDataSource) cls.newInstance();
            ids.setShowUnCheckExcel(isShowUnCheckExcel());// 是否显示未审核的excel记录
            // 子数据源别名，名称
            if (tds.getAllowToSave() != null && tds.getAllowToSave() == 1) {
                ids.setAllowSaved(true);
            } else {
                ids.setAllowSaved(false);
            }
            ids.setAllowNull(true);
            if (tds.getAllowNull() != null && tds.getAllowNull() == 0) {
                ids.setAllowNull(false);
            }
            if (tds.getImportDataBase() != null && tds.getImportDataBase().intValue() == 1) {
                ids.setImportDataBase(true);
            } else {
                ids.setImportDataBase(false);
            }
            ids.setCategoryId(tds.getCategoryId());
            ids.setDsSort(tds.getTmSort());
            ids.setDSAlias(tds.getTdsalias());
            ids.setModuleCode(tds.getModuleCode());
            ids.setDSName(tds.getTdsname());
            ids.setDSType(tds.getTdstype());
            ids.setDSClassName(tds.getTdsclassName());
            ids.setMemo(tds.getMemo());
            ids.setAllowSaved(tds.getAllowToSave() == null ? false : (tds.getAllowToSave() == 1 ? true : false));
            ids.setTdsQuerySql(tds.getTdsQuerySql());
            ids.setTdsInitSql(tds.getTdsInitSql());
            ids.setTdsInitType(tds.getTdsInitType() == null ? 2 : tds.getTdsInitType());
            ids.setBindTdsAlias(tds.getBindTdsAlias() == null ? "" : tds.getBindTdsAlias());
            ids.setTdsCalScript(tds.getTdsCalScript());
            ids.setTdsAfterEditScript(tds.getTdsAfterEditScript());
            ids.setTdsCanEditScript(tds.getTdsCanEditScript());
            ids.setTdsUpdateSql(tds.getTdsUpdateSql());
            ids.setCreateUid(tds.getCreateUid());
            ids.setCreateUname(tds.getCreateUname());
            ids.setUpdateUid(tds.getUpdateUid());
            ids.setUpdateUname(tds.getUpdateUname());
            ids.setUpdateDate(tds.getUpdateDate());
            ids.setRegTime(tds.getRegtime());
            ids.setUsed(tds.getUsed() == null ? false : (tds.getUsed() == 1 ? true : false));
            ids.setAutoLoad(tds.getAutoLoad() == null ? false : tds.getAutoLoad() == 1 ? true : false);
            ids.setScript(tds.getScript());
            ids.setMainDB(tds.getIsMainDb() == null ? false : tds.getIsMainDb() == 1 ? true : false);
            ids.setDbConnInfoId(tds.getDbConnInfoId());
            ids.setDbTableName(tds.getDbTableName());
            ids.setExcelAutoCreate(tds.getExcelAutoCreate() == null ? true : (tds.getExcelAutoCreate() == 1 ? true : false));
            ids.setExcelMultiSheet(tds.getExcelMultiSheet() == null ? true : (tds.getExcelMultiSheet() == 1 ? true : false));
            // ids.setIsFlow(tds.getIsFlow() == null ? false : (tds.getIsFlow() == 1 ? true
            // : false));
            ids.setAdvancedSearch(tds.getAdvancedSearch() == null ? false : (tds.getAdvancedSearch() == 1 ? true : false));
            ids.setFindByKey(tds.getFindByKey() == null ? false : (tds.getFindByKey() == 1 ? true : false));
            if (hasPara) {
                // 初始化输入参数
                List<TdsinPara> inlist = this.getInParaList(ids.getDSAlias());
                ids.setInPara(TdsinParaToTInParaList(ids, inlist));
                // 初始化输出参数
                List<TdsoutPara> outlist = this.getOutParaList(ids.getDSAlias());
                if (outlist == null) {
                    ids.setInitOutParaSize(0);
                } else {
                    ids.setInitOutParaSize(outlist.size());
                }
                ids.setOutPara(TdsoutParaToOutParaList(ids, outlist));
                ids.sortInPara();
                ids.sortOutPara();
            }
        } catch (ClassNotFoundException e) {
            log.error("", e);
        } catch (InstantiationException e) {
            log.error("", e);
        } catch (IllegalAccessException e) {
            log.error("", e);
        }
        return ids;
    }

    /**
     * 输出参数转换
     *
     * @param ds
     * @param dsout
     * @return
     */
    private TOutPara TdsoutParaToTOutPara(IDataSource ds, TdsoutPara dsout) {
        TOutPara op = new TOutPara(ds);
        op.setAlias(dsout.getParaAlias());
        if (StringUtils.isEmpty(dsout.getDataType())) {
            op.setDataType(IDataSource.DataType.tdsString);
        } else {
            op.setDataType(IDataSource.DataType.valueOf(dsout.getDataType()));
        }
        op.setID(dsout.getParaId());
        op.setIsKey(dsout.getIskey() != null && dsout.getIskey() == 1 ? true : false);
        op.setIsSum(dsout.getIsSum() == null || dsout.getIsSum() == 0 ? false : true);
        op.setIsSpan(dsout.getIsSpan() == null || dsout.getIsSpan() == 0 ? false : true);
        op.setIsGroup(dsout.getIsGroup() == null || dsout.getIsGroup() == 0 ? false : true);
        op.setMemo(dsout.getMemo());
        op.setName(dsout.getParaName());
        op.setCallFun(dsout.getCallFun());
        op.setRowflag(1);
        if (dsout.getVisible() == null) {
            op.setVisible(true);
        } else {
            op.setVisible(dsout.getVisible() == 1 ? true : false);
        }
        if (dsout.getWidth() == null) {
            op.setWidth(100);
        } else {
            op.setWidth(dsout.getWidth());
        }
        if (dsout.getAlign() == null) {
            op.setAlign("left");
        } else {
            op.setAlign(dsout.getAlign());
        }
        if (dsout.getRendererFun() == null) {
            op.setRendererFun("");
        } else {
            op.setRendererFun(dsout.getRendererFun());
        }
        if (dsout.getDefaultKeyScript() == null) {
            op.setDefaultKeyScript("");
        } else {
            op.setDefaultKeyScript(dsout.getDefaultKeyScript());
        }
        op.setComType(dsout.getComType() == null ? "" : dsout.getComType());
        op.setReportFormula(dsout.getReportFormula() == null ? "" : dsout.getReportFormula());
        op.setLx(dsout.getLx() == null ? "0" : dsout.getLx());
        if (dsout.getDefaultValueScript() == null) {
            op.setDefaultValueScript("");
        } else {
            op.setDefaultValueScript(dsout.getDefaultValueScript());
        }

        if (dsout.getOvertip() == null) {
            op.setOvertip(true);
        } else {
            op.setOvertip(dsout.getOvertip() == 1);
        }
        op.setFixed(dsout.getFixed());
        if (dsout.getAutowidth() == null) {
            op.setAutowidth(true);
        } else {
            op.setAutowidth(dsout.getAutowidth() == 1);
        }
        op.setMaxlength(dsout.getMaxlength());
        op.setSpanType(dsout.getSpanType() == null ? 0 : dsout.getSpanType());
        if (dsout.getSpanScript() == null) {
            op.setSpanScript("");
        } else {
            op.setSpanScript(dsout.getSpanScript());
        }
        if (dsout.getIsRequired() != null && dsout.getIsRequired() == 1) {
            op.setRequired(true);
        } else {
            op.setRequired(false);
        }
        if (dsout.getShowPlaceholder() != null && dsout.getShowPlaceholder() == 1) {
            op.setShowPlaceholder(true);
        } else {
            op.setShowPlaceholder(false);
        }
        op.setComParams(dsout.getComParams());
        op.setDataStatusFlag(dsout.getDataStatusFlag() == null ? 0 : dsout.getDataStatusFlag());
        op.setIsShowAsNum(dsout.getIsShowAsNum() == null ? 0 : dsout.getIsShowAsNum());
        op.setIsExportRender(dsout.getIsExportRender() == null ? 0 : dsout.getIsExportRender());
        op.setInsertDefaultValue(dsout.getInsertDefaultValue() == null ? "" : dsout.getInsertDefaultValue());

        return op;
    }

    /**
     * 解析脚本中的数据源
     *
     * @param script
     * @return
     */
    private String execTdsScript(String script, Map<String, String> mapInParasValues) {
        String result = "";
        try {
            if (StringUtils.isNotEmpty(script) && script.indexOf("$") >= 0) {
                Object obj = this.getScrictDefaultValue(script, mapInParasValues, null);
                //getScriptValue(script, mapInParasValues, null);// 解析数据源
                if (obj != null) {
                    if (obj instanceof Collection<?>) {
                        result = JSONArray.toJSONString(obj);
                        result = correctJsonString(result);
                    } else {
                        result = correctJsonString(obj.toString());
                    }
                }
            } else {
                result = correctJsonString(script);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return result;
    }

    /**
     * 获得录入模式值，并清除脚本解析结果的转义字符
     *
     * @param value
     * @return
     */
    private String getInsertDefaultValue(String script, Map<String, String> mapInParasValues) {
        String result = "";
        try {
            if (script != null && script.length() > 0) {
                Object value = this.getScrictDefaultValue(script, mapInParasValues, null);//this.getScriptValue(script, mapInParasValues, null);
                if (value != null) {
                    if (value instanceof String) {
                        result = value.toString();
                        if (StringUtils.isNotEmpty(result) && result.length() >= 4) {
                            if (result.startsWith("\\\"") && result.endsWith("\\\"")) {
                                result = result.substring(2, result.length() - 2);
                            }
                        }
                    } else {
                        result = value.toString();
                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return result;
    }

    /**
     * 数据源输出转换为表格列
     *
     * @param op
     * @return
     */
    public TdsTableColumn tdsOut2Column(TOutPara op) {
        return tdsOut2Column(op, null, null);
    }

    /**
     * 数据源输出转换为表格列
     *
     * @param op
     * @return
     */
    public TdsTableColumn tdsOut2Column(TOutPara op, Map<String, String> mapInParasValues, Map<String, TOutPara> outMap) {
        TdsTableColumn col = new TdsTableColumn();
        col.setAlias(op.getAlias());
        col.setHeader(op.getName()); // 标题
        if (op.getDataType() == null || "tdsDate".equals(op.getDataType().toString())) {
            col.setDataType("tdsString"); // 数据类型
        } else {
            col.setDataType(op.getDataType().toString());// 数据类型
        }
        col.setAutoSwapRow(op.isAutoSwapRow());
        col.setHidden(!op.getVisible());// 是否隐藏
        col.setRendererFun(rendererString(op.getRendererFun()));
        // col.setRendererFun(op.getRendererFun());
        col.setIsKey(String.valueOf(op.getIsKey()));// 是否是关键列
        col.setIsSumCol(String.valueOf(op.getIsSum())); // 是否合计
        col.setIsSpanCol(String.valueOf(op.getIsSpan()));// 是否数据合并
        if (op.getIsSpan()) {// 合并
            if (op.getSpanType() == null) {
                col.setSpanType(0);
            } else {
                col.setSpanType(op.getSpanType());
            }
            if (col.getSpanType() == 0) {// 相同则合并

            } else if (col.getSpanType() == 2) {// 按照输出列条件分组合并
                String s = op.getSpanScript();
                if (StringUtils.isEmpty(s)) {
                    col.setSpanScript("");
                } else {
                    col.setSpanScript(s);
                }
            } else if (col.getSpanType() == 1) {// 按照脚本合并
                String s = op.getSpanScript();
                if (!"".equals(s)) {
                    col.setSpanScript(rendererString(s));// 合并脚本
                }
            }
        }
        col.setComType(op.getComType());
        col.setLx(op.getLx());
        String reportFormula = rendererString(op.getReportFormula());
        col.setReportFormula(reportFormula);
        col.setIsGroupCol(String.valueOf(op.getIsGroup()));// 是否是分组列
        // 输出参数
        col.setDefaultKeyScript(execTdsScript(op.getDefaultKeyScript(), mapInParasValues));
        col.setDefaultValueScript(execTdsScript(op.getDefaultValueScript(), mapInParasValues));

        col.setInsertDefaultValue(getInsertDefaultValue(op.getInsertDefaultValue(), mapInParasValues));
        col.setOvertip(op.isOvertip());
        if (StringUtils.isNotEmpty(op.getFixed())) {
            col.setFixed(op.getFixed());
        }
        if (op.isAutowidth()) {// 自动
            col.setMinwidth(String.valueOf(op.getWidth()));
            col.setWidth("");
        } else {// 固定宽
            col.setWidth(String.valueOf(op.getWidth()));
            col.setMinwidth("");
        }
        if (op.getMaxlength() != null && op.getMaxlength() >= 0) {// 最大字符限制
            col.setMaxlength(op.getMaxlength());
        }
        col.setAlign(op.getAlign());
        col.setRequired(op.isRequired());
        col.setShowPlaceholder(op.isShowPlaceholder());
        col.setComParams(op.getComParams());
        col.setDataStatusFlag(op.getDataStatusFlag() == null ? 0 : op.getDataStatusFlag());
        col.setIsExportRender(op.getIsExportRender() == null ? 0 : op.getIsExportRender());
        col.setIsShowAsNum(op.getIsShowAsNum() == null ? 0 : op.getIsShowAsNum());
        col.setDisabled(op.getDisabled());
        col.setDisplayMode(op.getDisplayMode());
        col.setCopyAddDefaultMode(op.getCopyAddDefaultMode());
        // 列间公式翻译
        if (StringUtils.isNotEmpty(outMap) && StringUtils.isNotEmpty(op.getReportFormula())) {
            String formula = op.getReportFormula();
            List<String> parmList = AviatorUtils.getParams(formula);
            if (StringUtils.isNotEmpty(parmList)) {
                formula = AviatorUtils.formatOperator(formula) + " ";
                for (String param : parmList) {
                    TOutPara out = outMap.get(param);
                    if (out != null) {
                        // formula.replaceAll(param + " ", out.getName());
                        formula = AviatorUtils.replaceString(formula, param + " ", out.getName() == null ? "" : out.getName());
                    }
                }
            }
            col.setTips("列间公式：" + formula);
        }
        return col;
    }

    /**
     * 渲染函数字符替换
     *
     * @param rendererFun
     * @return
     */
    public String rendererString(String rendererFun) {
        if (rendererFun == null || "".equals(rendererFun)) {// 未设置列Renderer函数
            rendererFun = "";
        } else {// 转义 ",去掉回车符
            rendererFun = rendererFun.replaceAll("\r", " "); // 替换回车
            rendererFun = rendererFun.replaceAll("\n", " "); // 替换回车
            rendererFun = rendererFun.replaceAll("\r\n", " "); // 替换回车
            // rendererFun = rendererFun.replaceAll("'", "\""); // 替换单引号
            rendererFun = rendererFun.replaceAll("\"", "\\\\\"");// 替换 " //
        }
        return rendererFun;
    }

    public String correctJsonString(String str) {
        if (str != null) {
            str = str.replaceAll("\\\\", "\\\\\\\\");
            str = str.replaceAll("\r\n", "<br>");
            str = str.replaceAll("\"", "\\\\\"");
            str = str.replaceAll("'", "\\\\\'");
        }
        return str;
    }

    public List<TOutPara> TdsoutParaToOutParaList(IDataSource ds, List<TdsoutPara> list) {
        List<TOutPara> l = new ArrayList<TOutPara>();
        if (list != null) {
            for (TdsoutPara top : list) {
                TOutPara op = TdsoutParaToTOutPara(ds, top);
                l.add(op);
            }
        }
        return l;
    }

    public List<TInPara> TdsinParaToTInParaList(IDataSource ds, List<TdsinPara> list) {
        List<TInPara> l = new ArrayList<TInPara>();
        if (list != null) {
            for (TdsinPara tip : list) {
                TInPara ip = TdsinParaToTInPara(ds, tip);
                l.add(ip);
            }
        }
        return l;
    }

    private TInPara TdsinParaToTInPara(IDataSource ds, TdsinPara dsinpara) {
        TInPara tip = new TInPara(ds);
        tip.setComponentType(dsinpara.getComponentType());
        if (StringUtils.isEmpty(dsinpara.getDataType())) {
            tip.setDataType(IDataSource.DataType.tdsString);
        } else {
            tip.setDataType(IDataSource.DataType.valueOf(dsinpara.getDataType()));
        }
        tip.setDefaultKeyScript(dsinpara.getDefaultKeyScript());
        tip.setDefaultValueScript(dsinpara.getDefaultValueScript());
        tip.setDisplayed(dsinpara.getDisplay() != null && dsinpara.getDisplay() == 1 ? true : false);
        tip.setCanquery(dsinpara.getIscanquery() != null && dsinpara.getIscanquery() == 1 ? true : false);
        tip.setParaAlias(dsinpara.getParaAlias());
        tip.setParaID(dsinpara.getParaId());
        tip.setParaName(dsinpara.getParaName());
        tip.setRowflag(1);
        tip.setInsertEdit(dsinpara.getInsertEdit() != null && dsinpara.getInsertEdit() == 1 ? true : false);
        tip.setInitValueScript(dsinpara.getInitValueScript());
        tip.setWidth(dsinpara.getWidth() == null ? 0 : dsinpara.getWidth());
        tip.setComParams(dsinpara.getComParams());
        return tip;
    }

    public void IDsToTds(IDataSource ids, TdataSource tds) {
        if (ids != null && ids.getDSAlias() != "") {
            tds.setModuleCode(ids.getModuleCode());
            tds.setTdsname(ids.getDSName());
            tds.setTdsalias(ids.getDSAlias());
            tds.setTdstype(ids.getDSType());
            tds.setTdsclassName(ids.getDSClassName());
            tds.setMemo(ids.getMemo());
            tds.setAllowToSave(ids.getAllowSaved() ? 1 : 0);
            tds.setAllowNull(ids.isAllowNull() ? 1 : 0);
            tds.setCategoryId(ids.getCategoryId());
            tds.setTmSort(ids.getDsSort());
            tds.setTdsQuerySql(ids.getTdsQuerySql());
            tds.setTdsInitSql(ids.getTdsInitSql());
            tds.setTdsInitType(ids.getTdsInitType() == null ? 2 : ids.getTdsInitType());
            tds.setTdsCanEditScript(ids.getTdsCanEditScript());
            tds.setTdsCalScript(ids.getTdsCalScript());
            tds.setTdsAfterEditScript(ids.getTdsAfterEditScript());
            tds.setTdsUpdateSql(ids.getTdsUpdateSql());
            tds.setRegtime(ids.getRegTime());
            tds.setUsed(ids.getUsed() ? 1 : 0);
            tds.setAutoLoad(ids.getAutoLoad() ? 1 : 0);
            tds.setImportDataBase(ids.getImportDataBase() ? 1 : 0);
            tds.setScript(ids.getScript());
            tds.setIsMainDb(ids.isMainDB() ? 1 : 0);
            tds.setExcelAutoCreate(ids.isExcelAutoCreate() ? 1 : 0);
            tds.setDbConnInfoId(ids.getDbConnInfoId());
            tds.setDbTableName(ids.getDbTableName());
            tds.setExcelMultiSheet(ids.isExcelMultiSheet() ? 1 : 0);
            tds.setAdvancedSearch(ids.isAdvancedSearch() ? 1 : 0);
            tds.setFindByKey(ids.getFindByKey() ? 1 : 0);
            tds.setBindTdsAlias(ids.getBindTdsAlias() == null ? "" : ids.getBindTdsAlias());
        }
    }

    /**
     * 返回脚本中涉及到的数据源别名列表
     *
     * @param script 脚本字串
     * @return 数据源别名列表
     * @category 返回脚本中涉及到的数据源别名列表
     */
    public List<String> parseDataSourceAliasList(String script) {
        List<String> list = new ArrayList<String>();
        if (script == null) return list;
        lastError = "";
        String cstr = "[$][_a-zA-Z][_a-zA-Z0-9]*.{1}[_a-zA-Z][_a-zA-Z0-9]*\\({1}";
        Pattern p = Pattern.compile(cstr);
        Matcher m = p.matcher(script);
        while (m.find()) {
            String s = m.group();
            list.add(s.substring(1, s.indexOf('.')));
        }
        p = null;
        m = null;
        return list;
    }

    /**
     * 数据源公式校验
     *
     * @param script   (String) 公式
     * @param queryStr (String) 非自动加载数据源的检索条件
     * @return String
     */
    public String checkJSScript(String script, String queryStr) {
        LinkedHashMap<String, String> result = new LinkedHashMap<String, String>();
        if (script != null && script.length() != 0) {// 有公式
            LinkedHashMap<String, LinkedHashMap<String, String>> queryMap = null;// 检索条件map
            if (queryStr != null && queryStr.length() != 0) {// 有输入参数，需要解析检索条件
                queryMap = new LinkedHashMap<String, LinkedHashMap<String, String>>();
                try {
                    JSONArray jsonArray = JSONArray.parseArray(queryStr); // JSONArray.fromObject(queryStr);
                    for (int i = 0, j = jsonArray.size(); i < j; i++) {
                        JSONObject obj = jsonArray.getJSONObject(i); // JSONObject.fromObject(jsonArray.get(i));
                        String tdsAlias = obj.getString("name");// 数据源别名
                        LinkedHashMap<String, String> inparaMap = new LinkedHashMap<String, String>();
                        JSONArray inParaArray = obj.getJSONArray("data"); // JSONArray.fromObject(obj.get("data"));
                        if (inParaArray != null && inParaArray.size() > 0) {
                            for (int m = 0, n = inParaArray.size(); m < n; m++) {
                                JSONObject jso = inParaArray.getJSONObject(m);// JSONObject.fromObject(inParaArray.get(m));
                                String alias = jso.getString("name");
                                String val = jso.getString("value");
                                inparaMap.put(alias, val);// 记录参数值
                            }
                        }
                        queryMap.put(tdsAlias, inparaMap);
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            LinkedHashMap<String, IDataSource> idsMap = new LinkedHashMap<String, IDataSource>();// 数据map
            StringBuffer unLoadTdsSet = new StringBuffer();// 非自动加载的数据源列表
            List<String> tdsList = parseDataSourceAliasList(script);// 分析数据源
            if (tdsList != null && tdsList.size() > 0) {
                for (String temp : tdsList) {
                    if (idsMap.get(temp) == null) {// 未加载过的数据源
                        IDataSource ids = getDataSource(temp);
                        if (ids != null) {
                            if (ids.getAutoLoad()) {// 自动加载
                                idsMap.put(temp, ids);
                            } else {// 非自动
                                if (queryMap != null) {// 有检索条件，根据检索条件加载数据源
                                    LinkedHashMap<String, String> inparaMap = queryMap.get(temp);// 获取参数列表
                                    if (inparaMap != null && inparaMap.size() > 0) {// 获取到了参数
                                        for (String key : inparaMap.keySet()) {
                                            ids.setInParaByAlias(key, inparaMap.get(key));// 设置输入参数

                                        }
                                    }
                                    ids.load();// 加载数据源
                                    idsMap.put(temp, ids);

                                } else {// 无检索条件，向前台发送检索请求
                                    unLoadTdsSet.append("," + temp);
                                }
                            }
                        }
                    }
                }
            }
            if (unLoadTdsSet.length() > 0) {// 需要输入参数
                result.put("operateResult", "inputData");// 请求输入
                result.put("info", unLoadTdsSet.substring(1));
            } else {
                // TJSEngine jse = new TJSEngine();
                synchronized (this) {
                    if (checkJse == null) {
                        checkJse = new TJSEngine();
                        checkJse.setEnableCached(false);
                    }
                    checkJse.clear();
                    for (String temp : idsMap.keySet()) {
                        checkJse.put(temp, idsMap.get(temp));
                    }
                    String value = "";
                    try {
                        Object obj = checkJse.eval(checkJse.replaceFunction(script));
                        if (obj == null) {
                            value = "";
                        } else {
                            if (obj instanceof Double) {
                                try {
                                    value = Maths.formatNum(((Double) obj).doubleValue());// 处理科学计数法
                                } catch (Exception e) {
                                }
                            } else {
                                value = obj.toString();
                            }
                        }
                    } catch (ScriptException e) {
                        value = "错误信息:" + e.getMessage();
                    }
                    result.put("operateResult", "checkSuccess");// 校验成功
                    result.put("info", value);
                }
            }
        } else {
            result.put("operateResult", "checkSuccess");// 无公式
            result.put("info", "公式为空！");
        }
        String json = JSON.toJSONString(result);
        return json;
    }

    /**
     * 获取输入/输出参数脚本默认值
     *
     * @param script
     * @param mapInParasValues
     * @param publicIdsMap
     * @return
     */
    private Object getScrictDefaultValue(String script, Map<String, String> mapInParasValues, HashMap<String, IDataSource> publicIdsMap) {
        Object result = null;
        if (script != null && script.trim().length() != 0) {// 脚本有效
            Map<String, IDataSource> idsMap = this.loadScriptDs(script, mapInParasValues, publicIdsMap);
            try {
                result = this.getScrictDefaultValue(script, idsMap);
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return result;
    }

    /**
     * 加载公式中的数据源
     *
     * @param script
     * @param mapInParasValues
     * @param publicIdsMap
     * @return
     */
    public Map<String, IDataSource> loadScriptDs(String script, Map<String, String> mapInParasValues, HashMap<String, IDataSource> publicIdsMap) {
        Map<String, IDataSource> idsMap = new HashMap<String, IDataSource>();// 数据map
        if (script != null && script.trim().length() != 0) {// 脚本有效
            script = script.trim();
            List<String> tdsList = parseDataSourceAliasList(script);// 分析数据源
            if (tdsList != null && tdsList.size() > 0) {
                for (String temp : tdsList) {
                    if (idsMap.get(temp) == null) {// 未加载过的数据源
                        IDataSource ids = null;
                        if (publicIdsMap != null) {
                            ids = publicIdsMap.get(temp);// 从公用数据源中获取
                        }
                        if (ids == null) {// 共用数据源中没有，则自行加载数据源
                            ids = getDataSource(temp);
                            if (ids != null) {
                                if (ids.getAutoLoad()) {// 自动加载
                                    idsMap.put(temp, ids);
                                } else {
                                    ids.parseInParaDefaultValue();
                                    if (StringUtils.isNotEmpty(mapInParasValues)) {
                                        for (String inparaKey : mapInParasValues.keySet()) {
                                            TInPara inPara = ids.getInParaByAlias(inparaKey);
                                            if (inPara != null) {
                                                ids.setInParaByAlias(inparaKey, mapInParasValues.get(inparaKey));
                                            }
                                        }
                                    }
                                    ids.load();
                                    idsMap.put(temp, ids);
                                }
                                if (publicIdsMap != null) {
                                    publicIdsMap.put(temp, ids);// 将新加载的数据源添加到公共数据源map中
                                }
                            }
                        } else {
                            idsMap.put(temp, ids);
                        }
                    }
                }
            }
        }
        return idsMap;
    }

    /**
     * 获取脚本执行后的值（不推荐，可能存在内存泄漏)
     *
     * @param script       (String) 脚本
     * @param mapInParas   输入参数默认值
     * @param publicIdsMap (HashMap<String, IDataSource>) 脚本中用到的已经加载好的数据源
     *                     （map中有，会使用map中已加载好的数据源；如果map中没有，函数会现加载数据源然后put到该map中。
     *                     用于批量执行脚本时，相关数据源只加载一次(执行完该函数，map中会存放脚本中用的数据源,以供后续脚本直接调用)）
     *                     key:数据源别名 value:数据源
     * @return
     * @category 获取脚本执行后的值
     */
    @Deprecated
    public Object getScriptValue(String script, Map<String, String> mapInParasValues, HashMap<String, IDataSource> publicIdsMap) {
        Object result = null;
        if (script != null && script.trim().length() != 0) {// 脚本有效
            Map<String, IDataSource> idsMap = this.loadScriptDs(script, mapInParasValues, publicIdsMap);
//            String evalKey = ScriptEngineUtils.getMd5(script);
//            CompiledScriptEngine cse = evalMap.get(evalKey);
//            if (cse == null) {
//                String calScript = ScriptEngineUtils.replaceFunctionWithOutIIF(script);// 计算脚本
//                calScript = ScriptEngineUtils.clearScript(calScript);// 去掉 $ 符号
//                calScript = CustomFun.getCuntomFunScript() + " " + calScript;
//                cse = new CompiledScriptEngine(calScript);
//
//                if (cse.getScriptException() == null) {
//                    evalMap.put(evalKey, cse);
//                } else {// 脚本编译错误
//                    return null;
//                }
//            }
            try {
                if (this.cse == null) {
                    this.cse = new CompiledScriptEngine();
                    //log.info("初始化==========");
                }
                //CompiledScriptEngine cse = new CompiledScriptEngine();
                String calScript = ScriptEngineUtils.replaceFunctionWithOutIIF(script);// 计算脚本
                calScript = ScriptEngineUtils.clearScript(calScript);// 去掉 $ 符号
                calScript = CustomFun.getCuntomFunScript(calScript) + " " + calScript;
                //log.info(calScript);
                Map<String, Object> mp = new HashMap<String, Object>();
                if (idsMap != null && idsMap.size() > 0) {
                    mp.putAll(idsMap);
                }
                result = cse.eval(calScript, mp);
            } catch (ScriptException e) {
                if (!isNotPrintLog) {
                    log.error("脚本计算错误：" + script, e);
                }
            }
//			TJSEngine jse = new TJSEngine();
//			jse.setEnableCached(false);// 暂时这么处理，后期优化
//			for (Entry<String, IDataSource> temp : idsMap.entrySet()) {
//				jse.put(temp.getKey(), temp.getValue());
//			}
//			try {
//				result = jse.eval(jse.replaceFunctionWithOutIIF(script));// 替换脚本（不替换IIF）
//			} catch (ScriptException e) {
//				if (!isNotPrintLog) {
//					log.error("", e);
//				}
//			}
        }
        return result;
    }

    /**
     * 根据条件json字符串返回数据
     *
     * @param jsonData :条件字符串
     * @param type     :json or xml
     * @return 数据字符串json or xml
     * @category 根据条件json字符串返回数据
     */
    public String getData(String jsonData, String type) {
        return this.getData(jsonData, type, false);
    }

    /**
     * 根据条件json字符串返回数据**************
     *
     * @param jsonData 条件字符串
     * @param type     json or xml
     * @param errInfo  是否显示错误信息
     * @return
     */
    public String getData(IDataSource ds, String jsonData, String type, boolean errInfo) {
        JSONArray dsArray = JSONArray.parseArray(jsonData);// JSONArray.fromObject(jsonData);
        return this.getData(ds, dsArray, type, false, false, errInfo, null, null, null,"");
    }

    /**
     * 根据条件json字符串返回数据**************
     *
     * @param jsonData 条件字符串
     * @param type     json or xml
     * @param errInfo  是否显示错误信息
     * @return
     */
    public String getData(String jsonData, String type, boolean errInfo) {
        return this.getData(null, jsonData, type, errInfo);
    }

    /**
     * 根据条件json字符串返回数据
     *
     * @param jsonData
     * @param type
     * @return 数据字符串json格式
     * @category 根据条件json字符串返回数据
     */
    public String getJson(String jsonData) {
        return this.getData(jsonData, "json");
    }

    public String getJson(String jsonData, boolean errInfo) {
        return this.getData(jsonData, "json", errInfo);
    }

    public String getJsonData(String jsonData) {
        return this.getData(jsonData, "jsonData", false);
    }

    /**
     * 递归数据源内部引用的全部数据源(hm中也包含该数据源)
     *
     * @param tdsAlias (String) 要递归的数据源别名
     * @param hm       (HashMap<String,IDataSource>) 存放查找到的数据的HashMap
     * @category 递归数据源内部引用的全部数据源
     */
    private void getInnerDataSource(String tdsAlias, HashMap<String, IDataSource> hm) {
        if (tdsAlias != null && tdsAlias.length() != 0 && hm != null) {// 输入的数据源别名有效
            IDataSource ids = getDataSource(tdsAlias);
            if (ids != null) {// 获得到了数据源
                hm.put(tdsAlias, ids);// 存放该数据源
                Set<String> tdsAliasSet = new HashSet<String>();// 数据源别名set
                // 用于去掉重复的别名
                String Script = ids.getScript();// 获得脚本
                if (Script != null && Script.length() != 0) {// 有脚本
                    List<String> ScriptInnerDSList = parseDataSourceAliasList(Script);// 获得脚本中包含的数据源别名列表
                    if (ScriptInnerDSList != null && ScriptInnerDSList.size() > 0) {// 脚本中包含别的数据源
                        for (String ScriptInnerAlias : ScriptInnerDSList) {
                            if (ScriptInnerAlias != null && ScriptInnerAlias.length() != 0) {// 数据源别名有效
                                tdsAliasSet.add(ScriptInnerAlias);// 设置数据源别名
                            }
                        }
                    }
                }
                List<TInPara> iParaList = ids.getInParaList();// 获得输入参数列表
                if (iParaList != null && iParaList.size() != 0) {
                    for (TInPara temp : iParaList) {
                        String defaultKeyScript = temp.getDefaultKeyScript();// 获得初始化值
                        if (defaultKeyScript != null && defaultKeyScript.length() != 0) {// 有初始化值
                            List<String> DKScriptInnerDSList = parseDataSourceAliasList(defaultKeyScript);// 获得脚本中包含的数据源别名列表
                            if (DKScriptInnerDSList != null && DKScriptInnerDSList.size() > 0) {// 脚本中包含别的数据源
                                for (String ScriptInnerAlias : DKScriptInnerDSList) {
                                    if (ScriptInnerAlias != null && ScriptInnerAlias.length() != 0) {// 数据源别名有效
                                        tdsAliasSet.add(ScriptInnerAlias);// 设置数据源别名
                                    }

                                }

                            }

                        }
                        String defaultValueScript = temp.getDefaultValueScript();// 获得初始化名称
                        if (defaultValueScript != null && defaultValueScript.length() != 0) {// 有初始化名称
                            List<String> DVScriptInnerDSList = parseDataSourceAliasList(defaultValueScript);// 获得脚本中包含的数据源别名列表
                            if (DVScriptInnerDSList != null && DVScriptInnerDSList.size() > 0) {// 脚本中包含别的数据源
                                for (String ScriptInnerAlias : DVScriptInnerDSList) {
                                    if (ScriptInnerAlias != null && ScriptInnerAlias.length() != 0) {// 数据源别名有效
                                        tdsAliasSet.add(ScriptInnerAlias);// 设置数据源别名
                                    }
                                }
                            }
                        }
                    }
                }
                if (tdsAliasSet.size() > 0) {// 该数据源中包含其他数据源
                    for (String innerAlias : tdsAliasSet) {
                        if (innerAlias != null && innerAlias.length() != 0) {// 数据源别名有效
                            IDataSource ScriptAliasTempIDS = hm.get(innerAlias);// 检查是否已经获取到过该数据源
                            if (ScriptAliasTempIDS == null) {// 没有获取到过
                                getInnerDataSource(innerAlias, hm);// 递归该数据源
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获得数据源内部引用的全部数据源列表(包括该数据源)
     *
     * @param tdsAlias (String) 要检查的数据源别名
     * @return List<IDataSource> 数据源内部引用的全部数据源列表
     * @category 获得数据源内部引用的全部数据源列表
     */
    public List<IDataSource> getInnerDataSource(String tdsAlias) {
        List<IDataSource> result = new ArrayList<IDataSource>();
        if (tdsAlias != null && tdsAlias.length() != 0) {// 输入的数据源别名有效
            HashMap<String, IDataSource> hm = new HashMap<String, IDataSource>();
            getInnerDataSource(tdsAlias, hm);
            if (hm.size() > 0) {// 有内部数据源
                for (IDataSource temp : hm.values()) {
                    result.add(temp);
                }
            }
        }
        return result;
    }

    /**
     * 根据检索条件加载数据源
     *
     * @param ids         数据源
     * @param tdsQueryStr 检索条件
     */
    public void loadTdsByQueryString(IDataSource ids, String tdsQueryStr) {
        if (ids != null && ids.getDSAlias() != null) {
            try {
                if (tdsQueryStr == null || tdsQueryStr.length() == 0) {
                    tdsQueryStr = "[{name:'" + ids.getDSAlias() + "',pageInfo:{page:1,pageSize:50},data:[]}]";// 使用空条件加载
                }
                JSONArray dsArray = JSONArray.parseArray(tdsQueryStr);// JSONArray.fromObject(tdsQueryStr);
                for (int i = 0; i < dsArray.size(); i++) {
                    JSONObject dsObj = dsArray.getJSONObject(i);// JSONObject.fromObject(dsArray.get(i));
                    String dsCode = dsObj.getString("name");// 数据源别名
                    if (dsCode != null && dsCode.equals(ids.getDSAlias())) {// 和数据源对应的检索条件

                        int page = 1;
                        int pageSize = 0;
                        JSONObject pageInfo = dsObj.getJSONObject("pageInfo");// 分页信息
                        if (pageInfo != null) {
                            page = pageInfo.getInteger("page");// 当前页
                            pageSize = pageInfo.getInteger("pageSize");// 每页记录数
                        }
                        JSONArray dataArray = dsObj.getJSONArray("data");
                        if (dataArray != null && dataArray.size() > 0) {
                            for (int j = 0; j < dataArray.size(); j++) {
                                JSONObject dataObj = dataArray.getJSONObject(j);// JSONObject.fromObject(dataArray.get(j));
                                String paraAlias = dataObj.getString("name");// 参数
                                Object oValue = dataObj.get("value");// 参数值
                                ids.setInParaByAlias(paraAlias, oValue);// 输入参数
                            }
                        }
                        loadDsData(ids, "", page, pageSize, null, null, null);// 加载数据源
                        break;
                    }
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    /**
     * 加载数据源数据（如果有数据定位条件则只加载定位数据）
     *
     * @param ids               数据源
     * @param filter
     * @param page              页码
     * @param pageSize          每页数量
     * @param tdsSearchMoreTxt
     * @param tdsSearchMoreData
     * @param tdsSortMoreData
     * @category 加载数据源数据（如果有数据定位条件则只加载定位数据）
     */
    private void loadDsData(IDataSource ids, String filter, int page, int pageSize, String tdsSearchMoreTxt, List<TdsSearchMoreDataVo> tdsSearchMoreData, List<TdsSortMoreDataVo> tdsSortMoreData) {
        if (ids != null) {
            if (tdsSearchMoreTxt != null && tdsSearchMoreTxt.length() > 0) {
                tdsSearchMoreTxt = "if(" + tdsSearchMoreTxt + ",true,false)";
            }
            if (dataLocatorForTDataSource != null && dataLocatorForTDataSource.length() != 0) {// 需要进行数据定位
                ids.load();// 数据全部加载，用于检索
                // List<TRow> keepList = new ArrayList<TRow>();
                HashMap<Integer, TRow> dataLocatorTrow = getDataLocatorTrow(ids, dataLocatorForTDataSource);// 获取定位列
                // if(dataLocatorTrow!=null){
                // keepList.addAll(dataLocatorTrow);
                // }

                if (ids.getDSClassName() != null && ids.getDSClassName().equals("tds.TDSExcel")) {// Excel数据源特殊处理
                    // Excel数据源由于没有列名,如果过滤显示则无法看懂数据
                    if (dataLocatorTrow.size() == 0) {// 未检索到记录
                        ids.getDataStore().getTRows().clear();// 清理数据
                    }
                } else {
                    if (dataLocatorTrow.size() > 0) {// 检索到了记录
                        List<TRow> clearList = new ArrayList<TRow>();
                        for (TRow temp : ids.getDataStore().getTRows()) {
                            if (dataLocatorTrow.containsKey(temp.getRowId())) {// 有定位，需要保留

                            } else {
                                clearList.add(temp);// 无定位，移除
                            }
                        }
                        if (clearList.size() > 0) {
                            ids.getDataStore().getTRows().removeAll(clearList);// 移除无定位数据
                        }
                    } else {
                        ids.getDataStore().getTRows().clear();// 清理全部数据
                    }
                }
            } else {
                // 选择的过滤条件或排序
                if ((tdsSearchMoreTxt != null && tdsSearchMoreTxt.length() > 0) || (tdsSortMoreData != null && tdsSortMoreData.size() > 0)) {
                    ids.load();
                    if (ids != null && ids.getRowCount() > 0) {// 输入参数查询到了数据
                        // 过滤和排序
                        ids = getTdsDataMoreSearch(ids, filter, page, pageSize, tdsSearchMoreTxt, tdsSearchMoreData, tdsSortMoreData);
                        // 重新进行分页
                        TdsTools.paging(ids, page, pageSize);
                    }
                } else {

                    if (pageSize > 0) {// 分页模式
                        ids.load(page, pageSize);
                    } else if (pageSize == -1) {// 不检索数据
                        return;
                    } else {// 不分页
                        ids.load();
                    }

                }
                if (filter != null && filter.length() > 0) {
                    ids.filter(filter);
                }

            }
        }
    }

    /**
     * 对数据进行过滤或排序
     *
     * @param ids               数据
     * @param filter
     * @param page              页码
     * @param pageSize          每页 显示条数
     * @param tdsSearchMoreTxt  过滤数据规则
     * @param tdsSearchMoreData 过滤数据的参数
     * @return
     */
    public IDataSource getTdsDataMoreSearch(IDataSource ids, String filter, int page, int pageSize, String tdsSearchMoreTxt, List<TdsSearchMoreDataVo> tdsSearchMoreData, List<TdsSortMoreDataVo> tdsSortMortData) {
        List<TOutPara> outParaList = new ArrayList<TOutPara>();
        outParaList = ids.getOutParaList();
        HashMap<String, TOutPara> mapPara = new HashMap<String, TOutPara>();
        if (StringUtils.isNotEmpty(outParaList)) {
            for (TOutPara temp : outParaList) {
                mapPara.put(temp.getAlias(), temp);
            }
        }
        // 过滤
        if (tdsSearchMoreTxt != null && tdsSearchMoreTxt.length() > 0) {
            List<TRow> newList = ids
                    // 生成流
                    .getDataStore().getTRows().stream()
                    // 过滤
                    .filter(b -> idsDataFilter((TRow) b, tdsSearchMoreTxt, tdsSearchMoreData, mapPara))
                    // 流 -> List
                    .collect(Collectors.toCollection(ArrayList<TRow>::new));
            ids.getDataStore().setTRows(newList);
        }
        // 排序
        if (tdsSortMortData != null && tdsSortMortData.size() > 0) {
            List<TRow> newList = ids.getDataStore().getTRows().stream().sorted((tr, trNext) -> {
                return idsDataSort(tr, trNext, tdsSortMortData, mapPara);

            }).collect(Collectors.toCollection(ArrayList<TRow>::new));
            ids.getDataStore().setTRows(newList);
        }
        return ids;
    }

    /**
     * 过滤数据时判断是否是有效数据
     *
     * @param tr                行数据
     * @param tdsSearchMoreTxt  过滤数据规则
     * @param tdsSearchMoreData 过滤数据的参数
     * @param mapPara           输出参数map
     * @return
     */
    private boolean idsDataFilter(TRow tr, String tdsSearchMoreTxt, List<TdsSearchMoreDataVo> tdsSearchMoreData, HashMap<String, TOutPara> mapPara) {
        Boolean result = true;
        LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();// 过滤条件的参数
        if (tdsSearchMoreData != null && tdsSearchMoreData.size() > 0) {
            for (TdsSearchMoreDataVo temp : tdsSearchMoreData) {
                List<TdsSearchMoreChildData> tdsSearchPara = temp.getData();
                if (tdsSearchPara != null && tdsSearchPara.size() > 0) {
                    for (TdsSearchMoreChildData tempPara : tdsSearchPara) {
                        paramMap.put(tempPara.getParamCode(), tempPara.getParamCode());
                    }
                }
            }
        }
        Map<String, Object> valueMap = new HashMap<String, Object>();
        for (Entry<String, String> entry : paramMap.entrySet()) {
            String paramCode = entry.getKey();
            Object value = tr.getByAlias(paramCode);
            if (value != null) {
                if (mapPara != null && mapPara.containsKey(paramCode)) {
                    String paramType = "tdsString";
                    paramType = mapPara.get(paramCode).getDataType() == null ? "tdsString" : mapPara.get(paramCode).getDataType().toString();
                    if ("tdsDouble".equals(paramType)) {

                    } else {
                        paramType = "tdsString";
                    }
//		    		if("tdsString".equals(paramType)) {
//		    			value = "'" + TdsTools.castColumnValue(paramType, value)+"'";
//		    		}else {
                    value = TdsTools.castColumnValue(paramType, value);
//		    		}
                }
            }
            valueMap.put(paramCode, value);
        }

        AviatorResult result1 = AviatorUtils.execute(tdsSearchMoreTxt, valueMap, null);
        Object value1 = result1.getResult(); // 计算结果
        result = Boolean.valueOf(value1.toString());
        return result;
    }

    /**
     * 判断数据排序规则
     *
     * @param tr              当前行
     * @param trNext          下一行
     * @param tdsSortMoreData 排序规则
     * @param mapPara         输出参数map
     * @return
     */
    private int idsDataSort(TRow tr, TRow trNext, List<TdsSortMoreDataVo> tdsSortMoreData, HashMap<String, TOutPara> mapPara) {
        int result = 1;
        if (StringUtils.isNotEmpty(tdsSortMoreData)) {
            for (int i = 0; i < tdsSortMoreData.size(); i++) {
                TdsSortMoreDataVo temp = tdsSortMoreData.get(i);
                String paramCode = temp.getParamAlias();
                String paramType = "tdsString";
                int sortType = temp.getSortType();
                if (mapPara != null && mapPara.containsKey(paramCode)) {
                    paramType = mapPara.get(paramCode).getDataType() == null ? "tdsString" : mapPara.get(paramCode).getDataType().toString();
                    Object value = tr.getByAlias(paramCode);
                    value = TdsTools.castColumnValue(paramType, value);
                    Object valueNext = trNext.getByAlias(paramCode);
                    valueNext = TdsTools.castColumnValue(paramType, valueNext);
                    int tmSort = 1;
                    if (sortType == 1) {// 升序
                        if ("tdsString".equals(paramType)) {
                            String value1 = (String) value == null ? "" : (String) value;
                            String valueNext1 = (String) valueNext;
                            tmSort = TdsTools.stringCompare(value1, valueNext1);
//							tmSort = value1.compareTo(valueNext1);

                        } else {
                            BigDecimal value1 = new BigDecimal(value.toString().toCharArray());
                            BigDecimal valueNext1 = new BigDecimal(valueNext.toString().toCharArray());
                            tmSort = value1.compareTo(valueNext1);
                        }
                        if (tmSort == 0) {
                        } else {
                            result = tmSort;
                            i = tdsSortMoreData.size();
                        }
                    } else {
                        if ("tdsString".equals(paramType)) {
                            String value1 = (String) value == null ? "" : (String) value;
                            String valueNext1 = (String) valueNext;
//							tmSort = valueNext1.compareTo(value1);
                            tmSort = TdsTools.stringCompare(valueNext1, value1);

                        } else {
                            BigDecimal value1 = new BigDecimal(value.toString().toCharArray());
                            ;
                            BigDecimal valueNext1 = new BigDecimal(valueNext.toString().toCharArray());
                            ;
                            tmSort = valueNext1.compareTo(value1);
                        }
                        if (tmSort == 0) {
                        } else {
                            result = tmSort;
                            i = tdsSortMoreData.size();
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 根据数据定位获取数据源中的定位记录
     *
     * @param ids
     * @param dataLocatorForTDataSource
     * @return
     * @category 介绍函数的用途及注意事项
     */
    @SuppressWarnings("unchecked")
    private HashMap<Integer, TRow> getDataLocatorTrow(IDataSource ids, String dataLocatorForTDataSource) {
        HashMap<Integer, TRow> resultMap = new HashMap<Integer, TRow>();
        if (ids != null && dataLocatorForTDataSource != null && dataLocatorForTDataSource.length() != 0) {
            String[] dataLocatorForTDataSourceArr = dataLocatorForTDataSource.split("\\$");
            if (dataLocatorForTDataSourceArr != null && dataLocatorForTDataSourceArr.length > 0) {
                for (String temp : dataLocatorForTDataSourceArr) {
                    if (temp != null && temp.length() != 0) {
                        // log.info(temp);
                        String dataLocatorAlias = null;// 定位的列
                        List<TRow> result = new ArrayList<TRow>();

                        // String baseRegex =
                        // "\\$([a-zA-Z_]+[\\w]*)(\\.[^\\)]+\\))(\\.[^\\)]+\\))?";//
                        // group1=数据源别名
                        // group2=第一个方法
                        // group3=第二个方法
                        // 例：$aa.get(1).gets(0);

                        String[] a = temp.split("(?<=\\w|\\))\\.(?=\\w*\\()");
                        String dsAlias = null;// 数据源别名
                        String function1 = null;// 第一个方法
                        String function2 = null;// 第二个方法
                        if (a != null) {
                            if (a.length > 0) {
                                dsAlias = a[0];
                            }
                            if (a.length > 1) {
                                function1 = "." + a[1];
                            }
                            if (a.length > 2) {
                                function2 = "." + a[2];
                            }
                        }
                        if (dsAlias != null && ids.getDSAlias() != null && dsAlias.equals(ids.getDSAlias())) {// 数据源别名必须一致
                            if (function1 != null) {
                                // TJSEngine jse = new TJSEngine();
                                synchronized (this) {
                                    if (localJse == null) {
                                        localJse = new TJSEngine();
                                        localJse.setEnableCached(false);
                                    }
                                    localJse.clear();
                                    localJse.put(ids.getDSAlias(), ids);// 设置参数
                                    try {
                                        Object obj = localJse.eval("$" + ids.getDSAlias() + function1);
                                        if (obj != null) {
                                            if (obj instanceof TRow) {// 获取到了记录
                                                result.add((TRow) obj);
                                            } else if (obj instanceof IDataSource) {// 获取到了数据源
                                                List<TRow> trows = ((IDataSource) obj).getDataStore().getTRows();
                                                if (trows != null) {
                                                    result.addAll(trows);// 获取记录
                                                }
                                            } else if (obj instanceof List) {// 获取到了记录列表
                                                try {
                                                    result.addAll((List<TRow>) obj);// 获取记录
                                                } catch (Exception e) {

                                                }
                                            }
                                        }
                                    } catch (ScriptException e) {
                                        log.error("", e);
                                    }
                                }
                                if (result.size() == 0 && function1.indexOf("(") != -1) {// 没找到记录
                                    String function1Name = function1.substring(0, function1.indexOf("("));// 函数名
                                    String function1Body = function1.substring(function1.indexOf("(") + 1, function1.length() - 1);// 函数体
                                    if (function1Name != null && function1Name.length() != 0 && function1Body != null && function1Body.length() != 0) {// 方法获取正确
                                        String[] function1Param = function1Body.split(",");// 获取方法一的参数
                                        if (function1Param != null && function1Param.length >= 2) {// 参数为两个以上
                                            function1Param[0] = function1Param[0].trim();// 去空格
                                            if (function1Name.startsWith(".find")) {// find类的方法
                                                try {
                                                    TRow findObj = ids.find(function1Param[0].substring(1, function1Param[0].length() - 1));// 查找记录（去掉参数1两侧的双引号）
                                                    if (findObj != null) {
                                                        result.add(findObj);// 获取记录
                                                    }
                                                } catch (Exception e) {
                                                }
                                            } else {// get类的方法
                                                Integer rowId = null;
                                                try {
                                                    rowId = Integer.parseInt(function1Param[0]);
                                                } catch (Exception e) {
                                                }
                                                if (rowId != null) {
                                                    TRow getObj = ids.get(rowId.intValue());
                                                    if (getObj != null) {
                                                        result.add(getObj);// 获取记录
                                                    }
                                                }
                                            }
                                            if (result.size() > 0) {// 查找到了记录
                                                dataLocatorAlias = getDataLocatorAlias(result.get(0), function1Param[1]);
                                            }
                                        }
                                    }
                                }
                            }

                            if (function2 != null && result.size() > 0 && (dataLocatorAlias == null || dataLocatorAlias.length() == 0)) {// 找到了记录,未找到定位列
                                String function2Name = function2.substring(0, function2.indexOf("("));// 函数名
                                String function2Body = function2.substring(function2.indexOf("(") + 1, function2.length() - 1);// 函数体

                                if (function2Name != null && function2Name.length() != 0 && function2Body != null && function2Body.length() != 0) {// 方法获取正确
                                    dataLocatorAlias = getDataLocatorAlias(result.get(0), function2Body);// 方法2只能有一个参数
                                }
                            }

                            if (result.size() > 0) {// 找到了记录
                                if (dataLocatorAlias == null || dataLocatorAlias.length() == 0) {// 没找到定位列
                                    dataLocatorAlias = "？？？？？";// 无单元格定位，只定位行
                                }
                                for (TRow tempTr : result) {
                                    TRow resultTemp = resultMap.get(tempTr.getRowId());// 检索重复项
                                    if (resultTemp == null) {
                                        resultTemp = tempTr;
                                        resultMap.put(resultTemp.getRowId(), resultTemp);// 没有重复项则添加到map
                                    }
                                    if (resultTemp.getHighLightCellAlias() != null && resultTemp.getHighLightCellAlias().length() != 0) {// 已经有了高亮字段则最近高亮字段
                                        resultTemp.setHighLightCellAlias(resultTemp.getHighLightCellAlias() + "," + dataLocatorAlias);
                                    } else {// 没有高亮字段则设置高亮
                                        resultTemp.setHighLightCellAlias(dataLocatorAlias);
                                    }
                                }
                                // resultAll.addAll(result);
                            }
                        }
                    }
                }
                if (ids.getDataStore() != null) {
                    ids.getDataStore().setFilter("");// 由于可能存在filter函数，使得filter被赋值，这里需要将filter清空，防止后续加载记录时记录被2次过滤
                }
            }
        }
        return resultMap;
    }

    /**
     * 获取数据定位的列别名
     *
     * @param row   (获取数据定位的列别名) 记录
     * @param param (String) 参数 例："日期" 或 1
     * @return String 列别名
     * @category 获取数据定位的列别名
     */
    private String getDataLocatorAlias(TRow row, String param) {
        String result = "";
        if (row != null && param != null && param.length() != 0) {// 参数有效
            param = param.trim();// 去空格
            if (param.startsWith("\"") || param.startsWith("'")) {// string类型
                try {
                    result = row.getCellAlias(param.substring(1, param.length() - 1));// 去掉两边的引号
                } catch (Exception e) {

                }
            } else {
                Integer cellId = null;
                try {
                    cellId = Integer.parseInt(param);
                } catch (Exception e) {
                }
                if (cellId != null) {
                    result = row.getCellAlias(cellId.intValue());
                }
            }
        }
        return result;
    }

    /**
     * 数据源公式汉化函数
     *
     * @param tdsFormula
     * @return
     */
    public String tdsFormulaChinesization(String tdsFormula) {
        String value = "";
        try {
            // 取出公式中所有数据源别名
            List<String> aliasList = this.parseDataSourceAliasList(tdsFormula);
            // 数据源map
            LinkedHashMap<String, IDataSource> idsMap = new LinkedHashMap<String, IDataSource>();
            if (aliasList != null && aliasList.size() > 0) {
                value = tdsFormula;// 初始化返回值
                for (String temp : aliasList) {
                    if (idsMap.get(temp) == null) {// 未加载过的数据源
                        IDataSource ids = this.getDataSource(temp);
                        if (ids != null) {
                            idsMap.put(temp, ids);
                            // 替换别名 为 数据源中文名称
                            value = value.replaceAll(("\\$" + temp), ("[" + ids.getDSName() + "]"));
                        }
                    }
                }
            }

        } catch (Exception ex) {
            log.error("", ex);
        }
        return value;
    }

    /**
     * 获得数据源报表信息
     *
     * @param dsAlias 数据源别名
     * @return
     */
    public TdstableInfo getTDSTableInfo(String dsAlias) {
        TdstableInfo tdsTable = null;
        TdsTableInfoWithScript tdsTableInfoWithScript = null;
        try {

            tdsTable = tdsCached.getTdsTableInfo(dsAlias); // 从内存中获取
            if (tdsTable == null) {
                tdsTable = SpringUtils.getBean(IDataSourceService.class).getTDSTableInfo(dsAlias);
                tdsCached.putTdsTableInfo(dsAlias, tdsTable);
            }

            if (tdsTable != null) {
                tdsTableInfoWithScript = ObjUtils.copyTo(tdsTable, TdsTableInfoWithScript.class);
            }

            // 获取脚本
            TdsScriptEntity scriptByTds = SpringUtils.getBean(IDataSourceService.class).getScriptByTds(dsAlias);
            if (ObjUtils.notEmpty(scriptByTds)) {
                if (tdsTableInfoWithScript == null) {
                    tdsTableInfoWithScript = new TdsTableInfoWithScript();
                }
                tdsTableInfoWithScript.setTdsInitSql(scriptByTds.getTdsInitSql());
                tdsTableInfoWithScript.setTdsCalScript(scriptByTds.getTdsCalScript());
                tdsTableInfoWithScript.setTdsCanEditScript(scriptByTds.getTdsCanEditScript());
                tdsTableInfoWithScript.setTdsAfterEditScript(scriptByTds.getTdsAfterEditScript());
                tdsTableInfoWithScript.setTdsCellclickFun(scriptByTds.getTdsCellclickFun());
                tdsTableInfoWithScript.setStartTableScript(scriptByTds.getStartTableScript());
            }

        } catch (Exception e) {
            log.error("", e);
        }
        return tdsTableInfoWithScript;
    }

    /**
     * 更新数据源报表信息
     *
     * @param tdsTable
     * @return
     */
    public boolean updateTDSTableInfo(TdstableInfo tdsTable) {
        boolean bln = false;
        try {
            /*
             * EntityDao<TdstableInfo> ed = new EntityDao<TdstableInfo>(TdstableInfo.class);
             * bln = ed.updateById(tdsTable);
             */
        } catch (Exception e) {
            log.error("", e);
        }
        return bln;
    }

    /**
     * 添加数据源报表信息
     *
     * @param tdsTable
     * @return
     */
    public boolean insertTDSTableInfo(TdstableInfo tdsTable) {
        boolean bln = false;
        try {
            /*
             * EntityDao<TdstableInfo> ed = new EntityDao<TdstableInfo>(TdstableInfo.class);
             * bln = ed.insert(tdsTable);
             */
        } catch (Exception e) {
            log.error("", e);
        }
        return bln;
    }

    /**
     * 更新数据源报表信息
     *
     * @param tdsTable
     * @return
     */
    public boolean updateTdsSummary(List<TdstableSummary> list) {
        boolean bln = false;
        try {
            /*
             * EntityDao<TdstableSummary> ed = new
             * EntityDao<TdstableSummary>(TdstableSummary.class); bln = ed.update(list);
             */
        } catch (Exception e) {
            log.error("", e);
        }
        return bln;
    }

    /**
     * 添加数据源报表信息
     *
     * @param tdsTable
     * @return
     */
    public boolean insertTdsSummary(List<TdstableSummary> list) {
        boolean bln = false;
        try {
            /*
             * EntityDao<TdstableSummary> ed = new
             * EntityDao<TdstableSummary>(TdstableSummary.class); bln = ed.insert(list);
             */
        } catch (Exception e) {
            log.error("", e);
        }
        return bln;
    }

    /**
     * 获得数据源报表摘要信息
     *
     * @param dsAlias 数据源别名
     * @return
     */
    public List<TdstableSummary> getTDSTableSummary(String dsAlias) {
        List<TdstableSummary> list = null;
        /*
         * try { EntityDao<TdstableSummary> ed = new
         * EntityDao<TdstableSummary>(TdstableSummary.class); list =
         * ed.findByWhereString("tdsAlias='" + dsAlias +
         * "' and used =1  order by row,col"); } catch (Exception e) { log.error("",e);
         * }
         */
        return list;
    }

    /**
     * 获得数据源报表摘要信息
     *
     * @param dsAlias 数据源别名
     * @return
     */
    public String getTDSTableSummaryJson(String dsAlias) {
        String json = "";
        try {
            List<TdstableSummary> list = this.getTDSTableSummary(dsAlias);
            if (list != null && list.size() > 0) {
                int row = 1;
                int col = 0;
                String s = "";
                List<String> listTemp = new ArrayList<String>();
                for (TdstableSummary tdstableSummary : list) {
                    String s1 = JSON.toJSONString(tdstableSummary);// JsonUtil.getJsonfromObject(tdstableSummary);
                    if (row != tdstableSummary.getTrow()) {
                        listTemp.add(s.substring(0, s.length() - 1));
                        s = s1 + ",";
                    } else {
                        s += s1 + ",";
                    }
                    row = tdstableSummary.getTrow();
                    if (col < tdstableSummary.getTcol()) {
                        col = tdstableSummary.getTcol();
                    }
                }
                listTemp.add(s.substring(0, s.length() - 1));
                for (String s2 : listTemp) {
                    json += "[" + s2 + "],";
                }
                json = "{table:{row:" + row + ",col:" + col + "},datas:[" + json.substring(0, json.length() - 1) + "]}";
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return json;
    }

    /**
     * 获得数据源报表分组信息信息
     *
     * @param dsAlias 数据源别名
     * @return
     */
    public String getTDSTableGroup(String dsAlias) {
        String json = "";
        try {
            List<String> list_key = new ArrayList<String>();
            List<String> list_group = new ArrayList<String>();
            String groupId = "";
            List<TdsoutPara> list = this.getOutParaList(dsAlias);
            if (list != null && list.size() > 0) {
                for (TdsoutPara tdsoutPara : list) {
                    String paraAlias = tdsoutPara.getParaAlias();
                    Integer isGroup = tdsoutPara.getIsGroup();
                    if (isGroup == null) isGroup = 0;
                    if (tdsoutPara.getIskey() == 1) {// 关键列
                        list_key.add(paraAlias);
                    }
                    if (isGroup == 1 && tdsoutPara.getIskey() == 1 && "".equals(groupId)) {
                        groupId = paraAlias; // 分组主ID
                    }
                    if (isGroup == 1 && tdsoutPara.getIskey() != 1 && tdsoutPara.getVisible() == 1) {// 分组成员
                        list_group.add(paraAlias);
                    }
                }
                if (list_key.size() > 0 && groupId.length() > 0 && list_group.size() > 0) {
                    json += "groupId:'" + groupId + "',";
                    String s = "";
                    for (int i = 0; i < list_group.size(); i++) {
                        s += "'" + list_group.get(i) + "',";
                    }
                    json += "groups:[" + s.substring(0, s.length() - 1) + "],";
                    s = "";
                    for (int i = 0; i < list_key.size(); i++) {
                        s += "'" + list_key.get(i) + "',";
                    }
                    json += "keys:[" + s.substring(0, s.length() - 1) + "]";
                    json = "{" + json + "}";
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return json;
    }

    /**
     * 加载数据源
     *
     * @param tdsAlias (String) 数据源别名
     * @param params   (Map<String, Object>) 参数
     * @return IDataSource 加载好的数据源
     * @category 加载数据源
     */
    public IDataSource loadTds(String tdsAlias, Map<String, Object> params) {
        IDataSource result = null;
        if (tdsAlias != null && tdsAlias.length() != 0) {
            result = this.getDataSource(tdsAlias);
            if (result != null) { // 确认该数据源存在
                List<TInPara> inparamList = result.getInParaList();
                if (inparamList != null && inparamList.size() > 0 && params != null && params.size() > 0) {
                    for (TInPara para : inparamList) {
                        Object val = params.get(para.getParaAlias());
                        if (val != null) {
                            para.setValue(val);
                        }
                    }
                }
                result.setInPara(inparamList);
                try {
                    result.load();
                } catch (Exception e) {
                    log.error("", e);
                    log.info("数据源(" + tdsAlias + ")加载时发生错误！");
                }
            } else {
                log.info("数据源(" + tdsAlias + ")不存在！");
            }
        }
        return result;
    }

    /**
     * 解析摘要区数据源后台公式
     *
     * @param sumDatas (String) 摘要数据
     * @param queryStr (String) 非自动加载数据源的检索条件
     * @return String
     */
    public String getGsValue(String sumDatas, String queryStr) {
        String json = "[[]]";
        try {
            JSONArray Array2 = new JSONArray();

            JSONArray jsonArray = JSONArray.parseArray(sumDatas);// JSONArray.fromObject(sumDatas);
            int num = jsonArray.size();
            for (int k = 0; k < num; k++) {
                JSONArray jsonArray1 = jsonArray.getJSONArray(k);// JSONArray.fromObject(jsonArray.get(k));
                int l = jsonArray1.size();
                JSONArray Array1 = new JSONArray();
                for (int i = 0; i < l; i++) {
                    JSONObject jsonObject = jsonArray1.getJSONObject(i);// JSONObject.fromObject(jsonArray1.get(i));
                    String lx = jsonObject.getString("lx") == null ? "" : jsonObject.getString("lx");
                    if (lx.equals("1")) {
                        String gs = jsonObject.getString("gs");
                        String value = checkJSScript(gs, queryStr);
                        // {"operateResult":"checkSuccess","info":"27.5"}
                        JSONObject jsons = JSONObject.parseObject(value);// JSONObject.fromObject(value);
                        String operateResult = jsons.getString("operateResult");
                        if (operateResult.equals("checkSuccess")) {
                            String info = jsons.getString("info");
                            jsonObject.put("gs", info);
                        }
                    }
                    Array1.add(jsonObject);
                }
                Array2.add(Array1);
            }
            json = Array2.toString();
        } catch (Exception e) {
            log.error("", e);
        }
        return json;
    }

    /**
     * 解析摘要区数据源后台公式
     *
     * @param sumDatas (String) 数据源所有列输出参数集合
     * @param queryStr (String) 非自动加载数据源的检索条件
     * @return String
     */
    public String getBbGsValue(String storeFields, String queryStr) {
        String json = "[[]]";
        try {
            JSONArray Array1 = new JSONArray();
            JSONArray jsonArray = JSONArray.parseArray(storeFields);// JSONArray.fromObject(storeFields);
            int num = jsonArray.size();
            for (int k = 0; k < num; k++) {
                JSONObject jsonObject = jsonArray.getJSONObject(k); // JSONObject.fromObject(jsonArray.get(k));
                String reportFormula = jsonObject.getString("reportFormula") == null ? "" : jsonObject.getString("reportFormula");// 公式
                String lx = jsonObject.getString("lx") == null ? "0" : jsonObject.getString("lx");// 公式
                if (reportFormula != null && !"".equals(reportFormula)) {
                    if (lx.equals("1")) {// 后台公式
                        Double sumGsValue = 0.0;
                        String str = checkJSScript(reportFormula, queryStr);
                        if (str != null && !str.equals("")) {
                            try {
                                JSONObject jsons = JSONObject.parseObject(str);// JSONObject.fromObject(str);
                                String operateResult = jsons.getString("operateResult");
                                if (operateResult.equals("checkSuccess")) {
                                    String info = jsons.getString("info");
                                    sumGsValue = Double.valueOf(info);
                                }

                            } catch (Exception ex) {
                            }
                        }
                        jsonObject.put("sumGsValue", sumGsValue);
                    }
                }
                Array1.add(jsonObject);
            }
            json = Array1.toString();
        } catch (Exception e) {
            log.error("", e);
        }
        return json;
    }

    /**
     * 获取表头合并信息
     *
     * @param listOp
     * @return
     */
    public List<TdsTableColumn> getSpanColumn(LinkedHashMap<String, TOutPara> opMap) {
        List<TdsTableColumn> list = null;
        if (opMap != null && opMap.size() > 0) {
            TdsTableColumn rootColumn = this.newSpanColumn("root");
            for (String key : opMap.keySet()) {
                TOutPara op = opMap.get(key);
                rootColumn.getChildren().add(this.tdsOut2Column(op, null, opMap));
            }
            convertSpanColumn(rootColumn);// 递归计算合并信息
            list = rootColumn.getChildren();
            convertFixCol(list);// 计算合并列宽
        }
        return list;
    }

    /**
     * 计算锁定列宽度（带合并信息的列）
     *
     * @param list
     */
    private void convertFixCol(List<TdsTableColumn> list) {
        if (StringUtils.isNotEmpty(list)) {
            for (TdsTableColumn col : list) {
                if (StringUtils.isNotEmpty(col.getChildren()) && col.getAlias().startsWith("group_") && StringUtils.isNotEmpty(col.getFixed())) {// 需要递归计算列宽度
                    calWidth(col, col.getChildren());
                }
            }
        }
    }

    /**
     * 递归计算合并列的宽度
     *
     * @param col
     * @param list
     */
    void calWidth(TdsTableColumn col, List<TdsTableColumn> list) {
        for (TdsTableColumn c : list) {
            if (StringUtils.isEmpty(c.getChildren())) {// 叶子节点
                int w = StringUtils.isEmpty(c.getWidth()) ? 150 : Integer.parseInt(c.getWidth());
                int width = StringUtils.isEmpty(col.getWidth()) ? 0 : Integer.parseInt(col.getWidth());
                width = width + w;
                col.setWidth(width + "");
            } else {
                calWidth(col, c.getChildren());
            }
        }
    }

    /**
     * 获得表头合并信息
     *
     * @param listOp
     * @return
     */
    public List<TdsTableColumn> getTdsSpanColumn(List<TdsTableColumn> listOp) {
        List<TdsTableColumn> list = null;
        if (listOp != null && listOp.size() > 0) {
            TdsTableColumn rootColumn = this.newSpanColumn("root");
            rootColumn.getChildren().addAll(listOp);
//			for (int n = 0; n < listOp.size(); n++) {
//				TdsTableColumn op = listOp.get(n); // TOutPara op = listOp.get(n);
//				rootColumn.getChildren().add(op);
//			}
            convertSpanColumn(rootColumn);// 递归计算合并信息
            list = rootColumn.getChildren();
            convertFixCol(list);// 计算合并列宽
        }
        return list;
    }

    /**
     * 合并信息转换
     *
     * @param column
     */
    private void convertSpanColumn(TdsTableColumn column) {
        column.setChildren(this.convertSpanColumn(column.getChildren()));
//		for (TdsTableColumn bean : column.getChildren()) {
//			if (!bean.isHidden()) {
//				return;
//			}
//		}
//		column.setHidden(true);
    }

    /**
     * 整理合并信息
     *
     * @param list
     * @return
     */
    private List<TdsTableColumn> convertSpanColumn(List<TdsTableColumn> list) {
        List<TdsTableColumn> rtnList = null;
        if (StringUtils.isNotEmpty(list)) {
            Map<String, TdsTableColumn> map = new LinkedHashMap<String, TdsTableColumn>();
            rtnList = new ArrayList<TdsTableColumn>();
            for (TdsTableColumn tColumn : list) {
                String colname = tColumn.getHeader();
                List<String> listT = Arrays.asList(colname.split("_"));// mapTitle.get(alias);
                if (tColumn.isHidden() || listT == null || listT.size() <= 1) {// 没有合并
                    map.put("NOSPAN_" + tColumn.getAlias(), tColumn);
                } else {
                    colname = listT.get(0);// listT.get(listT.size() - 1);
                    String key = "SPAN" + listT.size() + "_" + colname;
                    TdsTableColumn temp = map.get(key);
                    if (temp == null) {
                        temp = this.newSpanColumn(colname);
                    }
                    String header = "";
                    for (int j = 1; j < listT.size(); j++) {
                        header += listT.get(j) + "_";
                    }
                    tColumn.setHeader(header.substring(0, header.length() - 1));
                    temp.setAlias("group_" + TMUID.getUID());
                    if (StringUtils.isNotEmpty(tColumn.getFixed())) {
                        temp.setFixed(tColumn.getFixed());// 列锁定
                    }
                    temp.getChildren().add(tColumn);
                    map.put(key, temp);
                }
            }
            for (String key : map.keySet()) {
                TdsTableColumn TempCol = map.get(key);
                List<TdsTableColumn> child = TempCol.getChildren();
                if (StringUtils.isNotEmpty(child)) {
                    convertSpanColumn(TempCol);
                }
                rtnList.add(TempCol);
            }
        }
        return rtnList;
    }

    /**
     * 新建分类合并信息
     *
     * @param name 标题名称
     * @return
     */
    private TdsTableColumn newSpanColumn(String name) {
        TdsTableColumn col = new TdsTableColumn();
        col.setHeader(name);
        col.setChildren(new ArrayList<TdsTableColumn>());
        return col;

    }


    /**
     * 脚本计算(不推荐，可能存在内存泄漏)
     *
     * @param s                计算公式 $T004_BNRKHTQ_abz.find("zzdm='@zzdm' and bzdm='@bzdm' and zyid='@zyid'").getf("jkj")
     * @param mapTds           数据源对象，已load
     * @param tdsFindParamsMap 查询参数
     * @return
     * @throws ScriptException
     */
    @Deprecated
    public Object evalTds(String s, Map<String, IDataSource> tdsMap, Map<String, Object> tdsFindParamsMap) throws ScriptException {
        Object obj = null;
        if (s != null && s.trim().length() > 0) {
            s = s.trim();
            String script = s;
            Map<String, Object> paramMap = new HashMap<String, Object>();
            //替换参数 @zzdm ->  TFP_zzdm
            if (tdsFindParamsMap != null && tdsFindParamsMap.size() > 0) {
                List<String> paraList = new ArrayList<String>();
                for (String k : tdsFindParamsMap.keySet()) {
                    paraList.add(k);
                }
                //重新排序，变量长度大的排前面，防止替换时替换错误
                if (paraList.size() > 1) {
                    Collections.sort(paraList, new Comparator<String>() {
                        @Override
                        public int compare(String s1, String s2) {
                            return Integer.compare(s2.length(), s1.length());
                        }
                    });
                }
                //替换变量
                for (String k : paraList) {
                    String p = "TFP_" + k;
                    script = script.replace("@" + k, "\"+ " + p + " +\"");
                    paramMap.put(p, tdsFindParamsMap.get(k));
                }
            }
            script = ScriptEngineUtils.clearScript(script);// 去掉 $ 符号
            CompiledScriptEngine cse = new CompiledScriptEngine(script);
            if (StringUtils.isEmpty(cse.getError())) {
                //evalMap.put(evalKey, cse);
            } else {
                throw (cse.getScriptException());
            }
            try {
                List<String> paramList = parseDataSourceAliasList(s);
                if (paramList != null && paramList.size() > 0 && tdsMap != null && tdsMap.size() > 0) {
                    for (String tdsAlias : paramList) {
                        IDataSource ids = tdsMap.get(tdsAlias);
                        if (ids != null) {
                            paramMap.put(tdsAlias, ids);
                        }
                    }
                    if (paramMap.size() > 0) {
                        obj = cse.eval(paramMap);
                    }
                }
            } catch (ScriptException e) {
                log.error("数据源默认参数脚本解析错误：" + s, e);
                throw (e);
            }
        }
        return obj;
    }

    /**
     * 数据源公式解析
     *
     * @param s      数据源公式 $T004.find("zzdm='001' and bzdm='002'").getf("jkj")
     * @param tdsMap 数据源对象，已load
     * @return
     * @throws Exception
     */
    public Object execTds(String s, Map<String, IDataSource> tdsMap) throws Exception {
        if (StringUtils.isNotEmpty(s) && tdsMap != null && tdsMap.size() > 0) {
            int start = s.indexOf("$");
            if (start > -1) {
                int end = s.indexOf(".", start);
                if (end > -1) {
                    String alias = s.substring(start + 1, end);
                    IDataSource tds = tdsMap.get(alias);
                    return this.execTds(s, tds);
                }
            }
        }
        return null;
    }

    /**
     * 数据源公式计算
     *
     * @param s   计算公式 $T004_BNRKHTQ_abz.find("zzdm='001' and bzdm='002' ").getf("jkj")
     * @param tds 数据源对象，已load
     * @return
     */
    public Object execTds(String s, IDataSource tds) throws Exception {
        if (StringUtils.isNotEmpty(s) && tds != null && tds.getRowCount() > 0) {
            String script = ScriptEngineUtils.clearScript(s.trim());// 去掉 $ 符号
            try {
                Map<String, Object> funMap = this.getFunMap(script);//解析公式中的函数和参数
                if (funMap.size() > 0) {
                    String funName1 = funMap.get("funName1").toString();//函数名 get find filter
                    List<String> funParamList1 = new ArrayList<>();//参数
                    if (funMap.containsKey("funParam1")) {
                        funParamList1 = (List<String>) funMap.get("funParam1");//参数
                    }
                    if (funName1 != null && funParamList1 != null) {
                        if ("getf".equals(funName1) || "gets".equals(funName1)) {
                            if ("getf".equals(funName1)) {
                                return tds.getf(Integer.parseInt(funParamList1.get(0)), funParamList1.get(1));
                            } else {
                                return tds.gets(Integer.parseInt(funParamList1.get(0)), funParamList1.get(1));
                            }

                        } else if ("getColValues".equals(funName1)) {
                            return tds.getColValues(funParamList1.get(0));
                        } else if ("get".equals(funName1) || "find".equals(funName1) || "filter".equals(funName1)) {
                            TRow row = null;
                            String funName2 = funMap.get("funName2").toString();//函数名 get find filter
                            List<String> funParamList2 = new ArrayList<>();
                            if (funMap.containsKey("funParam2")) {
                                funParamList2 = (List<String>) funMap.get("funParam2");//参数
                            }
                            if ("get".equals(funName1) || "find".equals(funName1)) {
                                if ("find".equals(funName1)) {
                                    row = tds.find(funParamList1.get(0));
                                } else if ("get".equals(funName1)) {
                                    row = tds.get(Integer.parseInt(funParamList1.get(0)));
                                }
                                if (row != null) {
                                    if ("getf".equals(funName2)) {
                                        return row.getf(funParamList2.get(0));
                                    } else if ("gets".equals(funName2)) {
                                        return row.gets(funParamList2.get(0));
                                    }
                                }
                            } else if ("filter".equals(funName1)) {
                                IDataSource fds = tds.filter(funParamList1.get(0));
                                if (fds != null) {
                                    if ("count".equals(funName2)) {//计数
                                        return fds.count();
                                    } else if ("sum".equals(funName2)) {//求和
                                        return fds.sum(funParamList2.get(0));
                                    } else if ("avg".equals(funName2)) {//求平均
                                        if (funParamList2.size() == 1) {
                                            return fds.avg(funParamList2.get(0));
                                        } else if (funParamList2.size() == 2) {
                                            boolean bln = true;
                                            if ("false".equals(funParamList2.get(1))) {
                                                bln = false;
                                            }
                                            return fds.avg(funParamList2.get(0), bln);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("数据源计算解析错误：" + script, e);
                throw (e);
            }
        }
        return null;
    }

    /**
     * 去掉变量中的双引号
     *
     * @param val
     * @return
     */
//    private String getStrParam(String val) {
//        if (val == null) {
//            return "";
//        }
//        if (val.startsWith("\"") && val.endsWith("\"")) {
//            return val.substring(1, val.length() - 1);
//        } else {
//            return val;
//        }
//    }

    /**
     * 解析脚本中的函数与参数
     *
     * @param script
     * @return
     */
    public Map<String, Object> getFunMap(String script) {
        Map<String, Object> returnMap = new HashMap<String, Object>();
        try {
            if (StringUtils.isNotEmpty(script)) {
                String input = script;
                List<String> strList = new ArrayList<>();
                List<String> paraList = new ArrayList<>();
                // 定义正则表达式
                String regex = "\"([^\"]*)\"";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(input);
                // 1.替换 双引号 中的内容为 ?，并记录双引号中的内容
                StringBuffer result = new StringBuffer();
                while (matcher.find()) {
                    // 获取匹配的内容
                    String matchedContent = matcher.group(1);
                    strList.add(matchedContent);
                    // 动态替换内容（例如在原内容前加上 "新的_"）
                    matcher.appendReplacement(result, "?");
                }
                matcher.appendTail(result);
                script = result.toString();
                //替换括号中的内容
                String regex1 = "\\([^)]*\\)";
                StringBuffer result1 = new StringBuffer();
                // 使用 Pattern 和 Matcher 进行替换
                Pattern pattern1 = Pattern.compile(regex1);
                Matcher matcher1 = pattern1.matcher(script);
                while (matcher1.find()) {
                    // 获取匹配的内容
                    String matchedContent = matcher1.group();
                    paraList.add(matchedContent);
                    // 动态替换内容
                    //String replacement = "(动态替换: " + matchedContent + ")";
                    // 追加到结果中
                    matcher1.appendReplacement(result1, "()");
                }
                matcher1.appendTail(result1);
                script = result1.toString();
                // 2.解析函数与参数
                List<String> funList = new ArrayList<>();
                String[] funs = script.split("\\.");
                for (String fun : funs) {
                    int start = fun.indexOf("(");
                    int end = fun.indexOf(")");
                    if (start >= 0 && end >= 0) {
                        String funName = fun.substring(0, start);
                        funList.add(funName);
                        //String params = fun.substring(start + 1, end).trim();
                        //paraList.add(params);
                    }
                }
                // 3.解析参数
                int index = -1;
                for (int i = 0; i < funList.size(); i++) {
                    String funName = funList.get(i);
                    String params = paraList.get(i).trim();
                    if (params.startsWith("(") && params.endsWith(")")) {
                        params = params.substring(1, params.length() - 1).trim();
                    }
                    int n = i + 1;
                    returnMap.put("funName" + n, funName.trim());
                    String[] paramsArr = params.split(",");
                    List<String> paramsList = new ArrayList<>();
                    for (String param : paramsArr) {
                        if ("?".equals(param.trim())) {
                            index++;
                            String s = strList.get(index);
                            if (s.startsWith("\"") && s.endsWith("\"")) {
                                s = s.substring(1, s.length() - 1);
                            }
                            paramsList.add(s);
                        } else {
                            paramsList.add(param.trim());
                        }
                    }
                    returnMap.put("funParam" + n, paramsList);
                }
            }
        } catch (Exception e) {
            log.error("数据源计算解析公式错误：" + script, e);
        }
        return returnMap;
    }


    /**
     * 获取渲染值
     *
     * @param script
     * @param value
     * @param row
     * @param colAlias
     * @param rowIndex
     * @param colIndex
     * @return
     * @throws ScriptException
     */
    public Object getRenderValue(String script, Object value, JSONObject row, String colAlias, int rowIndex, int colIndex) throws ScriptException {
        Object val = value;
        // String script = outPara.getRendererFun();// 渲染脚本
        if (script != null) {
            script = script.trim();
        }
        if (script.length() > 0) {
            String evalKey = ScriptEngineUtils.getMd5(script);
            CompiledScriptEngine cse = evalMap.get(evalKey);
            if (cse == null) {// 开始预编译脚本
                script = "function getRenderValue(value,row,colName,rowIndex,colIndex){\n" + script + "\n}";
                script = "getRenderValue(value,row,colName,rowIndex,colIndex); \n" + script;
                cse = new CompiledScriptEngine(script);
                if (StringUtils.isEmpty(cse.getError())) {// 编译成功
                    evalMap.put(evalKey, cse);// 存入map
                } else {
                    throw (cse.getScriptException());
                }
            }
            // 参数赋值
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("value", value == null ? "" : value); // 单元格值
            params.put("row", row);// 行数据
            params.put("colName", colAlias);// 列名
            params.put("rowIndex", rowIndex);// 行号
            params.put("colIndex", colIndex);// 列号
            val = cse.eval(params);// 脚本解析
            val = this.clearHtml(val);// 清除html标签
        }
        return val;
    }

    /**
     * 清除html标签
     *
     * @param obj
     * @return
     */
    private Object clearHtml(Object val) {
        if (val != null && val instanceof String) {
            String s = val.toString();
            if (s.indexOf("<") >= 0 && s.indexOf(">") >= 0) {
                return HtmlUtils.clearHtml(s);
            }
        }
        return val;
    }

    /**
     * 获取输入参数脚本默认值
     *
     * @param s   脚本
     * @param map
     * @return
     */
    public Object getScrictDefaultValue(String s, Map<String, IDataSource> map) throws Exception {
        Object obj = null;
        try {
            if (s != null && s.trim().length() > 0) {
                s = s.trim();
                obj = ScriptEngineUtils.evalConst(s);// 首先进行常量的计算
                if (obj != null) {
                    return obj;
                }
                if (s.indexOf("$") >= 0) {// 数据源解析
                    return this.execTds(s, map);
                } else {//自定义函数解析
                    obj = this.execCustomFun(s);
                    if (obj != null) {
                        return obj;
                    } else {
                        obj = this.evalFun(s);//利用脚本解析器解析
                    }
                }
            }
        } catch (Exception e) {
            log.error("数据源输入参数默认值解析错误:	" + s, e);
            throw (e);
        }
        return obj;
    }
    /**
     * 获取输入参数脚本默认值(不需要预先加载tds)
     *
     * @param s   脚本
     * @param publicIdsMap 给一个空map，后续会把加载好的数据源填入该map
     * @return
     */
    public Object getScrictDefaultValueWithOutLoadTds(String s, HashMap<String, IDataSource> publicIdsMap) throws Exception {
        Map<String, IDataSource> idsMap = this.loadScriptDs(s, null, publicIdsMap);
        return this.getScrictDefaultValue(s, idsMap) ;
    }
    /**
     * 利用脚本解析器计算函数值（不推荐，有可能存在内存泄漏）
     *
     * @param s
     * @param map
     * @return
     * @throws ScriptException
     */
    @Deprecated
    private Object evalFun(String s) throws ScriptException {
        Object obj = null;
        if (s != null && s.trim().length() > 0) {
            s = s.trim();
            //以下解析为了兼容 数据源解析在getScrictDefaultValue 未实现的函数，防止出错
            if (this.cse == null) {
                this.cse = new CompiledScriptEngine();
            }
            String script = CustomFun.getCuntomUsedFunScript(s);
            if (StringUtils.isNotEmpty(script)) {
                try {
                    script = script + " " + s;
                    obj = this.cse.eval(script, null);
                } catch (ScriptException e) {
                    log.error("数据源默认参数脚本解析错误：" + s, e);
                    throw (e);
                }
            }
        }
        return obj;
    }


    /**
     * 系统自定义函数解析
     *
     * @param s
     * @return
     * @throws Exception
     */
    public Object execCustomFun(String s) throws Exception {
        Object value = null;
        if (StringUtils.isNotEmpty(s)) {
            if (s.indexOf("+") > 0) {//含义字符串连接，无法解析
                return null;
            }
            Map<String, Object> funMap = this.getFunMap(s);
            if (funMap != null & funMap.size() > 0) {
                String funName1 = (String) funMap.get("funName1");
                if (getCustomFunList().contains(funName1)) {//可解析列表
                    List<String> funParamList1 = new ArrayList<>();
                    if (funMap.containsKey("funParam1")) {
                        funParamList1 = (List<String>) funMap.get("funParam1");//参数
                    }
                    if ("getToday".equals(funName1)) {
                        value = this.getToday();
                    } else if ("getMonth".equals(funName1)) {//获取月份
                        int num = 0;
                        if (StringUtils.isNotEmpty(funParamList1) && StringUtils.isNotEmpty(funParamList1.get(0))) {
                            num = Integer.parseInt(funParamList1.get(0));
                        }
                        value = this.getMonth(num);
                    } else if ("getYear".equals(funName1)) {//获取年份
                        int num = 0;
                        if (StringUtils.isNotEmpty(funParamList1) && StringUtils.isNotEmpty(funParamList1.get(0))) {
                            num = Integer.parseInt(funParamList1.get(0));
                        }
                        value = this.getYear(num);
                    } else if ("DateAdd".equals(funName1)) {//日期加减
                        int num = 0;
                        if (StringUtils.isNotEmpty(funParamList1) && StringUtils.isNotEmpty(funParamList1.get(0))) {
                            num = Integer.parseInt(funParamList1.get(0));
                        }
                        value = this.DateAdd(num);
                    } else if ("getLastDayOfMonth".equals(funName1)) {//获取上个月最后一天
                        int num = 0;
                        if (StringUtils.isNotEmpty(funParamList1) && StringUtils.isNotEmpty(funParamList1.get(0))) {
                            num = Integer.parseInt(funParamList1.get(0));
                        }
                        value = this.getDayOfMonth(1, num);
                    } else if ("getFirstDayOfMonth".equals(funName1)) {//获取下个月第一天
                        int num = 0;
                        if (StringUtils.isNotEmpty(funParamList1) && StringUtils.isNotEmpty(funParamList1.get(0))) {
                            num = Integer.parseInt(funParamList1.get(0));
                        }
                        value = this.getDayOfMonth(0, num);
                    } else if ("getQuarter".equals(funName1)) {//获取当前季度
                        int num = 0;
                        if (StringUtils.isNotEmpty(funParamList1) && StringUtils.isNotEmpty(funParamList1.get(0))) {
                            num = Integer.parseInt(funParamList1.get(0));
                        }
                        value = this.getQuarter(num);
                    } else if ("getWeekData".equals(funName1)) {//获取本周数据
                        int num = 0;
                        if (StringUtils.isNotEmpty(funParamList1) && StringUtils.isNotEmpty(funParamList1.get(0))) {
                            num = Integer.parseInt(funParamList1.get(0));
                        }
                        value = this.getWeekData(num);
                    } else if ("round".equals(funName1)) {//四舍五入
                        Double x = Double.parseDouble(funParamList1.get(0));
                        Integer y = Integer.parseInt(funParamList1.get(1));
                        return this.round(x, y);
                    } else if ("mod".equals(funName1)) {// 取余
                        Integer x = Integer.parseInt(funParamList1.get(0));
                        Integer y = Integer.parseInt(funParamList1.get(1));
                        return this.mod(x, y);
                    } else if ("abs".equals(funName1)) {//取绝对值
                        Double x = Double.parseDouble(funParamList1.get(0));
                        return this.abs(x);
                    } else if ("max".equals(funName1)) {//最大值
                        return this.max(funParamList1);
                    } else if ("min".equals(funName1)) {//最小值
                        return this.min(funParamList1);
                    }
                    if (value != null && funMap.containsKey("funName2")) {
                        String funName2 = (String) funMap.get("funName2");
                        if (value instanceof String && "substring".equals(funName2)) {
                            String val = (String) value;
                            if (val.length() > 0) {
                                List<String> funParamList2 = new ArrayList<>();
                                if (funMap.containsKey("funParam2")) {
                                    funParamList2 = (List<String>) funMap.get("funParam2");//参数
                                }
                                if (funParamList2.size() == 2) {
                                    value = val.substring(Integer.parseInt(funParamList2.get(0)), Integer.parseInt(funParamList2.get(1)));
                                }
                            }
                        }
                    }
                }
            }
        }
        return value;
    }


    /**
     * 获取系统自定义函数列表
     *
     * @return
     */
    private List<String> getCustomFunList() {
        if (customFunList == null) {
            customFunList = new ArrayList<>();
            customFunList.add("getToday");
            customFunList.add("getYear");
            customFunList.add("getMonth");
            customFunList.add("getFirstDayOfMonth");
            customFunList.add("getLastDayOfMonth");
            customFunList.add("DateAdd");
            customFunList.add("getWeekData");
            customFunList.add("getQuarter");
            customFunList.add("abs");
            customFunList.add("round");
            customFunList.add("mod");
            customFunList.add("max");
            customFunList.add("min");
        }
        return customFunList;
    }

    private int mod(Integer x, Integer y) {
        int x1 = x.intValue();
        int y1 = y.intValue();
        int m = x1 % y1;
        return m;
    }

    private double round(Double x, Integer y) {
        double val = x.doubleValue();
        int _pos = 0;
        if (y != null) {
            _pos = Math.abs(y.intValue());
            double parmNum = Math.pow(10, _pos);
            val = Math.round(val * parmNum) / parmNum;
        }
        return val;
    }

    private double abs(Double x) {
        return Math.abs(x.doubleValue());
    }

    private String getToday() {
        return DateTimeUtils.getNowDateStr();
    }


    private String getMonth(Integer num) {
        return DateTimeUtils.formatDate(this.getDay("month", num), "yyyy-MM");
    }

    private String getYear(Integer num) {
        return DateTimeUtils.formatDate(this.getDay("year", num), "yyyy");
    }

    private Date getDay(String type, Integer num) {
        Date day = new Date();
        if (num != null && num.intValue() != 0) {
            if ("day".equals(type)) {
                day = DateTimeUtils.doDate(day, num.intValue());//日期加减
            } else if ("month".equals(type)) {
                day = DateTimeUtils.doMonth(day, num.intValue());//月份加减
            } else if ("year".equals(type)) {
                day = DateTimeUtils.doYear(day, num.intValue());//年份加减
            }
        }
        return day;
    }

    /**
     * 获得某月的第一天或者最后一天
     *
     * @param type 0：第一天;1:最后一天
     * @param num  参数num:(0:本月;1:下月;-1:上月)
     * @return 返回格式为2010-01-01
     */
    private String getDayOfMonth(int type, Integer num) {
        String str = "";
        String[] days = DateTimeUtils.getNowMonthStr(this.getDay("month", num), 0);
        if (type == 0) {//月初
            str = days[0];
        } else if (type == 1) {//月末
            str = days[1];
        }
        if (str.length() > 10) {
            str = str.substring(0, 10);
        }
        return str;
    }

    /**
     * 日期加减
     *
     * @param num
     * @return
     */
    private String DateAdd(Integer num) {
        return DateTimeUtils.formatDate(this.getDay("day", num), "yyyy-MM-dd");
    }

    /**
     * 获取当前季度(给参数则对当前季度进行加减)，返回值为 03：第一季度 06：第二季度 09：第三季度 12：第四季度
     *
     * @param num
     * @return
     */
    private String getQuarter(Integer num) {
        String str = "";
        int addNum = 0;
        if (num == null || num == 0) {
            addNum = 0;
        } else {
            addNum = num.intValue();
            addNum = 4 + addNum % 4;
        }
        Double nowMonth = Double.parseDouble(DateTimeUtils.formatDate(new Date(), "MM"));
        Double nowQuarter = ((Math.ceil(nowMonth / 3) - 1 + addNum) % 4 + 1) * 3;
        if (nowQuarter.intValue() < 10) {
            str = "0" + nowQuarter.intValue();
        } else {
            str = "" + nowQuarter.intValue();
        }
        return str;
    }


    /**
     * 获取周
     *
     * @param num
     * @return
     */
    private String getWeekData(Integer num) {
        String str = "";
        int n = 0;
        if (num != null & num != 0) {
            n = num.intValue() * 7;
            if (n > 0) {
                n = n + 1;
            } else {
                n = n - 1;
            }
        }
        String[] days = DateTimeUtils.getWeek(this.getDay("day", n));
        //2025-02-10~2025-02-16
        String d1 = days[0];
        String d2 = days[1];
        if (d1.length() > 10) {
            d1 = d1.substring(0, 10);
        }
        if (d2.length() > 10) {
            d2 = d2.substring(0, 10);
        }
        str = d1 + "~" + d2;
        return str;
    }

    private String min(List<String> params) {
        String str = "";
        if (params.size() > 0) {
            str = params.get(0);
            for (int i = 1; i < params.size(); i++) {
                if (Double.compare(Double.parseDouble(str), Double.parseDouble(params.get(i))) == 1) {
                    str = params.get(i);
                }
            }
        }
        return str;
    }

    private String max(List<String> params) {
        String str = "";
        if (params.size() > 0) {
            str = params.get(0);
            for (int i = 1; i < params.size(); i++) {
                if (Double.compare(Double.parseDouble(str), Double.parseDouble(params.get(i))) == -1) {
                    str = params.get(i);
                }
            }
        }
        return str;
    }


}
