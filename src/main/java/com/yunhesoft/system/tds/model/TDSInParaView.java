package com.yunhesoft.system.tds.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.tds.service.IDataSourceInParaRelService;

import lombok.extern.log4j.Log4j2;

/**
 * 数据源输入参数预览
 * 
 * @category 数据源输入参数预览
 * <AUTHOR>
 * @version 0.1 <br>
 *          2010-4-1<br>
 */

@Log4j2
public class TDSInParaView {

	/**
	 * 获取数据源检索条件信息
	 * 
	 * @param tdsAlias       数据源别名
	 * @param inParaAlias    检索条件字符串
	 * @param inParaRawValue
	 * @return
	 */
	public HashMap<String, Object> getTdsQueryList(String tdsAlias, String inParaAlias, String inParaRawValue) {
		return this.getData(tdsAlias, inParaAlias, inParaRawValue);
	}

	/**
	 * 获取数据
	 */
	private HashMap<String, Object> getData(String tdsAlias, String inParaAlias, String inParaRawValue) {
		List<HashMap<String, Object>> listDs = new ArrayList<HashMap<String, Object>>();
		TDataSourceManager dsm = new TDataSourceManager();
		HashMap<String, Object> mpDs = new HashMap<String, Object>();
		try {
			String dsCode = tdsAlias;// 数据源
			IDataSource ids = dsm.getDataSource(dsCode);
			if (ids == null) {
				return null;
			}
			ids.parseInParaDefaultValue(getMapInParaAlias(inParaAlias));// 获取默认值
			String dsTitle = ids.getDSName();// 数据源名称
			mpDs.put("xtype", "fieldset");
			mpDs.put("id", dsCode);
			mpDs.put("title", dsTitle);
			// 是否有联动关系
			long relCount = SpringUtils.getBean(IDataSourceInParaRelService.class).getTdsInparaRelCount(dsCode);
			mpDs.put("relCount", relCount);
			List<TInPara> listIn = ids.getInParaList();
			if (listIn != null) {
				for (int j = 0; j < listIn.size(); j++) {
					TInPara inPara = listIn.get(j);
					String value = "";
					String rawValue = "";
					HashMap<String, Object> mpItem = new HashMap<String, Object>();
					String xtype = inPara.getComponentType();// 控件类型
					if (xtype == null || "".equals(xtype)) {
						xtype = "textfield";
					}
					mpItem.put("insertedit", inPara.getInsertEdit());// 检索条件值是否代入添加数据
					if (StringUtils.isNotEmpty(inPara.getComParams())) {
						mpItem.put("comParams", inPara.getComParams());// 控件属性
					}
					xtype = xtype.toLowerCase();
					String fieldLabel = inPara.getParaName();// 输入参数名称
					String fieldName = inPara.getParaAlias();// 输入参数代码
					// mpItem.put("id", dsCode + "_inObj_" + fieldName);
					if (inPara.getValue() != null) {
						value = replaceSpecial(inPara.getValue().toString());// 默认输入参数值
					}
					if (inPara.getRawValue() != null) {
						rawValue = replaceSpecial(inPara.getRawValue().toString());// 默认显示值
						if (rawValue != null && !"".equals(rawValue)) {
							mpItem.put("rawValue", rawValue);
						}
					}
					boolean display = inPara.getDisplayed();
					// String editable = "false";
					mpItem.put("xtype", xtype);
					if (!display || "hidden".equals(xtype)) {// 控件隐藏
						mpItem.put("xtype", "hidden");
						// mpItem.put("hidden", true);
					} else {// 控件显示
						// inParaCount++;
						mpItem.put("fieldLabel", fieldLabel);// 标题
						mpItem.put("label", fieldLabel);// 标题
						// mpItem.put("hidden", false);
						// mpItem.put("selectOnFocus ", "true");// 获得焦点时选中文本
					}
					mpItem.put("name", fieldName);
					if (inPara.getWidth() != null && inPara.getWidth() > 0) {
						mpItem.put("width", inPara.getWidth());
					}
					if ("combo".equals(xtype) || "lovcombo".equals(xtype) || "orgfields".equals(xtype)
							|| "orgfield".equals(xtype) || "userfield".equals(xtype) || "pagetagfield".equals(xtype)
							|| "udyearfield".equals(xtype) || "udhalfyearfield".equals(xtype)
							|| "udquarterfield".equals(xtype) || "udmonthfield".equals(xtype)

					) {// 下拉框、多选下拉框
						if ("pagetagfield".equals(xtype)) {// 公告选择标签
							// codeWithSign:"",//“'”返回值按照 'aaa','bbb','ccc'
							// 方式返回
							// textWithSign:",",//标签名称返回值分隔符
							// mpItem.put("codeWithSign", "\"'\"");
							// mpItem.put("textWithSign", "'，'");
						} else if ("orgfields".equals(xtype)) {// 机构多选
							if (display) {
								mpItem.put("xtype", "orgfield");
							}
							mpItem.put("checkTree", "true");
							// mpItem.put("codeWithSign", "\"'\"");
						} else if ("orgfield".equals(xtype)) {// 机构单选
							if (display) {
								mpItem.put("xtype", "orgfield");
							}
						} else {
							// mpItem.put("editable", editable);
							// mpItem.put("triggerAction", "all");
							mpItem.put("displayField", "value");
							mpItem.put("valueField", "key");
							mpItem.put("mode", "local");

							// ********************************************************************************
							Map<Object, Object> mpStore = inPara.getDefaultValueMap();// 生成store
							if (StringUtils.isNotEmpty(mpStore)) {
								List<LinkedHashMap<Object, Object>> listStore = new ArrayList<LinkedHashMap<Object, Object>>();
								for (Object key : mpStore.keySet()) {
									LinkedHashMap<Object, Object> tempmap = new LinkedHashMap<Object, Object>();
									Object objVal = mpStore.get(key);
									tempmap.put("key", key == null ? "" : key.toString());
									tempmap.put("value", objVal == null ? "" : objVal.toString());
									listStore.add(tempmap);
								}
								mpItem.put("store", listStore);
							}
						}
						mpItem.put("value", value);
						value = this.getMapInParaAlias(inParaAlias).get(fieldName);// url传入条件值
						if (value != null) {
							mpItem.put("value", value);
							rawValue = this.getMapInParaAlias(inParaRawValue).get(fieldName);// url
							if (rawValue == null)
								rawValue = value;
							if (rawValue != null) {// 传入的名称
								mpItem.put("rawValue", rawValue);
							}
						}
						mpItem.put("comParams",inPara.getComParams());
					} else if ("datefield".equals(xtype)) {// 日期选择
						if (value == null || "".equals(value) || value.length() < 10) {
							value = DateTimeUtils.getNowDateStr();
						} else {
							if (value.length() >= 10) {
								value = value.substring(0, 10);
							}
						}
						mpItem.put("value", value);
						value = this.getMapInParaAlias(inParaAlias).get(fieldName);// url传入值
						if (value != null) {
							mpItem.put("value", value);
						}
						if (inPara.getWidth() == null || inPara.getWidth() <= 0) {
							mpItem.put("width", 130);
						}
					} else if ("monthfield".equals(xtype)) {// 月份选择
						if (value == null || "".equals(value) || value.length() < 7) {
							value = DateTimeUtils.getNowDateStr().substring(0, 7);
						} else {
							if (value.length() >= 7) {
								value = value.substring(0, 7);
							}
						}
						mpItem.put("value", value);
						value = this.getMapInParaAlias(inParaAlias).get(fieldName);
						if (value != null) {
							if (value.length() >= 7) {
								value = value.substring(0, 7);
							}
							mpItem.put("value", value);
						}
						if (inPara.getWidth() == null || inPara.getWidth() <= 0) {
							mpItem.put("width", 120);
						}
					} else if ("datetimefield".equals(xtype)) {// 时间日期选择
						// 传入值
						String v = this.getMapInParaAlias(inParaAlias).get(fieldName);
						if (v != null) {
							value = v;
						}
						// mpItem.put("value", value);
						// value = this.getMapInParaAlias(inParaAlias).get(fieldName);
						if (StringUtils.isNotEmpty(value)) {
							mpItem.put("value", value);
						} else {
							mpItem.put("value", DateTimeUtils.getNowDateTimeStr());
						}
						if (inPara.getWidth() == null || inPara.getWidth() <= 0) {
							mpItem.put("width", 185);
						}
					} else if ("yearfield".equals(xtype)) {// 年份选择
						if (value == null || "".equals(value) || value.length() < 4) {
							value = DateTimeUtils.getNowYear();// "new Date().format('Y-m-d')";
						} else {
							if (value.length() >= 4) {
								value = value.substring(0, 4);
							}
						}
						mpItem.put("value", value);
						value = this.getMapInParaAlias(inParaAlias).get(fieldName);
						if (value != null) {
							if (value.length() >= 4) {
								value = value.substring(0, 4);
							}
							mpItem.put("value", value);
						}
						if (inPara.getWidth() == null || inPara.getWidth() <= 0) {
							mpItem.put("width", 100);
						}
					} else if ("comboseldatebyweek".equals(xtype)) {// 周选择框（日期区间）
						mpItem.put("valueMode", "tds");// 返回值格式为 2000-01-01~2000-01-07
						mpItem.put("listWidth", "350");
						mpItem.put("listHeight", "174");
						String tempValue = this.getMapInParaAlias(inParaAlias).get(fieldName);
						if (tempValue != null) {
							value = tempValue;
						}
						mpItem.put("value", value);
					} else if ("comboseldatebyweekextend".equals(xtype)) {// 周选择框（日期区间+周信息）
						mpItem.put("valueMode", "tdsextend");// 返回值格式为 2000-01-01~2000-01-07
						mpItem.put("listWidth", "350");
						mpItem.put("listHeight", "174");
						String tempValue = this.getMapInParaAlias(inParaAlias).get(fieldName);
						if (tempValue != null) {
							value = tempValue;
						}
						mpItem.put("value", value);
					} else if ("checkbox".equals(xtype)) {// 复选框
						if ("true".equals(value)) {
							value = "true";
						} else {
							value = "false";
						}
						mpItem.put("checked", Boolean.parseBoolean(value));
						value = this.getMapInParaAlias(inParaAlias).get(fieldName);
						if (value == null) {
						} else {
							if ("true".equals(value)) {// 判断是否被选中
								value = "true";
							} else {
								value = "false";
							}
							mpItem.put("checked", Boolean.parseBoolean(value));
						}
					} else {
						if (!"".equals(value)) {
							mpItem.put("value", value);
						}
						value = this.getMapInParaAlias(inParaAlias).get(fieldName);
						if (value != null) {
							mpItem.put("value", value);
						}
					}
					listDs.add(mpItem);
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}
		mpDs.put("data", listDs);
		return mpDs;
	}

	/**
	 * 根据传入值生成参数map
	 * 
	 * @param str 条件字符串
	 * @return
	 */
	private Map<String, String> getMapInParaAlias(String str) {
		Map<String, String> map = new HashMap<String, String>();
		try {
			if (str != null) {
				String[] ary1 = str.split("\\|");
				for (int i = 0; i < ary1.length; i++) {
					String ary2[] = ary1[i].split("=");
					if (ary2.length == 2) {// 参数值格式正确
						map.put(ary2[0], ary2[1]);
					}
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return map;
	}

	private String replaceSpecial(String Str) {
		if (Str != null && Str.length() != 0) {
			Str = Str.replace("\\", "\\\\");// 转义斜线防止页面出错
			Str = Str.replace("'", "\\'");// 转义单引号防止页面出错
		}
		return Str;
	}

	/**
	 * @param getDataType @Title: getCboContent @Description:
	 *                    TODO(获取下拉框绑定的数据源数据) @param @param defaultKeyScript 参数
	 *                    $123.getColValues("编号") @return String ext stroe
	 *                    Json @throws
	 */
	public String getCboContent(String defaultKeyScript, String defaultValueScript) {
		String result = "";
		String keyScript = defaultKeyScript.trim();
		String valueScript = defaultValueScript.trim();
		String strDs = "";
		if (keyScript.indexOf(".") != -1) {
			strDs = keyScript.substring(1, keyScript.indexOf(".")); // 解析数据源别名
		} else {
			return "";
		}
		String strKey = keyScript.substring(keyScript.indexOf("\"") + 1, keyScript.lastIndexOf("\"")); // 解析输出参数别名
		String strValue = valueScript.substring(valueScript.indexOf("\"") + 1, valueScript.lastIndexOf("\"")); // 解析输出参数别名
		String key = "";
		String value = "";
		LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();// 获取条件
		HashSet<String> tdsSet = new HashSet<String>();
		tdsSet.add(strDs);
		HashMap<String, IDataSource> publicIdsMap = getPublicIdsMap(tdsSet, map);// 获取已加载好的数据源
		IDataSource ids = publicIdsMap.get(strDs); // 按照别名获取数据源
		List<TOutPara> out = ids.getOutParaList(); // 获取所有输出参数列表
		for (TOutPara tOutPara : out) {
			if (tOutPara.getName().equals(strKey)) { // 查找需要的输出参数
				key = tOutPara.getAlias(); // 记录
			}
			if (tOutPara.getName().equals(strValue)) {
				value = tOutPara.getAlias(); // 记录
			}
		}
		String dataJson = ids.getJsonData();// dsm.getData(sts, "jsonData"); // 获取数据源中数据 无分页
		if (dataJson != null && !dataJson.equals("")) {
			JSONArray jsonArray = JSONArray.parseArray(dataJson); // JSONArray.fromObject(dataJson);
			if (jsonArray != null && jsonArray.size() > 0) {
				for (int i = 0; i < jsonArray.size(); i++) {
					// JSONObject jsonObject = JSONObject.fromObject(jsonArray.get(i));
					JSONObject jsonObject = jsonArray.getJSONObject(i);
					if (!key.equals("") && !value.equals("")) {
						result += "[\"" + jsonObject.getString(key) + "\",\"" + jsonObject.getString(value) + "\"],"; // 重新生成json数据，过滤掉不需要的数据
					} else if (!key.equals("") && value.equals("")) {
						result += "[\"" + jsonObject.getString(key) + "\",\"" + jsonObject.getString(key) + "\"],"; // 重新生成json数据，过滤掉不需要的数据
					} else if (key.equals("") && !value.equals("")) {
						result += "[\"" + jsonObject.getString(value) + "\",\"" + jsonObject.getString(value) + "\"],"; // 重新生成json数据，过滤掉不需要的数据
					}
				}
				result = result.substring(0, result.length() - 1); // 去除最后一个逗号
			} else {
				result = "";
			}
			result = "new Ext.data.SimpleStore({fields:['key', 'value'],data:[" + result + "]})"; // 整合json
		} else {
			result = "";
		}
		return result;
	}

	/**
	 * 获取已加载好的数据源列表
	 * 
	 * @category 获取已加载好的数据源列表
	 * @param tdsSet   (HashSet<String>) 数据源别名列表
	 * @param queryMap (LinkedHashMap<String, String>) 给定数据源参数列表
	 * @return HashMap<String, IDataSource> key数据源别名 value 数据源
	 */
	private HashMap<String, IDataSource> getPublicIdsMap(HashSet<String> tdsSet,
			LinkedHashMap<String, String> queryMap) {
		HashMap<String, IDataSource> result = new HashMap<String, IDataSource>();// 加载好的数据源
		if (tdsSet != null && tdsSet.size() > 0) {
			TDataSourceManager tsm = new TDataSourceManager();
			for (String temp : tdsSet) {
				IDataSource ids = tsm.getDataSource(temp);
				if (ids != null) {
					if (queryMap != null && queryMap.size() > 0) {// 有参数
						for (Entry<String, String> param : queryMap.entrySet()) {
							ids.setInParaByAlias(param.getKey(), param.getValue());// 设置输入参数
						}
						ids.load();// 加载数据源
					} else {
						if (ids.getAutoLoad()) {// 自动加载
						} else {
							ids.load();// 加载数据源
						}
					}
					result.put(temp, ids);
				}
			}
		}
		return result;
	}

	/**
	 * 获得记忆的条件值
	 * 
	 * @param mpMyMemory 条件值map
	 * @param fieldName  输入参数别名
	 * @return
	 */
	/*
	 * private Tdsmemory getMemoryValue(Map<String, Tdsmemory> mpMyMemory, String
	 * fieldName) { Tdsmemory tdsm = null; if (mpMyMemory != null &&
	 * mpMyMemory.size() > 0) { tdsm = mpMyMemory.get(fieldName); } return tdsm; }
	 */

	/**
	 * 获取数据源输入参数下拉框值
	 * 
	 * @param tdsCode     数据源别名
	 * @param inParaAlias 输入参数 逗号分隔
	 * @return
	 */
	/*
	 * public String getComboListData(String tdsCode, String inParaAlias) {
	 * List<String> list = new ArrayList<String>(); String[] ary =
	 * inParaAlias.split(","); for (String string : ary) { list.add(string); }
	 * return this.getComboListData(tdsCode, list); }
	 */

	/**
	 * 获取数据源输入参数下拉框值
	 * 
	 * @param tdsCode
	 * @param ListInParaAlias
	 * @return
	 */
	/*
	 * public String getComboListData(String tdsCode, List<String> list) { String
	 * json = ""; try { if (list != null && list.size() > 0) { TDataSourceManager
	 * dsm = new TDataSourceManager(); IDataSource ids = dsm.getDataSource(tdsCode);
	 * if (ids != null) { ids.parseInParaDefaultValue(); List<TInPara> listIn =
	 * ids.getInParaList(); for (TInPara tInPara : listIn) { if
	 * (list.contains(tInPara.getParaAlias())) { //Map<Object, Object> map =
	 * tInPara.getDefaultValueMap(); // log.info(extCom.getStoreData(map)); // json
	 * += tInPara.getParaAlias() + ":" + extCom.getStoreData(map) + ","; // map.pu }
	 * } } } if (json.length() > 0) { json = "{" + json.substring(0, json.length() -
	 * 1) + "}"; } } catch (Exception e) { log.error("",e); } return json; }
	 */
	/*
	 * private String getOutParaEventScript(String event, List<TdsoutPara> list) {
	 * StringBuffer sb = new StringBuffer(); try { if ("select".equals(event)) {//
	 * 下拉框选择事件脚本 sb.append("var VAL=combo.getValue();"); } } catch (Exception e) {
	 * log.error("",e); } return sb.toString(); }
	 */

	/**
	 * 获得数据源输入参数事件
	 * 
	 * @param tdsAlias  数据源别名
	 * @param paraAlias 输入参数别名
	 * @param event     事件类型
	 * @return
	 * @throws Exception
	 */
	/*
	 * private List<TdsinParaEvent> getInParaEventList(String tdsAlias, String
	 * paraAlias, String event) { List<TdsinParaEvent> list = null; try { ParaSQL
	 * paraSql = new ParaSQL(); list = paraSql.getInParaEventList(tdsAlias,
	 * paraAlias, event); } catch (Exception e) { log.error("",e); } return list; }
	 */
}
