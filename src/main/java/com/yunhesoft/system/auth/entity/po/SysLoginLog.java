package com.yunhesoft.system.auth.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登录日志
 * 
 * <AUTHOR>
 * @date 2021/09/26 08:14
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "SYS_LOGIN_LOG")
public class SysLoginLog extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/** 登录名称 */
	@Column(name = "USER_NAME", length = 255)
	private String userName;

	@Column(name = "TOKEN", length = 500)
	private String token;

	@Column(name = "DESCRIPTION", length = 255)
	private String description;

	/** 状态 1:登录 ；2：登出 */
	@Column(name = "STATUS")
	private Integer status;

	/** 注册时间 */
	@Column(name = "LOGTIME")
	private Date logTime;

	/** ip地址 */
	@Column(name = "IPADDRESS", length = 255)
	private String ipAddress;

}
