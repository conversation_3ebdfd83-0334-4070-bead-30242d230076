package com.yunhesoft.system.org.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.common.utils.Tree;
import com.yunhesoft.core.common.utils.TreeNode;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.dataperm.entity.vo.DataPermVo;
import com.yunhesoft.system.dataperm.service.ISysDataPermService;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.impl.TreeServiceImpl;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.dto.SysOrgAdd;
import com.yunhesoft.system.org.entity.dto.SysOrgDel;
import com.yunhesoft.system.org.entity.dto.SysOrgHandleDrop;
import com.yunhesoft.system.org.entity.dto.SysOrgMove;
import com.yunhesoft.system.org.entity.dto.SysOrgSelect;
import com.yunhesoft.system.org.entity.dto.SysOrgSort;
import com.yunhesoft.system.org.entity.dto.SysOrgType;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.entity.po.SysOrgLog;
import com.yunhesoft.system.org.entity.po.SysOrgRelation;
import com.yunhesoft.system.org.entity.vo.SysOrgTree;
import com.yunhesoft.system.org.entity.vo.SysOrgTreeData;
import com.yunhesoft.system.org.service.ISysOrgLogService;
import com.yunhesoft.system.org.service.ISysOrgRelationService;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.service.ISysDictTypeService;

import lombok.extern.log4j.Log4j2;

/**
 * 机构信息
 *
 * @category SysOrg接口
 * @date 2020/03/10
 */
@Log4j2
@Service
public class SysOrgImpl implements ISysOrgService {

    @Autowired
    private ISysOrgRelationService orgrelation;
    @Autowired
    private ISysOrgLogService orglog;
    @Autowired
    private EntityService entityService;
    @Autowired
    private ISysDictTypeService sysDictTypeServ;
    @Autowired
    private TreeServiceImpl treeServ;
    // 机构类型数据字典别名
    private static String ORG_TYPE = "sys_org_type";
    // 导入时判断导入层级字符
    private static String IMPORT_LEVEL_CHAR = "#";
    // 机构root节点级别
    private static int ROOT_LEVEL = 0;
    @Autowired
    private RedisUtil redis;
    @Autowired
    private ISysDataPermService dataPermServ; // 数据权限服务

    private static String RED_KEY = "SYSTEM:ORG:INFO";

    private String getRedKey() {

        if (MultiTenantUtils.enalbe()) {// 多租户模式
            return RED_KEY + ":" + MultiTenantUtils.getTenantId();
        } else {
            return RED_KEY;
        }

    }

    /**
     * 初始化跟节点
     *
     * @param String orgname
     * @return
     */
    @Override
    @Transactional
    public SysOrg initOrg(String orgname) {
        // 版本号
        return initOrg(orgname, "");
    }

    /**
     * 初始化跟节点
     *
     * @param String orgname
     * @return
     */
    @Override
    public SysOrg initOrg(String orgname, String tenantId) {
        // 版本号
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dayNow = sdf.format(d);
        SysOrg e = new SysOrg();
        e.setOrgcode(TMUID.getUID());
        e.setId(e.getOrgcode());
        e.setOrgname(orgname);
        e.setOrglevel(ROOT_LEVEL);
        e.setOrgpath("/" + e.getOrgcode());
        e.setTmSort(1);
        e.setOrgNumber("");
        e.setOrgHead("");
        e.setUsed(1);
        e.setVersionDate(dayNow);
        e.setSys(1);
//		e.setTenant_id(tenantId);
        int rs = entityService.rawInsertWithTenant(tenantId, e);// ====多租户====
        if (rs <= 0) {
            return null;
        } else {
            return e;
        }
    }

    /**
     * 插入机构数据
     *
     * @param list
     * @return
     */
    private int insertOrg(List<SysOrg> list) {
        int i = entityService.insertBatch(list);
        if (i > 0) {
            try {
                if (!redis.hasKey(getRedKey())) {
                    this.initRedis();
                } else {
                    this.setListToRedis(list);
                }
                // this.updateReids(list);
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return i;
    }

    /**
     * 插入机构数据
     *
     * @param list
     * @return
     */
    private int insertDisableTenantOrg(List<SysOrg> list, String tenantId) {
        int i = 1;
        try {
            entityService.rawInsertBatchWithTenant(tenantId, list);
            if (!redis.hasKey(getRedKey())) {
                this.initRedis();
            } else {
                this.setListToRedis(list);
            }
        } catch (Exception e) {
            log.error("", e);
            i = 0;
        }
        return i;
    }

    /**
     * 更新机构树
     *
     * @param list
     * @return
     */
    @Override
    public int updateOrg(List<SysOrg> list) {
        int i = 0;
        try {
            i = entityService.updateByIdBatch(list);
            if (i > 0) {
                if (!redis.hasKey(getRedKey())) {
                    this.initRedis();
                } else {
                    this.setListToRedis(list);
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return i;
    }

    /**
     * 获取数据
     *
     * @param list
     * @param name
     * @return
     */
    @Override
    public List<SysOrg> listDatas(List<String> orgcodes) {
        return this.listDatas(orgcodes, false);
    }

    private List<SysOrg> listDatas(List<String> orgcodes, boolean hasUsed) {
        List<SysOrg> returnList = null;
        try {
            if (StringUtils.isNotEmpty(orgcodes)) {
                returnList = new ArrayList<SysOrg>();
                if (orgcodes.size() == 1) {
                    SysOrg org = this.findOrgById(orgcodes.get(0), hasUsed);
                    if (org != null) {
                        returnList.add(org);
                    } else {
                        return null;
                    }
                } else {
                    List<SysOrg> listOrg = this.getOrgListFromRedis(orgcodes);//从缓存中获取
                    if (StringUtils.isNotEmpty(listOrg)) {
                        Map<String, SysOrg> orgMap = this.getOrgMap(listOrg);
                        for (String orgcode : orgcodes) {
                            SysOrg org = orgMap.get(orgcode);
                            if (org == null) {
                                org = this.findOrgById(orgcode, hasUsed);//redis中获取
                            }
                            if (org != null) {
                                returnList.add(org);
                            }
                        }
                    }
                    //循环从redis获取慢，可改为一次性取多个keys by x.zhong
//                    for (String orgid : orgcodes) {
//                        SysOrg org = this.findOrgById(orgid, hasUsed);
//                        if (org != null) {
//                            list.add(org);
//                        }
//                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
            throw new RuntimeException(e);
        }

        return returnList;
    }

    /**
     * 获取单节点信息数据
     *
     * @param id
     * @return List<SysOrg>
     */
    @Override
    public List<SysOrg> listData(String id) {
        List<SysOrg> list = null;
        try {
            SysOrg org = this.findOrgById(id);
            if (org != null) {
                list = new ArrayList<SysOrg>();
                list.add(org);
            }
        } catch (Exception e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
        return list;
    }

    /**
     * 获得机构根节点
     *
     * @return
     */
    private SysOrg getRootNode() {
        SysOrg root = null;
        Where where = Where.create();
        where.eq(SysOrg::getOrglevel, ROOT_LEVEL);
        where.eq(SysOrg::getUsed, 1);
        List<SysOrg> list = entityService.queryList(SysOrg.class, where);
        if (StringUtils.isEmpty(list)) {
            root = initOrg("机构");
        } else {
            root = list.get(0);
        }
        return root;
    }

    /**
     * 根据根据人员 获取 指定机构范围 的顶层机构id
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private String getTopOrgNodeByOrgScope(String orgScope) {
        return this.getTopOrgNodeByOrgScope(orgScope, SysUserHolder.getCurrentUser().getId());
    }

    @Override
    public String getTopOrgNodeByOrgScope(String orgScope, String userId) {
        //log.error("机构范围" + orgScope + "," + userId);
        if (StringUtils.isEmpty(orgScope) && StringUtils.isEmpty(userId)) {
            return null;
        }
        Map<String, String> orgScopeMap = this.getOrgScopeMap();
        String orgType = orgScopeMap.get(orgScope);
        if (StringUtils.isEmpty(orgType)) {
            return null;
        }
        //log.error("机构类型" + orgType);
        // 根据用户获取机构
        Where where = Where.create();
        where.eq(SysEmployeeOrg::getEmpid, userId);
        where.eq(SysEmployeeOrg::getUsed, 1);
        where.eq(SysEmployeeOrg::getStatus, 1);
        List<SysEmployeeOrg> sysEmployeeOrgs = entityService.rawQueryListByWhere(SysEmployeeOrg.class, where);
        if (StringUtils.isEmpty(sysEmployeeOrgs)) {
            return null;
        }
        //log.error("用户机构" + sysEmployeeOrgs);
        String thisOrgId = sysEmployeeOrgs.get(0).getOrgcode();
        SysOrg sysOrg = entityService.queryObjectById(SysOrg.class, thisOrgId);
        if (ObjUtils.isEmpty(sysOrg)) {
            return null;
        }
        String fullPath = sysOrg.getOrgpath();
        if (StringUtils.isEmpty(fullPath)) {
            return null;
        }
        String[] orgs = fullPath.split("/");
        List<String> orgPathList = Arrays.asList(orgs);
        Where where1 = Where.create();
        where1.eq(SysOrg::getUsed, 1);
        where1.in(SysOrg::getId, orgs);
        List<SysOrg> sysOrgs = entityService.rawQueryListByWhere(SysOrg.class, where1);
        if (StringUtils.isEmpty(sysOrgs)) {
            return null;
        }
        // 重排序 按照路径顺序倒序
        Collections.sort(sysOrgs, new Comparator<SysOrg>() {
            @Override
            public int compare(SysOrg o1, SysOrg o2) {
                int o1index = orgPathList.indexOf(o1.getId());
                int o2index = orgPathList.indexOf(o2.getId());
                if (o1index < o2index) {
                    return 1;
                } else if (o1index > o2index) {
                    return -1;
                } else {
                    return 0;
                }
            }
        });
        String orgcode = "";
        // 向上寻找第一个 符合机构范围的节点
        for (SysOrg org : sysOrgs) {
            if (orgType.equals(org.getOrgType())) {
                orgcode = org.getId();
                break;
            }
        }
        return orgcode;
    }

    /**
     * 机构范围关系映射
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private Map<String, String> getOrgScopeMap() {
        Map<String, String> map = new HashMap<>();
        // 本部门
        map.put("bbm", "department");
        // 本公司
        map.put("bgs", "company");
        // 本厂
        map.put("bc", "factory");
        // 本车间
        map.put("bcj", "workshop");
        // 本装置
        map.put("bzz", "equipment");
        // 本班组
        map.put("bbz", "shiftteam");
        return map;
    }

    /**
     * 获取id（包括自己）下级所有节点
     *
     * @param pid  父节点id
     * @param type 类型all：全部，subordinate：仅取下级
     * @return List<SysOrg>
     */
    @Override
    public List<SysOrgTreeData> listDatas(SysOrgSelect data) {
//        long aaa = System.currentTimeMillis();
        // 当机构范围不为空根据机构范围 确定顶层节点id
        //log.error("查询机构getOrgScope=" + data.getOrgScope());
        if (StringUtils.isNotEmpty(data.getOrgScope())) {
            // 当机构范围启用的时候 不使用数据权限
            data.setDataPermissVaild(false);
            // 根据范围代码获取指定范围的顶层节点
            String topid = this.getTopOrgNodeByOrgScope(data.getOrgScope());
            if (StringUtils.isNotEmpty(topid)) {
                data.setPorgcode(topid);
            }
        }
        SysOrg root = getRootNode();// 获得根节点， 没有数据初始化
        String pid = data.getPorgcode();
        if (StringUtils.isEmpty(pid)) {
            if (SysUserUtil.isAdmin()) {
                pid = root.getId();
            }
        }
        String filterOrgCode = data.getFilterOrgCode();// 过滤用机构代码
        boolean filter = false;// 是否过滤
        if (StringUtils.isNotEmpty(filterOrgCode)) {// 有过滤代码，父ID走过滤
            pid = filterOrgCode;
            filter = true;// 进行过滤
        }
        String datatype = data.getDatatype();
        String includeNode = data.getIncludeNode();
        if (datatype == null || "".equals(datatype)) {
            datatype = "all";
        }
        if (includeNode == null || "".equals(includeNode)) {
            includeNode = "true";
        }
        if (pid == null) {
            pid = "";
        }
        if ("".equals(pid)) {
            includeNode = "true";
        }
        List<SysOrgTreeData> listdata = new ArrayList<SysOrgTreeData>();
        // listdata.add(root);
        try {
            List<String> rightList = new ArrayList<String>();// 权限列表
            boolean allRight = true;// 查询全部的权限
            boolean changeRoot = false;// 是否需要改变根节点
            List<String> orgRootList = new ArrayList<String>();// 根节点
            Where where2 = Where.create();
            Order order2 = Order.create();
            if (!"".equals(pid)) { // 有父节点
                if (data.getIsSet() != null && data.getIsSet()) {
                    //编辑模式
                    where2.ne(SysOrg::getUsed, 0);
                } else {
                    where2.eq(SysOrg::getUsed, 1);
                }
                if ("all".equals(datatype)) {
                    // type=='1' 根据父节点id 查询下级所有节点需要递归往下查
                    // query.like(SysOrg::getOrgpath, pid).eq(SysOrg::getUsed,
                    // 1).orderByAsc(SysOrg::getOrglevel).orderByAsc(SysOrg::getTmSort);
//					where2.like(SysOrg::getOrgpath, pid);
                    likeOrgPath(where2, pid);// 机构路径查找
                    order2.orderByAsc(SysOrg::getOrglevel);
                    order2.orderByAsc(SysOrg::getTmSort);
                    // entityService.queryList(SysOrg.class, where2,order2);
                } else {
                    // type=='2' 根据父节点id 查询下级节点不递归继续往下查
                    List<String> listId = new ArrayList<String>();
                    // 通过父id获取下级id
                    List<SysOrgRelation> listRelation = orgrelation.listDataPids(pid);
                    if (listRelation != null && listRelation.size() > 0) {
                        int n = listRelation.size();
                        for (int i = 0; i < n; i++) {
                            String id = listRelation.get(i).getOrgcode();
                            listId.add(id);
                        }
                    } else {
                        return null;
                    }
                    // query.in(SysOrg::getOrgcode, listId).eq(SysOrg::getUsed,
                    // 1).orderByAsc(SysOrg::getTmSort);
                    where2.in(SysOrg::getOrgcode, listId.toArray());
                    order2.orderByAsc(SysOrg::getTmSort);
                    // entityService.queryList(SysOrg.class, where2,order2);
                }
            } else { // 没有父节点
                if (ObjUtils.isEmpty(data.getDataPermissVaild()) || data.getDataPermissVaild()) {
                    allRight = getOrgRight(rightList);
                }
                if (data.getIsSet() != null && data.getIsSet()) {
                    //编辑模式
                    where2.ne(SysOrg::getUsed, 0);
                } else {
                    where2.eq(SysOrg::getUsed, 1);
                }
                if ("all".equals(datatype)) {
                    if (allRight) {// 有全部权限
                        // type=='1' 查询公司及一下机构
                        // query.eq(SysOrg::getUsed,
                        // 1).orderByAsc(SysOrg::getOrglevel).orderByAsc(SysOrg::getTmSort);
                    } else {
                        changeRoot = true;
                        orgRootList = getRightOrgRoot(rightList);// 获取根节点
                        if (rightList.size() == 0) {
                            rightList.add("");// 添加一个条件避免in时空值
                        }
                        where2.in(SysOrg::getOrgcode, rightList.toArray());
                    }
                    order2.orderByAsc(SysOrg::getOrglevel);
                    order2.orderByAsc(SysOrg::getTmSort);
                } else {
                    if (allRight) {// 有全部权限
                        // type=='2' 根据父节点id 查询下级节点不递归继续往下查
                        List<Integer> listlevel = new ArrayList<Integer>();
                        listlevel.add(1);
                        listlevel.add(2);
                        // 通过父id获取下级id
                        // query.in(SysOrg::getOrglevel,
                        // listlevel).eq(SysOrg::getUsed,1).orderByAsc(SysOrg::getOrglevel).orderByAsc(SysOrg::getTmSort);
                        where2.in(SysOrg::getOrglevel, listlevel.toArray());
                    } else {
                        changeRoot = true;
                        orgRootList = getRightOrgRoot(rightList);// 获取根节点
                        if (orgRootList.size() == 0) {
                            orgRootList.add("");// 添加一个条件避免in时空值
                        }
                        where2.in(SysOrg::getOrgcode, orgRootList.toArray());
                    }
                    order2.orderByAsc(SysOrg::getOrglevel);
                    order2.orderByAsc(SysOrg::getTmSort);
                }
            }
            int sort = 0;
            Map<String, Integer> mapsort = new LinkedHashMap<String, Integer>();
            // List<SysOrg> list = this.list(query);
            // 获取全部机构类型
            List<SysOrgType> orgTypes = this.getOrgType();
            Map<String, String> orgTypesMap = new HashMap<>();
            for (SysOrgType orgType : orgTypes) {
                orgTypesMap.put(orgType.getValue(), orgType.getLabel());
            }

            List<SysOrgType> orgClasss = this.getOrgClassify();
            Map<String, String> orgClasssMap = new HashMap<>();
            for (SysOrgType orgType : orgClasss) {
                orgClasssMap.put(orgType.getValue(), orgType.getLabel());
            }


            List<SysOrg> list = entityService.queryList(SysOrg.class, where2, order2);
            Map<String, SysOrg> mapinfo = new LinkedHashMap<String, SysOrg>();
            if (list != null && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    SysOrg e = list.get(i);
                    String key = e.getOrgcode();
                    // 将机构类型代码做转换
                    e = this.orgTypeTransform(e, orgTypesMap, orgClasssMap);
                    list.set(i, e);
                    // 制作信息和排序map
                    mapinfo.put(key, e);
                    mapsort.put(key, e.getTmSort());
                    if (sort < e.getTmSort()) {
                        sort = e.getTmSort();
                    }
                }
            } else {
                // list.add(root);
                return null;
            }
            // 初始化
            //where2 = Where.create();
            // order2 = Order.create();
            // where2.eq(SysOrgRelation::getUsed, 1);
            //List<SysOrgRelation> listRelation = entityService.queryList(SysOrgRelation.class, where2);
            Map<String, String> map = new LinkedHashMap<String, String>();
            Map<String, List<String>> pmap = new LinkedHashMap<String, List<String>>();
            Map<String, LinkedHashMap> mapRel = orgrelation.getOrgRelationMapFromReids();
            if (mapRel != null && mapRel.size() > 0) {
                for (String key : mapRel.keySet()) {
                    LinkedHashMap obj = mapRel.get(key);
                    //String orgcode = key;
                    //String porgCode = mapRel.get(key).get("porgcode").toString();
                    SysOrgRelation bean = orgrelation.map2bean(obj);
                    this.setOrgRel(bean, changeRoot, orgRootList, map, pmap);
                }
            } else {
                List<SysOrgRelation> listRelation = orgrelation.getDataFromDb();
                orgrelation.setOrgRelationToReids(listRelation);
                if (listRelation != null && listRelation.size() > 0) {
                    int n = listRelation.size();
                    for (int i = 0; i < n; i++) {
                        SysOrgRelation obj = listRelation.get(i);
                        this.setOrgRel(obj, changeRoot, orgRootList, map, pmap);
                    }
                }
            }
            SysOrg e1 = new SysOrg();// 根节点
            SysOrgTreeData o1 = new SysOrgTreeData();
            if ("true".equals(includeNode)) {
                // query = new LambdaQueryWrapper<SysOrg>();
                // 初始化
                where2 = Where.create();
                order2 = Order.create();
                if (data.getIsSet() != null && data.getIsSet()) {
                    //编辑模式
                    where2.ne(SysOrg::getUsed, 0);
                } else {
                    where2.eq(SysOrg::getUsed, 1);
                }
                if ("".equals(pid)) {
                    // query.eq(SysOrg::getOrglevel, 1).eq(SysOrg::getUsed,
                    // 1).orderByAsc(SysOrg::getTmSort);
                    where2.eq(SysOrg::getOrglevel, ROOT_LEVEL);
                    order2.orderByAsc(SysOrg::getTmSort);
                } else {
                    // query.eq(SysOrg::getOrgcode, pid).eq(SysOrg::getUsed,
                    // 1).orderByAsc(SysOrg::getTmSort);
                    where2.eq(SysOrg::getOrgcode, pid);
                    order2.orderByAsc(SysOrg::getTmSort);
                }
                // List<SysOrg> listpid = this.list(query);
                List<SysOrg> listpid = entityService.queryList(SysOrg.class, where2, order2);
                for (SysOrg sysOrg : listpid) {
                    // 将机构类型代码做转换
                    sysOrg = this.orgTypeTransform(sysOrg, orgTypesMap, orgClasssMap);
                }
                if (listpid != null && listpid.size() > 0) {
                    e1 = listpid.get(0);
                }
                o1 = ObjUtils.copyTo(e1, SysOrgTreeData.class);
                o1.setPorgcode(map.get(e1.getOrgcode()));
//				o1.setHasChildren(false);

                String e1_orgCode = e1.getOrgcode();
                if (changeRoot) {
                    e1_orgCode = "root";// 切换管辖根节点
                }

//				if (pmap != null && pmap.containsKey(e1_orgCode)) {
//					List<String> lst = pmap.get(e1_orgCode);
//					if (lst != null && lst.size() > 0) {
//						o1.setHasChildren(true);
//					}
//				}
                pid = e1_orgCode;
            }
            if (pmap != null && pmap.containsKey(pid)) {
                List<String> listc = pmap.get(pid);
                if (listc != null && listc.size() > 0) {
                    listc = getListSort(listc, mapsort, mapinfo, sort);
                    int n = listc.size();
                    for (int i = 0; i < n; i++) {
                        String orgcode = listc.get(i);
                        // //判断刨除节点
                        String nocorgcode = data.getNotContainOrgcode();
                        if (nocorgcode != null && !"".equals(nocorgcode)) {
                            // 如果是刨除节点
                            if (orgcode.equals(nocorgcode)) {
                                continue;
                            }
                        }
                        SysOrg e2 = mapinfo.get(orgcode);
                        SysOrgTreeData o2 = ObjUtils.copyTo(e2, SysOrgTreeData.class);
                        o2.setPorgcode(pid);
                        List<SysOrgTreeData> listch = getTrees(orgcode, nocorgcode, mapinfo, pmap, mapsort, sort);
                        o2.setChildren(listch);
//						o2.setHasChildren(false);
//						if (pmap != null && pmap.containsKey(e2.getOrgcode())) {
//							List<String> lst = pmap.get(e2.getOrgcode());
//							if (lst != null && lst.size() > 0) {
//								o2.setHasChildren(true);
//							}
//						}
                        // 数据位置按排序位置插入
                        // int m=o2.getTmSort()-1;
                        // listdata.set(m,o2);
                        listdata.add(o2);
                    }
                }

            }
            if ("true".equals(includeNode) && o1 != null && o1.getOrgcode() != null) {
                o1.setChildren(listdata);
//				o1.setHasChildren(false);
//				if (listdata != null && listdata.size() > 0) {
//					o1.setHasChildren(true);
//				}
                listdata = new ArrayList<SysOrgTreeData>();
                listdata.add(o1);
            }
            if (filter) {// 机构过滤，需要添加机构根节点,让树形可以显示过滤的这个节点
                if (o1 != null) {
                    o1.setPorgcode("root");
                }
                SysOrgTreeData oRoot = ObjUtils.copyTo(root, SysOrgTreeData.class);
                oRoot.setChildren(listdata);
                listdata = new ArrayList<SysOrgTreeData>();
                listdata.add(oRoot);
            }
            if (StringUtils.isNotEmpty(listdata)) {
                if ("false".equals(data.getShowRootNode())) {// 不显示根节点
                    return listdata.get(0).getChildren();
                } else {
                    if (listdata.get(0).getSys() != null && listdata.get(0).getSys() == 1) {
                        if (SysUserUtil.isAdmin()) {
                            listdata.get(0).setIsEdit(1);
                        } else {
                            listdata.get(0).setIsEdit(0);//非超级管理员 机构节点不能编辑
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error("", ex);
        }
//        aaa = System.currentTimeMillis() - aaa;
//        log.info("查询机构树耗时：" + aaa + "ms");
        return listdata;
    }

    private void setOrgRel(SysOrgRelation obj, boolean changeRoot, List<String> orgRootList, Map<String, String> map, Map<String, List<String>> pmap) {
        //SysOrgRelation obj = listRelation.get(i);
        String orgcode = obj.getOrgcode();
        String porgCode = obj.getPorgcode();
        if (changeRoot) {
            if (orgRootList.contains(orgcode)) {// 是管辖根节点
                porgCode = "root";
            }
        }
        map.put(orgcode, porgCode);
        if (pmap != null && pmap.containsKey(porgCode)) {
            List<String> listCode = pmap.get(porgCode);
            listCode.add(orgcode);
            pmap.put(porgCode, listCode);
        } else {
            List<String> listCode = new ArrayList<String>();
            listCode.add(orgcode);
            pmap.put(porgCode, listCode);
        }
    }

    /**
     * 获取父机构树形
     *
     * @param data
     * @return
     */
    @Override
    public List<SysOrgTreeData> getParentOrgTree(SysOrgSelect data) {
        List<SysOrgTreeData> result = new ArrayList<SysOrgTreeData>();
        if (SysUserUtil.isAdmin()) {
            result = this.listDatas(data);
        } else {
            String orgCode = "";
            if (data != null) {
                orgCode = data.getFilterOrgCode();
                if (StringUtils.isEmpty(orgCode)) {
                    SysUser user = SysUserHolder.getCurrentUser();
                    if (user != null) {
                        orgCode = user.getOrgId();
                    }
                }
            }
            if (StringUtils.isNotEmpty(orgCode)) {
                SysOrg orgObj = this.findOrgById(orgCode);
                if (orgObj != null) {
                    String orgpath = orgObj.getOrgpath();
                    if (StringUtils.isNotEmpty(orgpath)) {
                        if (orgpath.startsWith("/")) {
                            orgpath = orgpath.substring(1);
                        }
                        String[] idArr = orgpath.split("/");
                        if (idArr != null && idArr.length > 0) {
                            HashMap<String, String> compareMap = this.getParentChildMap(idArr);
                            List<String> orgIdList = Arrays.asList(idArr);
                            List<SysOrg> orgList = this.getOrgListById(orgIdList);
                            if (StringUtils.isNotEmpty(orgList) && orgList.size() == idArr.length) { //判断数据准确性
                                Map<String, SysOrg> orgMap = orgList.stream().collect(Collectors.toMap(SysOrg::getOrgpath, Function.identity(), (key1, key2) -> key2));
                                if (StringUtils.isNotEmpty(orgMap) && StringUtils.isNotEmpty(compareMap) && compareMap.containsKey("")) {
                                    String rootCode = compareMap.get("");
                                    if (StringUtils.isNotEmpty(rootCode) && orgMap.containsKey(rootCode)) {
                                        SysOrg rootObj = orgMap.get(rootCode);
                                        SysOrgTreeData rootTree = new SysOrgTreeData();
                                        BeanUtils.copyProperties(rootObj, rootTree); // 赋予返回对象
                                        this.setOrgTreeChildData(rootTree, compareMap, orgMap);
                                        result.add(rootTree);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    //递归组对父子节点
    private HashMap<String, String> getParentChildMap(String[] idArr) {
        HashMap<String, String> map = new HashMap<String, String>();
        if (idArr != null && idArr.length > 0) {
            String key = "";
            String value = "";
            for (int i = 0; i < idArr.length; i++) {
                String id = idArr[i];
                value += "/" + id;
                map.put(key, value);
                key += "/" + id;
            }
        }
        return map;
    }

    // 设置树形子节点
    private void setOrgTreeChildData(SysOrgTreeData treeData, HashMap<String, String> compareMap, Map<String, SysOrg> orgMap) {
        if (StringUtils.isNotNull(treeData)) {
            String id = treeData.getId();
            String orgpath = treeData.getOrgpath();
            List<SysOrgTreeData> childTreeList = new ArrayList<SysOrgTreeData>();
            if (StringUtils.isNotEmpty(orgMap) && StringUtils.isNotEmpty(compareMap) && StringUtils.isNotEmpty(orgpath) && compareMap.containsKey(orgpath)) {
                String childOrgpath = compareMap.get(orgpath);
                if (orgMap.containsKey(childOrgpath)) {
                    SysOrg orgObj = orgMap.get(childOrgpath);
                    // 赋值属性
                    SysOrgTreeData childTree = new SysOrgTreeData();
                    BeanUtils.copyProperties(orgObj, childTree); //赋予返回对象
                    childTree.setPorgcode(id);
                    childTreeList.add(childTree);
                    //递归判断
                    this.setOrgTreeChildData(childTree, compareMap, orgMap);
                }
            }
            if (StringUtils.isNotEmpty(childTreeList)) {
                treeData.setChildren(childTreeList);
            }
            treeData.setHasChildren(StringUtils.isNotEmpty(childTreeList));
        }
    }

    /**
     * 机构类型代码转义
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private SysOrg orgTypeTransform(SysOrg e, Map<String, String> orgTypesMap, Map<String, String> orgClasssMap) {
        String orgTypeLabel = "";
        String orgType = e.getOrgType();
        if (StringUtils.isNotEmpty(orgType)) {
            if (orgType.contains("[")) {
                JSONArray parse = (JSONArray) JSONArray.parse(orgType);
                for (int i1 = 0; i1 < parse.size(); i1++) {
                    String o = (String) parse.get(i1);
                    if (StringUtils.isNotEmpty(orgTypeLabel)) {
                        orgTypeLabel = orgTypeLabel + "," + orgTypesMap.get(o);
                    } else {
                        orgTypeLabel = orgTypesMap.get(o);
                    }
                }
            } else {
                orgTypeLabel = orgTypesMap.get(orgType);
            }
        }
        e.setOrgTypeLabel(orgTypeLabel);
        if (StringUtils.isNotEmpty(e.getOrgClassify())) {
            e.setOrgClassifyLabel(orgClasssMap.get(e.getOrgClassify()));
        }
        return e;
    }

    /**
     * 递归树结点
     *
     * @param String      pid
     * @param Map<String, SysOrg> mapinfo
     * @param Map<String, List<String>> pmap
     * @return List<SysOrgTreeDate>
     */
    private List<SysOrgTreeData> getTrees(String pid, String nocorgcode, Map<String, SysOrg> mapinfo,
                                          Map<String, List<String>> pmap, Map<String, Integer> mapsort, int sort) {
        List<SysOrgTreeData> listdata = new ArrayList<SysOrgTreeData>();
        List<String> listc = pmap.get(pid);
        if (listc != null && listc.size() > 0) {
            listc = getListSort(listc, mapsort, mapinfo, sort);
            int n = listc.size();
            for (int i = 0; i < n; i++) {
                String orgcode = listc.get(i);
                if (mapinfo == null || !mapinfo.containsKey(orgcode)) {
                    continue;
                }
                if (nocorgcode != null && !"".equals(nocorgcode)) {
                    // 如果是刨除节点
                    if (orgcode.equals(nocorgcode)) {
                        continue;
                    }
                }
                SysOrg e2 = mapinfo.get(orgcode);
                SysOrgTreeData o2 = ObjUtils.copyTo(e2, SysOrgTreeData.class);
                o2.setPorgcode(pid);
                List<SysOrgTreeData> listch = getTrees(orgcode, nocorgcode, mapinfo, pmap, mapsort, sort);
                o2.setChildren(listch);
                // o2.setHasChildren(false);
//				if (pmap != null && pmap.containsKey(e2.getOrgcode())) {
//					List<String> lst = pmap.get(e2.getOrgcode());
//					if (lst != null && lst.size() > 0) {
//						// o2.setHasChildren(true);
//					}
//				}
                // int m=o2.getTmSort()-1;
                // listdata.set(m,o2);
                listdata.add(o2);
            }
        }
        return listdata;
    }

    private List<String> getListSort(List<String> listcode, Map<String, Integer> mapsort, Map<String, SysOrg> mapinfo,
                                     Integer sort) {
        List<String> list = new ArrayList<String>();
        // 解决序号乱时节点丢失的问题
        // for (int i = 0; i <= sort; i++) {
        int listInitSize = sort.intValue() > listcode.size() ? sort.intValue() : listcode.size();
        for (int i = 0; i <= listInitSize; i++) {
            list.add(null);
        }
        for (int i = 0; i < listcode.size(); i++) {
            String orgcode = listcode.get(i);
            if (mapinfo == null || !mapinfo.containsKey(orgcode)) {
                continue;
            }
            int _sort = mapsort.get(orgcode);
            // 解决sort值重复的节点发生数据覆盖，导致前端页面丢失节点的问题
            if (list.get(_sort) == null && list.size() > _sort) {
                list.set(_sort, orgcode);
            } else {
                list.add(orgcode);
            }
        }
        list.removeIf(Objects::isNull);
        return list;
    }

    /**
     * 创建部门信息
     *
     * @param SysOrgAdd
     * @return SysOrgTreeDate
     */
    @Override
    @Transactional
    public boolean insertData(List<SysOrgAdd> listData) {
        boolean bln = false;
        try {
            if (StringUtils.isEmpty(listData)) {
                return false;
            }
            String tenantId = listData.get(0).getTenantId();// 租户id

            SysOrg porg = new SysOrg();
            String pid = "";
            int sort = 1;
            int level = 0;
            Date d = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String dayNow = sdf.format(d);
            List<SysOrg> listo = new ArrayList<SysOrg>();
            List<SysOrgRelation> listr = new ArrayList<SysOrgRelation>();
            List<SysOrgLog> listl = new ArrayList<SysOrgLog>();
            if (StringUtils.isEmpty(listData.get(0).getId())) {// 非导入添加
                Where where = Where.create();
                // Order order = Order.create();
                pid = listData.get(0).getPorgcode();
                // 查询父节点信息
                where.eq(SysOrg::getOrgcode, pid);
                where.eq(SysOrg::getUsed, 1);

                List<SysOrg> listPorg = new ArrayList<SysOrg>();
                if (tenantId != null && !"".equals(tenantId)) {
//					where.eq(SysOrg::getTenant_id, tenantId);
                    // 注册不带tenantid
                    listPorg = entityService.rawQueryListByWhereWithTenant(tenantId, SysOrg.class, where);// ====更换多租户保存方式====
                } else {
                    listPorg = entityService.queryList(SysOrg.class, where);
                }

                if (listPorg != null && listPorg.size() > 0) {
                    porg = listPorg.get(0);
                } else {
                    return false;
                }
                level = porg.getOrglevel() + 1;
                // 查询父节点下排序的最值
                sort = getOrgSort(pid, porg.getOrglevel(), tenantId);
            }
            for (int i = 0; i < listData.size(); i++) {
                SysOrgAdd data = listData.get(i);
                pid = data.getPorgcode();
                // 部门主属性表
                SysOrg bOrg = new SysOrg();
                // 创建部门主表数据
                String orgCode = "";
                if (StringUtils.isEmpty(data.getId())) {
                    orgCode = TMUID.getUID();
                } else {
                    orgCode = data.getId();
                }

//				bOrg.setTenant_id(tenantId);
                bOrg.setOrgcode(orgCode);
                bOrg.setId(orgCode);
                bOrg.setOrgHead(data.getOrgHead());
                bOrg.setOrgNumber(data.getOrgNumber());
                bOrg.setOrgname(data.getOrgname());
                bOrg.setVersionDate(dayNow);
                bOrg.setUsed(1);
                // 路径=父路径+/+当前的orgCode
                if (StringUtils.isNotEmpty(data.getId())) {
                    bOrg.setOrgpath(data.getOrgpath());
                    bOrg.setOrglevel(data.getOrglevel());
                    bOrg.setTmSort(data.getTmSort());
                } else {
                    bOrg.setOrgpath(porg.getOrgpath() + "/" + orgCode);
                    // 父节点的级别+1
                    bOrg.setOrglevel(level);
                    bOrg.setTmSort(sort + i);
                }
                // 机构类型
                bOrg.setOrgType(data.getOrgType());
                bOrg.setOrgClassify(data.getOrgClassify());
                bOrg.setPhone(data.getPhone());
                bOrg.setIsHidden(data.getIsHidden());
                //设置停用
                if (bOrg.getIsHidden() != null && bOrg.getIsHidden() == 1) {
                    //停用  1
                    bOrg.setUsed(-1);
                } else {
                    bOrg.setUsed(1);
                }
                listo.add(bOrg);
                // 部门层级关系表 orgrelation
                SysOrgRelation borgRela = new SysOrgRelation();
                // 创建部门关系表数据
                borgRela.setId(TMUID.getUID());
                borgRela.setOrgcode(orgCode);
                borgRela.setPorgcode(pid);
                borgRela.setUsed(1);
                borgRela.setVersionDate(dayNow);
//				borgRela.setTenant_id(tenantId);
                listr.add(borgRela);
                // 部门变动日志表
                SysOrgLog borglog = new SysOrgLog();
                borglog = getSysOrgLog(dayNow, bOrg.getOrgcode(), bOrg.getOrgname(), pid, bOrg.getOrgcode(),
                        bOrg.getOrgname(), pid, 1);
//				borglog.setTenant_id(tenantId);
                borglog.setDescription("新建【" + bOrg.getOrgname() + "】部门;");
                listl.add(borglog);
            }
            boolean bool = true;
            int rs = 0;
            if (tenantId != null && !"".equals(tenantId)) {
                rs = this.insertDisableTenantOrg(listo, tenantId);
            } else {
                rs = this.insertOrg(listo);
            }
            if (rs <= 0) {
                bool = false;
            }
            if (bool) {
                if (tenantId != null && !"".equals(tenantId)) {
                    bool = orgrelation.insertDataWithTenant(listr, tenantId);
                } else {
                    bool = orgrelation.insertData(listr);
                }

                if (tenantId != null && !"".equals(tenantId)) {
                    if (bool) {
                        bool = orglog.saveDataDataWithTenant(listl, tenantId);
                    }
                } else {
                    if (bool) {
                        bool = orglog.saveData(listl);
                    }
                }

            }
            bln = bool;
        } catch (Exception ex) {
            log.error("", ex);
            bln = false;
        }

        return bln;
    }

    /**
     * 创建机构实体类
     *
     * @param data
     * @return
     */
    @Override
    public SysOrg insertSysOrgData(SysOrgAdd data) {
        SysOrg bOrg = new SysOrg();
        try {
            SysOrg porg = new SysOrg();
            String pid = "";
            int sort = 1;
            int level = 0;
            Date d = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String dayNow = sdf.format(d);
            if (StringUtils.isEmpty(data.getId()) || data.getTmSort() == null) {// 非导入添加
                Where where = Where.create();
                // Order order = Order.create();
                pid = data.getPorgcode();
                // 查询父节点信息
                where.eq(SysOrg::getOrgcode, pid);
                where.eq(SysOrg::getUsed, 1);
                List<SysOrg> listPorg = entityService.queryList(SysOrg.class, where);
                if (listPorg != null && listPorg.size() > 0) {
                    porg = listPorg.get(0);
                    level = porg.getOrglevel() + 1;
                    // 查询父节点下排序的最值
                    sort = getOrgSort(pid, porg.getOrglevel(), "");
                }
            }
            pid = data.getPorgcode();
            // 部门主属性表
            // 创建部门主表数据
            String orgCode = "";
            if (StringUtils.isEmpty(data.getId())) {
                orgCode = TMUID.getUID();
            } else {
                orgCode = data.getId();
            }
            bOrg.setOrgcode(orgCode);
            bOrg.setId(orgCode);
            bOrg.setOrgHead(data.getOrgHead());
            bOrg.setOrgNumber(data.getOrgNumber());
            bOrg.setOrgname(data.getOrgname());
            bOrg.setVersionDate(dayNow);
            bOrg.setUsed(1);
            // 路径=父路径+/+当前的orgCode
            if (StringUtils.isNotEmpty(data.getId())) {
                bOrg.setOrgpath(data.getOrgpath());
                bOrg.setOrglevel(data.getOrglevel());
                if (data.getTmSort() != null && data.getTmSort() > 0) {
                    bOrg.setTmSort(data.getTmSort());
                } else {
                    bOrg.setTmSort(sort);
                }
            } else {
                bOrg.setOrgpath(porg.getOrgpath() + "/" + orgCode);
                // 父节点的级别+1
                bOrg.setOrglevel(level);
                bOrg.setTmSort(sort + 1);
            }
            // 机构类型
            bOrg.setOrgType(data.getOrgType());
            bOrg.setPhone(data.getPhone());
        } catch (Exception ex) {
            log.error("", ex);
        }

        return bOrg;
    }

    /**
     * 创建机构关联关系实体
     *
     * @param orgCode
     * @param pid
     * @return
     */
    @Override
    public SysOrgRelation insertSysOrgRelationData(String orgCode, String pid) {
        SysOrgRelation borgRela = new SysOrgRelation();
        try {
            Date d = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String dayNow = sdf.format(d);
            // 部门层级关系表 orgrelation
            // 创建部门关系表数据
            borgRela.setId(TMUID.getUID());
            borgRela.setOrgcode(orgCode);
            borgRela.setPorgcode(pid);
            borgRela.setUsed(1);
            borgRela.setVersionDate(dayNow);
        } catch (Exception ex) {
            log.error("", ex);
        }
        return borgRela;
    }

    /**
     * 写日志
     *
     * @param msg
     */
    private void writeLog(String msg) {
        SysUser user = SysUserHolder.getCurrentUser();
        SysOrgLog borglog = new SysOrgLog();
        borglog.setCreateTime(new Date());
        borglog.setCreateBy(user.getRealName());
        borglog.setId(TMUID.getUID());
        borglog.setDescription(msg);
    }

    /**
     * 获取排序值
     *
     * @param pid       父ID
     * @param porgLevel 父机构层级
     * @return
     */
    private int getOrgSort(String pid, int porgLevel, String tenantId) {
        int sort = 1;
        Where where = Where.create();
        Order order = Order.create();
        int level = porgLevel + 1;
        where.eq(SysOrg::getUsed, 1);
        where.eq(SysOrg::getOrglevel, level);
//		where.like(SysOrg::getOrgpath, pid);
        likeOrgPath(where, pid);// 机构路径查找
        order.orderByDesc(SysOrg::getTmSort);
        List<SysOrg> listSort = entityService.queryList(SysOrg.class, where, order);
        if (listSort != null && listSort.size() > 0) {
            sort = listSort.get(0).getTmSort() + 1;
        }
        return sort;
    }

    /**
     * 删除部门信息
     *
     * @param SysOrgTreeData
     * @return boolean
     */
    @Override
    @Transactional
    public boolean deleteData(SysOrgDel orgdel) {
        boolean bool = false;
        try {
            String id = "";
            if (orgdel.getOrgcode() == null) {
                return false;
            } else {
                id = orgdel.getOrgcode();
            }
            Date d = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String dayNow = sdf.format(d);

            // 删除SysOrgRelation
            List<SysOrgRelation> delRelation = new ArrayList<SysOrgRelation>();
            // 更新SysOrg used=0
            List<SysOrg> listUp = new ArrayList<SysOrg>();
            // 添加日志
            List<SysOrgLog> listlog = new ArrayList<SysOrgLog>();

            Where where = Where.create();
            Order order = Order.create();
            // 封装一个部门关系的map

            Map<String, SysOrgRelation> map = orgrelation.getOrgRelation();
            // Map<String, SysOrgRelation> map = new LinkedHashMap<String,
            // SysOrgRelation>();
            // List<SysOrgRelation> listcj = orgrelation.listData();
            // if (listcj != null && listcj.size() > 0) {
            // map = listcj.stream().collect(Collectors.toMap(SysOrgRelation::getOrgcode, a
            // -> a, (k1, k2) -> k1));
            // }

            // 根据路径，和节点 查询下级都有那些子节点
            // query.like(SysOrg::getOrgpath, id).eq(SysOrg::getUsed,
            // 1).orderByAsc(SysOrg::getOrglevel);
//			where.like(SysOrg::getOrgpath, id);
            likeOrgPath(where, id);// 机构路径查找
            where.eq(SysOrg::getUsed, 1);
            order.orderByAsc(SysOrg::getOrglevel);
            List<SysOrg> list = entityService.queryList(SysOrg.class, where, order);
            // List<SysOrg> list = this.list(query);
            if (list != null && list.size() > 0) {
                int n = list.size();
                // List<String> listp = new ArrayList<String>();
                // List<SysOrgRelation> listRe = new ArrayList<SysOrgRelation>();
                // 有子记录---要处理排序问题
                for (int i = 0; i < n; i++) {
                    // 判断是否有人，有人不能删除
                    SysOrg org = list.get(i);
                    // 删除
                    org.setUsed(0);
                    org.setVersionDate(dayNow);
                    // TODO 版本未处理
                    listUp.add(org);
                    String pid = "";
                    if (map != null && map.containsKey(org.getOrgcode())) {
                        pid = map.get(org.getOrgcode()).getPorgcode();
                    }
                    if (i == 0) {
                        // 处理排序问题
                        updateSort(org, pid, listUp);
                    }
                    // listp.add(org.getOrgcode());
                    // 获取关系数据
                    SysOrgRelation re = map.get(org.getOrgcode());
                    if (re != null) {
                        re.setUsed(0);
                        delRelation.add(re);
                    }
                    // 日志
                    SysOrgLog slog = getSysOrgLog(dayNow, org.getOrgcode(), org.getOrgname(), pid, org.getOrgcode(),
                            org.getOrgname(), pid, 3);
                    slog.setDescription("删除了【" + org.getOrgname() + "】;");
                    listlog.add(slog);
                }
                // if (listp != null && listp.size() > 0) {
                // query1.in(SysOrgRelation::getOrgcode, listp);
                // where2.andIns(SysOrgRelation::getOrgcode, listp.toArray());
                // }
            }
            /*
             * // 封装要删除关系表数据的id List<SysOrgRelation> listrela =
             * entityService.queryList(SysOrgRelation.class, where2); //
             * List<SysOrgRelation> listrela = orgrelation.listData(query1); if (listrela !=
             * null && listrela.size() > 0) { int n = listrela.size(); for (int i = 0; i <
             * n; i++) { SysOrgRelation e = listrela.get(i); e.setUsed(0);
             * delRelation.add(e); } }
             */

            // 更新SysOrg used=0
            bool = true;
            int rs = this.updateOrg(listUp);// entityService.updateByIdBatch(listUp);
            if (rs <= 0) {
                bool = false;
            }
            // bool = this.updateBatchById(listUp);
            if (bool) {
                // 并且删除关系表数据
                bool = orgrelation.updateDataBatch(delRelation);
                if (bool) {
                    // 添加日志
                    bool = orglog.saveData(listlog);
                }
            }
        } catch (Exception ex) {
            log.error("", ex);
        }

        return bool;
    }

    /**
     * 更新排序
     *
     * @param SysOrg 部门信息
     * @param String pid 父节点id
     * @return List<SysOrg>
     */
    private void updateSort(SysOrg e, String pid, List<SysOrg> listUp) {
        try {
            Where where = Where.create();
            Order order = Order.create();
//			where.like(SysOrg::getOrgpath, pid);
            likeOrgPath(where, pid);// 机构路径查找
            where.eq(SysOrg::getUsed, 1);
            where.eq(SysOrg::getOrglevel, e.getOrglevel());
            where.ne(SysOrg::getOrgcode, e.getOrgcode()); // 刨除被删除节点
            order.orderByAsc(SysOrg::getTmSort);
            List<SysOrg> list = entityService.queryList(SysOrg.class, where, order);
            // List<SysOrg> list = this.list(query);
            if (list != null && list.size() > 0) {
                int n = list.size();
                for (int i = 0; i < n; i++) {
                    SysOrg bean = list.get(i);
                    bean.setTmSort(i + 1);
                    listUp.add(bean);
                }
            }
        } catch (Exception ex) {
            log.error("", ex);
        }
    }

    /**
     * 创建日志bean
     *
     * @param String dayNow 版本
     * @param String orgcode 部门编码
     * @param String orgname 部门名称
     * @param String pid 父节点id
     * @return SysOrgLog
     */
    private SysOrgLog getSysOrgLog(String dayNow, String orgcode1, String orgname1, String pid1, String orgcode2,
                                   String orgname2, String pid2, int type) {
        // 创建日志表数据
        SysOrgLog borglog = new SysOrgLog();
        borglog.setId(TMUID.getUID());
        borglog.setVersionDate(dayNow);
        borglog.setFormerOrgcode(orgcode1);
        borglog.setFormerOrgname(orgname1);
        borglog.setFormerPorgcode(pid1);
        borglog.setDestinationOrgcode(orgcode2);
        borglog.setDestinationOrgname(orgname2);
        borglog.setDestinationPorgcode(pid2);
        borglog.setVersionType(type);
        return borglog;
    }

    /**
     * 校验部门名称是否重复
     *
     * @param SysOrgAdd
     * @return bool
     */
    @Override
    public String getDuplicationName(List<SysOrgAdd> listadd) {
        String str = "";
        String porgcode = "";
        int orglevel = 0;
        Map<String, String> map = new HashMap<String, String>();
        List<String> listName = new ArrayList<String>();
        for (int i = 0; i < listadd.size(); i++) {
            SysOrgAdd e = listadd.get(i);
            String orgcode = e.getOrgname();
            if (orgcode == null || "".equals(orgcode)) {
                continue;
            }
            if (map != null && map.containsKey(orgcode)) {
                str = str + "【" + e.getOrgname() + "】";
            }
            map.put(orgcode, "");
            porgcode = e.getPorgcode();
            orglevel = e.getOrglevel() + 1;
            listName.add(e.getOrgname());
        }
        if (!"".equals(str)) {
            return str;
        }
        // 查询同层级的所有节点中，是否有同名
        // LambdaQueryWrapper<SysOrg> query = new LambdaQueryWrapper<SysOrg>();
        // query.like(SysOrg::getOrgpath, porgcode).eq(SysOrg::getUsed,
        // 1).eq(SysOrg::getOrglevel, orglevel)
        // .in(SysOrg::getOrgname, listName);
        // List<SysOrg> list = this.list(query);

        Where where = Where.create();
//		where.like(SysOrg::getOrgpath, porgcode);
        likeOrgPath(where, porgcode);// 机构路径查找
        where.eq(SysOrg::getUsed, 1);
        where.eq(SysOrg::getOrglevel, orglevel);
        where.in(SysOrg::getOrgname, listName.toArray());
        List<SysOrg> list = entityService.queryList(SysOrg.class, where);
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                SysOrg e = list.get(i);
                str = str + "【" + e.getOrgname() + "】";
            }
        }
        return str;
    }

    /**
     * 校验部门名称是否重复
     *
     * @param SysOrgAdd
     * @return bool
     */
    @Override
    public boolean getDuplicationName(String orgname, String porgcode, int orglevel) {
        boolean bool = false;
        if (orgname == null || "".equals(orgname)) {
            return true;
        }
        // 查询同层级的所有节点中，是否有同名
        // LambdaQueryWrapper<SysOrg> query = new LambdaQueryWrapper<SysOrg>();
        // query.like(SysOrg::getOrgpath, porgcode).eq(SysOrg::getUsed,
        // 1).eq(SysOrg::getOrglevel, orglevel)
        // .eq(SysOrg::getOrgname, orgname);
        // List<SysOrg> list = this.list(query);

        Where where = Where.create();
//		where.like(SysOrg::getOrgpath, porgcode);
        likeOrgPath(where, porgcode);// 机构路径查找
        where.eq(SysOrg::getUsed, 1);
        where.eq(SysOrg::getOrglevel, orglevel);
        where.eq(SysOrg::getOrgname, orgname);
        List<SysOrg> list = entityService.queryList(SysOrg.class, where);
        if (list != null && list.size() > 0) {
            // 有数据，不可用
            bool = false;
        } else {
            // 没有数据，可用
            bool = true;
        }
        return bool;
    }

    /**
     * 根据orgcode 获取节点对象
     *
     * @param orgcode
     * @return
     */
    @Override
    public SysOrg getOrgByOrgcode(String orgcode) {
        SysOrg org = this.findOrgById(orgcode);
        if (org != null && org.getUsed() == 1) {
            return org;
        } else {
            return null;
        }
        /*
         * Where where = Where.create(); where.eq(SysOrg::getOrgcode, orgcode);
         * where.eq(SysOrg::getUsed, 1); List<SysOrg> list =
         * entityService.queryList(SysOrg.class, where); if (list != null && list.size()
         * > 0) { return list.get(0); } else { return null; }
         */
    }

    /**
     * 批量更新机构数据
     *
     * @param listUpdate
     * @return
     */
    @Override
    public boolean updateData(List<SysOrgAdd> listUpdate) {
        boolean bool = false;
        try {
            if (StringUtils.isNotEmpty(listUpdate)) {
                Date d = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String dayNow = sdf.format(d);
                String des = "";
                List<SysOrgLog> listlog = new ArrayList<SysOrgLog>();// 日志表
                List<SysOrg> updateList = new ArrayList<SysOrg>();// 机构表
                List<SysOrgRelation> updateRelationList = new ArrayList<SysOrgRelation>();// 机构关系表
                List<SysOrgMove> moveList = new ArrayList<SysOrgMove>();// 改变父机构
                for (SysOrgAdd orgup : listUpdate) {
                    //将机构下的子机构的停用状态也设置
                    Where where = Where.create();
                    where.ne(SysOrg::getUsed, 0);
                    where.like(SysOrg::getOrgpath, orgup.getOrgcode());
                    List<SysOrg> orgList = entityService.queryList(SysOrg.class, where);
                    if (StringUtils.isNotEmpty(orgList)) {
                        orgList = Optional.ofNullable(orgList).orElse(new ArrayList<>()).stream()
                                .filter(item -> !item.getId().equals(orgup.getOrgcode()))
                                .collect(Collectors.toList());
                        //有子节点
                        for (SysOrg sysOrg : orgList) {
                            //设置子节点的停用状态
                            sysOrg.setIsHidden(orgup.getIsHidden());
                            //设置停用
                            if (sysOrg.getUsed() != 0) {
                                //不是已删除的
                                if (sysOrg.getIsHidden() != null && sysOrg.getIsHidden() == 1) {
                                    //停用  1
                                    sysOrg.setUsed(-1);
                                } else {
                                    sysOrg.setUsed(1);
                                }
                                updateList.add(sysOrg);
                            }
                        }
                    }
                    redis.delete(getRedKey());
                    SysOrg e = this.findOrgByIdAll(orgup.getOrgcode());
                    e.setIsHidden(orgup.getIsHidden());
                    if (e != null) {
                        // 原
                        String orgcode1 = e.getOrgcode();
                        String orgname1 = e.getOrgname();
                        String porgcode1 = orgup.getOrgcode();
                        String orgHead1 = e.getOrgHead();
                        String orgNumber1 = e.getOrgNumber();
                        String orgType1 = e.getOrgType() == null ? "" : e.getOrgType();
                        String groupname1 = e.getGroupname() == null ? "" : e.getGroupname().trim();
                        // 目标
                        String orgcode2 = orgup.getOrgcode();
                        String orgname2 = orgup.getOrgname();
                        String porgcode2 = orgup.getOrgcode();
                        String orgHead2 = orgup.getOrgHead();
                        String orgNumber2 = orgup.getOrgNumber();
                        String orgType2 = orgup.getOrgType();
                        String groupname2 = orgup.getGroupname() == null ? "" : orgup.getGroupname().trim();
                        SysOrgLog slog = getSysOrgLog(dayNow, orgcode1, orgname1, porgcode1, orgcode2, orgname2,
                                porgcode2, 2);
                        e.setOrgname(orgname2);
                        e.setOrgHead(orgHead2);
                        e.setOrgNumber(orgNumber2);
                        e.setOrgType(orgup.getOrgType());
                        e.setOrgClassify(orgup.getOrgClassify());
                        e.setPhone(orgup.getPhone());
                        e.setGroupname(groupname2);
                        des = "";
                        if (orgname1 != null && !orgname1.equals(orgname2)) {
                            des = des + "【" + orgname1 + "】修改为【" + orgname2 + "】;";
                        }
                        if (orgHead1 != null && !orgHead1.equals(orgHead2)) {
                            des = des + "部门负责人【" + orgHead1 + "】修改为【" + orgHead2 + "】;";
                        }
                        if (orgNumber1 != null && !orgNumber1.equals(orgNumber2)) {
                            des = des + "部门编号【" + orgNumber1 + "】修改为【" + orgNumber2 + "】;";
                        }
                        if (orgType1 != null && !orgType1.equals(orgType2)) {
                            des = des + "部门类型【" + orgType1 + "】修改为【" + orgType2 + "】;";
                        }
                        if (groupname1 != null && !groupname1.equals(groupname2)) {
                            des = des + "分组名称【" + groupname1 + "】修改为【" + groupname2 + "】;";
                        }
                        slog.setDescription(des);
                        listlog.add(slog);
                        if (orgup.isUpdateSort() && orgup.getTmSort() != null) {// 需要更新排序
                            e.setTmSort(orgup.getTmSort());
                        }
                        // 重新计算全路径（跨层级节点移动）
                        if (orgup.getCalfullpath() != null && orgup.getCalfullpath() == 1) {
                            SysOrgMove orgMove = new SysOrgMove();
                            orgMove.setOrgcode(e.getOrgcode());
                            orgMove.setOrgname(e.getOrgname());
                            orgMove.setTmSort(e.getTmSort());
                            orgMove.setTorgcode(orgup.getPorgcode());
                            moveList.add(orgMove);
                            // this.moveData(orgMove);// 修改机构关系
                        } else {
                            if (StringUtils.isNotEmpty(orgup.getId())) {// 需判断是否修改层级关系(Excel导入的数据)
                                if (StringUtils.isNotEmpty(orgup.getOrgpath())) {
                                    e.setOrgpath(orgup.getOrgpath());
                                    e.setOrglevel(orgup.getOrglevel());
                                }
                                if (StringUtils.isNotEmpty(orgup.getPorgcode())) {
                                    // 修改机构关系
                                    SysOrgRelation relation = new SysOrgRelation();
                                    relation.setPorgcode(orgup.getPorgcode());
                                    relation.setOrgcode(orgup.getId());
                                    updateRelationList.add(relation);
                                }
                            }
                        }
                        //设置停用
                        if (e.getIsHidden() != null && e.getIsHidden() == 1) {
                            //停用  1
                            e.setUsed(-1);
                        } else {
                            e.setUsed(1);
                        }
                        updateList.add(e);
                    }
                }
                int row = 0;
                if (updateList.size() > 0) {
                    row = this.updateOrg(updateList);// entityService.updateByIdBatch(updateList);
                    if (row > 0) {
                        bool = true;
                        if (moveList.size() > 0) {// 改变父机构
                            for (SysOrgMove movedata : moveList) {
                                this.moveData(movedata);
                            }
                        }
                    }
                }
                if (updateRelationList.size() > 0) {
                    orgrelation.saveByOrgcode(updateRelationList);
                }
                if (row > 0 && listlog.size() > 0) {
                    bool = orglog.saveData(listlog);
                }
            }
        } catch (Exception ex) {
            log.error("", ex);
            bool = false;
        }
        return bool;
    }

    /**
     * 更新部门信息
     *
     * @param SysOrgAdd
     * @return bool
     */
    @Override
    @Transactional
    public boolean updateData(SysOrgAdd orgup) {
        boolean bln = false;
        if (orgup != null) {
            List<SysOrgAdd> list = new ArrayList<SysOrgAdd>();
            list.add(orgup);
            bln = this.updateData(list);
        }
        return bln;

    }


    //TODD:待优化=======================

    /**
     * 移动部门节点
     *
     * @param SysOrgMove
     * @return bool
     */
    @Override
    @Transactional
    public boolean moveData(SysOrgMove data) {
        boolean bool = false;
        try {
            // 查询父原父节点 的信息
            String pid1 = "";
            String pname1 = "";
            int level1 = 0;
            // 查询新父节点信息
            String pid2 = data.getTorgcode();
            int level2 = 0;
            String pname2 = "";
            String orgpath2 = "";
            // 当前机构节点
            SysOrg curOrg = null;
            // 查询当前父节点的id
            SysOrgRelation e = new SysOrgRelation();
            List<SysOrgRelation> listRelation = orgrelation.listData(data.getOrgcode());
            if (listRelation != null && listRelation.size() > 0) {
                e = listRelation.get(0);
                pid1 = e.getPorgcode();
                e.setPorgcode(pid2); // 更新为新父节点id
            }
            List<String> listid = new ArrayList<String>();
            listid.add(pid1);
            listid.add(pid2);

            Where where = Where.create();
            //where.eq(SysOrg::getUsed, 1);
            where.in(SysOrg::getOrgcode, listid.toArray());
            List<SysOrg> list = entityService.queryList(SysOrg.class, where);
            if (list == null) { // 未获取到指定父节点
                return false;
            } else if (list.size() == 1) { // 移动至相同父节点
                return true;
            } else if (list.size() > 1) {
                Map<String, SysOrg> orgMap = list.stream()
                        .collect(Collectors.toMap(SysOrg::getOrgcode, a -> a, (k1, k2) -> k1));
                SysOrg e1 = orgMap.get(pid1);
                SysOrg e2 = orgMap.get(pid2);
                pname1 = e1.getOrgname();
                level1 = e1.getOrglevel();
                pname2 = e2.getOrgname();
                level2 = e2.getOrglevel();
                orgpath2 = e2.getOrgpath();
            }
            // 查询目标节点下最大排序
            int levelDiff = level2 - level1; // 新旧父节点层级差值
            int sort = 1;
            List<SysOrg> listup = new ArrayList<SysOrg>();
            where = Where.create();
            Order order = Order.create();
            // where.eq(SysOrg::getUsed, 1);
            where.eq(SysOrg::getOrglevel, (level2 + 1));
//			where.like(SysOrg::getOrgpath, pid2);
            likeOrgPath(where, pid2);// 机构路径查找
            order.orderByAsc(SysOrg::getTmSort);
            List<SysOrg> listp = entityService.queryList(SysOrg.class, where, order);
            if (listp != null && listp.size() > 0) {
                // 修正此层节点的排序值
                int i = 0;
                for (; i < listp.size(); i++) {
                    SysOrg o = listp.get(i);
                    o.setTmSort(i + 1);
                    listup.add(o);
                }
                sort = i + 1;
            }
            // 把原节点底下的节点 都-1
            where = Where.create();
            //where.eq(SysOrg::getUsed, 1);
            where.eq(SysOrg::getOrglevel, (level1 + 1));
//			where.like(SysOrg::getOrgpath, pid1);
            likeOrgPath(where, pid1);// 机构路径查找
            order = Order.create();
            order.orderByAsc(SysOrg::getTmSort);
            List<SysOrg> listorg = entityService.queryList(SysOrg.class, where, order);
            if (listorg != null && listorg.size() > 0) {
                int n = listorg.size();
                int st = 1;
                for (int i = 0; i < n; i++) {
                    SysOrg bean = listorg.get(i);
                    String orgcode = bean.getOrgcode();
                    if (StringUtils.isNotEmpty(orgcode) && orgcode.equals(data.getOrgcode())) {
                        // 当前的节点要变动到目标父节点下最大排序
                        bean.setTmSort(sort);
                        // 当前的节点要变动目标父节点下新的全路径
                        if (!orgpath2.endsWith("/")) {
                            orgpath2 = orgpath2 + "/";
                        }
                        bean.setOrgpath(orgpath2 + orgcode);
                        bean.setOrglevel(bean.getOrglevel() + levelDiff);
                        curOrg = bean;
                    } else {
                        // 处理原节点下的其他节点都-1
                        // int m = bean.getTmSort();
                        // bean.setTmSort(m - 1);
                        // 修正此层节点的排序值
                        bean.setTmSort(st);
                        st++;
                    }
                    listup.add(bean);
                }
            }
            // 处理节点移动
            e.setPorgcode(pid2);
            // 处理当前节点移动到 新父节点后，全路径问题
            // 1查询节点主表
            where = Where.create();
            //where.eq(SysOrg::getUsed, 1);
//			where.like(SysOrg::getOrgpath, data.getOrgcode());
            likeOrgPath(where, data.getOrgcode());// 机构路径查找
            where.ne(SysOrg::getOrgcode, data.getOrgcode()); // 刨除当前节点，只获取子节点
            order = Order.create();
            order.orderByAsc(SysOrg::getTmSort);
            List<SysOrg> listo = entityService.queryList(SysOrg.class, where, order);
            // 处理当前节点下所有子节点的全路径(fullpath)、层级(level)
            for (int i = 0; i < listo.size(); i++) {
                SysOrg o = listo.get(i);
                String fp = o.getOrgpath();
                int idx = fp.indexOf("/" + data.getOrgcode()); // 被移动的根节点编码定位坐标
                if (idx >= 0) {
                    String s = fp.substring(idx + data.getOrgcode().length() + 1);
                    if (StringUtils.isNotEmpty(s)) {
                        if (!s.startsWith("/")) {
                            s = "/" + s;
                        }
                        o.setOrgpath(curOrg.getOrgpath() + s);
                    }
                }
                o.setOrglevel(o.getOrglevel() + levelDiff);
                listup.add(o);
            }
            bool = orgrelation.updateData(e);
            if (bool) {
                // 处理原节点下的其他节点都-1，当前的节点要变动到目标父节点下最大排序
                bool = true;
                int rs = this.updateOrg(listup);
                if (rs <= 0) {
                    bool = false;
                }
                if (bool) {
                    // 日志
                    Date d = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    String dayNow = sdf.format(d);
                    List<SysOrgLog> listlog = new ArrayList<SysOrgLog>();
                    SysOrgLog slog = getSysOrgLog(dayNow, data.getOrgcode(), data.getOrgname(), pid1, data.getOrgcode(),
                            data.getOrgname(), pid2, 4);
                    slog.setDescription("【" + data.getOrgname() + "】从【" + pname1 + "】移动到【" + pname2 + "】");
                    listlog.add(slog);
                    bool = orglog.saveData(listlog);
                }
            }
        } catch (Exception ex) {
            log.error("", ex);
        }
        return bool;
    }

    @Override
    @Transactional
    public boolean handleDropSysOrg(SysOrgHandleDrop sysOrgHandleDrop) {
        boolean bool = true;

        // 源组织父组织
        // LambdaQueryWrapper<SysOrg> sourcePorgQuery = new
        // LambdaQueryWrapper<SysOrg>();
        // sourcePorgQuery.eq(SysOrg::getOrgcode, sysOrgHandleDrop.getSourcePOrgCode());
        // SysOrg sourcePorg = this.getOne(sourcePorgQuery);
        Where where = Where.create();
        where.eq(SysOrg::getOrgcode, sysOrgHandleDrop.getSourcePOrgCode());
        SysOrg sourcePorg = entityService.queryObject(SysOrg.class, where);

        // 目标组织父组织
        // LambdaQueryWrapper<SysOrg> targetPorgQuery = new
        // LambdaQueryWrapper<SysOrg>();
        // targetPorgQuery.eq(SysOrg::getOrgcode, sysOrgHandleDrop.getTargetPOrgCode());
        // SysOrg targetPorg = this.getOne(targetPorgQuery);
        where = Where.create();
        where.eq(SysOrg::getOrgcode, sysOrgHandleDrop.getTargetPOrgCode());
        SysOrg targetPorg = entityService.queryObject(SysOrg.class, where);

        // 源组织
        // LambdaQueryWrapper<SysOrg> sourceOrgQuery = new LambdaQueryWrapper<SysOrg>();
        // sourceOrgQuery.eq(SysOrg::getOrgcode, sysOrgHandleDrop.getSourceOrgCode());
        // SysOrg sourceOrg = this.getOne(sourceOrgQuery);
        where = Where.create();
        where.eq(SysOrg::getOrgcode, sysOrgHandleDrop.getSourceOrgCode());
        SysOrg sourceOrg = entityService.queryObject(SysOrg.class, where);

        // 目标组织
        // LambdaQueryWrapper<SysOrg> targetOrgQuery = new LambdaQueryWrapper<SysOrg>();
        // targetOrgQuery.eq(SysOrg::getOrgcode, sysOrgHandleDrop.getTargetOrgCode());
        // SysOrg targetOrg = this.getOne(targetOrgQuery);
        where = Where.create();
        where.eq(SysOrg::getOrgcode, sysOrgHandleDrop.getTargetOrgCode());
        SysOrg targetOrg = entityService.queryObject(SysOrg.class, where);

        String logDescription = "";

        List<SysOrg> sameLevelOrgList = new ArrayList<>();
        // LambdaQueryWrapper<SysOrg> sameLevelOrgQuery = new
        // LambdaQueryWrapper<SysOrg>();
        where = Where.create();
        // 行内移动
        if (sysOrgHandleDrop.getMoveType() == 1) {
            // 源在下，目标在上
            if (sourceOrg.getTmSort() > targetOrg.getTmSort()) {
                // 放置在前
                if (sysOrgHandleDrop.getLocation() == 1) {
                    // sameLevelOrgQuery.like(SysOrg::getOrgpath,
                    // sourcePorg.getOrgcode()).eq(SysOrg::getUsed, 1)
                    // .eq(SysOrg::getOrglevel, targetOrg.getOrglevel())
                    // .ge(SysOrg::getTmSort, targetOrg.getTmSort()).lt(SysOrg::getTmSort,
                    // sourceOrg.getTmSort());
                    // sameLevelOrgList = this.list(sameLevelOrgQuery);
//					where.like(SysOrg::getOrgpath, sourcePorg.getOrgcode());
                    likeOrgPath(where, sourcePorg.getOrgcode());// 机构路径查找
                    where.eq(SysOrg::getUsed, 1);
                    where.eq(SysOrg::getOrglevel, targetOrg.getOrglevel());
                    where.ge(SysOrg::getTmSort, targetOrg.getTmSort());
                    where.lt(SysOrg::getTmSort, sourceOrg.getTmSort());
                    sameLevelOrgList = entityService.queryList(SysOrg.class, where);

                    for (SysOrg so : sameLevelOrgList) {
                        so.setTmSort(so.getTmSort() + 1);
                    }
                    sourceOrg.setTmSort(targetOrg.getTmSort());
                    logDescription = "【" + sourceOrg.getOrgname() + "】移动到了【" + targetOrg.getOrgname() + "】的前面";
                    // 放置在后
                } else {
                    // sameLevelOrgQuery.like(SysOrg::getOrgpath,
                    // sourcePorg.getOrgcode()).eq(SysOrg::getUsed, 1)
                    // .eq(SysOrg::getOrglevel, targetOrg.getOrglevel())
                    // .gt(SysOrg::getTmSort, targetOrg.getTmSort()).lt(SysOrg::getTmSort,
                    // sourceOrg.getTmSort());
                    // sameLevelOrgList = this.list(sameLevelOrgQuery);
//					where.like(SysOrg::getOrgpath, sourcePorg.getOrgcode());
                    likeOrgPath(where, sourcePorg.getOrgcode());// 机构路径查找
                    where.eq(SysOrg::getUsed, 1);
                    where.eq(SysOrg::getOrglevel, targetOrg.getOrglevel());
                    where.gt(SysOrg::getTmSort, targetOrg.getTmSort());
                    where.lt(SysOrg::getTmSort, sourceOrg.getTmSort());
                    sameLevelOrgList = entityService.queryList(SysOrg.class, where);

                    for (SysOrg so : sameLevelOrgList) {
                        so.setTmSort(so.getTmSort() + 1);
                    }
                    sourceOrg.setTmSort(targetOrg.getTmSort() + 1);
                    logDescription = "【" + sourceOrg.getOrgname() + "】移动到了【" + targetOrg.getOrgname() + "】的后面";
                }
            } else {
                if (sysOrgHandleDrop.getLocation() == 1) {
                    // sameLevelOrgQuery.like(SysOrg::getOrgpath,
                    // sourcePorg.getOrgcode()).eq(SysOrg::getUsed, 1)
                    // .eq(SysOrg::getOrglevel, targetOrg.getOrglevel())
                    // .gt(SysOrg::getTmSort, sourceOrg.getTmSort()).lt(SysOrg::getTmSort,
                    // targetOrg.getTmSort());
                    // sameLevelOrgList = this.list(sameLevelOrgQuery);
//					where.like(SysOrg::getOrgpath, sourcePorg.getOrgcode());
                    likeOrgPath(where, sourcePorg.getOrgcode());// 机构路径查找
                    where.eq(SysOrg::getUsed, 1);
                    where.eq(SysOrg::getOrglevel, targetOrg.getOrglevel());
                    where.gt(SysOrg::getTmSort, sourceOrg.getTmSort());
                    where.lt(SysOrg::getTmSort, targetOrg.getTmSort());
                    sameLevelOrgList = entityService.queryList(SysOrg.class, where);

                    for (SysOrg so : sameLevelOrgList) {
                        so.setTmSort(so.getTmSort() - 1);
                    }
                    sourceOrg.setTmSort(targetOrg.getTmSort() - 1);
                    logDescription = "【" + sourceOrg.getOrgname() + "】移动到了【" + targetOrg.getOrgname() + "】的前面";
                } else {
                    // sameLevelOrgQuery.like(SysOrg::getOrgpath,
                    // sourcePorg.getOrgcode()).eq(SysOrg::getUsed, 1)
                    // .eq(SysOrg::getOrglevel, targetOrg.getOrglevel())
                    // .gt(SysOrg::getTmSort, sourceOrg.getTmSort()).le(SysOrg::getTmSort,
                    // targetOrg.getTmSort());
                    // sameLevelOrgList = this.list(sameLevelOrgQuery);
//					where.like(SysOrg::getOrgpath, sourcePorg.getOrgcode());
                    likeOrgPath(where, sourcePorg.getOrgcode());// 机构路径查找
                    where.eq(SysOrg::getUsed, 1);
                    where.eq(SysOrg::getOrglevel, targetOrg.getOrglevel());
                    where.gt(SysOrg::getTmSort, sourceOrg.getTmSort());
                    where.le(SysOrg::getTmSort, targetOrg.getTmSort());

                    sameLevelOrgList = entityService.queryList(SysOrg.class, where);

                    for (SysOrg so : sameLevelOrgList) {
                        so.setTmSort(so.getTmSort() - 1);
                    }
                    sourceOrg.setTmSort(targetOrg.getTmSort());
                    logDescription = "【" + sourceOrg.getOrgname() + "】移动到了【" + targetOrg.getOrgname() + "】的后面";
                }
            }
            sameLevelOrgList.add(sourceOrg);

            bool = true;
            int rs = this.updateOrg(sameLevelOrgList);// entityService.updateByIdBatch(sameLevelOrgList);
            if (rs <= 0) {
                bool = false;
            }
            if (bool) {
                Date d = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String dayNow = sdf.format(d);
                List<SysOrgLog> listlog = new ArrayList<SysOrgLog>();
                SysOrgLog slog = getSysOrgLog(dayNow, sourceOrg.getOrgcode(), sourceOrg.getOrgname(),
                        sysOrgHandleDrop.getSourcePOrgCode(), targetOrg.getOrgcode(), targetOrg.getOrgname(),
                        sysOrgHandleDrop.getTargetPOrgCode(), 5);
                slog.setDescription(logDescription);
                listlog.add(slog);
                bool = orglog.saveData(listlog);
            }

        } else if (sysOrgHandleDrop.getMoveType() == 2) {
            // 先挪过去再改排序
            SysOrgMove orgadd = new SysOrgMove();
            orgadd.setOrgcode(sourceOrg.getOrgcode());
            orgadd.setOrgname(sourceOrg.getOrgname());
            orgadd.setTorgcode(targetPorg.getOrgcode());
            orgadd.setTmSort(sourceOrg.getTmSort());
            bool = this.moveData(orgadd);
            if (bool) {
                // 放置在前
                if (sysOrgHandleDrop.getLocation() == 1) {
                    // sameLevelOrgQuery.like(SysOrg::getOrgpath, targetPorg.getOrgcode())
                    // .ne(SysOrg::getOrgcode, sourceOrg.getOrgcode()).eq(SysOrg::getUsed, 1)
                    // .eq(SysOrg::getOrglevel, targetOrg.getOrglevel())
                    // .ge(SysOrg::getTmSort, targetOrg.getTmSort());
                    // sameLevelOrgList = this.list(sameLevelOrgQuery);
//					where.like(SysOrg::getOrgpath, targetPorg.getOrgcode());
                    likeOrgPath(where, targetPorg.getOrgcode());// 机构路径查找
                    where.ne(SysOrg::getOrgcode, sourceOrg.getOrgcode());
                    where.eq(SysOrg::getUsed, 1);
                    where.eq(SysOrg::getOrglevel, targetOrg.getOrglevel());
                    where.ge(SysOrg::getTmSort, targetOrg.getTmSort());
                    sameLevelOrgList = entityService.queryList(SysOrg.class, where);

                    for (SysOrg so : sameLevelOrgList) {
                        so.setTmSort(so.getTmSort() + 1);
                    }
                    sourceOrg.setTmSort(targetOrg.getTmSort());
                } else {
                    // 放置在后
                    // sameLevelOrgQuery.like(SysOrg::getOrgpath, targetPorg.getOrgcode())
                    // .ne(SysOrg::getOrgcode, sourceOrg.getOrgcode()).eq(SysOrg::getUsed, 1)
                    // .eq(SysOrg::getOrglevel, targetOrg.getOrglevel())
                    // .gt(SysOrg::getTmSort, targetOrg.getTmSort());
                    // sameLevelOrgList = this.list(sameLevelOrgQuery);
//					where.like(SysOrg::getOrgpath, targetPorg.getOrgcode());
                    likeOrgPath(where, targetPorg.getOrgcode());// 机构路径查找
                    where.ne(SysOrg::getOrgcode, sourceOrg.getOrgcode());
                    where.eq(SysOrg::getUsed, 1);
                    where.eq(SysOrg::getOrglevel, targetOrg.getOrglevel());
                    where.gt(SysOrg::getTmSort, targetOrg.getTmSort());
                    sameLevelOrgList = entityService.queryList(SysOrg.class, where);

                    for (SysOrg so : sameLevelOrgList) {
                        so.setTmSort(so.getTmSort() + 1);
                    }
                    sourceOrg.setTmSort(targetOrg.getTmSort() + 1);
                }
                // 此处为防止全路径被更新回去
                SysOrg updateOrg = new SysOrg();
                updateOrg.setId(sourceOrg.getId());
                updateOrg.setTmSort(sourceOrg.getTmSort());
                sameLevelOrgList.add(updateOrg);

                bool = true;
                int rs = this.updateOrg(sameLevelOrgList);// entityService.updateByIdBatch(sameLevelOrgList);
                if (rs <= 0) {
                    bool = false;
                }
                if (bool) {
                    Date d = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    String dayNow = sdf.format(d);
                    List<SysOrgLog> listlog = new ArrayList<SysOrgLog>();
                    SysOrgLog slog = getSysOrgLog(dayNow, sourceOrg.getOrgcode(), sourceOrg.getOrgname(),
                            sysOrgHandleDrop.getSourcePOrgCode(), targetOrg.getOrgcode(), targetOrg.getOrgname(),
                            sysOrgHandleDrop.getTargetPOrgCode(), 5);
                    slog.setDescription(logDescription);
                    listlog.add(slog);
                    bool = orglog.saveData(listlog);
                }
            }

        } else if (sysOrgHandleDrop.getMoveType() == 3) {
            SysOrgMove orgadd = new SysOrgMove();
            orgadd.setOrgcode(sourceOrg.getOrgcode());
            orgadd.setOrgname(sourceOrg.getOrgname());
            orgadd.setTorgcode(targetOrg.getOrgcode());
            orgadd.setTmSort(sourceOrg.getTmSort());
            bool = this.moveData(orgadd);
        }
        return bool;
    }

    /**
     * 递归处理下级全路径
     *
     * @param pid
     * @param path
     * @param mapinfo
     * @param pmap
     * @param listup
     */
    private void getTrees(String pid, String path, int level, Map<String, SysOrg> mapinfo,
                          Map<String, List<String>> pmap, List<SysOrg> listup) {
        List<String> listc = pmap.get(pid);
        if (listc != null && listc.size() > 0) {
            int n = listc.size();
            for (int i = 0; i < n; i++) {
                String orgcode = listc.get(i);
                SysOrg e2 = mapinfo.get(orgcode);
                path = path + "/" + orgcode;
                e2.setOrgpath(path);
                e2.setOrglevel(level + 1);
                listup.add(e2);
                getTrees(orgcode, path, level + 1, mapinfo, pmap, listup);
            }
        }
    }

    /**
     * 根据父id获取下级节点list
     *
     * @param pid
     * @return
     */
    private List<SysOrg> getOrgListByPid(String pid, boolean hasUsed) {
        List<SysOrg> list = new ArrayList<SysOrg>();
        List<SysOrgRelation> relList = orgrelation.listDataPids(pid);
        List<String> idList = new ArrayList<String>();
        if (StringUtils.isNotEmpty(relList)) {
            for (SysOrgRelation e : relList) {
                idList.add(e.getOrgcode());
            }
            list = this.listDatas(idList, hasUsed);
            if (StringUtils.isNotEmpty(list)) {// 重新排序
                return list.stream().sorted(Comparator.comparing(SysOrg::getTmSort)).collect(Collectors.toList());
            }
        }
        return list;
    }

    /**
     * 更新父id
     *
     * @param org
     * @param pid
     */
    private SysOrg updateParentId(SysOrg org, String pid) {
        String path = "/" + pid + "/" + org.getId();
        if (!org.getOrgpath().endsWith(path)) {// 改变父节点
            SysOrgMove data = new SysOrgMove();
            data.setOrgcode(org.getOrgcode());
            data.setOrgname(org.getOrgname());
            data.setTmSort(org.getTmSort());
            data.setTorgcode(pid);
            if (this.moveData(data)) {// 跨节点移动
                return this.findOrgById(org.getOrgcode(), true);
            }
        }
        return org;
    }

    /**
     * 根据路径获取父id
     *
     * @param org
     * @return
     */
    private String getParentCodeByPath(SysOrg org) {
        String path = "";
        if (org != null && org.getOrgpath() != null) {
            String ary[] = org.getOrgpath().split("/");
            if (ary.length > 1) {
                return ary[ary.length - 2];
            }
        }
        return path;
    }

    /**
     * 排序部门节点
     *
     * @param SysOrgMove
     * @return bool
     */
    @Override
    public boolean sortData(SysOrgSort orgsort) {
        boolean bool = false;
        try {
            String dropType = orgsort.getDropType(); // before、after、inner
            String id = orgsort.getSorgcode(); // 源节点id
            String dropId = orgsort.getTorgcode();// 目标节点id
//			System.out.println("dropType:" + dropType + ";id:" + id + ",pid:" + pid + ",dropId:" + dropId);
            SysOrg node = this.findOrgById(id, true);
            log.info(node.getOrgname());
            if (node != null) {
                List<SysOrg> updateList = new ArrayList<SysOrg>();
                if ("inner".equals(dropType)) {
                    updateParentId(node, dropId);// 更新父节点
                    return true;
                } else {
                    SysOrg dorpNode = this.findOrgById(dropId, true);
                    if (dorpNode != null) {
                        String parentCode = getParentCodeByPath(dorpNode);// 根据路径获取父节点ID
                        node = updateParentId(node, parentCode);// 更新全路径
                        List<SysOrg> pList = this.getOrgListByPid(parentCode, true);// 获取父节点下的所有子节点
                        List<SysOrg> newList = new ArrayList<SysOrg>();
                        if (StringUtils.isNotEmpty(pList)) {
                            // 开始排序操作
                            int sort = 0;
                            for (int n = 0; n < pList.size(); n++) {
                                SysOrg e = pList.get(n);
                                if (e.getOrgcode().equals(id)) {
                                    continue;
                                }
                                if (e.getOrgcode().equals(dropId)) {
                                    if ("before".equals(dropType)) {// 放在节点之前
                                        sort++;
                                        node.setTmSort(sort);
                                        newList.add(node);
                                        sort++;
                                        e.setTmSort(sort);
                                        newList.add(e);
                                    } else if ("after".equals(dropType)) {// 放在节点之后
                                        sort++;
                                        e.setTmSort(sort);
                                        newList.add(e);
                                        sort++;
                                        node.setTmSort(sort);
                                        newList.add(node);
                                    }
                                    continue;
                                }
                                sort++;
                                e.setTmSort(sort);
                                newList.add(e);
                            }
                            updateList.addAll(newList);
                        }
                    }
                }
                if (updateList.size() > 0) {
                    int i = this.updateOrg(updateList);
                    bool = i > 0 ? true : false;
                }
            }
        } catch (Exception ex) {
            log.error("", ex);
        }
        return bool;
    }

    /**
     * 获取机构类型下拉框数据
     *
     * @return
     */
    @Override
    public List<SysOrgType> getOrgType() {
        List<SysDictData> list = getOrgTypeFromDict();
        return this.convertOrgTypeList(list);
    }

    /**
     * 获取机构类型下拉框数据
     *
     * @return
     */
    @Override
    public List<SysOrgType> getOrgClassify() {
        List<SysDictData> list = sysDictTypeServ.selectDictDataByType("sys_org_classify");
        return this.convertOrgTypeList(list);
    }


    private List<SysOrgType> convertOrgTypeList(List<SysDictData> list) {
        List<SysOrgType> rsList = new ArrayList<SysOrgType>();
        if (list != null && list.size() > 0) {
            for (SysDictData dt : list) {
                SysOrgType tp = new SysOrgType();
                tp.setLabel(dt.getDictLabel());
                tp.setValue(dt.getDictValue());
                rsList.add(tp);
            }
        }
        return rsList;
    }


    /**
     * 获得机构类型map
     *
     * @return
     */
    @Override
    public Map<String, String> getOrgTypeMap() {
        Map<String, String> rsMap = new HashMap<String, String>();
        List<SysDictData> list = getOrgTypeFromDict();
        if (list != null && list.size() > 0) {
            for (SysDictData dt : list) {
                rsMap.put(dt.getDictValue(), dt.getDictLabel());
            }
        }
        return rsMap;
    }

    /**
     * 从数据字典中获取机构类型
     *
     * @return
     */
    private List<SysDictData> getOrgTypeFromDict() {
        List<SysDictData> list = sysDictTypeServ.selectDictDataByType(ORG_TYPE);
        return list;
    }

    /**
     * 获得机构类型list
     *
     * @return list<类型名称>
     */
    @Override
    public List<String> getOrgTypeList() {
        List<String> rtnList = new ArrayList<String>();
        List<SysDictData> list = getOrgTypeFromDict();
        if (list != null && list.size() > 0) {
            for (SysDictData dt : list) {
                rtnList.add(dt.getDictLabel());
            }
        }
        return rtnList;
    }

    @Override
    public List<String> getOrgClassfList() {
        List<String> rtnList = new ArrayList<String>();
        List<SysOrgType> list = this.getOrgClassify();
        if (list != null && list.size() > 0) {
            for (SysOrgType dt : list) {
                rtnList.add(dt.getLabel());
            }
        }
        return rtnList;
    }


    /**
     * 生成中文机构全路径
     *
     * @param orgList
     * @param orgCodeMap
     */
    private void genOrgNamePath(List<SysOrg> orgList, Map<String, SysOrg> orgCodeMap) {
        if (orgList == null || orgList.size() <= 0 || orgCodeMap == null || orgCodeMap.size() <= 0) {
            return;
        }
        mainFor:
        for (SysOrg org : orgList) {
            String orgpath = org.getOrgpath();
            if (orgpath.startsWith("/")) {
                orgpath = orgpath.substring(1);
            }
            String[] pArr = orgpath.split("/");
            if (pArr != null && pArr.length > 0) {
                List<String> orgnameList = new ArrayList<String>();
                for (int i = 0; i < pArr.length; i++) {
                    String orgcode = pArr[i];
                    SysOrg fOrg = orgCodeMap.get(orgcode);
                    if (fOrg == null) {
                        continue mainFor;
                    }
                    orgnameList.add(fOrg.getOrgname());
                }
                org.setOrgNamePath("/" + String.join("/", orgnameList));
            }
        }
    }

    /**
     * 中文机构路径模糊匹配规则
     *
     * @param orgNamePath
     * @param filters
     * @return
     */
    private boolean checkOrgNamePath(SysOrg org, String filters) {
        if (filters != null && org.getOrgNamePath().endsWith(filters) && org.getTmp() != 1) { // 只过滤数据库节点
            return true;
        } else {
            return false;
        }
    }

    /**
     * 子节点过滤规则
     *
     * @param filterOrg 待过滤节点
     * @param porg      父节点
     * @return
     */
    private boolean checkChildOrg(SysOrg filterOrg, SysOrg porg) {
        if (filterOrg.getOrglevel() == (porg.getOrglevel() + 1)) { // 父节点下一层子节点
            if (filterOrg.getOrgpath().contains(porg.getOrgcode())) { // 全路径包含父节点
                return true;
            }
        }
        return false;
    }

    /**
     * 根据机构类型获取机构列表
     *
     * @param orgType
     * @return
     */
    @Override
    public List<SysOrg> getOrgByType(String orgType) {
        if (StringUtils.isNotEmpty(orgType)) {
            Where where = Where.create();
            where.eq(SysOrg::getUsed, 1);
            where.lb();
            where.eq(SysOrg::getOrgType, orgType);
            where.or();
            where.like(SysOrg::getOrgType, "\"" + orgType + "\"");
            where.rb();
            Order order = Order.create();
            order.orderByAsc(SysOrg::getTmSort);
            return entityService.rawQueryListByWhere(SysOrg.class, where, order);
        } else {
            return null;
        }
    }

    /**
     * 根据机构编码获取机构列表
     *
     * @param orgNumber
     * @return
     */
    @Override
    public List<SysOrg> getOrgByOrgNumber(String orgNumber) {
        if (StringUtils.isNotEmpty(orgNumber)) {
            Where where = Where.create();
            where.eq(SysOrg::getUsed, 1);
            where.eq(SysOrg::getOrgNumber, orgNumber);
            Order order = Order.create();
            order.orderByAsc(SysOrg::getTmSort);
            return entityService.rawQueryListByWhere(SysOrg.class, where, order);
        } else {
            return null;
        }
    }

    /**
     * 根据机构代码获取包括自己在内的全部子机构
     *
     * @param orgcode
     * @return
     */
    @Override
    public List<SysOrg> getOrgList(String orgcode) {
        if (StringUtils.isNotEmpty(orgcode)) {
            if ("root".equals(orgcode)) {
                orgcode = "";// 直接查询从根节点起的数据
            }
            Where where = Where.create();
            where.eq(SysOrg::getUsed, 1);
            likeOrgPath(where, orgcode);// 机构路径查找
            Order order = Order.create();
            order.orderByAsc(SysOrg::getOrglevel);
            order.orderByAsc(SysOrg::getTmSort);
            return entityService.rawQueryListByWhere(SysOrg.class, where, order);
        } else {
            return null;
        }
    }

    /**
     * 获得导出装置机构树形数据
     *
     * @param param
     * @return
     */
    @Override
    public List<SysOrgTree> getExportData(SysOrgSelect param) {
        List<SysOrgTree> listData = null;
        if ("1".equals(param.getExportType())) {// 导出空模板
            listData = new ArrayList<SysOrgTree>();
            SysOrgTree bean1 = new SysOrgTree();
            bean1.setOrgname("集团公司");
            listData.add(bean1);
            SysOrgTree bean2 = new SysOrgTree();
            bean2.setOrgname(IMPORT_LEVEL_CHAR + "辽宁分公司");
            listData.add(bean2);
            SysOrgTree bean3 = new SysOrgTree();
            bean3.setOrgname(StringUtils.leftPadStr("人事部", 2, IMPORT_LEVEL_CHAR));
            listData.add(bean3);
            SysOrgTree bean4 = new SysOrgTree();
            bean4.setOrgname(StringUtils.leftPadStr("办公室", 2, IMPORT_LEVEL_CHAR));
            listData.add(bean4);
            SysOrgTree bean5 = new SysOrgTree();
            bean5.setOrgname(IMPORT_LEVEL_CHAR + "北京分公司");
            listData.add(bean5);
            SysOrgTree bean6 = new SysOrgTree();
            bean6.setOrgname(StringUtils.leftPadStr("销售部", 2, IMPORT_LEVEL_CHAR));
            listData.add(bean6);
            for (SysOrgTree sysOrgTree : listData) {
                sysOrgTree.setOrgTypeLabel("");
                sysOrgTree.setOrgClassifyLabel("");
            }
        } else {// 导出数据模板
            List<SysOrgTreeData> tempList = this.listDatas(param);
            if (StringUtils.isNotEmpty(tempList)) {
                List<SysOrgTree> rtnList = treeServ.getTreeGridList(SysOrgTree.class, tempList, SysOrgTreeData.class,
                        "porgcode", "orgname", IMPORT_LEVEL_CHAR);
                if (StringUtils.isNotEmpty(rtnList)) {
                    rtnList.remove(0); // 删除根节点
                }
                return rtnList;
            }
        }
        return listData == null ? new ArrayList<SysOrgTree>() : listData;
    }

    /**
     * 数据导入
     *
     * @param dataList
     * @param params
     * @return
     */
    @Override
    public String importOrgData(List<SysOrgTree> dataList, String params) throws Exception {
        String info = "";
        int maxlevel = 0;
        if (StringUtils.isNotEmpty(dataList)) {
            SysOrg root = getRootNode();
            Map<Integer, List<SysOrgAdd>> mapOrgLevel = new HashMap<Integer, List<SysOrgAdd>>();
            for (SysOrgTree sysOrgTree : dataList) {
                String name = sysOrgTree.getOrgname();
                int level = getLevel(name);
                if (level > maxlevel) {
                    maxlevel = level;
                }
                SysOrgAdd sysOrgAdd = ObjUtils.copyTo(sysOrgTree, SysOrgAdd.class);
                if (StringUtils.isEmpty(sysOrgTree.getId())) {// 添加
                    sysOrgAdd.setId(TMUID.getUID());
                    sysOrgAdd.setRowFlag(0);
                } else {// 修改
                    sysOrgAdd.setRowFlag(1);
                }
                sysOrgAdd.setOrgcode(sysOrgAdd.getId());
                // 获取父节点
                if (level == 0) {
                    sysOrgAdd.setPorgcode(root.getOrgcode());
                    sysOrgAdd.setPorgpath(root.getOrgpath());
                    int sort = getOrgSort(root.getOrgcode(), root.getOrglevel(), "");
                    sysOrgAdd.setTmSort(sort);
                } else {
                    if (mapOrgLevel.size() == 0) {
                        throw new Exception("excle中未识别到根节点");
                    }
                    List<SysOrgAdd> listPNode = mapOrgLevel.get(level - 1);
                    if (StringUtils.isNotEmpty(listPNode)) {
                        SysOrgAdd pnode = listPNode.get(listPNode.size() - 1);
                        sysOrgAdd.setPorgcode(pnode.getOrgcode());
                        sysOrgAdd.setPorgpath(pnode.getOrgpath());
                    }
                    sysOrgAdd.setOrgname(name.substring(level));
                    sysOrgAdd.setTmSort(null);
                }
                sysOrgAdd.setOrgpath(sysOrgAdd.getPorgpath() + "/" + sysOrgAdd.getOrgcode());
                sysOrgAdd.setOrglevel(level + 1);
                if (mapOrgLevel.get(level) == null) {
                    mapOrgLevel.put(level, new ArrayList<SysOrgAdd>());
                }
                List<SysOrgAdd> tempList = mapOrgLevel.get(level);
                if (sysOrgAdd.getTmSort() == null) {
                    sysOrgAdd.setTmSort(tempList.size() + 1);
                }
                tempList.add(sysOrgAdd);
            }
            for (int i = 0; i <= maxlevel; i++) {
                importOrg(mapOrgLevel.get(i));// 按照层级依次导入
            }
        }
        return info;

    }

    /**
     * 添加或者修改导入数据
     *
     * @param list
     */
    private void importOrg(List<SysOrgAdd> list) {
        if (StringUtils.isNotEmpty(list)) {
            List<SysOrgAdd> insertList = new ArrayList<SysOrgAdd>();
            List<SysOrgAdd> updateList = new ArrayList<SysOrgAdd>();
            for (SysOrgAdd bean : list) {
                if (bean.getRowFlag() == 0) {// 添加
                    insertList.add(bean);
                } else {// 修改
                    updateList.add(bean);
                }
            }
            if (insertList.size() > 0) {
                insertData(insertList);
            }
            if (updateList.size() > 0) {
                updateData(updateList);
            }
            String msg = "组织机构Excel导入：新增（" + insertList.size() + "）条，修改（" + updateList.size() + "）条";
            writeLog(msg);
        }
    }

    /**
     * 根据符号获取级别
     *
     * @param name ###开发部
     * @return
     */
    private int getLevel(String name) {
        int level = 0;
        if (name != null && name.startsWith(IMPORT_LEVEL_CHAR)) {
            for (int n = 0; n < name.length(); n++) {
                String s = name.substring(n, n + 1);
                if (!s.equals(IMPORT_LEVEL_CHAR)) {
                    break;
                }
                level++;
            }
        }
        return level;
    }

    /**
     * 导入时判断层级的字符
     *
     * @return
     */
    @Override
    public String getImportLevelChar() {
        return IMPORT_LEVEL_CHAR;
    }

    /**
     * 获取机构节点
     *
     * @param orgid 机构id
     * @return
     * @category 根据主键查找机构信息
     */
    @Override
    public SysOrg findOrgById(String orgid) {
        return findOrgById(orgid, false);
    }

    /**
     * 获取机构节点
     *
     * @param orgid
     * @param hasUsed 是否包含 used=0 记录 true:包含 false:不包含
     * @return
     */
    private SysOrg findOrgById(String orgid, boolean hasUsed) {
        if (StringUtils.isEmpty(orgid)) {
            return null;
        }
        SysOrg bean = this.getOrgFromRedis(orgid);
        if (bean == null) {
            Where where = Where.create().eq(SysOrg::getId, orgid);
            if (!hasUsed) {
                where.eq(SysOrg::getUsed, 1);
            }
            bean = entityService.queryObject(SysOrg.class, where);
            this.setOrg2Reids(bean);
        }
        return bean;
    }

//	@Override
//	public SysOrg findOrgByIdMap(String orgid,Map<String,SysOrg> map) {
//		// TODO Auto-generated method stub
//		SysOrg result = null;
//        if (StringUtils.isNotEmpty(orgid)) {
//        	if(map!=null) { 
//				if(map.size()==0) {
//					Map<String, LinkedHashMap<String,Object>> map1 = redis.getMap(RED_KEY);
//					for(Entry<String, LinkedHashMap<String,Object>> temp:map1.entrySet()) {
//						map.put(temp.getKey(), ObjUtils.convertToObject(SysOrg.class, temp.getValue()));
//					}
//				}
//				result = map.get(orgid);
//		        if (result == null) {
//		            Where where = Where.create().eq(SysOrg::getId, orgid);

    /// /		            where.eq(SysOrg::getUsed, 1);
//		            result = entityService.queryObject(SysOrg.class, where);
//		            if(result!=null) {
//		            	this.setOrg2Reids(result); 
//		            	map.put(result.getId(), result);
//		            }
//		        }
//        	}else {
//        		result = findOrgById(orgid, false);
//        	}
//        }
//        return result;
//	}
    @Override
    public SysOrg findOrgByIdAll(String orgid) {
        if (StringUtils.isEmpty(orgid)) {
            return null;
        }
        SysOrg bean = this.getOrgFromRedis(orgid);
        if (bean == null) {
            bean = entityService.queryObject(SysOrg.class, Where.create().ne(SysOrg::getUsed, 0).eq(SysOrg::getId, orgid));
            this.setOrg2Reids(bean);
        }
        return bean;
    }

    /**
     * 获取机构信息列表
     *
     * @param hasRoot 是否包含根节点
     * @return
     */
    private List<SysOrg> getOrgList(boolean hasRoot) {
        Order order = Order.create();
        order.orderByAsc(SysOrg::getOrglevel);
        order.orderByAsc(SysOrg::getTmSort);
        return entityService.queryList(SysOrg.class, this.getOrgWhere(hasRoot), order);
    }

    private Where getOrgWhere(boolean hasRoot) {
        Where where = Where.create();
        Order order = Order.create();
        if (!hasRoot) {
            where.gt(SysOrg::getOrglevel, 0);
        }
        where.eq(SysOrg::getUsed, 1);
        return where;
    }

    private Long getOrgCount(boolean hasRoot) {
        return entityService.queryCount(SysOrg.class, getOrgWhere(hasRoot));
    }


    /**
     * 初始化机构信息到redis
     */
    @Override
    public List<SysOrg> initRedis() {
        log.info("*****正在初始化机构信息到redis。。。");
        clearRedis();
        List<SysOrg> list = this.getOrgList(true);
        if (StringUtils.isNotEmpty(list)) {
            setListToRedis(list);
        }
        log.info("*****机构信息初始化完毕！***********");
        orgrelation.initOrgRelationToReids();//初始化机构关系
        return list;
    }

    /**
     * 重新整理序号
     *
     * @param map
     * @param maxLevel
     * @param org
     */
//    private void rebuildSort(Map<String, SysOrg> map, int maxLevel, SysOrg org) {
//        String allpath = org.getOrgpath();
//        if (StringUtils.isNotEmpty(allpath)) {
//            String[] path = allpath.split("/");
//            List<Integer> list = new ArrayList<Integer>();
//            for (String s : path) {
//                if (map.get(s) != null) {
//                    SysOrg temporg = map.get(s);
//                    if (temporg == null) {
//                        list.add(0);
//                    } else {
//                        list.add(temporg.getTmSort() == null ? 0 : temporg.getTmSort());
//                    }
//                }
//            }
//            if (list.size() > 0) {
//                Double sort = 0d;
//                for (int n = 0; n <= maxLevel; n++) {
//                    int n1 = maxLevel - n + 1;
//                    if (list.size() > n) {
//                        sort += list.get(n) * Math.pow(1000, n1);
//                    }
//                }
//                sort = sort / 1000;
//                org.setTmp(sort.longValue());
//            } else {
//                org.setTmp(0L);
//            }
//        }
//    }

    /**
     * 获取机构列表
     *
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public List<SysOrg> getOrgList() {
        List<SysOrg> orgListAll = this.getOrgListAll(1);
//        orgListAll = Optional.ofNullable(orgListAll).orElse(new ArrayList<>()).stream().filter(item -> item.getUsed() == 1).collect(Collectors.toList());
        return orgListAll;
    }

    @Override
    public List<SysOrg> getOrgListAll() {
        return getOrgListAll(null);
    }

    /**
     * 获取全部机构列表
     *
     * @return
     * <AUTHOR>
     * @date 2025/1/5
     * @params used—— null：全部  0：只查used=0  1：只查used=1
     */
    @Override
    public List<SysOrg> getOrgListAll(Integer used) {
        List<SysOrg> list = new ArrayList<SysOrg>();
        Map<String, LinkedHashMap> map = redis.getMap(getRedKey());
        Long orgCount = this.getOrgCount(true);
        if (map == null || orgCount==null || map.size()!=orgCount.intValue()) {
            initRedis();
            map = redis.getMap(getRedKey());
        }
        if (map != null) {
            int maxLevel = 0;
            Map<String, SysOrg> orgMap = new HashMap<String, SysOrg>();
            for (String key : map.keySet()) {
                Map<String, Object> mp = map.get(key);
                if (mp != null) {
                    try {
                        SysOrg org = ObjUtils.convertToObject(SysOrg.class, mp);
                        if (used != null && org.getUsed() != null && !org.getUsed().equals(used)) {
                            //按used属性查询，不符合过滤
                            continue;
                        }
                        if (org.getOrglevel() != null && org.getOrglevel() > maxLevel) {
                            maxLevel = org.getOrglevel();
                        }
                        orgMap.put(org.getId(), org);
                        list.add(org);
                    } catch (Exception e) {
                        log.error("", e);
                    }
                }
            }
            // 重新整理序号

//            list.sort(Comparator.comparingInt(SysOrg::getOrglevel).thenComparingInt(SysOrg::getTmSort));

            list.sort((v1, v2) -> {
                String orgpath1 = v1.getOrgpath();
                String orgpath2 = v2.getOrgpath();
                if (StringUtils.isEmpty(orgpath1)) {
                    return -1;
                } else if (StringUtils.isEmpty(orgpath2)) {
                    return 1;
                }
                String[] path1 = orgpath1.split("/");
                String[] path2 = orgpath2.split("/");
                int minLength = Math.min(path1.length, path2.length);
                for (int i = 0; i < minLength; i++) {
                    String code1 = path1[i];
                    String code2 = path2[i];
                    int sort1 = Optional.ofNullable(orgMap.get(code1)).map(SysOrg::getTmSort).orElse(0);
                    int sort2 = Optional.ofNullable(orgMap.get(code2)).map(SysOrg::getTmSort).orElse(0);
                    int comparison = Integer.compare(sort1, sort2);
                    if (comparison != 0) {
                        return comparison;
                    }
                }
                return Integer.compare(path1.length, path2.length);
            });

//            for (SysOrg sysOrg : list) {
//                rebuildSort(orgMap, maxLevel, sysOrg);
//            }
//            // 重新排序
//            return list.stream().sorted(Comparator.comparing(SysOrg::getTmp)).collect(Collectors.toList());
            return list;
        }
        return list;
    }

    /**
     * 更新机构信息redis信息
     *
     * @param emp
     */
    @Override
    public void updateReids(SysOrg org) {
        if (org != null) {
            // Map<String, SysOrg> map = new HashMap<String, SysOrg>();
            // map.put(org.getId(), org);
            if (!redis.hasKey(getRedKey())) {
                this.initRedis();
            }
            this.setOrg2Reids(org);
            // redis.putMap(getRedKey(), map);
        }
    }

    public void setOrg2Reids(SysOrg org) {
        if (org != null) {
            redis.setMapValue(getRedKey(), org.getId(), org);
        }
    }

    /**
     * 获取机构信息map
     *
     * @param empList
     * @return
     */
    private Map<String, SysOrg> getOrgMap(List<SysOrg> list) {
        if (StringUtils.isNotEmpty(list)) {
            Map<String, SysOrg> map = list.stream().collect(Collectors.toMap(SysOrg::getId, a -> a, (k1, k2) -> k1));
            return map;
        } else {
            return null;
        }
    }

    /**
     * 更新redis信息
     */
    @Override
    public void updateReids(List<SysOrg> list) {
        if (list != null) {
            if (!redis.hasKey(getRedKey())) {
                this.initRedis();
            }
            setListToRedis(list);
        }
    }

    private void setListToRedis(List<SysOrg> list) {
        Map<String, SysOrg> map = getOrgMap(list);
        redis.putMap(getRedKey(), map);
    }

    /**
     * 从redis中获取对象
     *
     * @param orgid
     * @return
     */
    private SysOrg getOrgFromRedis(String orgid) {
        SysOrg bean = null;
        if (redis.hasKey(getRedKey())) {
            bean = redis.getMapValue(getRedKey(), orgid, SysOrg.class);
        } else {// 初始化
            this.initRedis();
            bean = redis.getMapValue(getRedKey(), orgid, SysOrg.class);
        }
        return bean;
    }

    /**
     * 从redis中获取机构对象
     *
     * @param orgidList
     * @return
     */
    private List<SysOrg> getOrgListFromRedis(List<String> orgidList) {
        List<SysOrg> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(orgidList)) {
            if (redis.hasKey(getRedKey())) {
                Collection<Object> params = new ArrayList<>(orgidList);
                List<Object> tempList = redis.getMultiMapValue(getRedKey(), params);
                if (tempList != null && tempList.size() > 0) {
                    for (Object obj : tempList) {
                        if (obj != null) {
                            LinkedHashMap map = (LinkedHashMap<String, Object>) obj;
                            SysOrg org = ObjUtils.convertToObject(SysOrg.class, map);
                            list.add(org);
                        }
                    }
                }
            } else {// 初始化
                list = this.initRedis();
            }
        }
        return list;
    }


    /**
     * 清除人员信息记录
     */
    public void clearRedis() {
        redis.delete(getRedKey());
    }

    /**
     * 获取全部父机构代码
     *
     * @param orgCode 机构代码
     * @return
     */
    @Override
    public List<String> getParentOrgCode(String orgCode) {
        SysOrg org = this.findOrgById(orgCode);
        return this.getParentOrgCode(org);
    }


    private List<String> getParentOrgCode(SysOrg org) {
        List<String> list = new ArrayList<String>();
        //SysOrg org = this.findOrgById(orgCode);
        if (org != null && org.getOrgpath() != null && org.getOrglevel() > 0) {
            String[] ids = org.getOrgpath().split("/");
            for (String code : ids) {
                if (StringUtils.isNotEmpty(code) && !code.equals(org.getId())) {
                    list.add(0, code);
                }
            }
            if (list.size() > 0) {
                list.remove(list.size() - 1);
            }
        }
        return list;
    }


    /**
     * 获取父机构全部信息（包括本身）
     *
     * @param orgCode 机构代码
     * @param deep    向上显示多少层级，null：全部
     * @return
     */
    @Override
    public List<SysOrg> getFullParentOrgList(String orgCode, Integer deep) {
        return this.getFullParentOrgList(orgCode, deep, null);
    }

    /**
     * 获取父机构全部信息（包括本身）
     *
     * @param orgCode
     * @param deep
     * @param orgMap  传入的机构信息，避免重复查询org
     * @return
     */
    @Override
    public List<SysOrg> getFullParentOrgList(String orgCode, Integer deep, Map<String, SysOrg> orgMap) {
        List<SysOrg> orgList = new ArrayList<SysOrg>();
        SysOrg myOrg = null;
        if (orgMap != null && orgMap.containsKey(orgCode)) {
            myOrg = orgMap.get(orgCode);
        }
        if (myOrg == null) {
            myOrg = this.findOrgById(orgCode);
            if (orgMap != null) {
                orgMap.put(orgCode, myOrg);
            }
        }
        List<String> listOrg = this.getParentOrgCode(myOrg);
        if (!listOrg.contains(orgCode)) {
            listOrg.add(0, orgCode);
        }
        int n = 0;
        for (String code : listOrg) {
            n++;
            if (deep == null || deep <= 0) {
            } else {
                if (n > deep) {
                    break;
                }
            }
            //SysOrg org = this.findOrgById(code);
            SysOrg org = null;
            if (orgMap != null && orgMap.containsKey(code)) {
                org = orgMap.get(code);
            } else {
                org = this.findOrgById(code);
                if (orgMap != null) {
                    orgMap.put(code, org);
                }
            }
            if (org != null) {
                orgList.add(org);
            }
        }
        return orgList;
    }

    /**
     * 获取机构全路径名称
     *
     * @param orgCode 机构代码
     * @param deep    向上显示多少层级，0：全部
     * @return
     */
    @Override
    public String getFullPathOrgName(String orgCode, Integer deep) {
        String names = "";
        List<SysOrg> orgList = this.getFullParentOrgList(orgCode, deep);
        if (StringUtils.isNotEmpty(orgList)) {
            for (int i = 0; i < orgList.size(); i++) {
                int index = orgList.size() - i - 1;
                SysOrg sysOrg = orgList.get(index);
                names += sysOrg.getOrgname() + "->";
            }
            names = names.substring(0, names.length() - 2);
        }
        return names;
    }

    /**
     * 根据机构ID List 获取机构信息
     *
     * @param orgIdList 机构ID集合
     * @return
     */
    @Override
    public List<SysOrg> getOrgListById(List<String> orgIdList) {
        Where where = Where.create();
        where.eq(SysOrg::getUsed, 1);
        where.in(SysOrg::getId, orgIdList.toArray());

        return entityService.queryData(SysOrg.class, where, null, null);
    }

    /**
     * 获取某个类型的父机构list
     *
     * @param orgCode 机构代码
     * @param orgType 机构类型
     * @return
     */
    @Override
    public List<SysOrg> getParentOrgByType(String orgCode, String orgType) {
        return this.getParentOrgByType(orgCode, orgType, null);
    }

    /**
     * 获取某个类型的父机构list
     *
     * @param orgCode 机构代码
     * @param orgType 机构类型
     * @param mapOrg  机构map,防止重复获取
     * @return
     */
    @Override
    public List<SysOrg> getParentOrgByType(String orgCode, String orgType, Map<String, SysOrg> mapOrg) {
        List<SysOrg> listOrg = new ArrayList<SysOrg>();
        if (StringUtils.isNotEmpty(orgCode) && StringUtils.isNotEmpty(orgType)) {
            List<SysOrg> parentOrg = this.getFullParentOrgList(orgCode, 0, mapOrg);
            if (StringUtils.isNotEmpty(parentOrg)) {
                for (SysOrg sysOrg : parentOrg) {
                    if (orgType.equals(sysOrg.getOrgType()) || (StringUtils.isNotEmpty(sysOrg.getOrgType())
                            && sysOrg.getOrgType().toLowerCase().contains("\"" + orgType.toLowerCase() + "\""))) {
                        listOrg.add(sysOrg);
                    }
                }
            }
        }
        return listOrg;
    }

    /**
     * 获取管辖机构权限列表
     *
     * @return
     * @category <AUTHOR>
     */
    @Override
    public boolean getOrgRight(List<String> rightList) {
        return getOrgRight(null, rightList);
    }

    /**
     * 获取管辖机构权限列表
     *
     * @param userId    人员id
     * @param rightList 返回权限列表
     * @return boolean true 有全部权限 false 没有全部权限
     * @category <AUTHOR>
     */
    @Override
    public boolean getOrgRight(String userId, List<String> rightList) {
        boolean result = false;
        DataPermVo dataPerm = null;
        if (StringUtils.isNotEmpty(userId)) {//传入了人员
            dataPerm = dataPermServ.getDataPerm(userId, "sys_org_gx");
        } else {//当前登录人员
            result = SysUserUtil.isAdmin();// 超管有全部权限
            if (!result) {// 非超管
                dataPerm = dataPermServ.getDataPerm("sys_org_gx");
            }
        }
        if (dataPerm != null) {// 读取到了权限
            if (dataPerm.getDatatype() != null) {
                if (dataPerm.getDatatype().intValue() == 1) {// || dataPerm.getDatatype().intValue() == 2) {//
                    // 有全部权限，不判断了
                    result = true;
                } else {
                    if (rightList != null) {
                        if (StringUtils.isNotEmpty(dataPerm.getDatacodeList())) {
                            rightList.addAll(dataPerm.getDatacodeList());
                        } else {
                            // 无权限，显示本部门及以下
                            SysUser user = SysUserHolder.getCurrentUser();
                            if (user != null) {
                                List<SysOrg> orgList = getOrgList(user.getOrgId());
                                if (StringUtils.isNotEmpty(orgList)) {
                                    for (SysOrg sysOrg : orgList) {
                                        rightList.add(sysOrg.getOrgcode());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 获取管辖机构根节点,需要配合getOrgRight使用，无法获取超管和全部权限对应的机构列表
     *
     * @param List<String> rightList 权限列表
     * @return List<String>
     * @category <AUTHOR>
     */
    @Override
    public List<String> getRightOrgRoot(List<String> rightList) {
        List<String> result = new ArrayList<String>();
        if (StringUtils.isNotEmpty(rightList)) {
            List<SysOrgRelation> orgRelationList = orgrelation.listDataByOrgCodes(rightList);// 获取映射关系
            if (StringUtils.isNotEmpty(orgRelationList)) {
                List<TreeNode<SysOrgRelation>> treeNodeList = new ArrayList<TreeNode<SysOrgRelation>>();
                TreeNode<SysOrgRelation> rootNode = new TreeNode<SysOrgRelation>("0", null, null);// 虚拟一个根节点
                treeNodeList.add(rootNode);
                for (SysOrgRelation temp : orgRelationList) {
                    TreeNode<SysOrgRelation> treeNode = new TreeNode<SysOrgRelation>(temp.getOrgcode(),
                            temp.getPorgcode(), temp);// 节点
                    treeNodeList.add(treeNode);
                }
                Tree<SysOrgRelation> dataTree = new Tree<SysOrgRelation>(rootNode);
                dataTree.buildTree(treeNodeList);// 构建树形
                for (TreeNode<SysOrgRelation> temp : treeNodeList) {
                    if (temp.bean != null) {// 去除虚拟跟节点
                        if (temp.parentNode == null || temp.parentNode.bean == null) {// 树形上级无节点，需要显示在权限树上
                            result.add(temp.bean.getOrgcode());
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 根据机构名称获取机构
     *
     * @param orgName
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<SysOrg> getOrgByOrgName(String orgName) {
        Where where = Where.create();
        where.eq(SysOrg::getOrgname, orgName);
        return entityService.rawQueryListByWhere(SysOrg.class, where);
    }

    /**
     * 获取当前登录人员指定机构的下级管辖机构列表（非管辖机构不返回，如果给定机构管辖，则给定机构也返回）
     *
     * @param orgId      机构代码
     * @param subOrgList 返回给定机构的下级管辖机构（非管辖机构不返回，如果给定机构管辖，则给定机构也返回） 不能为null
     * @return 是否有全部权限 true 有全部权限 false
     * 无全部权限，使用时如果不指定orgId，则需要判断是否有全部权限，有则代表可以使用全部机构
     * @category <AUTHOR>
     */
    @Override
    public boolean getRightSubOrg(String orgId, List<String> subOrgList) {
        boolean result = false;// 是否有全部查询权限
        if (subOrgList != null) {// 返回列表不能为null
            List<String> rightList = new ArrayList<String>();
            result = getOrgRight(rightList);// 获取管辖
            if (StringUtils.isNotEmpty(orgId)) {// 指定了机构代码
                List<SysOrg> orgList = getOrgList(orgId);// 获取给定机构的子机构
                if (StringUtils.isNotEmpty(orgList)) {
                    HashSet<String> rightSet = new HashSet<String>();
                    rightSet.addAll(rightList);// 放到set里，加快检索速度
                    for (SysOrg sysOrg : orgList) {
                        if (StringUtils.isNotEmpty(sysOrg.getOrgcode())) {
                            if (result || rightSet.contains(sysOrg.getOrgcode())) {// 管辖全部，或管辖指定机构
                                subOrgList.add(sysOrg.getOrgcode());
                            }
                        }
                    }
                }
            } else {// 不指定机构代码，则直接返回管辖机构
                subOrgList.addAll(rightList);
            }
        }
        return result;
    }

    /**
     * 通过orglevel获取机构信息
     *
     * @param level
     * @return
     */
    @Override
    public List<SysOrg> getOrgByOrgLevel(Integer level) {
        Where where = Where.create();
        // SELECT * from sys_org where ORGLEVEL=1 and USED=1 ORDER BY TM_SORT
        where.eq(SysOrg::getOrglevel, level);
        where.eq(SysOrg::getUsed, 1);
        Order order = Order.create();
        order.orderByAsc(SysOrg::getTmSort);
        return entityService.rawQueryListByWhere(SysOrg.class, where, order);
    }

    /**
     * 根据路径查找机构
     *
     * @param where
     * @param orgCode 机构代码
     * @category <AUTHOR>
     */
    private void likeOrgPath(Where where, String orgCode) {
        if (where != null && StringUtils.isNotEmpty(orgCode)) {
//			where.lb();
//			where.like(SysOrg::getOrgpath, "/" + orgCode+"/");
//			where.or();
//			where.likeLeft(SysOrg::getOrgpath, "/" + orgCode);
//			where.rb();
            where.like(where.addRight(SysOrg::getOrgpath, "/"), "/" + orgCode + "/");
        }
    }

    /**
     * 按租户编码获取机构信息
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<SysOrg> getOrgListByTenantId(String tenantId) {
        List<SysOrg> result = new ArrayList<SysOrg>();
        Where where = Where.create();
        Order order = Order.create();
        where.eq(SysOrg::getUsed, 1);
        order.orderByAsc(SysOrg::getOrglevel);
        order.orderByAsc(SysOrg::getTmSort);
        result = entityService.rawQueryListWithTenant(tenantId, SysOrg.class, where, order, null);
        return result;
    }

    /**
     * 当前机构是否为某个机构类型一下级别
     *
     * @return
     * <AUTHOR>
     * @date 2024/12/16
     * @params
     */
    @Override
    public JSONObject isOrgTypeLevelLow(String orgCode, String orgType) {
        JSONObject result = new JSONObject();
        boolean flag = false;
        SysOrg currentOrg = this.findOrgById(orgCode);
        if (currentOrg == null) {
            result.put("flag", flag);
            return result;
        }
        List<String> orgTypes = new ArrayList<>();
        if (StringUtils.isNotEmpty(orgType)) {
            String[] split = orgType.split(",");
            orgTypes = Arrays.asList(split);
        }
        if (StringUtils.isNotEmpty(currentOrg.getOrgType()) && orgTypes.contains(currentOrg.getOrgType())) {
            //本身为符合条件的节点
            flag = true;
            result.put("flag", flag);
            result.put("workshopOrg", currentOrg.getOrgcode());
            return result;
        }
        List<String> parentOrgCode = this.getParentOrgCode(orgCode);
        if (StringUtils.isEmpty(parentOrgCode)) {
            flag = false;
            result.put("flag", flag);
            return result;
        }
        for (String porgCode : parentOrgCode) {
            SysOrg porg = this.findOrgById(porgCode);
            if (porg != null && StringUtils.isNotEmpty(porg.getOrgType()) && orgTypes.contains(porg.getOrgType())) {
                //当前机构的 父级节点有 符合 orgType的节点 则证明此节点为目标类型以下级别的节点
                flag = true;
                result.put("flag", flag);
                result.put("workshopOrg", porg.getOrgcode());
                return result;
            }
        }
        flag = false;
        result.put("flag", flag);
        return result;
    }

    /**
     * 获取子机构
     *
     * @param orgCode   机构代码
     * @param orgType   机构类型列表
     * @param isWithOut 是否是排查机构列表 true，排除给定的orgtype列表 false查找给定的orgtype列表
     * @category
     * <AUTHOR>
     */
    private List<SysOrg> getSubOrgMain(String orgCode, List<String> orgType, boolean isWithOut) {
        List<SysOrg> result = new ArrayList<SysOrg>();
        if (StringUtils.isNotEmpty(orgCode)) {
            List<SysOrgRelation> dataList = orgrelation.getUsedDataList();
            List<SysOrg> orgList = this.getOrgList();
            if (StringUtils.isNotEmpty(dataList) && StringUtils.isNotEmpty(orgList)) {
                Map<String, SysOrg> orgMap = orgList.stream()
                        .collect(Collectors.toMap(SysOrg::getOrgcode, SysOrg -> SysOrg, (key1, key2) -> key1));// 将list转换为map
                HashSet<String> keySet = new HashSet<String>();
                List<TreeNode<SysOrgRelation>> treeNodeList = new ArrayList<TreeNode<SysOrgRelation>>();
                TreeNode<SysOrgRelation> rootNode = null;
                for (SysOrgRelation temp : dataList) {
                    if (!keySet.contains(temp.getOrgcode())) {
                        keySet.add(temp.getOrgcode());
                        TreeNode<SysOrgRelation> treeNode = new TreeNode<SysOrgRelation>(temp.getOrgcode(),
                                temp.getPorgcode(), temp);// 节点
                        treeNodeList.add(treeNode);
                        if (orgCode.equals(temp.getOrgcode())) {
                            rootNode = treeNode;
                        }
                    }
                }
                if (rootNode != null) {//未找到机构节点
                    Tree<SysOrgRelation> dataTree = new Tree<SysOrgRelation>(rootNode);
                    dataTree.buildTree(treeNodeList);// 构建树形
                    rootNode.bean.setVersionDate("");//清空用于排序
                    if (isWithOut) {
                        getSubOrgWithOut(rootNode, result, orgType, orgMap);//排除机构
                    } else {
                        getSubOrgBy(rootNode, result, orgType, orgMap);//查找机构
                    }
                }
            }
            if (result.size() > 0) {
                Collections.sort(result, new Comparator<SysOrg>() {//根据指标sort进行排序
                    public int compare(SysOrg arg0, SysOrg arg1) {
                        return ("" + arg0.getOrgNamePath()).compareTo("" + arg1.getOrgNamePath());
                    }
                });
            }
        }
        return result;
    }

    /**
     * 获取给定机构的子机构
     *
     * @param orgCode 指定的机构代码
     * @param orgType List<String> null或者不给定，则只判断无机构类型的机构跳过，如果指定了，则指定类型的机构也跳过，比如指定装置，则跳过装置，取班组
     *                company factory department workshop equipment shiftteam
     * @return
     * @category 获取给定机构的子机构(可以指定那种类型的机构不算子机构 ， 继续向下查找 。 默认为无类型的机构不算子机构)（只取一层）
     * <AUTHOR>
     */
    @Override
    public List<SysOrg> getSubOrgWithOutOrgType(String orgCode, List<String> orgType) {
        // TODO Auto-generated method stub
//        List<SysOrg> result = new ArrayList<SysOrg>();
//        if (StringUtils.isNotEmpty(orgCode)) {
//            List<SysOrgRelation> dataList = orgrelation.getUsedDataList();
//            List<SysOrg> orgList = this.getOrgList();
//            if (StringUtils.isNotEmpty(dataList) && StringUtils.isNotEmpty(orgList)) {
//                Map<String, SysOrg> orgMap = orgList.stream()
//                        .collect(Collectors.toMap(SysOrg::getOrgcode, SysOrg -> SysOrg, (key1, key2) -> key1));// 将list转换为map
//                HashSet<String> keySet = new HashSet<String>();
//                List<TreeNode<SysOrgRelation>> treeNodeList = new ArrayList<TreeNode<SysOrgRelation>>();
//                TreeNode<SysOrgRelation> rootNode = null;
//                for (SysOrgRelation temp : dataList) {
//                    if (!keySet.contains(temp.getOrgcode())) {
//                        keySet.add(temp.getOrgcode());
//                        TreeNode<SysOrgRelation> treeNode = new TreeNode<SysOrgRelation>(temp.getOrgcode(),
//                                temp.getPorgcode(), temp);// 节点
//                        treeNodeList.add(treeNode);
//                        if (orgCode.equals(temp.getOrgcode())) {
//                            rootNode = treeNode;
//                        }
//                    }
//                }
//                if (rootNode != null) {//未找到机构节点
//                    Tree<SysOrgRelation> dataTree = new Tree<SysOrgRelation>(rootNode);
//                    dataTree.buildTree(treeNodeList);// 构建树形
//                    rootNode.bean.setVersionDate("");//清空用于排序
//                    getSubOrgWithOut(rootNode, result, orgType, orgMap);
//                }
//            }
//            if (result.size() > 0) {
//                Collections.sort(result, new Comparator<SysOrg>() {//根据指标sort进行排序
//                    public int compare(SysOrg arg0, SysOrg arg1) {
//                        return ("" + arg0.getOrgNamePath()).compareTo("" + arg1.getOrgNamePath());
//                    }
//                });
//            }
//        }
//        return result;
        return this.getSubOrgMain(orgCode, orgType, true);
    }

    /**
     * 递归获取子机构
     *
     * @param node
     * @param subList 排除列表
     * @category
     * <AUTHOR>
     */
    private void getSubOrgWithOut(TreeNode<SysOrgRelation> node, List<SysOrg> subList, List<String> orgType, Map<String, SysOrg> orgMap) {
        if (StringUtils.isNotEmpty(node.childNodes)) {
            for (TreeNode<SysOrgRelation> temp : node.childNodes) {
                SysOrg org = orgMap.get(temp.id);
                if (org != null) {
                    String sort = "000000" + org.getTmSort();
                    sort = sort.substring(sort.length() - 6);//取6位
                    org.setOrgNamePath(node.bean.getVersionDate() + sort);
                    if (StringUtils.isEmpty(org.getOrgType()) || (orgType != null && orgType.contains(org.getOrgType()))) {//无机构类型（或指定了不做为子节点的类型），不做为子节点，去找下级节点
                        temp.bean.setVersionDate(org.getOrgNamePath());//记录上级的排序
                        getSubOrgWithOut(temp, subList, orgType, orgMap);
                    } else {//符合子节点要求，返回子节点
                        subList.add(org);
                    }
                }
            }
        }
    }

    /**
     * 递归获取子机构
     *
     * @param node
     * @param subList 排除列表
     * @category
     * <AUTHOR>
     */
    private void getSubOrgBy(TreeNode<SysOrgRelation> node, List<SysOrg> subList, List<String> orgType, Map<String, SysOrg> orgMap) {
        if (orgType != null) {
            if (StringUtils.isNotEmpty(node.childNodes)) {
                for (TreeNode<SysOrgRelation> temp : node.childNodes) {
                    SysOrg org = orgMap.get(temp.id);
                    if (org != null) {
                        String sort = "000000" + org.getTmSort();
                        sort = sort.substring(sort.length() - 6);//取6位
                        org.setOrgNamePath(node.bean.getVersionDate() + sort);
                        if (StringUtils.isNotEmpty(org.getOrgType()) && orgType.contains(org.getOrgType())) {//查找到了对应的机构
                            subList.add(org);
                        }
                        temp.bean.setVersionDate(org.getOrgNamePath());//记录上级的排序
                        getSubOrgBy(temp, subList, orgType, orgMap);//递归子机构        
                    }
                }
            }
        }
    }

    /**
     * 获取给定机构的子机构
     *
     * @param orgCode 指定的机构代码
     * @param orgType List<String> 要查找的机构类型的列表 company factory department workshop equipment shiftteam
     * @return
     * @category 获取给定机构的子机构(可以指定要获取那种类型的子机构)
     * <AUTHOR>
     */
    @Override
    public List<SysOrg> getSubOrgByOrgType(String orgCode, List<String> orgType) {
        // TODO Auto-generated method stub
        // TODO Auto-generated method stub
        return this.getSubOrgMain(orgCode, orgType, false);
    }

    /**
     * 获取给定机构的指定类型的上级机构
     *
     * @param orgCode    指定的机构代码
     * @param orgTypeInt 不指定类型ISysOrgService.OrgHasType（找到有类型的上级即返回）  需要找到指定类型机构：公司ISysOrgService.OrgCompany 分厂ISysOrgService.OrgFactory 车间或部门ISysOrgService.OrgWorkshopOrDepartment 装置ISysOrgService.OrgEquipment 班组ISysOrgService.OrgShiftteam
     * @return
     * @category 获取给定机构的指定类型的上级机构
     * <AUTHOR>
     */
    @Override
    public SysOrg getParentOrgByOrgType(String orgCode, int orgTypeInt) {
        // TODO Auto-generated method stub
        SysOrg reuslt = null;
        if (StringUtils.isNotEmpty(orgCode)) {
            List<String> orgType = new ArrayList<>();
            if (orgTypeInt > 0) {
                switch (orgTypeInt) {
                    case ISysOrgService.OrgCompany: { //班组
                        orgType.add("company");
                        break;
                    }
                    case ISysOrgService.OrgFactory: { //装置
                        orgType.add("factory");
                        break;
                    }
                    case ISysOrgService.OrgWorkshopOrDepartment: { //车间或部门
                        orgType.add("workshop");
                        orgType.add("department");
                        break;
                    }
                    case ISysOrgService.OrgEquipment: { //装置
                        orgType.add("equipment");
                        break;
                    }
                    case ISysOrgService.OrgShiftteam: { //班组
                        orgType.add("shiftteam");
                        break;
                    }
                }
            }
            SysOrg org = findOrgById(orgCode);//先查给定机构
            if (org != null && StringUtils.isNotEmpty(org.getOrgType())) {//有机构类型
                if (orgType.contains(org.getOrgType())) {//查找到了指定类型的机构
                    reuslt = org;//给定机构符合要查询的类型，那么直接返回给定机构即可
                }
            }
            if (reuslt == null) {//未查到机构，则取父机构
                List<String> parentPath = getParentOrgCode(orgCode);
                for (String temp : parentPath) {
                    SysOrg parentOrg = findOrgById(temp);
                    if (parentOrg != null) {
                        if (orgTypeInt == ISysOrgService.OrgAllType) {//直接查上级
                            reuslt = parentOrg;
                            break;
                        } else {
                            if (StringUtils.isNotEmpty(parentOrg.getOrgType())) {//有机构类型
                                if (orgTypeInt == ISysOrgService.OrgHasType) {//只查有类型的上级
                                    reuslt = parentOrg;
                                    break;
                                } else {
                                    if (orgType.contains(parentOrg.getOrgType())) {//查找到了指定类型的机构
                                        reuslt = parentOrg;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return reuslt;
    }

    /**
     * 对指定机构进行排序
     *
     * @param orgList
     * @return
     * @category
     * <AUTHOR>
     */
    @Override
    public void sortOrg(List<SysOrg> orgList) {
//		List<SysOrg> result = new ArrayList<SysOrg>();
        if (StringUtils.isNotEmpty(orgList)) {
            List<SysOrgRelation> dataList = orgrelation.getUsedDataList();
            List<SysOrg> allOrgList = this.getOrgList();
            if (StringUtils.isNotEmpty(dataList)) {
                String rootCode = null;//根节点
                for (SysOrg temp : orgList) {
                    if (rootCode == null) {
                        if (StringUtils.isNotEmpty(temp.getOrgpath())) {
                            String[] path = temp.getOrgpath().split("/");
                            if (path != null && path.length > 0) {
                                for (String tempPath : path) {
                                    if (StringUtils.isNotEmpty(tempPath)) {
                                        rootCode = tempPath;//查找机构根节点代码
                                        break;
                                    }
                                }
                            }
                        }
                    } else {
                        break;
                    }
                }
                if (StringUtils.isNotEmpty(rootCode)) {//查找到了根节点id
                    Map<String, SysOrg> orgMap = orgList.stream()
                            .collect(Collectors.toMap(SysOrg::getOrgcode, SysOrg -> SysOrg, (key1, key2) -> key1));// 将list转换为map   
                    Map<String, SysOrg> orgAllMap = allOrgList.stream()
                            .collect(Collectors.toMap(SysOrg::getOrgcode, SysOrg -> SysOrg, (key1, key2) -> key1));// 将list转换为map
                    HashSet<String> keySet = new HashSet<String>();
                    List<TreeNode<SysOrgRelation>> treeNodeList = new ArrayList<TreeNode<SysOrgRelation>>();
                    SysOrgRelation rootBean = new SysOrgRelation();
                    rootBean.setVersionDate("");//排序起始
                    TreeNode<SysOrgRelation> rootNode = new TreeNode<SysOrgRelation>(rootCode, "", rootBean);//根节点
                    treeNodeList.add(rootNode);
                    for (SysOrgRelation temp : dataList) {
                        if (!keySet.contains(temp.getOrgcode())) {
                            keySet.add(temp.getOrgcode());
                            TreeNode<SysOrgRelation> treeNode = new TreeNode<SysOrgRelation>(temp.getOrgcode(),
                                    temp.getPorgcode(), temp);// 节点
                            treeNodeList.add(treeNode);
                        }
                    }
                    Tree<SysOrgRelation> dataTree = new Tree<SysOrgRelation>(rootNode);
                    dataTree.buildTree(treeNodeList);// 构建树形
                    rootNode.bean.setVersionDate("");//清空用于排序
                    sortSubOrg(rootNode, orgMap, orgAllMap);
                    Collections.sort(orgList, new Comparator<SysOrg>() {//根据指标sort进行排序
                        public int compare(SysOrg arg0, SysOrg arg1) {
                            return ("" + arg0.getOrgNamePath()).compareTo("" + arg1.getOrgNamePath());
                        }
                    });
//					result.addAll(orgList);
                }
            }
        }
//		return result;
    }

    /**
     * 对指定机构进行排序（按机构代码查出机构后排序）
     *
     * @param orgList
     * @return
     * @category
     * <AUTHOR>
     */
    @Override
    public List<SysOrg> sortOrgByCode(List<String> orgCodeList) {
        // TODO Auto-generated method stub
        List<SysOrg> result = new ArrayList<SysOrg>();
        if (StringUtils.isNotEmpty(orgCodeList)) {
            List<SysOrg> orgList = this.listDatas(orgCodeList);
            if (StringUtils.isNotEmpty(orgList)) {
                result.addAll(orgList);
            }

//            for (String temp : orgCodeList) {
//                SysOrg org = findOrgById(temp);
//                if (org != null) {
//                    result.add(org);
//                }
//            }
            sortOrg(result);
        }
        return result;
    }

    /**
     * 递归获取子机构
     *
     * @param node
     * @param subList
     * @category
     * <AUTHOR>
     */
    private void sortSubOrg(TreeNode<SysOrgRelation> node, Map<String, SysOrg> orgMap, Map<String, SysOrg> orgAllMap) {
        if (StringUtils.isNotEmpty(node.childNodes)) {
            for (TreeNode<SysOrgRelation> temp : node.childNodes) {
                SysOrg org = orgAllMap.get(temp.id);
                if (org != null) {
                    String sort = "000000" + org.getTmSort();
                    sort = sort.substring(sort.length() - 6);//取6位
                    org.setOrgNamePath(node.bean.getVersionDate() + sort);
                    SysOrg orgData = orgMap.get(temp.id);
                    if (orgData != null) {
                        orgData.setOrgNamePath(org.getOrgNamePath());
                    }
                    orgMap.remove(temp.id);
                    if (orgMap.size() > 0) {
                        temp.bean.setVersionDate(org.getOrgNamePath());//记录上级的排序
                        sortSubOrg(temp, orgMap, orgAllMap);
                    }
                }
            }
        }
    }

    /**
     * 获取当前登录人员所在车间或部门代码
     *
     * @return
     */
    @Override
    public String getUserCjOrgDm() {
        String result = "";
        SysUser user = SysUserHolder.getCurrentUser();
        if (user != null) {
            String usrOrgCode = user.getOrgId();
            SysOrg org = this.getParentOrgByOrgType(usrOrgCode, this.OrgWorkshopOrDepartment);
            if (org != null) {
                result = org.getId();
            }
        }
        return result;
    }

    /**
     * 对机构树形进行裁剪
     *
     * @param treeList
     * @param showOrgType 可显示的机构类型：公司：company 分厂：factory 车间：workshop 部门：department 装置：equipment 班组：shiftteam 多选用逗号分隔
     * @return
     * @category
     * <AUTHOR>
     */
    @Override
    public void cutOrgTree(List<SysOrgTreeData> treeList, String showOrgType) {
        // TODO Auto-generated method stub
        if (StringUtils.isNotEmpty(treeList) && StringUtils.isNotEmpty(showOrgType)) {//需要过滤机构（按类型过滤）
            showOrgType = "," + showOrgType + ",";
            SysOrgTreeData root = new SysOrgTreeData();
            root.setChildren(treeList);//挂载要处理的数据列表
            cutTree(root, showOrgType);//剪切机构节点
            root.setChildren(null);//移除挂载
        }
    }

    /**
     * 对机构树进行裁剪
     *
     * @param node        机构树节点
     * @param showOrgType 可显示的机构类型：公司：company 分厂：factory 车间：workshop 部门：department 装置：equipment 班组：shiftteam 多选用逗号分隔
     * @return result true 下级节点有可显示内容 false 下级节点无显示内容
     * @category
     * <AUTHOR>
     */
    private boolean cutTree(SysOrgTreeData node, String showOrgType) {
        boolean result = false;
        if (node != null && showOrgType != null) {
            if (node.getOrgType() != null) {
                if (showOrgType.indexOf("," + node.getOrgType() + ",") >= 0) {//含有可显示的节点，注意showOrgType要提前在首尾加上逗号形成 ,a,b,格式
                    result = true;
                } else {
                    node.setIsDisabled(true);//禁止选择
                }
            } else {
                node.setIsDisabled(true);//禁止选择
            }
            if (StringUtils.isNotEmpty(node.getChildren())) {
                List<SysOrgTreeData> removeList = new ArrayList<SysOrgTreeData>();
                for (SysOrgTreeData temp : node.getChildren()) {
                    if (cutTree(temp, showOrgType)) {//下级有数据，需要保留此节点
                        result = true;
                    } else {
                        removeList.add(temp);
                    }
                }
                if (removeList.size() > 0) {
                    node.getChildren().removeAll(removeList);//移除不符合的节点
                }
            }
        }
        return result;
    }

    /**
     * 对机构树形进行默认机构添加
     *
     * @param treeList
     * @param defaultValue 默认机构代码
     * @return
     * @category
     * <AUTHOR>
     */
    @Override
    public void addDefaultTreeNode(List<SysOrgTreeData> treeList, String defaultValue) {
        // TODO Auto-generated method stub
        if (treeList != null && StringUtils.isNotEmpty(defaultValue)) {//有默认机构
            SysOrgTreeData root = new SysOrgTreeData();
            root.setChildren(treeList);//挂载要处理的数据列表
            boolean hasNode = findTreeNode(root, defaultValue);//剪切机构节点
            root.setChildren(null);//移除挂载
            if (!hasNode) {//默认机构节点不在树形中
                SysOrg org = findOrgById(defaultValue);//查找默认节点
                if (org != null) {//找到了节点
                    SysOrgTreeData defaultBean = ObjUtils.copyTo(org, SysOrgTreeData.class);
                    if (defaultBean != null) {
                        treeList.add(0, defaultBean);//插入到返回数据列表中
                    }
                }
            }
        }
    }

    /**
     * 对机构树进行裁剪
     *
     * @param node         机构树节点
     * @param defaultValue 默认机构代码
     * @return result true 找到了默认机构对应的节点
     * @category
     * <AUTHOR>
     */
    private boolean findTreeNode(SysOrgTreeData node, String defaultValue) {
        boolean result = false;
        if (node != null && defaultValue != null) {
            if (defaultValue.equals(node.getId())) {//判断节点代码是否为默认节点
                result = true;
            } else {
                if (StringUtils.isNotEmpty(node.getChildren())) {
                    for (SysOrgTreeData temp : node.getChildren()) {
                        if (findTreeNode(temp, defaultValue)) {//查找下级节点
                            result = true;
                            break;//找到就跳出
                        }
                    }
                }
            }
        }
        return result;
    }
}
