package com.yunhesoft.system.org.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrgRelation;
//import com.yunhesoft.system.org.mapper.SysOrgRelationMapper;
import com.yunhesoft.system.org.service.ISysOrgRelationService;

import lombok.extern.log4j.Log4j2;

/**
 * SysOrgRelationMapper
 *
 * <AUTHOR>
 * @category SysOrgRelation接口
 * @date 2020/03/10
 */
@Log4j2
@Service
public class SysOrgRelationImpl implements ISysOrgRelationService {
    @Autowired
    private EntityService entityService;

    @Autowired
    private RedisUtil redis;

    private static String RED_KEY = "SYSTEM:ORG:RELATION";

    @Override
    public List<SysOrgRelation> listData() {
        List<SysOrgRelation> list = null;
        Map<String, LinkedHashMap> rmap = this.getOrgRelationMapFromReids();
        if (rmap == null && rmap.size() == 0) {
            list = this.getDataFromDb();
            if (StringUtils.isNotEmpty(list)) {
                this.setOrgRelationToReids(list);
            }
        } else {
            list = new ArrayList<>();
            for (String key : rmap.keySet()) {
                LinkedHashMap val = rmap.get(key);
                list.add(this.map2bean(val));
            }
        }
        //list = entityService.queryList(SysOrgRelation.class);
        return list;
    }

    @Override
    public SysOrgRelation map2bean(LinkedHashMap val) {
        SysOrgRelation bean = new SysOrgRelation();
        if (val != null) {
            bean.setId(val.get("id") == null ? null : val.get("id").toString());
            bean.setOrgcode(val.get("orgcode") == null ? null : val.get("orgcode").toString());
            bean.setPorgcode(val.get("porgcode") == null ? null : val.get("porgcode").toString());
            bean.setVersionDate(val.get("versionDate") == null ? null : val.get("versionDate").toString());
            bean.setUsed(val.get("used") == null ? null : Integer.parseInt(val.get("used").toString()));
        }
        return bean;
    }


    @Override
    public List<SysOrgRelation> getDataFromDb() {
        Where where = Where.create();
        where.eq(SysOrgRelation::getUsed, 1);
        //Order order = Order.create();
        //order.orderByDesc(SysOrgRelation::getVersionDate);
        // order.orderByDesc(SysOrgRelation::getId);
        return entityService.queryList(SysOrgRelation.class, where);
    }

    private Map<String, SysOrgRelation> getOrgRelMap(List<SysOrgRelation> list) {
        Map<String, SysOrgRelation> map = new HashMap<>();
        if (StringUtils.isNotEmpty(list)) {
            for (SysOrgRelation e : list) {
                map.put(e.getOrgcode(), e);
            }
        }
        return map;
    }

    @Override
    public List<SysOrgRelation> listData(String orgcode) {
        List<SysOrgRelation> list = null;
        SysOrgRelation bean = this.getOrgRelationFromReids(orgcode);
        if (bean != null) {
            list = new ArrayList<>();
            list.add(bean);
        } else {
            Where where = Where.create();
            where.eq(SysOrgRelation::getOrgcode, orgcode);
            where.eq(SysOrgRelation::getUsed, 1);
            Order order = Order.create();
            order.orderByDesc(SysOrgRelation::getVersionDate);
            order.orderByDesc(SysOrgRelation::getId);
            list = entityService.queryList(SysOrgRelation.class, where, order);
            if (StringUtils.isNotEmpty(list)) {
                redis.setMapValue(RED_KEY, list.get(0).getOrgcode(), list.get(0));
            }
        }
        return list;
    }

    @Override
    public List<SysOrgRelation> listDataPids(String pid) {
        List<SysOrgRelation> list = null;
        Where where = Where.create();
        where.eq(SysOrgRelation::getPorgcode, pid);
        where.eq(SysOrgRelation::getUsed, 1);
        Order order = Order.create();
        order.orderByDesc(SysOrgRelation::getVersionDate);
        order.orderByDesc(SysOrgRelation::getId);
        list = entityService.queryList(SysOrgRelation.class, where, order);
        return list;
    }

    /**
     * 根据机构列表，查询机构的绑定关系
     *
     * @param pid
     * @return
     */
    @Override
    public List<SysOrgRelation> listDataByOrgCodes(List<String> orgList) {
        List<SysOrgRelation> list = null;
        if (StringUtils.isNotEmpty(orgList)) {
            Where where = Where.create();
            where.eq(SysOrgRelation::getUsed, 1);
            if (orgList.size() == 1) {
                list = this.listData(orgList.get(0));
                if (StringUtils.isNotEmpty(list)) {
                    return list;
                }
                where.eq(SysOrgRelation::getOrgcode, orgList.get(0));
            } else {
                where.in(SysOrgRelation::getOrgcode, orgList.toArray());
            }
            Order order = Order.create();
            order.orderByDesc(SysOrgRelation::getVersionDate);
            order.orderByDesc(SysOrgRelation::getId);
            list = entityService.queryList(SysOrgRelation.class, where, order);
        }
        return list;
    }

    @Override
    @Transactional
    public boolean deleteList(List<String> list) {
        // return this.removeByIds(list);
        Where where = Where.create();
        if (StringUtils.isNotEmpty(list)) {
            if (list.size() == 1) {
                where.eq(SysOrgRelation::getId, list.get(0));
            } else {
                where.in(SysOrgRelation::getId, list.toArray());
            }
        }
        int rs = entityService.delete(SysOrgRelation.class, where);
        if (rs <= 0) {
            return false;
        } else {
            this.initOrgRelationToReids();
            return true;
        }
    }

    @Override
    @Transactional
    public boolean saveData(SysOrgRelation data) {
        int rs = entityService.rawInsert(data);
        if (rs <= 0) {
            return false;
        } else {
            redis.setMapValue(RED_KEY, data.getOrgcode(), data);
            return true;
        }
    }

    @Override
    @Transactional
    public boolean updateData(SysOrgRelation data) {
        int rs = entityService.rawUpdateById(data);
        if (rs <= 0) {
            return false;
        } else {
            updateRedis(data);
            return true;
        }
    }

    private void updateRedis(SysOrgRelation data) {
        if (data.getUsed() == 0) {
            redis.hDelete(RED_KEY, data.getOrgcode());
        } else {
            redis.setMapValue(RED_KEY, data.getOrgcode(), data);
        }
    }

    @Override
    @Transactional
    public boolean updateDataBatch(List<SysOrgRelation> list) {
        // return this.updateBatchById(list);
        int rs = entityService.updateByIdBatch(list);
        if (rs <= 0) {
            return false;
        } else {
            if (list != null && list.size() > 0) {
                for (SysOrgRelation data : list) {
                    updateRedis(data);
                }
            }
        }
        return true;
    }

    @Override
    @Transactional
    public boolean insertData(List<SysOrgRelation> list) {
        int rs = entityService.insertBatch(list);
        if (rs <= 0) {
            return false;
        } else {
            if (list != null) {
                for (SysOrgRelation data : list) {
                    updateRedis(data);
                }
            }
        }
        return true;
    }

    @Override
    @Transactional
    public boolean insertDataWithTenant(List<SysOrgRelation> list, String tenantId) {
        try {
            entityService.rawInsertBatchWithTenant(tenantId, list);
            for (SysOrgRelation data : list) {
                updateRedis(data);
            }
        } catch (Exception ex) {
            log.error("", ex);
            return false;
        }
        return true;
    }

    @Override
    @Transactional
    public boolean deleteData(String id) {
        // return this.removeById(id);
        Where where = Where.create();
        where.eq(SysOrgRelation::getId, id);
        int rs = entityService.delete(SysOrgRelation.class, where);
        if (rs <= 0) {
            return false;
        } else {
            this.initOrgRelationToReids();
            //this.clearRedis();
            return true;
        }
    }

    /**
     * 获得机构关系map
     *
     * @return
     */
    @Override
    public Map<String, SysOrgRelation> getOrgRelation() {
        Map<String, SysOrgRelation> map = new LinkedHashMap<String, SysOrgRelation>();
        Map<String, LinkedHashMap> rmap = this.getOrgRelationMapFromReids();
        if (rmap == null || rmap.size() == 0) {
            List<SysOrgRelation> list = this.getDataFromDb();
            if (StringUtils.isNotEmpty(list)) {
                this.setOrgRelationToReids(list);
                map = list.stream().collect(Collectors.toMap(SysOrgRelation::getOrgcode, a -> a, (k1, k2) -> k1));
            }
        } else {
            for (String key : rmap.keySet()) {
                LinkedHashMap val = rmap.get(key);
                SysOrgRelation bean = this.map2bean(val);
                map.put(bean.getOrgcode(), bean);
            }
        }
        return map;
    }

    /**
     * 添加或者更新
     *
     * @param list
     * @return
     */
    @Override
    public boolean saveByOrgcode(List<SysOrgRelation> list) {
        boolean bln = false;
        if (StringUtils.isNotEmpty(list)) {
            Date d = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String versionDate = sdf.format(d);
            Map<String, SysOrgRelation> map = getOrgRelation();
            List<SysOrgRelation> insertList = new ArrayList<SysOrgRelation>();
            List<SysOrgRelation> updateList = new ArrayList<SysOrgRelation>();
            for (SysOrgRelation bean : list) {
                SysOrgRelation e = map.get(bean.getOrgcode());
                if (e == null) {// 添加
                    bean.setId(TMUID.getUID());
                    bean.setUsed(1);
                    bean.setVersionDate(versionDate);
                    insertList.add(bean);
                } else {// 修改
                    e.setPorgcode(bean.getPorgcode());
                    e.setUsed(1);
                    e.setVersionDate(versionDate); // 此处未考虑机构版本功能，否则有问题 by x.zhong 2021.8.8
                    updateList.add(e);
                }
            }
            if (insertList.size() > 0) {
                bln = this.insertData(insertList);
            }
            if (updateList.size() > 0) {
                bln = this.updateDataBatch(updateList);
            }
        }
        return bln;
    }

    /**
     * 根据机构代码获取平级的所有机构
     *
     * @param orgCode 机构代码
     * @return
     */
    @Override
    public List<SysOrgRelation> getLevelOrg(String orgCode) {
        List<SysOrgRelation> result = new ArrayList<SysOrgRelation>();
        if (StringUtils.isNotEmpty(orgCode)) {
            List<SysOrgRelation> list = new ArrayList<SysOrgRelation>();
//            Where where = Where.create();
//            where.eq(SysOrgRelation::getUsed, 1);
//            where.eq(SysOrgRelation::getOrgcode, orgCode);
//            list = entityService.queryData(SysOrgRelation.class, where, null, null);
            list = this.listData(orgCode);
            if (StringUtils.isNotEmpty(list)) {
                String pOrgCode = list.get(0).getPorgcode();
                result = this.listDataPids(pOrgCode);
//                Where wherelist = Where.create();
//                wherelist.eq(SysOrgRelation::getPorgcode, pOrgCode);
//                wherelist.eq(SysOrgRelation::getUsed, 1);
//                result = entityService.queryData(SysOrgRelation.class, wherelist, null, null);
            }
        }
        return result;
    }

    /**
     * 查所有使用中的
     *
     * @return
     */
    @Override
    public List<SysOrgRelation> getUsedDataList() {
        return this.listData();
//        Where where = Where.create();
//        where.eq(SysOrgRelation::getUsed, 1);
//        Order order = Order.create();
//        order.orderByDesc(SysOrgRelation::getVersionDate);
//        order.orderByDesc(SysOrgRelation::getId);
//        return entityService.queryList(SysOrgRelation.class, where, order);
    }

    @Override
    public Map<String, LinkedHashMap> getOrgRelationMapFromReids() {
        return redis.getMap(RED_KEY);
    }

    private SysOrgRelation getOrgRelationFromReids(String orgId) {
        return redis.getMapValue(RED_KEY, orgId, SysOrgRelation.class);
    }

    /**
     * 机构关系存储到redis
     *
     * @param list
     */
    @Override
    public void setOrgRelationToReids(List<SysOrgRelation> list) {
        Map<String, SysOrgRelation> map = this.getOrgRelMap(list);
        this.clearRedis();
        redis.setMap(RED_KEY, map);
    }

    /**
     * 初始化机构关系表到redis
     */
    @Override
    public void initOrgRelationToReids() {
        log.info("**********初始化机构关系表到redis**********");
        List<SysOrgRelation> list = this.getDataFromDb();
        if (StringUtils.isNotEmpty(list)) {
            this.setOrgRelationToReids(list);
        }
        log.info("**********机构关系表初始化到redis完毕**********");
    }

    @Override
    public void clearRedis() {
        redis.delete(RED_KEY);
    }
}
