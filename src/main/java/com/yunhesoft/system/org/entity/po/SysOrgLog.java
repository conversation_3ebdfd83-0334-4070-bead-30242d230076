package com.yunhesoft.system.org.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
/**
 * @category SysOrgLog接口
 * <AUTHOR>
 * @date 2020/03/10
 * @update 2021-04-08
 */
@ApiModel(value = "机构对象")
@Entity
@Setter
@Getter
@Table(name="SYS_ORG_LOG")
public class SysOrgLog extends BaseEntity{
	
	private static final long serialVersionUID = 1L;

	/**
	 * 版本
	 */
	@Column(name = "version_date" , length=20 )
	private String versionDate;

	/**
	 * 原部门代码
	 */
	@Column(name = "former_orgcode" , length=50 )
	private String formerOrgcode;
	/**
	 * 原部门名称
	 */
	@Column(name = "former_orgname" , length=200 )
	private String formerOrgname;
	/**
	 * 原部门上级代码
	 */
	@Column(name = "former_porgcode" , length=50 )
	private String formerPorgcode;
	/**
	 * 目标部门代码
	 */
	@Column(name = "destination_orgcode" , length=50 )
	private String destinationOrgcode;
	/**
	 * 目标部门名称
	 */
	@Column(name = "destination_orgname" , length=200 )
	private String destinationOrgname;
	/**
	 * 目标部门上级代码
	 */
	@Column(name = "destination_porgcode" , length=50 )
	private String destinationPorgcode;
	/**
	 * 类型
	 */
	@Column(name = "version_type" , columnDefinition = "int default 0 " )
	private int versionType;
	/**
	 * 描述
	 */
	@Column(name = "description" , length=4000 )
	private String description;
	
	/** 租户id 一般开发人员不需要考虑 **/
//	@Column(name = "TENANT_ID", columnDefinition = "varchar(50) default '0'", length = 50)
//	private String tenant_id = "0";
}
