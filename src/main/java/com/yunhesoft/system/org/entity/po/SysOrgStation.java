package com.yunhesoft.system.org.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 工位信息表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_ORG_STATION")
public class SysOrgStation extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 机构编码 */
    @Column(name="ORG_CODE", length=50)
    private String orgCode;
    
    /** 父节点ID */
    @Column(name="PID", length=50)
    private String pid;
    
    /** 节点类型2分类1工位 */
    @Column(name="NODE_TYPE")
    private Integer nodeType;
    
    /** 节点工位名称 */
    @Column(name="NODE_NAME", length=200)
    private String nodeName;
    
    /** 节点工位描述 */
    @Column(name="NODE_DESC")
    private String nodeDesc;
    
    /** 是否叶子节点 */
    @Column(name="IS_LEAF")
    private Integer isLeaf;
    
    /** 路径信息，包含本数据ID */
    @Column(name="PATH_STR", length=2000)
    private String pathStr;
    
    /** TMSOFT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}

