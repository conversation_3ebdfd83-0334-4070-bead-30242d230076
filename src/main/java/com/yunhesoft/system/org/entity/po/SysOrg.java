package com.yunhesoft.system.org.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @category 机构对象
 * @update 2021-04-08
 * @since 2020/03/10
 */
@ApiModel(value = "机构对象")
@Entity
@Setter
@Getter
@Table(name = "SYS_ORG")
public class SysOrg extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Column(name = "ORGCODE", length = 50)
    private String orgcode;

    @Column(name = "ORGNAME", length = 200)
    private String orgname;

    @Column(name = "ORGLEVEL", columnDefinition = "int default 0 ")
    private Integer orglevel;

    @Column(name = "TM_SORT", columnDefinition = "int default 0 ")
    private Integer tmSort;

    @Column(name = "ORGPATH", length = 1000)
    private String orgpath;

    @Column(name = "VERSION_DATE", length = 20)
    private String versionDate;

    @Column(name = "USED", columnDefinition = "int default 0 ")
    private Integer used;

    @Column(name = "ORG_NUMBER", length = 100)
    private String orgNumber;

    @Column(name = "ORG_HEAD", length = 200)
    private String orgHead;

    @Column(name = "ORG_TYPE", length = 1000)
    private String orgType;

    //机构分类，数据字典获得列表,用于奖金分析
    @Column(name = "ORG_CLASSIFY", length = 50)
    private String orgClassify;

    @Transient
    private String orgTypeLabel;

    @Transient
    private String orgClassifyLabel;

    @Column(name = "SYS", columnDefinition = "int default 0")
    private Integer sys;

    // 电话号码
    @Column(name = "PHONE", length = 1000)
    private String phone;

//	/** 租户ID */
//	@Column(name = "TENANT_ID", columnDefinition = "varchar(50) default '0'", length = 50)
//	private String tenant_id = "0";

    // 名称全路径
    @Transient
    private String orgNamePath;

    // 内存中临时节点，非数据库节点
    @Transient
    private Long tmp;

    //分组名称
    @Column(name = "GROUPNAME", length = 200)
    private String groupname;
    //是否停用  1停用 0启用
    @Column(name = "ISHIDDEN", length = 200, columnDefinition = "int default 0 ")
    private Integer isHidden;
}
