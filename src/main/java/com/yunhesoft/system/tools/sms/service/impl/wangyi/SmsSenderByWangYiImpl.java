package com.yunhesoft.system.tools.sms.service.impl.wangyi;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.system.kernel.service.HttpClientService;
import com.yunhesoft.system.tools.sms.dto.SmsRequest;
import com.yunhesoft.system.tools.sms.dto.SmsResponse;
import com.yunhesoft.system.tools.sms.service.ISmsSender;
import com.yunhesoft.system.tools.sms.service.SmsProperties;
import com.yunhesoft.system.tools.sms.service.impl.wangyi.util.ParamUtils;
import com.yunhesoft.system.tools.sms.service.impl.wangyi.util.RequestUtils;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
@ConditionalOnProperty(prefix = "tm4.sms", name = "provider", havingValue = "wangyi")
public class SmsSenderByWangYiImpl implements ISmsSender {

	@Autowired
	SmsProperties smsProperties;

	@Autowired
	HttpClientService httpClientService;

	@Override
	public SmsResponse sendMsg(SmsRequest smsRequest) {
		MultiValueMap<String, String> params = new LinkedMultiValueMap<>(32);
		SmsResponse smsResponse = new SmsResponse();
		try {
			// 1.设置公共参数
			params.add("secretId", smsProperties.getAccessKeyId());
			params.add("businessId", smsProperties.getProduct());
			params.add("version", "v2");
			params.add("timestamp", String.valueOf(System.currentTimeMillis()));
			// 32随机字符串
			params.add("nonce", ParamUtils.createNonce());
			params.add("mobile", smsRequest.getPhoneNumbers());
			// 使用JSON格式填充模板
			params.add("paramType", "json");
			params.add("params", smsRequest.getTemplateParam());
			params.add("templateId", smsRequest.getTemplateCode());

			// 3.生成签名信息
			String signature = ParamUtils.genSignature(smsProperties.getAccessKeySecret(), params);
			params.add("signature", signature);
			// 4.发送HTTP请求
			// String jsonObj = JSONObject.toJSONString(params);
			String response = RequestUtils.execForm(smsProperties.getDomain(), params);
			// 5.解析报文返回
			SendResponse sendSmsResponse = JSONObject.parseObject(response, SendResponse.class);
			// 6.返回结果
			if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode() == 200) {
				smsResponse.setCode(String.valueOf(sendSmsResponse.getCode()));
				// 请求成功
				  smsResponse.setSuccess(true);
				log.info("短信发送成功");
			} else {
				smsResponse.setCode(String.valueOf(sendSmsResponse.getCode()));
				smsResponse.setMessage(sendSmsResponse.getMsg());
				  smsResponse.setSuccess(false);
				String str = String.format("短信发送未成功，返回值为：%s", sendSmsResponse.getMsg());
				log.info(str);
			}
		} catch (final Exception e1) {
			log.error("Exception() error", e1);
		}

		return smsResponse;
	}

}
