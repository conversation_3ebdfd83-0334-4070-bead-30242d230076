package com.yunhesoft.system.tools.form.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Update;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.tools.files.entity.dto.ExcelQuery;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;
import com.yunhesoft.system.tools.form.dto.SysFormDto;
import com.yunhesoft.system.tools.form.dto.SysTplDto;
import com.yunhesoft.system.tools.form.entity.SysTemplate;
import com.yunhesoft.system.tools.form.entity.SysTemplateRelation;
import com.yunhesoft.system.tools.form.service.SysFormDataService;
import com.yunhesoft.system.tools.form.service.SysFormService;
import com.yunhesoft.system.tools.form.service.SysTplService;

@Service
public class SysTplServiceImpl implements SysTplService {

	@Autowired
	private EntityService dao;

	@Autowired
	private IFilesInfoService fileSrv;

	@Autowired
	private SysFormService formSrv;

	@Autowired
	private IFilesInfoService filesInfoService;

	@Autowired
	private SysFormDataService sysFormDataService;

	/** 获取模板（树形结构） */
	@Override
	public List<SysTplDto> getTplList() {
		List<SysTplDto> res = null;
		res = this.getTplListByPid("root");
		return res;
	}

	public List<SysTplDto> getTplListByPid(String pid) {
		List<SysTplDto> res = new ArrayList<SysTplDto>();
		Where where = Where.create();
		where.eq(SysTemplate::getUsed, 1);
		where.eq(SysTemplate::getPid, pid);
		Order order = Order.create();
		order.orderByAsc(SysTemplate::getId);
		List<SysTemplate> list = dao.rawQueryListByWhere(SysTemplate.class, where, order);
		if (list != null && list.size() > 0) {
			for (SysTemplate tpl : list) {
				SysTplDto dto = new SysTplDto();
				dto.setPid(pid);
				dto.setTplId(tpl.getId());
				dto.setTplName(tpl.getTplName());
				dto.setSort(tpl.getSort());
				List<SysTplDto> children = this.getTplListByPid(tpl.getId());
				if (children != null && children.size() > 0) {
					dto.setChildren(children);
				}
				res.add(dto);
			}
		}
		return res;
	}

	/**
	 * 保存模板信息
	 * 
	 * @param tpl  模板实体类
	 * @param user 操作用户
	 * @return
	 */
	@Override
	@Transactional
	public Boolean saveTpl(SysTplDto tpl, SysUser user) {
		List<SysTemplate> list = null;
		if (tpl != null && tpl.getTplId() != null && tpl.getTplId().length() > 0) {
			Where where = Where.create();
			where.eq(SysTemplate::getId, tpl.getTplId());
			Order order = Order.create();
			order.orderByAsc(SysTemplate::getId);
			list = dao.rawQueryListByWhere(SysTemplate.class, where, order);
		}
		Date now = DateTimeUtils.getNDT();
		String userId = null;
		if (user != null) {
			userId = user.getId();
		}
		SysTemplate bean = null;
		if (list != null && list.size() > 0) {
			bean = list.get(0);
		}
		if (bean == null) {
			bean = new SysTemplate();
			bean.setId(TMUID.getUID());
			bean.setCreateBy(userId);
			bean.setCreateTime(now);
			bean.setUsed(1);
			bean.setPid(tpl.getPid());
		}
		bean.setTplName(tpl.getTplName());
		bean.setUpdateBy(userId);
		bean.setUpdateTime(now);
		int res = dao.rawSave(bean);
		return res > 0 ? true : false;
	}

	/**
	 * 删除模板
	 * 
	 * @param tplId 模板id
	 * @param user  操作用户
	 * @return
	 */
	@Override
	@Transactional
	public boolean deleteTpl(String tplId, SysUser user) {
		Update update = Update.create(SysTemplate::getUsed, 0);
		Where where = Where.create();
		where.eq(SysTemplate::getId, tplId);
		int res = dao.rawUpdate(SysTemplate.class, update, where);
		return res > 0 ? true : false;
	}

	/**
	 * 上传文件
	 * 
	 * @param tplId   模板id
	 * @param fileIds 文档id（多个逗号隔开）
	 * @return
	 */
	@Override
	@Transactional
	public boolean uploadTplFiles(String tplId, String fileIds) {
		List<SysTemplateRelation> datalist = new ArrayList<SysTemplateRelation>();
		String[] ids = fileIds.split(",");
		for (String file : ids) {
			SysTemplateRelation data = new SysTemplateRelation();
			data.setId(TMUID.getUID());
			data.setRelatedType(2);
			data.setTplId(tplId);
			data.setRelatedId(file);
			datalist.add(data);
		}
		dao.insertBatch(datalist);
		return true;
	}

	/**
	 * 删除文件
	 * 
	 * @param list 文件实体类集合
	 * @return
	 */
	@Override
	@Transactional
	public boolean deleteTplFiles(List<SysFilesInfo> list) {
		List<String> idArray = new ArrayList<String>();
		for (SysFilesInfo file : list) {
			idArray.add(file.getId());
		}
		Where where = Where.create();
		where.andIns(SysTemplateRelation::getRelatedId, idArray.toArray());
		int res = dao.rawDeleteByWhere(SysTemplateRelation.class, where);
		if (res > 0) {
//			fileSrv.deleteFilesInfoByIds(idArray.toArray(new String[idArray.size()]));
			fileSrv.deleteFile(idArray.toArray(new String[idArray.size()]));
		}
		return res > 0 ? true : false;
	}

	/**
	 * 获取模板下文档列表
	 * 
	 * @param tplId 模板id
	 * @return
	 */
	@Override
	public List<SysFilesInfo> getTplFileList(String tplId) {
		List<SysFilesInfo> list = null;
		Where where = Where.create();
		where.eq(SysTemplateRelation::getRelatedType, 2);
		where.eq(SysTemplateRelation::getTplId, tplId);
		List<SysTemplateRelation> rlist = dao.rawQueryListByWhere(SysTemplateRelation.class, where);
		if (rlist != null && rlist.size() > 0) {
			List<String> rids = new ArrayList<String>();
			for (SysTemplateRelation r : rlist) {
//				where2.or("ID=?", r.getRelatedId());
				rids.add(r.getRelatedId());
			}
//			Where where2 = Where.create();
//			where2.andIns(SysFilesInfo::getId, rids.toArray());
//			list = dao.rawQueryListByWhere(SysFilesInfo.class, where2);
			list = fileSrv.getFilesInfoListByIds(rids);
		}
		return list;
	}

	/**
	 * 获取全部模板
	 * 
	 * @return
	 */
	@Override
	public List<SysTemplate> getAllTplList() {
		Where where = Where.create();
		where.eq(SysTemplate::getUsed, 1);
		Order order = Order.create();
		order.orderByAsc(SysTemplate::getId);
		List<SysTemplate> list = dao.rawQueryListByWhere(SysTemplate.class, where, order);
		return list;
	}

	/**
	 * 复制模板（同时复制模板下的表单和文档）
	 * 
	 * @param tpl  模板实体类
	 * @param user 操作用户
	 * @return
	 */
	@Override
	public boolean copyTpl(SysTplDto tpl, SysUser user) {
		String sourceTplId = tpl.getTplId();
		String tplName = tpl.getTplName();
		String pid = tpl.getPid();
		Where where = Where.create();
		where.eq(SysTemplate::getId, sourceTplId);
		List<SysTemplate> list = dao.rawQueryListByWhere(SysTemplate.class, where);
		if (list == null) {
			// 查询不到源模板
			return false;
		}
		SysTemplate sourceTpl = list.get(0);

		Date now = DateTimeUtils.getNDT();
		String userId = null;
		if (user != null) {
			userId = user.getId();
		}

		// 复制模板
		SysTemplate indexTpl = ObjUtils.copyTo(sourceTpl, SysTemplate.class);
		String indexTplId = TMUID.getUID();
		indexTpl.setId(indexTplId);
		indexTpl.setCreateBy(userId);
		indexTpl.setCreateTime(now);
		indexTpl.setUpdateBy(userId);
		indexTpl.setUpdateTime(now);
		indexTpl.setPid(pid);
		if (tplName != null && tplName.length() > 0) {
			indexTpl.setTplName(tplName);
		}
		int res = dao.rawSave(indexTpl);

		if (res > 0) {
			where = Where.create();
			where.eq(SysTemplateRelation::getTplId, sourceTplId);
			List<SysTemplateRelation> rlist = dao.rawQueryListByWhere(SysTemplateRelation.class, where);
			if (rlist != null && rlist.size() > 0) {
				for (SysTemplateRelation r : rlist) {
					int type = r.getRelatedType() == null ? 0 : r.getRelatedType();
					if (type == 1) {
						// 表单
						SysFormDto form = new SysFormDto();
						form.setFormId(r.getRelatedId());
						form.setTplId(indexTplId);
						formSrv.copySysForm(form, user);
					} else {
						// 文件
						this.copyTplFile(r.getRelatedId(), indexTplId, user);
					}
				}
			}
		}

		return true;
	}

	private boolean copyTplFile(String sourceFileId, String tplId, SysUser user) {
		Where where = Where.create();
		where.eq(SysFilesInfo::getId, sourceFileId);
		List<SysFilesInfo> list = dao.rawQueryListByWhere(SysFilesInfo.class, where);
		if (list == null) {
			// 查询不到源文件
			return false;
		}
		SysFilesInfo sourceFile = list.get(0);

		Date now = DateTimeUtils.getNDT();
		String userId = null;
		if (user != null) {
			userId = user.getId();
		}

		// 复制表单
		SysFilesInfo indexFile = ObjUtils.copyTo(sourceFile, SysFilesInfo.class);
		indexFile.setId(TMUID.getUID());
		indexFile.setCreateBy(userId);
		indexFile.setCreateTime(now);
		indexFile.setUpdateBy(userId);
		indexFile.setUpdateTime(now);
		int res = dao.rawSave(indexFile);

		// 添加模板信息
		if (res > 0 && tplId != null && tplId.length() > 0) {
			SysTemplateRelation r = new SysTemplateRelation();
			r.setId(TMUID.getUID());
			r.setRelatedType(2);
			r.setRelatedId(indexFile.getId());
			r.setTplId(tplId);
			r.setUpdateBy(userId);
			r.setUpdateTime(now);
			r.setCreateBy(userId);
			r.setCreateTime(now);
			Long maxSort = dao.rawQueryObject(Long.class,
					"select max(SORT) from SYS_TEMPLATE_RELATION where RELATED_TYPE=2 and TPL_ID=?", tplId);
			if (maxSort == null) {
				maxSort = 0L;
			}
			r.setSort(maxSort + 1);
//			r.setSort(dto.getSort());
			res = dao.rawSave(r);
		} else {
			return false;
		}

		return true;
	}

	@Override
	public XWPFDocument exportTplWord(SysTplDto tpl) {
		String tplid = tpl.getTplId();
		String titleName = null;
		Where where = Where.create();
		where.eq(SysTemplate::getUsed, 1);
		where.eq(SysTemplate::getId, tplid);
		List<SysTemplate> list = dao.rawQueryListByWhere(SysTemplate.class, where);
		if(list!=null && list.size() > 0) {
			titleName = list.get(0).getTplName();
		}
		return filesInfoService.getExportWordFromTplJson(tplid, titleName);
	}
	@Override
	public NiceXWPFDocument exportWordFile(String param, String dataId, String tplId, List<String> tempWordList, List<String> signatures) {
		return filesInfoService.exportWordFile(param, dataId, tplId, tempWordList, signatures);
	}

	@Override
    public String getWordName(String dataId, String tplId) {
        String name = "文档.docx";
        if(dataId!=null && dataId.length() > 0) {
        	tplId = sysFormDataService.getTplIdByDataId(dataId);
        }
        if(tplId!=null) {
        	List<SysFilesInfo> flitList = getTplFileList(tplId);
        	if (flitList != null && !flitList.isEmpty()) {
        		name = flitList.get(0).getOldFileName();
        	}
        }
        return name;
    }
	
	@Override
	public void clearTempWordFile(List<String> tempWordList) {
		filesInfoService.clearTempWordFile(tempWordList);
	}
	/**
	 * 导出Excel模板	
	 */
	@Override
	public XSSFWorkbook exportTplExcel(HttpServletResponse response,SysTplDto tpl) {
		String tplid = tpl.getTplId();
		String titleName = null;
		Where where = Where.create();
		where.eq(SysTemplate::getUsed, 1);
		where.eq(SysTemplate::getId, tplid);
		List<SysTemplate> list = dao.rawQueryListByWhere(SysTemplate.class, where);
		if(list!=null && list.size() > 0) {
			titleName = list.get(0).getTplName();
		}
		XSSFWorkbook workbook=filesInfoService.getExportExcelFromTplJson(tplid, titleName);
		return workbook;
	}
	/**
	 * 导出Excel
	 */
	@Override
	public XSSFWorkbook exportExcelFile(ExcelQuery bean) {
		return filesInfoService.exportExcelFile(bean);
	}
}
