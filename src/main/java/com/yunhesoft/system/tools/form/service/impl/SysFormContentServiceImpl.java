package com.yunhesoft.system.tools.form.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.system.kernel.service.impl.EntityServiceImpl;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.form.dto.SysFormDto;
import com.yunhesoft.system.tools.form.entity.SysFormContent;
import com.yunhesoft.system.tools.form.service.SysFormContentService;
import com.yunhesoft.system.tools.form.service.SysFormService;

@Service
public class SysFormContentServiceImpl implements SysFormContentService {

	@Autowired
	private EntityServiceImpl dao;

	@Autowired
	private SysFormService formSrv;

	/**
	 * 根据表单id和版本号（版本号暂时无用，预留以后）获取表单内容
	 * 
	 * @param formId                表单id
	 * @param version(暂时无用，传null即可)
	 * @return
	 */
	@Override
	public SysFormDto getSysFormContent(String formId, String version) {
		SysFormDto res = null;
		SysFormDto form = formSrv.getSysFormById(formId);
		Where where2 = Where.create();
		where2.eq(SysFormContent::getFormId, formId);
		List<SysFormContent> list = dao.rawQueryListByWhere(SysFormContent.class, where2);
		if (form != null && list != null && list.size() > 0) {
			res = convertToDto(list.get(0));
		}
		if (form != null) {
			if (res == null) {
				res = new SysFormDto();
			}
			res.setFormId(form.getFormId());
			res.setFormName(form.getFormName());
			res.setFormDesc(form.getFormDesc());
			res.setTableNames(form.getTableNames());
			res.setUsed(form.getUsed());
//			res.setSort(tpl.getSort());
		}

		return res;
	}

	/**
	 * 根据内容id获取表单内容
	 * 
	 * @param contentId 表单内容id
	 * @return
	 */
	@Override
	public SysFormDto getSysFormContentById(String contentId) {
		SysFormDto res = null;
		Where where = Where.create();
		where.eq(SysFormContent::getId, contentId);
		List<SysFormContent> list = dao.rawQueryListByWhere(SysFormContent.class, where);
		if (list != null && list.size() > 0) {
			res = convertToDto(list.get(0));
		}
		return res;
	}

	/**
	 * 保存表单内容
	 * 
	 * @param SysFormDto dto表单实体类
	 * @param user       操作用户
	 * @return
	 */
	@Override
	@Transactional
	public Boolean saveSysFormContent(SysFormDto dto, SysUser user) {

		boolean flag = formSrv.saveSysFormInfo(dto, user);
		if (!flag) {
			throw new RuntimeException("保存失败");
		}
		SysFormDto tpl = formSrv.getSysFormById(dto.getFormId());
		if (tpl == null) {
			throw new RuntimeException("保存失败");
		}

		List<SysFormContent> list = null;
		SysFormContent bean = null;
		// 先用内容id查找
		if (dto.getFormContentId() != null) {
			Where where = Where.create();
			where.eq(SysFormContent::getId, dto.getFormContentId());
			list = dao.rawQueryListByWhere(SysFormContent.class, where);
		}
		// 查找不到用表单id查找
		if (list == null && dto.getFormId() != null) {
			Where where = Where.create();
			where.eq(SysFormContent::getFormId, dto.getFormId());
			list = dao.rawQueryListByWhere(SysFormContent.class, where);
		}
		if (list != null && list.size() > 0) {
			bean = list.get(0);
		}
		Date now = DateTimeUtils.getNDT();
		String userId = null;
		if (user != null) {
			userId = user.getId();
		}
		if (bean == null) {
			bean = new SysFormContent();
			bean.setId(TMUID.getUID());
			bean.setFormId(tpl.getFormId());
			bean.setCreateBy(userId);
			bean.setCreateTime(now);
		}
		bean.setFormContent(dto.getFormContent());
		bean.setUpdateBy(userId);
		bean.setUpdateTime(now);
		int res = dao.rawSave(bean);
		return res > 0 ? true : false;
	}

	/**
	 * 根据内容id删除表单内容
	 * 
	 * @param contentId 表单内容id
	 * @param user      操作用户
	 * @return
	 */
	@Override
	public Boolean deleteSysFormContent(String contentId, SysUser user) {
		// Where where = Where.create();
		// where.eq(SysFormContent::getId, contentId);
		// int res = dao.rawDeleteByWhere(SysFormContent.class, where);
		SysFormContent bean = new SysFormContent();
		bean.setId(contentId);
		int res = dao.deleteById(bean);
		return res > 0 ? true : false;
	}

	/**
	 * 将数据库实体类转换成输出实体类
	 * 
	 * @param cont
	 * @return
	 */
	private SysFormDto convertToDto(SysFormContent cont) {
		SysFormDto res = null;
		if (cont != null) {
			res = new SysFormDto();
			res.setFormContentId(cont.getId());
			res.setFormId(cont.getFormId());
			res.setFormContent(cont.getFormContent());
			res.setVersion(cont.getVersion());
		}
		return res;
	}

	/**
	 * 根据模板id获取所有表单内容
	 * 
	 * @param tplId 模板id
	 * @return
	 */
	@Override
	public List<SysFormDto> getTplFormContents(String tplId) {
		List<SysFormDto> res = new ArrayList<SysFormDto>();
		List<SysFormDto> formList = formSrv.getSysFormList(tplId, null);
		if (formList != null && formList.size() > 0) {
			for (SysFormDto form : formList) {
				SysFormDto content = this.getSysFormContent(form.getFormId(), null);
				if (content != null) {
					res.add(content);
				}
			}
		}
		return res;
	}
}
