package com.yunhesoft.system.tools.redisManager.entity.vo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class KeyDataVo {

	private long ttl;

	// private long size;

	private String key;

	private String type;

	private String text;

	// private String json;

	// private String raws;

	// private String hexs;

	// private Object data;

	private String showType; // 显示方式

	private List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
}
