package com.yunhesoft.system.tools.redisManager.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.connection.RedisZSetCommands.Tuple;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yunhesoft.core.common.utils.PassUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.tools.redisManager.entity.vo.KeyDataVo;
import com.yunhesoft.system.tools.redisManager.entity.vo.RTreeVo;
import com.yunhesoft.system.tools.redisManager.service.IRMService;

import lombok.extern.log4j.Log4j2;

/**
 * redis管理器实现类
 * 
 * <AUTHOR>
 * @since 2022-08-11
 *
 */

@Log4j2
@Service
public class RMServiceImpl implements IRMService {

	@SuppressWarnings("rawtypes")
	@Autowired
	private RedisTemplate redisTemplate;

	@Value("${spring.redis.database:0}")
	private Integer redisDb;

	// 密码
	@Value("${spring.redis.password:''}")
	private String redisPassword;

	// 数据分页数量
	private static int pageSize = 100000;

	private static String ICON_FOLDER = "el-icon-folder";
	private static String ICON_KEY = "el-icon-s-flag";

	/**
	 * 获取redis工具类
	 * 
	 * @param dbindex
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	private RedisTemplate getRedisTemplate(Integer dbindex) {
		return redisTemplate;
	}

	/**
	 * 获取所有key值
	 * 
	 * @param dbindex
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private Set<String> getKeys(Integer dbindex, String pattern) {
		if (pattern == null || pattern.length() == 0) {
			pattern = "*";
		} else {
			if (!pattern.endsWith("*")) {
				pattern = pattern + "*";
			}
		}
		return getRedisTemplate(dbindex).keys(pattern);
	}

	@Override
	public void flushDb(Integer dbindex) {
		getRedisTemplate(dbindex).getConnectionFactory().getConnection().flushDb();
	}

	/**
	 * 获取默认数据库
	 * 
	 * @return
	 */
	@Override
	public int getDefaultDB() {
		return redisDb;
	}

	@Override
	public List<Map<String, Object>> getDBList() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("value", getDefaultDB());
		map.put("label", "DB" + getDefaultDB());
		list.add(map);
		return list;
	}

	@Override
	public List<RTreeVo> getTreeData(Integer dbindex, String pattern) {
		String pid = "ROOT_DB" + dbindex;
		if (dbindex == null) {
			dbindex = 0;
		}
		return getKeyTree(dbindex, 1, pid, pattern);
	}

	/**
	 * 按条件获取分页数据
	 */
	private List<RTreeVo> getKeyTree(Integer dbindex, int page, String pid, String pattern) {
		LinkedList<RTreeVo> treeList = new LinkedList<>();
		// long startTime = System.currentTimeMillis();
		// jedis.select(index);
		if (StringUtils.isEmpty(pattern)) {
			pattern = "*";
		}
		Set<String> keySet = getKeys(dbindex, pattern);// jedis.keys(pattern);
		if (page <= 0) {
			page = 1;
		}
		if (keySet != null) {
			// 排序
			List<String> keyList = new LinkedList<String>(keySet);
			Collections.sort(keyList);

			// 分页返回
			int startIndex = (page - 1) * pageSize;
			int endIndex = page * pageSize;
			if (endIndex > keyList.size()) {
				endIndex = keyList.size();
			}
			for (String key : keyList.subList(startIndex, endIndex)) {
				groupRecursiveLoad(treeList, key, null, pid);
			}
		}
		// long endTime = System.currentTimeMillis();
		// log.info("getKeyTree查询耗时（毫秒）：" + (endTime - startTime));
		return treeList;
	}

	/**
	 * 分组递归加载节点
	 *
	 * @param nodes
	 * @param key
	 * @param pref
	 * @param pid
	 */
	public static void groupRecursiveLoad(LinkedList<RTreeVo> nodes, String key, String pref, String pid) {
		String thisKey = new String(key);
		if (pref != null && thisKey.startsWith(pref)) {
			thisKey = thisKey.substring(pref.length());
		}

		/** 普通节点类型：直接添加 */
		String[] metas = thisKey.split(":");
		if (metas.length == 1) {
			RTreeVo node = RTreeVo.builder().id(TMUID.getUID()).pId(pid).name(key).text(key).leaf(true)
					.iconCls(ICON_KEY).prefix(key).build();
			nodes.add(node);
			return;
		}

		/** 对象节点类型：递归添加 */
		// 1、判断最后一个是否目标分组
		RTreeVo groupNode = null;
		if (!StringUtils.isEmpty(nodes)) {
			RTreeVo last = nodes.getLast();
			if (!last.isLeaf() && last.getName().equals(metas[0])) {
				groupNode = last;
			}
		}
		String name = metas[0];
		String prefstr = pref;
		if (StringUtils.isEmpty(prefstr)) {
			prefstr = metas[0] + "";
		} else {
			prefstr = prefstr + name;
		}

		// 2、创建分组节点
		if (groupNode == null) {

			groupNode = RTreeVo.builder().id(TMUID.getUID()).pId(pid).name(name).text(name).leaf(false)
					.iconCls(ICON_FOLDER).prefix(prefstr).children(new LinkedList<>()).build();
			nodes.add(groupNode);
		}
		// 3、递归——添加分组children
		groupRecursiveLoad(groupNode.getChildren(), key, (pref == null ? "" : pref) + metas[0] + ":",
				groupNode.getId());
	}

	/**
	 * 获取Redis Key信息
	 */
	@Override
	@SuppressWarnings("unchecked")
	public KeyDataVo getKeyData(Integer dbindex, String key) {
		KeyDataVo keyBean = new KeyDataVo();
		keyBean.setShowType("text");
		if (getRedisTemplate(dbindex).hasKey(key)) {
			keyBean.setKey(key);
			DataType dataType = getRedisTemplate(dbindex).type(key);
			String type = dataType.code();
			keyBean.setType(type);
			keyBean.setTtl(getRedisTemplate(dbindex).getExpire(key)); // keyBean.setTtl(jedis.ttl(key));
			switch (type) {
			case "set":// set (集合)
				keyBean.setShowType("table");
				Set<Object> set = getRedisTemplate(dbindex).opsForSet().members(key);// jedis.smembers(key);
				if (set != null && set.size() > 0) {
					for (Object k : set) {
						Map<String, Object> valMap = new HashMap<String, Object>();
						if (k instanceof Map || k instanceof List) {
							String v = JSON.toJSONString(k);
							valMap.put("key", v);
							valMap.put("value", v);
						} else {
							valMap.put("key", k);
							valMap.put("value", k);
						}
						keyBean.getDataList().add(valMap);
					}
				}
				break;
			case "list":// list (列表)
				keyBean.setShowType("table");
				List<Object> list = getRedisTemplate(dbindex).opsForList().range(key, 0, -1);
				if (list != null && list.size() > 0) {
					for (Object k : list) {
						if (k == null) {
							continue;
						}
						Map<String, Object> valMap = new HashMap<String, Object>();
						if (k instanceof Map || k instanceof List) {
							valMap.put("value", JSON.toJSONString(k));
						} else {
							valMap.put("value", k);
							// valMap.put("key", k);
						}
						keyBean.getDataList().add(valMap);
					}
				}
				break;
			case "zset":// zset (有序集)
				Set<Tuple> zset = getRedisTemplate(dbindex).opsForZSet().reverseRangeByScore(key, 0, -1);
				List<Tuple> zsetList = new ArrayList<>(zset);
//				if (StringUtils.isEmpty(order) || order.equals("asc")) {
//					Collections.reverse(zsetList);
//				}
				StringBuilder zsetBuf = new StringBuilder();
				for (Tuple info : zsetList) {
					zsetBuf.append(info.getValue()).append(",");
					// zsetBuf.append(info.getElement()).append(",");
				}
				String textZset = zsetBuf.toString();
				keyBean.setText(textZset.substring(0, textZset.length() - 1));
				// keyBean.setJson(JSON.toJSONString(zsetList));
				// keyBean.setRaws(keyBean.getText().replace(",", "\r\n"));
				break;
			case "hash": // hash (哈希表)
				keyBean.setShowType("table");
				Map<String, Object> map = getRedisTemplate(dbindex).opsForHash().entries(key);// jedis.hgetAll(key);
				if (map != null && map.size() > 0) {
					for (String k : map.keySet()) {
						Object val = map.get(k);
						Map<String, Object> valMap = new HashMap<String, Object>();
						valMap.put("key", k);
						if (val != null) {
							if (valMap instanceof Map) {
								valMap.put("value", JSON.toJSONString(val));
							} else {
								valMap.put("value", val);
							}

						} else {
							valMap.put("value", "null");
						}
						keyBean.getDataList().add(valMap);
					}
				}
				break;
			case "string":// string (字符串)
				Object obj = getRedisTemplate(dbindex).opsForValue().get(key);
				if (obj == null) {
					keyBean.setText("null");
				} else {
					if (obj instanceof Map || obj instanceof List) {
						keyBean.setText(JSON.toJSONString(obj));
					} else {
						keyBean.setText(obj.toString());
					}
				}
				break;
			}
		}
		return keyBean;
	}

	/**
	 * 删除key值
	 * 
	 * @param dbindex
	 * @param key
	 * @param isKey   key or 分类
	 */
	@SuppressWarnings("unchecked")
	@Override
	public boolean deleteKey(Integer dbindex, String key, Boolean isKey) {
		boolean bln = false;
		if (isKey) {
			bln = getRedisTemplate(dbindex).delete(key);
		} else {
			String keys = key + ":*";
			Set<String> set = getKeys(dbindex, keys);
			long i = getRedisTemplate(dbindex).delete(set);
			bln = i > 0 ? true : false;
		}
		return bln;

	}

	/**
	 * 密码校验
	 * 
	 * @param pass
	 * @return
	 */
	@Override
	public boolean checkPass(String pass) {
		boolean bln = false;
		try {
			if (StringUtils.isNotEmpty(pass) && StringUtils.isNotEmpty(redisPassword)) {
				String s = PassUtils.ConfigFileDecrypt(redisPassword);
				if (s.equals(pass)) {
					bln = true;
				}
			}
		} catch (Exception e) {
			log.error("redis密码校验", e);
		}
		return bln;
	}

}
