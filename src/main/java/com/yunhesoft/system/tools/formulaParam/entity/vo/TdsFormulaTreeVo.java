package com.yunhesoft.system.tools.formulaParam.entity.vo;


import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 	数据源公式设置Vo类
 * <AUTHOR>
 * @date 2025/03/13
 */
@Setter
@Getter
public class TdsFormulaTreeVo {
	
	private String nodeId; // 节点id
	
	private String nodeName; // 节点name
	
	private String id; // id
	
	private String pid; // 父id
	
	private Integer isleaf; // 叶子节点
	
	private String nodeType; // 节点类型：tds-数据源；param-参数；
	
	private List<TdsFormulaTreeVo> children; // 子节点
	
	
	private Boolean disabled = true; //[参数部分]树形用 --- false:显示勾选框（前台有样式限制）
	
	private String iconStr; // 图标
	
	private String paramCode; // 参数编码
	
	private String paramName; // 参数名称
	
}
