package com.yunhesoft.system.tools.weather.service;

import com.alibaba.fastjson.JSONObject;

/**
 * 天气预报服务
 * x.zhong 2025.4.17
 */
public interface IWeatherService {

    /**
     * 获取天气信息
     *
     * @param url      接口地址
     * @param cityCode 城市编码
     * @return
     */
    public JSONObject getWeatherInfo(String url, String cityCode);

    /**
     * 获取实时天气
     *
     * @param cityCode
     * @return
     */
    public JSONObject getWeatherNow(String cityCode);

    /**
     * 获取3天天气
     *
     * @param cityCode
     * @return
     */
    public JSONObject getWeather3d(String cityCode);

}
