package com.yunhesoft.system.tools.online.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.ClientInfo;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.HttpServletUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.tools.online.entity.dto.QueryOnlineUserDto;
import com.yunhesoft.system.tools.online.entity.vo.OnlineUserVo;
import com.yunhesoft.system.tools.online.service.IOnlineUserService;
import com.yunhesoft.tmtools.JwtUser;

import lombok.extern.log4j.Log4j2;

/**
 * 在线用户服务实现类
 * 
 * <AUTHOR>
 * @date 2022/08/04 10:25
 */
@Service
@Log4j2
public class OnlineUserServiceImpl implements IOnlineUserService {

	@Autowired
	private RedisUtil redis; // redis实例

	private static final String REDKEY = "USER_ONLINE:";

	private static final long ACTIVE_SECOND = 3 * 60; // 保持心跳时间 3分钟（单位秒）

	// private static boolean enable = true; // 是否启用

	/** 是否启用 */
	@Value("${app.useronline.enable:true}")
	private Boolean enable;

	/**
	 * 获取现在人员列表
	 * 
	 * @param param
	 * @return
	 */
	@Override
	public List<Map<String, String>> getOnlineUserList(QueryOnlineUserDto param) {
		// int page = param.getPage() == null ? 1 : param.getPage();
		int pageSize = param.getPageSize() == null ? 0 : param.getPageSize();
		List<Map<String, String>> list = this.getData(param);
		param.setRecordCount(list.size());
		if (pageSize > 0 && list.size() > pageSize) {// 此功能不支持分页，只支持获取前多少条记录
			int toIndex = pageSize;
			return list.subList(0, toIndex);
		}
		return list;
	}

	/**
	 * 用户登录注册
	 */
	@Override
	public void userLogIn(OnlineUserVo vo) {
		try {
			if (enable) {
				String key = REDKEY + vo.getLoginIp() + ":" + vo.getLoginId();
				redis.setObject(key, vo, ACTIVE_SECOND);
			}
		} catch (Exception e) {
			log.error("在线用户", e);
		}
	}

	/**
	 * 用户登出
	 */
	@Override
	public void userLogOut(String ipAddress, String loginName) {
		try {
			if (enable) {
				String key = REDKEY + ipAddress + ":" + loginName;
				redis.delete(key);
			}
		} catch (Exception e) {
			log.error("在线用户", e);
		}
	}

	/**
	 * 用户心跳保持
	 */
	@Override
	public boolean userActive(HttpServletRequest request, JwtUser jwtUser) {
		try {
			if (enable) {
				String ip = HttpServletUtils.getRemoteAddr(request); // 获取客户端ip地址
				String loginName = jwtUser.getUserName();
				OnlineUserVo vo = getOnlineUserVo(ip, loginName);
				if (vo == null) {
					SysUser user = ObjUtils.copyTo(jwtUser, SysUser.class);
					vo = getOnlineUserVo(request, user);// 初始化
				} else {// 更新最后时间
					// setUserInfo(user, vo);
					vo.setLastActiveTime(DateTimeUtils.getNowDateTimeStr());
				}
				this.userLogIn(vo);
				return true;

			}
		} catch (Exception e) {
			log.error("在线用户", e);
		}
		return false;
	}

	/**
	 * 从redis中获取用户在线信息
	 * 
	 * @param ipAddress
	 * @return
	 */
	private OnlineUserVo getOnlineUserVo(String ipAddress, String loginName) {
		if (enable) {
			String key = REDKEY + ipAddress + ":" + loginName;
			return redis.getClassObject(OnlineUserVo.class, key);
		} else {
			return null;
		}
	}

	/**
	 * 更新用户信息
	 * 
	 * @param vo
	 */
	private void setUserInfo(SysUser user, OnlineUserVo vo) {
		if (enable) {
			if (user == null) {
				user = SysUserHolder.getCurrentUser();
			}
			if (user != null) {
				// vo.setToken(this.getToken());
				vo.setLoginId(user.getUserName());
				vo.setUserId(user.getId());
				vo.setUserName(user.getRealName());
				vo.setOrgName(user.getOrgName());
				vo.setOrgCode(user.getOrgId());
			}
		}
	}

	/**
	 * 初始化在线vo对象
	 * 
	 * @param request
	 * @return
	 */
	private OnlineUserVo initOnlineUser(HttpServletRequest request, SysUser user, OnlineUserVo vo) {
		if (enable) {
			if (vo == null) {
				vo = new OnlineUserVo();
				setUserInfo(user, vo);
			}
			vo.setLastActiveTime(DateTimeUtils.getNowDateTimeStr());
			vo.setLoginInTime(DateTimeUtils.getNowDateTimeStr());
			// vo.setOnlineFlag(1);
			return setClient(request, vo);
		} else {
			return null;
		}
	}

	@Override
	public OnlineUserVo getOnlineUserVo(HttpServletRequest request, SysUser user) {
		return this.initOnlineUser(request, user, null);
	}

	/**
	 * 客户端信息赋值
	 * 
	 * @param request
	 * @param vo
	 * @return
	 */
	private OnlineUserVo setClient(HttpServletRequest request, OnlineUserVo vo) {
		ClientInfo clientInfo = new ClientInfo(request);// 客户端信息
		vo.setExplorerName(clientInfo.parseExplorerName());
		vo.setExplorerVer(clientInfo.getExplorerVer());
		if (StringUtils.isEmpty(vo.getLoginIp())) {
			vo.setLoginIp(clientInfo.getIpAddress());
		}
		vo.setOsName(clientInfo.parseOSName());
		vo.setOsVer(clientInfo.getOSVer());
		return vo;
	}

	/**
	 * 获取在线用户
	 * 
	 * @param param
	 * @param page
	 * @param pageSize
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private List<Map<String, String>> getData(QueryOnlineUserDto param) {
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		String pattern = REDKEY + "*";
		Collection<String> keys = redis.keys(pattern);
		if (keys != null) {
			List<?> tempList = redis.multiGet(keys);
			if (tempList != null && tempList.size() > 0) {
				list = (List<Map<String, String>>) tempList;
				list = this.filterList(list, "userName", param.getUserName());// 按照姓名过滤
				list = this.filterList(list, "loginId", param.getLoginId());// 按照登陆名过滤
			}
		}
		// 按照登陆时间降序排列
		Collections.sort(list, (o1, o2) -> {
			return (((String) o2.get("loginInTime"))).compareTo((String) o1.get("loginInTime"));
		});
		return list;
	}

	/**
	 * 数据过滤
	 * 
	 * @param list
	 * @return
	 */
	private List<Map<String, String>> filterList(List<Map<String, String>> list, String filterCol, String filterStr) {
		if (StringUtils.isNotEmpty(filterStr)) {
			return list.stream().filter(u -> u.get(filterCol) != null && u.get(filterCol).indexOf(filterStr) >= 0)
					.collect(Collectors.toList());
		} else {
			return list;
		}
	}
}
