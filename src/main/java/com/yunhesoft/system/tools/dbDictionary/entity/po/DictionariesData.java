package com.yunhesoft.system.tools.dbDictionary.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * DictionariesData
 *
 * <AUTHOR>
 * @date 2019/12/24
 */
@Entity
@Getter
@Setter
@Table(name = "DICTIONARIES_DATA")
public class DictionariesData extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "父id")
	@Column(name = "PARENTID", length = 50)
	private String parentId;

	@ApiModelProperty(value = "排序")
	@Column(name = "SORT")
	private int sort;

	@ApiModelProperty(value = "列名")
	@Column(name = "COLUMNNAME", length = 200)
	private String columnName;

	@ApiModelProperty(value = "中文名")
	@Column(name = "CHINESENAME", length = 200)
	private String chineseName;

	@ApiModelProperty(value = "列备注")
	@Column(name = "COLUMNDESCRIBES", length = 4000)
	private String columnDescribes;

	@ApiModelProperty(value = "列标记")
	@Column(name = "COLUMNMARK")
	private Integer columnMark;

	@ApiModelProperty(value = "键可用")
	@Column(name = "KEYABLE")
	private Integer keyable;

	@ApiModelProperty(value = "数据类型")
	@Column(name = "COLUMNTYPE", length = 50)
	private String columnType;

	@ApiModelProperty(value = "是否可以为空")
	@Column(name = "NOTNULLABLE")
	private Integer notNullable;

	@ApiModelProperty(value = "列默认值")
	@Column(name = "COLUMNDEFAULT", length = 100)
	private String columnDefault;

	@ApiModelProperty(value = "是否启用")
	@Column(name = "USED")
	private Integer used;

	@ApiModelProperty(value = "是否更新")
	@Column(name = "ISUPDATE")
	private int isUpdate;
}
