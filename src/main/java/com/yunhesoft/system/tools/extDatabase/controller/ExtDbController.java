package com.yunhesoft.system.tools.extDatabase.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.tools.extDatabase.entity.dto.ExtDbConnDto;
import com.yunhesoft.system.tools.extDatabase.entity.po.SysExtdbConn;
import com.yunhesoft.system.tools.extDatabase.service.IExtDbService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "外部数据库连接")
@RestController
@RequestMapping("/system/extdatabase")
public class ExtDbController {

	@Autowired
	private IExtDbService extDbServ;

	@ApiOperation(value = "获取数据字典表的列信息")
	@RequestMapping(value = "/getDbConnList", method = RequestMethod.POST)
	public Res<?> getDbConnList() {
		return Res.OK(extDbServ.getDbConnConfigList());
	}

	@ApiOperation(value = "测数据库是否连接成功")
	@RequestMapping(value = "/connTest", method = RequestMethod.POST)
	public Res<?> dbConnTest(@RequestBody ExtDbConnDto dto) {
		return Res.OK(extDbServ.dbConnTest(dto));
	}

	@ApiOperation(value = "添加数据库连接配置")
	@RequestMapping(value = "/insertDbConnConfig", method = RequestMethod.POST)
	public Res<?> insertDbConnConfig(@RequestBody SysExtdbConn bean) {
		return Res.OK(extDbServ.insertDbConnConfig(bean));
	}

	@ApiOperation(value = "修改数据库连接配置")
	@RequestMapping(value = "/updateDbConnConfig", method = RequestMethod.POST)
	public Res<?> updateDbConnConfig(@RequestBody SysExtdbConn bean) {
		return Res.OK(extDbServ.updateDbConnConfig(bean));
	}

	@ApiOperation(value = "删除数据库连接配置")
	@RequestMapping(value = "/deleteDbConnConfig", method = RequestMethod.POST)
	public Res<?> deleteDbConnConfig(@RequestBody SysExtdbConn bean) {
		return Res.OK(extDbServ.deleteDbConnConfig(bean));
	}

}
