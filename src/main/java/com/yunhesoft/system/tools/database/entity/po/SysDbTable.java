package com.yunhesoft.system.tools.database.entity.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import lombok.Data;

@Entity
@Table(name = "SYS_DB_TABLE", uniqueConstraints = { @UniqueConstraint(columnNames = { "TABLE_NAME" }) })
@Data
public class SysDbTable {
	/** 表格标识 */
	@Column(name = "ID")
	@Id
	private long id;
	/** 数据库 */
	@Column(name = "DATABASE_NAME")
	private String databaseName;
	/** 表格名称 */
	@Column(name = "TABLE_NAME", length = 32)
	private String tableName;
	/** 表格模式 0 实体表格 1 列式表格 */
	@Column(name = "TABLE_PERSIST_MODE")
	private int tablePersistMode;
	/** 表格备注 */
	@Column(name = "TABLE_COMMENT", length = 255)
	private String comment;
	/** 创建时间 */
	@Column(name = "CREATE_TIME")
	private Date createTime;
	/** 更新时间 */
	@Column(name = "UPDATE_TIME")
	private Date updateTime;
	/** 删除时间 */
	@Column(name = "DELETE_TIME")
	private Date deleteTime;
	@Column(name = "CREATE_BY")
	private String createBy;
	@Column(name = "UPDATE_BY")
	private String updateBy;
	// 差异 字段名称，字段值
	@Transient
	private Map<String, Object> differences;
	/** 是否已同步到数据库 */
	@Column(name = "IS_SYNCHRONIZED")
	private Integer isSynchronized;

	@Transient
	private List<SysDbTableField> fields = new ArrayList<SysDbTableField>();
	@Transient
	private List<SysDbTableIndex> indexes = new ArrayList<SysDbTableIndex>();

	/** 来源： 人工设置=0 数据表格=1 数据库反转=2 */
	@Transient
	private int source;

	/** 删除标识 0 无操作 1 创建 2 更新 3 删除 */
	@Transient
	private int status;
}
