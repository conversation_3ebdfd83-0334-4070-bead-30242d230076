package com.yunhesoft.system.tools.dict.utils;

import java.util.Collection;
import java.util.List;

import com.yunhesoft.core.constants.Constants;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.tools.dict.entity.SysDictData;

/**
 * 字典工具类
 * 
 */
public class DictUtils {

	/**
	 * 分隔符
	 */
	public static final String SEPARATOR = ",";

	/**
	 * 设置字典缓存
	 * 
	 * @param key       参数键
	 * @param dictDatas 字典数据列表
	 */
	public static void setDictCache(String key, List<SysDictData> dictDatas) {
		SpringUtils.getBean(RedisUtil.class).setObject(getCacheKey(key), dictDatas);
	}

	/**
	 * 获取字典缓存
	 * 
	 * @param key 参数键
	 * @return dictDatas 字典数据列表
	 */
	public static List<SysDictData> getDictCache(String key) {
		List<SysDictData> dictDatas = SpringUtils.getBean(RedisUtil.class).getClassList(SysDictData.class, key);
		return dictDatas;
	}

	/**
	 * 根据字典类型和字典值获取字典标签
	 * 
	 * @param dictType  字典类型
	 * @param dictValue 字典值
	 * @return 字典标签
	 */
	public static String getDictLabel(String dictType, String dictValue) {
		return getDictLabel(dictType, dictValue, SEPARATOR);
	}

	/**
	 * 根据字典类型和字典标签获取字典值
	 * 
	 * @param dictType  字典类型
	 * @param dictLabel 字典标签
	 * @return 字典值
	 */
	public static String getDictValue(String dictType, String dictLabel) {
		return getDictValue(dictType, dictLabel, SEPARATOR);
	}

	/**
	 * 根据字典类型和字典值获取字典标签
	 * 
	 * @param dictType  字典类型
	 * @param dictValue 字典值
	 * @param separator 分隔符
	 * @return 字典标签
	 */
	public static String getDictLabel(String dictType, String dictValue, String separator) {
		StringBuilder propertyString = new StringBuilder();
		List<SysDictData> datas = getDictCache(dictType);

		if (StringUtils.containsAny(separator, dictValue) && StringUtils.isNotEmpty(datas)) {
			for (SysDictData dict : datas) {
				for (String value : dictValue.split(separator)) {
					if (value.equals(dict.getDictValue())) {
						propertyString.append(dict.getDictLabel() + separator);
						break;
					}
				}
			}
		} else {
			for (SysDictData dict : datas) {
				if (dictValue.equals(dict.getDictValue())) {
					return dict.getDictLabel();
				}
			}
		}
		return StringUtils.stripEnd(propertyString.toString(), separator);
	}

	/**
	 * 根据字典类型和字典标签获取字典值
	 * 
	 * @param dictType  字典类型
	 * @param dictLabel 字典标签
	 * @param separator 分隔符
	 * @return 字典值
	 */
	public static String getDictValue(String dictType, String dictLabel, String separator) {
		StringBuilder propertyString = new StringBuilder();
		List<SysDictData> datas = getDictCache(dictType);

		if (StringUtils.containsAny(separator, dictLabel) && StringUtils.isNotEmpty(datas)) {
			for (SysDictData dict : datas) {
				for (String label : dictLabel.split(separator)) {
					if (label.equals(dict.getDictLabel())) {
						propertyString.append(dict.getDictValue() + separator);
						break;
					}
				}
			}
		} else {
			for (SysDictData dict : datas) {
				if (dictLabel.equals(dict.getDictLabel())) {
					return dict.getDictValue();
				}
			}
		}
		return StringUtils.stripEnd(propertyString.toString(), separator);
	}

	/**
	 * 清空字典缓存
	 */
	public static void clearDictCache() {
		// Collection<String> keys =
		// SpringUtils.getBean(RedisCache.class).keys(Constants.SYS_DICT_KEY + "*");
		// SpringUtils.getBean(RedisCache.class).deleteObject(keys);
		Collection<String> keys = SpringUtils.getBean(RedisUtil.class).keys(Constants.SYS_DICT_KEY + "*");
		SpringUtils.getBean(RedisUtil.class).delete(keys);
	}

	/**
	 * 设置cache key
	 * 
	 * @param configKey 参数键
	 * @return 缓存键key
	 */
	public static String getCacheKey(String configKey) {
		return Constants.SYS_DICT_KEY + configKey;
	}
}
