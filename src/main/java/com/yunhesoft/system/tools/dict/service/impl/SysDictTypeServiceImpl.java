package com.yunhesoft.system.tools.dict.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.constants.UserConstants;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Update;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.entity.SysDictType;
import com.yunhesoft.system.tools.dict.service.ISysDictDataService;
import com.yunhesoft.system.tools.dict.service.ISysDictTypeService;
import com.yunhesoft.system.tools.dict.utils.DictUtils;

/**
 * 字典 业务层处理
 * 
 */
@Service
public class SysDictTypeServiceImpl implements ISysDictTypeService {
	@Autowired
	private EntityService dao;

	@Autowired
	private ISysDictDataService dictDataService;

	/**
	 * 项目启动时，初始化字典到缓存
	 */
	@Override
	public void init() {
		List<SysDictType> dictTypeList = dao.rawQueryListAll(SysDictType.class);
		for (SysDictType dictType : dictTypeList) {
			Where where = Where.create();
			where.eq(SysDictData::getDictType, dictType.getDictType());
			List<SysDictData> dictDatas = dao.rawQueryListByWhere(SysDictData.class, where);
			DictUtils.setDictCache(dictType.getDictType(), dictDatas);
		}
	}

	/**
	 * 根据条件分页查询字典类型
	 * 
	 * @param dictType 字典类型信息
	 * @return 字典类型集合信息
	 */
	@Override
	public List<SysDictType> selectDictTypeList(SysDictType dictType) {
		Where where = Where.create();
		if (dictType != null) {
			if (StringUtils.isNotEmpty(dictType.getModuleCode())) {
				where.eq(SysDictType::getModuleCode, dictType.getModuleCode());
			}
			if (StringUtils.isNotEmpty(dictType.getDictType())) {
				where.eq(SysDictType::getDictType, dictType.getDictType());
			}
			if (StringUtils.isNotEmpty(dictType.getDictName())) {
				where.like(SysDictType::getDictName, dictType.getDictName());
			}
			return dao.rawQueryListByWhere(SysDictType.class, where);
		} else {
			return dao.rawQueryListAll(SysDictType.class);
		}
	}

	/**
	 * 根据所有字典类型
	 * 
	 * @return 字典类型集合信息
	 */
	@Override
	public List<SysDictType> selectDictTypeAll() {
		return dao.rawQueryListAll(SysDictType.class);
	}

	/**
	 * 根据字典类型查询字典数据
	 * 
	 * @param dictType 字典类型
	 * @return 字典数据集合信息
	 */
	@Override
	public List<SysDictData> selectDictDataByType(String dictType) {
		List<SysDictData> dictDatas = DictUtils.getDictCache(dictType);
		if (StringUtils.isNotEmpty(dictDatas)) {
			return dictDatas;
		}
		SysDictData dictData = new SysDictData();
		dictData.setDictType(dictType);
		dictData.setStatus("0");
		dictDatas = dictDataService.selectDictDataList(dictData);
		if (StringUtils.isNotEmpty(dictDatas)) {
			DictUtils.setDictCache(dictType, dictDatas);
			return dictDatas;
		}
		return null;
	}

	/**
	 * 根据字典类型ID查询信息
	 * 
	 * @param dictId 字典类型ID
	 * @return 字典类型
	 */
	@Override
	public SysDictType selectDictTypeById(String dictId) {
		return dao.queryObjectById(SysDictType.class, dictId);
	}

	/**
	 * 根据字典类型查询信息
	 * 
	 * @param dictType 字典类型
	 * @return 字典类型
	 */
	@Override
	public SysDictType selectDictTypeByType(String dictType) {
		return dao.rawQueryObjectByColumn(SysDictType.class, SysDictType::getDictType, dictType);
	}

	/**
	 * 批量删除字典类型信息
	 * 
	 * @param dictIds 需要删除的字典ID
	 * @return 结果
	 */
	@Override
	public int deleteDictTypeById(String dictId) {
		int res = 0;
		if (StringUtils.isNotEmpty(dictId)) {
			SysDictType dictType = selectDictTypeById(dictId);
			Where where = Where.create();
			where.eq(SysDictData::getDictType, dictType.getDictType());
			if (dao.countByWhere(SysDictData.class, where) > 0) {
				throw new RuntimeException(String.format("%1$s已分配,不能删除", dictType.getDictName()));
			}
			res = delDictTypeById(dictId);
		}
		if (res > 0) {
			DictUtils.clearDictCache();
		}
		return res;
	}

	/**
	 * 删除字典类型数据
	 * 
	 * @param dictIds
	 * @return
	 */
	private int delDictTypeById(String dictId) {
		SysDictType bean = new SysDictType();
		bean.setId(dictId);
		return dao.rawDeleteById(bean);
		// return dao.deleteIn(SysDictType.class, SysDictType::getId, dictIds);
	}

	/**
	 * 清空缓存数据
	 */
	@Override
	public void clearCache() {
		DictUtils.clearDictCache();
	}

	/**
	 * 新增保存字典类型信息
	 * 
	 * @param dictType 字典类型信息
	 * @return 结果
	 */
	@Override
	public int insertDictType(SysDictType dictType) {
		dictType.setId(TMUID.getUID());
		dictType.setCreateTime(new Date());
		int row = dao.rawInsert(dictType);
		if (row > 0) {
			DictUtils.clearDictCache();
		}
		return row;
	}

	@Override
	public int insertDictTypeDisableTenant(SysDictType dictType) {
		dictType.setId(TMUID.getUID());
		dictType.setCreateTime(new Date());
		int row = dao.rawInsertDisableTenant(dictType);
		if (row > 0) {
			DictUtils.clearDictCache();
		}
		return row;
	}

	/**
	 * 修改保存字典类型信息
	 * 
	 * @param dictType 字典类型信息
	 * @return 结果
	 */
	@Override
	@Transactional
	public int updateDictType(SysDictType dictType) {
		SysDictType oldDict = this.selectDictTypeById(dictType.getId());
		Where where = Where.create();
		where.eq(SysDictType::getDictType, oldDict.getDictType());
		dao.rawUpdate(SysDictData.class, Update.create(SysDictType::getDictType, dictType.getDictType()), where);
		int row = dao.rawUpdateById(dictType);
		if (row > 0) {
			DictUtils.clearDictCache();
		}
		return row;
	}

	/**
	 * 校验字典类型称是否唯一
	 * 
	 * @param dict 字典类型
	 * @return 结果
	 */
	@Override
	public String checkDictTypeUnique(SysDictType dict) {
		if (dao.checkUniqueByColumn(SysDictType.class, SysDictType::getDictType, dict.getDictType())) {
			return UserConstants.NOT_UNIQUE;
		}
		return UserConstants.UNIQUE;
	}
}
