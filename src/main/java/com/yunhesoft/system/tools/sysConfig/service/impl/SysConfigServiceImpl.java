package com.yunhesoft.system.tools.sysConfig.service.impl;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.EntityUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.sysConfig.entity.SysConfig;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;

import lombok.extern.log4j.Log4j2;

/**
 * 系统配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-31
 */
@Log4j2
@Service
public class SysConfigServiceImpl implements ISysConfigService {

    @Autowired
    private EntityService entityService;

    @Autowired
    private RedisUtil redis;

    private String configKey = "SYSTEM:CONFIG";


    @Value("${TM4Sys.themesCss:''}")
    private String TM4SysThemesCss;

    /**
     * 获得系统参数值（优先内存数据库）
     *
     * @param key 参数编码
     * @return
     */
    @Override
    public String getSysConfig(String key) {
        try {
            String value = this.getSysConfigByKeyFromRedis(key);
            if (value == null) {
                SysConfig sysConfig = getSysConfigFromDB(key);
                value = sysConfig.getConfigValue();
            }
            return value;
        } catch (Exception e) {
            log.error("", e);
            return null;
        }

    }

    /**
     * 从数据库获取系统参数值（不推荐使用）
     *
     * @param key
     * @return
     */
    @Override
    public SysConfig getSysConfigFromDB(String key) {
        SysConfig cfg = getSysConfigByKey(key);
        if (cfg != null) {
            JSONObject valueObj = new JSONObject();
            valueObj.put("configValue", cfg.getConfigValue());
            valueObj.put("isOpen", cfg.getIsOpen());
            this.setSysConfigByKeyToRedis(key, valueObj.toJSONString());
        }
        return cfg;
    }

    @Override
    public void initCofig() {
        List<SysConfig> list = getList();
        if (StringUtils.isNotEmpty(list)) {
            for (SysConfig sysConfig : list) {
                JSONObject valueObj = new JSONObject();
                valueObj.put("configValue", sysConfig.getConfigValue());
                valueObj.put("isOpen", sysConfig.getIsOpen());
                setSysConfigByKeyToRedis(sysConfig.getConfigKey(), valueObj.toJSONString());
            }
        }
    }

    /**
     * 通过key值获取数据
     */
    @Override
    public SysConfig getSysConfigByKey(String key) {
        try {
            Where where = Where.create();
            where.eq(SysConfig::getConfigKey, key);
            return entityService.queryObjectDisableTenant(SysConfig.class, where);
        } catch (Exception e) {
            log.error("系统参数查询出错", e);
            return null;
        }
    }

    /**
     * 通过redis值获取数据
     */
    private String getSysConfigByKeyFromRedis(String key) {
        try {
            String rst = null;
            String mapValue = redis.getMapValue(configKey, key);
            if (StringUtils.isNotEmpty(mapValue)) {
                JSONObject valueObj = JSONObject.parseObject(mapValue);
                rst = valueObj.getString("configValue");
            }
            return rst;
        } catch (Exception e) {
            log.error("redis查询出错,Key:" + key, e);
        }
        return null;
    }

    /**
     * redis存储数据
     */
    private void setSysConfigByKeyToRedis(String key, String value) {
        try {
            if (value == null) {
                redis.hDelete(configKey, key);
            } else {
                redis.setMapValue(configKey, key, value);
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }

    private void clearSysConfigByKeyToRedis() {
        try {
            redis.delete(configKey);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    /**
     * 查询系统配置
     *
     * @param id 系统配置ID
     * @return 系统配置
     */
    @Override
    public SysConfig selectSysConfigById(String id) {
        Where where = Where.create();
        where.eq(SysConfig::getId, id);
        return entityService.queryObjectDisableTenant(SysConfig.class, where);
    }

    /**
     * 查询列表
     *
     * @return
     */
    private List<SysConfig> getList() {
        Order order = Order.create();
        order.orderByAsc(SysConfig::getModuleCode);
        order.orderByAsc(SysConfig::getTmSort);
        return entityService.queryListDisableTenant(SysConfig.class, order);
    }

    /**
     * 查询系统配置列表
     *
     * @param sysConfig 系统配置
     * @return 系统配置
     */
    @Override
    public Res<List<SysConfig>> selectSysConfigList(SysConfig sysConfig, Pagination<?> page) {
        Where where = Where.create();
        try {
            Object config_name_value = EntityUtils.getValue("config_name", sysConfig);
            if (ObjUtils.notEmpty(config_name_value)) {
                // where.and("config_name like ?", "%" + config_name_value + "%");
                where.like(SysConfig::getConfigName, config_name_value);
            }
            Object config_key_value = EntityUtils.getValue("config_key", sysConfig);
            if (ObjUtils.notEmpty(config_key_value)) {
                // where.and("config_key like ?", "%" + config_key_value + "%");
                where.like(SysConfig::getConfigKey, config_key_value);
            }
            /*
             * Object config_type_value = EntityUtils.getValue("config_type", sysConfig); if
             * (ObjUtils.notEmpty(config_type_value)) { where.and("config_type = ?",
             * config_type_value); }
             */
            Object moduleCode = EntityUtils.getValue("moduleCode", sysConfig);
            if (ObjUtils.notEmpty(moduleCode)) {
                where.eq(SysConfig::getModuleCode, moduleCode);
            }
            Order order = Order.create();
            order.orderByAsc(SysConfig::getModuleCode);
            order.orderByAsc(SysConfig::getTmSort);
            // 读取总记录数量
            Res<List<SysConfig>> res = new Res<>();
            res.setTotal(entityService.queryCountDisableTenant(SysConfig.class, where));
            // 读取记录结果
            res.setResult(entityService.queryListDisableTenant(SysConfig.class, where, order, page));
            return res;
        } catch (IllegalArgumentException | IllegalAccessException e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 新增系统配置
     *
     * @param sysConfig 系统配置
     * @return 结果
     */
    @Override
    public int insertSysConfig(SysConfig sysConfig) {

        Where where = Where.create();
        where.eq(SysConfig::getConfigKey, sysConfig.getConfigKey());
        List<SysConfig> list = entityService.queryDataDisableTenant(SysConfig.class, where, null, null);
        if (StringUtils.isNotEmpty(list)) {
            throw new RuntimeException("参数key值" + "【" + sysConfig.getConfigKey() + "】已存在");
        }
        sysConfig.setId(TMUID.getUID());
        Long c = entityService.queryCountDisableTenant(SysConfig.class, Where.create());
        sysConfig.setTmSort(c.intValue() + 1);
        int i = entityService.rawInsertDisableTenant(sysConfig);
        if (i > 0) {
            JSONObject valueObj = new JSONObject();
            valueObj.put("configValue", sysConfig.getConfigValue());
            valueObj.put("isOpen", sysConfig.getIsOpen());
            this.setSysConfigByKeyToRedis(sysConfig.getConfigKey(), valueObj.toJSONString());
        }
        return i;
    }

    /**
     * 修改系统配置
     *
     * @param sysConfig 系统配置
     * @return 结果
     */
    @Override
    public int updateSysConfig(SysConfig sysConfig) {
        int i = entityService.rawUpdateByIdDisableTenant(sysConfig);
        if (i > 0) {
            JSONObject valueObj = new JSONObject();
            valueObj.put("configValue", sysConfig.getConfigValue());
            valueObj.put("isOpen", sysConfig.getIsOpen());
            this.setSysConfigByKeyToRedis(sysConfig.getConfigKey(), valueObj.toJSONString());
        }
        return i;
    }

    /**
     * 批量删除系统配置
     *
     * @param ids 需要删除的系统配置ID
     * @return 结果
     */
    @Override
    public int deleteSysConfigByIds(String[] ids) {
        if (ids.length == 1) {
            return deleteSysConfigById(ids[0]);
        } else {
            Where where = Where.create();
            where.in(SysConfig::getId, ids);
            int i = entityService.deleteDisableTenant(SysConfig.class, where);
            if (i > 0) {
                clearSysConfigByKeyToRedis();
            }
            return i;
        }
    }

    /**
     * 删除系统配置信息
     *
     * @param id 系统配置ID
     * @return 结果
     */
    @Override
    public int deleteSysConfigById(String id) {
        int i = 0;
        SysConfig bean = selectSysConfigById(id);
        if (bean != null) {
            i = entityService.rawDeleteByIdDisableTenant(bean);
            if (i > 0) {
                this.setSysConfigByKeyToRedis(bean.getConfigKey(), null);
            }
        }
        return i;
    }

    /**
     * 根据key修改系统参数值
     * @param paramObj
     * @return
     */
    @Override
    public int setSysConfigValueByKey(SysConfig paramObj) {
        SysConfig sysConfig = this.getSysConfigByKey(paramObj.getConfigKey());
        if (sysConfig == null) {
            return 0;
        }
        sysConfig.setConfigValue(paramObj.getConfigValue());
        return updateSysConfig(sysConfig);
    }

    /**
     * 获得系统皮肤
     *
     * @return
     */
    @Override
    public String getTM4SysThemesCss() {
        if (StringUtils.isNotEmpty(this.TM4SysThemesCss)) {
            if (this.TM4SysThemesCss.startsWith("\"") && this.TM4SysThemesCss.endsWith("\"")) {
                this.TM4SysThemesCss = this.TM4SysThemesCss.substring(1, this.TM4SysThemesCss.length() - 1);
            }
            return this.TM4SysThemesCss;
        } else {
            return "";
        }
    }

    /**
     * 获得对外开放的系统参数
     * @param key
     * @return
     */
    @Override
    public String getOpenSystemConfig(String key) {
        String configValue = null;
        Integer isOpen = null;

        String mapValue = redis.getMapValue(configKey, key);
        if (StringUtils.isNotEmpty(mapValue)) {
            JSONObject valueObj = JSONObject.parseObject(mapValue);
            configValue = valueObj.getString("configValue");
            isOpen = valueObj.getInteger("isOpen");
        } else {
            SysConfig sysConfig = getSysConfigFromDB(key);
            if (sysConfig != null) {
                configValue = sysConfig.getConfigValue();
                isOpen = sysConfig.getIsOpen();
            }
        }

        if (isOpen == 1) { //是对外开放参数
            return configValue;
        } else { //未对外开放
            log.warn("[系统参数] key：{}，未开放对外访问", key);
            return null;
        }
    }
}
