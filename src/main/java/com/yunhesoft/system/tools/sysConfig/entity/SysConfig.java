package com.yunhesoft.system.tools.sysConfig.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 系统配置对象 SYS_CONFIG
 * 
 * <AUTHOR>
 * @date 2021-03-31
 */
@ApiModel(value = "系统配置对象")
@Getter
@Setter
@Entity
@Table(name = "SYS_CONFIG")
public class SysConfig extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/** 参数名称 */
	@ApiModelProperty(value = "模块编码")
	@Column(name = "MODULECODE", length = 100)
	private String moduleCode;

	/*
	 * @ApiModelProperty(value = "模块名称")
	 * 
	 * @Column(name = "MODULENAME", length=100) private String moduleName;
	 */

	/** 参数名称 */
	@ApiModelProperty(value = "参数名称")
	@Column(name = "CONFIG_NAME", length = 255)
	private String configName;
	/** 参数键名 */
	@ApiModelProperty(value = "参数键名")
	@Column(name = "CONFIG_KEY", length = 255)
	private String configKey;
	/** 参数键值 */
	@ApiModelProperty(value = "参数键值")
	@Column(name = "CONFIG_VALUE", length = 4000)
	private String configValue;

	/** 排序 */
	@ApiModelProperty(value = "排序字段")
	@Column(name = "TMSORT")
	private Integer tmSort;

	/** 参数类型 */
	@ApiModelProperty(value = "参数类型")
	@Column(name = "CONFIG_TYPE", length = 255)
	private String configType;

	/** 备注说明 */
	@ApiModelProperty(value = "备注说明")
	@Column(name = "REMARK", length = 255)
	private String remark;

	@ApiModelProperty(value = "是否为对外开放参数")
	@Column(name = "IS_OPEN")
	private Integer isOpen;

}
