package com.yunhesoft.system.tools.diy.entity.po;


import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Description: 分析图分类
 * <AUTHOR>
 * @date 2023/2/6
 */
@ApiModel(value = "分析图分类实体")
@Data
@Entity
@Table(name = "DIY_PAGE_ANALYSIS_DRAW_TYPE")
public class DiyPageAnalysisDrawType extends BaseEntity {
	
	@ApiModelProperty(value = "名称")
    @Column(name = "NAME", length = 255)
    private String name;
	
	@ApiModelProperty(value = "类型id")
    @Column(name = "CLASSID", length = 255)
    private String classId;
	
    @ApiModelProperty(value = "类型名称")
    @Column(name = "CLASSNAME", length = 255)
    private String className;
    
    @ApiModelProperty(value = "示例id")
    @Column(name = "slId", length = 255)
    private String slId;
    
    /** 排序 */
	@Column(name = "TMSORT")
	private Integer tmsort;

	/** 是否启用 1：使用 0：禁用*/
	@Column(name = "TMUSED")
	private Integer tmused;
}
