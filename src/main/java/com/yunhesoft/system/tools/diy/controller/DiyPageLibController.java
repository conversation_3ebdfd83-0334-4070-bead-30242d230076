package com.yunhesoft.system.tools.diy.controller;


import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.org.entity.vo.SysOrgTreeData;
import com.yunhesoft.system.tds.entity.dto.TdsExportDto;
import com.yunhesoft.system.tds.entity.vo.TdsInParaRelTreeVo;
import com.yunhesoft.system.tools.diy.entity.dto.*;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageLibInfo;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibInfoVo;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibInparaVo;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibVo;
import com.yunhesoft.system.tools.diy.service.IDiyPageLibInfoService;
import com.yunhesoft.system.tools.diy.service.IDiyPageLibInparaService;
import com.yunhesoft.system.tools.diy.service.IDiyPageLibService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/system/tools/diy/pagelib")
@Api(tags = "自定义页面配置")
public class DiyPageLibController extends BaseRestController {
    @Autowired
    private IDiyPageLibInfoService diyPageLibSetService;
    @Autowired
    private IDiyPageLibService diyPageLibService;
    @Autowired
    private IDiyPageLibInparaService diyPageLibInparaService;


    @ApiOperation(value = "通过页面id获取自定义页面配置数据", notes = "通过页面id获取自定义页面配置数据")
    @RequestMapping(value = "/getPageLibInfoByPageId", method = {RequestMethod.GET})
    public Res<List<DiyPageLibInfo>> getPageLibInfoByPageId(@RequestParam("pageId") String pageId,
                                                            @RequestParam("type") String type) {
        List<DiyPageLibInfoVo> pageLibInfoByPageIdIncludeRouter = diyPageLibSetService.getPageLibInfoByPageIdIncludeRouter(pageId, type);
        return Res.OK(pageLibInfoByPageIdIncludeRouter);
    }


    @ApiOperation(value = "获取自定义页面", notes = "获取自定义页面")
    @RequestMapping(value = "/getPage", method = {RequestMethod.POST})
    public Res<List<DiyPageLibVo>> getPage(@RequestBody PageParamDto paramDto) {
        Res<List<DiyPageLibVo>> res = new Res<>();
        Pagination<?> page = null;
        //创建分页
        if (paramDto.getPageSize() != null && paramDto.getPageSize() > 0) {
            page = Pagination.create(paramDto.getPageNum() == null ? 1 : paramDto.getPageNum(), paramDto.getPageSize());
        }
        List<DiyPageLibVo> listVo = diyPageLibService.getPage(paramDto, page);
        res.setResult(listVo);
        res.setSuccess(true);
        if (ObjUtils.notEmpty(page) && page.getSize() > 0) {
            res.setTotal(page.getTotal());
        }
        return res;
    }

    @ApiOperation(value = "保存自定义页面", notes = "保存自定义页面")
    @RequestMapping(value = "/savePage", method = {RequestMethod.POST})
    public Res<?> savePage(@RequestBody SavePageDto paramDto) {
        return Res.OK(diyPageLibService.savePages(paramDto));
    }


    @ApiOperation(value = "保存自定义页面配置", notes = "保存自定义页面配置")
    @RequestMapping(value = "/savePageInfo", method = {RequestMethod.POST})
    public Res<?> savePage(@RequestBody SavePageInfoDto paramDto) {
        //更新输入参数
        List<DiyPageLibInfoVo> data = paramDto.getData();
        if(StringUtils.isNotEmpty(data)){
            List<String> tdsAlias = data.stream().map(i -> i.getTdsAlias()).distinct().collect(Collectors.toList());
            String tdsAliasStr = StringUtils.join(tdsAlias,",");
            Boolean aBoolean = diyPageLibInparaService.updateDiyPageLibInpara(data.get(0).getPageId(), tdsAliasStr);
        }
        return Res.OK(diyPageLibSetService.savePageInfo(paramDto));
    }
    @ApiOperation(value = "同步输入参数", notes = "保存自定义页面配置")
    @RequestMapping(value = "/syncInpara", method = {RequestMethod.POST})
    public Res<?> syncInpara(@RequestParam("pageId") String pageId,
                             @RequestParam("type") String type) {
        //更新输入参数
        return Res.OK(diyPageLibInparaService.syncInpara(pageId,type));
    }
    @ApiOperation(value = "通过页面id获取自定义页面输入参数", notes = "通过页面id获取自定义页面输入参数")
    @RequestMapping(value = "/getPageLibInpara", method = {RequestMethod.GET})
    public Res<List<DiyPageLibInparaVo>> getPageLibInpara(@RequestParam("pageId") String pageId,
                                                          @RequestParam("type") String type) {
        return Res.OK(diyPageLibInparaService.getPageLibInpara(pageId,type));
    }

    @ApiOperation(value = "保存自定义页面输入参数", notes = "保存自定义页面输入参数")
    @RequestMapping(value = "/savePageLibInpara", method = {RequestMethod.POST})
    public Res<?> savePageLibInpara(@RequestBody SavePageLibInparaDto paramDto) {
        return Res.OK(diyPageLibInparaService.savePageLibInpara(paramDto));
    }

    @ApiOperation(value = "复制自定义页面输入参数", notes = "复制自定义页面输入参数")
    @RequestMapping(value = "/copyPageLibInpara", method = {RequestMethod.POST})
    public Res<?> savePageLibInpara(@RequestParam("alias") String alias, @RequestParam("pageId") String pageId) {
        return Res.OK(diyPageLibInparaService.copyDataSourceInpara(alias, pageId));
    }


    @ApiOperation(value = "复制页面", notes = "复制页面")
    @RequestMapping(value = "/operTemplate", method = {RequestMethod.POST})
    public void operTemplate(@RequestBody TemplateParamDto param) {
        diyPageLibService.operTemplate(param);
    }

    @ApiOperation(value = "指定机构继承页面", notes = "复制页面")
    @RequestMapping(value = "/extendsPageById", method = {RequestMethod.POST})
    public Res<?> extendsPageById(@RequestParam("srcOrgId") String srcOrgId, @RequestParam("targetOrgId") String targetOrgId, @RequestParam("type") String type) {
        return Res.OK(diyPageLibService.extendsPageById(srcOrgId, targetOrgId, type,1));
    }

    @ApiOperation(value = "继承父机构页面", notes = "复制页面")
    @RequestMapping(value = "/extendsPageBySuper", method = {RequestMethod.POST})
    public Res<?> extendsPageBySuper(@RequestBody SysOrgTreeData param, @RequestParam("type") String type) {
        return Res.OK(diyPageLibService.extendsPageBySuper(param, type,2));
    }

    @ApiOperation(value = "按照类型获取模板", notes = "复制页面")
    @RequestMapping(value = "/getTemPlateByType", method = {RequestMethod.POST})
    public Res<?> getTemPlateByType(@RequestParam("type") String type) {
        return Res.OK(diyPageLibService.getTemPlateByType(type));
    }

    @ApiOperation(value = "通过机构id获取显示方式", notes = "复制页面")
    @RequestMapping(value = "/getShowTypeByOrg", method = {RequestMethod.POST})
    public Res<?> getShowTypeByOrg(@RequestParam("objId")String orgId,@RequestParam("type") String type) {
        return Res.OK(diyPageLibService.getShowTypeByOrg(orgId,type));
    }

    @ApiOperation(value = "通过机构id获取来源方式", notes = "复制页面")
    @RequestMapping(value = "/getOrignType", method = {RequestMethod.POST})
    public Res<?> getOrignType(@RequestParam("objId")String orgId,@RequestParam("type") String type) {
        return Res.OK(diyPageLibService.getOrignType(orgId,type));
    }

    @ApiOperation(value = "通过机构id获取继承方式", notes = "复制页面")
    @RequestMapping(value = "/getExtendsByOrg", method = {RequestMethod.POST})
    public Res<?> getExtendsByOrg(@RequestParam("objId")String orgId,@RequestParam("type") String type) {
        return Res.OK(diyPageLibService.getExtendsByOrg(orgId,type));
    }

    /**
     * 删除引用
     * 设为不继承  和  更改继承方式的时候需要调用
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @ApiOperation(value = "不继承", notes = "复制页面")
    @RequestMapping(value = "/deleteRelationByReq", method = {RequestMethod.POST})
    public Res<?> deleteRelationByReq(@RequestParam("reqId")String reqId) {
        return Res.OK(diyPageLibService.deleteRelationByReq(reqId));
    }

    /**
     * 获取当前用户  进行自定义页面设置
     * 设为不继承  和  更改继承方式的时候需要调用
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @ApiOperation(value = "获取当前用户", notes = "获取当前用户")
    @RequestMapping(value = "/getCurrentUser", method = {RequestMethod.POST})
    public Res<?> getCurrentUser() {
        SysUser currentUser = SysUserUtil.getCurrentUser();
        JSONObject obj = new JSONObject();
        obj.put("id",currentUser.getId());
        obj.put("name",currentUser.getUserName());
        return Res.OK(obj);
    }

    /**
     * 根据当前登录用户获取 机构首页 岗位首页 个人首页
     * <AUTHOR>
     * @return
     * @params
    */
    @ApiOperation(value = "根据当前登录用户获取", notes = "根据当前登录用户获取")
    @RequestMapping(value = "/getCurrentUserPages", method = {RequestMethod.POST})
    public Res<?> getCurrentUserPages() {
        return Res.OK(diyPageLibService.getCurrentUserPages());
    }

    /**
     * 导出数据源
     * <AUTHOR>
     * @return
     * @params
     */
    @ApiOperation(value = "导出数据源", notes = "导出数据源")
    @RequestMapping(value = "/exportTds", method = {RequestMethod.POST})
    public void exportTds(@RequestBody List<TdsExportDto> params) {
        diyPageLibService.exportTds(params);
    }

    /**
     * 自定义页面输入参数联动关系
     * <AUTHOR>
     * @return
     * @params
    */

    @ApiOperation(value = "获取数据源输入参数联动树数据")
    @RequestMapping(value = "/getDiyInParaRelTreeData", method = { RequestMethod.POST })
    public Res<?> getDiyInParaRelTreeData(@RequestParam("pageId") String pageId,@RequestParam("type") String type) {
        return Res.OK(diyPageLibInparaService.getDiyInParaRelTreeData(pageId,type));
    }

    /**
     * 保存自定义页面输入参数联动关系
     * <AUTHOR>
     * @return
     * @params
     */

    @ApiOperation(value = "获取数据源输入参数联动树数据")
    @RequestMapping(value = "/saveDiyInParaRelTreeData", method = { RequestMethod.POST })
    public Res<?> saveDiyInParaRelTreeData(@RequestParam("pageId") String pageId, @RequestParam("type") String type, @RequestBody TdsInParaRelTreeVo node) {
        return Res.OK(diyPageLibInparaService.saveDiyInParaRelTreeData(pageId,type,node));
    }
    @ApiOperation(value = "输入参数改变事件")
    @RequestMapping(value = "/queryChange", method = { RequestMethod.POST })
    public Res<?> queryChange(@RequestBody PageInparaQueryChangeDto changeDto) {
        return Res.OK(diyPageLibInparaService.queryChange(changeDto));
    }
}
