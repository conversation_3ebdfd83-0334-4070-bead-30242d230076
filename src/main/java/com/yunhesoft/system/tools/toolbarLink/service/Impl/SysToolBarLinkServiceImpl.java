package com.yunhesoft.system.tools.toolbarLink.service.Impl;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.toolbarLink.entity.dto.SaveToolBarLindDto;
import com.yunhesoft.system.tools.toolbarLink.entity.po.SysToolBarLink;
import com.yunhesoft.system.tools.toolbarLink.entity.vo.SysToolBarLinkVo;
import com.yunhesoft.system.tools.toolbarLink.service.ISysToolBarLinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 工具栏连接实现类$
 * @date 2022/3/17
 */
@Service
public class SysToolBarLinkServiceImpl implements ISysToolBarLinkService {

    @Autowired
    private EntityService dao;

    /**
     * 获得工具栏连接
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @Override
    public List<SysToolBarLinkVo> getToolBarLinks() {
        Where where = Where.create();
        where.eq(SysToolBarLink::getTmUsed, 1);
        Order order = Order.create();
        order.orderByAsc(SysToolBarLink::getTmSort);
        List<SysToolBarLink> list = dao.rawQueryListByWhere(SysToolBarLink.class, where, order);
        List<SysToolBarLinkVo> listVo = new ArrayList<>();
        if (StringUtils.isNotEmpty(list)) {
            for (SysToolBarLink link : list) {
                SysToolBarLinkVo bean = ObjUtils.copyTo(link, SysToolBarLinkVo.class);
                listVo.add(bean);
            }
        }
        return listVo;
    }

    /**
     * 保存工具栏链接
     * <AUTHOR>
     * @param
     * @return
     */
    @Override
    public Boolean saveToolBarLinks(SaveToolBarLindDto paramDto) {
        Boolean bln = false;
        if (ObjUtils.notEmpty(paramDto)) {
            if (StringUtils.isNotEmpty(paramDto.getData())) {
                List<SysToolBarLink> insertList = new ArrayList<>();
                List<SysToolBarLink> updateList = new ArrayList<>();
                List<SysToolBarLink> deleteList = new ArrayList<>();
                for (SysToolBarLinkVo linkVo : paramDto.getData()) {
                    Integer rowFlag = linkVo.getRowFlag();
                    SysToolBarLink bean = ObjUtils.copyTo(linkVo, SysToolBarLink.class);
                    if (rowFlag == null || rowFlag == 0) {
                        insertList.add(bean);
                    } else if (rowFlag == 1) {
                        updateList.add(bean);
                    } else {
                        deleteList.add(bean);
                    }
                }
                if (StringUtils.isNotEmpty(insertList)) {
                    bln = this.addToolBarLinks(insertList);
                }
                if (StringUtils.isNotEmpty(updateList)) {
                    bln = this.updateToolBarLinks(updateList);
                }
                if (StringUtils.isNotEmpty(deleteList)) {
                    bln = this.deleteToolBarLinks(deleteList);
                }
            }
        }
        return bln;
    }

    /**
     * 删除工具栏链接
     * <AUTHOR>
     * @param
     * @return
     */
    private Boolean deleteToolBarLinks(List<SysToolBarLink> deleteList) {
        int msg = 0;
        if (StringUtils.isNotEmpty(deleteList)) {
            for (SysToolBarLink link : deleteList) {
                link.setTmUsed(0);
            }
            msg = dao.updateByIdBatch(deleteList);
        }
        return msg > 0;
    }

    /**
     * 修改工具栏链接
     * <AUTHOR>
     * @param
     * @return
     */
    private Boolean updateToolBarLinks(List<SysToolBarLink> updateList) {
        int msg = 0;
        if (StringUtils.isNotEmpty(updateList)) {
            for (SysToolBarLink link : updateList) {
                link.setTmUsed(1);
            }
            msg = dao.updateByIdBatch(updateList);
        }
        return msg > 0;
    }

    /**
     * 添加工具栏链接
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private Boolean addToolBarLinks(List<SysToolBarLink> insertList) {
        int msg = 0;
        Where where = Where.create();
        Integer max = dao.findMaxValue(SysToolBarLink.class,SysToolBarLink::getTmSort,Integer.class,where);
        if(ObjUtils.isEmpty(max)){
            max = 0;
        }
        if (StringUtils.isNotEmpty(insertList)) {
            for (SysToolBarLink link : insertList) {
                link.setId(TMUID.getUID());
                link.setTmUsed(1);
                link.setTmSort(++max);
            }
            msg = dao.insertBatch(insertList);
        }
        return msg > 0;
    }
}
