package com.yunhesoft.system.tools.files.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.tools.files.entity.dto.FileManageQueryDto;
import com.yunhesoft.system.tools.files.entity.po.SysFileManage;
import com.yunhesoft.system.tools.files.service.ISysFileManage;
import com.yunhesoft.tmtools.TokenUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * 文件管理Controller
 */
@Log4j2
@RestController
@Api(tags = "文件管理")
@RequestMapping("/system/fileManage")
public class FileManageController extends BaseRestController {
    @Autowired
    private ISysFileManage srv;

    @ApiOperation(value = "查询数据列表")
    @RequestMapping(value = "/queryDataList", method = {RequestMethod.POST})
    public Res queryDataList(@RequestBody FileManageQueryDto dto) {
        Pagination<?> page = getRequestPagination();
        return Res.OK(srv.queryDataList(dto));
    }

    @ApiOperation(value = "保存文件夹")
    @RequestMapping(value = "/saveFolder", method = {RequestMethod.POST})
    public Res saveFolder(@RequestBody SysFileManage folder) {
        return Res.OK(srv.saveFolder(folder));
    }

    @ApiOperation(value = "批量删除")
    @RequestMapping(value = "/deleteDataByIdList", method = {RequestMethod.POST})
    public Res deleteDataByIdList(@RequestBody List<String> idList) {
        return Res.OK(srv.deleteDataByIdList(idList));
    }

    @ApiOperation(value = "彻底删除")
    @RequestMapping(value = "/realDeleteFileByIdList", method = {RequestMethod.POST})
    public Res realDeleteFileByIdList(@RequestBody List<String> idList) {
        return Res.OK(srv.realDeleteFileByIdList(idList));
    }

    @ApiOperation(value = "文件上传")
    @RequestMapping(value = "/fileUpload", method = {RequestMethod.POST})
    public Res<?> fileUpload(@RequestParam("pid") String pid, @RequestParam("dataClass") String dataClass, @RequestParam("moduleCode") String moduleCode, @RequestParam("file") MultipartFile file) {
        return Res.OK(srv.fileUpload(pid, dataClass, moduleCode, file));
    }

    @ApiOperation(value = "下载文件")
    @RequestMapping("/fileDownload")
    public void fileDownload(HttpServletResponse response, @RequestBody FileManageQueryDto dto) {
        srv.fileDownload(response, dto);

    }

    @ApiOperation(value = "批量还原")
    @RequestMapping(value = "/restoreFiles", method = {RequestMethod.POST})
    public Res restoreFiles(@RequestBody List<String> idList) {
        return Res.OK(srv.restoreFiles(idList));
    }

    @ApiOperation(value = "文件是否存在")
    @RequestMapping(value = "/fileExists", method = {RequestMethod.POST})
    public Res fileExists(@RequestBody FileManageQueryDto dto) {
        return Res.OK(srv.fileExists(dto));
    }

    @ApiOperation(value = "文件预览")
    @RequestMapping(value = "/getFileBase64", method = {RequestMethod.POST})
    public Res getFileBase64(@RequestBody SysFileManage file) {
        return Res.OK(srv.getFileBase64(file));
    }

    /**
     * go-fastfds 文件服务器验证接口
     *
     * @return
     */
    @ApiOperation(value = "验证token是否有效(go-fast)")
    @RequestMapping(value = "/checkAuthToken", method = {RequestMethod.POST, RequestMethod.GET})
    public void checkAuthToken(/*@RequestHeader("auth_token") String hearder_token*/) {
        String returnStr = "fail";
        try {
            String req_token = request.getParameter("auth_token");
            //log.info("go-fastfds 文件服务器验证接口，token：" + (token == null ? "" : token));
            String token = req_token;
            log.info("go-fastfds 文件服务器验证接口，req_token：" + (req_token == null ? "" : req_token));
            if (StringUtils.isNotEmpty(token)) {
                if (TokenUtils.checkToken(token)) {// 验证成功
                    returnStr = "ok";
                    log.info("go-fastfds验证成功：" + returnStr);
                } else {
                    log.error("go-fastfds token认证失败2，token：" + token);
                }
            } else {
                log.error("go-fastfds token认证失败1，token：" + (token == null ? "" : token));
            }
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write(returnStr);
            response.getWriter().flush();
            response.getWriter().close();
        } catch (Exception e) {
            log.error("", e);
        }
    }
}
