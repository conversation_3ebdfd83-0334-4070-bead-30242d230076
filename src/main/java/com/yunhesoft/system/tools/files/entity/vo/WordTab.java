package com.yunhesoft.system.tools.files.entity.vo;

import java.util.List;

import lombok.Data;

@Data
/**
 * <AUTHOR>
 * word表格对象
 */
public class WordTab {

	//类型 title是表头
	private String type;
	//表头标题
	private String title;
	//是否将表格分组标题拆分显示，默认false
	private Boolean isSplit;
	//行数
	private Integer rowNum;
	//列数
	private Integer colNum;
	//表格行对象列表
	private List<WordTabRow> rows;
	//表格合并对象列表
	private List<WordTabMerge> mergeList;
	//是否数据源
	private Boolean isDs;
	//数据源别名
	private String dsAlias;
	//是否图片
	private Boolean isPic;
	//相关数据
	private String info;
	//合并区域
	private List<WordTabMerge> merList;
}
