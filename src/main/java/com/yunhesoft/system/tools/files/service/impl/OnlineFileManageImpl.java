package com.yunhesoft.system.tools.files.service.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.aliyuncs.utils.Base64Helper;
import com.itextpdf.styledxmlparser.jsoup.Jsoup;
import com.itextpdf.styledxmlparser.jsoup.nodes.Document;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.impl.TreeServiceImpl;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Update;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.dto.SysOrgSelect;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.entity.vo.SysOrgTreeData;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.role.utils.IPermUtils;
import com.yunhesoft.system.tools.files.entity.dto.FileManageQueryDto;
import com.yunhesoft.system.tools.files.entity.po.SysFileManage;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;
import com.yunhesoft.system.tools.files.entity.po.SysOnlinefileAudit;
import com.yunhesoft.system.tools.files.entity.po.SysOnlinefileContent;
import com.yunhesoft.system.tools.files.entity.po.SysOnlinefileManager;
import com.yunhesoft.system.tools.files.entity.po.SysOnlinefileNode;
import com.yunhesoft.system.tools.files.entity.po.SysOnlinefileUpdLog;
import com.yunhesoft.system.tools.files.entity.po.SysOnlinefileVersion;
import com.yunhesoft.system.tools.files.entity.vo.FileManageVo;
import com.yunhesoft.system.tools.files.entity.vo.FileManegeDataClass;
import com.yunhesoft.system.tools.files.entity.vo.TreeVo;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;
import com.yunhesoft.system.tools.files.service.ISysFileOnlineManage;
import com.yunhesoft.system.tools.files.service.ISysFileService;
import com.yunhesoft.system.tools.files.tools.DiffString;
import com.yunhesoft.system.tools.files.tools.HtmlToPdfUtils;
import com.yunhesoft.system.tools.files.tools.ImageManagerImpl;

import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLConverter;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLOptions;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class OnlineFileManageImpl implements ISysFileOnlineManage {

    public static final String MODEL_ID = "ONLINEFILE_MANAGE";
    
    private String cjmark = "workshop";//机构车间标识
    private String zzmark = "equipment";//机构装置标识

    @Autowired
    private ISysFileService fs;

    @Autowired
    private IFilesInfoService fileInfoSrv;

    @Autowired
    private EntityService srv;

    @Autowired
    private TreeServiceImpl treeSrv;
    
    @Autowired
    private IPermUtils permUtils;// 权限服务
    
    @Autowired
    private ISysOrgService orgsrv;	//机构
    
    
    private int rowLimit = 2000;//行限制

    @Override
    public List<FileManageVo> queryDataList(FileManageQueryDto dto) {
        List<FileManageVo> result = new ArrayList<>();
        FileManegeDataClass dataClassEnum = dto.getDataClass();
        if (FileManegeDataClass.system.equals(dataClassEnum)) {
            //系统文件查询
            return this.querySystemFileList();
        }

        String moduleCode = dto.getModuleCode();
        String dataClass = dataClassEnum.toString();
        SysUser currentUser = SysUserHolder.getCurrentUser();
        String dataGroupId = null;
        if (FileManegeDataClass.personal.equals(dto.getDataClass())) {
            //查询个人
            dataGroupId = currentUser.getId();
        } else if (FileManegeDataClass.tenantpublic.equals(dto.getDataClass())) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        }

        if ("root".equals(dto.getPid())) {
            //查询层级1
            dto.setPid(getTenantRoot(moduleCode, dataClass, dataGroupId, dto.getLevel1()).getId());
        }

        //其余情况为文件管理中的查询
        Where where = Where.create();
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getTmused, dto.getTmused());
        where.eq(SysFileManage::getDataGroupId, dataGroupId);
        where.eq(SysFileManage::getDelFlag, dto.getDelflag());
        if (StringUtils.isNotEmpty(dto.getPid())) {
            //根据父id查
            where.eq(SysFileManage::getPid, dto.getPid());
        }
        if (dto.getFileType() > 0) {
            //指定类型
            where.eq(SysFileManage::getFileType, dto.getFileType());
        }
        if (StringUtils.isNotEmpty(dto.getName())) {
            //按名称查询
//            where.eq(SysFileManage::getName, dto.getName());
            where.like(SysFileManage::getName, dto.getName());
        }

        Order order = Order.create();
        order.orderByDesc(SysFileManage::getFileType);
        if ("time".equals(dto.getOrder()) && dto.getDesc() == 1) {
            order.orderByDesc(SysFileManage::getCreateTime);
        } else {
            if ("name".equals(dto.getOrder()) && dto.getDesc() == 0) {
                //名称正序
                order.orderByAsc(SysFileManage::getName);
            } else if ("name".equals(dto.getOrder()) && dto.getDesc() == 1) {
                //名称倒序
                order.orderByDesc(SysFileManage::getName);
            } else if ("size".equals(dto.getOrder()) && dto.getDesc() == 0) {
                //大小正序
                order.orderByAsc(SysFileManage::getFileSize);
            } else if ("size".equals(dto.getOrder()) && dto.getDesc() ==1) {
                //大小倒序
                order.orderByDesc(SysFileManage::getFileSize);
            }
            //默认创建时间正序
            order.orderByAsc(SysFileManage::getCreateTime);
        }

        List<SysFileManage> list = srv.queryData(SysFileManage.class, where, order, null);
        if (StringUtils.isNotEmpty(list)) {
            List<String> urlIdList = new ArrayList<>();
            for (SysFileManage file : list) {
                FileManageVo fileManageVo = ObjUtils.copyTo(file, FileManageVo.class);
                result.add(fileManageVo);
                if (StringUtils.isNotEmpty(dto.getName()) && StringUtils.isEmpty(dto.getPid())) {
                    //全局按名称查
//                    urlIdList.add(file.get)
                }
            }
        }
        return result;
    }

    /**
     * 查询根节点
     * @param moduleCode
     * @param dataClass
     * @param dataGroupId
     * @return
     */
    private SysFileManage getTenantRoot (String moduleCode, String dataClass, String dataGroupId, String level1) {
    	
    	SysFileManage obj = null;
    	
        Where rootWhere = Where.create();
        rootWhere.eq(SysFileManage::getTmused, 1);
        rootWhere.eq(SysFileManage::getModelId, MODEL_ID);
        rootWhere.eq(SysFileManage::getPid, "root");
        SysFileManage root = srv.rawQueryObjectByWhere(SysFileManage.class, rootWhere);
//        List<SysFileManage> insertList = new ArrayList<>();
        if (root == null) {
            root = new SysFileManage();
            root.setId(TMUID.getUID());
            root.setPid("root");
            root.setModelId(MODEL_ID);
//            root.setModuleCode(moduleCode);
//            root.setDataClass(dataClass);
//            root.setDataGroupId(dataGroupId);
            root.setDelFlag(0);
            root.setTmused(1);
//            insertList.add(root);
            srv.insert(root);
        }

        Where where = Where.create();
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getModelId, MODEL_ID);
        where.eq(SysFileManage::getPid, root.getId());
        where.eq(SysFileManage::getModuleCode, moduleCode);
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getDataGroupId, dataGroupId);
        SysFileManage tenantroot = srv.rawQueryObjectByWhere(SysFileManage.class, where);
        if (tenantroot == null) {
            tenantroot = new SysFileManage();
            tenantroot.setId(TMUID.getUID());
            tenantroot.setPid(root.getId());
            tenantroot.setModelId(MODEL_ID);
            tenantroot.setModuleCode(moduleCode);
            tenantroot.setDataClass(dataClass);
            tenantroot.setDataGroupId(dataGroupId);
            tenantroot.setDelFlag(0);
            tenantroot.setTmused(1);
//            insertList.add(tenantroot);
            treeSrv.append(SysFileManage.class, MODEL_ID, tenantroot.getPid(), tenantroot);
//            srv.insert(tenantroot);
        }
        obj = tenantroot;
        
        //判断第一层级分是否指定
        if(StringUtils.isNotEmpty(level1)) {
        	SysFileManage sf = srv.queryObjectById(SysFileManage.class, level1);//记录是否存在
        	 Where where1 = Where.create();
        	 where1.eq(SysFileManage::getId, level1);//用标识做ID
             where1.eq(SysFileManage::getTmused, 1);
             where1.eq(SysFileManage::getFileType, 2);
             where1.eq(SysFileManage::getModelId, MODEL_ID);
             where1.eq(SysFileManage::getPid, tenantroot.getId());
             where1.eq(SysFileManage::getModuleCode, moduleCode);
             where1.eq(SysFileManage::getDataClass, dataClass);
             where1.eq(SysFileManage::getDataGroupId, dataGroupId);
             SysFileManage tenantlevel1 = srv.rawQueryObjectByWhere(SysFileManage.class, where1);
             if (tenantlevel1 == null && sf == null) {
            	 tenantlevel1 = new SysFileManage();
            	 tenantlevel1.setId(level1);
            	 tenantlevel1.setName(level1);
            	 tenantlevel1.setFileType(2);
            	 tenantlevel1.setPid(tenantroot.getId());
                 tenantlevel1.setModelId(MODEL_ID);
                 tenantlevel1.setModuleCode(moduleCode);
                 tenantlevel1.setDataClass(dataClass);
                 tenantlevel1.setDataGroupId(dataGroupId);
                 tenantlevel1.setDelFlag(0);
                 tenantlevel1.setTmused(1);
                 treeSrv.append(SysFileManage.class, MODEL_ID, tenantlevel1.getPid(), tenantlevel1);
//                 srv.insert(tenantroot);
                 obj = tenantlevel1;
             }else if(tenantlevel1 == null && sf != null){
            	 //不考虑其他条件
            	 sf.setTmused(1);
            	 sf.setDelFlag(0);
            	 srv.update(sf);
            	 obj = sf;
             }else {
            	 obj = tenantlevel1;
             }
        }

//        if (StringUtils.isNotEmpty(insertList)) {
//            srv.insertBatch(insertList);
//        }
        return obj;
    }

    /**
     * 查询系统内文件
     * @return
     */
    private List<FileManageVo> querySystemFileList() {
        List<SysFilesInfo> list = srv.queryData(SysFilesInfo.class, Where.create(), Order.create().orderByAsc(SysFilesInfo::getCreateTime), null);
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        List<FileManageVo> result = new ArrayList<>();
        for (SysFilesInfo fileInfo : list) {
            FileManageVo file = new FileManageVo();
            file.setFileType(1);
            file.setId(fileInfo.getId());
            file.setName(fileInfo.getNewFileName() + (StringUtils.isEmpty(fileInfo.getFileExt()) ? "" : "."+fileInfo.getFileExt()));
            file.setFileExt(fileInfo.getFileExt());
            file.setFileUrl(fileInfo.getFileUrl());
            file.setFileDiskAddress(fileInfo.getFileAddress());
            file.setCreateTime(fileInfo.getCreateTime());
            file.setCreateBy(fileInfo.getCreateBy());
            result.add(file);
        }
        return result;
    }

    @Override
    public SysFileManage saveFolder(SysFileManage folder) {
    	Date nd = DateTimeUtils.getNowDate();
        String moduleCode = folder.getModuleCode();
        String dataClass = folder.getDataClass();
        SysUser currentUser = SysUserHolder.getCurrentUser();
        String dataGroupId = null;
        if (FileManegeDataClass.personal.toString().equals(dataClass)) {
            //查询个人
            dataGroupId = currentUser.getId();
        } else if (FileManegeDataClass.tenantpublic.toString().equals(dataClass)) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        } else if (FileManegeDataClass.tpublic.toString().equals(dataClass)) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        }
        String id = folder.getId();
        if (StringUtils.isEmpty(folder.getPid())) {
            folder.setPid("root");
        }
        if ("root".equals(folder.getPid())) {
            folder.setPid(getTenantRoot(moduleCode, dataClass, dataGroupId, folder.getBasedOn()).getId());
        }
        if(new Integer(1).equals(folder.getFileMode()) || new Integer(2).equals(folder.getFileType())) {//存档文档保存时直接更新人和时间
        	folder.setUpdateUserId(currentUser.getId());
        	folder.setUpdateUserName(currentUser.getRealName());
        	folder.setLastUpdateTime(nd);
        }
        
        if (StringUtils.isEmpty(id)) {
            //新增
            folder.setId(TMUID.getUID());
            folder.setTmused(1);
            folder.setModelId(MODEL_ID);
            folder.setModuleCode(moduleCode);
            folder.setDelFlag(0);
            folder.setDataGroupId(dataGroupId);
//            folder.setFileUrl(folderUrl);
            folder.setCreateUserName(currentUser.getRealName());
//            srv.insert(folder);
            treeSrv.append(SysFileManage.class, MODEL_ID, folder.getPid(), folder);
//            treeSrv.append(SysFileManage.class, folder.getDataGroupId(), null, folder);
//            treeSrv.getRoot(SysFileManage.class, "FILE_MANAGE");
//            treeSrv.getNode(SysFileManage.class, "FILE_MANAGE", "test1");
//            try {
//                treeSrv.correctTree(SysFileManage.class, "FILE_MANAGE");
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//            treeSrv.append(SysFileManage.class, folder, folder);

        } else {
            //修改
//            srv.rawUpdateByIdDisableTenant(folder);
            treeSrv.updateNode(SysFileManage.class, MODEL_ID, folder);
        }

        return folder;
    }

    private String getFileRootUrl (String dataClass) {
        SysUser currentUser = SysUserHolder.getCurrentUser();
        if (FileManegeDataClass.personal.toString().equals(dataClass)) {
            return "/filemanage/personal/user_"+currentUser.getId().toLowerCase();

        } else if (FileManegeDataClass.tenantpublic.toString().equals(dataClass) || FileManegeDataClass.tpublic.toString().equals(dataClass)) {
            return  "/filemanage/tenantpublic/tenant_"+currentUser.getTenant_id().toLowerCase();
        }
        return null;
    }

    @Override
    public String deleteDataByIdList(List<String> idList) {
        List<SysFileManage> dataList = srv.queryData(SysFileManage.class, Where.create().in(SysFileManage::getId, idList.toArray()), null, null);
        if (StringUtils.isEmpty(dataList)) {
            throw new RuntimeException("传入参数有误");
        }
        
        //检测文件夹或文件是否存在在线审核中或存档文件，如果有，不予删除并返回提示
        StringBuffer sb = new StringBuffer();
        List<SysFileManage> folderIds = new ArrayList<SysFileManage>();
        for (SysFileManage obj : dataList) {
			if(new Integer(2).equals(obj.getFileType())) {
				folderIds.add(obj);
			}else if(new Integer(2).equals(obj.getFileMode())){//在线文档
				if(obj.getArchiveTime()==null && obj.getFileStatus()<2) {
					//pass
				}else {
					sb.append(","+obj.getName());
				}
			}
		}
        
        String fs = judgeFloder(folderIds);
        String s = "";
    	if(fs!=null) {
    		s+=fs;
    	}
        if(sb.length() > 0) {
        	if(s.length() > 0) {
        		s = "所选文件夹及项目中有审核中或已存档文件";
        	}else {
        		s = "所选项目中有审核中或已存档文件，文件如下："+sb.toString().substring(1);
        	}
        }
        if(s.length() > 0) {
        	return s;
        }
        
        
        dataList.forEach(item -> item.setTmused(0));
        srv.updateByIdBatch(dataList);
        for (String id : idList) {
            treeSrv.delete(SysFileManage.class, true, MODEL_ID, id);
        }

        /*
        Where where = Where.create();
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getDataClass, dataList.get(0).getDataClass());
        where.eq(SysFileManage::getDataGroupId, dataList.get(0).getDataGroupId());
        List<SysFileManage> allList = srv.queryData(SysFileManage.class, where, null, null);
        if (StringUtils.isEmpty(allList)) {
            throw new RuntimeException("找不到记录");
        }
        Map<String, List<SysFileManage>> group = allList.stream().collect(Collectors.groupingBy(SysFileManage::getPid));
        List<SysFileManage> result = new ArrayList<>();
        for (SysFileManage file : dataList) {
//            deleteDataByChildrenGroup(result, file, group);
            treeSrv.delete(SysFileManage.class, true, MODEL_ID, file.getId());
        }
        */
//        srv.updateBatch(result);
//        srv.rawUpdate(SysFileManage.class, Update.create(SysFileManage::getTmused, 0), Where.create().in(SysFileManage::getId, idList.toArray()));
        return null;
    }

    private String judgeFloder(List<SysFileManage> folderIds) {
		if(StringUtils.isNotEmpty(folderIds)) {
			SysUser currentUser = SysUserHolder.getCurrentUser();
			
			Where wheref = Where.create();
			wheref.eq(SysFileManage::getTmused, 1);
			wheref.eq(SysFileManage::getFileType, 2);
			wheref.eq(SysFileManage::getModelId, this.MODEL_ID);
			List<SysFileManage> list = srv.queryData(SysFileManage.class, wheref, null, null);
			
			Map<String, List<SysFileManage>> map = new HashMap<String, List<SysFileManage>>();
			for (SysFileManage node : list) {
				if(map.containsKey(node.getPid())) {
					map.get(node.getPid()).add(node);
				}else {
					List<SysFileManage> tlist = new ArrayList<SysFileManage>();
					tlist.add(node);
					map.put(node.getPid(), tlist);
				}
			}
//			String pid = getTenantRoot("onlineFileManage", "tpublic", currentUser.getTenant_id(), null).getId();
//			List<List<String>> nodeList = new ArrayList<List<String>>();
//			getFilePathNode(map, nodeList, pid, pid);
			
			List<String> nodeList = new ArrayList<String>();
			for (SysFileManage obj : folderIds) {
				getNodePath(map, obj.getId(), nodeList);
			}
			
			Where where = Where.create();
			where.eq(SysFileManage::getTmused, 1);
			where.eq(SysFileManage::getFileType, 1);
			where.eq(SysFileManage::getFileMode, 2);
//			where.ne(SysFileManage::getPid, pid);
			if(nodeList.size() > 0) {
				where.in(SysFileManage::getPid, nodeList.toArray());
			}
			where.and().lb();
			where.lb();
			where.isNull(SysFileManage::getArchiveTime);
			where.gt(SysFileManage::getFileStatus, 1);
			where.rb();
			where.or().notNull(SysFileManage::getArchiveTime);
			where.rb();
			
//			Boolean a = false;
//			where.and();
//			if(folderIds.size() > 1) {
//				where.lb();
//			}
//			for (SysFileManage obj : folderIds) {
//				if(a) {
//					where.and();
//				}
//				where.lb();
//				where.gt(SysFileManage::getLft, obj.getLft());
//				where.lt(SysFileManage::getRgt, obj.getRgt());
//				where.rb();
//				a = true;
//			}
//			if(folderIds.size() > 1) {
//				where.rb();
//			}
			//
			List<SysFileManage> dataList = srv.queryData(SysFileManage.class, where, null, null);
			if(dataList.size() > 0) {
//				StringBuffer sb = new StringBuffer();
//				for (SysFileManage obj : dataList) {
//					
//				}
				
				return "所选文件夹中有审核中或已存档文件";
			}
		}
		return null;
	}
    private void getNodePath(Map<String, List<SysFileManage>> map, String pid, List<String> nodeList) {
    	if(!nodeList.contains(pid)) {
    		nodeList.add(pid);
    	}
    	List<SysFileManage> list = map.get(pid);
    	if(StringUtils.isNotEmpty(list)) {
			for (SysFileManage item : list) {
				getNodePath(map, item.getId(), nodeList);
			}
		}
	}

	//获取所有文件夹路径
    private void getFilePathNode(Map<String, List<SysFileManage>> map, List<List<String>> nodeList, String pid, String path) {
		List<SysFileManage> list = map.get(pid);
		if(StringUtils.isNotEmpty(list)) {
			for (SysFileManage item : list) {
				getFilePathNode(map, nodeList, item.getId(), path+","+item.getId());
			}
		}else {
			nodeList.add(Coms.StrToList(path, ","));
		}
	}

	private void deleteDataByChildrenGroup (List<SysFileManage> result, SysFileManage item, Map<String, List<SysFileManage>> group) {
        item.setTmused(0);
        result.add(item);
        List<SysFileManage> children = group.get(item.getId());
        if (StringUtils.isEmpty(children)) {
            return;
        }
        for (SysFileManage child : children) {
            deleteDataByChildrenGroup(result, child, group);
        }
    }

    private MultipartFile generateNewMultipartFile (MultipartFile file, SysFileManage fm) {
        String fileName = file.getOriginalFilename();
        String fileOriginalName = fileName;
        String fileExt = null;
        if (StringUtils.isNotEmpty(fileName) && fileName.contains(".")) {
            fileOriginalName = fileName.substring(0, fileName.lastIndexOf("."));
            fileExt = fileName.substring(fileName.lastIndexOf(".") + 1);
        }
        fileName = fileOriginalName+"_"+System.currentTimeMillis() + (StringUtils.isEmpty(fileExt)?"":"."+fileExt);
        MultipartFile newMultipartFile = null;
        try {
            newMultipartFile = new MockMultipartFile(fileName, fileName, file.getContentType(), IOUtils.toByteArray(file.getInputStream()));
        }catch (Exception e) {}
        return newMultipartFile;
    }

    @Override
    public String fileUpload(String pid, String dataClass, String moduleCode,String fileMode, String fileAuditFlow, String addFileMode, String level1, String applyType, MultipartFile file) {
    	return fileUpload(pid, dataClass, moduleCode, fileMode, fileAuditFlow, addFileMode, level1, applyType, null, file);
    }
    @Override
    public String fileUpload(String pid, String dataClass, String moduleCode,String fileMode, String fileAuditFlow, String addFileMode, String level1, String applyType, String orgCode, MultipartFile file) {
        SysUser currentUser = SysUserHolder.getCurrentUser();
        Date nd = DateTimeUtils.getNowDate();
        String dataGroupId = null;
        if (FileManegeDataClass.personal.toString().equals(dataClass)) {
            //查询个人
            dataGroupId = currentUser.getId();
        } else if (FileManegeDataClass.tenantpublic.toString().equals(dataClass)) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        } else if (FileManegeDataClass.tpublic.toString().equals(dataClass)) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        }

        SysFileManage folder = "root".equals(pid) ? null : srv.queryObjectByIdDisableTenant(SysFileManage.class, pid);
        dataClass = folder == null ? dataClass : folder.getDataClass();
        Where where = Where.create();
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getTmused, 1);
        if (FileManegeDataClass.personal.toString().equals(dataClass)) {
            //查询个人
            where.eq(SysFileManage::getDataGroupId, currentUser.getId());
        } else if (FileManegeDataClass.tenantpublic.toString().equals(dataClass)) {
            //查询企业
            where.eq(SysFileManage::getDataGroupId, currentUser.getTenant_id());
        } else if (FileManegeDataClass.tpublic.toString().equals(dataClass)) {
            //查询企业
            where.eq(SysFileManage::getDataGroupId, currentUser.getTenant_id());
        }
        if ("root".equals(pid)) {
            pid = getTenantRoot(moduleCode, dataClass, dataGroupId, level1).getId();
        }
        if (StringUtils.isNotEmpty(pid)) {
            //根据父id查
            where.eq(SysFileManage::getPid, pid);
        }
        //指定类型
        String fileName = file.getOriginalFilename();
        where.eq(SysFileManage::getFileType, 1);
        if (StringUtils.isNotEmpty(fileName)) {
            //按名称查询
            where.likeRight(SysFileManage::getName, fileName);
        }
        List<SysFileManage> files = srv.queryData(SysFileManage.class, where, null, null);
//        SysFileManage fm = StringUtils.isNotEmpty(files) ? files.get(0) : null;
        List<String> flist = new ArrayList<String>();
        if(StringUtils.isNotEmpty(files)) {
        	for (SysFileManage fm : files) {
				flist.add(fm.getName());
			}
        }

        String fileOriginalName = fileName;
        String fileExt = null;
        if (StringUtils.isNotEmpty(fileName) && fileName.contains(".")) {
            fileOriginalName = fileName.substring(0, fileName.lastIndexOf("."));
            fileExt = fileName.substring(fileName.lastIndexOf(".") + 1);
        }
        //改为重名后，创建新文件进行保存
//        file = generateNewMultipartFile(file, fm);
//        fileName = getNewFileName(flist, fileName);
//        if (fm != null) {//相同文件名称，上传后更新位置（原文件未删）
////            String oldUrl = fm.getFileUrl();
//            SysFilesInfo fileInfo = new SysFilesInfo();
//            fileInfo.setFileUrl(fm.getFileUrl());
//            fs.uploadFile(file, fileInfo);
//
//            fm.setFileDiskAddress(fileInfo.getFileAddress());
//            fm.setFileSize(file.getSize());
//            //更新
////            srv.update(fm);
//            treeSrv.updateNode(SysFileManage.class, MODEL_ID, fm);
//        } else 
        {
        	
        	fileName = getNewFileName(flist, fileName);
        	
            //新增
            String fileNewName = fileOriginalName+"_"+System.currentTimeMillis() + (StringUtils.isEmpty(fileExt)?"":"."+fileExt);
            SysFilesInfo fileInfo = new SysFilesInfo();
            String url = getFileRootUrl(dataClass);
            url = url+"/"+fileNewName;
            fileInfo.setFileUrl(url);
            fs.uploadFile(file, fileInfo);
            
            SysFileManage fm = new SysFileManage();
            fm.setModuleCode(moduleCode);
            fm.setFileSize(file.getSize());
            fm.setName(fileName);
            fm.setFileOriginalName(fileOriginalName);
            fm.setFileNewName(fileNewName);

            fm.setFileExt(fileExt);
            fm.setFileDiskAddress(fileInfo.getFileAddress());
            fm.setFileType(1);
            fm.setId(TMUID.getUID());
            fm.setTmused(1);
            fm.setPid(pid);
            fm.setDelFlag(0);
            fm.setModelId(MODEL_ID);
            fm.setDataClass(dataClass);
            fm.setDataGroupId(dataGroupId);
            fm.setFileUrl(url);
            
            fm.setCreateUserName(currentUser.getRealName());
            Integer iFileMode = fileMode==null?1:Integer.parseInt(fileMode);
            Integer iFddFileMode = addFileMode==null?1:Integer.parseInt(addFileMode);
            fm.setFileMode(iFileMode);
            fm.setAddFileMode(iFddFileMode);
            fm.setSourceDiskAddress(fm.getFileDiskAddress());
            fm.setCreateUserName(currentUser.getRealName());
        	
            if(1 == iFileMode) {//存档文件
            	fm.setFileStatus(9);//存档状态
            	fm.setPublishTime(nd);//发布时间
            	fm.setArchiveTime(nd);//存档时间
            	fm.setUpdateUserId(currentUser.getId());
            	fm.setUpdateUserName(currentUser.getRealName());
            	fm.setLastUpdateTime(nd);
            }else {
            	fm.setFileStatus(1);//未发布状态
            	fm.setAuditFlowCode(fileAuditFlow);
            }
            fm.setBasedOn(level1);
//            fm.setManageUserId(currentUser.getId());
//            fm.setManageUserName(currentUser.getRealName());
//            fm.setFilePath();//文件所属路径
            
            String orgId = null; 
        	if(currentUser!=null) {
        		orgId = currentUser.getOrgId();
        	}
        	encBelongCode(fm, orgCode, orgId);
            
//            srv.insert(fm);
            if(StringUtils.isEmpty(applyType)) {
            	treeSrv.append(SysFileManage.class, MODEL_ID, fm.getPid(), fm);
            }else {
            	fm.setCategoryValue(pid);
            	srv.insert(fm);
            }
            
            createManager(fm.getId());
            
            if(1 != iFileMode) {//在线文件进行解析
            	parseWordToHtml(fileInfo.getFileAddress(), fm.getId(), fileOriginalName);
            }
        }

        return null;
    }
    //解析word生成对应内容
    private void parseWordToHtml(String fileAddress, String fileId, String name) {
    	Date nd = DateTimeUtils.getNowDate();
    	
    	InputStream in = getFileInputStream(fileAddress);
    	
    	String html = getWordHtml(in);
    	
    	if(html != null) {
    		html = html.replace("\"#\"_Toc", "\"#_Toc");
    		//版本信息创建
    		SysOnlinefileVersion version = insertVersion(fileId);
    		//大纲根节点创建
    		SysOnlinefileNode rootNode = insertRootNode(fileId, version, name, nd);
    		//大纲节点变量
    		List<SysOnlinefileNode> nodeList = new ArrayList<SysOnlinefileNode>();
    		List<SysOnlinefileContent> contentList = new ArrayList<SysOnlinefileContent>();
    		
    		//解析html，获取目录，并根据目录进行分割
    		List<String> tocList = new ArrayList<String>();
        	List<String> hlist = Coms.getBl(html, "#_Toc\\d+");
        	for (String href : hlist) {
        		String toc = href.substring(1);
    			if(!tocList.contains(toc)) {
    				tocList.add(toc);
    			}
    		}
        	
        	int rowNum = 0;
        	int sort = 2;
        	StringBuffer sb = new StringBuffer();
        	String rowTitle = null;
        	String divtitle = null;
        	
        	List<String> divList = Coms.getBl(html, "<div[^>]*>[\\s\\S]*?<\\/div>");
        	for (String divstr : divList) {
        		//去除外层div标签
        		List<String> idivList = Coms.getBl(divstr, "<div[^>]*>");
        		divtitle = idivList.get(0);
        		List<String> repList = new ArrayList<String>();
        		for (String idiv : idivList) {
//    				System.out.println("内层div标签内容"+idiv);
    				repList.add("");
    			}
        		String inhtml = divstr.replace("</div>", "");
        		inhtml = Coms.replaceBl(inhtml, "<div[^>]*>", repList);
        		
        		//获取每个区域行数
        		List<String> plist = Coms.getBl(inhtml, "<([^>\\s]+)(?:[^>]*?)>([\\s\\S]*?)<\\/\\1>|<([^>\\s]+)(?:[^>]*?)\\/>");
//        		System.out.println("行数"+plist.size());
        		
        		//循环行
        		Boolean haveToc = false;
        		for (String rowstr : plist) {
        			for (String toc : tocList) {
    					if(rowstr.indexOf(toc)!=-1 && rowstr.indexOf("#"+toc)==-1) {//发现链接排除源链接标识
    						haveToc = true;
    						break;
    					}
    				}
        			
        			int inRows = getInRows(rowstr);
        			if(haveToc) {//有链接，进行分段
        				if(sb.length() > 0) {
        					//新建分支并存入对应内容，开启下一个内容
        					SysOnlinefileNode node = createNode(fileId, version, rowTitle, rootNode, nd, divtitle, sort++);
        					nodeList.add(node);
        					//创建内容
        					contentList.add(createContent(fileId, version, node, nd, sb.toString()));
        					
        					rowTitle = null;
        					sb.setLength(0);//清空原stringbuffer
        					sb.append(rowstr);
        					rowNum = 0;
        				}
        				haveToc = false;
        			}else if(rowNum > this.rowLimit) {//超限进行分段，暂不拆表格，表格一行算一行
        				//新建分支并存入对应内容，开启下一个内容
        				SysOnlinefileNode node = createNode(fileId, version, rowTitle, rootNode, nd, divtitle, sort++);
    					nodeList.add(node);
    					//创建内容
    					contentList.add(createContent(fileId, version, node, nd, sb.toString()));
    					
    					rowTitle = null;
    					sb.setLength(0);//清空原stringbuffer
        				sb.append(rowstr);
        				rowNum = 0;
            		}else {
        				sb.append(rowstr);
        			}
        			
        			rowNum = rowNum + inRows;
        			
        			if(rowTitle == null) {
        				rowTitle = getRowTitle(rowstr);
        			}
				}
        	}
        	
        	if(sb.length() > 0) {
        		//新建分支并存入对应内容，开启下一个内容
				SysOnlinefileNode node = createNode(fileId, version, rowTitle, rootNode, nd, divtitle, sort++);
				nodeList.add(node);
				//创建内容
				contentList.add(createContent(fileId, version, node, nd, sb.toString()));
        	}
        	
    		
    		if(StringUtils.isNotEmpty(nodeList)) {
    			srv.insertBatch(nodeList);
    		}
    		if(StringUtils.isNotEmpty(contentList)) {
    			srv.insertBatch(contentList);
    		}
    	}
	}
    //创建节点内容对象
    private SysOnlinefileContent createContent(String fileId, SysOnlinefileVersion version, SysOnlinefileNode node, Date nd, String nr) {
    	SysUser currentUser = SysUserHolder.getCurrentUser();
    	//内容保存
		SysOnlinefileContent content = new SysOnlinefileContent();
		content.setId(TMUID.getUID());
		content.setFileId(fileId);
		content.setVersionId(version.getId());
		content.setNodeId(node.getId());
		content.setNodeContent(nr);
		content.setLastUpdateUserId(currentUser.getId());
		content.setLastUpdateUserName(currentUser.getRealName());
		content.setLastUpdateTime(nd);
		content.setTmsort(1);
		content.setTmused(1);
		return content;
	}
    //创建节点对象
	private SysOnlinefileNode createNode(String fileId, SysOnlinefileVersion version, String name, SysOnlinefileNode rootNode, Date nd, String nodeCls, Integer sort) {
    	SysUser currentUser = SysUserHolder.getCurrentUser();
    	SysOnlinefileNode node = new SysOnlinefileNode();
		node.setId(TMUID.getUID());
		node.setFileId(fileId);
		node.setVersionId(version.getId());
		node.setDid(node.getId());
		node.setFid(rootNode.getDid()); 
		node.setAddVersion(version.getId());
		node.setAddVersionNo(version.getTmsort());
		node.setDelVersion(null);
		node.setNodeName(name);
		node.setUpdMark(0);
		node.setNodeCls(nodeCls);
		node.setLastUpdUser(currentUser.getRealName());
		node.setLastUpdTime(nd);
		node.setTmsort(sort);
		node.setTmused(1);
		return node;
	}
	//创建大纲根节点对象
	private SysOnlinefileNode insertRootNode(String fileId, SysOnlinefileVersion version, String name, Date nd) {
    	SysUser currentUser = SysUserHolder.getCurrentUser();
    	//属性节点保存
		SysOnlinefileNode rootnode = new SysOnlinefileNode();
		rootnode.setId(TMUID.getUID());
		rootnode.setFileId(fileId);
		rootnode.setVersionId(version.getId());
		rootnode.setDid("root");
		rootnode.setFid(null); 
		rootnode.setAddVersion(version.getId());
		rootnode.setAddVersionNo(version.getTmsort());
		rootnode.setDelVersion(null);
		rootnode.setNodeName(name);
		rootnode.setUpdMark(0);
		rootnode.setNodeCls(null);
		rootnode.setLastUpdUser(currentUser.getRealName());
		rootnode.setLastUpdTime(nd);
		rootnode.setTmsort(1);
		rootnode.setTmused(1);
		srv.insert(rootnode);
		return rootnode;
	}
	//创建版本信息对象
	private SysOnlinefileVersion insertVersion(String fileId) {
    	//版本信息保存
		SysOnlinefileVersion version = new SysOnlinefileVersion();
		version.setId(TMUID.getUID());
		version.setCurrMark(0);//当前活动状态1发布3存档
		version.setFileId(fileId);
		version.setActiveMark(1);
		version.setActiveArchiveMark(0);
		version.setTmsort(1);
		version.setTmused(1);
		srv.insert(version);
		return version;
	}

	/**
     * @category 获取一个行数据的文字（表格获取第一行）
     * @param rowstr
     * @return
     */
    private String getRowTitle(String rowstr) {
		//去除格式，获取文字内容，并提取
    	String title = rowstr.replace("\r", "").replace("\n", "");
    	title = title.replaceAll("</?p[^>]*>", "").replaceAll("<br[^>]*>", "").replaceAll("<BR[^>]*>", "");
    	//表格特殊处理
    	if(title.startsWith("<table")) {//取第一行内容
    		List<String> trlist = Coms.getBl(title, "<tr[^>]*>.*?</tr>");
    		if(trlist.size() > 0) {
    			title = trlist.get(0).replaceAll("</?[^>]*>", "");
    		}else {
    			title = "";
    		}
    	}else {
    		title = title.replaceAll("</?[^>]*>", "");
    	}
    	if(StringUtils.isEmpty(title)) {
    		title = "节点";
    	}
    	
		return title;
	}
    /**
     * @category 获取字符串行数，p标签算一行，table标签按tr计算行数
     * @param rowstr
     * @return
     */
	private int getInRows(String rowstr) {
		if(rowstr.indexOf("<table")!=-1) {
			List<String> list = Coms.getBl(rowstr, "</tr>");
			return list.size();
		}
		return 1;
	}

	private InputStream getFileInputStream(String fileAddress) {
    	InputStream in = null;
    	try {
    		File file = new File(fileAddress);
			in = new FileInputStream(file);
		} catch (FileNotFoundException e) {
			log.error("wordToHtml file not exists:"+e.getMessage());
		}
    	return in;
    }
    
    private String getWordHtml(InputStream in) {
    	String html = null;
    	if(in != null) {
    		try {
    			//Zip bomb detected! The file would exceed the max. ratio of compressed file size to the size of the expanded data
    			//有的时候会报这种错，这个代表的是传输文件过大，禁止传入，未压缩大小：105758，原始/压缩大小：894，比例：0.008453，限制：MIN_INFLATE_RATIO:0.01，调整压缩比限制，-1.0d不限制
    			ZipSecureFile.setMinInflateRatio(0.0001d);//
    			// 1.加载解析docx文档用的XWPFDocument对象
    			XWPFDocument document = new XWPFDocument(in);
//				int pageCount = document.getProperties().getExtendedProperties().getUnderlyingProperties().getPages();
//        		System.out.println("Total number of pages: " + pageCount);
    			// 2.解析XHTML配置 设置图片链接
    			XHTMLOptions options = XHTMLOptions.create();
    			// 3.将word中图片保存到指定目录
    			options.setImageManager(new ImageManagerImpl(fileInfoSrv));
    			options.setIgnoreStylesIfUnused(false);
    			options.setFragment(true);
    			//输入文件改为输出为字节数组
//        		@Cleanup
    			ByteArrayOutputStream out = new ByteArrayOutputStream();//new FileOutputStream(templatePath + htmlName);
    			// 将XWPFDocument转换成XHTML
    			XHTMLConverter.getInstance().convert(document, out, options);
    			
    			byte[] obarr = out.toByteArray();
    			
    			Charset cs = Charset.defaultCharset();
    			log.info("Charset:"+cs.toString());
    			html = new String(obarr, cs);//StandardCharsets.UTF_8
    			
    			// 将html文件上传到doc文件夹内
//				htmlFile = new File(templatePath + htmlName);
//				content = readFile(htmlFile);
    		} catch (Exception e) {
    			log.error("word parse error:"+e.getMessage());
    		} finally {
    			try {
    				in.close();
    			} catch (IOException e) {
    			}
    		}
    	}
    	return html;
    }

    @Override
    public Boolean addNullOnlineFile(FileManageQueryDto dto) {
    	return addNullOnlineFile(dto.getPid(), dto.getDataClass(), dto.getModuleCode(), dto.getMode(), dto.getFileAuditFlow(), "1", dto.getName(), dto.getLevel1(), dto.getApplyType(), dto.getOrgCode());
    }
	/**
     * @category 添加空在线文档
     * @param pid
     * @param dataClass
     * @param moduleCode
     * @param fileMode
     * @param fileAuditFlow
     * @param addFileMode
     * @param fileName
     */
    public Boolean addNullOnlineFile(String pid, FileManegeDataClass dataClass, String moduleCode,Integer fileMode, String fileAuditFlow, String addFileMode, String fileName, String level1, String applyType, String orgCode) {
    	SysUser currentUser = SysUserHolder.getCurrentUser();
//        Date nd = DateTimeUtils.getNowDate();
        String dataGroupId = null;
        if (FileManegeDataClass.personal.toString().equals(dataClass.name())) {
            //查询个人
            dataGroupId = currentUser.getId();
        } else if (FileManegeDataClass.tenantpublic.toString().equals(dataClass.name())) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        } else if (FileManegeDataClass.tpublic.toString().equals(dataClass.name())) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        }

        SysFileManage folder = "root".equals(pid) ? null : srv.queryObjectByIdDisableTenant(SysFileManage.class, pid);
        String dc = folder == null ? dataClass.name() : folder.getDataClass();
        Where where = Where.create();
        where.eq(SysFileManage::getDataClass, dc);
        where.eq(SysFileManage::getTmused, 1);
        if (FileManegeDataClass.personal.toString().equals(dc)) {
            //查询个人
            where.eq(SysFileManage::getDataGroupId, currentUser.getId());
        } else if (FileManegeDataClass.tenantpublic.toString().equals(dataClass)) {
            //查询企业
            where.eq(SysFileManage::getDataGroupId, currentUser.getTenant_id());
        }
        if ("root".equals(pid)) {
            pid = getTenantRoot(moduleCode, dc, dataGroupId, level1).getId();
        }
        if (StringUtils.isNotEmpty(pid)) {
            //根据父id查
            where.eq(SysFileManage::getPid, pid);
        }
        
        fileName = fileName+".hand";
        where.eq(SysFileManage::getFileType, 1);
        if (StringUtils.isNotEmpty(fileName)) {
            //按名称查询
            where.likeRight(SysFileManage::getName, fileName);
        }
        List<SysFileManage> files = srv.queryData(SysFileManage.class, where, null, null);
//        SysFileManage fm = StringUtils.isNotEmpty(files) ? files.get(0) : null;
        
        List<String> flist = new ArrayList<String>();
        if(StringUtils.isNotEmpty(files)) {
        	for (SysFileManage fm : files) {
				flist.add(fm.getName());
			}
        }
    	
        String fileOriginalName = fileName;
        String fileExt = null;
        if (StringUtils.isNotEmpty(fileName) && fileName.contains(".")) {
            fileOriginalName = fileName.substring(0, fileName.lastIndexOf("."));
            fileExt = fileName.substring(fileName.lastIndexOf(".") + 1);
        }

        String fileNewName = fileOriginalName+"_"+System.currentTimeMillis() + (StringUtils.isEmpty(fileExt)?"":"."+fileExt);
        
        fileName = getNewFileName(flist, fileName);

//        if (flist.contains(fileName)) {//相同文件名称，修改名称
//        	fileName = getNewFileName(flist, fileName);//fileOriginalName+"_副本."+fileExt;
//        }
        //新增
        SysFileManage fm = new SysFileManage();
        fm.setModuleCode(moduleCode);
        fm.setFileSize(0l);
        fm.setName(fileName);
        fm.setFileOriginalName(fileOriginalName);
        fm.setFileNewName(fileNewName);
        fm.setFileExt(fileExt);
        fm.setFileType(1);
//        fm.setId(TMUID.getUID());
        fm.setTmused(1);
        fm.setPid(pid);
        fm.setDelFlag(0);
        fm.setModelId(MODEL_ID);
        fm.setDataClass(dc);
        fm.setDataGroupId(dataGroupId);
        fm.setFileDiskAddress(null);
        fm.setFileUrl(null);
        
//        Integer iFileMode = fileMode==null?2:Integer.parseInt(fileMode);
        Integer iFddFileMode = addFileMode==null?2:Integer.parseInt(addFileMode);
        fm.setFileMode(fileMode);
        fm.setAddFileMode(iFddFileMode);
        fm.setSourceDiskAddress(null);
    	fm.setFileStatus(1);//手工在线文件未发布状态
    	fm.setAuditFlowCode(fileAuditFlow);
    	fm.setBasedOn(level1);
    	fm.setCreateUserName(currentUser.getRealName());
    	
    	String orgId = null; 
    	if(currentUser!=null) {
    		orgId = currentUser.getOrgId();
    	}
    	encBelongCode(fm, orgCode, orgId);
    	
    	fm.setId(TMUID.getUID());
    	
//    	fm.setUpdateUserId(currentUser.getId());
//    	fm.setUpdateUserName(currentUser.getRealName());
//    	fm.setUpdateTime(nd);
    	
//    	fm.setManageUserId(currentUser.getId());
//    	fm.setManageUserName(currentUser.getRealName());
//    	fm.setFilePath(null);
        if(StringUtils.isEmpty(applyType)) {
        	treeSrv.append(SysFileManage.class, MODEL_ID, fm.getPid(), fm);
        }else {
        	fm.setCategoryValue(pid);
        	srv.insert(fm);
        }
        
        createNullVerInfo(fm.getId(), fileOriginalName);
        
        createManager(fm.getId());
        
        return true;
    }
    //赋值所属机构
    private void encBelongCode(SysFileManage fm, String orgCode, String orgId) {
    	if(StringUtils.isNotEmpty(orgCode)) {//如果传入机构（装置）代码，直接保存并获取车间代码保存
    		fm.setBelongZz(orgCode);
    		List<SysOrg> orgs = orgsrv.getParentOrgByType(orgCode, cjmark);
    		if(StringUtils.isNotEmpty(orgs)) {
				String cjdm = orgs.get(0).getOrgcode();
				fm.setBelongCj(cjdm);
			}
    	}else if(StringUtils.isNotEmpty(orgId)) {//如果未传入机构（装置）代码，根据人员所在机构，获取装置、车间代码保存
			List<SysOrg> orgs = orgsrv.getParentOrgByType(orgId, zzmark);
			if(StringUtils.isNotEmpty(orgs)) {
				String zzdm = orgs.get(0).getOrgcode();
				fm.setBelongZz(zzdm);
			}
			
			orgs = orgsrv.getParentOrgByType(orgId, cjmark);
			if(StringUtils.isNotEmpty(orgs)) {
				String cjdm = orgs.get(0).getOrgcode();
				fm.setBelongCj(cjdm);
			}
    	}
    }
    
    private void createManager(String id) {
    	SysUser currentUser = SysUserHolder.getCurrentUser();
    	SysOnlinefileManager manager = new SysOnlinefileManager();
    	manager.setId(TMUID.getUID());
    	manager.setManageUserId(currentUser.getId());
    	manager.setManageUserName(currentUser.getRealName());
    	manager.setTmused(1);
    	manager.setFileId(id);
    	
    	srv.insert(manager);
	}

	private String getNewFileName(List<String> flist, String fileName) {
		if(flist.contains(fileName)) {
			String ndstr = DateTimeUtils.getYmdhmssStr();
			String[] ss = fileName.split("\\.");
			String fn = ss[0];
			String newName = fn+ndstr;
			if(ss.length > 1) {
				String fileExt = ss[ss.length-1];
				newName = fn+ndstr+"."+fileExt;
			}
			return getNewFileName(flist, newName);
		}
		
		if(fileName.indexOf(".")!=-1) {
			fileName = fileName.substring(0, fileName.lastIndexOf("."));
		}
		
		return fileName;
	}

	public void createNullVerInfo(String fileId, String name) {
    	SysUser currentUser = SysUserHolder.getCurrentUser();
    	Date nd = DateTimeUtils.getNowDate();
    	
    	//版本信息保存
    	SysOnlinefileVersion version = new SysOnlinefileVersion();
    	version.setId(TMUID.getUID());
    	version.setCurrMark(0);//当前活动状态1发布3存档
    	version.setActiveMark(1);
    	version.setActiveArchiveMark(0);
    	version.setFileId(fileId);
    	version.setTmsort(1);
    	version.setTmused(1);
    	
    	srv.insert(version);
    	
    	//属性节点保存
    	SysOnlinefileNode rootnode = new SysOnlinefileNode();
    	rootnode.setId(TMUID.getUID());
    	rootnode.setFileId(fileId);
    	rootnode.setVersionId(version.getId());
    	rootnode.setDid("root");
    	rootnode.setFid(null); 
    	rootnode.setAddVersion(version.getId());
    	rootnode.setAddVersionNo(version.getTmsort());
    	rootnode.setDelVersion(null);
    	rootnode.setNodeName(name);
    	rootnode.setUpdMark(0);
    	rootnode.setNodeCls(null);
    	rootnode.setLastUpdUser(currentUser.getRealName());
    	rootnode.setLastUpdTime(nd);
    	rootnode.setTmsort(1);
    	rootnode.setTmused(1);
    	
    	SysOnlinefileNode node = new SysOnlinefileNode();
    	node.setId(TMUID.getUID());
    	node.setFileId(fileId);
    	node.setVersionId(version.getId());
    	node.setDid(node.getId());
    	node.setFid("root"); 
    	node.setAddVersion(version.getId());
    	node.setAddVersionNo(version.getTmsort());
    	node.setDelVersion(null);
    	node.setNodeName(name);
    	node.setUpdMark(0);
    	node.setNodeCls(null);
    	node.setLastUpdUser(currentUser.getRealName());
    	node.setLastUpdTime(nd);
    	node.setTmsort(1);
    	node.setTmused(1);
    	
    	List<SysOnlinefileNode> nodeList = new ArrayList<SysOnlinefileNode>(2);
    	nodeList.add(rootnode);
    	nodeList.add(node);
    	
    	srv.insertBatch(nodeList);
    	
    	//内容保存
    	SysOnlinefileContent content = new SysOnlinefileContent();
    	content.setId(TMUID.getUID());
    	content.setFileId(fileId);
    	content.setVersionId(version.getId());
    	content.setNodeId(node.getId());
    	content.setNodeContent("");
    	content.setLastUpdateUserId(currentUser.getId());
    	content.setLastUpdateUserName(currentUser.getRealName());
    	content.setLastUpdateTime(nd);
    	content.setTmsort(1);
    	content.setTmused(1);
    	
    	srv.insert(content);
    }

    @Override
    public void fileDownload(HttpServletResponse response, FileManageQueryDto dto) {
    	FileManegeDataClass dataClass = dto.getDataClass();
        String id = dto.getId();
        if (FileManegeDataClass.system.equals(dataClass)) {
            //系统文件
            fileInfoSrv.downloadFile(id, response);
        } else {
            //个人或者企业文件
            SysFileManage file = srv.queryObjectById(SysFileManage.class, id);
            if (file == null) {
                throw new RuntimeException("找不到文件");
            }
            SysFilesInfo fileInfo = new SysFilesInfo();
            fileInfo.setFileUrl(file.getFileUrl());
            fileInfo.setFileAddress(file.getFileDiskAddress());
            fileInfo.setOldFileName(file.getName());
            fileInfoSrv.downloadFile(fileInfo, response);
        }
    }

    @Override
    public String realDeleteFileByIdList(List<String> idList) {
        if (StringUtils.isEmpty(idList)) {
            throw new RuntimeException("传入参数有误");
        }
        srv.rawUpdate(SysFileManage.class, Update.create(SysFileManage::getTmused, -1), Where.create().in(SysFileManage::getId, idList.toArray()));

        return null;
    }

    @Override
    public String restoreFiles(List<String> idList) {
        List<SysFileManage> dataList = srv.queryData(SysFileManage.class, Where.create().in(SysFileManage::getId, idList.toArray()), null, null);
        if (StringUtils.isEmpty(dataList)) {
            throw new RuntimeException("找不到要还原的记录");
        }

        String moduleCode = dataList.get(0).getModuleCode();
        String dataClass = dataList.get(0).getDataClass();
        String dataGroupId = dataList.get(0).getDataGroupId();
        Where where = Where.create();
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getModuleCode, moduleCode);
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getDataGroupId, dataGroupId);

        List<SysFileManage> allList = srv.queryData(SysFileManage.class, where, null, null);
        if (StringUtils.isEmpty(allList)) {
            throw new RuntimeException("找不到记录");
        }

        Map<String, List<SysFileManage>> childGroup = new HashMap<>();
        Map<String, SysFileManage> dataMap = new HashMap<>();
        for (SysFileManage file : allList) {
            dataMap.put(file.getId(), file);
            childGroup.computeIfAbsent(file.getPid(), v -> new ArrayList<>()).add(file);
        }

        Set<String> set = new HashSet<>();
        for (SysFileManage file : dataList) {
            getRestorePidList(set, file, dataMap);
            getRestoreChildIdList (set, file, childGroup);
        }

        srv.rawUpdate(SysFileManage.class, Update.create(SysFileManage::getDelFlag,0).update(SysFileManage::getTmused,1), Where.create().in(SysFileManage::getId, set.toArray()));


        /*
        String rootId = getRoot(moduleCode, dataClass, dataGroupId).getId();
        Where where = Where.create();
        where.eq(SysFileManage::getPid, rootId);
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getFileType, 2);
        where.eq(SysFileManage::getName, "回收站还原");
        where.eq(SysFileManage::getDataGroupId, dataList.get(0).getDataGroupId());
        where.eq(SysFileManage::getDataClass, dataList.get(0).getDataClass());
        List<SysFileManage> folders = srv.queryData(SysFileManage.class, where, null, null);
        SysFileManage folder = null;
        if (StringUtils.isEmpty(folders)) {
            //没有还原文件夹
            folder = new SysFileManage();
            folder.setId(TMUID.getUID());
            folder.setPid(rootId);
            folder.setFileType(2);
            folder.setName("回收站还原");
            folder.setTmused(1);
            folder.setDataGroupId(dataList.get(0).getDataGroupId());
            folder.setDataClass(dataList.get(0).getDataClass());
            folder.setModelId(MODEL_ID);
            folder.setDelFlag(0);
            srv.insert(folder);
        } else {
            folder = folders.get(0);
        }
        for (SysFileManage file : dataList) {
            file.setTmused(1);
            file.setPid(folder.getId());
        }
        srv.updateBatch(dataList);
        */

        return null;
    }

    @Override
    public boolean fileExists(FileManageQueryDto dto) {

        if ("root".equals(dto.getPid())) {
            //查询层级1
            FileManegeDataClass dataClassEnum = dto.getDataClass();
            String moduleCode = dto.getModuleCode();
            String dataClass = dataClassEnum.toString();
            SysUser currentUser = SysUserHolder.getCurrentUser();
            String dataGroupId = null;
            if (FileManegeDataClass.personal.equals(dto.getDataClass())) {
                //查询个人
                dataGroupId = currentUser.getId();
            } else if (FileManegeDataClass.tenantpublic.equals(dto.getDataClass())) {
                //查询企业
                dataGroupId = currentUser.getTenant_id();
            }
            dto.setPid(getTenantRoot(moduleCode, dataClass, dataGroupId, dto.getLevel1()).getId());
        }

        Where where = Where.create();
        where.eq(SysFileManage::getPid, dto.getPid());
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getDelFlag, 0);
        if (dto.getFileType() > 0) {
            //指定类型
            where.eq(SysFileManage::getFileType, dto.getFileType());
        }
        if (StringUtils.isNotEmpty(dto.getName())) {
            //按名称查询
            where.eq(SysFileManage::getName, dto.getName());
        }

        List<SysFileManage> list = srv.queryData(SysFileManage.class, where, null, null);
        return StringUtils.isNotEmpty(list);
    }

    @Override
    public String getFileBase64(SysFileManage file) {
        String dataClass = file.getDataClass();
        SysFilesInfo fileInfo = new SysFilesInfo();
        fileInfo.setFileUrl(file.getFileUrl());
        fileInfo.setFileAddress(file.getFileDiskAddress());
        InputStream fis = fs.getFileSteam(fileInfo);
        String res = null;
        if (fis == null) {
            return null;
        }
        try {
            byte[] buffer = IOUtils.toByteArray(fis);
            res = Base64Helper.encode(buffer);
        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                }
            }
        }
        return res;
    }


    private void getRestorePidList (Set<String> set, SysFileManage file, Map<String, SysFileManage> dataMap) {
        set.add(file.getId());
        String pid = file.getPid();
        SysFileManage p = dataMap.get(pid);
        if (p != null) {
            getRestorePidList(set, p, dataMap);
        }
    }

    private void getRestoreChildIdList (Set<String> set, SysFileManage file, Map<String, List<SysFileManage>> childGroup) {
        set.add(file.getId());
        List<SysFileManage> children = childGroup.get(file.getId());
        if (StringUtils.isNotEmpty(children)) {
            for (SysFileManage child : children) {
                getRestoreChildIdList (set, child, childGroup);
            }
        }
    }
    
    
    /**--在线文档处理----------------------------------------------------------------------******************/
    
    /*
     * 查询数据列表
     */
    @Override
    public List<FileManageVo> queryOnlineDataList(FileManageQueryDto dto) {
        List<FileManageVo> result = new ArrayList<>();
        FileManegeDataClass dataClassEnum = dto.getDataClass();

        String moduleCode = dto.getModuleCode();
        String dataClass = dataClassEnum==null?"tpublic":dataClassEnum.toString();
        SysUser currentUser = SysUserHolder.getCurrentUser();
        String dataGroupId = null;
       if ("tpublic".equals(dataClass)) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        }

        if ("root".equals(dto.getPid())) {
            //查询层级1
            dto.setPid(getTenantRoot(moduleCode, dataClass, dataGroupId, dto.getLevel1()).getId());
        }
        

        //其余情况为文件管理中的查询
        Where where = Where.create();
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getTmused, dto.getTmused());
        where.eq(SysFileManage::getDataGroupId, dataGroupId);
        where.eq(SysFileManage::getDelFlag, dto.getDelflag());
        if (StringUtils.isNotEmpty(dto.getPid())) {
            //根据父id查
            where.eq(SysFileManage::getPid, dto.getPid());
        }
        if (dto.getFileType() > 0) {
            //指定类型
            where.eq(SysFileManage::getFileType, dto.getFileType());
        }
        if (StringUtils.isNotEmpty(dto.getName())) {
            //按名称查询
//            where.eq(SysFileManage::getName, dto.getName());
            where.like(SysFileManage::getName, dto.getName());
        }
        
        //查管理人管理的文档ID
        List<SysFileManage> tlist = srv.queryData(SysFileManage.class, where, null, null);
        List<String> manageIdList = getManageIds(tlist, currentUser.getId());
        
        //文档查询未发布的文档不显示
        where.and().lb();
        where.eq(SysFileManage::getFileType, 2);//文件夹
        where.or();
        where.eq(SysFileManage::getFileMode, 1);//存档文件
        where.or().lb();
	        where.eq(SysFileManage::getFileMode, 2);//在线存档
	        where.and().lb();
	        	where.notEmpty(SysFileManage::getArchiveTime);//有发布时间（找最后一个版本的）
	        	where.or();
//	        	where.eq(SysFileManage::getManageUserId, currentUser.getId());
	        	where.in(SysFileManage::getId, manageIdList.toArray());
        	where.rb();
	        where.rb();
        where.rb();
        

        Order order = Order.create();
        order.orderByDesc(SysFileManage::getFileType);
        if ("time".equals(dto.getOrder()) && dto.getDesc() == 1) {
            order.orderByDesc(SysFileManage::getLastUpdateTime);
        } else {
            if ("name".equals(dto.getOrder()) && dto.getDesc() == 0) {
                //名称正序
                order.orderByAsc(SysFileManage::getName);
            } else if ("name".equals(dto.getOrder()) && dto.getDesc() == 1) {
                //名称倒序
                order.orderByDesc(SysFileManage::getName);
            } else if ("size".equals(dto.getOrder()) && dto.getDesc() == 0) {
                //大小正序
                order.orderByAsc(SysFileManage::getFileSize);
            } else if ("size".equals(dto.getOrder()) && dto.getDesc() ==1) {
                //大小倒序
                order.orderByDesc(SysFileManage::getFileSize);
            }
            //默认创建时间正序
            order.orderByAsc(SysFileManage::getLastUpdateTime);
        }

        List<SysFileManage> list = srv.queryData(SysFileManage.class, where, order, null);
        if (StringUtils.isNotEmpty(list)) {
        	
        	List<String> idList = new ArrayList<String>();
            for (SysFileManage obj : list) {
            	if(new Integer(1).equals(obj.getFileType())) {
            		idList.add(obj.getId());
            	}
    		}
            
            Map<String, String> verMap = getFilePassVerMap(idList);
            Map<String, String> opverMap = getFileVerMap(idList);
            
            
            for (SysFileManage file : list) {
                FileManageVo fileManageVo = ObjUtils.copyTo(file, FileManageVo.class);
                fileManageVo.setVersion(verMap.get(file.getId()));
                fileManageVo.setVersionOp(opverMap.get(file.getId()));
                if(manageIdList.contains(file.getId())) {
                	fileManageVo.setManageMark(1);
            	}
                result.add(fileManageVo);
                if (StringUtils.isNotEmpty(dto.getName()) && StringUtils.isEmpty(dto.getPid())) {
                    //全局按名称查
//                    urlIdList.add(file.get)
                }
            }
        }
        return result;
    }
    private List<String> getManageIds(List<SysFileManage> tlist, String uid) {
    	//缩小查询范围，查询指定目录文件，查对应文件指定管理人数据，合并出管理文件id数组
    	List<String> rlist = new ArrayList<String>();
		List<String> idList = new ArrayList<String>();
		if(StringUtils.isNotEmpty(tlist)) {
			for (SysFileManage obj : tlist) {
				if(new Integer(1).equals(obj.getFileType())) {
					idList.add(obj.getId());
				}
			}
		}
		if(StringUtils.isNotEmpty(idList)) {
			Where where = new Where();
			where.eq(SysOnlinefileManager::getTmused, 1);
			where.eq(SysOnlinefileManager::getManageUserId, uid);
			where.in(SysOnlinefileManager::getFileId, idList.toArray());
			List<SysOnlinefileManager> list = srv.queryData(SysOnlinefileManager.class, where, null, null);
			if(StringUtils.isNotEmpty(list)) {
				for (SysOnlinefileManager obj : list) {
					rlist.add(obj.getFileId());
				}
			}
			
		}
		
		if(StringUtils.isEmpty(rlist)){
			rlist.add("0");
		}
		return rlist;
	}

	/**
     * 获取文档树形
     */
    public FileManageVo getOnlineFileTree(FileManageQueryDto dto) {
    	FileManageVo root = new FileManageVo();
    	
    	String moduleCode = dto.getModuleCode();
        String dataClass = dto.getDataClass()==null?"tpublic":dto.getDataClass().toString();
        SysUser currentUser = SysUserHolder.getCurrentUser();
        String dataGroupId =  currentUser.getTenant_id();

        if ("root".equals(dto.getPid())) {
            //查询层级1
            dto.setPid(getTenantRoot(moduleCode, dataClass, dataGroupId, dto.getLevel1()).getId());
        }
		
    	Where where = Where.create();
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getDelFlag, 0);
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getDataGroupId, dataGroupId);
        where.eq(SysFileManage::getFileType, 2);//文件夹
        
        Order order = Order.create();
        order.orderByDesc(SysFileManage::getCreateTime);
        List<SysFileManage> list = srv.queryData(SysFileManage.class, where, order, null);
        Map<String, List<SysFileManage>> map = new HashMap<String, List<SysFileManage>>();
    	
		for (SysFileManage node : list) {
			if(map.containsKey(node.getPid())) {
				map.get(node.getPid()).add(node);
			}else {
				List<SysFileManage> tlist = new ArrayList<SysFileManage>();
				tlist.add(node);
				map.put(node.getPid(), tlist);
			}
		}
		List<SysFileManage> rootList = map.get(dto.getPid());
		if(StringUtils.isNotEmpty(rootList)) {
			root.setId(dto.getPid());
			root.setPid("root");
			root.setName("文档");
//			root = ObjUtils.copyTo(rootList.get(0), TreeVo.class);
			getFileTreeNode(map, root, dto.getName());
			
		}
		
		return root;
    }
    private void getFileTreeNode(Map<String, List<SysFileManage>> map, FileManageVo node, String name) {
		List<SysFileManage> list = map.get(node.getId());
		List<FileManageVo> children = new ArrayList<FileManageVo>();
		if(StringUtils.isNotEmpty(list)) {
			for (SysFileManage item : list) {
				FileManageVo vo = ObjUtils.copyTo(item, FileManageVo.class);
				if(StringUtils.isNotEmpty(name) && item.getName().indexOf(name)!=-1) {
					vo.setFileMode(3);
				}else {
					vo.setFileMode(1);
				}
				children.add(vo);
				getFileTreeNode(map, vo, name);
			}
		}
		node.setChildren(children);
	}
    /**
     * @category 获取对应数据id的文件夹路径
     * @param dto
     * @return
     */
    public List<Map<String, String>> getOnlineFolderMap(FileManageQueryDto dto) {
    	SysUser currentUser = SysUserHolder.getCurrentUser();
    	String moduleCode = dto.getModuleCode();
        String dataClass = dto.getDataClass()==null?"tpublic":dto.getDataClass().toString();
        String dataGroupId =  currentUser.getTenant_id();

        dto.setPid(getTenantRoot(moduleCode, dataClass, dataGroupId, dto.getLevel1()).getId());
		
    	Where where = Where.create();
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getDelFlag, 0);
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getDataGroupId, dataGroupId);
        where.eq(SysFileManage::getFileType, 2);//文件夹
        
        Order order = Order.create();
        order.orderByDesc(SysFileManage::getCreateTime);
        List<SysFileManage> list = srv.queryData(SysFileManage.class, where, order, null);
        
        Map<String, List<SysFileManage>> map = new HashMap<String, List<SysFileManage>>();
        Map<String, String> nameMap = new HashMap<String, String>();
        Map<String, String> pathMap = new HashMap<String, String>();
    	
		for (SysFileManage node : list) {
			if(map.containsKey(node.getPid())) {
				map.get(node.getPid()).add(node);
			}else {
				List<SysFileManage> tlist = new ArrayList<SysFileManage>();
				tlist.add(node);
				map.put(node.getPid(), tlist);
			}
		}
		
		List<Map<String, String>> rlist = new ArrayList<Map<String,String>>(2);
		rlist.add(nameMap);
		rlist.add(pathMap);
		nameMap.put(dto.getPid(), "公共文档");
		pathMap.put(dto.getPid(), "公共文档");
		
		List<SysFileManage> rootList = map.get(dto.getPid());
		FileManageVo root = new FileManageVo();
		if(StringUtils.isNotEmpty(rootList)) {
			root.setId(dto.getPid());
			root.setPid("root");
			root.setName("文档");
			root = ObjUtils.copyTo(root, FileManageVo.class);
			getFolderTreeNode(map, root, nameMap, pathMap);
			
		}
		
		return rlist;
    }
    private void getFolderTreeNode(Map<String, List<SysFileManage>> map, FileManageVo node, Map<String, String> nameMap, Map<String, String> pathMap) {
		List<SysFileManage> list = map.get(node.getId());
		List<FileManageVo> children = new ArrayList<FileManageVo>();
		if(StringUtils.isNotEmpty(list)) {
			for (SysFileManage item : list) {
				FileManageVo vo = ObjUtils.copyTo(item, FileManageVo.class);
				nameMap.put(vo.getId(), vo.getName());
				pathMap.put(vo.getId(), pathMap.get(vo.getPid())+"-"+vo.getName());
				children.add(vo);
				getFolderTreeNode(map, vo, nameMap, pathMap);
			}
		}
		node.setChildren(children);
	}
    
    /**
     * @category 获取本地物理路径
     * @return
     */
    public String getResourcesPath() {
    	String filePath = null;
        String os = System.getProperty("os.name");
//        System.out.println(os);
        if (os != null && os.toLowerCase().startsWith("windows")) {
            filePath = System.getProperty("user.dir") + File.separator + "src"+ File.separator+"main"+ File.separator+"resources" + File.separator;
        } else if (os != null && os.toLowerCase().startsWith("linux")) {
            filePath = this.getClass().getClassLoader().getResource("").getPath();
        }
//        System.out.println(filePath);
        
        return filePath;
    }

	@Override
	/**
	 * @category 获取个人数据列表（待发布、待审核、已发布）
	 */
	public FileManageVo queryFileList(FileManageQueryDto dto) {
		SysUser currentUser = SysUserHolder.getCurrentUser();
		
		Boolean isFormFile = "formfile".equals(dto.getApplyType());
		String dataClass = dto.getDataClass()==null?"tpublic":dto.getDataClass().toString();
		String dataGroupId = null;
		if ("tpublic".equals(dataClass)) {
            dataGroupId = currentUser.getTenant_id();
        }
		Where where = Where.create();
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getDataGroupId, dataGroupId);
        where.eq(SysFileManage::getDelFlag, 0);
        where.eq(SysFileManage::getTmused, 1);
        if(StringUtils.isNotEmpty(dto.getName())) {
        	where.like(SysFileManage::getName, dto.getName());
        }
        
        if(StringUtils.isNotEmpty(dto.getLevel1())) {
        	where.eq(SysFileManage::getBasedOn, dto.getLevel1());
        }
        
      //有管理权限，显示全部数据
        if(isFormFile) {
        	if(permUtils.isHasPerm("system:onlineFile:formFileManage:manageAll") || "administrator".equals(currentUser.getId())) {
        	}else if(permUtils.isHasPerm("system:onlineFile:formFileManage:manage")) {//本车间
        		List<String> olist = new ArrayList<String>();
        		String orgId = currentUser.getOrgId();
        		List<SysOrg> orgs = orgsrv.getParentOrgByType(orgId, cjmark);
        		if(StringUtils.isNotEmpty(orgs)) { 
        			SysOrg org = orgs.get(0);
        			SysOrgSelect data = new SysOrgSelect();
        			data.setPorgcode(org.getOrgcode());
        			List<SysOrgTreeData> allsuborgs = orgsrv.listDatas(data);
        			setOrgList(allsuborgs, olist);
        		}else {
        			olist.add(orgId);
        		}
        		where.in(SysFileManage::getBelongZz, olist.toArray());
        	}else {
        		List<String> fids = getManageFileIds(currentUser.getId());
        		where.in(SysFileManage::getId, fids.toArray());
        	}
        }else {
        	if(permUtils.isHasPerm("system:onlineFile:onlineFileManage:manage") || "administrator".equals(currentUser.getId())) {
        	}else {
//        	where.eq(SysFileManage::getManageUserId, currentUser.getId());
        		List<String> fids = getManageFileIds(currentUser.getId());
        		where.in(SysFileManage::getId, fids.toArray());
        	}
        }
        
        
        if(StringUtils.isNotEmpty(dto.getNewId())) {
//        	where.eq(SysFileManage::getManageUserId, dto.getNewId());
        	List<String> fids = getManageFileIds(dto.getNewId());
        	where.in(SysFileManage::getId, fids.toArray());
        }
        
        Order order = Order.create();
        
        if(1 == dto.getMode()) {//待发布
        	where.in(SysFileManage::getFileStatus, 1, -1);
        	order.orderByDesc(SysFileManage::getUpdateTime);
        	order.orderByDesc(SysFileManage::getCreateTime);
//        }else if(2 == dto.getMode()) {//待审核
//        	where.in(SysFileManage::getFileStatus, 2, 3);
        }else if(3 == dto.getMode()) {//已发布
        	if(0 == dto.getOnlineFileStatus()) {
        		where.and().lb();
        			where.in(SysFileManage::getFileStatus, 2, 3);
                where.or();
	                where.lb();
	                where.eq(SysFileManage::getFileStatus, 9);
	            	where.eq(SysFileManage::getFileMode, 2);
	            	where.rb();
                where.rb();
        	}else if(1 == dto.getOnlineFileStatus()) {
        		where.in(SysFileManage::getFileStatus, 2, 3);
        	}else if(2 == dto.getOnlineFileStatus()) {
        		where.eq(SysFileManage::getFileStatus, 9);
            	where.eq(SysFileManage::getFileMode, 2);
        	}
        	
        	order.orderByDesc(SysFileManage::getPublishTime);
        	order.orderByDesc(SysFileManage::getUpdateTime);
        	order.orderByDesc(SysFileManage::getCreateTime);
        }
        
        List<SysFileManage> list = srv.queryData(SysFileManage.class, where, order, dto.getPage());
        //分页无数据情况（当前页数据已删除），向前一页
        if(dto.getPage().getPage() > 1 && StringUtils.isEmpty(list)) {
        	dto.getPage().setPage(dto.getPage().getPage() - 1);
        	list = srv.queryData(SysFileManage.class, where, order, dto.getPage());
        }
        
        List<String> idList = new ArrayList<String>();
        for (SysFileManage obj : list) {
        	idList.add(obj.getId());
		}
        
        Map<String, String> verMap = getFileVerMap(idList);
		
        FileManageVo returnVo = new FileManageVo();
        returnVo.setPage(dto.getPage());
        
        List<Map<String, String>> mlist = getOnlineFolderMap(dto);
        Map<String, String> nameMap = mlist.get(0);
        Map<String, String> pathMap = mlist.get(1);
        
        Map<String, Map<String, String>> managerMap = getManagerMap(idList, currentUser.getId());
        
        List<FileManageVo> rlist = new ArrayList<FileManageVo>();
        for (SysFileManage obj : list) {
        	FileManageVo vo = ObjUtils.copyTo(obj, FileManageVo.class);
        	vo.setVersion(verMap.get(obj.getId()));
        	vo.setFolderName(nameMap.get(obj.getPid()));
        	vo.setFolderPath(pathMap.get(obj.getPid()));
        	
        	Map<String, String> tmap = managerMap.get(obj.getId());
        	if(tmap!=null) {
        		vo.setManageUserName(tmap.get("name"));
        		if("1".equals(tmap.get("mark"))) {
        			vo.setManageMark(1);
        		}
        	}else {
        		vo.setManageUserName("");
        		vo.setManageMark(0);
        	}
        	
//        	if(currentUser.getId().equals(obj.getManageUserId())) {
//        		vo.setManageMark(1);
//        	}
        	rlist.add(vo);
		}
        returnVo.setChildren(rlist);
        
        return returnVo;
	}
	private void setOrgList(List<SysOrgTreeData> allsuborgs, List<String> olist) {
		if(StringUtils.isNotEmpty(allsuborgs)) {
			for (SysOrgTreeData suborg : allsuborgs) {
				olist.add(suborg.getOrgcode());
				setOrgList(suborg.getChildren(), olist);
			}
		}
	}

	private Map<String, Map<String, String>> getManagerMap(List<String> idList, String uid) {
		Map<String, Map<String, String>> map = new HashMap<String, Map<String, String>>();

		if(StringUtils.isNotEmpty(idList)) {
			
			Where where = new Where();
			where.eq(SysOnlinefileManager::getTmused, 1);
			where.in(SysOnlinefileManager::getFileId, idList.toArray());
			Order order = Order.create();
			order.order(SysOnlinefileManager::getFileId);
			List<SysOnlinefileManager> list = srv.queryData(SysOnlinefileManager.class, where, order, null);
			
			
			for (SysOnlinefileManager obj : list) {
				if(map.containsKey(obj.getFileId())) {
					Map<String, String> tmap = map.get(obj.getFileId());
					tmap.put("name", tmap.get("name") + "," + obj.getManageUserName());
					if(uid.equals(obj.getManageUserId())) {
						tmap.put("mark", "1");
					}
				}else {
					Map<String, String> tmap = new HashMap<String, String>();
					tmap.put("name", obj.getManageUserName());
					if(uid.equals(obj.getManageUserId())) {
						tmap.put("mark", "1");
					}else {
						tmap.put("mark", "0");
					}
					map.put(obj.getFileId(), tmap);
				}
			}
		}
		
		return map;
	}

	/*
	 * 获取指定人员管理的所有文件ID数组
	 */
	private List<String> getManageFileIds(String id) {
		List<String> fileids = new ArrayList<String>();
		Where where = new Where();
		where.eq(SysOnlinefileManager::getTmused, 1);
		where.eq(SysOnlinefileManager::getManageUserId, id);
		
		List<SysOnlinefileManager> list = srv.queryData(SysOnlinefileManager.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			for (SysOnlinefileManager obj : list) {
				fileids.add(obj.getFileId());
			}
		}else {
			fileids.add("A");
		}
		
		return fileids;
	}

	/**
	 * @category 获取文件对应的应用版本
	 * @param idList
	 * @return
	 */
	private Map<String, String> getFileVerMap(List<String> idList) {
		Map<String, String> map = new HashMap<String, String>();
		if(StringUtils.isNotEmpty(idList)) {
			Where whereVer = new Where();
			whereVer.eq(SysOnlinefileVersion::getTmused, 1);
			whereVer.eq(SysOnlinefileVersion::getActiveMark, 1);
			whereVer.in(SysOnlinefileVersion::getFileId, idList.toArray());
			List<SysOnlinefileVersion> verList = srv.queryData(SysOnlinefileVersion.class, whereVer, null, null);
			
			for (SysOnlinefileVersion obj : verList) {
				map.put(obj.getFileId(), obj.getId());
			}
		}
		return map;
	}
	
	/**
	 * @category 获取文件已审核通过对应的应用版本
	 * @param idList
	 * @return
	 */
	private Map<String, String> getFilePassVerMap(List<String> idList) {
		Map<String, String> map = new HashMap<String, String>();
		if(StringUtils.isNotEmpty(idList)) {
			Where whereVer = new Where();
			whereVer.eq(SysOnlinefileVersion::getTmused, 1);
			whereVer.in(SysOnlinefileVersion::getFileId, idList.toArray());
			whereVer.notEmpty(SysOnlinefileVersion::getArchiveTime);
			Order order = Order.create();
			order.order(SysOnlinefileVersion::getFileId);
			order.orderByDesc(SysOnlinefileVersion::getArchiveTime);
			List<SysOnlinefileVersion> verList = srv.queryData(SysOnlinefileVersion.class, whereVer, order, null);
			
			for (SysOnlinefileVersion obj : verList) {
				if(!map.containsKey(obj.getFileId())) {
					map.put(obj.getFileId(), obj.getId());
				}
			}
		}
		return map;
	}

	/**
	 * 在线文档下载
	 */
	@Override
    public void onlineFileDownload(HttpServletResponse response, FileManageQueryDto dto) {
        String id = dto.getId();
        String ext = dto.getFileExt();
        String path = dto.getPath();
        
        SysFileManage file = srv.queryObjectById(SysFileManage.class, id);
        String name = file.getName();
        
        //获取版本信息
        Where whereVer = new Where();
        whereVer.eq(SysOnlinefileVersion::getTmused, 1);
        whereVer.eq(SysOnlinefileVersion::getFileId, id);
        whereVer.eq(SysOnlinefileVersion::getActiveMark, 1);
        Order orderVer = new Order();
        orderVer.orderByDesc(SysOnlinefileVersion::getArchiveTime);
        List<SysOnlinefileVersion> verList = srv.queryData(SysOnlinefileVersion.class, whereVer, orderVer, null); 
        
        String version = null;
        if(StringUtils.isNotEmpty(verList)) {
        	version = verList.get(0).getId();
        }
        
        String content = getOnlineContent(id, version);
        
//        SysUser currentUser = SysUserHolder.getCurrentUser();
//        String tenantid = "tenant_"+currentUser.getTenant_id();
//        path = "http://localhost:8081";// 改成读取配置
        List<String> rlist = new ArrayList<String>();
        List<String> list = Coms.getBl(content, "src=\"\\.?/files");
        for (String s : list) {
//        	rlist.add("src=\""+path+"/files/"+tenantid);
        	rlist.add("src=\""+path+"/files");
		}
        content = Coms.replaceBl(content, "src=\"\\.?/files", rlist);
        if(content==null || "".equals(content)) {
			content = " ";
		}else {
	        try {
                //标准化html 避免 特殊字符导致无法导出
                Document doc = Jsoup.parse(content);
                String html = doc.html();
                content = html;
            } catch (Exception e) {
                log.error("", e);
            }
		}
      //获取html内容，转pdf或word
        if("docx".equalsIgnoreCase(ext)) {
        	
        	InputStream fileSteam = getWordInputStream(content);//new ByteArrayInputStream(content.getBytes());
        	byte[] buffer = new byte[1024];
        	int len;
        	OutputStream outputStream = null;
        	try {
        		outputStream = response.getOutputStream();
        		response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(name, "UTF-8"));
        		response.setHeader("content-type", "application/octet-stream");
        		response.setContentType("application/octet-stream");
        		response.setCharacterEncoding("UTF-8");
        		while ((len = fileSteam.read(buffer)) > 0) {
        			outputStream.write(buffer, 0, len);
        		}
        	} catch (Exception e) {
        	}
        	try {
        		if (outputStream != null) {
        			outputStream.close();
        		}
        	} catch (Exception e) {
        	}
        	try {
        		fileSteam.close();
        	} catch (Exception e) {
        	}
        }else {
        	getPdfInputStream(content, response, name);
        }
        
    }
	
	private InputStream getWordInputStream(String html) {
		
		InputStream in = null;
		Charset cs = StandardCharsets.UTF_8;//html页面编码与解析编码需一致，否则导出word会产生乱码 Charset.forName("GBK");
		
		String str = " <!--[if gte mso 9]><xml><w:WordDocument><w:View>Print</w:View><w:TrackMoves>false</w:TrackMoves><w:TrackFormatting/><w:ValidateAgainstSchemas/><w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid><w:IgnoreMixedContent>false</w:IgnoreMixedContent><w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText><w:DoNotPromoteQF/><w:LidThemeOther>EN-US</w:LidThemeOther><w:LidThemeAsian>ZH-CN</w:LidThemeAsian><w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript><w:Compatibility><w:BreakWrappedTables/><w:SnapToGridInCell/><w:WrapTextWithPunct/><w:UseAsianBreakRules/><w:DontGrowAutofit/><w:SplitPgBreakAndParaMark/><w:DontVertAlignCellWithSp/><w:DontBreakConstrainedForcedTables/><w:DontVertAlignInTxbx/><w:Word11KerningPairs/><w:CachedColBalance/><w:UseFELayout/></w:Compatibility><w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel><m:mathPr><m:mathFont m:val='Cambria Math'/><m:brkBin m:val='before'/><m:brkBinSub m:val='--'/><m:smallFrac m:val='off'/><m:dispDef/><m:lMargin m:val='0'/> <m:rMargin m:val='0'/><m:defJc m:val='centerGroup'/><m:wrapIndent m:val='1440'/><m:intLim m:val='subSup'/><m:naryLim m:val='undOvr'/></m:mathPr></w:WordDocument></xml><![endif]-->";
        // 其中content为富文本编辑器生成的内容
        String h = " <html xmlns:v='urn:schemas-microsoft-com:vml'xmlns:o='urn:schemas-microsoft-com:office:office'xmlns:w='urn:schemas-microsoft-com:office:word'xmlns:m='http://schemas.microsoft.com/office/2004/12/omml'xmlns='http://www.w3.org/TR/REC-html40'  ";
        String content =h+"<head>"+"<meta http-equiv='Content-Type' content='text/html; charset=utf-8' />"+str+"</head><body>"+html+"</body> </html>";
        byte b[] = content.getBytes(cs);
        ByteArrayInputStream bais = null;
        ByteArrayOutputStream ostream = null;
        try {
        	bais = new ByteArrayInputStream(b);
        	POIFSFileSystem poifs = new POIFSFileSystem();
        	poifs.createDocument(bais, "WordDocument");
//			DirectoryEntry directory = poifs.getRoot();
//			directory.createDocument("WordDocument", bais);
			
            ostream = new ByteArrayOutputStream();
			poifs.writeFilesystem(ostream);
        	in = new ByteArrayInputStream(ostream.toByteArray());
		} catch (Exception e) {
		} finally {
			try {
				if(bais!=null) {
					bais.close();
				}
			} catch (IOException e) {
			}
			try {
				if(ostream!=null) {
					ostream.close();
				}
			} catch (IOException e) {
			}
		}
		return in;
	}
	
	private InputStream getPdfInputStream(String content, HttpServletResponse response, String name) {
		InputStream inputStream = null;
    	OutputStream outputStream = null;
    	Charset cs = StandardCharsets.UTF_8;
    	try {
//    		long startTime = System.currentTimeMillis();
    		//自定义水印
    		String waterMarkText =  "";
    		inputStream = new ByteArrayInputStream(content.getBytes(cs));//修改编码一致为UTF-8，避免出现乱
    		outputStream = response.getOutputStream();
    		response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(name, "UTF-8"));
    		response.setHeader("content-type", "application/octet-stream");
    		response.setContentType("application/octet-stream");
    		response.setCharacterEncoding("UTF-8");
    		//微软雅黑在windows系统里的位置如下，linux系统直接拷贝该文件放在linux目录下即可
    		//String fontPath = "src/main/resources/font/STHeiti Light.ttc,0";
    		String fontPath = getResourcesPath()+"font" + File.separator + "simsun.ttc,0";//"src/main/resources/font/simsun.ttc,0";//"C:/Windows/Fonts/simsun.ttc,0";//
    		HtmlToPdfUtils.convertToPdf(inputStream, waterMarkText, fontPath, outputStream);
//    		log.info("转换结束，耗时：{}ms",System.currentTimeMillis()-startTime);
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			if(inputStream!=null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if(outputStream!=null) {
				try {
					outputStream.close();
				} catch (Exception e2) {
					e2.printStackTrace();
				}				
			}
			
		}
		return null;
	}

	/**
	 * @category 根据版本号获取在线文档内容
	 * @param version
	 * @return
	 */
	private String getOnlineContent(String fileId, String version) {
		
		Map<String, List<SysOnlinefileNode>> map = new HashMap<String, List<SysOnlinefileNode>>();
		List<SysOnlinefileNode> list = getNodeList(fileId, version);
		for (SysOnlinefileNode node : list) {
			if(map.containsKey(node.getFid())) {
				map.get(node.getFid()).add(node);
			}else {
				List<SysOnlinefileNode> tlist = new ArrayList<SysOnlinefileNode>();
				tlist.add(node);
				map.put(node.getFid(), tlist);
			}
		}
		//按顺序的列表结果
		List<SysOnlinefileNode> sortlist = new ArrayList<SysOnlinefileNode>();
		List<SysOnlinefileNode> rootList = map.get("root");
		getSortList(map, rootList, sortlist);
		
		Map<String, SysOnlinefileContent> cmap = new HashMap<String, SysOnlinefileContent>();
		List<SysOnlinefileContent> clist = getVersionContentList(fileId, version);
		for (SysOnlinefileContent content : clist) {
			cmap.put(content.getNodeId(), content);
		}
		
		StringBuffer sb = new StringBuffer();
		
		for (SysOnlinefileNode node : sortlist) {
			SysOnlinefileContent content = cmap.get(node.getDid());
			if(content!=null && content.getNodeContent()!=null) {
				sb.append(content.getNodeContent());
			}
		}
		
		return sb.toString();
	}
	private List<SysOnlinefileNode> getNodeList(String fileId, String version) {
		Where where = new Where();
        where.eq(SysOnlinefileNode::getTmused, 1);
        where.eq(SysOnlinefileNode::getFileId, fileId);
        where.eq(SysOnlinefileNode::getVersionId, version);
        Order order = new Order();
        order.order(SysOnlinefileNode::getTmsort);
		List<SysOnlinefileNode> list = srv.queryData(SysOnlinefileNode.class, where, order, null);
		return list;
	}

	private List<SysOnlinefileContent> getVersionContentList(String fileId, String version) {
		Where where = new Where();
        where.eq(SysOnlinefileContent::getTmused, 1);
        where.eq(SysOnlinefileContent::getFileId, fileId);
        where.eq(SysOnlinefileContent::getVersionId, version);
		List<SysOnlinefileContent> list = srv.queryData(SysOnlinefileContent.class, where, null, null); 
		return list;
	}

	//按树形顺序的列表结果
	private void getSortList(Map<String, List<SysOnlinefileNode>> map, List<SysOnlinefileNode> rootList, List<SysOnlinefileNode> sortlist) {
		if(StringUtils.isNotEmpty(rootList)) {
			for (SysOnlinefileNode node : rootList) {
				sortlist.add(node);
				getSortList(map, map.get(node.getDid()), sortlist);
			}
		}
	}

	/**
	 * 保存大纲节点
	 */
	public TreeVo saveNode(FileManageQueryDto dto) {
		SysUser currentUser = SysUserHolder.getCurrentUser();
        Date nd = DateTimeUtils.getNowDate();
        
		String nodeId = dto.getId();
		SysOnlinefileNode node = null;
		
		//版本属性处理
		Where where = new Where();
		where.eq(SysOnlinefileVersion::getTmused, 1);
		where.eq(SysOnlinefileVersion::getFileId, dto.getFileId());
		Order order = Order.create();
		order.orderByDesc(SysOnlinefileVersion::getCreateTime);
		List<SysOnlinefileVersion> list = srv.queryData(SysOnlinefileVersion.class, where, order, null);
		SysOnlinefileVersion currVer = list.get(0);
		if(currVer!=null && new Integer(1).equals(currVer.getActiveArchiveMark())) {
			//创建新版本
			node = createNewVerWithNode(list, nodeId, dto);
			SysFileManage sfm = srv.queryObjectById(SysFileManage.class, currVer.getFileId());
			sfm.setFileStatus(1);
			srv.updateById(sfm);
		}else {
			if(StringUtils.isNotEmpty(nodeId)) {//更新
				node = srv.queryObjectById(SysOnlinefileNode.class, nodeId);
				node.setNodeName(dto.getNodeName());
				node.setLastUpdUser(currentUser.getRealName());
				node.setLastUpdTime(nd);
				if(-1 == dto.getOp()) {
					node.setTmused(0);
				}
				srv.update(node);
			}else {//添加
				node = new SysOnlinefileNode();
				node.setId(TMUID.getUID());
				node.setDid(node.getId());
				node.setFid(dto.getPid());
				node.setFileId(dto.getFileId());
				node.setVersionId(dto.getVersion());
				node.setAddVersion(dto.getVersion());
				node.setAddVersionNo(dto.getVersionSort());
				node.setNodeName(dto.getNodeName());
				node.setUpdMark(1);
				node.setLastUpdUser(currentUser.getRealName());
				node.setLastUpdTime(nd);
				node.setTmsort(dto.getMaxSort()+1);//添加时，前台将对应节点下的最大排序号返回
				node.setTmused(1);
				srv.insert(node);
				
				//添加节点内容
				SysOnlinefileContent content = new SysOnlinefileContent();
				content.setId(TMUID.getUID());
				content.setFileId(node.getFileId());
				content.setVersionId(node.getVersionId());
				content.setNodeId(node.getDid());
				content.setNodeContent("");
				content.setLastUpdateUserId(currentUser.getId());
				content.setLastUpdateUserName(currentUser.getRealName());
				content.setLastUpdateTime(nd);
				content.setTmsort(node.getTmsort());//添加时
				content.setTmused(1);
				srv.insert(content);
			}
		}
		
		
		return ObjUtils.copyTo(node, TreeVo.class);
	}
	/**
	 * 获取文件树形信息
	 */
	public TreeVo getFileTree(FileManageQueryDto dto) {
		TreeVo root = new TreeVo();
		
		Map<String, List<SysOnlinefileNode>> map = new HashMap<String, List<SysOnlinefileNode>>();
		List<SysOnlinefileNode> list = getNodeList(dto.getFileId(), dto.getVersion());
		for (SysOnlinefileNode node : list) {
			if(map.containsKey(node.getFid())) {
				map.get(node.getFid()).add(node);
			}else {
				List<SysOnlinefileNode> tlist = new ArrayList<SysOnlinefileNode>();
				tlist.add(node);
				map.put(node.getFid(), tlist);
			}
		}
		List<SysOnlinefileNode> rootList = map.get(null);
		if(StringUtils.isNotEmpty(rootList)) {
			root = ObjUtils.copyTo(rootList.get(0), TreeVo.class);
			getTreeNode(map, root);
		}
		
		return root;
	}
	private void getTreeNode(Map<String, List<SysOnlinefileNode>> map, TreeVo node) {
		List<SysOnlinefileNode> list = map.get(node.getDid());
		List<TreeVo> children = new ArrayList<TreeVo>();
		if(StringUtils.isNotEmpty(list)) {
			for (SysOnlinefileNode item : list) {
				TreeVo vo = ObjUtils.copyTo(item, TreeVo.class);
				children.add(vo);
				getTreeNode(map, vo);
			}
		}
		node.setChildren(children);
	}

	/**
	 * 获取节点内容
	 */
	public SysOnlinefileContent getNodeData(FileManageQueryDto dto) {
		String nodeId = dto.getNodeId();
		Where where = new Where();
        where.eq(SysOnlinefileContent::getTmused, 1);
        where.eq(SysOnlinefileContent::getNodeId, nodeId);
        where.eq(SysOnlinefileContent::getVersionId, dto.getVersion());
		List<SysOnlinefileContent> list = srv.queryData(SysOnlinefileContent.class, where, null, null);
		if(StringUtils.isNotEmpty(list)) {
			return list.get(0);
		}
		return null;
	}
	/**
	 * 保存节点内容
	 */
	public SysOnlinefileContent saveNodeData(FileManageQueryDto dto) {
		SysOnlinefileContent obj = null;
		//需要先确认版本信息，如果已经发布，则需要创建新版本，否则只需更新内容即可
		//版本属性处理
		Where where = new Where();
		where.eq(SysOnlinefileVersion::getTmused, 1);
		where.eq(SysOnlinefileVersion::getFileId, dto.getFileId());
		Order order = Order.create();
		order.orderByDesc(SysOnlinefileVersion::getCreateTime);
		List<SysOnlinefileVersion> list = srv.queryData(SysOnlinefileVersion.class, where, order, null);
		SysOnlinefileVersion currVer = list.get(0);
		if(currVer!=null && new Integer(1).equals(currVer.getActiveArchiveMark())) {
			//创建新版本
			obj = createNewVer(list, dto.getNodeId(), dto.getNodeContent());
			//更新文件状态
			SysFileManage sfm = srv.queryObjectById(SysFileManage.class, currVer.getFileId());
			sfm.setFileStatus(1);
			srv.updateById(sfm);
		}else {
			obj = srv.queryObjectById(SysOnlinefileContent.class, dto.getId());
			createUpdLog(obj, dto.getNodeContent(), currVer.getId());
			obj.setNodeContent(dto.getNodeContent());
			srv.updateById(obj);
			//更新节点状态
			List<SysOnlinefileNode> nodelist = getNodeList(currVer.getFileId(), currVer.getId());
			List<SysOnlinefileNode> newnodelist = new ArrayList<SysOnlinefileNode>(nodelist.size());
			
			for (SysOnlinefileNode node : nodelist) {
				
				if(obj.getNodeId().equals(node.getDid())) {
					node.setUpdMark(1);
					newnodelist.add(node);
				}
			}
	    	if(StringUtils.isNotEmpty(newnodelist)) {
	    		srv.updateByIdBatch(newnodelist);
	    	}
		}
		
		return obj;
	}
	/**
	 * @category 创建修改日志
	 * @param obj
	 * @param dto
	 */
	private void createUpdLog(SysOnlinefileContent obj, String contentstr, String version) {
		SysUser currentUser = SysUserHolder.getCurrentUser();
		Date nd = DateTimeUtils.getNowDate();
		
		
		//获取每个区域行数（<p>）
		List<String> oldList = Coms.getBl(obj.getNodeContent(), "<([^>\\s]+)(?:[^>]*?)>([\\s\\S]*?)<\\/\\1>|<([^>\\s]+)(?:[^>]*?)\\/>");
		List<String> newList = Coms.getBl(contentstr, "<([^>\\s]+)(?:[^>]*?)>([\\s\\S]*?)<\\/\\1>|<([^>\\s]+)(?:[^>]*?)\\/>");
		
		String updContent = new DiffString().getUpdateLog(oldList, newList, true);
		
		SysOnlinefileUpdLog ulog = new SysOnlinefileUpdLog();
		ulog.setId(TMUID.getUID());
		ulog.setNodeId(obj.getNodeId());
		ulog.setFileId(obj.getFileId());
		ulog.setVersionId(version);
		ulog.setTmused(1);
		ulog.setUpdTime(nd);
		ulog.setUpdUserId(currentUser.getId());
		ulog.setUpdUserName(currentUser.getRealName());
		ulog.setUpdContent(updContent);
		
		if(StringUtils.isNotEmpty(updContent)) {
			srv.insert(ulog);
		}
		
	}

	private SysOnlinefileNode createNewVerWithNode(List<SysOnlinefileVersion> list, String nodeId, FileManageQueryDto dto) {
		SysOnlinefileNode node = null;
		
		SysUser currentUser = SysUserHolder.getCurrentUser();
    	Date nd = DateTimeUtils.getNowDate();
    	String did = dto.getDid();
    	
		SysOnlinefileVersion currVer = list.get(0);
		currVer.setActiveMark(0);
		currVer.setActiveArchiveMark(0);
		srv.updateById(currVer);
		
		SysOnlinefileVersion newVer = new SysOnlinefileVersion();
		newVer = ObjUtils.copyTo(currVer, SysOnlinefileVersion.class);
		newVer.setId(TMUID.getUID());
		newVer.setActiveMark(1);
		newVer.setActiveArchiveMark(0);
		newVer.setArchiveTime(null);
		newVer.setPublishTime(null);
		newVer.setPublishUserId(null);
		newVer.setPublishUserName(null);
		srv.insert(newVer);
		
		//复制大纲，版本等属性调整，did与fid保持不变
		List<SysOnlinefileNode> nodelist = getNodeList(currVer.getFileId(), currVer.getId());
		List<SysOnlinefileNode> newnodelist = new ArrayList<SysOnlinefileNode>(nodelist.size());
		
		for (SysOnlinefileNode oldnode : nodelist) {
			SysOnlinefileNode newnode = ObjUtils.copyTo(oldnode, SysOnlinefileNode.class);
			newnode.setId(TMUID.getUID());
			newnode.setVersionId(newVer.getId());
			newnode.setAddVersion(null);
			newnode.setAddVersionNo(null);
			if(did.equals(newnode.getDid())) {
				newnode.setUpdMark(1);
			}else {
				newnode.setUpdMark(0);
			}
			newnode.setLastUpdUser(currentUser.getRealName());
			newnode.setLastUpdTime(nd);
			
			if(oldnode.getId().equals(nodeId)) {
				newnode.setNodeName(dto.getNodeName());
				if(-1 == dto.getOp()) {
					newnode.setTmused(0);
					node = newnode;
				}
			}
//			if(StringUtils.isNotEmpty(nodeId)) {//更新处理在插入数据时同步处理
//				newnode.setNodeName(dto.getNodeName());
//				if(-1 == dto.getOp()) {
//					newnode.setTmused(0);
//				}
//			}
			
			newnodelist.add(newnode);
		}
		if(-1 == dto.getOp() && node == null) {
			node = newnodelist.get(0);
		}
    	
    	srv.insertBatch(newnodelist, 50);
    	
    	//复制节点内容
    	List<SysOnlinefileContent> clist = getVersionContentList(currVer.getFileId(), currVer.getId());
    	List<SysOnlinefileContent> newcontentlist = new ArrayList<SysOnlinefileContent>(clist.size());
    	for (SysOnlinefileContent content : clist) {
    		SysOnlinefileContent newcontent = ObjUtils.copyTo(content, SysOnlinefileContent.class);
    		newcontent.setId(TMUID.getUID());
    		newcontent.setVersionId(newVer.getId());
    		newcontent.setLastUpdateUserId(currentUser.getId());
    		newcontent.setLastUpdateUserName(currentUser.getRealName());
    		newcontent.setLastUpdateTime(nd);
    		
    		newcontentlist.add(newcontent);
		}
    	
    	srv.insertBatch(newcontentlist, 50);
    	
    	if(StringUtils.isEmpty(nodeId)) {//添加
			node = new SysOnlinefileNode();
			node.setId(TMUID.getUID());
			node.setDid(node.getId());
			node.setFid(dto.getPid());
			node.setFileId(dto.getFileId());
			node.setVersionId(newVer.getId());
			node.setAddVersion(newVer.getId());
			node.setAddVersionNo(newVer.getTmsort());
			node.setNodeName(dto.getNodeName());
			node.setUpdMark(1);
			node.setLastUpdUser(currentUser.getRealName());
			node.setLastUpdTime(nd);
			node.setTmsort(dto.getMaxSort()+1);//添加时，前台将对应节点下的最大排序号返回
			node.setTmused(1);
			srv.insert(node);
			
			//添加节点内容
			SysOnlinefileContent content = new SysOnlinefileContent();
			content.setId(TMUID.getUID());
			content.setFileId(node.getFileId());
			content.setVersionId(node.getVersionId());
			content.setNodeId(node.getDid());
			content.setNodeContent("");
			content.setLastUpdateUserId(currentUser.getId());
			content.setLastUpdateUserName(currentUser.getRealName());
			content.setLastUpdateTime(nd);
			content.setTmsort(node.getTmsort());//添加时
			content.setTmused(1);
			srv.insert(content);
		}else {
//			node = srv.queryObjectById(SysOnlinefileNode.class, newNodeId);
//			node.setNodeName(dto.getNodeName());
//			node.setLastUpdUser(currentUser.getRealName());
//			node.setLastUpdTime(nd);
//			if(-1 == dto.getOp()) {
//				node.setTmused(0);
//			}
//			srv.update(node);
		}
		
		return node;
	}
	/**
	 * @category 更新节点内容创建新版本
	 * @param currVer
	 * @param nodeId
	 * @return
	 */
	private SysOnlinefileContent createNewVer(List<SysOnlinefileVersion> list, String nodeId, String contentstr) {
		SysUser currentUser = SysUserHolder.getCurrentUser();
    	Date nd = DateTimeUtils.getNowDate();
    	
		SysOnlinefileVersion currVer = list.get(0);
		currVer.setActiveMark(0);
		currVer.setActiveArchiveMark(0);
		srv.updateById(currVer);
		
		SysOnlinefileVersion newVer = new SysOnlinefileVersion();
		newVer = ObjUtils.copyTo(currVer, SysOnlinefileVersion.class);
		newVer.setId(TMUID.getUID());
		newVer.setActiveMark(1);
		newVer.setActiveArchiveMark(0);
		newVer.setArchiveTime(null);
		newVer.setPublishTime(null);
		newVer.setPublishUserId(null);
		newVer.setPublishUserName(null);
		srv.insert(newVer);
		
		//复制大纲，版本等属性调整，did与fid保持不变
		List<SysOnlinefileNode> nodelist = getNodeList(currVer.getFileId(), currVer.getId());
		List<SysOnlinefileNode> newnodelist = new ArrayList<SysOnlinefileNode>(nodelist.size());
		
		for (SysOnlinefileNode node : nodelist) {
			SysOnlinefileNode newnode = ObjUtils.copyTo(node, SysOnlinefileNode.class);
			newnode.setId(TMUID.getUID());
			newnode.setVersionId(newVer.getId());
			newnode.setAddVersion(null);
			newnode.setAddVersionNo(null);
			if(nodeId.equals(newnode.getDid())) {
				newnode.setUpdMark(1);
			}else {
				newnode.setUpdMark(0);
			}
			newnode.setLastUpdUser(currentUser.getRealName());
			newnode.setLastUpdTime(nd);
			newnodelist.add(newnode);
		}
    	
    	srv.insertBatch(newnodelist, 50);
    	
    	//复制节点内容
    	SysOnlinefileContent saveObj = null;
    	List<SysOnlinefileContent> clist = getVersionContentList(currVer.getFileId(), currVer.getId());
    	List<SysOnlinefileContent> newcontentlist = new ArrayList<SysOnlinefileContent>(clist.size());
    	for (SysOnlinefileContent content : clist) {
    		SysOnlinefileContent newcontent = ObjUtils.copyTo(content, SysOnlinefileContent.class);
    		newcontent.setId(TMUID.getUID());
    		newcontent.setVersionId(newVer.getId());
    		newcontent.setLastUpdateUserId(currentUser.getId());
    		newcontent.setLastUpdateUserName(currentUser.getRealName());
    		newcontent.setLastUpdateTime(nd);
    		if(nodeId.equals(newcontent.getNodeId())) {
    			createUpdLog(newcontent, contentstr, newVer.getId());
    			
    			newcontent.setNodeContent(contentstr);
    			saveObj = newcontent;
    		}
    		newcontentlist.add(newcontent);
		}
    	
    	srv.insertBatch(newcontentlist, 50);
		
		return saveObj;
	}

	/**
	 * 获取节点对比前后内容
	 */
	public List<String> getDiffNodeData(FileManageQueryDto dto) {
		List<String> list = new ArrayList<String>(2);
		
//		String fileId = dto.getFileId();
		String nodeId = dto.getNodeId();
		String version = dto.getVersion();
		String upversion = "";
		
		Boolean curr = false;
		List<SysOnlinefileVersion> vlist = getVerLog(dto);
		for (SysOnlinefileVersion ver : vlist) {
			if(version.equals(ver.getId())) {
				curr = true;
			}else if(curr) {
				upversion = ver.getId();
				break;
			}
		}
		
		Where where = new Where();
        where.eq(SysOnlinefileContent::getTmused, 1);
        where.eq(SysOnlinefileContent::getNodeId, nodeId);
        where.eq(SysOnlinefileContent::getVersionId, version);
		List<SysOnlinefileContent> clist = srv.queryData(SysOnlinefileContent.class, where, null, null);
		if(StringUtils.isNotEmpty(clist) && clist.get(0).getNodeContent()!=null) {
			list.add(clist.get(0).getNodeContent());
		}else {
			list.add("");
		}
		
		Where whereup = new Where();
		whereup.eq(SysOnlinefileContent::getTmused, 1);
		whereup.eq(SysOnlinefileContent::getNodeId, nodeId);
		whereup.eq(SysOnlinefileContent::getVersionId, upversion);
		List<SysOnlinefileContent> uplist = srv.queryData(SysOnlinefileContent.class, whereup, null, null);
		if(StringUtils.isNotEmpty(uplist) && clist.get(0).getNodeContent()!=null) {
			list.add(uplist.get(0).getNodeContent());
		}else {
			list.add("");
		}
		
		return list;
	}
	/**
	 * 在线文档发布
	 */
	public List<SysOnlinefileAudit> publishOnlineFile(FileManageQueryDto dto) {
		Boolean flag = true;
		SysUser currentUser = SysUserHolder.getCurrentUser();
		Date nd = DateTimeUtils.getNowDate();
		List<String> idList = Coms.StrToList(dto.getId(), ",");
		
		List<SysOnlinefileAudit> rlist = null;
		
		Where wheref = new Where();
		wheref.eq(SysFileManage::getTmused, 1);
		wheref.in(SysFileManage::getId, idList.toArray());
		
		List<SysFileManage> flist = srv.queryData(SysFileManage.class, wheref, null, null);
		
		for (SysFileManage file : flist) {
//			SysFileManage file = srv.queryObjectById(SysFileManage.class, dto.getId());
			boolean pass = StringUtils.isEmpty(file.getAuditFlowCode()) || "0".equals(file.getAuditFlowCode());
			if(pass) {
				file.setFileStatus(9);
				file.setUpdateUserId(currentUser.getId());
				file.setUpdateUserName(currentUser.getRealName());
				file.setLastUpdateTime(nd);
			}else {
				file.setFileStatus(2);
			}
			file.setPublishTime(nd);
			
			//版本属性处理
			Where where = new Where();
			where.eq(SysOnlinefileVersion::getTmused, 1);
			where.eq(SysOnlinefileVersion::getFileId, file.getId());
			Order order = Order.create();
			order.orderByDesc(SysOnlinefileVersion::getCreateTime);
			List<SysOnlinefileVersion> list = srv.queryData(SysOnlinefileVersion.class, where, order, null);
			
			if(pass) {
				file.setArchiveTime(nd);
				boolean isLast = true;
				for (SysOnlinefileVersion ver : list) {
					if(isLast) {
						ver.setActiveMark(1);
						ver.setActiveArchiveMark(1);
						ver.setPublishTime(nd);
						ver.setPublishUserId(currentUser.getId());
						ver.setPublishUserName(currentUser.getRealName());
						ver.setArchiveTime(nd);
						isLast = false;
					}else {
						ver.setActiveMark(0);
						ver.setActiveArchiveMark(0);
					}
				}
				srv.updateByIdBatch(list);
			}else {
				SysOnlinefileVersion ver = list.get(0);
				ver.setPublishTime(nd);
				ver.setPublishUserId(currentUser.getId());
				ver.setPublishUserName(currentUser.getRealName());
				srv.update(ver);
				
				//生成新流程数据ID
				SysOnlinefileAudit audit = dealPublishFileFlow(file, ver);
				
				file.setAuditFlowDataId(audit.getId());//流程数据ID保存为指定ID
				
//				createAuditFlow(file, audit);//调用审核流，创建审核流程，目前使用前台的调用
				
				if(rlist==null) {
					rlist = new ArrayList<SysOnlinefileAudit>();
				}
				rlist.add(audit);
			}
			
			flag = flag && srv.update(file) == 1;
		}
		
		return rlist;
	}
	/**
	 * @category 文件历史数据ID做处理，生成新流程数据ID
	 * @param file
	 * @param ver
	 * @return
	 */
	private SysOnlinefileAudit dealPublishFileFlow(SysFileManage file, SysOnlinefileVersion ver) {
		SysUser currentUser = SysUserHolder.getCurrentUser();
		//历史数据处理
//		Where where = new Where();
//		where.eq(SysOnlinefileAudit::getTmused, 1);
//		where.eq(SysOnlinefileAudit::getAuditStatus, 0);//未审核结束的数据
//		List<SysOnlinefileAudit> list = srv.queryData(SysOnlinefileAudit.class, where, null, null);
//		if(StringUtils.isNotEmpty(list)) {
//			for (SysOnlinefileAudit audit : list) {
//				audit.setAuditStatus(1);
//				audit.setAuditId("0");
//			}
//			srv.updateByIdBatch(list);
//		}
		
		SysOnlinefileAudit audit = new SysOnlinefileAudit();
		audit.setId(file.getAuditFlowCode()+":0:"+TMUID.getUID());//在线文档审核流程ID格式做调整
		audit.setFileId(file.getId());
		audit.setVersionId(ver.getId());
		audit.setFlowId(file.getAuditFlowCode());
		audit.setAuditStatus(1);
		audit.setTmused(1);
		audit.setCreateUserName(currentUser.getRealName());//提交人姓名
		audit.setFileName(file.getName());
		srv.insert(audit);
		
		return audit;
	}

//	private void createAuditFlow(SysFileManage file, SysOnlinefileAudit audit) {//后台审核流程调用，暂不使用，暂用前台
//		
//		
//	}

	/**
	 * 在线文档删除
	 */
	public Boolean deleteOnlineFile(FileManageQueryDto dto) {
		Boolean flag = true;
		List<String> idList = Coms.StrToList(dto.getId(), ",");
		
		Where wheref = new Where();
		wheref.eq(SysFileManage::getTmused, 1);
		wheref.in(SysFileManage::getId, idList.toArray());
		
		List<SysFileManage> flist = srv.queryData(SysFileManage.class, wheref, null, null);
		
		for (SysFileManage file : flist) {
			
//			SysFileManage file = srv.queryObjectById(SysFileManage.class, dto.getId());
			file.setTmused(0);
			file.setDelFlag(1);
			flag = flag && srv.update(file) == 1;
		}
		
		return flag;
	}
	/**
	 * 在线文档审核
	 */
	public Boolean auditOnlieFile(FileManageQueryDto dto) {
		Date nd = DateTimeUtils.getNowDate();
		SysFileManage file = srv.queryObjectById(SysFileManage.class, dto.getId());
		if(-1 == dto.getAuditStatus()) {
			file.setFileStatus(-1);
		}else {
			if(3 == dto.getOp()) {
				file.setFileStatus(9);//存档
				file.setLastUpdateTime(nd);
			}else {
				file.setFileStatus(3);//审核中
			}
		}
		if(new Integer(9).equals(file.getFileStatus())) {
			//版本属性处理
			Where where = new Where();
			where.eq(SysOnlinefileVersion::getTmused, 1);
			where.eq(SysOnlinefileVersion::getFileId, dto.getId());
			Order order = Order.create();
			order.orderByDesc(SysOnlinefileVersion::getCreateTime);
			List<SysOnlinefileVersion> list = srv.queryData(SysOnlinefileVersion.class, where, order, null);
			boolean isLast = true;
			for (SysOnlinefileVersion ver : list) {
				if(isLast) {
					ver.setActiveMark(1);
					ver.setActiveArchiveMark(1);
					isLast = false;
					file.setUpdateUserId(ver.getPublishUserId());//更新人为发布人
					file.setUpdateUserName(ver.getPublishUserName());
				}else {
					ver.setActiveMark(0);
					ver.setActiveArchiveMark(0);
				}
			}
			srv.updateByIdBatch(list);
		}
		return srv.update(file) == 1;
	}
//	/**
//	 * 获取个人数据列表
//	 */
//	public List<SysFileManage> queryUserFileList(FileManageQueryDto dto) {
//		//已有
//		return null;
//	}
	/**
	 * 获取修改日志
	 */
	public List<SysOnlinefileUpdLog> getUpdLog(FileManageQueryDto dto) {
		Where where = new Where();
        where.eq(SysOnlinefileUpdLog::getTmused, 1);
        where.eq(SysOnlinefileUpdLog::getFileId, dto.getFileId());
        where.eq(SysOnlinefileUpdLog::getVersionId, dto.getVersion());
//        where.eq(SysOnlinefileUpdLog::getNodeId, dto.getNodeId());
        Order order = Order.create();
        order.orderByDesc(SysOnlinefileUpdLog::getUpdTime);
		List<SysOnlinefileUpdLog> list = srv.queryData(SysOnlinefileUpdLog.class, where, order, null);
		return list;
	}
	/**
	 * 获取版本日志
	 */
	public List<SysOnlinefileVersion> getVerLog(FileManageQueryDto dto) {
		Where where = new Where();
        where.eq(SysOnlinefileVersion::getTmused, 1);
        where.eq(SysOnlinefileVersion::getFileId, dto.getFileId());
//        where.eq(SysOnlinefileUpdLog::getNodeId, dto.getNodeId());
        Order order = Order.create();
        order.orderByDesc(SysOnlinefileVersion::getCreateTime);
		List<SysOnlinefileVersion> list = srv.queryData(SysOnlinefileVersion.class, where, order, null);
		return list;
	}

	@Override
	public SysOnlinefileAudit getFileIdVersion(FileManageQueryDto dto) {
		SysOnlinefileAudit obj = srv.queryObjectById(SysOnlinefileAudit.class, dto.getId());
		return obj;
	}

	@Override
	public Boolean moveTreeNode(FileManageQueryDto dto) {
		List<TreeVo> nodeList = dto.getNodeList();
		if(StringUtils.isNotEmpty(nodeList)) {
			List<String> idList = new ArrayList<String>();
			Map<String, TreeVo> map = new HashMap<String, TreeVo>();
			for (TreeVo node : nodeList) {
				idList.add(node.getId());
				map.put(node.getId(), node);
			}
			
			Where where = new Where();
	        where.eq(SysOnlinefileNode::getTmused, 1);
//	        where.eq(SysOnlinefileNode::getVersionId, dto.getVersion());
	        where.in(SysOnlinefileNode::getId, idList.toArray());
	        List<SysOnlinefileNode> list = srv.queryData(SysOnlinefileNode.class, where, null, null);
	        
	        for (SysOnlinefileNode file : list) {
	        	TreeVo node = map.get(file.getId());
	        	if(node!=null) {
	        		file.setTmsort(node.getTmsort());
	        		file.setFid(node.getFid());
	        	}
			}
	        return srv.updateByIdBatch(list) == 1;
		}
		
		return null;
	}

	@Override
	public Boolean changeFileManager(FileManageQueryDto dto) {
		String id = dto.getId();
//		if(id.indexOf(",")!=-1) {
//			Where where = Where.create();
//	        where.in(SysFileManage::getId, Coms.StrToList(id, ",").toArray());
//	        List<SysFileManage> list = srv.queryData(SysFileManage.class, where, null, null);
//	        if(StringUtils.isNotEmpty(list)) {
//	        	for (SysFileManage file : list) {
//	        		file.setManageUserId(dto.getNewId());
//	    			file.setManageUserName(dto.getName());
//				}
//	        	return srv.updateBatch(list, 50) == 1;
//	        }
//	        return false;
//		}else {
//			SysFileManage file = srv.queryObjectById(SysFileManage.class, id);
//			file.setManageUserId(dto.getNewId());
//			file.setManageUserName(dto.getName());
//			return srv.update(file) == 1;
//		}
		
		List<String> fileIdList = Coms.StrToList(id, ",");
		
		List<Map<String, String>> list = new ArrayList<Map<String,String>>();
		List<String> uids = Coms.StrToList(dto.getNewId(), ",");
		List<String> unames = Coms.StrToList(dto.getName(), ",");
		for (int i = 0, il=uids.size(); i < il; i++) {
			String uid = uids.get(i);
			String uname = unames.get(i);
			Map<String, String> map = new HashMap<String, String>();
			map.put("id", uid);
			map.put("name", uname);
			list.add(map);
		}
		
		//删除原有文件的管理人（避免造成数据冗余，物理删除）
		Where where = Where.create();
        where.in(SysOnlinefileManager::getFileId, fileIdList.toArray());
        srv.delete(SysOnlinefileManager.class, where);
		
		//添加新文件管理人
        List<SysOnlinefileManager> addList = new ArrayList<SysOnlinefileManager>();
        for (Map<String, String> tmap : list) {
        	String manageUserId = tmap.get("id");
        	String manageUserName = tmap.get("name");
        	
        	for (String fileId : fileIdList) {
        		SysOnlinefileManager obj = new SysOnlinefileManager();
        		obj.setId(TMUID.getUID());
        		obj.setTmused(1);
        		obj.setManageUserId(manageUserId);
        		obj.setManageUserName(manageUserName);
        		obj.setFileId(fileId);
				obj.setFileId(fileId);
				addList.add(obj);
			}
		}
        
		if(StringUtils.isNotEmpty(addList)) {
			return 1 == srv.insertBatch(addList, 50);
		}else {
			return false;
		}
		
	}
}
