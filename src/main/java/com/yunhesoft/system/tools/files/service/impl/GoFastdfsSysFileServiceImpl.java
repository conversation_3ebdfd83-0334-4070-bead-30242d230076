package com.yunhesoft.system.tools.files.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;
import com.yunhesoft.system.tools.files.service.ISysFileService;
import com.yunhesoft.system.tools.files.tools.SysFileServUtils;
import com.yunhesoft.tmtools.JwtUser;
import com.yunhesoft.tmtools.TokenUtils;

import lombok.extern.log4j.Log4j2;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * go-FastDSF 文件上传类
 *
 * <AUTHOR>
 * @since 2023.11.15
 */
@Log4j2
@Service
@ConditionalOnProperty(value = "file.upload.mode", havingValue = "gofast")
public class GoFastdfsSysFileServiceImpl implements ISysFileService {

    // 文件服务器地址 eg:http://*************:8080
    @Value("${file.gofast.url:}")
    private String url;

    // 组名称 eg:group1
    @Value("${file.gofast.group:group1}")
    private String group;

    /**
     * token超期时间
     */
    private static final long TokenExpireTime = 60000; // 1分钟

    private static final String AuthToken = "auth_token";

    private String getUrl() {
        if (StringUtils.isEmpty(url)) {
            log.error("请设置go-fastdfs服务地址，参数名称：file.gofast.url");
            return "";
        } else {
            // url = "http://192.168.99.28:8080";
            return url + "/" + group;
        }
    }

    /**
     * 获得token认证
     *
     * @return
     */
    private String getToken() {
        String token = "";
        SysUser sysUser = SysUserHolder.getCurrentUser();
        if (sysUser != null) {
            JwtUser juser = JwtUser.create(sysUser.getTenant_id(), sysUser.getEuid(), sysUser.getId(),
                    sysUser.getUserName(), sysUser.getRealName(), "");
            juser.setExpire(TokenExpireTime);// 过期时间
            token = TokenUtils.createToken(juser);
        }
        return token;
    }

    /**
     * 上传地址
     */
    private String getUploadPath() {
        // http://*************:8080/group1/upload
        return this.getUrl() + "/upload";
    }

    /**
     * 删除地址
     *
     * @return
     */
    private String getDeletePath() {
        return this.getUrl() + "/delete";
    }

    /**
     * 上传文件
     */
    @Override
    public String uploadFile(MultipartFile file, SysFilesInfo fileInfo) {
        String result = null;
        File tempFile = null;
        try {
            String fileName = "";
            RequestBody requestBody = null;
            if ("image/jpeg".equalsIgnoreCase(file.getContentType())
                    && "blob".equalsIgnoreCase(file.getOriginalFilename())) {
                tempFile = this.getTempFile(file, fileInfo);
                fileName = tempFile.getName();// 生成临时文件
                requestBody = this.getRequestBody(tempFile);
            } else {
                fileName = fileInfo.getNewFileName() + "." + fileInfo.getFileExt();
                requestBody = this.getRequestBody(file);
            }
            result = this.upload(fileName, requestBody);
            if (result != null) {
                // System.out.println(result);
                JSONObject rtnData = JSONObject.parseObject(result);
                // JSONObject rtnData = rtnObj.getJSONObject("data");
                fileInfo.setFileMd5(rtnData.getString("md5"));
                fileInfo.setFileUrl(rtnData.getString("src"));// 网络地址，不带ip
                // fileInfo.setFileAddress(rtnData.getString("path"));// 路径
                fileInfo.setFileServ("gofast");
                Map<String, String> mp = getFileName(rtnData.getString("src"));
                fileInfo.setNewFileName(mp.get("name"));
                // 返回值
//					{
//					    "domain": "http://*************:8080",
//					    "md5": "86e9a0b9d709f4b667e50a67d8f41084",
//					    "mtime": 1700098131,
//					    "path": "/group1/default/20231116/09/28/3/企业微信截图_16981089863242.png",
//					    "retcode": 0,
//					    "retmsg": "",
//					    "scene": "default",
//					    "scenes": "default",
//					    "size": 51251,
//					    "src": "/group1/default/20231116/09/28/3/企业微信截图_16981089863242.png",
//					    "url": "http://*************:8080/group1/default/20231116/09/28/3/企业微信截图_16981089863242.png?name=%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_16981089863242.png\u0026download=1"
//					  }

            } else {
                // log.error("文件上传错误");
            }
        } catch (Exception e) {
            // log.error("文件上传错误", e);
            throw new RuntimeException("文件上传失败，失败原因：" + e.getMessage());
        } finally {
            if (tempFile != null) {
                try {
                    tempFile.deleteOnExit();
                } catch (Exception ex) {
                    log.error("临时文件删除错误", ex);
                }
            }
        }
        return result;
    }

    /**
     * 根据url地址获得文件名
     *
     * @param url
     * @return
     */
    Map<String, String> getFileName(String url) {
        Map<String, String> map = new HashMap<String, String>();
        if (StringUtils.isNotEmpty(url)) {
            String[] ary = url.split("\\/");
            if (ary.length > 0) {
                String s = ary[ary.length - 1];
                map.put("fullname", s);

                String[] ary2 = s.split("\\.");
                if (ary2.length > 1) {
                    map.put("ext", ary2[1]);// 扩展名
                    map.put("name", ary2[0]);// 扩展名
                } else {
                    map.put("name", s);// 扩展名
                }
            }
        }
        return map;
    }

    /**
     * 用原始的客户端文件上传
     *
     * @param multipFile
     * @return
     * @throws IOException
     */
    RequestBody getRequestBody(MultipartFile multipFile) throws IOException {
        return MultipartBody.create(MediaType.parse("multipart/form-data;charset=utf-8"), multipFile.getBytes());
    }

    /**
     * 生成临时文件上传
     *
     * @param file
     * @return
     */
    RequestBody getRequestBody(File file) {
        return MultipartBody.create(MediaType.parse("multipart/form-data;charset=utf-8"), file);
    }

    /**
     * 生成临时文件（注意，使用后要调用删除方法，删除临时文件）
     *
     * @param multipFile
     * @param fileInfo
     * @return
     */
    private File getTempFile(MultipartFile multipFile, SysFilesInfo fileInfo) {
        File tempFile = null;
        try {
            tempFile = File.createTempFile(fileInfo.getNewFileName(), "." + fileInfo.getFileExt());
            multipFile.transferTo(tempFile);
        } catch (Exception e) {
            log.error("", e);
        }
        return tempFile;
    }

    /**
     * 文件上传
     *
     * @param fileName
     * @param requestBody
     * @return
     */
    private String upload(String fileName, RequestBody requestBody) throws Exception {
        String result = null;

        String url = getUploadPath();// 上传地址
        if (StringUtils.isNotEmpty(url)) {
            String scene = this.getScene();// 场景
            String token = this.getToken();// auth_token
            MultipartBody multipartBody = new MultipartBody.Builder().setType(MultipartBody.FORM)
                    .addFormDataPart("file", fileName, requestBody)
                    .addFormDataPart("output", "json")
                    .addFormDataPart("scene", scene)
                    .addFormDataPart(AuthToken, token)
                    .build();
            Request request = new Request.Builder().url(getUploadPath()).post(multipartBody).build();//.addHeader(AuthToken, "Bearer " + token)
            OkHttpClient httpClient = new OkHttpClient();
            Response response = httpClient.newCall(request).execute();
            try {
                if (response.isSuccessful()) {
                    ResponseBody body = response.body();
                    if (body != null) {
                        result = body.string();
                        return result;
                    }
                } else {
                    log.error("文件上传失败,失败原因：" + response.message());
                }
            } catch (Exception e) {
                log.error("文件上传失败", e);
            } finally {
                if (response != null) {
                    response.close();
                }
            }
        }
        return result;
    }

    /**
     * 获得文件上传场景，用于区分多租户，文件放在同一文件夹下
     *
     * @return
     */
    private String getScene() {
        String scene = getTenantId();
        if (StringUtils.isEmpty(scene)) {
            return "default";
        } else {
            return scene;
        }
    }

    /**
     * 获得租户id
     *
     * @return
     */
    private String getTenantId() {
        if (MultiTenantUtils.enalbe()) {
            // 多租户
            SysUser currentUser = SysUserHolder.getCurrentUser();
            if (currentUser != null) {
                return currentUser.getTenant_id();
            } else {
                throw new RuntimeException("多租户模式下无法获取租户ID，请登录后再上传文件！");
            }
        }
        return null;
    }

    /**
     * 删除文件
     */
    @Override
    public void deleteFile(SysFilesInfo fileInfo) {
        try {
            if (fileInfo == null) {
                return;
            }
            if (this.multiTenantCheck(fileInfo)) {// 多账户模式下的文件访问权限校验
                String md5 = fileInfo.getFileMd5();// 获取md5值
                if (StringUtils.isNotEmpty(md5)) {
                    OkHttpClient httpClient = new OkHttpClient();
                    MultipartBody multipartBody = new MultipartBody.Builder().setType(MultipartBody.FORM)
                            .addFormDataPart("md5", md5).addFormDataPart(AuthToken, this.getToken()).build();
                    // .addFormDataPart("path", path)
                    Request request = new Request.Builder().url(this.getDeletePath()).post(multipartBody).build();
                    httpClient.newCall(request).execute();
                } else {
                    log.error(fileInfo.getId() + ",删除文件错误，MD5值为空");
                }
            } else {
                log.error("无文件删除权限，" + fileInfo.getFileUrl() + "," + JSON.toJSONString(SysUserHolder.getCurrentUser()));
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }

    @Override
    public void deleteFileBatch(List<SysFilesInfo> list) {
        list.forEach(this::deleteFile);
    }

    /**
     * 获得文件网络地址
     *
     * @param fileInfo
     * @return
     */
    private String getWebUrl(SysFilesInfo fileInfo) {
        String url = "";
        if (fileInfo != null && "gofast".equals(fileInfo.getFileServ())) {
            url = this.url + fileInfo.getFileUrl();
        }
        return url;
    }

    @Override
    public void filePreview(SysFilesInfo fileInfo, HttpServletResponse response) {
        if (fileInfo != null) {
            InputStream fileIs = null;
            try {
                fileIs = getFileSteam(fileInfo);
            } catch (Exception e) {
                log.error("系统找不到图像文件");
                return;
            }
            SysFileServUtils.filePreview(fileIs, response);
        }
    }

    /**
     * 文件下载
     *
     * @param fileInfo
     * @param response
     * @throws Exception
     */
    public void download(SysFilesInfo fileInfo, HttpServletResponse response) throws Exception {
        if (fileInfo == null) {
            return;
        }
        if (this.multiTenantCheck(fileInfo)) {// 多账户模式下的文件访问权限校验
            InputStream stream = this.getFileSteam(fileInfo);
            byte[] buffer = new byte[1024];
            int len;
            OutputStream outputStream = response.getOutputStream();
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileInfo.getOldFileName(), "UTF-8"));
            response.setHeader("content-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            while ((len = stream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.close();
            stream.close();
            return;
        } else {
            throw new RuntimeException(
                    "无文件访问权限，" + fileInfo.getFileUrl() + "," + JSON.toJSONString(SysUserHolder.getCurrentUser()));
        }
    }

    @Override
    public MultipartFile getMultipartFile(SysFilesInfo fileInfo) {
        InputStream inputStream = getFileSteam(fileInfo);
        if (inputStream != null) {
            return SysFileServUtils.getMultipartFile(inputStream, fileInfo.getOldFileName());
        } else {
            return null;
        }
    }

    /**
     * 上传本地文件到文件服务器(同步历史数据专用)
     *
     * @return
     */
    @Override
    public boolean uploadLocalFilesToFileSystem(List<SysFilesInfo> list) {
        return false;
    }

    @Override
    public List<String> getFileNameList(String path) {
        return null;
    }

    @Override
    public InputStream getFileSteam(SysFilesInfo fileInfo) {
        InputStream fileIs = null;
        if (fileInfo != null) {
            String webUrl = getWebUrl(fileInfo);
            // Map<String, String> mp = new HashMap<String, String>();
            // mp.put("auth_token", token);
            if (StringUtils.isNotEmpty(webUrl)) {
                webUrl += "?" + AuthToken + "=" + this.getToken();
                if (this.multiTenantCheck(fileInfo)) {// 多账户模式下的文件访问权限校验
                    try {
                        fileIs = SysFileServUtils.getWebFileSteam(webUrl);
                    } catch (Exception e) {
                        log.error("系统找不到图像文件");
                    }
                } else {
                    log.error("无文件访问权限，" + webUrl + "," + JSON.toJSONString(SysUserHolder.getCurrentUser()));
                }
            } else {
                log.error("系统找不到图像文件地址");
            }
        }
        return fileIs;
    }

    /**
     * 多租户模式下文件校验，防止跨租户访问文件
     *
     * @param fileInfo
     * @return
     */
    private boolean multiTenantCheck(SysFilesInfo fileInfo) {
        if (MultiTenantUtils.enalbe()) {// 多租户
            String tid = getTenantId();// 租户id
            String url = fileInfo.getFileUrl();
            if (url != null && tid != null) {
//				if (tid == null) {
//					tid = "default";
//				}
//				 /group1/default/20231120/14/54/3/img_17004632591434048554613070826656.jpg
                String[] ary = url.split("\\/");
                if (ary.length > 2) {
                    if (ary[2].equals(tid)) {// 判断url 路径中的场景是否与租户id相同
                        return true;
                    }
                }
            }
            return false;
        } else {
            return true;
        }
    }

}
