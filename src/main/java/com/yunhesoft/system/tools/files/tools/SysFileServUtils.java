package com.yunhesoft.system.tools.files.tools;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.xmlbeans.impl.common.IOUtil;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;

import lombok.extern.log4j.Log4j2;

/**
 * 文件上传服务工具类
 * 
 * <AUTHOR>
 *
 */
@Log4j2
public class SysFileServUtils {

	public static MultipartFile getMultipartFile(SysFilesInfo fileInfo) {
		String fileAddress = fileInfo == null ? null : fileInfo.getFileAddress();
		if (StringUtils.isEmpty(fileAddress)) {
			return null;
		}
		MultipartFile result = null;
		try {
			File file = new File(fileAddress);
			FileInputStream input = new FileInputStream(file);
			result = new MockMultipartFile(file.getName(), file.getName(), Files.probeContentType(file.toPath()),
					input);
		} catch (Exception e) {
			log.error("", e);
		}
		return result;
	}

	public static MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
		// InputStream inputStream = getFileSteam(fileInfo);
		FileItem fileItem = createFileItem(inputStream, fileName);
		// CommonsMultipartFile是feign对multipartFile的封装，但是要FileItem类对象
		try {
			return new CommonsMultipartFile(fileItem);
		} catch (Exception e) {
			log.error("", e);
		}
		return null;
	}

	public static InputStream getFileSteam(SysFilesInfo fileInfo) {
		String fileAddress = fileInfo == null ? null : fileInfo.getFileAddress();
		if (StringUtils.isEmpty(fileAddress)) {
			return null;
		}
		File file = new File(fileAddress);
		InputStream result = null;
		if (file.exists()) {
			try {
				result = Files.newInputStream(file.toPath());
			} catch (Exception e) {
				log.error("", e);
			}
		}
		return result;
	}

	public static void filePreview(InputStream fileIs, HttpServletResponse response) {
		try {
			int i = fileIs.available(); // 得到文件大小
			byte data[] = new byte[i];
			fileIs.read(data); // 读数据
			response.setContentType("image/*"); // 设置返回的文件类型
			OutputStream outStream = response.getOutputStream(); // 得到向客户端输出二进制数据的对象
			outStream.write(data); // 输出数据
			outStream.flush();
			outStream.close();
		} catch (Exception e) {
			log.error("", e);
		} finally {
			if (fileIs != null) {
				try {
					fileIs.close();
				} catch (IOException e) {
					log.error("", e);
				}
			}
		}
	}

	public static void filePreview(SysFilesInfo fileInfo, HttpServletResponse response) {
		FileInputStream fileIs = null;
		try {
			fileIs = new FileInputStream(fileInfo.getFileAddress());
		} catch (Exception e) {
			log.error("系统找不到图像文件");
			return;
		}
		filePreview(fileIs, response);
	}

	/**
	 * 根据网络文件路径获取文件流
	 * 
	 * @param filePath 网络地址
	 * @return
	 * @throws Exception
	 */
	public static InputStream getWebFileSteam(String filePath, Map<String, String> propertyMap) throws Exception {
		HttpURLConnection httpURLConnection = null;
		ByteArrayOutputStream  outputStream = new ByteArrayOutputStream();
		ByteArrayInputStream  newInputStream = null;
		try {
			String address = filePath.replaceAll(" ", "%20");
			// 统一资源并将filePath中的空格替换为特殊符号
			URL url = new URL(address);
			// 连接类的父类,抽象类
			URLConnection urlConnection = url.openConnection();
			// http的连接类
			httpURLConnection = (HttpURLConnection) urlConnection;
			// 设置超时时间
			httpURLConnection.setConnectTimeout(1000 * 5);
			// 设置请求方式,默认是GET
			httpURLConnection.setRequestMethod("POST");
			if (StringUtils.isNotEmpty(propertyMap)) {
				for (String k : propertyMap.keySet()) {// 传递参数
					httpURLConnection.setRequestProperty(k, propertyMap.get(k));
				}
			}
			// 设置字符编码
			httpURLConnection.setRequestProperty("Charset", "UTF-8");
			// 打开到此 URL 引用的资源的通信链接(如果尚未建立这样的连接)
			httpURLConnection.connect();
			InputStream inputStream = httpURLConnection.getInputStream();
			byte[] buffer = new byte[4096];
			int bytesRead = -1;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}
			newInputStream = new ByteArrayInputStream(outputStream.toByteArray());
			return newInputStream;
		} catch (Exception e) {
			// log.error("", e);
			throw e;
		} finally {
			outputStream.close();
			if(newInputStream!=null){
				newInputStream.close();
			}
		}
	}

	/**
	 * 根据网络文件路径获取文件流
	 * 
	 * @param filePath 网络地址
	 * @return
	 * @throws Exception
	 */
	public static InputStream getWebFileSteam(String filePath) throws Exception {
		return getWebFileSteam(filePath, null);
	}

	/**
	 * FileItem类对象创建
	 *
	 * @param inputStream inputStream
	 * @param fileName    fileName
	 * @return FileItem
	 */
	public static FileItem createFileItem(InputStream inputStream, String fileName) {
		FileItemFactory factory = new DiskFileItemFactory(16, null);
		String textFieldName = "file";
		FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
		int bytesRead = 0;
		byte[] buffer = new byte[8192];
		OutputStream os = null;
		// 使用输出流输出输入流的字节
		try {
			os = item.getOutputStream();
			while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			}
			inputStream.close();
		} catch (IOException e) {
			log.error("Stream copy exception", e);
			throw new IllegalArgumentException("文件转换失败");
		} finally {
			if (os != null) {
				try {
					os.close();
				} catch (IOException e) {
					log.error("Stream close exception", e);

				}
			}
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					log.error("Stream close exception", e);
				}
			}
		}

		return item;
	}

}
