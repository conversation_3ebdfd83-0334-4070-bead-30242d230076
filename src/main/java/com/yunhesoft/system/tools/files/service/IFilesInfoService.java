package com.yunhesoft.system.tools.files.service;

import java.io.File;
import java.io.InputStream;
import java.util.List;

import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.web.multipart.MultipartFile;

import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.tools.files.entity.dto.ExcelQuery;
import com.yunhesoft.system.tools.files.entity.dto.WordParamDto;
import com.yunhesoft.system.tools.files.entity.dto.WordQueryDto;
import com.yunhesoft.system.tools.files.entity.dto.WordSaveDto;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;
import com.yunhesoft.system.tools.files.entity.po.WordExpDs;
import com.yunhesoft.system.tools.files.entity.vo.FileInfo;

import javax.servlet.http.HttpServletResponse;

/**
 * 上传文件信息Service接口
 *
 * <AUTHOR>
 * @date 2021-05-06
 */
public interface IFilesInfoService {
    /**
     * 查询上传文件信息
     *
     * @param id 上传文件信息ID
     * @return 上传文件信息
     */
    public SysFilesInfo selectFilesInfoById(String id);

    /**
     * 查询上传文件信息列表
     *
     * @param filesInfo 上传文件信息
     * @return 上传文件信息集合
     */
    public Res<List<SysFilesInfo>> selectFilesInfoList(SysFilesInfo filesInfo, Pagination<?> page);

    /**
     * 新增上传文件信息
     *
     * @param filesInfo 上传文件信息
     * @return 结果
     */
    public int insertFilesInfo(SysFilesInfo filesInfo);

    /**
     * 修改上传文件信息
     *
     * @param filesInfo 上传文件信息
     * @return 结果
     */
    public int updateFilesInfo(SysFilesInfo filesInfo);

    /**
     * 批量上传文件信息
     *
     * @param list 上传文件信息
     * @return 结果
     */
    public int updateFilesInfos(List<SysFilesInfo> list);

    /**
     * 批量删除上传文件信息
     *
     * @param ids 需要删除的上传文件信息ID
     * @return 结果
     */
    public int deleteFilesInfoByIds(String[] ids);

    /**
     * 删除上传文件信息信息
     *
     * @param id 上传文件信息ID
     * @return 结果
     */
    public int deleteFilesInfoById(String id);

    /**
     * 上传文件
     *
     * @param file
     * @param moduleCode
     * @param fileType   文件类型 : img:保存图片文件
     * @return
     */
    public SysFilesInfo saveFilesImg(MultipartFile file, String moduleCode);

    /**
     * 上传文件
     *
     * @param file
     * @param moduleCode
     * @param fileType   文件类型 : all:根据文件扩展名保存
     * @return
     */
    public SysFilesInfo saveFiles(MultipartFile file, String moduleCode);

    /**
     * 上传文件
     *
     * @param file
     * @param moduleCode
     * @param fileType   文件类型 : all:根据文件扩展名保存，img:保存图片文件
     * @return
     */
    public SysFilesInfo saveFiles(MultipartFile file, String moduleCode, String fileType);

    /**
     * 上传文件
     */
    public List<SysFilesInfo> saveFiles(MultipartFile[] files, String moduleCode);

    /**
     * 上传文件
     */
    public List<SysFilesInfo> saveFiles(MultipartFile[] files, String moduleCode, String fileType);

    /**
     * 上传文件
     *
     * @param isPublic 0私有 1公共  此属性主要针对多租户，默认私有
     */
    List<SysFilesInfo> saveFiles(MultipartFile[] files, String moduleCode, String fileType, int isPublic);

    /**
     * 上传文件
     *
     * @param isPublic 0私有 1公共  此属性主要针对多租户，默认私有
     */
    List<SysFilesInfo> saveFiles(MultipartFile[] files, String moduleCode, String fileType, String rootUrl);

    /**
     * 上传文件
     *
     * @param isPublic 0私有 1公共  此属性主要针对多租户，默认私有
     */
    List<SysFilesInfo> saveFiles(MultipartFile[] files, String moduleCode, String fileType, String rootUrl, int isPublic);

    /**
     * 获取上传文件类型
     */
    public String getFileType();

    /**
     * 获取上传文件类型文本
     */
    public String getFileTypeStr();

    /**
     * 获取上传文件大小M
     */
    public Integer getFileSize();

    /**
     * 查询上传文件信息列表
     *
     * @param filesInfo 上传文件信息
     * @return 上传文件信息集合
     */
    public Res<List<FileInfo>> selectFilesInfoListByIds(String ids);

    /**
     * 根据网络地址获取上传文件数据对象
     */
    public SysFilesInfo selectFilesInfoByUrl(String weburl);

    /**
     * 根据模板json生成word文件
     */
    public XWPFDocument getExportWordFromTplJson(String tplId);

    public XWPFDocument getExportWordFromTplJson(String tplId, String tplName);

    List<SysFilesInfo> getFilesInfoListByIds(List<String> idList);

    public void updatePicWH(File picFile, SysFilesInfo obj);

    public void updatePicWH(String address, SysFilesInfo obj);

    public NiceXWPFDocument exportWordFile(String param, String dataId, String tplId, List<String> tempWordList,
                                           List<String> signatures);

    /**
     * @param dto
     * @return
     * @category 中小企业诊断报告导出
     */
    public NiceXWPFDocument exportWordFile(WordParamDto dto);

    /**
     * 清除临时word模板
     */
    public void clearTempWordFile(List<String> tempWordList);

    /**
     * 保存替换数据
     */
    public boolean saveReplaceCharData(WordSaveDto param);

    /**
     * 列表查询
     *
     * @param queryParam 检索条件
     * @param page       分页信息
     * @return
     */
    public List<WordExpDs> queryWordList(WordQueryDto queryParam, Pagination<?> page);

    public List<WordExpDs> queryDownWordPara(WordQueryDto queryParam);

    public XSSFWorkbook getExportExcelFromTplJson(String tplId, String tplName);

    public XSSFWorkbook exportExcelFile(ExcelQuery bean);


    /**
     * 下载文件
     *
     * @param fileId   文件id
     * @param response
     */
    void downloadFile(String fileId, HttpServletResponse response);

    /**
     * 下载文件
     *
     * @param fileInfo 文件对象
     * @param response
     */
    void downloadFile(SysFilesInfo fileInfo, HttpServletResponse response);


    void downloadFileByUrl(String fileUrl, HttpServletResponse response);

    /**
     * 删除文件
     *
     * @param fileId 文件id
     */
    boolean deleteFile(String fileId);

    /**
     * 删除文件
     *
     * @param fileUrl 文件url
     */
    boolean deleteFileByUrl(String fileUrl);

    /**
     * 批量删除文件
     *
     * @param ids 文件id
     */
    int deleteFile(String[] ids);

    InputStream getFileSteam(String id);


    /**
     * 预览
     *
     * @param fileUrl
     * @return
     */
    void filePreview(String fileUrl, HttpServletResponse response);

    /**
     * 上传本地文件到文件服务器(同步历史数据专用)
     *
     * @return
     */
    boolean uploadLocalFilesToFileSystem();


    SysFilesInfo getFileInfoByUrl(String fileUrl);

    List<SysFilesInfo> getFilesByModule(String moudleCode);

    List<SysFilesInfo> getUrlPathFiles(String urlPath);


}
