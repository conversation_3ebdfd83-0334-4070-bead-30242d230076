package com.yunhesoft.system.tools.files.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 文件服务接口Controller
 * 
 * <AUTHOR> @date
 */
//@Log4j2
@RestController
@Api(tags = "文件服务接口")
@RequestMapping("/files")
public class SysFileController extends BaseRestController {

	@Autowired
	private IFilesInfoService fs;

	@ApiOperation(value = "获取文件预览地址")
	@GetMapping(value = "/**")
	public String getFilePreviewUrl() {
//		String requestURI = request.getRequestURI();
//		StringBuffer requestURL = request.getRequestURL();
//		String pathInfo = request.getPathInfo();
//		String contextPath = request.getContextPath();
		String fileUrl = request.getServletPath();
		fs.filePreview(fileUrl, response);

//		return "http://127.0.0.1:9000/tm4//files/a/b/mayun.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=1X3fsXQttt2LQZnr%2F20230419%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230419T050609Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=839bb363b96ef57c88e3c5a8e51749add4c0c7eb59686b69cf072794e5add3fa";
		return null;
	}

}
