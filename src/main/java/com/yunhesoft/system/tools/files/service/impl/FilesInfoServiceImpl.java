package com.yunhesoft.system.tools.files.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.yunhesoft.core.common.component.SystemProperties;
import com.yunhesoft.core.common.exception.AuthException;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.script.ScriptEngineUtils;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.EntityUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.model.IDataSource;
import com.yunhesoft.system.tds.model.TDataSourceManager;
import com.yunhesoft.system.tds.model.TInPara;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import com.yunhesoft.system.tools.dict.service.ISysDictTypeService;
import com.yunhesoft.system.tools.eval.CompiledScriptEngine;
import com.yunhesoft.system.tools.eval.model.CustomFun;
import com.yunhesoft.system.tools.files.entity.dto.*;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;
import com.yunhesoft.system.tools.files.entity.po.WordExpDs;
import com.yunhesoft.system.tools.files.entity.vo.*;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;
import com.yunhesoft.system.tools.files.service.ISysFileService;
import com.yunhesoft.system.tools.files.tools.PoitlTools;
import com.yunhesoft.system.tools.form.dto.SysFormDataDto;
import com.yunhesoft.system.tools.form.dto.SysFormDto;
import com.yunhesoft.system.tools.form.service.SysFormContentService;
import com.yunhesoft.system.tools.form.service.SysFormDataService;
import com.yunhesoft.system.tools.form.service.SysTplService;
import com.yunhesoft.system.tools.signature.entity.po.SigData;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.Units;
import org.apache.poi.xssf.usermodel.*;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;
import javax.script.ScriptException;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 上传文件信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-06
 */
@Log4j2
@Service
public class FilesInfoServiceImpl implements IFilesInfoService {

    @Autowired
    private ISysFileService fs;

    @Autowired
    private EntityService entityService;
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private SystemProperties props;
    @Autowired
    private SysFormContentService formService;
    @Autowired
    private SysFormDataService dataService;
    @Autowired
    private SysTplService tplService;
    @Autowired
    private IDataSourceService dsService;
    @Autowired
    private IDataSourceService tdsServ; // 数据源服务

    private final String ttocString = "TTOCT";// 目录标识字符串
    private TDataSourceManager dsm;
    private Map<String, CompiledScriptEngine> evalMap = new HashMap<String, CompiledScriptEngine>();

    //默认允许上传文件扩展名
    private final static String FILE_EXT = "doc,docx,xls,xlsx,ppt,pptx,txt,jpg,png,gif,jpeg,bmp,rar,zip";

    /**
     * 查询上传文件信息
     *
     * @param id 上传文件信息ID
     * @return 上传文件信息
     */
    @Override
    public SysFilesInfo selectFilesInfoById(String id) {
        return entityService.queryObject(SysFilesInfo.class, Where.create("id=?", id));
    }

    /**
     * 查询上传文件信息
     *
     * @param id 上传文件信息ID
     * @return 上传文件信息
     */
    @Override
    public SysFilesInfo selectFilesInfoByUrl(String weburl) {
        Where where = Where.create();
        where.and("FILE_URL = ?", weburl);
        List<SysFilesInfo> list = entityService.queryList(SysFilesInfo.class, where);
        if (list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }

    /**
     * 查询上传文件信息列表
     *
     * @param SysFilesInfo 上传文件信息
     * @return 上传文件信息
     */
    @Override
    public Res<List<SysFilesInfo>> selectFilesInfoList(SysFilesInfo SysFilesInfo, Pagination<?> page) {
        Where where = Where.create();
        try {
            Object ID_value = EntityUtils.getValue("ID", SysFilesInfo);
            if (ObjUtils.notEmpty(ID_value)) {
                where.and("ID = ?", ID_value);
            }
            Object SOURCE_MODULE_value = EntityUtils.getValue("SOURCE_MODULE", SysFilesInfo);
            if (ObjUtils.notEmpty(SOURCE_MODULE_value)) {
                where.and("SOURCE_MODULE = ?", SOURCE_MODULE_value);
            }
            Object FILE_TYPE_TMUID_value = EntityUtils.getValue("FILE_TYPE_TMUID", SysFilesInfo);
            if (ObjUtils.notEmpty(FILE_TYPE_TMUID_value)) {
                where.and("FILE_TYPE_TMUID = ?", FILE_TYPE_TMUID_value);
            }
            Object FILE_TYPE_value = EntityUtils.getValue("FILE_TYPE", SysFilesInfo);
            if (ObjUtils.notEmpty(FILE_TYPE_value)) {
                where.and("FILE_TYPE = ?", FILE_TYPE_value);
            }
            Object OLD_FILE_NAME_value = EntityUtils.getValue("OLD_FILE_NAME", SysFilesInfo);
            if (ObjUtils.notEmpty(OLD_FILE_NAME_value)) {
                where.and("OLD_FILE_NAME = ?", OLD_FILE_NAME_value);
            }
            Object NEW_FILE_NAME_value = EntityUtils.getValue("NEW_FILE_NAME", SysFilesInfo);
            if (ObjUtils.notEmpty(NEW_FILE_NAME_value)) {
                where.and("NEW_FILE_NAME = ?", NEW_FILE_NAME_value);
            }
            Object FILE_ADDRESS_value = EntityUtils.getValue("FILE_ADDRESS", SysFilesInfo);
            if (ObjUtils.notEmpty(FILE_ADDRESS_value)) {
                where.and("FILE_ADDRESS = ?", FILE_ADDRESS_value);
            }
            Object FILE_URL_value = EntityUtils.getValue("FILE_URL", SysFilesInfo);
            if (ObjUtils.notEmpty(FILE_URL_value)) {
                where.and("FILE_URL = ?", FILE_URL_value);
            }
            Object FILE_EXT_value = EntityUtils.getValue("FILE_EXT", SysFilesInfo);
            if (ObjUtils.notEmpty(FILE_EXT_value)) {
                where.and("FILE_EXT = ?", FILE_EXT_value);
            }
            Object FILE_SIZE_value = EntityUtils.getValue("FILE_SIZE", SysFilesInfo);
            if (ObjUtils.notEmpty(FILE_SIZE_value)) {
                where.and("FILE_SIZE = ?", FILE_SIZE_value);
            }
            Object CREATE_BY_value = EntityUtils.getValue("CREATE_BY", SysFilesInfo);
            if (ObjUtils.notEmpty(CREATE_BY_value)) {
                where.and("CREATE_BY = ?", CREATE_BY_value);
            }
            Object CREATE_TIME_value = EntityUtils.getValue("CREATE_TIME", SysFilesInfo);
            if (ObjUtils.notEmpty(CREATE_TIME_value)) {
                where.and("CREATE_TIME = ?", CREATE_TIME_value);
            }
            Object UPDATE_BY_value = EntityUtils.getValue("UPDATE_BY", SysFilesInfo);
            if (ObjUtils.notEmpty(UPDATE_BY_value)) {
                where.and("UPDATE_BY = ?", UPDATE_BY_value);
            }
            Object UPDATE_TIME_value = EntityUtils.getValue("UPDATE_TIME", SysFilesInfo);
            if (ObjUtils.notEmpty(UPDATE_TIME_value)) {
                where.and("UPDATE_TIME = ?", UPDATE_TIME_value);
            }
            // 读取总记录数量
            Res<List<SysFilesInfo>> res = new Res<List<SysFilesInfo>>();
            res.setTotal(entityService.queryCount(SysFilesInfo.class, where));
            // 读取记录结果
            res.setResult(entityService.queryList(SysFilesInfo.class, where, page));
            return res;
        } catch (IllegalArgumentException | IllegalAccessException e) {
            log.error("", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 新增上传文件信息
     *
     * @param SysFilesInfo 上传文件信息
     * @return 结果
     */
    @Override
    public int insertFilesInfo(SysFilesInfo SysFilesInfo) {
        SysFilesInfo.setId(TMUID.getUID());
        SysFilesInfo.setCreateTime(new Date());
        return entityService.insert(SysFilesInfo);
    }

    /**
     * 修改上传文件信息
     *
     * @param SysFilesInfo 上传文件信息
     * @return 结果
     */
    @Override
    public int updateFilesInfo(SysFilesInfo SysFilesInfo) {
        SysFilesInfo.setUpdateTime(new Date());
        return entityService.updateById(SysFilesInfo);
    }

    /**
     * 批量修改上传文件信息
     *
     * @param SysFilesInfo 上传文件信息
     * @return 结果
     */
    @Override
    public int updateFilesInfos(List<SysFilesInfo> list) {
        for (SysFilesInfo sysFiles : list) {
            sysFiles.setUpdateTime(new Date());
        }
        return entityService.updateByIdBatch(list);
    }

    /**
     * 批量删除上传文件信息
     *
     * @param ids 需要删除的上传文件信息ID
     * @return 结果
     */
    @Override
    public int deleteFilesInfoByIds(String[] ids) {
        return entityService.delete(SysFilesInfo.class, Where.create().andIns("id", ids));
    }

    /**
     * 删除上传文件信息信息
     *
     * @param id 上传文件信息ID
     * @return 结果
     */
    @Override
    public int deleteFilesInfoById(String id) {
        return entityService.delete(SysFilesInfo.class, Where.create("id=?", id));
    }

    @Override
    public SysFilesInfo saveFiles(MultipartFile file, String moduleCode) {
        return this.saveFiles(file, moduleCode, "all");
    }

    @Override
    public SysFilesInfo saveFilesImg(MultipartFile file, String moduleCode) {
        return this.saveFiles(file, moduleCode, "img");
    }

    @Override
    public SysFilesInfo saveFiles(MultipartFile file, String moduleCode, String fileType) {
        List<SysFilesInfo> rlist = new ArrayList<SysFilesInfo>();
        SysFilesInfo sysFileInfo = new SysFilesInfo();
        if (file != null) {
            MultipartFile[] flist = new MultipartFile[]{file};
            rlist = this.saveFiles(flist, moduleCode, fileType);
        }
        if (rlist != null && rlist.size() > 0) {
            sysFileInfo = rlist.get(0);
        }
        return sysFileInfo;
    }

    @Override
    public List<SysFilesInfo> saveFiles(MultipartFile[] files, String moduleCode) {
        return this.saveFiles(files, moduleCode, null);
    }

    @Override
    public List<SysFilesInfo> saveFiles(MultipartFile[] files, String moduleCode, String fileType) {
        return saveFiles(files, moduleCode, fileType, null, 0);
    }

    /**
     * 保存文件
     *
     * @param files
     * @param moduleCode
     * @param fileType
     * @param isPublic   0私有 1公共 此属性主要针对多租户，默认私有
     * @return
     */
    @Override
    public List<SysFilesInfo> saveFiles(MultipartFile[] files, String moduleCode, String fileType, int isPublic) {
        return saveFiles(files, moduleCode, fileType, null, isPublic);
    }

    /**
     * 保存文件
     *
     * @param files
     * @param moduleCode
     * @param fileType
     * @param isPublic   0私有 1公共 此属性主要针对多租户，默认私有
     * @return
     */
    @Override
    public List<SysFilesInfo> saveFiles(MultipartFile[] files, String moduleCode, String fileType, String rootUrl) {
        return saveFiles(files, moduleCode, fileType, rootUrl, 0);
    }

    /**
     * 获得文件扩展名
     *
     * @param file
     * @return
     */
    private String getFileExt(MultipartFile file) {
        String ext = "";
        if(file!=null) {
            String name = file.getOriginalFilename();
            if (name != null && name.contains(".")) {
                ext = name.substring(name.lastIndexOf(".") + 1);
            }else{
                String type = file.getContentType();
                if(type!=null && type.contains("/")){
                    ext = type.substring(type.lastIndexOf("/") + 1);
                }
            }
        }
        return ext.toLowerCase();
    }

    /**
     * 保存文件
     *
     * @param files
     * @param moduleCode
     * @param fileType
     * @param isPublic   0私有 1公共 此属性主要针对多租户，默认私有
     * @return
     */
    @Override
    public List<SysFilesInfo> saveFiles(MultipartFile[] files, String moduleCode, String fileType, String rootUrl,
                                        int isPublic) {
        List<SysFilesInfo> rlist = new ArrayList<SysFilesInfo>();
        /*
         * 参数获取并判断是否存入指定物理地址 参数获取网络访问的地址
         */
        String addr = sysConfigService.getSysConfig("file_path");
        String url = sysConfigService.getSysConfig("file_url");
        if (addr == null) {
            addr = "d:/tm4UploadFiles";
        }
        url = "/files"; // 此处为兼容历史，固定写死
        if (StringUtils.isNotEmpty(rootUrl)) {
            url = rootUrl;
        }
        String file_ext = sysConfigService.getSysConfig("file_ext"); //文件扩展名
        if (StringUtils.isNotEmpty(file_ext)) {
            file_ext = FILE_EXT + "," + file_ext;
        } else {
            file_ext = FILE_EXT;
        }
        List<String>  extList = Arrays.asList(file_ext.toLowerCase().split(","));
        for (MultipartFile file : files) {
            SysFilesInfo obj = new SysFilesInfo();
            String fileExt = getFileExt(file);// 获取文件扩展名
            if (StringUtils.isNotEmpty(fileExt)) {//判断扩展名是否合法
                if (!extList.contains(fileExt)) {
                    throw new RuntimeException("TM4-INFO:上传文件类型(扩展名:" + fileExt + ")不合法");
                }
            }
            obj.setFileExt(fileExt);
            obj.setAccessPolicy(isPublic);
            String yearMonth = DateTimeUtils.format(DateTimeUtils.getNowDate(), "yyyy_MM");
            obj.setSourceModule(moduleCode);
            if ("img".equals(fileType)) {
                if(!file.getContentType().startsWith("image/")) {
                    throw new RuntimeException("TM4-INFO:上传图片类型不合法");
                }
                String fileName = "img_" + System.currentTimeMillis();
                obj.setOldFileName(fileName + ".jpg");
                obj.setNewFileName(fileName);
                obj.setFileExt("jpg");
            } else {
                obj.setOldFileName(file.getOriginalFilename());
                obj.setNewFileName(file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("/") + 1,
                        file.getOriginalFilename().lastIndexOf(".")) + System.currentTimeMillis());

            }
            if ("png_del".equalsIgnoreCase(obj.getFileExt()) || "jpg".equalsIgnoreCase(obj.getFileExt())
                    || "jpeg".equalsIgnoreCase(obj.getFileExt()) || "gif".equalsIgnoreCase(obj.getFileExt())
                    || "bmp".equalsIgnoreCase(obj.getFileExt())) {
                // 压缩图片
                file = compressPictures(file, obj);
            }

            // 月份存储附件
//			String fileaddr = addr + "/" + yearMonth + "/" + obj.getNewFileName() + "." + obj.getFileExt();
//			File tempFile = new File(fileaddr);
//			tempFile.mkdirs();
//			obj.setFileAddress(fileaddr);
            String fileUrl = url + "/" + yearMonth + "/" + obj.getNewFileName() + "." + obj.getFileExt();
            if (StringUtils.isNotEmpty(rootUrl)) {
                fileUrl = url + "/" + obj.getNewFileName() + "." + obj.getFileExt();
            }
            obj.setFileUrl(fileUrl);
            obj.setFileSize(file.getSize());
            setFileType(obj);
            // 调用文件上传接口
            fs.uploadFile(file, obj);
            // 保存文件表信息
            insertFilesInfo(obj);
            rlist.add(obj);
        }
        return rlist;
    }

    /*
     * @Override public List<SysFilesInfo> saveFiles(MultipartFile[] files, String
     * moduleCode, String fileType) { List<SysFilesInfo> rlist = new
     * ArrayList<SysFilesInfo>(); //参数获取并判断是否存入指定物理地址 参数获取网络访问的地址 String addr =
     * sysConfigService.getSysConfig("file_path"); String url =
     * sysConfigService.getSysConfig("file_url"); if (addr == null) { addr =
     * "d:/tm4UploadFiles"; } if (url == null) { url = "/files"; } for
     * (MultipartFile file : files) { SysFilesInfo obj = new SysFilesInfo(); String
     * yearMonth = DateTimeUtils.format(DateTimeUtils.getNowDate(), "yyyy_MM");
     *
     * // obj.setId(TMUID.getUID()); obj.setSourceModule(moduleCode); if (fileType
     * != null && "img".equals(fileType)) { String fileName = "img_" +
     * System.currentTimeMillis(); obj.setOldFileName(fileName + ".jpg");
     * obj.setNewFileName(fileName); obj.setFileExt("jpg"); } else {
     * obj.setOldFileName(file.getOriginalFilename());
     * obj.setNewFileName(file.getOriginalFilename().substring(file.
     * getOriginalFilename().lastIndexOf("/") + 1,
     * file.getOriginalFilename().lastIndexOf(".")) + System.currentTimeMillis());
     * obj.setFileExt(file.getOriginalFilename().substring(file.getOriginalFilename(
     * ).lastIndexOf(".") + 1)); }
     *
     * if (addr != null) { // 月份存储附件 String fileaddr = addr + "/" + yearMonth + "/"
     * + obj.getNewFileName() + "." + obj.getFileExt(); File tempFile = new
     * File(fileaddr); tempFile.mkdirs(); try { // png不进行压缩，压缩后背景变成黑色了 if
     * ("png_del".equalsIgnoreCase(obj.getFileExt()) ||
     * "jpg".equalsIgnoreCase(obj.getFileExt()) ||
     * "jpeg".equalsIgnoreCase(obj.getFileExt()) ||
     * "gif".equalsIgnoreCase(obj.getFileExt()) ||
     * "bmp".equalsIgnoreCase(obj.getFileExt())) { this.compressPictures(fileaddr,
     * file.getInputStream(), obj); // 获取并设置高度宽度 try { BufferedImage bi =
     * ImageIO.read(new FileInputStream(tempFile)); int w = bi.getWidth(); int h =
     * bi.getHeight();
     *
     * obj.setPicWidth(w); obj.setPicHeight(h); } catch (Exception e) {
     * log.error("file upload get pic width and height error", e.getMessage()); } }
     * else { file.transferTo(tempFile); } // 如果是图片，获取图片的原始尺寸并保存 } catch
     * (IllegalStateException | IOException e) { log.error("", e); }
     * obj.setFileAddress(fileaddr); } else { // 文件服务器存储 } obj.setFileUrl(url + "/"
     * + yearMonth + "/" + obj.getNewFileName() + "." + obj.getFileExt());
     * obj.setFileSize(file.getSize()); setFileType(obj);
     *
     * insertFilesInfo(obj);
     *
     * rlist.add(obj); } return rlist; }
     */

    @Override
    public void updatePicWH(File picFile, SysFilesInfo obj) {

    }

    @Override
    public void updatePicWH(String address, SysFilesInfo obj) {
        File picFile = new File(address);
        updatePicWH(picFile, obj);
    }

    /**
     * 图片超过1080p缩放
     *
     * @param outFilePath
     * @param stream
     * @param obj
     * @return
     */
    private boolean compressPictures(String outFilePath, InputStream stream, SysFilesInfo obj) {
        boolean result = false;

        if (outFilePath != null && outFilePath.length() != 0 && stream != null) {// 参数有效

            ImageOutputStream out = null;
            try {
                BufferedImage img = ImageIO.read(stream);

                int width = img.getWidth(null);
                int height = img.getHeight(null);

                if ((width > 0 && height > 0)) {// 图片宽高比限制大，需要进行压缩

                    if (width > 1910) {
                        double tempw = width - 1900;
                        double bil = tempw / width;
                        height = (int) Math.floor(height - (height * bil));
                        width = 1910;
                    }

                    if (height > 1070) {
                        double temph = height - 1000;
                        double bil = temph / height;
                        width = (int) Math.floor(width - (width * bil));
                        height = 1070;
                    }
                    Image image = img.getScaledInstance(width, height, Image.SCALE_SMOOTH);
                    BufferedImage outputImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
                    Graphics graphics = outputImage.getGraphics();
                    graphics.drawImage(image, 0, 0, null);
                    graphics.dispose();

                    ImageIO.write(outputImage, obj.getFileExt(), new File(outFilePath));
                    result = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (out != null) {
                    try {
                        out.close();
                    } catch (IOException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
            }
        }

        return result;
    }

    /**
     * 图片超过1080p缩放
     *
     * @param outFilePath
     * @param stream
     * @param obj
     * @return
     */
    private MultipartFile compressPictures(MultipartFile file, SysFilesInfo fileInfo) {
        MultipartFile result = file;
        ImageOutputStream out = null;
        try {
            BufferedImage img = ImageIO.read(file.getInputStream());

            int width = img.getWidth(null);
            int height = img.getHeight(null);

            if (width == 0 || height == 0) {
                // 宽高为0不需要压缩
                return result;
            }

            boolean needCompress = false;
            if (width > 1910) {
                double tempw = width - 1900;
                double bil = tempw / width;
                height = (int) Math.floor(height - (height * bil));
                width = 1910;
                needCompress = true;
            }

            if (height > 1070) {
                double temph = height - 1000;
                double bil = temph / height;
                width = (int) Math.floor(width - (width * bil));
                height = 1070;
                needCompress = true;
            }

            // 赋值图片宽高信息
            fileInfo.setPicWidth(width);
            fileInfo.setPicHeight(height);

            if (!needCompress) {
                return result;
            }

            // 缓存文件磁盘全路径（目录+文件名）
            String tempAddr = getTempFileDiskAdress(fileInfo);

            Image image = img.getScaledInstance(width, height, Image.SCALE_SMOOTH);
            BufferedImage outputImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics graphics = outputImage.getGraphics();
            graphics.drawImage(image, 0, 0, null);
            graphics.dispose();

            File tempFile = new File(tempAddr);
            if (!tempFile.exists()) {
                tempFile.createNewFile();
            }
            ImageIO.write(outputImage, fileInfo.getFileExt(), tempFile);
            result = fileConvertToMultipartFile(tempFile);
            tempFile.delete();
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }

        return result;
    }

    private MultipartFile fileConvertToMultipartFile(File file) throws IOException {
        MultipartFile result = null;
        if (null != file) {
            FileInputStream input = new FileInputStream(file);
            result = new MockMultipartFile(file.getName(), file.getName(), Files.probeContentType(file.toPath()),
                    input);
        }
        return result;
    }

    private List<SysDictData> getFileTypeConf() {
        return dictTypeService.selectDictDataByType("sys_file_type");
    }

    private SysFilesInfo setFileType(SysFilesInfo obj) {
        String id = null, name = null;
        List list = getFileTypeConf();
        if (list != null) {
            if (list.get(0) instanceof java.util.Map) {
                for (Map<String, String> map : (List<Map<String, String>>) list) {
                    if (obj.getFileExt().indexOf(map.get("dictValue")) != -1) {
                        id = map.get("dictCode");
                        name = map.get("dictLabel");
                        break;
                    }
                }
            } else if (list.get(0) instanceof SysDictData) {
                for (SysDictData type : (List<SysDictData>) list) {
                    if (obj.getFileExt().indexOf(type.getDictValue()) != -1) {
                        // id = String.valueOf(type.getDictCode());
                        id = type.getId();
                        name = type.getDictLabel();
                        break;
                    }
                }
            }
        }
        if (id != null && name != null) {
            obj.setFileType(name);
            obj.setFileTypeId(id);
        }
        return obj;
    }

    @Override
    public String getFileType() {
        StringBuilder sb = new StringBuilder();
        List list = getFileTypeConf();
        if (list != null) {
            if (list.get(0) instanceof java.util.Map) {
                for (Map<String, String> map : (List<Map<String, String>>) list) {
                    if (sb.length() > 0)
                        sb.append(",");
                    sb.append(map.get("dictValue"));

                }
            } else if (list.get(0) instanceof SysDictData) {
                for (SysDictData type : (List<SysDictData>) list) {
                    if (sb.length() > 0)
                        sb.append(",");
                    sb.append(type.getDictValue());
                }
            }
        }

        return sb.toString();
    }

    @Override
    public String getFileTypeStr() {
        StringBuilder sb = new StringBuilder();
        List list = getFileTypeConf();
        if (list != null) {
            if (list.get(0) instanceof java.util.Map) {
                for (Map<String, String> map : (List<Map<String, String>>) list) {
                    if (sb.length() > 0)
                        sb.append(",");
                    sb.append(map.get("dictLabel"));

                }
            } else if (list.get(0) instanceof SysDictData) {
                for (SysDictData type : (List<SysDictData>) list) {
                    if (sb.length() > 0)
                        sb.append(",");
                    sb.append(type.getDictValue());
                }
            }
        }

        return sb.toString();
    }

    /**
     * 获取上传文件限制大小
     */
    @Override
    public Integer getFileSize() {
        // Integer isize = 1;
        // String size = props.getPropertyUtf8("spring.servlet.multipart.max-file-size",
        // "1MB");
        String size = sysConfigService.getSysConfig("file_maxSize");
        if (StringUtils.isEmpty(size)) {
            size = props.getPropertyUtf8("spring.servlet.multipart.max-file-size", "10MB");
        }
        size = size.toUpperCase();
        size = size.replace("M", "").replace("B", "").replace("G","");
        try {
            return Integer.parseInt(size);
        } catch (Exception e) {
            return 0;
        }
        // size = size.replace("M", "").replace("B", "");

//		if ("".equals(size)) {
//			size = "1";
//		} else if ("-1".equals(size)) {
//
//			if (size == null) {
//				size = "1";
//			}
//		}
//		if (Coms.judgeLong(size)) {
//			isize = Integer.parseInt(size);
//		}
//		return isize;
    }

    @Override
    public Res<List<FileInfo>> selectFilesInfoListByIds(String ids) {
        Where where = Where.create();
        where.in(SysFilesInfo::getId, ids.split(","));
        Res<List<FileInfo>> res = new Res<List<FileInfo>>();
        List<FileInfo> rvlist = new ArrayList<FileInfo>();
        List<SysFilesInfo> list = entityService.queryList(SysFilesInfo.class, where);
        for (SysFilesInfo sysFilesInfo : list) {
            FileInfo obj = new FileInfo();
            obj.setId(sysFilesInfo.getId());
            obj.setName(sysFilesInfo.getOldFileName());
            obj.setUrl(sysFilesInfo.getFileUrl());
            obj.setAddress(sysFilesInfo.getFileAddress());
            obj.setFileExt(sysFilesInfo.getFileExt());
            obj.setFileSize(sysFilesInfo.getFileSize());
            rvlist.add(obj);
        }
        res.setResult(rvlist);
        return res;
    }

    @Override
    public XWPFDocument getExportWordFromTplJson(String tplId) {
        return getExportWordFromTplJson(tplId, null);
    }

    @Override
    public XWPFDocument getExportWordFromTplJson(String tplId, String name) {
        XWPFDocument document = new XWPFDocument();

        String titleName = "WORD模板";
        if (name != null && name.length() > 0) {
            titleName = name;
        }

        XWPFParagraph titleParagraph = document.createParagraph();
        // 设置段落居中
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleParagraphRun = titleParagraph.createRun();
        titleParagraphRun.setText(titleName);
        titleParagraphRun.setColor("000000");
        titleParagraphRun.setFontSize(20);

        List<WordTab> tabList = new ArrayList<WordTab>();
        // 当前活动的表格
        WordTab wtab = null;

        List<SysFormDto> list = formService.getTplFormContents(tplId);
        if (list != null && list.size() > 0) {
            boolean isSplitTab = false;
            // 循环表单
            for (SysFormDto dto : list) {
//        	{
                /*
                 * tdsData : tdsData=["t_bd_jbsj"] inParaAlias=id=@dataid 组件示例
                 * {"sjkobj":{"name":"DLWZ","label":"地理位置(DLWZ)","tableName":
                 * "ACT_BUSS_USER_JBSJ"}, "display":true, "cshSjk":"t_bd_jbsj", 初始化数据源/表
                 * "className":"FORM_FONTSIZE", "label":"1、地理位置", "type":"input", "isCsh":true,
                 * 是否允许初始化数据 "sjk":"ACT_BUSS_USER_JBSJ", 表名 "prop":"1655703270803_3517",
                 * "prepend": "前缀", "append": "后缀", "sjkzd":"DLWZ", 字段名
                 * "CshSjkobj":{"name":"dlwz","label":"dlwz","tableName":"t_bd_jbsj"}, 初始化数据对象
                 * "cshSjkzd":"dlwz", 初始化数据源/表字段 "span":24}
                 */

                String formId = dto.getFormId();
                wtab = null;
                String formJson = dto.getFormContent();
                System.out.println(formJson);
//        		String formJson = list.get(2).getFormContent();
//				System.out.println(formJson);
                JSONObject jsonObj = JSONObject.parseObject(formJson);
                String steps = jsonObj == null || !jsonObj.containsKey("steps") ? null : jsonObj.getString("steps");
                JSONArray jsonArr = StringUtils.isEmpty(steps) ? null : JSONArray.parseArray(steps);// 取出属性信息
                if (jsonArr.size() > 0) {
                    // 内部数据标签，目前一个表单就一个
                    int colsum = 0;
                    int groupFirstRow = 1;
                    int currrow = 0;
                    int temprow = 0;
                    for (int i = 0, il = jsonArr.size(); i < il; i++) {

                        // 表格活动行
                        WordTabRow wrow = null;

                        JSONObject bq = JSONObject.parseObject(jsonArr.getString(i));
                        String title = bq.getString("titleName");
                        title = (title == null || "".equals(title)) ? dto.getFormName() : title;
                        JSONArray groups = JSONArray.parseArray(bq.getString("group"));
                        JSONArray singleColumns = JSONArray.parseArray(bq.getString("column"));

                        String formAlias = bq.getString("formAlias");
                        formAlias = StringUtils.isEmpty(formAlias) ? "" : "." + formAlias;

                        // 表单绑定数据源
                        String tdsinpara = bq.getString("inParaAlias");
                        tdsinpara = tdsinpara == null ? "" : tdsinpara;
                        JSONArray tdsDataArr = JSONArray.parseArray(bq.getString("tdsData"));
                        List<String> tdsList = new ArrayList<String>();
                        for (int j = 0, jl = tdsDataArr.size(); j < jl; j++) {
                            String tdsData = tdsDataArr.getString(j);
                            if (tdsData != null && tdsData.length() > 0) {
                                tdsList.add(tdsData);
                            }
                        }

                        // 保存表头
                        WordTab wtabtitle = new WordTab();
                        wtabtitle.setType("title");
                        wtabtitle.setTitle(title);
                        tabList.add(wtabtitle);

                        if (!isSplitTab && wtab == null) {// (wtab==null || wtab.getTitle()==null)) {
                            wtab = new WordTab();
                            wtab.setTitle(title);
                        }

                        // 有独立的无分组组件设置
                        if (singleColumns != null && singleColumns.size() > 0) {
                            // 分组内部组件循环
                            for (int k = 0, kl = singleColumns.size(); k < kl; k++) {
                                JSONObject column = JSONObject.parseObject(singleColumns.getString(k));
                                // 组件名
                                String label = column.getString("label");
                                // 栅格数，使用bootstrap栅格布局，整行为24
                                Integer span = column.getInteger("span");
                                // 是否可见
                                Boolean display = true;
                                if (column.containsKey("display")) {
                                    display = column.getBoolean("display");
                                }
                                // 前后缀
                                String prepend = column.getString("prepend");
                                String append = column.getString("append");
                                prepend = prepend == null ? "" : prepend;
                                append = append == null ? "" : append;

                                // 关联数据表和字段信息
                                String table = column.getString("sjk");
                                String tableCol = column.getString("sjkzd");
                                String dateCol = "";
                                if (table != null && !"".equals(table) && tableCol != null && !"".equals(tableCol)) {
                                    dateCol = table + "." + tableCol + formAlias;
                                } else {
                                    // 数据源配置
                                    String cshSjk = column.getString("cshSjk");// 初始化数据源/表
                                    String cshSjkzd = column.getString("cshSjkzd");// 初始化数据源/表字段
                                    if (tdsList != null && cshSjk != null && cshSjkzd != null && cshSjk.length() > 0
                                            && cshSjkzd.length() > 0 && tdsList.contains(cshSjk)) {// 配置的数据源
                                        String temppara = tdsinpara;
                                        temppara = temppara.replace("=", "__d__");
                                        temppara = temppara.replace("|", "__1__");
                                        temppara = temppara.replace("@", "__a__");
                                        dateCol = "tds_" + cshSjk + "______" + temppara + "______" + cshSjkzd + "______"
                                                + formId;
                                    }
                                }
                                String type = column.getString("type");
                                if ("title".equals(type)) {
                                    label = column.getString("value");
                                }

                                Boolean isTitle = "title".equals(type);
                                Boolean isDsTab = false, isPic = false, isRowPic = false, isWord = false,
                                        isNameMake = false, isRowNameMark = false;
                                if (column.containsKey("component")) {
                                    String component = column.getString("component");
                                    if ("tdsedit-table".equals(component)) {// 表格組件
                                        isDsTab = true;
                                    } else if ("hhuploadFormImg".equals(component)) {// 图片組件
                                        isPic = true;
                                        if (24 == span) {
                                            isRowPic = true;
                                        }
                                    } else if ("hhWordUploadSub".equals(component)) {// word組件
                                        isWord = true;
                                    } else if ("hhSignatureSub".equals(component) || "hhSignSub".equals(component)) {// 签名組件
                                        isNameMake = true;
                                        if (24 == span) {
                                            isRowNameMark = true;
                                        }
                                    }
                                }

                                // 如果是数据源配置，结束已整理表格，输出数据源配置模板；如果是图片，判断占位是否满行（24），如果满行就结束已整理表格独立输出，否则在表格里显示
                                if (isTitle || isDsTab || isRowPic || isWord || isRowNameMark) {

                                    // 如果有上个分组，对上个分组进行处理；
                                    if (wtab.getRows() != null && wtab.getRows().size() > 0) {
                                        if (temprow != currrow) {
                                            // 整体表格情况下，每个分组，第一列合并
                                            WordTabMerge merge = new WordTabMerge();
                                            merge.setFromCol(0);
                                            merge.setToCol(0);
                                            merge.setFromRow(temprow);
                                            merge.setToRow(currrow);
                                            if (wtab.getMergeList() == null) {
                                                wtab.setMergeList(new ArrayList<WordTabMerge>());
                                            }
                                            wtab.getMergeList().add(merge);
                                        }
                                    }

                                    if (isDsTab) {// 数据源
                                        JSONObject params = JSONObject.parseObject(column.getString("params"));
                                        String tdsAlias = params.getString("tdsAlias");
                                        String tdsInParaAlias = params.getString("tdsInParaAlias");
                                        if (tdsInParaAlias == null) {
                                            tdsInParaAlias = "";
                                        }
//									String dataId = "0";
//									if(tdsInParaAlias.split("=").length>1) {
//										String parai = tdsInParaAlias.split("=")[1];
//										if("@xmId".equals(parai)) {
//											dataId="1";
//										}else if("@htId".equals(parai)) {
//											dataId="2";
//										}else if("@jtId".equals(parai)) {
//											dataId="3";
//										}
//									}
                                        WordTab tab = wtab;
                                        if (wtab != null && wtab.getRows() != null && wtab.getRows().size() > 0) {
                                            tabList.add(tab);
                                        }
                                        // 设置了数据源，进行变量输入，否则空着
                                        if (tdsAlias != null && tdsAlias.length() > 0) {
                                            tdsAlias = tdsAlias.replace(" ", "");
                                            // 因参数只支持字符、数值、下划线，对特殊字符进行替换，否则被组件判定为不规范，根据模板导出时，无法生成
                                            tdsInParaAlias = tdsInParaAlias.replace("=", "__d__");
                                            tdsInParaAlias = tdsInParaAlias.replace("|", "__1__");
                                            tdsInParaAlias = tdsInParaAlias.replace("@", "__a__");

                                            WordTab dstab = new WordTab();
                                            dstab.setIsDs(true);
                                            dstab.setDsAlias("{{#tds_" + tdsInParaAlias + "______" + tdsAlias + "______"
                                                    + formId + "}}");
                                            tabList.add(dstab);
                                        }

                                        wtab = new WordTab();
                                        wrow = null;
                                        currrow = 0;
                                        temprow = 0;
                                        continue;
                                    } else if (isTitle) {
                                        WordTab tab = wtab;
                                        if (wtab != null && wtab.getRows() != null && wtab.getRows().size() > 0) {
                                            tabList.add(tab);
                                        }

                                        WordTab ttab = new WordTab();

                                        ttab.setType("itemTitle");
                                        ttab.setTitle(label);
                                        tabList.add(ttab);

                                        wtab = new WordTab();
                                        wrow = null;
                                        currrow = 0;
                                        temprow = 0;
                                        continue;
                                    } else if (isRowPic) {
                                        WordTab tab = wtab;
                                        if (wtab != null && wtab.getRows() != null && wtab.getRows().size() > 0) {
                                            tabList.add(tab);
                                        }

                                        WordTab pictab = new WordTab();
                                        pictab.setIsPic(true);
                                        pictab.setInfo("{{@pic_" + dateCol.replace(".", "____") + "}}");// 图片.替换____
                                        tabList.add(pictab);

                                        wtab = new WordTab();
                                        wrow = null;
                                        currrow = 0;
                                        temprow = 0;
                                        continue;
                                    } else if (isWord) {
                                        WordTab tab = wtab;
                                        if (wtab != null && wtab.getRows() != null && wtab.getRows().size() > 0) {
                                            tabList.add(tab);
                                        }

                                        WordTab wordtab = new WordTab();
                                        wordtab.setIsPic(true);
                                        wordtab.setInfo("____wordcom____" + dateCol);
                                        tabList.add(wordtab);

                                        wtab = new WordTab();
                                        wrow = null;
                                        currrow = 0;
                                        temprow = 0;
                                        continue;
                                    } else if (isRowNameMark) {
                                        WordTab tab = wtab;
                                        if (wtab != null && wtab.getRows() != null && wtab.getRows().size() > 0) {
                                            tabList.add(tab);
                                        }

                                        WordTab namemarktab = new WordTab();
                                        namemarktab.setIsPic(true);
                                        namemarktab.setInfo("{{@picnamemark}}");
                                        tabList.add(namemarktab);

                                        wtab = new WordTab();
                                        wrow = null;
                                        currrow = 0;
                                        temprow = 0;
                                        continue;
                                    }
                                } else {
                                    if (k == 0 || groupFirstRow == 1 || wrow == null) {
                                        // 如果按分组拆分表，每个分组是一个表
                                        if (isSplitTab) {
                                            wtab = new WordTab();
                                        } else {
                                            // 如果查看表已有行数据，说明到了下一个分组，对上个分组进行处理；如果是新分组，初始化行数据
                                            if (wtab.getRows() != null && wtab.getRows().size() > 0) {
                                                if (temprow != currrow) {
                                                    // 整体表格情况下，每个分组，第一列合并
                                                    WordTabMerge merge = new WordTabMerge();
                                                    merge.setFromCol(0);
                                                    merge.setToCol(0);
                                                    merge.setFromRow(temprow);
                                                    merge.setToRow(currrow);
                                                    if (wtab.getMergeList() == null) {
                                                        wtab.setMergeList(new ArrayList<WordTabMerge>());
                                                    }
                                                    wtab.getMergeList().add(merge);
                                                }
                                                currrow++;
                                                temprow = currrow;
                                            } else {
                                                List<WordTabRow> _rlist = new ArrayList<WordTabRow>();
                                                wtab.setRows(_rlist);
                                            }

                                            // 分组title作为表格第一列输出
                                            List<WordTabCell> cellList = new ArrayList<WordTabCell>();
                                            WordTabCell cell = new WordTabCell();
                                            cell.setGridNum(0);
                                            cell.setText("信息");
                                            cell.setIsVar(false);
                                            cellList.add(cell);

                                            // 定义分组第一行，并加入首列
                                            WordTabRow _row = new WordTabRow();
                                            _row.setCols(cellList);
                                            // 表格加入行
                                            wtab.getRows().add(_row);
                                            // 行句柄定位
                                            wrow = _row;

                                            colsum = 0;
                                        }

                                        groupFirstRow = 0;
                                    }
                                }

                                if (!display) {
                                    continue;
                                }

                                // 一个组件分为两列，名称和内容
                                WordTabCell cell1 = new WordTabCell();
                                cell1.setGridNum(span);
                                cell1.setText(label);
                                cell1.setIsVar(false);

                                // 字段变量赋值，如果是图片，赋值图片变量
                                String colVar = dateCol;
                                if (isPic) {
                                    colVar = "@pic_" + span + "____" + dateCol.replace(".", "____");
                                } else if (isNameMake) {
                                    colVar = "@picnamemark" + span;
                                }
                                WordTabCell cell2 = new WordTabCell();
                                cell2.setGridNum(span);
                                cell2.setText(prepend + ("".equals(colVar) ? "" : ("{{" + colVar + "}}")) + append);
                                cell2.setIsVar(true);

                                // 列栅格数进行累计
                                colsum += span;

                                // 如果超过列栅格最大值，进行换行，说明这个内容在下一行
                                if (colsum > 24) {
                                    // 记录分组名为新行的第一个单元格
                                    WordTabCell groupcell = new WordTabCell();
                                    groupcell.setGridNum(0);
                                    groupcell.setText("信息");
                                    groupcell.setIsVar(false);

                                    // 列信息写入
                                    List<WordTabCell> cellList = new ArrayList<WordTabCell>();
                                    cellList.add(groupcell);
                                    if ("title".equals(type)) {
                                        cellList.add(cell1);
                                    } else {
                                        cellList.add(cell1);
                                        cellList.add(cell2);
                                    }

                                    // 行信息写入
                                    WordTabRow _row = new WordTabRow();
                                    _row.setCols(cellList);
                                    wtab.getRows().add(_row);
                                    // 活动行句柄定位
                                    wrow = _row;
                                    // 栅格累计重置
                                    colsum = span;
                                    // 行计数累加
                                    currrow++;
                                } else {
                                    // 同行累加单元格
                                    if ("title".equals(type)) {
                                        wrow.getCols().add(cell1);
                                    } else {
                                        wrow.getCols().add(cell1);
                                        wrow.getCols().add(cell2);
                                    }

                                }
                                if (span == 24) {
                                    // 整体表格情况下，每个分组，第一列合并
                                    WordTabMerge merge = new WordTabMerge();
                                    if ("title".equals(type)) {
                                        merge.setFromCol(1);
                                        merge.setToCol(4);
                                    } else {
                                        merge.setFromCol(2);
                                        merge.setToCol(4);
                                    }
                                    merge.setFromRow(currrow);
                                    merge.setToRow(currrow);
                                    if (wtab.getMergeList() == null) {
                                        wtab.setMergeList(new ArrayList<WordTabMerge>());
                                    }
                                    wtab.getMergeList().add(merge);
                                }
                            }
                        }

                        // 表单内部分组循环
                        if (groups != null) {
                            for (int j = 0, jl = groups.size(); j < jl; j++) {
                                JSONObject group = JSONObject.parseObject(groups.getString(j));
                                String groupLabel = group.getString("label");
                                JSONArray columns = JSONArray.parseArray(group.getString("column"));

                                // 新分组，列计数重新开始
                                colsum = 0;
                                groupFirstRow = 1;

                                // 分组内部组件循环
                                for (int k = 0, kl = columns.size(); k < kl; k++) {
                                    JSONObject column = JSONObject.parseObject(columns.getString(k));
                                    // 组件名
                                    String label = column.getString("label");
                                    // 栅格数，使用bootstrap栅格布局，整行为24
                                    Integer span = column.getInteger("span");
                                    // 是否可见
                                    Boolean display = true;
                                    if (column.containsKey("display")) {
                                        display = column.getBoolean("display");
                                    }

                                    // 前后缀
                                    String prepend = column.getString("prepend");
                                    String append = column.getString("append");
                                    prepend = prepend == null ? "" : prepend;
                                    append = append == null ? "" : append;
                                    // 关联数据表和字段信息
                                    String table = column.getString("sjk");
                                    String tableCol = column.getString("sjkzd");
                                    String dateCol = "";
                                    if (table != null && !"".equals(table) && tableCol != null
                                            && !"".equals(tableCol)) {
                                        dateCol = table + "." + tableCol + formAlias;
                                    } else {
                                        // 数据源配置
                                        String cshSjk = column.getString("cshSjk");// 初始化数据源/表
                                        String cshSjkzd = column.getString("cshSjkzd");// 初始化数据源/表字段
                                        if (tdsList != null && cshSjk != null && cshSjkzd != null && cshSjk.length() > 0
                                                && cshSjkzd.length() > 0 && tdsList.contains(cshSjk)) {// 配置的数据源
                                            String temppara = tdsinpara;
                                            temppara = temppara.replace("=", "__d__");
                                            temppara = temppara.replace("|", "__1__");
                                            temppara = temppara.replace("@", "__a__");
                                            dateCol = "tds_" + cshSjk + "______" + temppara + "______" + cshSjkzd
                                                    + "______" + formId;
                                        }
                                    }

                                    String type = column.getString("type");
                                    if ("title".equals(type)) {
                                        label = column.getString("value");
                                    }

                                    Boolean isTitle = "title".equals(type);
                                    Boolean isDsTab = false, isPic = false, isRowPic = false, isWord = false,
                                            isNameMake = false, isRowNameMark = false;
                                    if (column.containsKey("component")) {
                                        String component = column.getString("component");
                                        if ("tdsedit-table".equals(component)) {
                                            isDsTab = true;
                                        } else if ("hhuploadFormImg".equals(component)) {
                                            isPic = true;
                                            if (24 == span) {
                                                isRowPic = true;
                                            }
                                        } else if ("hhWordUploadSub".equals(component)) {// word組件
                                            isWord = true;
                                        } else if ("hhSignatureSub".equals(component)
                                                || "hhSignSub".equals(component)) {// 签名組件
                                            isNameMake = true;
                                            if (24 == span) {
                                                isRowNameMark = true;
                                            }
                                        }
                                    }

                                    // 如果是数据源配置，结束已整理表格，输出数据源配置模板；如果是图片，判断占位是否满行（24），如果满行就结束已整理表格独立输出，否则在表格里显示
                                    if (isTitle || isDsTab || isRowPic || isWord || isRowNameMark) {

                                        // 如果有上个分组，对上个分组进行处理；
                                        if (wtab.getRows() != null && wtab.getRows().size() > 0) {
                                            if (temprow != currrow) {
                                                // 整体表格情况下，每个分组，第一列合并
                                                WordTabMerge merge = new WordTabMerge();
                                                merge.setFromCol(0);
                                                merge.setToCol(0);
                                                merge.setFromRow(temprow);
                                                merge.setToRow(currrow);
                                                if (wtab.getMergeList() == null) {
                                                    wtab.setMergeList(new ArrayList<WordTabMerge>());
                                                }
                                                wtab.getMergeList().add(merge);
                                            }
                                        }

                                        if (isDsTab) {// 数据源
                                            JSONObject params = JSONObject.parseObject(column.getString("params"));
                                            String tdsAlias = params.getString("tdsAlias");
                                            String tdsInParaAlias = params.getString("tdsInParaAlias");
                                            if (tdsInParaAlias == null) {
                                                tdsInParaAlias = "";
                                            }
//										String dataId = "0";
//										if(tdsInParaAlias.split("=").length>1) {
//											String parai = tdsInParaAlias.split("=")[1];
//											if("@xmId".equals(parai)) {
//												dataId="1";
//											}else if("@htId".equals(parai)) {
//												dataId="2";
//											}else if("@jtId".equals(parai)) {
//												dataId="3";
//											}
//										}
                                            WordTab tab = wtab;
                                            if (wtab != null && wtab.getRows() != null && wtab.getRows().size() > 0) {
                                                tabList.add(tab);
                                            }
                                            // 设置了数据源，进行变量输入，否则空着
                                            if (tdsAlias != null && tdsAlias.length() > 0) {
                                                tdsAlias = tdsAlias.replace(" ", "");
                                                // 因参数只支持字符、数值、下划线，对特殊字符进行替换，否则被组件判定为不规范，根据模板导出时，无法生成
                                                tdsInParaAlias = tdsInParaAlias.replace("=", "__d__");
                                                tdsInParaAlias = tdsInParaAlias.replace("|", "__1__");
                                                tdsInParaAlias = tdsInParaAlias.replace("@", "__a__");

                                                WordTab dstab = new WordTab();
                                                dstab.setIsDs(true);
                                                dstab.setDsAlias(
                                                        "{{#tds_" + tdsInParaAlias + "______" + tdsAlias + "}}");
                                                tabList.add(dstab);
                                            }

                                            wtab = new WordTab();
                                            wrow = null;
                                            currrow = 0;
                                            temprow = 0;
                                            continue;
                                        } else if (isTitle) {
                                            WordTab tab = wtab;
                                            if (wtab != null && wtab.getRows() != null && wtab.getRows().size() > 0) {
                                                tabList.add(tab);
                                            }

                                            WordTab ttab = new WordTab();
                                            ;
                                            ttab.setType("itemTitle");
                                            ttab.setTitle(label);
                                            tabList.add(ttab);

                                            wtab = new WordTab();
                                            wrow = null;
                                            currrow = 0;
                                            temprow = 0;
                                            continue;
                                        } else if (isRowPic) {
                                            WordTab tab = wtab;
                                            if (wtab != null && wtab.getRows() != null && wtab.getRows().size() > 0) {
                                                tabList.add(tab);
                                            }

                                            WordTab pictab = new WordTab();
                                            pictab.setIsPic(true);
                                            pictab.setInfo("{{@pic_" + dateCol + "}}");
                                            tabList.add(pictab);

                                            wtab = new WordTab();
                                            wrow = null;
                                            currrow = 0;
                                            temprow = 0;
                                            continue;
                                        } else if (isWord) {
                                            WordTab tab = wtab;
                                            if (wtab != null && wtab.getRows() != null && wtab.getRows().size() > 0) {
                                                tabList.add(tab);
                                            }

                                            WordTab wordtab = new WordTab();
                                            wordtab.setIsPic(true);
                                            wordtab.setInfo("____wordcom____" + dateCol);
                                            tabList.add(wordtab);

                                            wtab = new WordTab();
                                            wrow = null;
                                            currrow = 0;
                                            temprow = 0;
                                            continue;
                                        } else if (isRowNameMark) {
                                            WordTab tab = wtab;
                                            if (wtab != null && wtab.getRows() != null && wtab.getRows().size() > 0) {
                                                tabList.add(tab);
                                            }

                                            WordTab namemarktab = new WordTab();
                                            namemarktab.setIsPic(true);
                                            namemarktab.setInfo("{{@picnamemark}}");
                                            tabList.add(namemarktab);

                                            wtab = new WordTab();
                                            wrow = null;
                                            currrow = 0;
                                            temprow = 0;
                                            continue;
                                        }
                                    } else {
                                        if (k == 0 || groupFirstRow == 1 || wrow == null) {
                                            // 如果按分组拆分表，每个分组是一个表
                                            if (isSplitTab) {
                                                wtab = new WordTab();
                                            } else {
                                                // 如果查看表已有行数据，说明到了下一个分组，对上个分组进行处理；如果是新分组，初始化行数据
                                                if (wtab.getRows() != null && wtab.getRows().size() > 0) {
                                                    if (temprow != currrow) {
                                                        // 整体表格情况下，每个分组，第一列合并
                                                        WordTabMerge merge = new WordTabMerge();
                                                        merge.setFromCol(0);
                                                        merge.setToCol(0);
                                                        merge.setFromRow(temprow);
                                                        merge.setToRow(currrow);
                                                        if (wtab.getMergeList() == null) {
                                                            wtab.setMergeList(new ArrayList<WordTabMerge>());
                                                        }
                                                        wtab.getMergeList().add(merge);
                                                    }
                                                    currrow++;
                                                    temprow = currrow;
                                                } else {
                                                    List<WordTabRow> _rlist = new ArrayList<WordTabRow>();
                                                    wtab.setRows(_rlist);
                                                }

                                                // 分组title作为表格第一列输出
                                                List<WordTabCell> cellList = new ArrayList<WordTabCell>();
                                                WordTabCell cell = new WordTabCell();
                                                cell.setGridNum(0);
                                                cell.setText(groupLabel);
                                                cell.setIsVar(false);
                                                cellList.add(cell);

                                                // 定义分组第一行，并加入首列
                                                WordTabRow _row = new WordTabRow();
                                                _row.setCols(cellList);
                                                // 表格加入行
                                                wtab.getRows().add(_row);
                                                // 行句柄定位
                                                wrow = _row;

                                                colsum = 0;
                                            }

                                            groupFirstRow = 0;
                                        }
                                    }

                                    if (!display) {
                                        continue;
                                    }

                                    // 一个组件分为两列，名称和内容
                                    WordTabCell cell1 = new WordTabCell();
                                    cell1.setGridNum(span);
                                    cell1.setText(label);
                                    cell1.setIsVar(false);

                                    // 字段变量赋值，如果是图片，赋值图片变量
                                    String colVar = dateCol;
                                    if (isPic) {
                                        colVar = "@pic_" + span + "____" + dateCol.replace(".", "____");
                                    } else if (isNameMake) {
                                        colVar = "@picnamemark" + span;
                                    }
                                    WordTabCell cell2 = new WordTabCell();
                                    cell2.setGridNum(span);
                                    cell2.setText(prepend + ("".equals(colVar) ? "" : ("{{" + colVar + "}}")) + append);
                                    cell2.setIsVar(true);

                                    // 列栅格数进行累计
                                    colsum += span;

                                    // 如果超过列栅格最大值，进行换行，说明这个内容在下一行
                                    if (colsum > 24) {
                                        // 记录分组名为新行的第一个单元格
                                        WordTabCell groupcell = new WordTabCell();
                                        groupcell.setGridNum(0);
                                        groupcell.setText(groupLabel);
                                        groupcell.setIsVar(false);

                                        // 列信息写入
                                        List<WordTabCell> cellList = new ArrayList<WordTabCell>();
                                        cellList.add(groupcell);
                                        if ("title".equals(type)) {
                                            cellList.add(cell1);
                                        } else {
                                            cellList.add(cell1);
                                            cellList.add(cell2);
                                        }

                                        // 行信息写入
                                        WordTabRow _row = new WordTabRow();
                                        _row.setCols(cellList);
                                        wtab.getRows().add(_row);
                                        // 活动行句柄定位
                                        wrow = _row;
                                        // 栅格累计重置
                                        colsum = span;
                                        // 行计数累加
                                        currrow++;
                                    } else {
                                        // 同行累加单元格
                                        if ("title".equals(type)) {
                                            wrow.getCols().add(cell1);
                                        } else {
                                            wrow.getCols().add(cell1);
                                            wrow.getCols().add(cell2);
                                        }

                                    }
                                    if (span == 24) {
                                        // 整体表格情况下，每个分组，第一列合并
                                        WordTabMerge merge = new WordTabMerge();
                                        if ("title".equals(type)) {
                                            merge.setFromCol(1);
                                            merge.setToCol(4);
                                        } else {
                                            merge.setFromCol(2);
                                            merge.setToCol(4);
                                        }
                                        merge.setFromRow(currrow);
                                        merge.setToRow(currrow);
                                        if (wtab.getMergeList() == null) {
                                            wtab.setMergeList(new ArrayList<WordTabMerge>());
                                        }
                                        wtab.getMergeList().add(merge);
                                    }
                                }
                            }
                        }

                        if (wtab.getRows() != null && wtab.getRows().size() > 0 && temprow != currrow) {
                            tabList.add(wtab);
                            // 整体表格情况下，每个分组，第一列合并
                            WordTabMerge merge = new WordTabMerge();
                            merge.setFromCol(0);
                            merge.setToCol(0);
                            merge.setFromRow(temprow);
                            merge.setToRow(currrow);
                            if (wtab.getMergeList() == null) {
                                wtab.setMergeList(new ArrayList<WordTabMerge>());
                            }
                            wtab.getMergeList().add(merge);
                        } else if (tabList.isEmpty() || !tabList.contains(wtab)) {
                            tabList.add(wtab);
                        }
                    }
                }
            }
        }

        // 更新各表格中的行列数
        if (!tabList.isEmpty()) {
            for (WordTab tab : tabList) {
                if (tab.getRows() != null) {
                    tab.setRowNum(tab.getRows().size());

                    int maxcols = 0;
                    List<WordTabRow> rlist = tab.getRows();
                    for (WordTabRow row : rlist) {
                        int cols = row.getCols().size();
                        if (cols > maxcols) {
                            maxcols = cols;
                        }
                    }

                    tab.setColNum(maxcols);
                }
            }
        }

//		int titleNo = 1;
        for (WordTab tab : tabList) {

            if ("title".equals(tab.getType())) {
                insertBr(document);
                if (tab.getTitle() != null && tab.getTitle().length() > 0) {
                    // 标签标题
                    insertParagraph(document, tab.getTitle(), 16, true);
//        			addCustomHeadingStyle(document, (titleNo++)+"."+tab.getTitle(), 1);
                }
                insertBr(document);
            } else if ("itemTitle".equals(tab.getType())) {
//        		insertBr(document);
                if (tab.getTitle() != null && tab.getTitle().length() > 0) {
                    // 标签标题
                    insertParagraph(document, tab.getTitle(), 11, false);
                }
            } else if (tab.getIsDs() != null && tab.getIsDs()) {
//        		insertBr(document);
                insertParagraph(document, tab.getDsAlias(), null, false);
                insertBr(document);
            } else if (tab.getIsPic() != null && tab.getIsPic()) {
//        		insertBr(document);
                insertParagraph(document, tab.getInfo(), null, false);
                insertBr(document);
            } else {

                if (tab.getRows() == null || tab.getRows().size() == 0) {
                    continue;
                }
                // 表格
                XWPFTable infoTable = document.createTable();

                CTTblPr tblPr = infoTable.getCTTbl().getTblPr();
                CTTblLayoutType t = tblPr.isSetTblLayout() ? tblPr.getTblLayout() : tblPr.addNewTblLayout();
                t.setType(STTblLayoutType.FIXED);
                // 暂不定义表格整体宽度
//        		tblPr.getTblW().setType(STTblWidth.DXA);
//        		tblPr.getTblW().setW(new BigInteger("9072"));

//        		//设置指定宽度
//				CTTbl ttbl = infoTable.getCTTbl();
//				CTTblGrid tblGrid = ttbl.addNewTblGrid();
                int[] colWidths = new int[]{400, 1423, 2623, 1423, 2623};// 9072 默认A4宽度，5列
//				for (int i : colWidths) {
//				    CTTblGridCol gridCol = tblGrid.addNewGridCol();
//				    gridCol.setW(new BigInteger(i+""));
//				}

                Integer colNum = tab.getColNum();
                List<WordTabRow> rlist = tab.getRows();
                for (int i = 0, il = rlist.size(); i < il; i++) {
                    List<WordTabCell> clist = rlist.get(i).getCols();
                    // 第一行特殊处理，其他行创建并输出
                    if (i == 0) {
                        XWPFTableRow infoTableRow = infoTable.getRow(0);
                        setRowHeight(infoTableRow, 360);
                        for (int j = 0, jl = clist.size(); j < jl; j++) {
                            if (j == 0) {
                                setCell(infoTableRow, colWidths, clist, j, colNum, 11, true, true, true);
                            } else {
                                setCell(infoTableRow, colWidths, clist, j, colNum, 10, !clist.get(j).getIsVar(),
                                        !clist.get(j).getIsVar(), true);
                            }
                        }
                        if (clist.size() < colNum) {
                            for (int k = clist.size(), kl = colNum; k < kl; k++) {
                                setCell(infoTableRow, colWidths, clist, k, colNum, 10, false, false, true);
                            }
                        }
                    } else {
                        XWPFTableRow infoTableRow = infoTable.createRow();
                        setRowHeight(infoTableRow, 360);
                        for (int j = 0, jl = clist.size(); j < jl; j++) {
                            if (j == 0) {
                                setCell(infoTableRow, colWidths, clist, j, colNum, 11, true, true, true);
                            } else if (colWidths.length > j) {
                                setCell(infoTableRow, colWidths, clist, j, colNum, 10, !clist.get(j).getIsVar(),
                                        !clist.get(j).getIsVar(), true);
                            }
                        }
                        if (clist.size() < colNum) {
                            for (int k = clist.size(), kl = colNum; k < kl; k++) {
                                setCell(infoTableRow, colWidths, clist, k, colNum, 10, false, false, true);
                            }
                        }
                    }
                }
                // 合并处理
                if (tab.getMergeList() != null && tab.getMergeList().size() > 0) {
                    for (WordTabMerge mer : tab.getMergeList()) {
                        // 暂时只支持一个行、列合并
                        if (mer.getFromRow().equals(mer.getToRow())) {
                            if (mer.getToCol() + 1 > tab.getColNum()) {
                                mer.setToCol(tab.getColNum() - 1);
                            }
                            mergeCellsHorizontal(infoTable, mer.getFromRow(), mer.getFromCol(), mer.getToCol());
                        } else if (mer.getFromCol().equals(mer.getToCol())) {
                            mergeCellsVertically(infoTable, mer.getFromCol(), mer.getFromRow(), mer.getToRow());
                        }
                    }
                }
                insertBr(document);
            }
        }

        return document;
    }

    private void setRowHeight(XWPFTableRow infoTableRow, int height) {
        CTTrPr trPr = infoTableRow.getCtRow().addNewTrPr();
        CTHeight ht = trPr.addNewTrHeight();
        ht.setVal(BigInteger.valueOf(height));
    }

    private void setCell(XWPFTableRow infoTableRow, int[] colWidths, List<WordTabCell> cellList, int pos, int colNum,
                         int fontSize, Boolean bold, Boolean align, Boolean valign) {
        // 单元格获取
        XWPFTableCell cell = infoTableRow.getCell(pos);
        if (cell == null) {
            cell = infoTableRow.createCell();
        }
        // 设置垂直居中
        if (valign) {
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER); // 垂直居中
        }
        // 设置单元格宽度
        int cellWidth = 10;
        if (pos < colWidths.length) {
            cellWidth = colWidths[pos];
        }
        // 如果最后一个单元格比整体列少，扩充宽度
        if (colWidths.length > pos && colNum == pos + 1) {
            cellWidth = 0;
            for (int i = pos, il = colWidths.length; i < il; i++) {
                cellWidth += colWidths[i];
            }
        }
        CTTcPr ctTcPr = cell.getCTTc().isSetTcPr() ? cell.getCTTc().getTcPr() : cell.getCTTc().addNewTcPr();
        CTTblWidth ctTblWidth = ctTcPr.addNewTcW();
        ctTblWidth.setW(BigInteger.valueOf(cellWidth));
        ctTblWidth.setType(STTblWidth.DXA);

        // 单元格内容设置
        XWPFParagraph p = cell.getParagraphs().get(0);
        XWPFRun headRun0 = p.createRun();
        if (cellList.size() > pos) {
            headRun0.setText(cellList.get(pos).getText());
        } else {
            headRun0.setText("");
        }
        headRun0.setFontSize(fontSize);
        if (bold) {
            headRun0.setBold(bold);// 是否粗体
        }
        // 水平居中
        if (align) {
            p.setAlignment(ParagraphAlignment.CENTER);
        }
//        infoTableRow.getCell(j).setColor("DEDEDE");
    }

    // word插入换行
    private void insertBr(XWPFDocument document) {
        // 插入换行
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun paragraphRun = paragraph.createRun();
        paragraphRun.setText("\r");
    }

    private void insertParagraph(XWPFDocument document, String text, Integer fontSize, Boolean bold) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        if (fontSize != null) {
            run.setFontSize(fontSize);
        }
        if (bold) {
            run.setBold(bold);// 是否粗体
        }
    }

    private void insertParagraph(XWPFDocument document, String text, Integer fontSize, Boolean bold, String styleId) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        if (fontSize != null) {
            run.setFontSize(fontSize);
        }
        if (bold) {
            run.setBold(bold);// 是否粗体
        }
        if (styleId != null && styleId.length() > 0) {
            paragraph.setStyle(styleId);
        }
    }

//	//跨行合并单元格
//	public static void mergeCellsVertically(XWPFTable table, int col, int fromRow, int toRow) {
//		for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
//            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
//            if ( rowIndex == fromRow ) {
//                // The first merged cell is set with RESTART merge value
//                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
//            } else {
//                // Cells which join (merge) the first one, are set with CONTINUE
//                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
//            }
//        }
//	}
//
//	//跨列合并单元格
//	public static void mergeCellsHorizontal(XWPFTable table, int row, int fromCell, int toCell) {
//		for (int cellIndex = fromCell; cellIndex <= toCell; cellIndex++) {
//			XWPFTableCell cell = table.getRow(row).getCell(cellIndex);
//			if ( cellIndex == fromCell ) {
//				cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
//			} else {
//				cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
//			}
//		}
//	}

    /**
     * word单元格列合并
     *
     * @param table     表格
     * @param row       合并列所在行
     * @param startCell 开始列
     * @param endCell   结束列
     * @date 2021-7-10
     */
    public static void mergeCellsHorizontal(XWPFTable table, int row, int startCell, int endCell) {
        for (int i = startCell; i <= endCell; i++) {
            XWPFTableCell cell = table.getRow(row).getCell(i);
            if (i == startCell) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                cell.getCTTc().getPArray(0).removeR(0);
            }
        }
    }

    /**
     * word单元格行合并
     *
     * @param table    表格
     * @param col      合并行所在列
     * @param startRow 开始行
     * @param endRow   结束行
     * @date 2021-7-10
     */
    public static void mergeCellsVertically(XWPFTable table, int col, int startRow, int endRow) {
        for (int i = startRow; i <= endRow; i++) {
            XWPFTableCell cell = table.getRow(i).getCell(col);
            if (i == startRow) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
                cell.getCTTc().getPArray(0).removeR(0);
            }
        }
    }

    private static void addCustomHeadingStyle(XWPFDocument docxDocument, String strStyleId, int headingLevel) {
        CTStyle ctStyle = CTStyle.Factory.newInstance();
        ctStyle.setStyleId(strStyleId);

        CTString styleName = CTString.Factory.newInstance();
        styleName.setVal(strStyleId);
        ctStyle.setName(styleName);

        CTDecimalNumber indentNumber = CTDecimalNumber.Factory.newInstance();
        indentNumber.setVal(BigInteger.valueOf(headingLevel));
        // lower number > style is more prominent in the formats bar
        ctStyle.setUiPriority(indentNumber);
        CTOnOff onoffnull = CTOnOff.Factory.newInstance();
        ctStyle.setUnhideWhenUsed(onoffnull);
        // style shows up in the formats bar
        ctStyle.setQFormat(onoffnull);
        // style defines a heading of the given level

        CTPPr ppr = CTPPr.Factory.newInstance();
        ppr.setOutlineLvl(indentNumber);
        ctStyle.setPPr(ppr);

        XWPFStyle style = new XWPFStyle(ctStyle);
        // is a null op if already defined
        XWPFStyles styles = docxDocument.createStyles();
        style.setType(STStyleType.PARAGRAPH);
        styles.addStyle(style);
    }

    @Override
    public List<SysFilesInfo> getFilesInfoListByIds(List<String> idList) {
        if (idList == null || idList.size() <= 0) {
            return null;
        }
        Where where = Where.create();
        where.andIns(SysFilesInfo::getId, idList.toArray());
        List<SysFilesInfo> res = entityService.queryList(SysFilesInfo.class, where);
//		res.setResult(rvlist);
        return res;
    }

    public SysFilesInfo getFilesInfoById(String id) {
        SysFilesInfo obj = entityService.queryObjectById(SysFilesInfo.class, id);
        return obj;
    }

    @Override
    public NiceXWPFDocument exportWordFile(String param, String dataId, String tplId, List<String> tempWordList,
                                           List<String> signatures) {
        NiceXWPFDocument tpl = null;
        // 根据数据ID获取模板ID TODO
        Map<String, String> sendmap = new HashMap<String, String>();
        if (param != null && param.startsWith("{")) {
            try {
                JSONObject json = JSONObject.parseObject(param);
                for (String key : json.keySet()) {
                    String val = json.getString(key);
                    sendmap.put(key, val);
                }
            } catch (Exception e) {
            }
            param = "";
        }
        Map<String, String> pmap = new HashMap<String, String>();

        if (dataId != null && dataId.length() > 0) {
            tplId = dataService.getTplIdByDataId(dataId);

            if (sendmap.size() > 0) {
                pmap.put("businessKey", dataId);
                pmap.putAll(sendmap);
                String sjparam = "";
                for (String key : sendmap.keySet()) {
                    String val = sendmap.get(key);
                    if (param == null || param.indexOf(key + "=") == -1) {
                        sjparam += "|" + key + "=" + val;
                    }
                }
                if (sjparam.length() > 0) {
                    if (param == null || param.length() == 0) {
                        param = sjparam.substring(1);
                    } else {
                        param = param + sjparam;
                    }
                }
            }

//			try {//其他项目可能没有这个表
//				String sql = "select KSSJSTR,JSSJSTR from SCHEDULE_PERIOD_INFO where DATA_ID=?";
//				List<Object> _list = new ArrayList<Object>();
//				_list.add(dataId);
//				List<Map<String, Object>> jglist = entityService.queryListMap(sql, _list.toArray());
//				if(StringUtils.isNotEmpty(jglist)) {
//					Map<String, Object> _map = jglist.get(0);
//					String kssj = String.valueOf(_map.get("KSSJSTR"));
//					String jssj = String.valueOf(_map.get("JSSJSTR"));
//					pmap.put("kssj", kssj);
//					pmap.put("jssj", jssj);
//
//					String sjparam = "";
//					if(param==null || param.indexOf("kssj")==-1) {
//						sjparam+="|kssj="+kssj;
//					}
//					if(param==null || param.indexOf("jssj")==-1) {
//						sjparam+="|jssj="+jssj;
//					}
//					if(param==null || param.indexOf("businessKey")==-1) {
//						sjparam+="|businessKey="+dataId;
//					}
//					if(sjparam.length() > 0) {
//						if(param==null || param.length()==0) {
//							param = sjparam.substring(1);
//						}else {
//							param = param + sjparam;
//						}
//					}
//				}
//			} catch (Exception e) {
//			}

        } else {
            dataId = "";
        }
        List<SysFilesInfo> flitList = tplService.getTplFileList(tplId);
        if (flitList != null && !flitList.isEmpty()) {
            // 图片相关变量
            Map<String, Integer> picmap = new HashMap<String, Integer>();// 多个图片记录
            Map<String, String> picKeyId = new HashMap<String, String>();// 多个图片记录
            Map<String, Integer> picWmap = new HashMap<String, Integer>();// 多个图片记录
            Map<String, String> namemarkKeyId = new HashMap<String, String>();// 多个签名记录
            List<String> picidList = new ArrayList<String>();// 图片id
            // 换行文本相关变量
            Map<String, Integer> textmap = new HashMap<String, Integer>();// 换行文本记录
            List<String> textKeyList = new ArrayList<String>();// 文本记录
            Map<String, List<String>> textKeyId = new HashMap<String, List<String>>();// 换行文本记录
            // 纯数据源公式
            Map<String, Object> sourceDsgsMap = new HashMap<String, Object>();// <ftds_ds______get(0,"colname"),
            // $ds.get(0,"colname")>
            Map<String, String> jgDsgsMap = new HashMap<String, String>();// <$ds.get(0,"colname"), 公式解析结果>

            // 根据dataid获取对应formid的dataindexid
            Map<String, String> formDataIndexIdMap = dataService.getDataIndexIdMapByDataId(dataId);

            // 根据模板，获取单、复选、下拉菜单等对应的key,value信息
            Map<String, Map<String, String>> kvmap = getTplComMap(tplId);

            PoitlTools pt = new PoitlTools();
            List<XWPFTemplate> tplList = new ArrayList<XWPFTemplate>();
            List<String> varList = new ArrayList<String>();

            // 第一次循环，获取所有变量，进行赋值
            Map<String, Object> map = new HashMap<String, Object>();// 整理变量对应对象map
            Map<String, Object> rmap = new HashMap<String, Object>();// 最终模板替换变量时用的map对象
            List<String> tabList = new ArrayList<String>();// 记录变量中设置的数据表名
            Map<String, String> dsFiles = new HashMap<String, String>();// 记录数据源对应的word文件id
            // 循环模板文件，获取所有变量

            Map<String, File> tempFileMap = new HashMap<>();
            for (SysFilesInfo fi : flitList) {
                String suffix = fi.getFileExt();// 模板类型docx

//				File file = new File(fi.getFileAddress());// 模板地址D:/tm4UploadFiles/2022_11/完井总结报告（模板）1-71667370848545.docx

                // 20230421文件上传统一修改，不一定使用本地上传
                // 所以文件磁盘地址不一定存在
                // 创建缓存文件，使用后删除

                File file = createTempFileInDisk(fi);

                /*
                 * String tempFileDiskAdress = getTempFileDiskAdress(fi); File file = new
                 * File(tempFileDiskAdress); if (!file.exists()) { try { file.createNewFile(); }
                 * catch (IOException e) { } } InputStream fileSteam = fs.getFileSteam(fi);
                 * FileOutputStream fos = null; try { fos = new FileOutputStream(file);
                 * IOUtils.copy(fileSteam, fos); } catch (Exception e) {
                 *
                 * } finally { // try { // if (fileSteam != null) { // fileSteam.close(); // }
                 * // if (fos != null) { // fos.close(); // } // } catch (Exception e) {} }
                 */

                tempWordList.add(file.getAbsolutePath());
                tempFileMap.put(fi.getId(), file);

                List<String> fileVarList = pt.getVarList(file, suffix);
                varList.addAll(fileVarList);
                for (String var : fileVarList) {
                    if (var.startsWith("tds_")) {// 数据源公式
                        // var==tds_t_bd_zlfxbg______id__d____a__dataId______XMB______ZZVIFNUJ0121UV95WD0891
                        dsFiles.put(var.split("______")[0].substring(4), fi.getId());// ====================================dsFiles={t_bd_zlfxbg=ZZVQ442304XWS8MFD18829}
                    }
                    if (var.startsWith("ftds_")) {// 纯数据源公式
                        // var==ftds_ds______get(0,"colname")
                        String formula = "$" + var.substring(5).replace("______", ".");
                        sourceDsgsMap.put(var, formula);//
                        jgDsgsMap.put(formula, null);//
                    }
                    if (var.startsWith("$")) {// 纯数据源公式
                        // var==$ds______get(0,"colname")
                        String formula = "$" + var.substring(1).replace("______", ".");
                        sourceDsgsMap.put(var, formula);//
                        jgDsgsMap.put(formula, null);//
                    }
                }
            }

            // 根据变量信息进行拆分记录
            Map<String, String> tdscolMap = new HashMap<String, String>();// 内容对应数据源
            List<String> tdsList = new ArrayList<String>();// 数据源列表信息
            Map<String, List<Map<String, String>>> tdsValList = new HashMap<String, List<Map<String, String>>>();
            for (String string : varList) {
                if (!string.startsWith("#") && !string.startsWith("@") && string.indexOf(".") != -1) {
                    String[] s = string.split("\\.");
                    if (s.length == 3) {
                        if (map.containsKey(s[0])) {
                            Map<String, Object> tmap = ((Map) map.get(s[0]));
                            if (tmap.containsKey(s[1])) {
                                Map<String, Object> tmap2 = ((Map) tmap.get(s[1]));
                                tmap2.put(s[2], "");
                            } else {
                                Map<String, Object> tmap2 = new HashMap<String, Object>();
                                tmap2.put(s[2], "");
                                tmap.put(s[1], tmap2);
                            }
                        } else {
                            Map<String, Object> tmap = new HashMap<String, Object>();
                            Map<String, Object> tmap2 = new HashMap<String, Object>();
                            tmap2.put(s[2], "");
                            tmap.put(s[1], tmap2);
                            map.put(s[0], tmap);
                        }
                    } else if (s.length == 2) {
                        if (map.containsKey(s[0])) {
                            ((Map) map.get(s[0])).put(s[1], "");
                        } else {
                            tabList.add(s[0].toUpperCase());// ====================================所有的表名[ACT_BUSS_USER_JBSJ]
                            Map<String, String> tmap = new HashMap<String, String>();
                            tmap.put(s[1], "");// ==============
                            map.put(s[0], tmap);// ============={ACT_BUSS_USER_JBSJ={JH=}}
                        }
                    } else {
                        map.put(s[0], "");
                    }
                    textKeyList.add(string);
                } else {
                    map.put(string, "");

                    // 获取相关设置数据源的变量并整理
                    if (string.startsWith("tds_")) {// tds_数据源名称______条件内容______字段______formid
                        String[] ss = string.split("______");// tds_t_bd_zlfxbg______id__d____a__dataId______XMB______ZZVIFNUJ0121UV95WD0891
                        if (ss.length >= 4) {
                            String formid = ss[3], dataIndexId = "";
                            if (formDataIndexIdMap != null) {
                                String did = formDataIndexIdMap.get(formid);// ========dataindexid
                                if (did != null) {
                                    dataIndexId = did;
                                }
                            }
                            String tdsinfo = ss[0].substring(4) + "," + ss[1];// 数据源名称+条件===t_bd_zlfxbg,id__d____a__dataId
                            tdsinfo = tdsinfo.replace("__a__", "@");
                            tdsinfo = tdsinfo.replace("__1__", "|");
                            tdsinfo = tdsinfo.replace("__d__", "=");
                            tdsinfo = tdsinfo.replace("@dataid", dataId);
                            tdsinfo = tdsinfo.replace("@dataindexid", dataIndexId);
                            tdscolMap.put(tdsinfo + "____" + ss[2].toUpperCase(), string);// <数据源,条件____字段 , 变量>
                            if (!tdsList.contains(tdsinfo)) {
                                tdsList.add(tdsinfo);
                            }
                        }
                    }
                }

                // 记录图片相关数据，单独处理
                if (string.startsWith("@") && !string.startsWith("@picnamemark")) {
                    String zd = string.substring(5);
                    String[] zds = zd.split("____");
                    if (zds.length > 2) {
                        if (Coms.judgeLong(zds[0])) {
                            picWmap.put(string.substring(1), Integer.parseInt(zds[0]));
                            zd = zd.substring(zds[0].length() + 4).replace("____", ".");// zds[1] + "." + zds[2];
                            if (!tabList.contains(zds[1])) {
                                tabList.add(zds[1].toUpperCase());
                            }
                        } else {
                            zd = zd.replace("____", ".");
                            if (!tabList.contains(zds[0])) {
                                tabList.add(zds[0].toUpperCase());
                            }
                        }
                    } else {
                        zd = zd.replace("____", ".");
                        if (!tabList.contains(zds[0])) {
                            tabList.add(zds[0].toUpperCase());
                        }
                    }
                    picKeyId.put(string.substring(1), zd);// ========================={pic_ACT_BUSS_USER_tp____tp1=ACT_BUSS_USER_tp.tp1}
                }
                // 签名
                if (string.startsWith("@picnamemark")) {
                    if (signatures != null && signatures.size() > 0) {
                        namemarkKeyId.put(string.substring(1), Coms.listToString(signatures));
                    }
                }
            }

            // 获取数据
            List<String> idList = new ArrayList<String>();
            idList.add(dataId);
            Map<String, String> valmap = new HashMap<String, String>();
            if (!idList.isEmpty()) {
                Map<String, Map<String, String>> dmap = dataService.getSysTplData(idList, null);
                // 按顺序加载值
                for (String tid : idList) {
                    Map<String, String> _tmap = dmap.get(tid);
                    if (_tmap != null) {
                        // 接口key添加了formalias，这里暂时不支持，先屏蔽掉
                        Map<String, String> temp_map = new HashMap<String, String>();
                        for (String key : _tmap.keySet()) {
                            String val = _tmap.get(key);
                            String[] ks = key.split("\\.");
                            if (ks.length > 2) {
//								temp_map.put(ks[0] + "." + ks[1], val);//有别名的情况 tab.col.alias
                                temp_map.put(key, val);
                            } else {
                                temp_map.put(key, val);
                            }
                        }
                        valmap.putAll(temp_map);
                    }
                }
            }

            // 查询图片对应数据，整理图片地址，多个图片等相关处理并记录、整理
            if (picKeyId.size() > 0) {
                for (String key : picKeyId.keySet()) {
                    String tc = picKeyId.get(key);
                    String colval = valmap.get(tc.toUpperCase());
                    if (colval == null || "".equals(colval)) {// 无图片
                        picKeyId.put(key, "");
                    } else if (colval.indexOf(",") == -1) {// 单个图片
                        picKeyId.put(key, colval);

                        picidList.add(colval);
                    } else {// 多个图片
                        picKeyId.put(key, colval);
                        picmap.put("{{@" + key + "}}", colval.split(",").length);

                        String[] cs = colval.split(",");
                        for (int i = 0, il = cs.length; i < il; i++) {
                            picidList.add(cs[i]);
                        }
                    }
                }
                if (picidList.size() > 0) {
                    changeValmap(rmap, picidList, picKeyId, picWmap);
                }
            }

            // 签名图片的处理
            if (namemarkKeyId.size() > 0) {
                List<String> markidList = new ArrayList<String>();
                Map<String, Integer> markWmap = new HashMap<String, Integer>();
                for (String key : namemarkKeyId.keySet()) {
                    String namemarkid = namemarkKeyId.get(key);
                    String wspan = namemarkid.replace("@picnamemark", "");
                    if (Coms.judgeLong(wspan)) {
                        markWmap.put(key, Integer.parseInt(wspan));
                    }
                    if (namemarkid == null || "".equals(namemarkid)) {// 无签名
                        namemarkKeyId.put(key, "");
                    } else if (namemarkid.indexOf(",") == -1) {// 单个图片
                        namemarkKeyId.put(key, namemarkid);
                        markidList.add(namemarkid);
                    } else {// 多个图片
                        namemarkKeyId.put(key, namemarkid);
                        picmap.put("{{@" + key + "}}", namemarkid.split(",").length);// 加入到图片判断中，其他变量整合到图片中，导出时一并处理

                        String[] cs = namemarkid.split(",");
                        for (int i = 0, il = cs.length; i < il; i++) {
                            markidList.add(cs[i]);
                        }
                    }
                }
                if (markidList.size() > 0) {
                    changeValmap(rmap, markidList, namemarkKeyId, markWmap);

                }
            }

            // 换行字符处理
            if (textKeyList.size() > 0) {
                for (String key : textKeyList) {
                    String colval = valmap.get(key.toUpperCase());
                    if (colval == null || "".equals(colval)) {// 无内容
//						textKeyId.put(key, "");
                    } else if (colval.indexOf("\n") == -1) {// 文字无换行
//						textKeyId.put(key, colval);
//						valmap.put(key.toUpperCase(), colval.replace("\n", "\r\n"));
                    } else {// 文字换行
                        List<String> txtList = Coms.StrToList(colval, "\n");
                        textKeyId.put(key, txtList);
                        textmap.put("{{" + key + "}}", txtList.size());
                    }
                }
                if (textmap.size() > 0) {
                    changeValmapTxt(rmap, textKeyId);
                }
            }

            // 单元格数据源获取内容
            Map<String, List<Map<String, Object>>> dsFileMap = new HashMap<String, List<Map<String, Object>>>();// word模板id对应数据源列表值对象
            Map<String, List<List<Map<String, String>>>> dsvalMap = new HashMap<String, List<List<Map<String, String>>>>();
            if (tdsList != null && tdsList.size() > 0) {// tds_数据源名称______条件内容______字段______formid
                // 循环解析数据源，将值整理出针对文件的最终结果
                for (String string : tdsList) {
                    String[] ss = string.split(",");// 数据源名称,条件内容
                    String dsstr = ss[0];
                    String para = "";
                    if (ss.length > 1)
                        para = ss[1];
                    if (para.length() > 0) {
                        para = para + (param != null && param.length() > 0 ? "|" + param : "");
                    } else {
                        para = param;
                    }
                    List<Map<String, String>> data = getDsValCols(dsstr, para);
                    if (data.size() > 0) {
                        List<Map<String, String>> newdata = new ArrayList<Map<String, String>>();
                        for (Map<String, String> _map : data) {
                            Map<String, String> _nmap = new HashMap<String, String>();
                            if (_map != null) {
                                for (String key : _map.keySet()) {
                                    String val = _map.get(key);
                                    String newKey = tdscolMap.get(string + "____" + key.toUpperCase());
                                    _nmap.put(newKey, val);
                                }
                            }
                            newdata.add(_nmap);
                        }

                        String dsFileId = dsFiles.get(dsstr);
                        if (dsvalMap.containsKey(dsFileId)) {
                            dsvalMap.get(dsFileId).add(newdata);
                        } else {
                            List<List<Map<String, String>>> _list = new ArrayList<List<Map<String, String>>>();
                            _list.add(newdata);
                            dsvalMap.put(dsFileId, _list);
                        }
                    }
                }
                // 按文件循环，包括的数据源结果整理
                for (String dsFileId : dsvalMap.keySet()) {
                    List<List<Map<String, String>>> _list = dsvalMap.get(dsFileId);
                    // 获取同一文件中包括所有数据源结果的最大行数
                    int maxlen = 0;
                    for (List<Map<String, String>> vlist : _list) {
                        if (vlist.size() > maxlen) {
                            maxlen = vlist.size();
                        }
                    }

                    List<Map<String, Object>> fileDsVallist = new ArrayList<Map<String, Object>>();
                    for (int i = 0; i < maxlen; i++) {
                        Map<String, Object> _map = new HashMap<String, Object>();
                        for (List<Map<String, String>> vlist : _list) {
                            if (vlist.size() > i) {
                                _map.putAll(vlist.get(i));
                            }
                        }
                        fileDsVallist.add(_map);
                    }
                    dsFileMap.put(dsFileId, fileDsVallist);
                }
            }

            for (String key : map.keySet()) {
//				String[] ss = key.split("\\.");
                Object val = map.get(key);
                if (val instanceof Map) {// 组件下拉选择，值存的id，需要转化成对应txt // && val instanceof Map
                    Map<String, Object> tmap = (Map<String, Object>) val;
                    for (String key2 : tmap.keySet()) {//
                        Object val2 = tmap.get(key2);
                        if (val2 instanceof Map) {
                            Map<String, String> tmap2 = (Map<String, String>) val2;
                            for (String key3 : tmap2.keySet()) {
                                String newKey = key.toUpperCase() + "." + key2.toUpperCase() + "." + key3.toUpperCase();
                                Map<String, String> _tmap = kvmap.get(newKey);
                                if (valmap.containsKey(newKey)) {
                                    String v = valmap.get(newKey);
                                    v = v == null ? "" : v;
                                    if (v.startsWith("[],")) {
                                        v = v.replace("[],", "");
                                        v = replaceStrKeyVal(v, _tmap);
                                    } else if (v.startsWith("[]")) {
                                        v = v.replace("[]", "");
                                        v = replaceStrKeyVal(v, _tmap);
                                    } else if (v.startsWith("[") && v.endsWith("]")) {
                                        StringBuffer sb = new StringBuffer();
                                        JSONArray jarr = JSONArray.parseArray(v);
                                        if (jarr != null && jarr.size() > 0) {
                                            for (int i = 0, il = jarr.size(); i < il; i++) {
                                                if (i > 0) {
                                                    sb.append(",");
                                                }
                                                sb.append(replaceKeyVal(jarr.getString(i), _tmap));
                                            }
                                        }
                                        if (sb.length() > 0)
                                            v = sb.toString();
                                    } else {
                                        if (_tmap != null && _tmap.get(v) != null) {
                                            v = _tmap.get(v);
                                        }
                                    }
                                    tmap2.put(key3, v);
                                }
                            }
                        } else {
                            String newKey = key.toUpperCase() + "." + key2.toUpperCase();
                            Map<String, String> _tmap = kvmap.get(newKey);
                            if (valmap.containsKey(newKey)) {
                                String v = valmap.get(newKey);
                                v = v == null ? "" : v;
                                if (v.startsWith("[],")) {
                                    v = v.replace("[],", "");
                                    v = replaceStrKeyVal(v, _tmap);
                                } else if (v.startsWith("[]")) {
                                    v = v.replace("[]", "");
                                    v = replaceStrKeyVal(v, _tmap);
                                } else if (v.startsWith("[") && v.endsWith("]")) {
                                    StringBuffer sb = new StringBuffer();
                                    JSONArray jarr = JSONArray.parseArray(v);
                                    if (jarr != null && jarr.size() > 0) {
                                        for (int i = 0, il = jarr.size(); i < il; i++) {
                                            if (i > 0) {
                                                sb.append(",");
                                            }
                                            sb.append(replaceKeyVal(jarr.getString(i), _tmap));
                                        }
                                    }
                                    if (sb.length() > 0)
                                        v = sb.toString();
                                } else {
                                    if (_tmap != null && _tmap.get(v) != null) {
                                        v = _tmap.get(v);
                                    }
                                }
                                tmap.put(key2, v);
                            }
                        }

                    }
                    if (rmap.containsKey(key) && StringUtils.isNotEmpty(tmap)) {
                        ((Map) rmap.get(key)).putAll(tmap);
                    } else {
                        rmap.put(key, tmap);
                    }

//					if(ss.length == 2) {
//						Map<String, String> tmap = (Map<String, String>) map.get(key);
//						for (String key2 : tmap.keySet()) {//
//							String newKey = key.toUpperCase() + "." + key2.toUpperCase();
//							Map<String, String> _tmap = kvmap.get(newKey);
//							if (valmap.containsKey(newKey)) {
//								String v = valmap.get(newKey);
//								v = v == null ? "" : v;
//								if (v.startsWith("[],")) {
//									v = v.replace("[],", "");
//									v = replaceStrKeyVal(v, _tmap);
//								} else if (v.startsWith("[]")) {
//									v = v.replace("[]", "");
//									v = replaceStrKeyVal(v, _tmap);
//								} else if (v.startsWith("[") && v.endsWith("]")) {
//									StringBuffer sb = new StringBuffer();
//									JSONArray jarr = JSONArray.parseArray(v);
//									if (jarr != null && jarr.size() > 0) {
//										for (int i = 0, il = jarr.size(); i < il; i++) {
//											if (i > 0) {
//												sb.append(",");
//											}
//											sb.append(replaceKeyVal(jarr.getString(i), _tmap));
//										}
//									}
//									if (sb.length() > 0)
//										v = sb.toString();
//								} else {
//									if (_tmap != null && _tmap.get(v) != null) {
//										v = _tmap.get(v);
//									}
//								}
//								tmap.put(key2, v);
//							}
//						}
//						if(rmap.containsKey(key) && StringUtils.isNotEmpty(tmap)) {
//							((Map)rmap.get(key)).putAll(tmap);
//						}else {
//							rmap.put(key, tmap);
//						}
//					}else if(ss.length == 3) {
//						Map<String, Object> tmap2 = (Map<String, Object>) map.get(key);
//						for (String key2 : tmap2.keySet()) {
//							Map<String, String> tmap = (Map<String, String>) tmap2.get(key);
//							for (String key3 : tmap.keySet()) {// 有两个.的情况
//								String newKey = key.toUpperCase() + "." + key2.toUpperCase() + "." + key3.toUpperCase();
//								Map<String, String> _tmap = kvmap.get(newKey);
//								if (valmap.containsKey(newKey)) {
//									String v = valmap.get(newKey);
//									v = v == null ? "" : v;
//									if (v.startsWith("[],")) {
//										v = v.replace("[],", "");
//										v = replaceStrKeyVal(v, _tmap);
//									} else if (v.startsWith("[]")) {
//										v = v.replace("[]", "");
//										v = replaceStrKeyVal(v, _tmap);
//									} else if (v.startsWith("[") && v.endsWith("]")) {
//										StringBuffer sb = new StringBuffer();
//										JSONArray jarr = JSONArray.parseArray(v);
//										if (jarr != null && jarr.size() > 0) {
//											for (int i = 0, il = jarr.size(); i < il; i++) {
//												if (i > 0) {
//													sb.append(",");
//												}
//												sb.append(replaceKeyVal(jarr.getString(i), _tmap));
//											}
//										}
//										if (sb.length() > 0)
//											v = sb.toString();
//									} else {
//										if (_tmap != null && _tmap.get(v) != null) {
//											v = _tmap.get(v);
//										}
//									}
//									tmap.put(key3, v);
//								}
//							}
////							if(rmap.containsKey(key) && StringUtils.isNotEmpty(tmap2)) {
////								Map<String, Object> _tmap = (Map)rmap.get(key2);
////								if(_tmap != null) {// && StringUtils.isNotEmpty(tmap2)
////									_tmap.putAll(tmap);
////								}else {
////									rmap.put(key, tmap2);
////								}
////							}else {
////								rmap.put(key, tmap2);
////							}
//						}
//						rmap.put(key, tmap2);
//					}
                } else {
                    if (key.startsWith("#")) {
                        Double allWidth = 18.8d;
                        List<List<String>> data = null;
                        double[] colWidths = null;
                        List<List<WordTilte>> titleList = new ArrayList<List<WordTilte>>();
                        List<Integer> combineColList = new ArrayList<Integer>();
                        if (key.startsWith("#tds_")) {
                            try {
//								String tdataId = dataId;
                                String dsstr = key.replace("#tds_", "");
                                String[] dss = dsstr.split("______");// 参数分割字符
                                String para = dss[0];
                                dsstr = dss[1];
                                String dataIndexId = "";
                                if (dss.length > 2) {
                                    String formid = dss[2];
                                    if (formDataIndexIdMap != null) {
                                        String did = formDataIndexIdMap.get(formid);
                                        if (did != null) {
                                            dataIndexId = did;
                                        }
                                    }
                                }
                                if ("".equals(para)) {
                                    para = "FORM_DATA_INDEX_ID=" + dataId;
                                } else {
                                    // 因参数只支持字符、数值、下划线，对特殊字符进行替换，否则被组件判定为不规范，根据模板导出时，无法生成。接收时做转换
                                    para = para.replace("__a__", "@");
                                    para = para.replace("__1__", "|");
                                    para = para.replace("__d__", "=");
                                    para = para.replace("@dataid", dataId);
                                    para = para.replace("@dataindexid", dataIndexId);
                                }
                                List<Double> wlist = new ArrayList<Double>();
                                List<Double> tlist = new ArrayList<Double>();
                                tlist.add(null);

                                if (!StringUtils.isEmpty(param) && param.indexOf("=") != -1) {
                                    if (para.length() > 0) {
                                        para = para + "|" + param;
                                    } else {
                                        para = param;
                                    }
                                }

                                data = getDsVal(dsstr, para, wlist, tlist, titleList, combineColList);
                                if (tlist.get(0) != null) {
                                    allWidth = tlist.get(0);
                                }
                                if (wlist.size() > 0) {
                                    colWidths = new double[wlist.size()];
                                    for (int j = 0; j < wlist.size(); j++) {
                                        colWidths[j] = wlist.get(j);
                                    }
                                }
                            } catch (Exception e) {
                                log.info("数据源解析出错，" + key);
                            }
                        }
                        if (data == null) {
                            rmap.put(key.substring(1), null);
                        } else {
                            rmap.put(key.substring(1),
                                    pt.getTabObj(data, false, 2, null, colWidths, allWidth, titleList, combineColList));
                        }
                        titleList = null;
                    } else {
                        if (valmap.containsKey(key.toUpperCase())) {
                            String v = valmap.get(key.toUpperCase());
                            v = v == null ? "" : v;
                            rmap.put(key, v);
                        }
                    }
                }
            }

            // 纯数据源公式解析
            if (StringUtils.isNotEmpty(sourceDsgsMap)) {
                TDataSourceManager tsm = new TDataSourceManager();
                List<String> dsList = new ArrayList<String>();// 分析数据源
                HashMap<String, IDataSource> idsMap = new HashMap<String, IDataSource>();
                for (String gs : jgDsgsMap.keySet()) {
                    List<String> _dsList = tsm.parseDataSourceAliasList(gs);// 分析数据源
                    for (String ds : _dsList) {
                        if (!dsList.contains(ds)) {
                            dsList.add(ds);
                            IDataSource ids = tsm.getDataSource(ds);
                            if (ids != null) {
                                ids.parseInParaDefaultValue();
                                if (StringUtils.isNotEmpty(pmap)) {
                                    for (String inparaKey : pmap.keySet()) {
                                        TInPara inPara = ids.getInParaByAlias(inparaKey);
                                        if (inPara != null) {
                                            ids.setInParaByAlias(inparaKey, pmap.get(inparaKey));
                                        }
                                    }
                                }
                                ids.load();
                                idsMap.put(ds, ids);
                            }
                        }
                    }
                }
                for (String gs : jgDsgsMap.keySet()) {
                    Object jg = tsm.getScriptValue(gs, pmap, idsMap);
                    String val = "";
                    if (jg != null) {
                        val = String.valueOf(jg);
                    }
                    for (String key : sourceDsgsMap.keySet()) {
                        String value = String.valueOf(sourceDsgsMap.get(key));
                        if (gs.equals(value)) {
                            sourceDsgsMap.put(key, val);
                            rebuildMap(sourceDsgsMap, key);
                            break;
                        }
                    }
                }
            }

            // 第二次循环，赋值并生成word并合并
            // 如果有多个图片，需要调整原word模板文件，另存文件到服务器上，用完之后删除
            for (SysFilesInfo fi : flitList) {
                // 根据图片是否多个进行判断，调整模板内容，将{{@pic}}改为多个{{@pic______1}} {{@pic______2}}等等（前期map需要调整）
                // 处理特殊word表格，如果为一条记录一个表，多条记录时，循环处理
//				File file = new File(fi.getFileAddress());

                // 从缓存map中获取文件
                File file = tempFileMap.get(fi.getId());

                List<Map<String, Object>> vmaplist = dsFileMap.get(fi.getId());
                if (vmaplist != null && vmaplist.size() > 1) {
                    Map<String, Object> tempmap = copyMap(rmap);
                    for (int i = 0, il = vmaplist.size(); i < il; i++) {
                        tempmap.putAll(vmaplist.get(i));
                        if (StringUtils.isNotEmpty(sourceDsgsMap)) {
                            combineMap(tempmap, sourceDsgsMap);
//							tempmap.putAll(sourceDsgsMap);
                        }
                    }
                    if (picmap.isEmpty() && textmap.isEmpty()) {// 无图片或文字换行
                        tplList.add(pt.getWord(file, tempmap));
                    } else {
                        if (tempWordList == null) {
                            tempWordList = new ArrayList<String>();
                        }
                        File tempFile = file;
                        if (!picmap.isEmpty()) {
                            File newFile = changeWordTpl(file, picmap, tempWordList);
                            tplList.add(pt.getWord(newFile, tempmap));
                            tempFile = newFile;
                        }
                        if (!textmap.isEmpty()) {
                            File newFile = changeWordTplTxt(tempFile, textmap, tempWordList);
                            tplList.add(pt.getWord(newFile, tempmap));
                        }
                    }
                } else {
                    Map<String, Object> tempmap = copyMap(rmap);
                    if (vmaplist != null && vmaplist.size() == 1) {
                        tempmap.putAll(vmaplist.get(0));
                    }
                    if (StringUtils.isNotEmpty(sourceDsgsMap)) {
                        combineMap(tempmap, sourceDsgsMap);
//						tempmap.putAll(sourceDsgsMap);
                    }
                    if (picmap.isEmpty() && textmap.isEmpty()) {// 无图片或文字换行
                        tplList.add(pt.getWord(file, tempmap));
                    } else {
                        if (tempWordList == null) {
                            tempWordList = new ArrayList<String>();
                        }
                        File tempFile = file;
                        if (!picmap.isEmpty()) {
                            File newFile = changeWordTpl(file, picmap, tempWordList);
                            tplList.add(pt.getWord(newFile, tempmap));
                            tempFile = newFile;
                        }
                        if (!textmap.isEmpty()) {
                            File newFile = changeWordTplTxt(tempFile, textmap, tempWordList);
                            tplList.add(pt.getWord(newFile, tempmap));
                        }
                    }
                }

            }

            List<NiceXWPFDocument> nxdList = insertWordCom(tplList, valmap);

            tpl = pt.combineWordNXD(nxdList);// 合并多个word模板

            // 判断字符串并生成目录
            boolean pause = false;
            for (XWPFParagraph p : tpl.getParagraphs()) {
                for (XWPFRun r : p.getRuns()) {
                    int pos = r.getTextPosition();
                    String text = r.getText(pos);
                    if (text != null && text.contains(this.ttocString)) {
                        text = text.replace(this.ttocString, "");
                        r.setText(text, 0);
                        addField(p, " TOC \\o \"1-3\" \\h \\z \\u ");
//                        addField(p, "TOC \\h");
                        pause = true;
                        break;
                    }
                }
                if (pause) {
                    break;
                }
            }

            map = null;
            rmap = null;
            idList = null;
            picmap = null;
            picKeyId = null;
            picidList = null;
            namemarkKeyId = null;
            tplList = null;
        }
        return tpl;
    }

    private void combineMap(Map<String, Object> tempmap, Map<String, Object> sourceDsgsMap) {
        for (String key : sourceDsgsMap.keySet()) {
            if (tempmap.containsKey(key)) {
                Object obj = sourceDsgsMap.get(key);
                if (obj != null) {
                    Object oldVal = tempmap.get(key);
                    if (obj instanceof String) {
                        if (oldVal instanceof String || oldVal == null) {// 都是字符串，进行替换；否则不变
                            tempmap.put(key, obj);
                        }
                    } else if (obj instanceof Map) {
                        if (oldVal instanceof Map) {
                            Map<String, Object> oldtmap = (Map) oldVal;
                            Map<String, Object> tmap = (Map) obj;
                            for (String key2 : tmap.keySet()) {
                                Object val2 = tmap.get(key2);
                                if (val2 != null) {
                                    oldtmap.put(key2, val2);
                                }
                            }
                        } else {
                            tempmap.put(key, sourceDsgsMap.get(key));
                        }
                    }
                }
            } else {
                tempmap.put(key, sourceDsgsMap.get(key));
            }
        }

    }

    private void rebuildMap(Map<String, Object> sourceDsgsMap, String key) {
        if (key.indexOf(".") != -1) {
            String[] s = key.split("\\.");
            Object val = sourceDsgsMap.get(key);
            // 暂时只支持最多两个点.
            if (s.length == 2) {
                if (sourceDsgsMap.containsKey(s[0])) {
                    Map<String, Object> tmap = ((Map) sourceDsgsMap.get(s[0]));
                    if (val instanceof List) {
                        List<String> _list = (List<String>) val;
                        for (int i = 0, il = _list.size(); i < il; i++) {
                            tmap.put(s[1] + "____" + i, _list.get(i));
                        }
                    } else {
                        tmap.put(s[1], val);
                    }
                } else {
                    Map<String, Object> tmap = new HashMap<String, Object>();
                    if (val instanceof List) {
                        List<String> _list = (List<String>) val;
                        for (int i = 0, il = _list.size(); i < il; i++) {
                            tmap.put(s[1] + "____" + i, _list.get(i));
                        }
                    } else {
                        tmap.put(s[1], val);
                    }
                    sourceDsgsMap.put(s[0], tmap);
                }
            } else if (s.length == 3) {
                if (sourceDsgsMap.containsKey(s[0])) {
                    Map<String, Object> tmap = (Map) sourceDsgsMap.get(s[0]);
                    if (tmap.containsKey(s[1])) {
                        Map<String, Object> _tmap = ((Map) tmap.get(s[1]));
                        if (val instanceof List) {
                            List<String> _list = (List<String>) val;
                            for (int i = 0, il = _list.size(); i < il; i++) {
                                _tmap.put(s[2] + "____" + i, _list.get(i));
                            }
                        } else {
                            _tmap.put(s[2], val);
                        }
                    } else {
                        Map<String, Object> _tmap = new HashMap<String, Object>();
                        if (val instanceof List) {
                            List<String> _list = (List<String>) val;
                            for (int i = 0, il = _list.size(); i < il; i++) {
                                _tmap.put(s[2] + "____" + i, _list.get(i));
                            }
                        } else {
                            _tmap.put(s[2], val);
                        }
                        tmap.put(s[1], _tmap);
                    }

                } else {
                    Map<String, Object> _tmap = new HashMap<String, Object>();
                    if (val instanceof List) {
                        List<String> _list = (List<String>) val;
                        for (int i = 0, il = _list.size(); i < il; i++) {
                            _tmap.put(s[2] + "_" + i, _list.get(i));
                        }
                    } else {
                        _tmap.put(s[2], val);
                    }
                    Map<String, Object> tmap = new HashMap<String, Object>();
                    tmap.put(s[1], _tmap);
                    sourceDsgsMap.put(s[0], tmap);
                }
            }

        }
    }

    private List<NiceXWPFDocument> insertWordCom(List<XWPFTemplate> tplList, Map<String, String> valmap) {
        List<NiceXWPFDocument> nlist = new ArrayList<NiceXWPFDocument>();
        for (XWPFTemplate xtpl : tplList) {
            // 确定文档中有多少个word组件
            NiceXWPFDocument xwpf = xtpl.getXWPFDocument();
            int wordComNum = getWordComNum(xwpf);
            // 循环组件个数
            for (int i = 0; i < wordComNum; i++) {
                // 查找
                XWPFRun temprun = null;
                NiceXWPFDocument xwpf_target = null;
//				FileInputStream in_target = null;

//        		List<XWPFParagraph> plist = xwpf.getParagraphs();
//        		for (XWPFParagraph paragraph : plist) {
//				}

                List<IBodyElement> elements = xwpf.getBodyElements();
                for (IBodyElement element : elements) {
                    // 段落
                    if (element instanceof XWPFParagraph) {

                        StringBuffer runText = new StringBuffer();
                        XWPFParagraph paragraph = (XWPFParagraph) element;
                        List<XWPFRun> runs = paragraph.getRuns();
                        if (runs.size() > 0) {
                            for (XWPFRun run : runs) {
                                runText.append(run.text());
                            }
                        }
                        if (runText.length() > 0) {
                            int start = runText.indexOf("____wordcom");
                            if (start >= 0) {//if (runText.toString().startsWith("____wordcom")) {
                                int ri = runs.size();
                                for (int j = ri - 1; j > 0; j--) {
                                    paragraph.removeRun(j);
                                }
                                temprun = runs.get(0);
                                temprun.setText("");
                                String inWordCol = runText.toString().substring(start + 15);//String inWordCol = runText.toString().substring(15);
                                String wordId = valmap.get(inWordCol.toUpperCase());
                                if (wordId != null && wordId.length() > 0) {
                                    try {
                                        JSONObject obj = JSONObject.parseObject(wordId);
                                        wordId = obj.getString("id");
                                    } catch (Exception e) {
                                    }
                                    if (wordId != null && wordId.length() > 0) {
                                        SysFilesInfo wordflie = getFileById(wordId);
                                        InputStream fileInputStream = fs.getFileSteam(wordflie);
                                        if (fileInputStream != null) {
                                            try {
                                                xwpf_target = new NiceXWPFDocument(fileInputStream);
                                            } catch (IOException e1) {
                                            } finally {
                                                try {
                                                    fileInputStream.close();
                                                } catch (IOException e) {
                                                }
                                            }
                                        }
//										String address = wordflie.getFileAddress();
//										try {
//											in_target = new FileInputStream(new File(address));
//										} catch (FileNotFoundException e1) {
//										}
//										if (in_target != null) {
//											try {
//												xwpf_target = new NiceXWPFDocument(in_target);
//											} catch (IOException e1) {
//											} finally {
//												try {
//													in_target.close();
//												} catch (IOException e) {
//												}
//												in_target = null;
//											}
//										}
                                        if (xwpf_target != null) {
                                            break;
                                        }
                                    }
                                } else {
                                    paragraph.removeRun(0);//没上传word 删除标识
                                }
                            }
                        }
                    }
                }

                if (xwpf_target != null) {
                    // 插入word内容后，重新开始处理
                    try {
                        xwpf = xwpf.merge(Arrays.asList(xwpf_target), temprun);
                    } catch (Exception e) {
                        log.error("word insert error:" + e.getMessage());
                    } finally {
//						if (in_target != null) {
                        try {
                            xwpf_target.close();
                        } catch (IOException e) {
                        }
//							in_target = null;
//						}
                    }
                }
            }

            nlist.add(xwpf);
        }
        return nlist;
    }

    private int getWordComNum(NiceXWPFDocument xwpf) {
        int count = 0;
        List<IBodyElement> elements = xwpf.getBodyElements();
        for (IBodyElement element : elements) {
            // 段落
            if (element instanceof XWPFParagraph) {
                StringBuffer runText = new StringBuffer();
                XWPFParagraph paragraph = (XWPFParagraph) element;
                List<XWPFRun> runs = paragraph.getRuns();
                if (runs.size() > 0) {
                    for (XWPFRun run : runs) {
                        runText.append(run.text());
                    }
                    if (runText.length() > 0) {
                        if (runText.toString().indexOf("____wordcom") >= 0) {//if (runText.toString().startsWith("____wordcom")) {
                            count++;
                        }
                    }
                }
            }
        }
        return count;
    }

    @Override
    public void clearTempWordFile(List<String> tempWordList) {
        // 删除临时转化文件
        if (tempWordList != null && tempWordList.size() > 0) {
            for (String addr : tempWordList) {
                File delFile = new File(addr);
                if (delFile.exists()) {
                    delFile.delete();
                }
            }
        }
    }

    private Map<String, Object> copyMap(Map<String, Object> rmap) {
        Map<String, Object> map = new HashMap<String, Object>();
        for (String key : rmap.keySet()) {
            Object value = rmap.get(key);
            if (value instanceof Map) {
                Map<String, Object> map2 = new HashMap<String, Object>();
                Map<String, Object> tmap = (Map<String, Object>) value;
                for (String key2 : tmap.keySet()) {
                    map2.put(key2, tmap.get(key2));
                }
                map.put(key, map2);
            } else {
                map.put(key, value);
            }
        }
        return map;
    }

    // 整理图片并插入对应值map中
    private void changeValmap(Map<String, Object> valmap, List<String> picidList, Map<String, String> picKeyId,
                              Map<String, Integer> picWmap) {
        Map<String, Map<String, String>> picIdAddr = new HashMap<String, Map<String, String>>();// 多个图片记录
        // 根据id获取图片地址，同步宽高并回存， 整理图片地址数据 picIdAddr TODO
        boolean update = false;
        List<SysFilesInfo> fileList = getListByIds(picidList);
        Map<String, SysFilesInfo> fileMap = fileList == null ? null
                : fileList.stream().collect(Collectors.toMap(SysFilesInfo::getId, v -> v));
        for (SysFilesInfo fi : fileList) {
            Map<String, String> tmap = new HashMap<String, String>();
            tmap.put("addr", fi.getFileAddress());
            tmap.put("fileId", fi.getId());
            if (fi.getPicWidth() != null && fi.getPicHeight() != null) {
                tmap.put("w", fi.getPicWidth().toString());
                tmap.put("h", fi.getPicHeight().toString());
            } else {
                // 获取并设置高度宽度
                InputStream inputStream = null;
                try {
//					BufferedImage bi = ImageIO.read(new FileInputStream(new File(fi.getFileAddress())));
                    inputStream = fs.getFileSteam(fi);
                    BufferedImage bi = ImageIO.read(inputStream);
                    int w = bi.getWidth();
                    int h = bi.getHeight();
                    fi.setPicWidth(w);
                    fi.setPicHeight(h);
                    tmap.put("w", fi.getPicWidth().toString());
                    tmap.put("h", fi.getPicHeight().toString());
                    update = true;
                } catch (Exception e) {
                    log.error("get pic width and height", e.getMessage());
                } finally {
                    try {
                        if (inputStream != null) {
                            inputStream.close();
                        }
                    } catch (Exception e) {
                    }
                }
            }
            picIdAddr.put(fi.getId(), tmap);
        }
        if (update) {
            updateFilesInfos(fileList);
        }
        // 插入valmap
        for (String key : picKeyId.keySet()) {
            String val = picKeyId.get(key);
            Integer setw = picWmap.get(key);
            if (val.indexOf(",") == -1) {
                Map<String, String> tmap = picIdAddr.get(val);
                if (tmap != null) {
//					String addr = tmap.get("addr");
                    String fileId = tmap.get("fileId");
                    int w = tmap.get("w") == null ? 300 : Integer.parseInt(tmap.get("w"));
                    int h = tmap.get("h") == null ? 300 : Integer.parseInt(tmap.get("h"));
                    List<Integer> whChange = getWH(w, h, 595, setw);
                    SysFilesInfo fi = fileMap == null ? null : fileMap.get(fileId);
                    InputStream inputStream = null;
                    try {
                        inputStream = fs.getFileSteam(fi);
                        valmap.put(key, Pictures.ofStream(inputStream, PictureType.JPEG)
                                .size(whChange.get(0), whChange.get(1)).create());
                    } catch (Exception e) {
//						e.printStackTrace();
                        log.error("file insert error by init pic", e);
                    } finally {
                        try {
                            if (inputStream != null) {
                                inputStream.close();
                            }
                        } catch (Exception e) {
                        }
                    }
                }
            } else {
                String[] picids = val.split(",");
                for (int i = 0, il = picids.length; i < il; i++) {
                    String picid = picids[i];
                    Map<String, String> tmap = picIdAddr.get(picid);
                    if (tmap != null) {
//						String addr = tmap.get("addr");
                        String fileId = tmap.get("fileId");
                        int w = tmap.get("w") == null ? 300 : Integer.parseInt(tmap.get("w"));
                        int h = tmap.get("h") == null ? 300 : Integer.parseInt(tmap.get("h"));
                        List<Integer> whChange = getWH(w, h, 595, setw);
                        SysFilesInfo fi = fileMap == null ? null : fileMap.get(fileId);
                        InputStream inputStream = null;
                        try {
                            inputStream = fs.getFileSteam(fi);
                            valmap.put(key + "____" + i, Pictures.ofStream(inputStream, PictureType.JPEG)
                                    .size(whChange.get(0), whChange.get(1)).create());
                        } catch (Exception e) {
//							e.printStackTrace();
                            log.error("file insert error by init pic", e);
                        } finally {
                            try {
                                if (inputStream != null) {
                                    inputStream.close();
                                }
                            } catch (Exception e) {
                            }
                        }
                    }
                }
            }
        }
    }

    // 整理文字并插入对应值map中
    private void changeValmapTxt(Map<String, Object> valmap, Map<String, List<String>> txtKeyId) {

        // 插入valmap
        for (String key : txtKeyId.keySet()) {
            List<String> vals = txtKeyId.get(key);
            if (StringUtils.isNotEmpty(vals)) {
                for (int i = 0, il = vals.size(); i < il; i++) {
                    String val = vals.get(i);
                    if (key.indexOf(".") != -1) {
                        String[] ss = key.split("\\.");
                        if (ss.length == 3) {
                            if (valmap.containsKey(ss[0])) {
                                Map<String, Object> tmap = (Map) valmap.get(ss[0]);
                                if (tmap.containsKey(ss[1])) {
                                    ((Map) tmap.get(ss[1])).put(ss[2] + "____" + i, val);
                                } else {
                                    Map<String, String> _tmap = new HashMap<String, String>();
                                    _tmap.put(ss[2] + "____" + i, val);
                                    tmap.put(ss[1], _tmap);
                                }
                            } else {
                                Map<String, String> _tmap = new HashMap<String, String>();
                                _tmap.put(ss[2] + "____" + i, val);
                                Map<String, Object> tmap = new HashMap<String, Object>();
                                tmap.put(ss[1], _tmap);
                                valmap.put(ss[0], tmap);
                            }
                        } else if (ss.length == 2) {
                            if (valmap.containsKey(ss[0])) {
                                ((Map) valmap.get(ss[0])).put(ss[1] + "____" + i, val);
                            } else {
                                Map<String, String> tmap = new HashMap<String, String>();
                                tmap.put(ss[1] + "____" + i, val);
                                valmap.put(ss[0], tmap);
                            }
                        }
                    } else {
                        valmap.put(key + "____" + i, val);
                    }
                }
            }
        }
    }

    // 图片宽度高度调整，按比例缩小
    private List<Integer> getWH(int w, int h, int i, Integer setw) {
        /*
         * Word文档一般默认的是（国际标准）A4纸型，长宽分别为：210 X 297毫米。 至于是多少像素，得看设定的分辨率是多少。
         * 当设定的分辨率是72像素/英寸时，A4纸的尺寸图像像素为：595×842；
         * 当设定的分辨率是150像素/英寸时，A4纸的尺寸图像像素为：1240×1754；
         * 当设定的分辨率是300像素/英寸时，A4纸的尺寸图像像素为：2479×3508
         */
        if (setw != null) {
            if (24 > setw) {
                i = (int) Math.floor(i * (setw * 1.0 / 24) / 2);
            }
        }

        List<Integer> list = new ArrayList<Integer>();
        if (w > i) {
            list.add(i);
            list.add(h * i / w);
        } else {
            list.add(w);
            list.add(h);
        }
        return list;
    }

    private List<SysFilesInfo> getListByIds(List<String> ids) {
        if (ids == null || ids.size() <= 0) {
            return null;
        }
        Where where = Where.create();
        where.in(SysFilesInfo::getId, ids.toArray());
//		where.and("ID in ?", ID_value);
        List<SysFilesInfo> list = entityService.queryList(SysFilesInfo.class, where);
        return list;
    }

    private SysFilesInfo getFileById(String id) {
        Where where = Where.create();
        where.in(SysFilesInfo::getId, id);
//		where.and("ID in ?", ID_value);
        List<SysFilesInfo> list = entityService.queryList(SysFilesInfo.class, where);
        if (list != null && list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }

    /**
     * @param file         原文件
     * @param picmap       图片对应变量及数量
     * @param tempWordList 生成的新文件列表，用于后期删除
     * @return
     * @category 多图片显示处理，模板更换变量为带序号的多个变量（并换行）并生成新模板进行处理
     */
    private File changeWordTpl(File file, Map<String, Integer> picmap, List<String> tempWordList) {
        String addr = file.getAbsolutePath();
        String newAddr = null;
        if (addr.lastIndexOf("/") == -1) {
            newAddr = addr.substring(0, addr.lastIndexOf("\\") + 1) + TMUID.getUID() + ".docx";
        } else {
            newAddr = addr.substring(0, addr.lastIndexOf("/") + 1) + TMUID.getUID() + ".docx";
        }
        FileInputStream in;
        try {
            in = new FileInputStream(file);
            XWPFDocument xwpf = new XWPFDocument(in);
            // 获取word中的所有段落与表格
            List<IBodyElement> elements = xwpf.getBodyElements();
            for (IBodyElement element : elements) {
                // 段落
                if (element instanceof XWPFParagraph) {
                    List<XWPFRun> runs = ((XWPFParagraph) element).getRuns();
                    if (runs.size() > 0) {
                        StringBuffer runText = new StringBuffer();
                        for (XWPFRun run : runs) {
                            List<XWPFPicture> pics = run.getEmbeddedPictures();
                            if (pics.isEmpty()) {
                                runText.append(run.text());
                            } else {
                                continue;
                            }
                        }
                        String rtxt = runText.toString();
                        Integer len = picmap.get(rtxt);
                        if (rtxt.length() > 0 && len != null) {
                            XWPFRun run = runs.get(0);
                            for (int i = 1, il = runs.size(); i < il; i++) {
                                runs.get(i).setText("", 0);
                            }
                            String val = rtxt.substring(2, rtxt.length() - 2);
                            run.setText("{{" + val + "____0}}", 0);
                            for (int i = 1, il = runs.size(); i < il; i++) {
                                run.addBreak();
                                run.setText("{{" + val + "____" + i + "}}");
                            }
                        }
                    }
                } else if (element instanceof XWPFTable) {
                    // 表格
                    List<XWPFTableRow> rows = ((XWPFTable) element).getRows();
                    for (XWPFTableRow row : rows) {
                        String key = null;
                        List<XWPFTableCell> cells = row.getTableCells();
                        for (XWPFTableCell cell : cells) {
                            // 一个单元格可以理解为一个word文档，单元格里也可以加段落与表格
                            List<XWPFParagraph> paragraphs = cell.getParagraphs();
                            for (XWPFParagraph paragraph : paragraphs) {
                                List<XWPFRun> runs = paragraph.getRuns();
                                if (runs.size() > 0) {
                                    StringBuffer runText = new StringBuffer();
                                    for (XWPFRun run : runs) {
                                        List<XWPFPicture> pics = run.getEmbeddedPictures();
                                        if (pics.isEmpty()) {
                                            runText.append(run.text());
                                        } else {
                                            continue;
                                        }
                                    }
                                    String rtxt = runText.toString();
                                    Integer len = picmap.get(rtxt);
                                    if (rtxt.length() > 0 && len != null) {
                                        XWPFRun run = runs.get(0);
                                        for (int i = 1, il = runs.size(); i < il; i++) {
                                            runs.get(i).setText("", 0);
                                        }
                                        String val = rtxt.substring(2, rtxt.length() - 2);
                                        run.setText("{{" + val + "____0}}", 0);
                                        for (int i = 1, il = len; i < il; i++) {
                                            run.addBreak();
                                            run.setText("{{" + val + "____" + i + "}}");
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
//	                System.out.println("其他内容");
                }
            }
            try {
                in.close();
            } catch (Exception e) {
            }
            FileOutputStream fo = new FileOutputStream(newAddr);
            try {
                xwpf.write(fo);
            } catch (IOException e) {
                log.error("write temp docx", e.getMessage());
            } finally {
                fo.close();
            }
            tempWordList.add(newAddr);
            return new File(newAddr);
        } catch (Exception e) {
            log.error("file error", e);
        }

        return file;
    }

    /**
     * @param file         原文件
     * @param picmap       文本对应变量及数量
     * @param tempWordList 生成的新文件列表，用于后期删除
     * @return
     * @category 文本换行显示处理，模板更换变量为带序号的多个变量（并换行）并生成新模板进行处理
     */
    private File changeWordTplTxt(File file, Map<String, Integer> textmap, List<String> tempWordList) {
        String addr = file.getAbsolutePath();
        String newAddr = null;
        if (addr.lastIndexOf("/") == -1) {
            newAddr = addr.substring(0, addr.lastIndexOf("\\") + 1) + TMUID.getUID() + ".docx";
        } else {
            newAddr = addr.substring(0, addr.lastIndexOf("/") + 1) + TMUID.getUID() + ".docx";
        }
        FileInputStream in;
        try {
            in = new FileInputStream(file);
            XWPFDocument xwpf = new XWPFDocument(in);
            // 获取word中的所有段落与表格
            List<IBodyElement> elements = xwpf.getBodyElements();
            for (IBodyElement element : elements) {
                // 段落
                if (element instanceof XWPFParagraph) {
                    List<XWPFRun> runs = ((XWPFParagraph) element).getRuns();
                    if (runs.size() > 0) {
                        StringBuffer runText = new StringBuffer();
                        for (XWPFRun run : runs) {
                            List<XWPFPicture> pics = run.getEmbeddedPictures();
                            if (pics.isEmpty()) {
                                runText.append(run.text());
                            } else {
                                continue;
                            }
                        }
                        String rtxt = runText.toString();
                        Integer len = textmap.get(rtxt);
                        if (rtxt.length() > 0 && len != null) {
                            XWPFRun run = runs.get(0);
                            for (int i = 1, il = runs.size(); i < il; i++) {
                                runs.get(i).setText("", 0);
                            }
                            String val = rtxt.substring(2, rtxt.length() - 2);// 去掉前后两个大括号，获取变量内容
                            run.setText("{{" + val + "____0}}", 0);
                            for (int i = 1, il = runs.size(); i < il; i++) {
                                run.addCarriageReturn();// 硬回车只在文本中可用，单元格中不生效
//								run.addBreak();
                                run.setText("{{" + val + "____" + i + "}}");
                            }
                        }
                    }
                } else if (element instanceof XWPFTable) {
                    // 表格
                    List<XWPFTableRow> rows = ((XWPFTable) element).getRows();
                    for (XWPFTableRow row : rows) {
                        String key = null;
                        List<XWPFTableCell> cells = row.getTableCells();
                        for (XWPFTableCell cell : cells) {
                            // 一个单元格可以理解为一个word文档，单元格里也可以加段落与表格
                            List<XWPFParagraph> paragraphs = cell.getParagraphs();
                            for (XWPFParagraph paragraph : paragraphs) {
                                List<XWPFRun> runs = paragraph.getRuns();
                                if (runs.size() > 0) {
                                    StringBuffer runText = new StringBuffer();
                                    for (XWPFRun run : runs) {
                                        List<XWPFPicture> pics = run.getEmbeddedPictures();
                                        if (pics.isEmpty()) {
                                            runText.append(run.text());
                                        } else {
                                            continue;
                                        }
                                    }
                                    String rtxt = runText.toString();
                                    Integer len = textmap.get(rtxt);
                                    if (rtxt.length() > 0 && len != null) {
                                        XWPFRun run = runs.get(0);
                                        for (int i = 1, il = runs.size(); i < il; i++) {
                                            runs.get(i).setText("", 0);
                                        }
                                        String val = rtxt.substring(2, rtxt.length() - 2);
                                        run.setText("{{" + val + "____0}}", 0);
                                        for (int i = 1, il = runs.size(); i < il; i++) {
                                            // run.addCarriageReturn();//硬回车只在文本中可用，单元格中不生效
                                            run.addBreak();
                                            run.setText("{{" + val + "____" + i + "}}");
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
//	                System.out.println("其他内容");
                }
            }
            try {
                in.close();
            } catch (Exception e) {
            }
            FileOutputStream fo = new FileOutputStream(newAddr);
            try {
                xwpf.write(fo);
            } catch (IOException e) {
                log.error("write temp docx", e.getMessage());
            } finally {
                fo.close();
            }
            tempWordList.add(newAddr);
            return new File(newAddr);
        } catch (Exception e) {
            log.error("file error", e);
        }

        return file;
    }

    private Map<String, Map<String, String>> getTplComMap(String tplId) {
        Map<String, Map<String, String>> map = new HashMap<String, Map<String, String>>();
        List<SysFormDto> list = formService.getTplFormContents(tplId);
        if (list != null && list.size() > 0) {
            for (SysFormDto dto : list) {
                String formJson = dto.getFormContent();
                JSONObject jsonObj = JSONObject.parseObject(formJson);
                JSONArray jsonArr = JSONArray.parseArray(jsonObj.getString("steps"));
                if (jsonArr.size() > 0) {
                    for (int i = 0, il = jsonArr.size(); i < il; i++) {
                        JSONObject bq = JSONObject.parseObject(jsonArr.getString(i));
                        JSONArray groups = JSONArray.parseArray(bq.getString("group"));
                        JSONArray singleColumns = JSONArray.parseArray(bq.getString("column"));
                        if (singleColumns != null && singleColumns.size() > 0) {
                            // 内部组件循环
                            for (int k = 0, kl = singleColumns.size(); k < kl; k++) {
                                JSONObject column = JSONObject.parseObject(singleColumns.getString(k));
                                // 组件类型
                                String type = column.getString("type");

                                if (column.containsKey("component") || "title".equals(type)) {
                                    continue;
                                }

                                if (column.getString("dicData") != null) {
                                    // 关联数据表和字段信息
                                    String table = column.getString("sjk");
                                    String tableCol = column.getString("sjkzd");
                                    String dateCol = "";
                                    if (table != null && !"".equals(table) && tableCol != null
                                            && !"".equals(tableCol)) {
                                        dateCol = table + "." + tableCol;
                                    }
                                    JSONArray kvarr = column.getJSONArray("dicData");
                                    Map<String, String> tmap = new HashMap<String, String>();
                                    for (int l = 0, ll = kvarr.size(); l < ll; l++) {
                                        JSONObject kv = JSONObject.parseObject(kvarr.getString(l));
                                        tmap.put(kv.getString("value"), kv.getString("label"));
                                    }
                                    map.put(dateCol.toUpperCase(), tmap);
                                }
                            }
                        }

                        // 表单内部分组循环
                        if (groups != null) {
                            for (int j = 0, jl = groups.size(); j < jl; j++) {
                                JSONObject group = JSONObject.parseObject(groups.getString(j));
                                JSONArray columns = JSONArray.parseArray(group.getString("column"));
                                // 分组内部组件循环
                                for (int k = 0, kl = columns.size(); k < kl; k++) {
                                    JSONObject column = JSONObject.parseObject(columns.getString(k));
                                    // 组件类型
                                    String type = column.getString("type");

                                    if (column.containsKey("component") || "title".equals(type)) {
                                        continue;
                                    }

                                    if (column.getString("dicData") != null) {
                                        // 关联数据表和字段信息
                                        String table = column.getString("sjk");
                                        String tableCol = column.getString("sjkzd");
                                        String dateCol = "";
                                        if (table != null && !"".equals(table) && tableCol != null
                                                && !"".equals(tableCol)) {
                                            dateCol = table + "." + tableCol;
                                        }
                                        JSONArray kvarr = column.getJSONArray("dicData");
                                        Map<String, String> tmap = new HashMap<String, String>();
                                        for (int l = 0, ll = kvarr.size(); l < ll; l++) {
                                            JSONObject kv = JSONObject.parseObject(kvarr.getString(l));
                                            tmap.put(kv.getString("value"), kv.getString("label"));
                                        }
                                        map.put(dateCol.toUpperCase(), tmap);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return map;
    }

    private String replaceKeyVal(String val, Map<String, String> map) {
        if (map != null && map.get(val) != null) {
            return map.get(val);
        } else {
            return val;
        }
    }

    private String replaceStrKeyVal(String val, Map<String, String> map) {
        StringBuffer sb = new StringBuffer();
        String[] ss = val.split(",");
        for (int i = 0, il = ss.length; i < il; i++) {
            String s = ss[i];
            if (map != null && map.get(val) != null) {
                s = map.get(val);
            }
            if (i > 0) {
                sb.append(",");
            }
            sb.append(s);
        }
        return sb.toString();
    }

    //    @Override
    public String getWordName(String tplId) {
        String name = "文档.docx";
        List<SysFilesInfo> flitList = tplService.getTplFileList(tplId);
        if (flitList != null && !flitList.isEmpty()) {
            name = flitList.get(0).getOldFileName();
        }
        return name;
    }

    /**
     * @param tdsAlias
     * @return
     * @category 根据数据源别名获取结果
     */
    private List<List<String>> getDsVal(String tdsAlias, String para, List<Double> wlist, List<Double> tlist,
                                        List<List<WordTilte>> titleObjList, List<Integer> combineColList) {
        List<List<String>> rlist = new ArrayList<List<String>>();
        TdsQueryDto param = new TdsQueryDto();
        param.setTdsAlias(tdsAlias);
//		param.setInParaAlias("FORM_DATA_INDEX_ID="+dataId);
        param.setInParaAlias(para);
//		param.setInParaRawValue("dataId="+dataId);
        JSONArray json = dsService.getTDSData(param);
        if (json.size() > 0) {

            Map<String, Map<String, String>> kvmap = new HashMap<String, Map<String, String>>();

            JSONObject jobj = (JSONObject) json.get(0);
            // 输出信息
            JSONObject props = jobj.getJSONObject("props");
            // 暂时根据输入参数判断是否横表，调整表格宽度 TODO
            JSONObject queryprop = props.getJSONObject("queryprop");
            if (queryprop.containsKey("bdsx")) {
                tlist.set(0, 25.9d);
//            	allWidth = 25.9d;
            }
            // 表头合并内容
            JSONArray colspan = props.getJSONArray("colspan");
            if (colspan != null) {// 多表头
                int rowNum = 0, colNum = 0, cpos = 0, rpos = 0;
                Map<String, WordTilte> posmap = new HashMap<String, WordTilte>();
                for (int i = 0, il = colspan.size(); i < il; i++) {
                    JSONObject colObj1 = colspan.getJSONObject(i);
                    if ("true".equals(colObj1.getString("hidden"))) {
                        continue;
                    }
                    if (colObj1.containsKey("children")) {
                        JSONArray children1 = colObj1.getJSONArray("children");
                        if (rowNum < 2) {
                            rowNum = 2;
                        }

                        int colSpan1 = 0;
                        int cpos1 = cpos + 0;

                        for (int j = 0, jl = children1.size(); j < jl; j++) {// 第二层
                            JSONObject colObj2 = children1.getJSONObject(j);

                            int colSpan2 = 0;
                            int cpos2 = cpos + 0;

                            if (colObj2.containsKey("children")) {
                                JSONArray children2 = colObj2.getJSONArray("children");
                                if (rowNum < 3) {
                                    rowNum = 3;
                                }
                                for (int k = 0, kl = children2.size(); k < kl; k++) {// 第三层
                                    JSONObject colObj3 = children2.getJSONObject(k);

                                    int colSpan3 = 0;
                                    int cpos3 = cpos + 0;

                                    if (colObj3.containsKey("children")) {
                                        JSONArray children3 = colObj3.getJSONArray("children");
                                        if (rowNum < 4) {
                                            rowNum = 4;
                                        }
                                        colNum += children3.size();
                                        colSpan1 += children3.size();
                                        colSpan2 += children3.size();
                                        colSpan3 += children3.size();

                                        for (int l = 0, ll = children3.size(); l < ll; l++) {// 第四层
                                            JSONObject colObj4 = children3.getJSONObject(k);
                                            WordTilte title = new WordTilte();
                                            title.setRowPos(3);
                                            title.setColPos(cpos3 + l);
                                            title.setContent(colObj4.getString("header"));
                                            title.setRowSpan(1);
                                            title.setColSpan(1);
                                            posmap.put("3_" + (cpos3 + l), title);
                                        }

                                    } else {
                                        colNum++;
                                        colSpan1++;
                                        colSpan2++;
                                        colSpan3++;
                                        cpos++;
                                    }

                                    WordTilte title = new WordTilte();
                                    title.setRowPos(2);
                                    title.setColPos(cpos3);
                                    title.setContent(colObj3.getString("header"));
                                    title.setRowSpan(1);
                                    title.setColSpan(colSpan3);
                                    posmap.put("2_" + cpos3, title);
                                }
                            } else {
                                colNum++;
                                colSpan1++;
                                cpos++;
                            }

                            WordTilte title = new WordTilte();
                            title.setRowPos(1);
                            title.setColPos(cpos2);
                            title.setContent(colObj2.getString("header"));
                            title.setRowSpan(1);
                            title.setColSpan(colSpan2);
                            posmap.put("1_" + cpos2, title);
                        }

                        WordTilte title = new WordTilte();
                        title.setRowPos(0);
                        title.setColPos(cpos1);
                        title.setContent(colObj1.getString("header"));
                        title.setRowSpan(1);
                        title.setColSpan(colSpan1);
                        posmap.put("0_" + cpos1, title);
                    } else {
                        if (rowNum < 1) {
                            rowNum = 1;
                        }
                        WordTilte title = new WordTilte();
                        title.setRowPos(0);
                        title.setColPos(cpos);
                        title.setContent(colObj1.getString("header"));
                        title.setRowSpan(null);
                        title.setColSpan(1);
                        posmap.put("0_" + cpos, title);

                        colNum++;
                        cpos++;
                    }
                }
                // 整理表头信息

                for (int i = 0; i < rowNum; i++) {
                    List<WordTilte> colList = new ArrayList<WordTilte>();
                    for (int j = 0; j < colNum; j++) {
                        String pos = i + "_" + j;
                        WordTilte title = posmap.get(pos);
                        if (title != null) {
                            if (title.getRowPos() == 0 && title.getRowSpan() == null) {
                                title.setRowSpan(rowNum);
                            }
                        } else {
                            title = new WordTilte();// 补充空单元格
                            title.setRowPos(i);
                            title.setColPos(j);
                            title.setContent("");
                            title.setRowSpan(1);
                            title.setColSpan(1);
                            posmap.put(pos, title);
                        }
                        colList.add(title);
                    }
                    titleObjList.add(colList);
                }
            }
            // 列id及顺序
            JSONObject colprop = props.getJSONObject("colprop");
            // 输出列配置信息
            JSONArray cols = props.getJSONArray("cols");
            // 数据
            JSONArray data = jobj.getJSONArray("data");

            List<String> colIdList = new ArrayList<String>();
            List<String> titleList = new ArrayList<String>();

            for (Object val : cols) {
                colIdList.add(String.valueOf(val));
            }

            List<Double> cws = new ArrayList<Double>();

            int colpos = 0;
            for (Iterator iterator = colIdList.iterator(); iterator.hasNext(); ) {
                String colId = (String) iterator.next();
                JSONObject colInfo = colprop.getJSONObject(colId);
                String comType = colInfo.getString("comType");
                boolean hidden = "true".equalsIgnoreCase(colInfo.getString("hidden"));
                if ("uploadfield".equals(comType)) {
                    hidden = true;
                }
                // 隐藏字段列删除
                if (hidden) {
                    iterator.remove();
                } else {
                    String header = colInfo.getString("header");
                    String widthStr = colInfo.getString("width");
                    String isSpanCol = colInfo.getString("isSpanCol");// true
                    String spanType = colInfo.getString("spanType");// 0
                    if (Coms.judgeLong(widthStr)) {
                        cws.add(Double.parseDouble(widthStr));
                    } else {
                        cws.add(null);
                    }
                    titleList.add(header);
                    if ("combo".equals(comType)) {
                        try {

                            Map<String, String> tmap = new HashMap<String, String>();
                            String keys = colInfo.getString("defaultKeyScript").replace("\\\"", "");
                            String vals = colInfo.getString("defaultValueScript").replace("\\\"", "");
                            keys = keys.substring(1, keys.length() - 1);
                            vals = vals.substring(1, keys.length() - 1);
                            String[] ks = keys.split(",");
                            String[] vs = vals.split(",");
                            for (int i = 0, il = ks.length; i < il; i++) {
                                if (vs.length > i) {
                                    tmap.put(ks[i], vs[i]);
                                }
                            }

                            kvmap.put(colId, tmap);
                        } catch (Exception e) {
                            // handle exception
                        }
                    } else if ("checkbox".equals(comType)) {
                        Map<String, String> tmap = new HashMap<String, String>();
                        tmap.put("1", "是");
                        tmap.put("0", "否");
                        tmap.put("", "否");
                        tmap.put(null, "否");
                        kvmap.put(colId, tmap);
                    }

                    if ("true".equals(isSpanCol) && "0".equals(spanType)) {
                        combineColList.add(colpos);
                    }

                    colpos++;
                }
            }

            if (StringUtils.isNotEmpty(titleObjList)) {
                for (List<WordTilte> _list : titleObjList) {// 多表头情况下，补正数据数量，用于后期算合并
                    rlist.add(titleList);
                }
            } else {
                rlist.add(titleList);
            }

            for (int i = 0, il = titleList.size(); i < il; i++) {
                double d = -1;
                if (cws.size() > i) {
                    Double cs = cws.get(i);
                    if (cs != null) {
                        d = cs;
                    }
                }
                wlist.add(d);
            }

            if (data.size() > 0) {
                for (int i = 0, il = data.size(); i < il; i++) {
                    JSONObject dataobj = (JSONObject) data.get(i);
                    List<String> dlist = new ArrayList<String>();
                    for (String colId : colIdList) {
                        String v = dataobj.getString(colId);
                        if (kvmap.containsKey(colId)) {
                            Map<String, String> tmap = kvmap.get(colId);
                            if (tmap != null && tmap.get(v) != null) {
                                v = tmap.get(v);
                            }
                        }
                        dlist.add(v);
                    }
                    rlist.add(dlist);
                }
            }

        }

        return rlist;
    }

    private List<Map<String, String>> getDsValCols(String tdsAlias, String para) {
        List<Map<String, String>> rlist = new ArrayList<Map<String, String>>();
        TdsQueryDto param = new TdsQueryDto();
        param.setTdsAlias(tdsAlias);
//		param.setInParaAlias("FORM_DATA_INDEX_ID="+dataId);
        param.setInParaAlias(para);
//		param.setInParaRawValue("dataId="+dataId);
        JSONArray json = dsService.getTDSData(param);
        if (json != null && json.size() > 0) {

            Map<String, Map<String, String>> kvmap = new HashMap<String, Map<String, String>>();

            JSONObject jobj = (JSONObject) json.get(0);
            // 输出信息
            JSONObject props = jobj.getJSONObject("props");
            // 暂时根据输入参数判断是否横表，调整表格宽度 TODO
//            JSONObject queryprop = props.getJSONObject("queryprop");
            // 列id及顺序
            JSONObject colprop = props.getJSONObject("colprop");
            // 输出列配置信息
            JSONArray cols = props.getJSONArray("cols");
            // 数据
            JSONArray data = jobj.getJSONArray("data");

            List<String> colIdList = new ArrayList<String>();

            for (Object val : cols) {
                colIdList.add(String.valueOf(val));
            }

            Map<String, String> headerMap = new HashMap<String, String>();

            for (Iterator iterator = colIdList.iterator(); iterator.hasNext(); ) {
                String colId = (String) iterator.next();
                JSONObject colInfo = colprop.getJSONObject(colId);
                String comType = colInfo.getString("comType");
                boolean hidden = "true".equalsIgnoreCase(colInfo.getString("hidden"));
                if ("uploadfield".equals(comType)) {
                    hidden = true;
                }
                // 记录列显示内容与列名不一致（用于另外存值，实现设置为显示内容时获取对应值）
//                if(!colId.equals(colInfo.getString("header"))) {
//                	headerMap.put(colId, colInfo.getString("header"));
//                }
                // 隐藏字段列删除
                if (hidden) {
                    iterator.remove();
                } else {
                    if ("combo".equals(comType)) {
                        try {

                            Map<String, String> tmap = new HashMap<String, String>();
                            String keys = colInfo.getString("defaultKeyScript").replace("\\\"", "");
                            String vals = colInfo.getString("defaultValueScript").replace("\\\"", "");
                            keys = keys.substring(1, keys.length() - 1);
                            vals = vals.substring(1, keys.length() - 1);
                            String[] ks = keys.split(",");
                            String[] vs = vals.split(",");
                            for (int i = 0, il = ks.length; i < il; i++) {
                                if (vs.length > i) {
                                    tmap.put(ks[i], vs[i]);
                                }
                            }

                            kvmap.put(colId, tmap);
                        } catch (Exception e) {
                            // handle exception
                        }
                    } else if ("checkbox".equals(comType)) {
                        Map<String, String> tmap = new HashMap<String, String>();
                        tmap.put("1", "是");
                        tmap.put("0", "否");
                        tmap.put("", "否");
                        tmap.put(null, "否");
                        kvmap.put(colId, tmap);
                    }
                }
            }

            if (data.size() > 0) {
                for (int i = 0, il = data.size(); i < il; i++) {
                    JSONObject dataobj = (JSONObject) data.get(i);
                    Map<String, String> datamap = new HashMap<String, String>();
                    for (String colId : colIdList) {
                        String v = dataobj.getString(colId);
                        if (kvmap.containsKey(colId)) {
                            Map<String, String> tmap = kvmap.get(colId);
                            if (tmap != null && tmap.get(v) != null) {
                                v = tmap.get(v);
                            }
                        }
                        datamap.put(colId, v);
//                        if(headerMap.containsKey(colId)) {
//                        	datamap.put(headerMap.get(colId), v);
//                        }
                    }
                    rlist.add(datamap);
                }
            }

        }

        return rlist;
    }

    private TableRenderData getDsObject(String tdsAlias, String para) {
//        List<List<String>> rlist = new ArrayList<List<String>>();
        TableRenderData renderData = null;
        TdsQueryDto param = new TdsQueryDto();
        param.setTdsAlias(tdsAlias);
        param.setInParaAlias(para);
        JSONArray json = dsService.getTDSData(param);
        if (json.size() > 0) {

            Map<String, Map<String, String>> kvmap = new HashMap<String, Map<String, String>>();

            JSONObject jobj = (JSONObject) json.get(0);
            // 输出信息
            JSONObject props = jobj.getJSONObject("props");
            // 列id及顺序
            JSONObject colprop = props.getJSONObject("colprop");
            // 输出列配置信息
            JSONArray cols = props.getJSONArray("cols");
            // 数据
            JSONArray data = jobj.getJSONArray("data");

            List<String> colIdList = new ArrayList<String>();
            List<String> titleList = new ArrayList<String>();

            for (Object val : cols) {
                colIdList.add(String.valueOf(val));
            }

            for (Iterator iterator = colIdList.iterator(); iterator.hasNext(); ) {
                String colId = (String) iterator.next();
                JSONObject colInfo = colprop.getJSONObject(colId);
                String comType = colInfo.getString("comType");
                boolean hidden = "true".equalsIgnoreCase(colInfo.getString("hidden"));
                if ("uploadfield".equals(comType)) {
                    hidden = true;
                }
                // 隐藏字段列删除
                if (hidden) {
                    iterator.remove();
                } else {
                    String header = colInfo.getString("header");
                    titleList.add(header);
                    if ("combo".equals(comType)) {
                        try {

                            Map<String, String> tmap = new HashMap<String, String>();
                            String keys = colInfo.getString("defaultKeyScript").replace("\\\"", "");
                            String vals = colInfo.getString("defaultValueScript").replace("\\\"", "");
                            keys = keys.substring(1, keys.length() - 1);
                            vals = vals.substring(1, keys.length() - 1);
                            String[] ks = keys.split(",");
                            String[] vs = vals.split(",");
                            for (int i = 0, il = ks.length; i < il; i++) {
                                if (vs.length > i) {
                                    tmap.put(ks[i], vs[i]);
                                }
                            }

                            kvmap.put(colId, tmap);
                        } catch (Exception e) {
                            // handle exception
                        }
                    } else if ("checkbox".equals(comType)) {
                        Map<String, String> tmap = new HashMap<String, String>();
                        tmap.put("1", "是");
                        tmap.put("0", "否");
                        tmap.put("", "否");
                        tmap.put(null, "否");
                        kvmap.put(colId, tmap);
                    }
                }
            }

            double[] cds = new double[titleList.size()];
            for (int i = 0, il = titleList.size(); i < il; i++) {
                cds[0] = 29.7d / il;
            }

            Tables.TableBuilder tableBuilder = Tables.of();
            tableBuilder.width(29.7d, cds);
            renderData = tableBuilder.create();

            RowRenderData titleData = Rows.of((String[]) titleList.toArray()).center().create();
            renderData.addRow(titleData);

            if (data.size() > 0) {
                for (int i = 0, il = data.size(); i < il; i++) {
                    JSONObject dataobj = (JSONObject) data.get(i);
                    List<String> dlist = new ArrayList<String>();
                    for (String colId : colIdList) {
                        String v = dataobj.getString(colId);
                        if (kvmap.containsKey(colId)) {
                            Map<String, String> tmap = kvmap.get(colId);
                            if (tmap != null && tmap.get(v) != null) {
                                v = tmap.get(v);
                            }
                        }
                        dlist.add(v);
                    }
                    RowRenderData datas = Rows.of((String[]) dlist.toArray()).center().create();
                    renderData.addRow(datas);
                }
            }

        }

        return renderData;
    }

    /**
     * 保存替换数据
     */
    @Override
    public boolean saveReplaceCharData(WordSaveDto param) {
        boolean bln = false;
        if (param != null && StringUtils.isNotEmpty(param.getData())) {
            Integer tempsort = getMaxSort();

            List<WordExpDs> insertList = new ArrayList<WordExpDs>();
            List<WordExpDs> upateList = new ArrayList<WordExpDs>();
            List<WordExpDs> deleteList = new ArrayList<WordExpDs>();
            JSONArray datas = JSONArray.parseArray(param.getData());
            if (datas != null) {
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject row = datas.getJSONObject(i);
                    Integer rowFlag = row.getInteger("TDSROW_rowFlag"); // 标识字段
                    WordExpDs bean = new WordExpDs();
                    String id = row.getString("id");// 主键
                    bean.setId(id);
                    bean.setParaAlias(row.getString("paraAlias"));//
                    bean.setParaName(row.getString("paraName"));//
                    bean.setComponentType(row.getString("componentType"));
                    bean.setDefaultKeyScript(row.getString("defaultKeyScript"));
                    bean.setDefaultValueScript(row.getString("defaultValueScript"));
                    bean.setInitvaluescript(row.getString("initvaluescript"));
                    bean.setSortNo(row.getInteger("sortNo"));
                    // 本例使用editortable，如果改成调用数据源输入参数组件，请修改判断条件（注）
                    if (rowFlag == null || rowFlag == 0) {// 添加
                        bean.setId(TMUID.getUID());
                        bean.setTplId(row.getString("tplId"));
                        bean.setUsed(1);
                        bean.setSortNo(tempsort++);
                        insertList.add(bean);
                    } else if (rowFlag == 1) {// 修改
                        upateList.add(bean);
                    } else {// 删除
                        if (StringUtils.isNotEmpty(id)) {
                            bean.setUsed(0);// 假删
                            upateList.add(bean);
                        }
                    }
                }
            }
            if (StringUtils.isNotEmpty(insertList)) {
                bln = this.insertReplaceCharData(insertList);// 添加
            }
            if (StringUtils.isNotEmpty(upateList)) {
                bln = this.updateReplaceCharData(upateList);// 更新
            }
            if (StringUtils.isNotEmpty(deleteList)) {
                bln = this.deleteReplaceCharData(deleteList);// 删除
            }
        }
        return bln;

    }

    private Integer getMaxSort() {
        Integer i = 0;
        List<Map<String, Object>> list = entityService.queryListMap("select max(sort_no) tmsort from WORD_EXP_DS",
                null);
        Object sort = list.get(0).get("tmsort");
        if (sort instanceof Integer) {
            i = Integer.parseInt(sort.toString());
        }
        i = i + 1;
        return i;
    }

    /**
     * 批量添加数据
     *
     * @param list
     * @return
     */
    private boolean insertReplaceCharData(List<WordExpDs> list) {
        int row = entityService.insertBatch(list);
        return row > 0 ? true : false;
    }

    /**
     * 批量更新数据
     *
     * @param list
     * @return
     */
    private boolean updateReplaceCharData(List<WordExpDs> list) {
        int row = entityService.updateByIdBatch(list);
        return row > 0 ? true : false;
    }

    /**
     * 批量删除数据
     *
     * @param list
     * @return
     */
    private boolean deleteReplaceCharData(List<WordExpDs> list) {
        int row = entityService.deleteByIdBatch(list);
        return row > 0 ? true : false;
    }

    /**
     * 列表查询
     *
     * @param queryParam 检索条件
     * @param page       分页信息
     * @return
     */
    @Override
    public List<WordExpDs> queryWordList(WordQueryDto queryParam, Pagination<?> page) {
        // where 条件
        Where where = Where.create();
        where.eq(WordExpDs::getUsed, 1);
        where.eq(WordExpDs::getTplId, queryParam.getTplId());
        if (queryParam != null) {

            if (StringUtils.isNotEmpty(queryParam.getAliasName())) {
                where.like(WordQueryDto::getAliasName, queryParam.getAliasName());
            }

            if (StringUtils.isNotEmpty(queryParam.getName())) {
                where.like(WordQueryDto::getName, queryParam.getName());
            }
        }
        // 排序
        Order order = Order.create();
        order.orderByAsc(WordExpDs::getSortNo);
        return entityService.queryData(WordExpDs.class, where, order, page);
    }

    /**
     * 导出word输入参数查询
     *
     * @param queryParam 检索条件
     * @return
     */
    @Override
    public List<WordExpDs> queryDownWordPara(WordQueryDto queryParam) {
        // where 条件
        Where where = Where.create();
        where.eq(WordExpDs::getUsed, 1);
        where.eq(WordExpDs::getTplId, queryParam.getTplId());
        Order order = Order.create();
        order.orderByAsc(WordExpDs::getSortNo);
        List<WordExpDs> rlist = entityService.queryData(WordExpDs.class, where, order, null);
        // 根据模板ID，查询并获取所有数据源，并获取数据源相关输入参数，同名做合并处理，去除已设置的参数，剩余进行合并
        if (rlist == null)
            rlist = new ArrayList<WordExpDs>();
        if (!rlist.isEmpty()) {// 设置内容去重
            List<WordExpDs> nlist = new ArrayList<WordExpDs>();
            List<String> plist = new ArrayList<String>();
            for (WordExpDs para : rlist) {
                if (!plist.contains(para.getParaAlias())) {
                    plist.add(para.getParaAlias());
                    nlist.add(para);
                }
            }
            rlist = nlist;

        }
        rlist = getTplDsInpara(queryParam.getTplId(), rlist);

        return rlist;
    }

    private List<WordExpDs> getTplDsInpara(String tplId, List<WordExpDs> rlist) {
        // 根据模板ID，查询并获取所有数据源，并获取数据源相关输入参数，同名做合并处理，去除已设置的参数 TODO
        List<String> dsList = new ArrayList<String>();
        List<SysFormDto> list = formService.getTplFormContents(tplId);
        if (list != null && list.size() > 0) {
            // 循环表单
            for (SysFormDto dto : list) {
                String formJson = dto.getFormContent();
                if (formJson == null)
                    continue;
                JSONObject jsonObj = JSONObject.parseObject(formJson);
                JSONArray jsonArr = JSONArray.parseArray(jsonObj.getString("steps"));
                if (jsonArr.size() > 0) {
                    for (int i = 0, il = jsonArr.size(); i < il; i++) {

                        JSONObject bq = JSONObject.parseObject(jsonArr.getString(i));
                        String title = bq.getString("titleName");
                        title = (title == null || "".equals(title)) ? dto.getFormName() : title;
                        JSONArray groups = JSONArray.parseArray(bq.getString("group"));
                        JSONArray singleColumns = JSONArray.parseArray(bq.getString("column"));

                        // 表单绑定数据源
//						String tdsinpara = bq.getString("inParaAlias");
                        JSONArray tdsDataArr = JSONArray.parseArray(bq.getString("tdsData"));
                        List<String> tdsList = new ArrayList<String>();
                        for (int j = 0, jl = tdsDataArr.size(); j < jl; j++) {
                            String tdsData = tdsDataArr.getString(j);
                            if (tdsData != null && tdsData.length() > 0) {
                                tdsList.add(tdsData);
                            }
                        }

                        // 有独立的无分组组件设置
                        if (singleColumns != null && singleColumns.size() > 0) {
                            // 分组内部组件循环
                            for (int k = 0, kl = singleColumns.size(); k < kl; k++) {
                                JSONObject column = JSONObject.parseObject(singleColumns.getString(k));
                                // 关联数据表和字段信息
                                String table = column.getString("sjk");
                                String tableCol = column.getString("sjkzd");
                                if (table != null && !"".equals(table) && tableCol != null && !"".equals(tableCol)) {
                                } else {
                                    // 数据源配置
                                    String cshSjk = column.getString("cshSjk");// 初始化数据源/表
                                    String cshSjkzd = column.getString("cshSjkzd");// 初始化数据源/表字段
                                    if (tdsList != null && cshSjk != null && cshSjkzd != null && cshSjk.length() > 0
                                            && cshSjkzd.length() > 0 && tdsList.contains(cshSjk)) {// 配置的数据源
                                        if (!dsList.contains(cshSjk)) {
                                            dsList.add(cshSjk);
                                        }
                                    }
                                }
                                if (column.containsKey("component")) {
                                    String component = column.getString("component");
                                    if ("tdsedit-table".equals(component)) {
                                        JSONObject params = JSONObject.parseObject(column.getString("params"));
                                        String tdsAlias = params.getString("tdsAlias");
//										String tdsInParaAlias = params.getString("tdsInParaAlias");
//										if(tdsInParaAlias == null) {
//											tdsInParaAlias = "";
//										}
                                        if (!dsList.contains(tdsAlias)) {
                                            dsList.add(tdsAlias);
                                        }
                                    }
                                }
                            }
                        }
                        // 有独立的无分组组件设置 结束 》》

                        // 表单内部分组循环
                        if (groups != null) {
                            for (int j = 0, jl = groups.size(); j < jl; j++) {
                                JSONObject group = JSONObject.parseObject(groups.getString(j));
                                JSONArray columns = JSONArray.parseArray(group.getString("column"));

                                // 分组内部组件循环
                                for (int k = 0, kl = columns.size(); k < kl; k++) {
                                    JSONObject column = JSONObject.parseObject(columns.getString(k));
                                    // 关联数据表和字段信息
                                    String table = column.getString("sjk");
                                    String tableCol = column.getString("sjkzd");
                                    if (table != null && !"".equals(table) && tableCol != null
                                            && !"".equals(tableCol)) {
                                    } else {
                                        // 数据源配置
                                        String cshSjk = column.getString("cshSjk");// 初始化数据源/表
                                        String cshSjkzd = column.getString("cshSjkzd");// 初始化数据源/表字段
                                        if (tdsList != null && cshSjk != null && cshSjkzd != null && cshSjk.length() > 0
                                                && cshSjkzd.length() > 0 && tdsList.contains(cshSjk)) {// 配置的数据源
                                            if (!dsList.contains(cshSjk)) {
                                                dsList.add(cshSjk);
                                            }
                                        }
                                    }
                                    if (column.containsKey("component")) {
                                        String component = column.getString("component");
                                        if ("tdsedit-table".equals(component)) {// 数据源
                                            JSONObject params = JSONObject.parseObject(column.getString("params"));
                                            String tdsAlias = params.getString("tdsAlias");
//											String tdsInParaAlias = params.getString("tdsInParaAlias");
//											if(tdsInParaAlias == null) {
//												tdsInParaAlias = "";
//											}
                                            if (!dsList.contains(tdsAlias)) {
                                                dsList.add(tdsAlias);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Map<String, WordExpDs> aliasMap = new HashMap<String, WordExpDs>();
        List<String> alist = new ArrayList<String>();
        if (dsList.size() > 0) {
            // 自定义的参数
            for (WordExpDs obj : rlist) {
                aliasMap.put(obj.getParaAlias(), obj);
                alist.add(obj.getParaAlias());
                String initVal = "";
                if (obj.getDefaultKeyScript() != null && !"".equals(obj.getDefaultKeyScript())) {
                    Map<String, IDataSource> ipsMap = parseInParaTds(obj.getDefaultKeyScript(), null);
                    try {
                        Object ov = eval(obj.getDefaultKeyScript(), ipsMap);
                        if (ov != null) {
                            if (ov instanceof String) {
                                initVal = String.valueOf(ov);
                            } else if (ov instanceof List) {
                                List olist = (List) ov;
                                if (olist.size() > 0) {
                                    initVal = String.valueOf(olist.get(0));
                                }
                            }
                        }
                    } catch (ScriptException e) {
                    }
                }
                obj.setValue(initVal);
            }
            // 数据源自带的输入参数
            for (String ds : dsList) {
                // 获取数据源相关参数列表，无已配置，进行添加 TODO
                // for WordExpDs rlist.add();
                List<TdsinPara> ilist = tdsServ.getTDSInPara(ds);
                for (TdsinPara inpara : ilist) {
                    String alias = inpara.getParaAlias();
                    if (!aliasMap.containsKey(alias)) {
                        WordExpDs obj = new WordExpDs();
                        obj.setComponentType(StringUtils.isEmpty(inpara.getComponentType()) ? "textfield"
                                : inpara.getComponentType());
                        obj.setParaAlias(alias);
                        obj.setParaName(inpara.getParaName());
                        obj.setDefaultKeyScript(inpara.getDefaultKeyScript());
                        obj.setDefaultValueScript(inpara.getDefaultValueScript());

                        String initVal = "";
                        if (inpara.getDefaultKeyScript() != null && !"".equals(inpara.getDefaultKeyScript())) {
                            Map<String, IDataSource> ipsMap = parseInParaTds(obj.getInitvaluescript(), null);
                            try {
                                Object ov = eval(obj.getInitvaluescript(), ipsMap);
                                if (ov != null) {
                                    if (ov instanceof String) {
                                        initVal = String.valueOf(ov);
                                    } else if (ov instanceof List) {
                                        List olist = (List) ov;
                                        if (olist.size() > 0) {
                                            initVal = String.valueOf(olist.get(0));
                                        }
                                    }
                                }
                            } catch (ScriptException e) {
                            }
                        }
                        obj.setValue(initVal);
                        aliasMap.put(alias, obj);
                    }
                }
            }
        }

        for (String alias : aliasMap.keySet()) {
            if (!alist.contains(alias)) {
                rlist.add(aliasMap.get(alias));
            }
        }

        return rlist;
    }

    /**
     * 脚本计算
     *
     * @param s   计算公式
     * @param map 参数
     * @return
     * @throws ScriptException
     */
    private Object eval(String s, Map<String, IDataSource> map) throws ScriptException {
        Object obj = null;
        if (s != null && s.trim().length() > 0) {
            s = s.trim();
            obj = ScriptEngineUtils.evalConst(s);// 首先进行常量的计算
            if (obj != null) {
                return obj;
            }
            // 判断是否为数据源解析
            boolean isTds = false;
            if (s.indexOf("$") >= 0) {// 数据源解析
                isTds = true;
            }
            String evalKey = ScriptEngineUtils.getMd5(s);
            CompiledScriptEngine cse = evalMap.get(evalKey);
            if (cse == null) {
                String script = "";
                if (isTds) {// 数据源解析
                    script = ScriptEngineUtils.clearScript(s);// 去掉 $ 符号
                } else {// 函数解析
                    script = CustomFun.getCuntomUsedFunScript(s) + " " + s;
                }
                cse = new CompiledScriptEngine(script);
                if (StringUtils.isEmpty(cse.getError())) {
                    evalMap.put(evalKey, cse);
                } else {
                    throw (cse.getScriptException());
                }
            }
            try {
                if (isTds) {// 数据源解析
                    List<String> paramList = parseDataSourceAliasList(s);
                    if (paramList != null && paramList.size() > 0 && map != null && map.size() > 0) {
                        Map<String, Object> paramMap = new HashMap<String, Object>();
                        for (String tdsAlias : paramList) {
                            IDataSource ids = map.get(tdsAlias);
                            if (ids != null) {
                                paramMap.put(tdsAlias, ids);
                            }
                        }
                        if (paramMap.size() > 0) {
                            obj = cse.eval(paramMap);
                        }
                    }
                } else {// 函数解析
                    obj = cse.eval();
                }
            } catch (ScriptException e) {
//	            log.error("数据源默认参数脚本解析错误：[" + this.getDSAlias() + "]" + s, e);
                throw (e);
            }
        }
        return obj;
    }

    private List<String> parseDataSourceAliasList(String script) {
        if (script == null) {
            return null;
        }
        if (this.dsm == null) {
            this.dsm = new TDataSourceManager();
        }
        return this.dsm.parseDataSourceAliasList(script);
    }

    private Map<String, IDataSource> parseInParaTds(String script, Map<String, Object> valueMap) {
        Map<String, IDataSource> map = new LinkedHashMap<String, IDataSource>();
        if (script != null && script.trim().length() > 0) {
            List<String> list = this.parseDataSourceAliasList(script);
            if (list != null && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    String s = list.get(i);
                    if (map.get(s) == null) {
                        map.put(s, getDs(s, valueMap));// 此处需考虑下拉框支持带参数的数据源加载
                    }
                }
            }
        }
        return map;
    }

    /**
     * 获得数据源
     *
     * @param alias
     * @param valueMap
     * @return
     */
    private IDataSource getDs(String alias, Map<String, Object> valueMap) {
        if (this.dsm == null) {
            this.dsm = new TDataSourceManager();
        }
        IDataSource ds = this.dsm.getDataSource(alias);
        try {
            ds = this.dsm.getDataSource(alias);
            if (ds != null) {
                if (StringUtils.isNotEmpty(valueMap)) {
                    for (String key : valueMap.keySet()) {
                        ds.setInParaByAlias(key, valueMap.get(key));
                    }
                }
                if (!ds.getAutoLoad()) {
                    ds.load();
                }
            }
        } catch (Exception e) {
            log.error("输入参数数据源加载错误[" + alias + "]", e);
        }
        return ds;
    }

    private static void addField(XWPFParagraph paragraph, String fieldName) {
        CTSimpleField ctSimpleField = paragraph.getCTP().addNewFldSimple();
        ctSimpleField.setInstr(fieldName);
        ctSimpleField.setDirty(STOnOff.TRUE);
        ctSimpleField.addNewR().addNewT().setStringValue("<<请更新目录内容>>");
    }

    /**
     * 返回非数据源类型的字段对应的值
     *
     * @param dataid
     * @param formid
     * @return
     */
    public Map<String, String> getMapTable(String dataid, String formid) {
        // 1.找到表单中所有的非数据源的，存储在表中的值
        // 封装表名字段名，就是模板中非数据源的模式，列如： {{ACT_BUSS_USER_JBSJ.JH}}
        // 封装结果{{ACT_BUSS_USER_JBSJ.JH}}="双北33-47" 便于下面循环Excel表时替换用
        Map<String, String> map_table = new HashMap<String, String>();// ====================
        // {{ACT_BUSS_USER_JBSJ.JH}}="双北33-47"

        SysFormDto dto_0 = new SysFormDto();
        dto_0.setDataId(dataid);
        dto_0.setFormId(formid);// ==============这里目前就用了一个表单模板id，如果有多个，下面考虑重新查询方法
        // 获取表单中的所有数据(不包括数据源)
        SysFormDto bean = dataService.getSysFormData(dto_0);// 调用刘涛方法获取
        List<SysFormDataDto> formdata = bean.getFormData();
        // SysFormDataDto(tableName=ACT_BUSS_USER_JBSJ, columnName=JH, value=双北33-47),
        if (formdata != null && formdata.size() > 0) {
            int m = formdata.size();
            for (int i = 0; i < m; i++) {
                SysFormDataDto e = formdata.get(i);
                String tableName = e.getTableName();// 表名
                String columnName = e.getColumnName();// 字段名
                String value = e.getValue() == null ? "" : String.valueOf(e.getValue());// 字段值
                String key = "{{" + tableName + "." + columnName + "}}";// 修改为和模板要替换的一样
                map_table.put(key, value);// {{ACT_BUSS_USER_JBSJ.JH}}="双北33-47"
            }
        }
        return map_table;
    }

    /**
     * 替换数据源输入参数中变量的值
     *
     * @param para
     * @param dataid
     * @param dataindexid
     * @param businessKeys
     * @return
     */
    public String getbusinessKeysReplace(String para, String dataid, String dataindexid, String[] businessKeys) {
        if (para != null && !para.equals("")) {
            para = para.replace("@dataid", dataid);
            para = para.replace("@dataindexid", dataindexid);

            if (para.indexOf("@businessKeys#") > -1) {
                // dataid=@businessKeys#arr=0|name=张三|age=45
                String bk[] = para.split("\\|");
                if (bk.length > 0) {
                    for (int i = 0; i < bk.length; i++) {
                        if (bk[i] != null && bk[i].indexOf("@businessKeys#") > -1) {// dataid=@businessKeys#arr=0
                            String str = bk[i];
//							String th = str.substring(str.indexOf("@")) + "|";// @businessKeys#arr=0   有问题
                            String th = str.substring(str.indexOf("@"));// @businessKeys#arr=0
                            str = str.substring(str.indexOf("#") + 1);// #arr=0
                            str = str.replace("arr=", "");// 0
                            Integer m = Integer.parseInt(str);// 0
                            if (businessKeys != null) {
                                try {
                                    para = para.replace(th, businessKeys[m]);// 把这个@businessKeys#arr=0 替换成实际值
                                } catch (Exception ex) {
                                }
                            }
                        }
                    }
                }
            }
        }
        return para;
    }

    /**
     * 解析数据源
     *
     * @return
     */
    public List<List<String>> getJsonDatas(JSONArray json) {
        List<List<String>> list = new ArrayList<List<String>>();
        JSONObject object = JSONObject.parseObject(json.getString(0));

        Map<String, String> mapCols = new LinkedHashMap<String, String>();// 列数组==用于判断那些列启用，不在map中的是无效列
        JSONObject props = JSONObject.parseObject(object.getString("props"));// 列信息（带顺序的 ）
        JSONObject colprop = props.getJSONObject("colprop");// 列属性设置
        JSONArray cols = JSONArray.parseArray(props.getString("cols"));// 列别名（带顺序的 ） "cols": ["FJMC", "FJSM", "WZMC",
        // "FJDATE", "WJMC", "XZDZ", "ID", "PID",
        // "FORM_DATA_INDEX_ID", "FJLX"]

        boolean hhSignSubbool = false;// 是否有印章组件
        Map<String, String> mapimg = new HashMap<String, String>();// 遍历数据源的时候，要知道哪些列是图片
        Map<String, String> mapdefaultKeyScript = new HashMap<String, String>();// 下拉列表
        // 按excel输出，先输出表的表头
        List<String> listtable = new ArrayList<String>();// excel==============输出表头
        if (cols != null && cols.size() > 0) {
            for (int i = 0; i < cols.size(); i++) {
//				"cols": ["FJMC", "FJSM", "WZMC", "FJDATE", "WJMC", "XZDZ", "ID", "PID", "FORM_DATA_INDEX_ID", "FJLX"]
                String tableCol = cols.getString(i);// FJMC
                JSONObject colpropcl = colprop.getJSONObject(tableCol);
                boolean hidden = colpropcl.getBoolean("hidden"); // "hidden": true,
                String header = colpropcl.getString("header");// 列中文名
                if (!hidden) {// 是否隐藏，hidden=false不隐藏
                    listtable.add(header);
                    mapCols.put(tableCol, "");// 用于判断那些列启用，不在map中的是无效列
                }
                String comType = colpropcl.getString("comType");// 列类型
                // 下拉：combo， 图片：uploadImg 印章签名 ：hhSignSub
                if ("combo".equals(comType)) {
                    // [\"889002\",\"889063\",\"889064\",\"889020\",\"889021\",\"889022\",\"889023\",\"889024\",\"889025\",\"889026\",\"889027\",\"889028\",\"889029\",\"889030\",\"889031\",\"889032\",\"889033\",\"889034\",\"889035\",\"889036\",\"889037\",\"889038\",\"889039\",\"889040\",\"889041\",\"889042\",\"889043\",\"889044\",\"889045\",\"889046\",\"889047\",\"889048\",\"889049\",\"889050\",\"889051\",\"889052\",\"889053\",\"889054\",\"889055\",\"889056\",\"889057\",\"889058\",\"889059\",\"889060\",\"889061\",\"889062\",\"889065\",\"889066\",\"889067\",\"889068\",\"889069\",\"889070\",\"889071\",\"889072\",\"889073\",\"889074\",\"889075\",\"889076\",\"889077\",\"889078\",\"889079\",\"889080\",\"889081\",\"889082\",\"889083\",\"889084\",\"889085\",\"889086\",\"889087\",\"889088\",\"889089\",\"889090\",\"889091\",\"889092\",\"889093\",\"889094\",\"889095\",\"889096\",\"889097\",\"889098\",\"889099\",\"889100\",\"889101\",\"889102\",\"889103\",\"889104\",\"889105\",\"889106\",\"889107\",\"889108\",\"889109\",\"889110\",\"889111\",\"889112\",\"889113\",\"889114\",\"889115\",\"889116\",\"889117\"]
                    String defaultKeyScript = colpropcl.getString("defaultKeyScript");// 实际值
                    // [\"辽阳石化30万吨/年高性能聚丙烯项目\",\"“五号工程”\",\"聚酯厂PETG质量提升改造\",\"炼油厂地下管网综合治理\",\"成品油管道输送汽柴油隐患治理\",\"抚锦线辽阳支线新增计量设施\",\"炼油厂汽油、航煤栈台密闭装车系统隐患治理\",\"炼油厂脱硫车间HAZOP分析必改项整改\",\"炼油厂第一第二循环水场隐患治理\",\"炼油厂船用燃料油调和设施完善\",\"炼油厂消防水系统地下管网隐患治理\",\"芳烃厂芳烃联合装置高含硫气隐患治理\",\"芳烃厂苯密闭装车尾气回收系统升级改造\",\"环氧乙烷/乙二醇装置环氧乙烷外输管线隐患治理\",\"储运厂输油管道隐患治理项目增补隐患治理\",\"热电厂低压配电柜隐患治理\",\"热电厂灰浆泵节能改造\",\"热电厂#5-#8发电机励磁系统升级改造\",\"热电厂大机组隐患治理\",\"热电厂东区电气系统隐患治理\",\"热电厂#1-7锅炉制粉系统安全隐患治理\",\"热电厂锅炉及附属设施完善\",\"动力厂440#装置增设隔油池\",\"动力厂污水处理装置设施完善\",\"动力厂液氮及液氧系统设施完善\",\"生产监测部计量检验室计量检定标准设备购置\",\"生产监测部安全隐患综合治理\",\"继电保护及控制柜电源柜隐患治理\",\"接地选线装置隐患治理\",\"局域网线路及设备隐患治理\",\"信息技术部机房隐患治理\",\"视频监控系统完善三期\",\"完善火灾报警系统\",\"制约生产装置长周期运行设备改造\",\"机泵安装无线监测系统\",\"储罐设备设施安全隐患治理\",\"现场安全设施完善\",\"炼油厂东、西油品车间储罐腐蚀隐患治理\",\"常压储罐安全隐患治理\",\"炼油厂外浮顶储罐增设主动安全防护系统\",\"部分公用管廊蒸汽凝结水回收利用节能改造\",\"HSE体系审核问题隐患治理\",\"换热设施完善\",\"部分循环水场节能技术改造\",\"分厂办公楼生活污水集中整治\",\"动力厂污泥干化\",\"辽阳石化分公司40万吨/年聚丙烯\",\"辽阳石化30万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化分公司31万吨/年聚丙烯\",\"辽阳石化分公司32万吨/年聚丙烯\",\"辽阳石化分公司41万吨/年聚丙烯\",\"辽阳石化35万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化分公司30万吨/年聚丙烯\",\"辽阳石化分公司33万吨/年聚丙烯\",\"辽阳石化分公司34万吨/年聚丙烯\",\"辽阳石化公司五号工程\",\"辽阳石化38万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化分公司43万吨/年聚丙烯\",\"辽阳石化分公司30万吨年聚丙烯\",\"辽阳石化37万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化分公司39万吨/年聚丙烯\",\"炼油厂东、西油品车间储罐腐蚀隐患治理项目监理合同\",\"辽阳石化分公司37万吨/年聚丙烯\",\"辽阳石化分公司36万吨/年聚丙烯\",\"生产监测部安全隐患综合治理项目监理合同\",\"高性能聚丙烯项目\",\"辽阳石化分公司31万吨年聚丙烯\",\"储运厂输油管道隐患治理项目增补储罐隐患治理\",\"辽阳石化分公司35万吨/年聚丙烯\",\"辽阳石化33万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化40万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化公司五号工程项目\",\"烯烃厂丙烯储运系统隐患治理项目PC总承包\",\"辽阳石化39万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化31万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化36万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化储罐设施安全隐患治理\",\"热电厂#1-7锅炉制粉系统安全隐患治理项目施工总承包\",\"辽阳石化公司五号工程项目EPC总承包\",\"动力厂液氮及液氧系统设施完善监理项目\",\"辽阳石化32万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化34万吨/年高性能聚丙烯项目配套工程施工总承包\",\"辽阳石化分公司芳烃厂挖潜优化增效节能改造-部分公用管廊蒸汽凝结水回收利用节能改造项目\",\"辽阳石化30万吨/年聚丙烯\",\"芳烃厂芳烃联合装置高含硫气隐患治理项目\",\"30万吨/年高性能聚丙烯项目部\",\"辽阳石化分公司动力厂液氮及液氧系统设施完善项目\",\"辽阳石化分公司动力厂440#装置增设隔油池项目\",\"辽阳石化6号工程\",\"辽阳石化5号工程\",\"辽阳石化分公司动力厂440#装置增设隔油池项目（项目）\",\"辽阳石化分公司热电厂东区电气系统隐患治理项目安装工程\",\"部分加热炉环保达标改造项目施工总承包\",\"辽阳石化分公司五号工程项目\",\"热电厂东区电气系统隐患治理项目安装工程项目\",\"聚酯厂电气设备隐患综合治理项目炼油厂总变完善保护子项和烯烃厂裂解装置变频回路改造增补内容\",\"炼油厂东、西油品车间储罐腐蚀隐患治理项目\",\"制约生产装置长周期运行设备改造项目监理\",\"辽阳石化分公司油化厂催化裂化装置烟气脱硫部分环保提标改造项目\"]
                    String defaultValueScript = colpropcl.getString("defaultValueScript");// 显示值

                    defaultKeyScript = defaultKeyScript.replace("[", "");
                    defaultKeyScript = defaultKeyScript.replace("\\\"", "");
                    defaultValueScript = defaultValueScript.replace("[", "");
                    defaultValueScript = defaultValueScript.replace("\\\"", "");

                    if (defaultKeyScript != null && !"".equals(defaultKeyScript)) {
                        String[] arr1 = defaultKeyScript.split(",");// 实际值
                        String[] arr2 = defaultValueScript.split(",");// 显示值
                        for (int i1 = 0; i1 < arr1.length; i1++) {
                            String defkey = arr1[i1];
                            String defval = "";
                            try {
                                defval = arr2[i1];
                            } catch (Exception ex) {
                            }
                            mapdefaultKeyScript.put(tableCol + "." + defkey, defval);
                        }
                    }
                } else if ("uploadImg".equals(comType)) {

                    mapimg.put(tableCol, "");
                } else if ("hhSignSub".equals(comType)) {
                    hhSignSubbool = true;
                }

            }
        }
        list.add(listtable);// 添加到list中，第一行标题输出列

        List<String> imgList = new ArrayList<String>();// 普通图片的，图片id集合
        List<String> hhSignSubid = new ArrayList<String>();// 保存盖章签名的id，用于查询图片对照表的
        JSONArray jdatas = JSONArray.parseArray(object.getString("data"));// 数据源数据
        if (jdatas != null && jdatas.size() > 0) {
            for (int i = 0; i < jdatas.size(); i++) {
                JSONObject jvalue = jdatas.getJSONObject(i);
                String ID = jvalue.getString("ID");
                hhSignSubid.add(ID);
                if (mapimg != null) {
                    for (String key : mapimg.keySet()) {
                        String celImg = jvalue.getString(key);// 取出数据源中图片的值
                        // [{\"name\":\"333.jpg\",\"id\":\"ZZVQIDXR05UFZQOZUV1918\",\"url\":\"/files/2022_11/3331667983232194.jpg\",\"urlSave\":\"\",\"uid\":1667983232794,\"status\":\"success\"}]
                        JSONArray jimg = JSONArray.parseArray(celImg);
                        if (jimg != null && jimg.size() > 0) {
                            for (int im = 0; im < jimg.size(); im++) {
                                JSONObject joimg = jimg.getJSONObject(im);
                                String imgid = joimg.getString("id");
                                imgList.add(imgid);// 这个整体一次性查图片路径用的
                            }

                        }
                    }
                }
            }
        }

        // 获取印章签名的map{dataindexid.组件id=[1印章图片id，2印章图片id] }
        Map<String, List<String>> maphhSignSub = new HashMap<String, List<String>>();
        // 封装印章图片id = 图片路径
//		Map<String, String> mapImg = new HashMap<String, String>();
        if (hhSignSubbool) {
            // 获取印章签名的map{dataindexid.组件id=[1印章图片id，2印章图片id] }
            List<String> imgids = gethhSignSub(null, hhSignSubid, maphhSignSub);
//			// 封装图片id = 图片路径
//			mapImg = getImgAddress(imgids);
        }
        // 封装数据中普通图片id = 图片路径
//		Map<String, String> mapPtImg = new HashMap<String, String>();
//		if (imgList != null && imgList.size() > 0) {
//			// 封装图片id = 图片路径
//			mapPtImg = getImgAddress(imgList);
//		}

        if (jdatas != null && jdatas.size() > 0) {
            int n = jdatas.size();
            for (int i = 0; i < n; i++) {
                List<String> listdata = new ArrayList<String>();// excel==============输出数据
                JSONObject jvalue = jdatas.getJSONObject(i);

                String ID = jvalue.getString("ID");
                for (String key : mapCols.keySet()) {
                    String value = jvalue.getString(key);
                    // 图片，印章，这里需要获取图片路径，并且要在前面加上标识{{#img}}
                    String c_key = key + "." + value;
                    if (mapdefaultKeyScript.containsKey(c_key)) {// 下拉列表========================
                        // 下拉列表需要取显示值
                        value = mapdefaultKeyScript.get(c_key);
                    }
                    String d_key = ID + "." + key;
                    if (maphhSignSub.containsKey(d_key)) {// 印章组件=============================
                        value = "";
                        // 获取印章签名的map{dataindexid.组件id=[1印章图片id，2印章图片id] }
                        List<String> listhhSignSub = maphhSignSub.get(d_key);// [1印章图片id，2印章图片id]
                        for (int k1 = 0; k1 < listhhSignSub.size(); k1++) {

                            String id = listhhSignSub.get(k1);// 获取印章图片id
                            if (StringUtils.isNotEmpty(id)) {
                                // 获取 图片路径
                                value = value + "," + id;
                            }
//							if (mapImg.containsKey(id)) {
//								// 获取 图片路径
//								value = value + "," + mapImg.get(id);
//							}
                        }
                        if (!"".equals(value)) {
                            value = value.substring(1);
                            value = "#img_" + value;
                        }
                    }
                    if (mapimg.containsKey(key)) {// 普通图片==========================================
                        JSONArray jimg = JSONArray.parseArray(value);
                        if (jimg != null && jimg.size() > 0) {
                            value = "";
                            for (int im = 0; im < jimg.size(); im++) {
                                JSONObject joimg = jimg.getJSONObject(im);
                                String imgid = joimg.getString("id");
                                if (StringUtils.isNotEmpty(imgid)) {
                                    // 获取 图片路径
                                    value = value + "," + imgid;
                                }
//								if (mapPtImg.containsKey(imgid)) {
//									// 获取 图片路径
//									value = value + "," + mapPtImg.get(imgid);
//								}
                            }
                            if (!"".equals(value)) {
                                value = value.substring(1);
                                value = "#img_" + value;
                            }
                        }
                    }

                    listdata.add(value);
                }
                list.add(listdata);// 添加到list中，每一行的数据列
            }

        }
        return list;
    }

    /**
     * 获取模板中数据源相关的值
     *
     * @param map_tds
     * @param map_tdsData
     * @param formid
     * @param dataid
     * @param dataindexid
     * @param businessKeys
     */
    public void getTdsDataExcel(Map<String, String> mapv, Map<String, JSONObject> map_tds,
                                Map<String, List<List<String>>> map_tdsData, String formid, String dataid, String dataindexid,
                                String[] businessKeys) {

        // 需要从表单设置中，拿出所有的 数据源配置，在解析数据源，查出数据源的值
        SysFormDto dto_1 = formService.getSysFormContent(formid, null);
        String formJson = dto_1.getFormContent();// 表单所有属性
        JSONObject jsonObj = JSONObject.parseObject(formJson);// 转json
        JSONArray jsonArr = JSONArray.parseArray(jsonObj.getString("steps"));// 取出属性信息
        if (jsonArr.size() > 0) {
            int il = jsonArr.size();
            // 遍历当前表单中的所有组件
            for (int i = 0; i < il; i++) {// 这里写循环，是未来可能会有一个表单多个页清，目前就一页
                // 获取表单中的第一页数据
                JSONObject bq = JSONObject.parseObject(jsonArr.getString(i));
                // *************一、表单中 所有组件（不在分组内的组件）*****************************
                JSONArray singleColumns = JSONArray.parseArray(bq.getString("column"));
                String inParaAlias_all = bq.getString("inParaAlias");// "inParaAlias": "dataid=@businessKeys#arr=0|"
                // *************一、表单中 所有组件（在分组内的组件）*****************************
                JSONArray groups = JSONArray.parseArray(bq.getString("group"));
                if (groups != null && groups.size() > 0) {
                    // 开始循环，取每一个组件
                    for (int p = 0, pl = groups.size(); p < pl; p++) {
                        JSONObject column_grop = JSONObject.parseObject(groups.getString(p));
                        JSONArray singleColumns1 = JSONArray.parseArray(column_grop.getString("column"));
                        if (singleColumns1 != null && singleColumns1.size() > 0) {
                            singleColumns.addAll(singleColumns1);
                        }
                    }
                }
                // 上面代码是把正常的组件集合，和分组内的组件集合，进行了合并，合并后，下面统一执行一次组件类型遍历

                if (singleColumns != null && singleColumns.size() > 0) {
                    // 开始循环，取每一个组件
                    for (int k = 0, kl = singleColumns.size(); k < kl; k++) {
                        JSONObject column = JSONObject.parseObject(singleColumns.getString(k));

                        if (column.containsKey("sjk")) {// 绑定具体表的一般的组件
                            String table = column.getString("sjk");// 组件绑定的表名
                            String tableCol = column.getString("sjkzd");// 组件绑定的字段名
                            String type = column.getString("type");// 组件类型select，radio ，checkbox
                            // {{ACT_BUSS_USER_XMJYB.TCBM}}.ZDADFUF2341HXXQ11
                            if ("select".equals(type) || "radio".equals(type) || "checkbox".equals(type)) {
                                JSONArray dicData = column.getJSONArray("dicData");
                                if (dicData != null && dicData.size() > 0) {
                                    for (int k1 = 0; k1 < dicData.size(); k1++) {
                                        JSONObject valueData = dicData.getJSONObject(k1);// {label: '测试1',value: '1'}
                                        String label = valueData.getString("label");
                                        String value = valueData.getString("value");
                                        // 项目名称：{{ABC.name}}
                                        String xm = "{{" + table + "." + tableCol + "}}." + value;
                                        mapv.put(xm, label);// {{ACT_BUSS_USER_XMJYB.TCBM}}.1='测试1'
                                    }
                                }
                            }

                        }
                        // 绑定数据源的字段的（组件）
                        if (column.containsKey("cshSjk") && !column.containsKey("sjk")) {
                            String cshSjk = column.getString("cshSjk");// 数据源表 "sys_sstzd",
//							String cshSjkzd=column.getString("cshSjkzd");//数据源字段 "devcode",
                            String inParaAlias = inParaAlias_all;// 数据源输入参数 "dataid=@businessKeys#arr=0|"
//							{{tds_sys_sstzd______dataid__d____a__businessKeys#arr__d__0__1________devcode______ZZVFO9BS0121CVAO3P4650}}
//							String dateCol = "tds_"+cshSjk+"______"+inParaAlias+"______"+cshSjkzd+"______"+formId;

                            String key_qc = "tds_" + cshSjk + "______" + inParaAlias + "______" + formid;// 数据源表+输入参数
                            if (map_tds.containsKey(key_qc)) {
                                // 表示该数据源已经查过了
                                continue;
                            } else {
                                String tdsAlias = cshSjk;// 数据源别名
                                String para = inParaAlias_all;// 数据源参数
//								if("".equals(inParaAlias)) {
//									inParaAlias="FORM_DATA_INDEX_ID=" + dataid;
//								}
                                para = getbusinessKeysReplace(para, dataid, dataindexid, businessKeys);

                                TdsQueryDto param = new TdsQueryDto();
                                param.setTdsAlias(tdsAlias);
                                param.setInParaAlias(para);
                                JSONArray json = dsService.getTDSData(param);
                                JSONObject object = JSONObject.parseObject(json.getString(0));
                                JSONArray datas = JSONArray.parseArray(object.getString("data"));
                                JSONObject dataval = null;
                                if (datas != null && datas.size() > 0) {
                                    dataval = datas.getJSONObject(0);
                                }
                                // ========================json里包含了大量的
                                // 数据源设置，这里要改成只要【字段=值】============================================
                                // tds_sys_sstzd______dataid__d____a__businessKeys#arr__d__0__1__ , { "FJLX":
                                // "xxsj_mr","WZMC": ""}
                                map_tds.put(key_qc, dataval);
                            }

                        }
                        // 表格组件
                        else if (column.containsKey("component")
                                && "tdsedit-table".equals(column.getString("component"))) {
                            JSONObject params = JSONObject.parseObject(column.getString("params"));
                            String tdsAlias = params.getString("tdsAlias");
                            String tdsInParaAlias = params.getString("tdsInParaAlias");
                            String inParaAlias = params.getString("tdsInParaAlias");
                            if (inParaAlias == null) {
                                inParaAlias = "";
                            }
                            if (tdsAlias != null && tdsAlias.length() > 0) {
                                tdsAlias = tdsAlias.replace(" ", "");
                            }
//							String dsAlias="#tds_"+tdsInParaAlias+"______"+tdsAlias+"______"+formId+"";
                            String key_qc = "#tds_" + inParaAlias + "______" + tdsAlias + "______" + formid;// 数据源表+输入参数
                            if (map_tdsData.containsKey(key_qc)) {
                                // 表示该数据源已经查过了
                                continue;
                            } else {
//								tdsAlias;//数据源别名
                                String para = tdsInParaAlias;// 数据源参数
//								if("".equals(inParaAlias)) {
//									inParaAlias="FORM_DATA_INDEX_ID=" + dataid;
//								}
                                para = getbusinessKeysReplace(para, dataid, dataindexid, businessKeys);
                                TdsQueryDto param = new TdsQueryDto();
                                param.setTdsAlias(tdsAlias);
                                param.setInParaAlias(para);
                                JSONArray json = dsService.getTDSData(param);
                                List<List<String>> listval = getJsonDatas(json);
                                // =========================================json里包含了大量的
                                // 数据源设置，这里要改成只要【表头】【字段=值】============================================
                                // tds_sys_sstzd______dataid__d____a__businessKeys#arr__d__0__1__ ,
                                // [[项目，负责人],[项目A，张三],[项目B，李四]]
                                map_tdsData.put(key_qc, listval);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取 印章组件的图片id
     *
     * @param dataindexid
     * @return
     */
    public List<String> gethhSignSub(String dataindexid, List<String> dataindexids,
                                     Map<String, List<String>> maphhSignSub) {
        // 获取印章签名的map{dataindexid.组件id=[1印章图片id，2印章图片id] }
        List<String> listImgs = new ArrayList<String>();
        Where where = Where.create();
//		where.in(SigData::getDataId, listImgs.toArray());
        where.eq(SigData::getTmused, 1);
        if (dataindexid != null && !"".equals(dataindexid)) {
            where.eq(SigData::getDataId, dataindexid);
        }
        if (dataindexids != null && dataindexids.size() > 0) {
            where.in(SigData::getDataId, dataindexids.toArray());
        }
        Order order = Order.create();
        order.orderByAsc(SigData::getTmsort);
        List<SigData> rlist = entityService.queryData(SigData.class, where, order, null);
        if (rlist != null && rlist.size() > 0) {
            int m = rlist.size();
            for (int i = 0; i < m; i++) {
                SigData e = rlist.get(i);
                String comid = e.getComId();// 组件id
                String sig_image = e.getSigImage();// 图片id
                String dataid = e.getDataId();
                listImgs.add(sig_image);// 用于查询真实图片地址
                String key = dataid + "." + comid;
                if (maphhSignSub.containsKey(key)) {
                    List<String> list = maphhSignSub.get(key);
                    list.add(sig_image);
                    maphhSignSub.put(key, list);
                } else {
                    List<String> list = new ArrayList<String>();
                    list.add(sig_image);
                    maphhSignSub.put(key, list);
                }
            }
        }

        // select file_address from SYS_FILES_INFO where id='ZZVQ49QB01061OSVU65127'
        return listImgs;
    }

    public Map<String, SysFilesInfo> getImgFileMap(List<String> listImgs) {
        if (StringUtils.isEmpty(listImgs)) {
            return null;
        }
        Where where = Where.create();
        where.in(SysFilesInfo::getId, listImgs.toArray());
        List<SysFilesInfo> list = entityService.queryData(SysFilesInfo.class, where, null, null);
        return StringUtils.isEmpty(list) ? null : list.stream().collect(Collectors.toMap(SysFilesInfo::getId, v -> v));
    }

    @Override
    /**
     * 获取表单信息模板，返回excel
     */
    public XSSFWorkbook getExportExcelFromTplJson(String tplId, String name) {
        // ================================创建excel=================================
        XSSFWorkbook workbook = new XSSFWorkbook();
        String title_sheet = "导出文件";
        if (name != null && !"".equals(name)) {
            title_sheet = name;
        }
        // ================================创建excel--sheet==========================
        XSSFSheet sheet = workbook.createSheet(title_sheet);
        // ================================创建excel--sheet---宽度=================
        sheet.setColumnWidth(0, 6000);
        sheet.setColumnWidth(1, 10000);
        sheet.setColumnWidth(2, 20000);
        CellRangeAddress regionx = new CellRangeAddress(0, 0, 0, 2);
        sheet.addMergedRegion(regionx);

        // ================================创建excel--第一行标题样式=================
        XSSFCellStyle styleHead0 = workbook.createCellStyle();
        styleHead0.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
        styleHead0.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
// 		styleHead0.setFillPattern(FillPatternType.SOLID_FOREGROUND);
// 		styleHead0.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        // 分类名字字体
        XSSFFont fontHead0 = workbook.createFont();
        fontHead0.setColor(IndexedColors.BLACK.getIndex());// HSSFColor.VIOLET.index //字体颜色
        fontHead0.setFontHeightInPoints((short) 18);// 字体大小
        fontHead0.setFontName("黑体");// 字体
        fontHead0.setBold(true);// 加粗
        // 生成表头字体
        styleHead0.setFont(fontHead0);

        // ================================创建excel--每一个表单项的列表头样式=================
        XSSFCellStyle styleHead = workbook.createCellStyle();
        styleHead.setAlignment(HorizontalAlignment.CENTER); // 设置水平对齐方式
        styleHead.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
        styleHead.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styleHead.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        styleHead.setBorderBottom(BorderStyle.THIN);
        styleHead.setBorderLeft(BorderStyle.THIN);
        styleHead.setBorderRight(BorderStyle.THIN);
        styleHead.setBorderTop(BorderStyle.THIN);
        // 分类名字字体
        XSSFFont fontHead = workbook.createFont();
        fontHead.setColor(IndexedColors.WHITE.getIndex());// HSSFColor.VIOLET.index //字体颜色
        fontHead.setFontHeightInPoints((short) 12);
        fontHead.setFontName("黑体");
        fontHead.setBold(true);// HSSFFont.BOLDWEIGHT_BOLD
        // 生成表头字体
        styleHead.setFont(fontHead);

        // ================================创建excel--数据样式=================
        XSSFCellStyle styleHead1 = workbook.createCellStyle();
        styleHead1.setWrapText(true);
        styleHead1.setAlignment(HorizontalAlignment.LEFT); // 设置水平对齐方式
        styleHead1.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
//		styleHead1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
// 		styleHead1.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        styleHead1.setBorderBottom(BorderStyle.THIN);
        styleHead1.setBorderLeft(BorderStyle.THIN);
        styleHead1.setBorderRight(BorderStyle.THIN);
        styleHead1.setBorderTop(BorderStyle.THIN);

        // ================================创建excel--红色字体数据样式=================
        XSSFCellStyle styleHead2 = workbook.createCellStyle();
        styleHead2.setWrapText(true);
        styleHead2.setAlignment(HorizontalAlignment.LEFT); // 设置水平对齐方式
        styleHead2.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
//		styleHead2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
// 		styleHead2.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        styleHead2.setBorderBottom(BorderStyle.THIN);
        styleHead2.setBorderLeft(BorderStyle.THIN);
        styleHead2.setBorderRight(BorderStyle.THIN);
        styleHead2.setBorderTop(BorderStyle.THIN);
        // 分类名字字体
        XSSFFont fontHead2 = workbook.createFont();
        fontHead2.setColor(IndexedColors.RED.getIndex());// HSSFColor.VIOLET.index //字体颜色
        fontHead2.setFontHeightInPoints((short) 12);
        fontHead2.setFontName("黑体");
        // 生成表头字体
        styleHead2.setFont(fontHead2);

        // ================================以上代码为初始化EXCEL样式等==============================

        int num = 0;// excel 行
        // 获取表单的所有配置数据
        List<SysFormDto> list = formService.getTplFormContents(tplId);// 这个方法有问题，以后的优化，里面有很多重复循环查询等等

        if (list != null && list.size() > 0) {

            // ================================创建excel--第一行标题内容=================
            XSSFRow row = sheet.createRow(num);
            XSSFCell cell = row.createCell(0);
            cell.setCellStyle(styleHead0);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(name);
            // ===========================================================================

            num = num + 1;// 行标识
            // 循环表单所有表单
            for (SysFormDto dto : list) {
                // 当前表单
                String formId = dto.getFormId();// 表单id
                String formJson = dto.getFormContent();// 表单所有属性
                JSONObject jsonObj = JSONObject.parseObject(formJson);// 转json
                String steps = jsonObj == null || !jsonObj.containsKey("steps") ? null : jsonObj.getString("steps");
                JSONArray jsonArr = StringUtils.isEmpty(steps) ? null : JSONArray.parseArray(steps);// 取出属性信息
                if (jsonArr != null && jsonArr.size() > 0) {
                    int il = jsonArr.size();
                    // 遍历当前表单中的所有组件
                    for (int i = 0; i < il; i++) {// 这里写循环，是未来可能会有一个表单多个页清，目前就一页
                        // 获取表单中的第一页数据
                        JSONObject bq = JSONObject.parseObject(jsonArr.getString(i));

                        // 表单属性--表单名称【项目建议表】
                        String title = bq.getString("titleName");// "titleName": "【项目建议表】",
                        title = (title == null || "".equals(title)) ? dto.getFormName() : title;
                        String inParaAlias_all = bq.getString("inParaAlias");// "inParaAlias":
                        // "dataid=@businessKeys#arr=0|"
                        // ================================创建excel--每一个表单项的列表头内容=================
                        XSSFRow row1 = sheet.createRow(num);// 创建行
                        XSSFCell cell11 = row1.createCell(0);// 创建第一列
                        cell11.setCellStyle(styleHead);// 单元格样式
                        cell11.setCellType(CellType.STRING);// 单元格类型
                        cell11.setCellValue("表单项");// 单元格的值
                        XSSFCell cell12 = row1.createCell(1);// 创建第二列
                        cell12.setCellStyle(styleHead);// 单元格样式
                        cell12.setCellType(CellType.STRING);// 单元格类型
                        cell12.setCellValue("组件名称");// 单元格的值
                        XSSFCell cell13 = row1.createCell(2);// 创建第三列
                        cell13.setCellStyle(styleHead);// 单元格样式
                        cell13.setCellType(CellType.STRING);// 单元格类型
                        cell13.setCellValue("替换值");// 单元格的值
                        // =====================================================================================

                        num = num + 1;// 行标识

                        int ks = num;// 合并行开始标识
                        int js = num;// 合并行结束标识
                        // *************一、表单中 所有组件（不在分组内的组件）*****************************
                        JSONArray singleColumns = JSONArray.parseArray(bq.getString("column"));

                        // *************一、表单中 所有组件（在分组内的组件）*****************************
                        JSONArray groups = JSONArray.parseArray(bq.getString("group"));
                        if (groups != null && groups.size() > 0) {
                            // 开始循环，取每一个组件
                            for (int p = 0, pl = groups.size(); p < pl; p++) {
                                JSONObject column_grop = JSONObject.parseObject(groups.getString(p));
                                JSONArray singleColumns1 = JSONArray.parseArray(column_grop.getString("column"));
                                if (singleColumns1 != null && singleColumns1.size() > 0) {
                                    singleColumns.addAll(singleColumns1);
                                }
                            }
                        }
                        // 上面代码是把正常的组件集合，和分组内的组件集合，进行了合并，合并后，下面统一执行一次组件类型遍历

                        if (singleColumns != null && singleColumns.size() > 0) {
                            // 开始循环，取每一个组件
                            for (int k = 0, kl = singleColumns.size(); k < kl; k++) {
                                String key = "";// 名称
                                String value = "";// 值
                                // 组件信息对象
                                JSONObject column = JSONObject.parseObject(singleColumns.getString(k));
                                String label = column.getString("label");// 组件名称

                                // 是否显示
                                if (column.containsKey("display") && "false".equals(column.getString("display"))) {
                                    continue;
                                }
                                key = label;
                                // 绑定具体表的具体字段的（组件）
                                if (column.containsKey("sjk")) {// 绑定具体表的一般的组件
                                    String table = column.getString("sjk");// 组件绑定的表名
                                    String tableCol = column.getString("sjkzd");// 组件绑定的字段名

                                    // 项目名称：{{ABC.name}}
                                    value = "{{table__" + table + "." + tableCol + "}}";

                                    if (column.containsKey("component")
                                            && "hhuploadFormImg".equals(column.getString("component"))) {
                                        // 图片组件
                                        value = "{{img_3_2____" + table + "." + tableCol + "}}";
                                    }
                                }
                                // 绑定数据源的字段的（组件）
                                else if (column.containsKey("cshSjk")) {
                                    String cshSjk = column.getString("cshSjk");// 数据源表 "sys_sstzd",
                                    String cshSjkzd = column.getString("cshSjkzd");// 数据源字段 "devcode",
                                    String inParaAlias = inParaAlias_all;// 数据源输入参数 "dataid=@businessKeys#arr=0|"
                                    inParaAlias = inParaAlias == null ? "" : inParaAlias;
                                    inParaAlias = inParaAlias.replace("=", "__d__");
                                    inParaAlias = inParaAlias.replace("|", "__1__");
                                    inParaAlias = inParaAlias.replace("@", "__a__");
//									{{tds_sys_sstzd______dataid__d____a__businessKeys#arr__d__0__1________devcode______ZZVFO9BS0121CVAO3P4650}}
                                    String dateCol = "tds_" + cshSjk + "______" + inParaAlias + "______" + cshSjkzd
                                            + "______" + formId;
                                    value = "{{" + dateCol + "}}";
                                }
                                // 表格组件
                                else if (column.containsKey("component")
                                        && "tdsedit-table".equals(column.getString("component"))) {
                                    JSONObject params = JSONObject.parseObject(column.getString("params"));
                                    String tdsAlias = params.getString("tdsAlias");
                                    String tdsInParaAlias = params.getString("tdsInParaAlias");
                                    if (tdsInParaAlias == null) {
                                        tdsInParaAlias = "";
                                    }
                                    if (tdsAlias != null && tdsAlias.length() > 0) {
                                        tdsAlias = tdsAlias.replace(" ", "");
                                    }
                                    tdsInParaAlias = tdsInParaAlias.replace("=", "__d__");
                                    tdsInParaAlias = tdsInParaAlias.replace("|", "__1__");
                                    tdsInParaAlias = tdsInParaAlias.replace("@", "__a__");

                                    String dsAlias = "#tds_" + tdsInParaAlias + "______" + tdsAlias + "______" + formId
                                            + "";

                                    key = label + "(数据表)";
                                    value = "{{" + dsAlias + "}}";
                                } // 印章组件
                                else if (column.containsKey("component")
                                        && "hhSignSub".equals(column.getString("component"))) {
                                    String prop = column.getString("prop");
//									hhSignSub
                                    key = label;
//									value="{{img3行_2列____"+table+"."+tableCol+"}}";
                                    value = "{{hhSignSub_2_2____" + prop + "}}";
                                }
                                // 表格跳转链接组件
                                else if (column.containsKey("component")
                                        && "hhToUrlSub".equals(column.getString("component"))) {
//									JSONObject params = JSONObject.parseObject(column.getString("params"));
//									String url = params.getString("url");
                                    value = "不支持跳转链接的组件！";
                                }
                                if (!"".equals(key) && "".equals(value)) {
                                    value = "未配置对应表字段！";
                                }

                                // ================================创建excel--数据内容=================
                                // excel写组件名=========
                                XSSFRow row2 = sheet.createRow(num);
                                XSSFCell cellx = row2.createCell(0);
                                cellx.setCellStyle(styleHead1);
                                cellx.setCellType(CellType.STRING);
                                cellx.setCellValue(title);

                                // excel写组件名=========
                                XSSFCell cell2 = row2.createCell(1);
                                cell2.setCellStyle(styleHead1);
                                cell2.setCellType(CellType.STRING);
                                cell2.setCellValue(key);

                                // excel组件替换值=========
                                XSSFCell cell3 = row2.createCell(2);
                                if (!"".equals(key) && ("未配置对应表字段！".equals(value) || "不支持跳转链接的组件！".equals(value))) {
                                    cell3.setCellStyle(styleHead2);// 红色字
                                } else {
                                    cell3.setCellStyle(styleHead1);// 黑色字
                                }
                                cell3.setCellType(CellType.STRING);
                                cell3.setCellValue(value);
                                // =======================================================================

                                num = num + 1;// 行加1
                                js = js + 1;// 这个是合并行的结束标识
                            }
                        }

                        // ================================创建excel--合并第一列【表单项】的单元格=================
                        int js_v = js - 1;
                        if (js_v - ks >= 1) {
                            CellRangeAddress regionx1 = new CellRangeAddress(ks, js - 1, 0, 0);
                            sheet.addMergedRegion(regionx1);
                        }

                    }
                }
            }
        }

        return workbook;
    }

    @Override
    public XSSFWorkbook exportExcelFile(ExcelQuery bean) {
        XSSFWorkbook workbook = null;
        FileInputStream fileInputStream = null;
        try {
            // 根据表单id获取表单数据，并且封装，为下面Excel替换做准备
//			String excelfile = bean.getFileAddress();// "C:/Users/<USER>/Desktop/222222222/全生命周期_excel模板1.xlsx";
            String fileId = bean.getFileAddress(); // 此处传入的是图片id
            String dataid = bean.getBusinessKey();// "ZZVQIDUM05UFZQIDVZ1978";
            String formid = bean.getFormId();// "ZZVFO98C0121CVRP3I4620";//===这里目前就用了一个表单模板id，如果有多个，下面考虑重新查询方法
            String dataindexid = "";
            String dataids = bean.getBusinessKeys();// "[\"ZZVQIDUM05UFZQIDVZ1978\"]";
            String[] businessKeys = (dataids.replace("[", "").replace("\"", "")).split(",");

            // 根据dataid获取对应formid的dataindexid
            Map<String, String> formDataIndexIdMap = dataService.getDataIndexIdMapByDataId(dataid);
            if (formDataIndexIdMap != null && formDataIndexIdMap.size() > 0) {
                dataindexid = formDataIndexIdMap.get(formid);
            }

            // 1.找到表单中所有的非数据源的，存储在表中的值
            // 封装表名字段名，就是模板中非数据源的模式，列如： {{ACT_BUSS_USER_JBSJ.JH}}
            // 封装结果{{ACT_BUSS_USER_JBSJ.JH}}="双北33-47" 便于下面循环Excel表时替换用
            Map<String, String> map_table = getMapTable(dataid, formid);// ====================
            // {{ACT_BUSS_USER_JBSJ.JH}}="双北33-47"

            // 获取 关于这3种组件的 显示值select，radio ，checkbox
            Map<String, String> mapv = new HashMap<String, String>();

            // 获取数据源相关的值
            Map<String, JSONObject> map_tds = new HashMap<String, JSONObject>();
            Map<String, List<List<String>>> map_tdsData = new HashMap<String, List<List<String>>>();
            getTdsDataExcel(mapv, map_tds, map_tdsData, formid, dataid, dataindexid, businessKeys);

            // 获取印章签名的map{dataindexid.组件id=[1印章图片id，2印章图片id] }
            Map<String, List<String>> maphhSignSub = new HashMap<String, List<String>>();
            List<String> imgids = gethhSignSub(dataindexid, null, maphhSignSub);

            // 封装图片id = 图片路径
//			Map<String, String> mapImg = new HashMap<String, String>();
//			if (imgids != null && imgids.size() > 0) {
//				mapImg = getImgAddress(imgids);
//			}

            Map<String, SysFilesInfo> imgFileMap = getImgFileMap(imgids);

            // 根据地址，获取excel流
//			FileInputStream fis = new FileInputStream(excelfile);
//			BufferedInputStream bis = new BufferedInputStream(fis);
//			InputStream inputStream = bis;

            // C:\Users\<USER>\Desktop
            /*
             * SysFilesInfo fileInfo = getFileById(fileId); // InputStream fileInputStream =
             * fs.getFileSteam(fileInfo); // File tempFile = createTempFileInDisk(fileInfo);
             * MultipartFile multipartFile = fs.getMultipartFile(fileInfo); String
             * tempFileDiskAdress = getTempFileDiskAdress(fileInfo); File tempFile = new
             * File(tempFileDiskAdress); if (!tempFile.exists()) { tempFile.mkdir(); }
             * InputStream fileSteam = fs.getFileSteam(fileInfo); FileOutputStream fos =
             * null; fos = new FileOutputStream(tempFile); IOUtils.copy(fileSteam, fos);
             */
//			File tempFile = new File("C:/Users/<USER>/Desktop/签章.xls");
//			FileInputStream fis = new FileInputStream("C:/Users/<USER>/Desktop/签章.xlsx");
//			BufferedInputStream bis = new BufferedInputStream(fis);
//			InputStream inputStream = bis;
            SysFilesInfo fileInfo = getFileById(fileId);
            MultipartFile multipartFile = fs.getMultipartFile(fileInfo);
            fileInputStream = (FileInputStream) multipartFile.getInputStream();
            workbook = new XSSFWorkbook(fileInputStream);

            XSSFCellStyle styleHead1 = workbook.createCellStyle();
            styleHead1.setWrapText(true);
            styleHead1.setAlignment(HorizontalAlignment.LEFT); // 设置水平对齐方式
            styleHead1.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直对齐方式
//			styleHead1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//	 		styleHead1.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            styleHead1.setBorderBottom(BorderStyle.THIN);
            styleHead1.setBorderLeft(BorderStyle.THIN);
            styleHead1.setBorderRight(BorderStyle.THIN);
            styleHead1.setBorderTop(BorderStyle.THIN);

            XSSFSheet sheet;
            for (int j = 0; j < workbook.getNumberOfSheets(); j++) {
                // 获取sheet内容
                sheet = workbook.getSheetAt(j);
                // 画图的顶级管理器，一个sheet只能获取一个（一定要注意这点）
                XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                // 获取行信息
                for (int x = 1; x <= sheet.getLastRowNum(); x++) {
                    // 取每一行的数据
                    XSSFRow row = sheet.getRow(x);
                    if (row != null) {
                        // 获取列个数
                        int cellnum = row.getLastCellNum();
                        // 遍历行的每一列
                        for (int i = 0; i < cellnum; i++) {
                            // 获取列信息3
                            XSSFCell cell = row.getCell(i);
                            if (cell == null) {// 如果列为空跳出
                                continue;
                            }
                            if (cell.getCellType().equals(CellType.STRING)) {// 判断取值类型，只有字符型才是可替换变量
                                // 取当前值
                                String value = cell.getStringCellValue();
                                // 判断值如果不为空
                                if (!"".equals(value)) {
                                    // 判断值是否为要替换的--这里以后要替换为map
                                    if (value.startsWith("{{#tds")) {// 数据源表格======================
                                        String tdsinfo = value;
                                        tdsinfo = tdsinfo.replace("{{", "");
                                        tdsinfo = tdsinfo.replace("}}", "");
                                        tdsinfo = tdsinfo.replace("__a__", "@");
                                        tdsinfo = tdsinfo.replace("__1__", "|");
                                        tdsinfo = tdsinfo.replace("__d__", "=");
//	                            		String key_qc="tds_"+cshSjk+"______"+inParaAlias;//数据源表+输入参数
                                        String arr[] = tdsinfo.split("______");
                                        String key = arr[0] + "______" + arr[1] + "______" + arr[2];
                                        if (map_tdsData.containsKey(key)) {
                                            List<List<String>> list = map_tdsData.get(key);
                                            int addRow = list.size();// 需要添加多少行
                                            int addCell = list.get(0).size();// 需要多少列
                                            // 动态插入行
                                            if (x + 1 < sheet.getLastRowNum()) {// 不是最后，再继续加行
                                                sheet.shiftRows(x + 1, sheet.getLastRowNum(), addRow);
                                            }
                                            // 插入新行

                                            for (int i2 = 0; i2 < list.size(); i2++) {// 数据行循环
                                                int dqh = x + i2;
                                                XSSFRow row_add = sheet.createRow(dqh);
                                                List<String> list_col = list.get(i2);// 动态列个数
                                                if (list_col != null && list_col.size() > 0) {
                                                    for (int i3 = 0; i3 < list_col.size(); i3++) {// 列循环

                                                        String val = list_col.get(i3);
                                                        // 添加数据源字段列
                                                        if (val.startsWith("#img_")) {// 如果是图片===============
                                                            val = val.replace("#img_", "") + ",";
                                                            String[] imgarr = val.split(",");

                                                            for (int a1 = 0; a1 < imgarr.length; a1++) {
                                                                XSSFCell cell2 = row_add.createCell(i + i3);
                                                                cell2.setCellStyle(styleHead1);
                                                                cell2.setCellType(CellType.STRING);
                                                                cell2.setCellValue("图片");
                                                                String imgId = imgarr[a1]; // 此处为图片id
                                                                if (!"".equals(imgId)) {

                                                                    int dx1 = 1;// x轴偏移开始
                                                                    int dx2 = 1 * Units.EMU_PER_POINT;// x轴偏移截止
                                                                    int dy1 = 1;// y轴偏移开始
                                                                    int dy2 = 1 * Units.EMU_PER_POINT;// y轴偏移截止
                                                                    if (a1 > 0) {
                                                                        dx1 = 1 * Units.EMU_PER_POINT
                                                                                + (a1 * 3 * Units.EMU_PER_POINT);// x轴偏移开始
                                                                        dx2 = 1 * Units.EMU_PER_POINT
                                                                                + (a1 * 3 * Units.EMU_PER_POINT);// x轴偏移截止
                                                                        dy1 = 1 * Units.EMU_PER_POINT
                                                                                + (a1 * 3 * Units.EMU_PER_POINT);// y轴偏移开始
                                                                        dy2 = 1 * Units.EMU_PER_POINT
                                                                                + (a1 * 3 * Units.EMU_PER_POINT);// y轴偏移截止
                                                                    }
                                                                    // createAnchor(x轴偏移开始, y轴偏移开始, x轴偏移截止, y轴偏移截止,
                                                                    // (short) 开始列,开始行, (short) 截止列, 截止行);
                                                                    XSSFClientAnchor anchor = patriarch.createAnchor(
                                                                            dx1, dy1, dx2, dy2, (short) i + i3, dqh,
                                                                            (short) i + i3 + 1, dqh + 1);

                                                                    // 创建流
//																	BufferedImage bufferImg;
//																	ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
//																	try {
//																		bufferImg = ImageIO.read(new File(imgAddress));
//																		ImageIO.write(bufferImg, "jpg", byteArrayOut);
//																	} catch (IOException e) {
//																	}
//																	byte[] img = byteArrayOut.toByteArray();

                                                                    SysFilesInfo imgFileInfo = getFileById(imgId);
                                                                    byte[] img = getImageFileBytes(imgFileInfo);
                                                                    if (img != null) {
                                                                        patriarch.createPicture(anchor,
                                                                                workbook.addPicture(
                                                                                        Objects.requireNonNull(img),
                                                                                        HSSFWorkbook.PICTURE_TYPE_JPEG));
                                                                    }

                                                                }

                                                            }
                                                        } else {
                                                            // 非图片，普通字段
                                                            XSSFCell cell2 = row_add.createCell(i + i3);
                                                            cell2.setCellStyle(styleHead1);
                                                            cell2.setCellType(CellType.STRING);
                                                            cell2.setCellValue(val);
                                                        }

                                                    }
                                                }
                                            }
                                            i = i + addCell;
                                        }
                                    } else if (value.startsWith("{{tds")) {// 数据源字段======================
                                        // {{tds_sys_gidc__d__a____dataid__d____a__dataid____name____ZDIAFJAIAVS23W2}}
                                        String tdsinfo = value;
                                        tdsinfo = tdsinfo.replace("{{", "");
                                        tdsinfo = tdsinfo.replace("}}", "");
                                        tdsinfo = tdsinfo.replace("__a__", "@");
                                        tdsinfo = tdsinfo.replace("__1__", "|");
                                        tdsinfo = tdsinfo.replace("__d__", "=");
//	                            		String key_qc="tds_"+cshSjk+"______"+inParaAlias;//数据源表+输入参数
                                        String arr[] = tdsinfo.split("______");
                                        String key = arr[0] + "______" + arr[1] + "______" + arr[3];
                                        String col = arr[2];

                                        if (map_tds.containsKey(key)) {
                                            JSONObject jobj = map_tds.get(key);
                                            if (jobj.containsKey(col)) {
                                                cell.setCellValue(jobj.getString(col));
                                            }
                                        }

                                    } else if (value.startsWith("{{table__")) {// 普通字段======================
                                        // 普通的{{ACT_BUSS_USER_XMJYB.TCBM}}，直接解析
                                        value = value.replace("table__", "");
                                        if (map_table.containsKey(value)) {
                                            String c_value = map_table.get(value);// ZDADFUF2341HXXQ11
                                            String key = value + "." + c_value;// {{ACT_BUSS_USER_XMJYB.TCBM}}.ZDADFUF2341HXXQ11
                                            if (mapv.containsKey(key)) {// 如果有值，就属于下拉框,多选等，这里要把真实值 ，变成显示值
                                                c_value = mapv.get(key);
                                            }
                                            cell.setCellValue(c_value);
                                        }
                                    } else if (value.startsWith("{{img")) {// 普通图片======================
                                        // {{img_3_2____ACT_BUSS_USER_XMJYB.SCTPIMG}}
                                        value = value.replace("{{img_", "");
                                        value = value.replace("}}", "");
                                        // {{2_2____1667872276578_19449}}
                                        String[] arr1 = value.split("____");// [2_2,1667872276578_19449]
                                        String zjid = "";
                                        int h = 1;// 图片占几行
                                        int l = 1;// 图片占几列
                                        try {
                                            if (arr1 != null && arr1.length > 1) {
                                                zjid = arr1[1];// 2_2,1667872276578_19449
                                                String[] zw = arr1[0].split("_");
                                                if (zw != null && zw.length > 1) {
                                                    h = Integer.parseInt(zw[0]);
                                                    l = Integer.parseInt(zw[1]);
                                                }
                                            }
                                        } catch (Exception ex) {
                                        }

                                        String img_id = "";// 图片id
                                        value = "{{" + arr1[1] + "}}";
                                        if (map_table.containsKey(value)) {
                                            img_id = map_table.get(value);// ZDADFUF2341HXXQ11
                                        }
//										String imgAddress = "";
//										if (mapImg.containsKey(img_id)) {
//											imgAddress = mapImg.get(img_id);// 图片路径=========
//										} else {
//											imgAddress = getImgAddress(img_id);
//										}

                                        SysFilesInfo imgInfo = null;
                                        if (imgFileMap.containsKey(img_id)) {
                                            imgInfo = imgFileMap.get(img_id);
                                        } else {
                                            imgInfo = getFilesInfoById(img_id);
                                        }

                                        // 图片占位=====整体向下添加新行
                                        int addRow = h;// excel需要向下添加多少行
                                        int addCell = l;// 需要向右添加多少列
                                        // 动态插入行
                                        if (x + 1 < sheet.getLastRowNum()) {// 不是最后，再继续加行
                                            sheet.shiftRows(x + 1, sheet.getLastRowNum(), addRow);
                                        }

                                        int dx1 = 1;// x轴偏移开始
                                        int dx2 = 1;// x轴偏移截止
                                        int dy1 = 1;// y轴偏移开始
                                        int dy2 = 1;// y轴偏移截止
                                        // createAnchor(x轴偏移开始, y轴偏移开始, x轴偏移截止, y轴偏移截止, (short) 开始列,开始行, (short) 截止列,
                                        // 截止行);
                                        XSSFClientAnchor anchor = patriarch.createAnchor(dx1, dy1, dx2, dy2, (short) i,
                                                x, (short) i + l, x + h);

                                        // 创建流
//										BufferedImage bufferImg;
//										ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
//										try {
//											bufferImg = ImageIO.read(new File(imgAddress));
//											ImageIO.write(bufferImg, "jpg", byteArrayOut);
//										} catch (IOException e) {
//										}
//										byte[] img = byteArrayOut.toByteArray();

                                        byte[] img = getImageFileBytes(imgInfo);
                                        if (img != null) {
                                            patriarch.createPicture(anchor, workbook.addPicture(
                                                    Objects.requireNonNull(img), HSSFWorkbook.PICTURE_TYPE_JPEG));
                                        }
                                        i = i + addCell;
                                    } else if (value.startsWith("{{hhSignSub")) {// 印章签名======================
                                        // 印章签名组件 {{hhSignSub_2_2____1667872276578_19449}}
                                        value = value.replace("{{hhSignSub_", "");
                                        value = value.replace("}}", "");
                                        // {{2_2____1667872276578_19449}}
                                        String[] arr1 = value.split("____");// [2_2,1667872276578_19449]
                                        String zjid = "";

                                        int h = 1;// 图片占几行
                                        int l = 1;// 图片占几列
                                        try {
                                            if (arr1 != null && arr1.length > 1) {
                                                zjid = arr1[1];// 2_2,1667872276578_19449
                                                String[] zw = arr1[0].split("_");
                                                if (zw != null && zw.length > 1) {
                                                    h = Integer.parseInt(zw[0]);
                                                    l = Integer.parseInt(zw[1]);
                                                }
                                            }
                                        } catch (Exception ex) {
                                        }

                                        String key = dataindexid + "." + zjid;// ZDADFUF2341HXXQ11.1667872276578_19449
                                        if (maphhSignSub.containsKey(key)) {
                                            List<String> listimgs = maphhSignSub.get(key);// 获取图片的id
                                            if (listimgs != null && listimgs.size() > 0) {
                                                int num1 = listimgs.size();
                                                for (int k2 = 0; k2 < num1; k2++) {
                                                    String img_id = listimgs.get(k2);// 图片id
//													String imgAddress = "";
                                                    if (imgFileMap.containsKey(img_id)) {
                                                        SysFilesInfo imgInfo = imgFileMap.get(img_id);
//														imgAddress = mapImg.get(img_id);// 图片路径=========

                                                        // 图片占位=====整体向下添加新行
                                                        int addRow = h;// excel需要向下添加多少行
                                                        int addCell = l;// 需要向右添加多少列
                                                        // 动态插入行
                                                        if (x + (h * k2) + 1 <= sheet.getLastRowNum()) {// 不是最后，再继续加行
                                                            sheet.shiftRows(x + (h * k2) + 1, sheet.getLastRowNum(),
                                                                    addRow);
                                                        }

                                                        // 插入图片===========
                                                        int dx1 = 1;
                                                        int dx2 = 1;
                                                        int dy1 = 1;
                                                        int dy2 = 1;
                                                        // createAnchor(x轴偏移开始, y轴偏移开始, x轴偏移截止, y轴偏移截止, (short) 开始列,开始行,
                                                        // (short) 截止列, 截止行);
                                                        XSSFClientAnchor anchor = patriarch.createAnchor(dx1, dy1, dx2,
                                                                dy2, (short) i, x + (h * k2), (short) i + addCell,
                                                                x + (h * k2) + addRow);

                                                        // 创建流
//														BufferedImage bufferImg;
//														ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
//														try {
//															bufferImg = ImageIO.read(new File(imgAddress));
//															ImageIO.write(bufferImg, "jpg", byteArrayOut);
//														} catch (IOException e) {
//														}
//														byte[] img = byteArrayOut.toByteArray();
                                                        byte[] img = getImageFileBytes(imgInfo);
                                                        if (img != null) {
                                                            patriarch.createPicture(anchor,
                                                                    workbook.addPicture(Objects.requireNonNull(img),
                                                                            HSSFWorkbook.PICTURE_TYPE_JPEG));
                                                        }
                                                    }
                                                }
                                                i = i + l;
                                            }
                                        }

                                        cell.setCellValue("");

                                    }

                                }
                            }
                        }
                    }
                }
            }
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
//				if (bis != null) {
//					bis.close();
//				}
//				try {
//					if (fileSteam != null) {
//						fileSteam.close();
//					}
//					if (fos != null) {
//						fos.close();
//					}
//				} catch (Exception e) {}
            } catch (Exception e) {
            }

        } catch (Exception e) {
            log.error("Excel导出数据失败", e);
        } finally {
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
            } catch (Exception e) {
            }
        }
        return workbook;
    }

    public NiceXWPFDocument exportWordFile(WordParamDto dto) {

        NiceXWPFDocument tpl = null;

        String dataId = dto.getDataId();
        // 数据源解析参数赋值
        String param = "";
        Map<String, String> pmap = new HashMap<String, String>();
        pmap.put("dataId", dataId);
        if (StringUtils.isNotEmpty(dto.getParamMap())) {
            pmap.putAll(dto.getParamMap());
        }
        if (StringUtils.isNotEmpty(dto.getParamStr())) {
            String paraStr = dto.getParamStr().replace(",,,", "|");
            paraStr = paraStr.replace(",", "|");
            String[] params = paraStr.split("\\|");
            for (String s : params) {
                String[] ss = s.split("=");
                if (ss.length > 1) {
                    pmap.put(ss[0], ss[1]);
                } else if (ss.length > 0) {
                    pmap.put(ss[0], "");
                }
            }
            param = paraStr;
        }

        List<String> fids = new ArrayList<String>();
        fids.add(dto.getFileId());
        List<SysFilesInfo> fileList = getFilesInfoListByIds(fids);

        if (fileList != null && !fileList.isEmpty()) {
            // 图片相关变量
            Map<String, Integer> picmap = new HashMap<String, Integer>();// 多个图片记录
            Map<String, String> picKeyId = new HashMap<String, String>();// 多个图片记录
            Map<String, Integer> picWmap = new HashMap<String, Integer>();// 多个图片记录
            Map<String, String> namemarkKeyId = new HashMap<String, String>();// 多个签名记录
            List<String> picidList = new ArrayList<String>();// 图片id
            // 换行文本相关变量
            Map<String, Integer> textmap = new HashMap<String, Integer>();// 换行文本记录
            List<String> textKeyList = new ArrayList<String>();// 文本记录
            Map<String, List<String>> textKeyId = new HashMap<String, List<String>>();// 换行文本记录
            // 纯数据源公式
            Map<String, Object> sourceDsgsMap = new HashMap<String, Object>();// <ftds_ds______get(0,"colname"),
            // $ds.get(0,"colname")>
            Map<String, String> jgDsgsMap = new HashMap<String, String>();// <$ds.get(0,"colname"), 公式解析结果>
            Map<String, Integer> dsvalmap = new HashMap<String, Integer>();// 数据源结果是列表，word模板做换行调整

            // 根据dataid获取对应formid的dataindexid
//			Map<String, String> formDataIndexIdMap = dataService.getDataIndexIdMapByDataId(dataId);

            // 根据模板，获取单、复选、下拉菜单等对应的key,value信息
//			Map<String, Map<String, String>> kvmap = getTplComMap(tplId);

            PoitlTools pt = new PoitlTools();
            List<XWPFTemplate> tplList = new ArrayList<XWPFTemplate>();
            List<String> varList = new ArrayList<String>();

            // 第一次循环，获取所有变量，进行赋值
            Map<String, Object> map = new HashMap<String, Object>();// 整理变量对应对象map
            Map<String, Object> rmap = new HashMap<String, Object>();// 最终模板替换变量时用的map对象
            List<String> tabList = new ArrayList<String>();// 记录变量中设置的数据表名
            Map<String, String> dsFiles = new HashMap<String, String>();// 记录数据源对应的word文件id
            // 循环模板文件，获取所有变量
            for (SysFilesInfo fi : fileList) {
                String suffix = fi.getFileExt();// 模板类型docx
                File file = new File(fi.getFileAddress());// 模板地址D:/tm4UploadFiles/2022_11/完井总结报告（模板）1-71667370848545.docx
                List<String> fileVarList = pt.getVarList(file, suffix);
                varList.addAll(fileVarList);
                for (String var : fileVarList) {
                    if (var.startsWith("tds_")) {// 数据源公式
                        // var==tds_t_bd_zlfxbg______id__d____a__dataId______XMB______ZZVIFNUJ0121UV95WD0891
                        dsFiles.put(var.split("______")[0].substring(4), fi.getId());// ====================================dsFiles={t_bd_zlfxbg=ZZVQ442304XWS8MFD18829}
                    }
                    if (var.startsWith("$")) {// 纯数据源公式
                        // var==$ds______get(0,"colname")
                        String formula = var.replace("______", ".");
                        sourceDsgsMap.put(var, formula);//
                        jgDsgsMap.put(formula, null);//
                    }
                }
            }

            // 根据变量信息进行拆分记录
            Map<String, String> tdscolMap = new HashMap<String, String>();// 内容对应数据源
            List<String> tdsList = new ArrayList<String>();// 数据源列表信息
            Map<String, List<Map<String, String>>> tdsValList = new HashMap<String, List<Map<String, String>>>();
            for (String string : varList) {
                if (!string.startsWith("#") && !string.startsWith("@") && string.indexOf(".") != -1) {
                    String[] s = string.split("\\.");
                    if (map.containsKey(s[0])) {
                        ((Map) map.get(s[0])).put(s[1], "");
                    } else {
                        tabList.add(s[0].toUpperCase());// ====================================所有的表名[ACT_BUSS_USER_JBSJ]
                        Map<String, String> tmap = new HashMap<String, String>();
                        tmap.put(s[1], "");// ==============
                        map.put(s[0], tmap);// ============={ACT_BUSS_USER_JBSJ={JH=}}
                    }
                    textKeyList.add(string);
                } else {
                    map.put(string, "");

                    // 获取相关设置数据源的变量并整理
                    if (string.startsWith("tds_")) {// tds_数据源名称______条件内容______字段______formid
                        String[] ss = string.split("______");// tds_t_bd_zlfxbg______id__d____a__dataId______XMB______ZZVIFNUJ0121UV95WD0891
                        if (ss.length >= 4) {
                            String formid = ss[3], dataIndexId = "";
                            String tdsinfo = ss[0].substring(4) + "," + ss[1];// 数据源名称+条件===t_bd_zlfxbg,id__d____a__dataId
                            tdsinfo = tdsinfo.replace("__a__", "@");
                            tdsinfo = tdsinfo.replace("__1__", "|");
                            tdsinfo = tdsinfo.replace("__d__", "=");
                            tdsinfo = tdsinfo.replace("@dataid", dataId);
                            tdsinfo = tdsinfo.replace("@dataindexid", dataIndexId);
                            tdscolMap.put(tdsinfo + "____" + ss[2].toUpperCase(), string);// <数据源,条件____字段 , 变量>
                            if (!tdsList.contains(tdsinfo)) {
                                tdsList.add(tdsinfo);
                            }
                        }
                    }
                }

                // 记录图片相关数据，单独处理
                if (string.startsWith("@") && !string.startsWith("@picnamemark")) {
                    String zd = string.substring(5);
                    String[] zds = zd.split("____");
                    if (zds.length > 2) {
                        if (Coms.judgeLong(zds[0])) {
                            picWmap.put(string.substring(1), Integer.parseInt(zds[0]));
                        }
                        zd = zds[1] + "." + zds[2];
                        if (!tabList.contains(zds[1])) {
                            tabList.add(zds[1].toUpperCase());
                        }
                    } else {
                        zd = zd.replace("____", ".");
                        if (!tabList.contains(zds[0])) {
                            tabList.add(zds[0].toUpperCase());
                        }
                    }
                    picKeyId.put(string.substring(1), zd);// ========================={pic_ACT_BUSS_USER_tp____tp1=ACT_BUSS_USER_tp.tp1}
                }
                // 签名
//				if (string.startsWith("@picnamemark")) {
//					if (signatures != null && signatures.size() > 0) {
//						namemarkKeyId.put(string.substring(1), Coms.listToString(signatures));
//					}
//				}
            }

            // 获取数据
//			List<String> idList = new ArrayList<String>();
//			idList.add(dataId);
            Map<String, String> valmap = new HashMap<String, String>();
//			if (!idList.isEmpty()) {
//				Map<String, Map<String, String>> dmap = dataService.getSysTplData(idList, null);
//				// 按顺序加载值
//				for (String tid : idList) {
//					Map<String, String> _tmap = dmap.get(tid);
//					if (_tmap != null) {
//						// 接口key添加了formalias，这里暂时不支持，先屏蔽掉
//						Map<String, String> temp_map = new HashMap<String, String>();
//						for (String key : _tmap.keySet()) {
//							String val = _tmap.get(key);
//							String[] ks = key.split(".");
//							if (ks.length > 2) {
//								temp_map.put(ks[0] + "." + ks[1], val);
//							} else {
//								temp_map.put(key, val);
//							}
//						}
//						valmap.putAll(temp_map);
//					}
//				}
//			}

            // 查询图片对应数据，整理图片地址，多个图片等相关处理并记录、整理
            if (picKeyId.size() > 0) {
                for (String key : picKeyId.keySet()) {
                    String tc = picKeyId.get(key);
                    String colval = valmap.get(tc.toUpperCase());
                    if (colval == null || "".equals(colval)) {// 无图片
                        picKeyId.put(key, "");
                    } else if (colval.indexOf(",") == -1) {// 单个图片
                        picKeyId.put(key, colval);

                        picidList.add(colval);
                    } else {// 多个图片
                        picKeyId.put(key, colval);
                        picmap.put("{{@" + key + "}}", colval.split(",").length);

                        String[] cs = colval.split(",");
                        for (int i = 0, il = cs.length; i < il; i++) {
                            picidList.add(cs[i]);
                        }
                    }
                }
                if (picidList.size() > 0) {
                    changeValmap(rmap, picidList, picKeyId, picWmap);
                }
            }

            // 签名图片的处理
            if (namemarkKeyId.size() > 0) {
                List<String> markidList = new ArrayList<String>();
                Map<String, Integer> markWmap = new HashMap<String, Integer>();
                for (String key : namemarkKeyId.keySet()) {
                    String namemarkid = namemarkKeyId.get(key);
                    String wspan = namemarkid.replace("@picnamemark", "");
                    if (Coms.judgeLong(wspan)) {
                        markWmap.put(key, Integer.parseInt(wspan));
                    }
                    if (namemarkid == null || "".equals(namemarkid)) {// 无签名
                        namemarkKeyId.put(key, "");
                    } else if (namemarkid.indexOf(",") == -1) {// 单个图片
                        namemarkKeyId.put(key, namemarkid);
                        markidList.add(namemarkid);
                    } else {// 多个图片
                        namemarkKeyId.put(key, namemarkid);
                        picmap.put("{{@" + key + "}}", namemarkid.split(",").length);// 加入到图片判断中，其他变量整合到图片中，导出时一并处理

                        String[] cs = namemarkid.split(",");
                        for (int i = 0, il = cs.length; i < il; i++) {
                            markidList.add(cs[i]);
                        }
                    }
                }
                if (markidList.size() > 0) {
                    changeValmap(rmap, markidList, namemarkKeyId, markWmap);

                }
            }

            // 换行字符处理
            if (textKeyList.size() > 0) {
                for (String key : textKeyList) {
                    String colval = valmap.get(key.toUpperCase());
                    if (colval == null || "".equals(colval)) {// 无内容
//						textKeyId.put(key, "");
                    } else if (colval.indexOf("\n") == -1) {// 文字无换行
//						textKeyId.put(key, colval);
//						valmap.put(key.toUpperCase(), colval.replace("\n", "\r\n"));
                    } else {// 文字换行
                        List<String> txtList = Coms.StrToList(colval, "\n");
                        textKeyId.put(key, txtList);
                        textmap.put("{{" + key + "}}", txtList.size());
                    }
                }
                if (textmap.size() > 0) {
                    changeValmapTxt(rmap, textKeyId);
                }
            }

            // 单元格数据源获取内容
            Map<String, List<Map<String, Object>>> dsFileMap = new HashMap<String, List<Map<String, Object>>>();// word模板id对应数据源列表值对象
            Map<String, List<List<Map<String, String>>>> dsvalMap = new HashMap<String, List<List<Map<String, String>>>>();
            if (tdsList != null && tdsList.size() > 0) {// tds_数据源名称______条件内容______字段______formid
                // 循环解析数据源，将值整理出针对文件的最终结果
                for (String string : tdsList) {
                    String[] ss = string.split(",");// 数据源名称,条件内容
                    String dsstr = ss[0];
                    String para = "";
                    if (ss.length > 1)
                        para = ss[1];
                    if (para.length() > 0) {
                        para = para + (param != null && param.length() > 0 ? "|" + param : "");
                    } else {
                        para = param;
                    }
                    List<Map<String, String>> data = getDsValCols(dsstr, para);
                    if (data.size() > 0) {
                        List<Map<String, String>> newdata = new ArrayList<Map<String, String>>();
                        for (Map<String, String> _map : data) {
                            Map<String, String> _nmap = new HashMap<String, String>();
                            if (_map != null) {
                                for (String key : _map.keySet()) {
                                    String val = _map.get(key);
                                    String newKey = tdscolMap.get(string + "____" + key.toUpperCase());
                                    _nmap.put(newKey, val);
                                }
                            }
                            newdata.add(_nmap);
                        }

                        String dsFileId = dsFiles.get(dsstr);
                        if (dsvalMap.containsKey(dsFileId)) {
                            dsvalMap.get(dsFileId).add(newdata);
                        } else {
                            List<List<Map<String, String>>> _list = new ArrayList<List<Map<String, String>>>();
                            _list.add(newdata);
                            dsvalMap.put(dsFileId, _list);
                        }
                    }
                }
                // 按文件循环，包括的数据源结果整理
                for (String dsFileId : dsvalMap.keySet()) {
                    List<List<Map<String, String>>> _list = dsvalMap.get(dsFileId);
                    // 获取同一文件中包括所有数据源结果的最大行数
                    int maxlen = 0;
                    for (List<Map<String, String>> vlist : _list) {
                        if (vlist.size() > maxlen) {
                            maxlen = vlist.size();
                        }
                    }

                    List<Map<String, Object>> fileDsVallist = new ArrayList<Map<String, Object>>();
                    for (int i = 0; i < maxlen; i++) {
                        Map<String, Object> _map = new HashMap<String, Object>();
                        for (List<Map<String, String>> vlist : _list) {
                            if (vlist.size() > i) {
                                _map.putAll(vlist.get(i));
                            }
                        }
                        fileDsVallist.add(_map);
                    }
                    dsFileMap.put(dsFileId, fileDsVallist);
                }
            }

            for (String key : map.keySet()) {
                Object val = map.get(key);
                if (val instanceof Map) {
                    Map<String, String> tmap = (Map<String, String>) map.get(key);
                    for (String key2 : tmap.keySet()) {
                        String newKey = key.toUpperCase() + "." + key2.toUpperCase();
//						Map<String, String> _tmap = kvmap.get(newKey);
                        if (valmap.containsKey(newKey)) {
                            String v = valmap.get(newKey);
                            v = v == null ? "" : v;
                            if (v.startsWith("[],")) {
                                v = v.replace("[],", "");
//								v = replaceStrKeyVal(v, _tmap);
                            } else if (v.startsWith("[]")) {
                                v = v.replace("[]", "");
//								v = replaceStrKeyVal(v, _tmap);
                            } else if (v.startsWith("[") && v.endsWith("]")) {
                                StringBuffer sb = new StringBuffer();
                                JSONArray jarr = JSONArray.parseArray(v);
                                if (jarr != null && jarr.size() > 0) {
                                    for (int i = 0, il = jarr.size(); i < il; i++) {
                                        if (i > 0) {
                                            sb.append(",");
                                        }
                                        sb.append(jarr.getString(i));
//										sb.append(replaceKeyVal(jarr.getString(i), _tmap));
                                    }
                                }
                                if (sb.length() > 0)
                                    v = sb.toString();
                            } else {
//								if (_tmap != null && _tmap.get(v) != null) {
//									v = _tmap.get(v);
//								}
                            }
                            tmap.put(key2, v);
                        }
                    }
                    if (rmap.containsKey(key) && StringUtils.isNotEmpty(tmap)) {
                        ((Map) rmap.get(key)).putAll(tmap);
                    } else {
                        rmap.put(key, tmap);
                    }
                } else {
                    if (key.startsWith("#")) {
                        Double allWidth = 18.8d;
                        List<List<String>> data = null;
                        double[] colWidths = null;
                        List<List<WordTilte>> titleList = new ArrayList<List<WordTilte>>();
                        List<Integer> combineColList = new ArrayList<Integer>();
                        if (key.startsWith("#tds_")) {
                            try {
//								String tdataId = dataId;
                                String dsstr = key.replace("#tds_", "");
                                String[] dss = dsstr.split("______");// 参数分割字符
                                String para = dss[0];
                                dsstr = dss[1];
                                String dataIndexId = "";
                                if ("".equals(para)) {
                                    para = "FORM_DATA_INDEX_ID=" + dataId;
                                } else {
                                    // 因参数只支持字符、数值、下划线，对特殊字符进行替换，否则被组件判定为不规范，根据模板导出时，无法生成。接收时做转换
                                    para = para.replace("__a__", "@");
                                    para = para.replace("__1__", "|");
                                    para = para.replace("__d__", "=");
                                    para = para.replace("@dataid", dataId);
                                    para = para.replace("@dataindexid", dataIndexId);
                                }
                                List<Double> wlist = new ArrayList<Double>();
                                List<Double> tlist = new ArrayList<Double>();
                                tlist.add(null);

                                if (!StringUtils.isEmpty(param) && param.indexOf("=") != -1) {
                                    if (para.length() > 0) {
                                        para = para + "|" + param;
                                    } else {
                                        para = param;
                                    }
                                }

                                data = getDsVal(dsstr, para, wlist, tlist, titleList, combineColList);
                                if (tlist.get(0) != null) {
                                    allWidth = tlist.get(0);
                                }
                                if (wlist.size() > 0) {
                                    colWidths = new double[wlist.size()];
                                    for (int j = 0; j < wlist.size(); j++) {
                                        colWidths[j] = wlist.get(j);
                                    }
                                }
                            } catch (Exception e) {
                                log.info("数据源解析出错，" + key);
                            }
                        }
                        if (data == null) {
                            rmap.put(key.substring(1), null);
                        } else {
                            rmap.put(key.substring(1),
                                    pt.getTabObj(data, false, 2, null, colWidths, allWidth, titleList, combineColList));
                        }
                        titleList = null;
                    } else {
                        if (valmap.containsKey(key.toUpperCase())) {
                            String v = valmap.get(key.toUpperCase());
                            v = v == null ? "" : v;
                            rmap.put(key, v);
                        }
                    }
                }
            }

            // 纯数据源公式解析
            if (StringUtils.isNotEmpty(sourceDsgsMap)) {
                TDataSourceManager tsm = new TDataSourceManager();
                List<String> dsList = new ArrayList<String>();// 分析数据源
                HashMap<String, IDataSource> idsMap = new HashMap<String, IDataSource>();
                for (String gs : jgDsgsMap.keySet()) {
                    List<String> _dsList = tsm.parseDataSourceAliasList(gs);// 分析数据源
                    for (String ds : _dsList) {
                        if (!dsList.contains(ds)) {
                            dsList.add(ds);
                            IDataSource ids = tsm.getDataSource(ds);
                            if (ids != null) {
                                ids.parseInParaDefaultValue();
                                if (StringUtils.isNotEmpty(pmap)) {
                                    for (String inparaKey : pmap.keySet()) {
                                        TInPara inPara = ids.getInParaByAlias(inparaKey);
                                        if (inPara != null) {
                                            ids.setInParaByAlias(inparaKey, pmap.get(inparaKey));
                                        }
                                    }
                                }
                                ids.load();
                                idsMap.put(ds, ids);
                            }
                        }
                    }
                }
                for (String gs : jgDsgsMap.keySet()) {
                    Object jg = tsm.getScriptValue(gs, pmap, idsMap);
                    List<String> tempList = null;
                    String val = "";
                    if (jg != null) {
                        if (jg instanceof List) {
                            List<String> _list = (List<String>) jg;
                            if (_list.size() > 0) {
                                if (_list.size() > 1) {
                                    dsvalmap.put("{{" + gs + "}}", _list.size());
                                    tempList = _list;
                                } else {
                                    val = _list.get(0);
                                }
                            }
                        } else {
                            val = String.valueOf(jg);
                        }
                    }
//					//测试数据
//					if(gs.startsWith("$test_word_export")) {
//						List<String> _list = new ArrayList<String>();
//						_list.add("每家企业现场诊断服务不少于3次，每次不少于3小时。现场诊断内容应包括且不限于访谈交流、技术诊断、方案沟通、主题培训、向企业高层作诊断结果汇报等。");
//						_list.add("bbbbbbbb");
//						_list.add("cccccccc");
//						tempList = _list;
//						dsvalmap.put("{{" + gs + "}}", _list.size());
//
////						NumberingBuilder nb = Numberings.ofDecimalParentheses();
////						for (String s : _list) {
////							nb.addItem(s);
////						}
////						rmap.put("list_test", nb.create());
//					}
                    for (String key : sourceDsgsMap.keySet()) {
                        String value = String.valueOf(sourceDsgsMap.get(key));
                        if (gs.equals(value)) {
                            sourceDsgsMap.put(key, tempList != null ? tempList : val);
                            rebuildMap(sourceDsgsMap, key);
                            break;
                        }
                    }
                }
            }

            // 第二次循环，赋值并生成word并合并
            // 如果有多个图片，需要调整原word模板文件，另存文件到服务器上，用完之后删除
            List<String> tempWordList = null;
            for (SysFilesInfo fi : fileList) {
                // 根据图片是否多个进行判断，调整模板内容，将{{@pic}}改为多个{{@pic______1}} {{@pic______2}}等等（前期map需要调整）
                // 处理特殊word表格，如果为一条记录一个表，多条记录时，循环处理
                File file = new File(fi.getFileAddress());

                List<Map<String, Object>> vmaplist = dsFileMap.get(fi.getId());
                if (vmaplist != null && vmaplist.size() > 1) {
                    for (int i = 0, il = vmaplist.size(); i < il; i++) {
                        Map<String, Object> tempmap = copyMap(rmap);
                        tempmap.putAll(vmaplist.get(i));
                        if (StringUtils.isNotEmpty(sourceDsgsMap)) {
                            combineMap(tempmap, sourceDsgsMap);
//							tempmap.putAll(sourceDsgsMap);
                        }
                        if (picmap.isEmpty() && textmap.isEmpty() && StringUtils.isEmpty(dsvalmap)) {// 无图片或文字换行
                            tplList.add(pt.getWord(file, tempmap));
                        } else {
                            if (tempWordList == null) {
                                tempWordList = new ArrayList<String>();
                            }
                            File tempFile = file;
                            if (!picmap.isEmpty()) {
                                File newFile = changeWordTpl(file, picmap, tempWordList);
                                tplList.add(pt.getWord(newFile, tempmap));
                                tempFile = newFile;
                            }
                            if (!textmap.isEmpty()) {
                                File newFile = changeWordTplTxt(tempFile, textmap, tempWordList);
                                tplList.add(pt.getWord(newFile, tempmap));
                            }
                            if (StringUtils.isNotEmpty(dsvalmap)) {
                                File newFile = changeWordTplTxt(tempFile, dsvalmap, tempWordList);
                                tplList.add(pt.getWord(newFile, tempmap));
                            }
                        }
                    }
                } else {
                    Map<String, Object> tempmap = copyMap(rmap);
                    if (vmaplist != null && vmaplist.size() == 1) {
                        tempmap.putAll(vmaplist.get(0));
                    }
                    if (StringUtils.isNotEmpty(sourceDsgsMap)) {
                        combineMap(tempmap, sourceDsgsMap);
//						tempmap.putAll(sourceDsgsMap);
                    }
                    if (picmap.isEmpty() && textmap.isEmpty() && StringUtils.isEmpty(dsvalmap)) {// 无图片或文字换行
                        tplList.add(pt.getWord(file, tempmap));
                    } else {
                        if (tempWordList == null) {
                            tempWordList = new ArrayList<String>();
                        }
                        File tempFile = file;
                        if (!picmap.isEmpty()) {
                            File newFile = changeWordTpl(file, picmap, tempWordList);
                            tplList.add(pt.getWord(newFile, tempmap));
                            tempFile = newFile;
                        }
                        if (!textmap.isEmpty()) {
                            File newFile = changeWordTplTxt(tempFile, textmap, tempWordList);
                            tplList.add(pt.getWord(newFile, tempmap));
                        }
                        if (StringUtils.isNotEmpty(dsvalmap)) {
                            File newFile = changeWordTplTxt(tempFile, dsvalmap, tempWordList);
                            tplList.add(pt.getWord(newFile, tempmap));
                        }
                    }
                }

            }

            List<NiceXWPFDocument> nxdList = insertWordCom(tplList, valmap);

            tpl = pt.combineWordNXD(nxdList);// 合并多个word模板

            // 判断字符串并生成目录
            boolean pause = false;
            for (XWPFParagraph p : tpl.getParagraphs()) {
                for (XWPFRun r : p.getRuns()) {
                    int pos = r.getTextPosition();
                    String text = r.getText(pos);
                    if (text != null && text.contains(this.ttocString)) {
                        text = text.replace(this.ttocString, "");
                        r.setText(text, 0);
                        addField(p, " TOC \\o \"1-3\" \\h \\z \\u ");
//                        addField(p, "TOC \\h");
                        pause = true;
                        break;
                    }
                }
                if (pause) {
                    break;
                }
            }

            map = null;
            rmap = null;
//			idList = null;
            picmap = null;
            picKeyId = null;
            picidList = null;
            namemarkKeyId = null;
            tplList = null;
        }
        return tpl;
    }

    /**
     * 下载文件
     *
     * @param fileId   文件id
     * @param response
     */
    public void downloadFile(String fileId, HttpServletResponse response) {
        SysFilesInfo fileInfo = this.getFileById(fileId);
        downloadFile(fileInfo, response);
    }

    /**
     * 下载文件
     *
     * @param fileId   文件id
     * @param response
     */
    public void downloadFile(SysFilesInfo fileInfo, HttpServletResponse response) {
        InputStream fileSteam = fs.getFileSteam(fileInfo);
        byte[] buffer = new byte[1024];
        int len;
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileInfo.getOldFileName(), "UTF-8"));
            response.setHeader("content-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            while ((len = fileSteam.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
        } catch (Exception e) {
        }
        try {
            if (outputStream != null) {
                outputStream.close();
            }
        } catch (Exception e) {
        }
        try {
            fileSteam.close();
        } catch (Exception e) {
        }
    }

    /**
     * 下载文件
     *
     * @param fileUrl  文件url
     * @param response
     */
    @Override
    public void downloadFileByUrl(String fileUrl, HttpServletResponse response) {
        SysFilesInfo fileInfo = this.getFileInfoByUrl(fileUrl);
        if (fileInfo != null) {
            downloadFile(fileInfo, response);
        }
    }

    /**
     * 删除文件
     *
     * @param fileId 文件id
     */
    public boolean deleteFile(String fileId) {
        SysFilesInfo fileInfo = getFilesInfoById(fileId);
        if (fileInfo != null) {
            fs.deleteFile(fileInfo);
            entityService.deleteById(fileInfo);
        }
        return true;
    }

    /**
     * 删除文件
     *
     * @param fileUrl 文件url
     */
    @Override
    public boolean deleteFileByUrl(String fileUrl) {
        SysFilesInfo fileInfo = getFileInfoByUrl(fileUrl);
        if (fileInfo != null) {
            fs.deleteFile(fileInfo);
            entityService.deleteById(fileInfo);
        }
        return true;
    }

    /**
     * 批量删除文件
     *
     * @param ids 文件id
     */
    public int deleteFile(String[] ids) {
        int result = 0;
        for (String id : ids) {
            if (deleteFile(id)) {
                result++;
            }
        }
        return result;
    }

    public InputStream getFileSteam(String id) {
        SysFilesInfo fileInfo = getFilesInfoById(id);
        return fs.getFileSteam(fileInfo);
    }

    /**
     * 上传本地文件到文件服务器(同步历史数据专用)
     *
     * @return
     */
    public boolean uploadLocalFilesToFileSystem() {

        List<SysFilesInfo> list = entityService.queryData(SysFilesInfo.class,
                Where.create().notEmpty(SysFilesInfo::getFileAddress), null, null);
        if (StringUtils.isEmpty(list)) {
            return false;
        }
        List<SysFilesInfo> resultList = list.stream().filter(item -> item.getLocalUploadFlag() == 0)
                .collect(Collectors.toList());
        boolean res = fs.uploadLocalFilesToFileSystem(resultList);
        if (res) {
            List<SysFilesInfo> updateList = resultList.stream().filter(item -> item.getLocalUploadFlag() == 1)
                    .collect(Collectors.toList());
            if (StringUtils.isNotEmpty(updateList)) {
                entityService.updateBatch(updateList);
            }
        }
        return res;
    }

    private String getDiskFileRootPath() {
        String addr = sysConfigService.getSysConfig("file_path");
        if (addr == null) {
            addr = "d:/tm4UploadFiles";
        }
        return addr;
    }

    private String getTempFileDirDiskAdress() {
        String root = getDiskFileRootPath();
        String dirName = root + "/temp";
        File dir = new File(dirName);
        if (!dir.exists() || !dir.isDirectory()) {
            dir.mkdirs();
        }
        return dirName;
    }

    /**
     * 获取缓存文件地址
     *
     * @param fileInfo
     * @return
     */
    private String getTempFileDiskAdress(SysFilesInfo fileInfo) {
        String dirName = getTempFileDirDiskAdress();
        return dirName + "/temp" + System.currentTimeMillis() + "_" + fileInfo.getOldFileName();
    }

    private FileInputStream getTempFileInputStream(SysFilesInfo fileInfo) {
//		String tempFileDiskAdress = getTempFileDiskAdress(fileInfo);
//		File file = new File(tempFileDiskAdress);
//		if (!file.exists()) {
//			file.mkdir();
//		}
        InputStream fileStream = fs.getFileSteam(fileInfo);
        FileOutputStream fos = null;
        try {
            return (FileInputStream) fileStream;
        } catch (Exception e) {

        } finally {
            try {
                if (fileStream != null) {
                    fileStream.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (Exception e) {
            }
        }
        return null;
    }

    /**
     * 创建本地缓存文件
     *
     * @param fileInfo
     * @return
     */
    private File createTempFileInDisk(SysFilesInfo fileInfo) {
        String tempFileDiskAdress = getTempFileDiskAdress(fileInfo);
        File file = new File(tempFileDiskAdress);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
            }
        }
        InputStream fileSteam = fs.getFileSteam(fileInfo);
        FileOutputStream fos = null;

        try {
            fos = new FileOutputStream(file);
            IOUtils.copy(fileSteam, fos);
        } catch (Exception e) {

        } finally {
            try {
                if (fileSteam != null) {
                    fileSteam.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (Exception e) {
            }
        }
        return file;
    }

    private byte[] getImageFileBytes(SysFilesInfo fileInfo) {
        InputStream inputStream = fs.getFileSteam(fileInfo);
        if (inputStream == null) {
            return null;
        }
//		// 创建流
//		BufferedImage bufferImg;
//		ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();

        byte[] buff = new byte[8000];
        int bytesRead = 0;
        ByteArrayOutputStream bao = new ByteArrayOutputStream();
        try {
            while ((bytesRead = inputStream.read(buff)) != -1) {
                bao.write(buff, 0, bytesRead);
            }
//			bufferImg = bufferImgImageIO.read(new File(imgAddress));
//			ImageIO.write(bufferImg, "jpg", byteArrayOut);
        } catch (IOException e) {
        }
        return bao.toByteArray();
    }

    /**
     * 预览
     *
     * @param fileUrl
     * @return
     */
    public void filePreview(String fileUrl, HttpServletResponse response) {
        SysFilesInfo fileInfo = getFileInfoByUrl(fileUrl);
        if (fileInfo == null) {
            return;
        }
        fs.filePreview(fileInfo, response);
    }

    @Override
    public SysFilesInfo getFileInfoByUrl(String fileUrl) {
//		SysUser currentUser = SysUserHolder.getCurrentUser();
        List<SysFilesInfo> list;
//		if (currentUser != null) {
//			list = entityService.queryData(SysFilesInfo.class, Where.create().eq(SysFilesInfo::getFileUrl, fileUrl),
//					null, null);
//		} else {
        list = entityService.queryDataDisableTenant(SysFilesInfo.class,
                Where.create().eq(SysFilesInfo::getFileUrl, fileUrl), null, null);
//		}
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<SysFilesInfo> getFilesByModule(String moudleCode) {
        if (StringUtils.isEmpty(moudleCode)) {
            return null;
        }
        return entityService.queryData(SysFilesInfo.class, Where.create().like(SysFilesInfo::getSourceModule, moudleCode),
                Order.create().orderByAsc(SysFilesInfo::getCreateTime), null);
    }

    @Override
    public List<SysFilesInfo> getUrlPathFiles(String urlPath) {
        if (StringUtils.isEmpty(urlPath)) {
            return null;
        }
        if (!urlPath.endsWith("/")) {
            urlPath += "/";
        }
        return entityService.queryData(SysFilesInfo.class, Where.create().like(SysFilesInfo::getFileUrl, urlPath),
                Order.create().orderByAsc(SysFilesInfo::getCreateTime), null);
    }

    /**
     * -------------------------------------------------------------------------------------------------------------------------------------------------------------------------
     */

    /**
     * @param response
     * @param document
     * @category 导出文件
     */
    public void outputWordFile(HttpServletResponse response, NiceXWPFDocument document) {
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        OutputStream out;
        try {
            out = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(out);
            document.write(bos);
            bos.flush();
            out.flush();
            PoitlIOUtils.closeQuietlyMulti(document, bos, out);
            document.close();
            bos.close();
            out.close();
        } catch (Exception e) {
            log.error("", e);
        }
    }

}
