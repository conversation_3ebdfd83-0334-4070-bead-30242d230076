package com.yunhesoft.system.tools.todo.service.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.ClassExecute;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.menu.entity.po.SysModule;
import com.yunhesoft.system.menu.service.ISysMenuLibInitService;
import com.yunhesoft.system.menu.service.ISysMenuLibService;
import com.yunhesoft.system.tools.sysConfig.service.impl.SysConfigServiceImpl;
import com.yunhesoft.system.tools.todo.entity.dto.ToDoApiDto;
import com.yunhesoft.system.tools.todo.entity.po.SysTodoInfo;
import com.yunhesoft.system.tools.todo.entity.po.SysTodoModule;
import com.yunhesoft.system.tools.todo.entity.vo.ToDoApiVo;
import com.yunhesoft.system.tools.todo.entity.vo.ToDoVo;
import com.yunhesoft.system.tools.todo.service.TodoService;
import com.yunhesoft.system.tools.todo.util.TodoNumberType;

import lombok.extern.log4j.Log4j2;

/**
 * 待办服务实现类
 * 
 * <AUTHOR>
 * @date 2021/10/10 10:25
 */
@Service
@Log4j2
public class TodoServiceImpl implements TodoService {

	@Autowired
	private RedisUtil redis; // redis实例

	@Autowired
	private AuthService authServ; // 用户对象服务

	@Autowired
	private EntityService entityService; // 数据库操作

	private static String RED_KEY = "SYSTODO";// 待办key值

	@Autowired
	private ISysMenuLibInitService menuLibInitServ; // 模块初始化

	@Autowired
	private ISysMenuLibService menuLibServ;
	
	@Autowired
	private SysConfigServiceImpl configService;// 系统配置信息
	
    @Resource(name = "CustomRestTemplate")
	private RestTemplate restTemplate;
	
	private String getRedKey() {
		if (MultiTenantUtils.enalbe()) {// 多租户模式
			return RED_KEY + ":" + MultiTenantUtils.getTenantId();
		} else {
			return RED_KEY;
		}
	}

	/**
	 * 获取当前用户待办数据量
	 * 
	 * @return
	 */
	@Override
	public int getTodoCount() {
		return getTodoCount("");
	}

	/**
	 * 获取用户待办数量
	 * 
	 * @param loginName 登录账号
	 * @return
	 */
	@Override
	public int getTodoCount(String loginName) {
		return this.getTodoCount(getUser(loginName)); // 计算待办数量
	}

	/**
	 * 获取当前用户未读数据量
	 * 
	 * @return
	 */
	@Override
	public int getUnReadCount() {
		return getUnReadCount("");
	}

	/**
	 * 获取用户未读数量
	 * 
	 * @param loginName 登录账号
	 * @return
	 */
	@Override
	public int getUnReadCount(String loginName) {
		return this.getTodoNumber(getUser(loginName), TodoNumberType.UNREAD); // 计算待办数量
	}

	/**
	 * 获取用户对象
	 * 
	 * @param loginName
	 * @return
	 */
	private SysUser getUser(String loginName) {
		SysUser currentUser = SysUserHolder.getCurrentUser();
		if (StringUtils.isEmpty(loginName)) {// 当前用户
			return currentUser;
		} else {
			if (currentUser == null || !loginName.equals(currentUser.getUserName())) {
				return authServ.getUserByLoginName(loginName);
			} else {
				return currentUser;
			}
		}
	}

	/**
	 * 获取待办数量
	 * 
	 * @param user 用户对象
	 * @return
	 */
	private int getTodoCount(SysUser user) {
		return getTodoNumber(user, TodoNumberType.TODO);
	}

	/**
	 * 获取待办数量
	 * 
	 * @param user 用户对象
	 * @param type 待办数量类型：待办数量、未读数量等
	 * @return
	 */
	private int getTodoNumber(SysUser user, TodoNumberType type) {
		int count = 0;
		if (user != null) {
			if (type == null) {
				type = TodoNumberType.TODO;// 默认获取待办数量
			}

			List<ToDoVo> listTodo = getTodoSetting();
			if (StringUtils.isNotEmpty(listTodo)) {
				Map<String, String> mapPara = this.paraMapInit(user);
				for (ToDoVo bean : listTodo) {
					int c = 0;
					// 判断获取某个类型的待办数量
					if (TodoNumberType.TODO.equals(type)) {// 待办数量
						c = calTodoCount(user, bean, mapPara);// 计算待办数量
					} else if (TodoNumberType.UNREAD.equals(type)) {// 未读数量
						c = calTodoNumber(user, bean, mapPara, TodoNumberType.UNREAD);
					}
					count = count + c;
				}
			}
			if (TodoNumberType.TODO.equals(type)) {// tm3待办数量
				List<ToDoVo> tm3TodoList = getTm3TodoList(user);
				if (StringUtils.isNotEmpty(tm3TodoList)) {
					for(ToDoVo temp:tm3TodoList) {
						count = count + temp.getTodoCount();
					}
				}
			}
		}
		return count;
	}

	/**
	 * 计算待办数量
	 * 
	 * @param bean
	 */
	private int calTodoCount(SysUser user, ToDoVo bean, Map<String, String> mapPara) {
		return calTodoNumber(user, bean, mapPara, TodoNumberType.TODO);// 获取待办数量
	}

	/**
	 * 计算待办数据（例如：待办数、未读数等）
	 * 
	 * @param user    user对象
	 * @param bean    传入的bean对象
	 * @param mapPara 变量解析
	 * @param type    待办数量类型：待办数量、未读数量等
	 * @return
	 */
	private int calTodoNumber(SysUser user, ToDoVo bean, Map<String, String> mapPara, TodoNumberType type) {
		int count = 0;
		if (user != null && StringUtils.isNotEmpty(user.getUserName())) {

			if (type == null) {
				type = TodoNumberType.TODO;// 默认获取待办数量
			}

			// String key = REDKEY + ":" + type.getKey() + ":" + bean.getModuleCode() + ":"
			// + bean.getFuncCode() + ":" + bean.getTodoCode();
			StringBuffer keybuff = new StringBuffer();
			keybuff.append(getRedKey());
			keybuff.append(":");
			keybuff.append(type.getKey());
			keybuff.append(":");
			keybuff.append(bean.getModuleCode());
			keybuff.append(":");
			keybuff.append(bean.getFuncCode());
			keybuff.append(":");
			keybuff.append(bean.getTodoCode());
			String key = keybuff.toString();

			Integer c = getTodoCountFromRedis(key, user.getUserName());// 从redis 中获取
			if (c == null) {// 计算待办数
				String execContent = null;
				Integer execType = null;
				// 判断获取某个类型的待办数量
				if (TodoNumberType.TODO.equals(type)) {// 待办数量
					execType = bean.getExecType();
					execContent = bean.getExecContent();
				} else if (TodoNumberType.UNREAD.equals(type)) {// 未读数量
					execType = bean.getExecTypeUnRead();
					execContent = bean.getExecContentUnRead();
				}

				// 计算
				if (execContent != null && execContent.length() > 0) {
					if (execType == null || execType == 1) {// java类 class
						execContent = replaceVar(execContent, mapPara);// 替换变量
						count = this.execClass(bean, execContent);
					} else if(execType == 2){ //REST API
						//REST API方式访问各业务模块微服务取待办数
						execContent = replaceVar(execContent, mapPara);// 替换变量
						count = this.execRestApiGetTodoCount(bean, execContent);
					} else {// sql语句
						execContent = replaceVar(execContent, mapPara);// 替换变量
						count = this.execSql(execContent);
					}

					// 判断获取某个类型的待办数量
					if (TodoNumberType.TODO.equals(type)) {// 待办数量
						bean.setExecContent(execContent);
					} else if (TodoNumberType.UNREAD.equals(type)) {// 未读数量
						bean.setExecContentUnRead(execContent);
					}
				}
				setTodoCountToRedis(user.getUserName(), key, count);
			} else {
				count = c;
			}
			// 判断获取某个类型的待办数量
			if (TodoNumberType.TODO.equals(type)) {// 待办数量
				bean.setTodoCount(count);
			} else if (TodoNumberType.UNREAD.equals(type)) {// 未读数量
				bean.setTodoCountUnRead(count);
			}
		}
		return count;
	}

	/**
	 * 获取执行的java类变量名
	 * 
	 * @param className
	 * @return
	 */
//	private String getClassVarName(String className) {
//		className = className.replace("com.yunhesoft.", "");
//		className = className.replaceAll("\\.", "_");
//		className = StringUtils.toCamelCase(className); // 转换为驼峰模式
//		return className;
//	}

	/**
	 * 替换变量初始化
	 */
	private Map<String, String> paraMapInit(SysUser user) {
		Map<String, String> mapPara = new HashMap<String, String>();
		if (user != null) {
			mapPara.put("loginName", user.getUserName());// 登录名
			mapPara.put("userId", user.getId());// 用户id
			mapPara.put("zyid", user.getId());
			mapPara.put("gwid", user.getPostId());// 岗位id
			// mapPara.put("gwzzdm", String.valueOf(user.getGw().getZzdm()));
			// mapPara.put("myzzdm", String.valueOf(user.getMyOrg().getZzdm()));
			// if (user.getEno() != null && user.getEno().length() > 0) {
			// mapPara.put("workid", user.getEno());// 工号
			// }
			// if (user.getCardid() != null && user.getCardid().length() > 0) {
			// mapPara.put("cardid", user.getCardid());// 身份证号
			// }
		}
		return mapPara;
	}

	/**
	 * 替换变量
	 * 
	 * @param s
	 * @return
	 */
	private String replaceVar(String s, Map<String, String> mp) {
		String sign = "'";
		String str = s;
		try {
			if (StringUtils.isNotEmpty(str) && str.indexOf("@") >= 0) {
				for (String key : mp.keySet()) {
					String v = mp.get(key);
					if (StringUtils.isNotEmpty(sign)) {
						v = sign + v + sign;
					}
					str = str.replaceAll("@" + key, v);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		if (str != null) {
			return str.trim();
		} else {
			return "";
		}
	}

	/**
	 * sql语句计算待办数量
	 * 
	 * @param sql
	 * @return
	 */
	private int execSql(String sql) {
		int c = 0;
		Connection conn = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		if (StringUtils.isNotEmpty(sql)) {
			try {
				conn = connection();
				stmt = conn.prepareStatement(sql);
				rs = stmt.executeQuery();
				if (rs != null && rs.next()) {
					Integer i = rs.getInt(1);
					if (i != null) {
						c = i;
					}
				}
			} catch (Exception e) {
				log.error("待办语句出错," + sql, e);
			} finally {
				if (rs != null) {
					try {
						rs.close();
					} catch (SQLException e) {
					}
				}
				try {
					if (stmt != null && !stmt.isClosed()) {
						stmt.close();
					}
				} catch (SQLException e) {
				}
				disconnection(conn);
			}
		}
		return c;
	};

	/**
	 * 打开数据库连接
	 */
	private Connection connection() {
		return SpringUtils.getBean(EntityService.class).getConnection();
	}

	/**
	 * 关闭数据库连接
	 */
	private void disconnection(Connection conn) {
		SpringUtils.getBean(EntityService.class).closeConnection(conn);
	}

	/**
	 * java类 计算待办数量
	 * 
	 * @param execContent
	 * @return
	 */
	private int execClass(ToDoVo bean, String execContent) {
		int c = 0;
		try {
			Object obj = null;
			if (bean.getExecType() == null || bean.getExecType() == 1) {// java类 class
				if (StringUtils.isNotEmpty(bean.getClassName())) {
					String className = bean.getClassName().trim();
					ClassExecute classExec = new ClassExecute();
					obj = classExec.execClassByName(className, execContent);
				}
			}
			if (obj != null) {
				if (obj instanceof Integer) {
					c = Integer.parseInt(obj.toString());
				} else if (obj instanceof Double) {
					Double temp = Double.parseDouble(obj.toString());
					c = temp.intValue();
				} else {
					c = Integer.parseInt(obj.toString());
				}
			}
		} catch (Exception e) {
			log.error("待办语句出错," + execContent, e);
		}
		return c;
	}

	/**
	 * 通过REST API方式请求其它业务模块微服务获取待办数
	 * @param bean
	 * @param execContent
	 * @return
	 */
	private int execRestApiGetTodoCount(ToDoVo bean, String execContent) {
		int todoCount = 0;

		ToDoApiVo res = execRestApi(bean, execContent);
		if (res != null) {
			Integer count = res.getTodoCount();
			todoCount = Optional.ofNullable(count).orElse(0);
		}

		return todoCount;
	}

	/**
	 * 通过REST API方式请求其它业务模块微服务获取待办数据
	 * @param bean
	 * @param execContent
	 * @return
	 */
	private ToDoApiVo execRestApi(ToDoVo bean, String execContent) {
		ToDoApiVo res = null;

		//解析参数
		ToDoApiDto dto = new ToDoApiDto();
		if(StringUtils.isNotEmpty(execContent)) {
			try {
				JSONObject paramObject = JSON.parseObject(execContent);
				Class<? extends ToDoApiDto> dtoClass = dto.getClass();
				for (String key : paramObject.keySet()) {
					try {
						dtoClass.getMethod("set" + key.substring(0, 1).toUpperCase() + key.substring(1), String.class).invoke(dto, paramObject.get(key));
					} catch (Exception e) {
						log.warn("REST API待办请求解析参数，填充属性值失败！参数key：" + key, e);
					}
                }
			} catch (Exception e) {
				log.warn("REST API待办请求解析参数失败！参数字符串：" + execContent);
			}
        }

		String serviceName = bean.getServiceName();
		String requestPath = bean.getRequestPath();
		try {
			String url = "http://" + serviceName + "/" + requestPath;
	
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			String currentToken = SysUserHolder.getCurrentToken();
			headers.set("Authorization", currentToken);
			HttpEntity<Object> entity = new HttpEntity<>(dto, headers);
			ParameterizedTypeReference<Res<ToDoApiVo>> responseType = new ParameterizedTypeReference<Res<ToDoApiVo>>() {
			};
			Res<ToDoApiVo> sysRes = restTemplate.exchange(url, HttpMethod.POST, entity, responseType).getBody();
	
			if (sysRes != null) {
				res = sysRes.getResult();
			}
		} catch (Exception e) {
			log.warn("REST API待办请求链接失败！链接地址参数字符串：" + serviceName+"/"+requestPath, e);
		}
		return res;
	}

	/**
	 * 从redis中获取待办数量
	 * 
	 * @param loginName
	 * @return
	 */
	private Integer getTodoCountFromRedis(String key, String loginName) {
		Integer c = null;
		try {
			c = redis.getMapValue(key.toUpperCase(), loginName);
		} catch (Exception e) {
			log.error("", e);
		}
		return c;
	}

	/**
	 * 待办数量存入redis
	 * 
	 * @param loginName
	 */
	private void setTodoCountToRedis(String loginName, String key, Integer count) {
		try {
			if (count == null) {
				redis.hDelete(key.toUpperCase(), loginName);
			} else {
				redis.setMapValue(key.toUpperCase(), loginName, count);
			}
		} catch (Exception e) {
			log.error("", e);
		}
	}

	/**
	 * 获取当前用户的待办列表
	 * 
	 * @return
	 */
	@Override
	public List<ToDoVo> getTodoList() {
		return this.getTodoList(SysUserHolder.getCurrentUser());
	}

	/**
	 * 获取待办列表
	 * 
	 * @param loginName 登录账号
	 * @return
	 */
	@Override
	public List<ToDoVo> getTodoList(String loginName) {
		SysUser user = getUser(loginName);
		return this.getTodoList(user);
	}

	/**
	 * 获取待办列表
	 * 
	 * @param user
	 * @return
	 */
	@Override
	public List<ToDoVo> getTodoList(SysUser user) {
		List<ToDoVo> listTodo = null;
		if (user != null) {
			listTodo = this.getTodoSetting();
			if (StringUtils.isNotEmpty(listTodo)) {
				Map<String, String> map = this.paraMapInit(user);
				Map<String, SysModule> moduleMap = menuLibServ.getMobuleMap();
				for (ToDoVo bean : listTodo) {
					if (moduleMap.containsKey(bean.getModuleCode())) {
						bean.setModuleName(moduleMap.get(bean.getModuleCode()).getModuleName());
					}
					if (menuLibInitServ.isModuleRegister(bean.getModuleCode())) {// 判断模块是否注册
						// calTodoCount(user, bean, map);// 计算待办数量
						for (TodoNumberType t : TodoNumberType.values()) {
							calTodoNumber(user, bean, map, t);// 计算数量
						}
					}
				}				
			}
			List<ToDoVo> tm3TodoList =  getTm3TodoList(user);//获取tm3待办
			if (StringUtils.isNotEmpty(tm3TodoList)) {
				if(listTodo==null) {
					listTodo = new ArrayList<ToDoVo>();
				}
				listTodo.addAll(tm3TodoList);//添加到待办列表
			}
		}
		//过滤有效数据
		List<ToDoVo> newlistTodo = new ArrayList<ToDoVo>();
		for (ToDoVo toDoVo : listTodo) {
			Integer todoCount = toDoVo.getTodoCount();
			if(todoCount!=null&&todoCount>0) {
				newlistTodo.add(toDoVo);
			}
		}
		return newlistTodo;
	}

	/**
	 * 获取待办的配置信息
	 * 
	 * @return
	 */
	private List<ToDoVo> getTodoSetting() {
		List<ToDoVo> listVo = getTodoFromRedis();// redis 获取
		if (listVo == null) {// 数据库中读取
			return getTodoFromDb();
		}
		return listVo;
	}

	/**
	 * @category 清理个人的所有的待办信息
	 * @param loginName
	 */
	@Override
	public void clearTodoCachedForUser(String loginName) {
		if (ObjUtils.isEmpty(loginName)) {
			return;
		}
		List<ToDoVo> listVo = getTodoSetting();
		for (ToDoVo todo : listVo) {
			String value = (todo.getModuleCode() + ":" + todo.getFuncCode() + ":" + todo.getTodoCode()).toUpperCase();
			String key = "SYSTODO:TODO:" + value;
			String key1 = "SYSTODO:UNREAD:" + value;
			redis.hDelete(key, loginName);
			redis.hDelete(key1, loginName);
		}
	}

	/**
	 * 清理列表中的用户的所有的待办信息
	 * 
	 * @param loginNames
	 */
	@Override
	public void clearTodoCachedForUsers(List<String> loginNames) {
		if (loginNames == null || loginNames.size() == 0) {
			return;
		}
		List<ToDoVo> listVo = getTodoSetting();
		for (ToDoVo todo : listVo) {
			String value = (todo.getModuleCode() + ":" + todo.getFuncCode() + ":" + todo.getTodoCode()).toUpperCase();
			String key = "SYSTODO:TODO:" + value;
			String key1 = "SYSTODO:UNREAD:" + value;
			for (String loginName : loginNames) {
				if (redis.hasKey(key)) {
					redis.hDelete(key, loginName);
				}
				if (redis.hasKey(key1)) {
					redis.hDelete(key1, loginName);
				}
			}
		}
	}

	/**
	 * 数据库中读取待办信息
	 * 
	 * @return
	 */
	private List<ToDoVo> getTodoFromDb() {
		List<ToDoVo> listVo = new ArrayList<ToDoVo>();
		List<SysTodoModule> listModule = getTodoModule(false);
		if (StringUtils.isNotEmpty(listModule)) {
			List<SysTodoInfo> listTodoInfo = getTodoInfo(false);
			if (StringUtils.isNotEmpty(listTodoInfo)) {
				Map<String, List<SysTodoInfo>> mapInfo = new HashMap<String, List<SysTodoInfo>>();
				for (SysTodoInfo sysTodoInfo : listTodoInfo) {
					if (sysTodoInfo.getTodoType() == null || sysTodoInfo.getTodoType() == 1) {// 1:待办；0：已办
						String key = sysTodoInfo.getModuleCode() + ":" + sysTodoInfo.getFuncCode();
						List<SysTodoInfo> listTemp = mapInfo.get(key);
						if (listTemp == null) {
							mapInfo.put(key, new ArrayList<SysTodoInfo>());
						}
						mapInfo.get(key).add(sysTodoInfo);
					}
				}
				for (SysTodoModule sysTodoModule : listModule) {
					String key = sysTodoModule.getModuleCode() + ":" + sysTodoModule.getFuncCode();
					List<SysTodoInfo> listInfo = mapInfo.get(key);
					if (StringUtils.isNotEmpty(listInfo)) {
						for (SysTodoInfo todoInfo : listInfo) {
							listVo.add(getTodoVo(sysTodoModule, todoInfo));
						}
					}

				}
			}
		}
		setTodoToRedis(listVo);
		return listVo;
	}

	/**
	 * 清除待办相关的redis缓存（慎用）
	 */
	@Override
	public void clearAllTodoCached() {
		log.info("正在清除待办缓存。。。");
		// 清除所有，不考虑多租户
		Collection<String> keys = redis.keys(RED_KEY + "*"); // redis.keys(getRedKey() + "*");
		redis.delete(keys);
		log.info("清除待办缓存完毕！！！");
	}

	/**
	 * 清除待办缓存(清除全部待办数量类型)
	 * 
	 * @param moduleCode 模块（微服务）编码
	 * @param funcCode   功能代码，必填项目，不允许为空
	 * @param todoCode   待办项代码，可选，允许为空
	 */
	@Override
	public void clearTodoCached(String moduleCode, String funcCode, String todoCode) {
		for (TodoNumberType t : TodoNumberType.values()) {
			this.clearNubmerCached(moduleCode, funcCode, todoCode, t);
		}
	}

	/**
	 * 清除缓存（某一项待办数量类型）
	 * 
	 * @param moduleCode 模块（微服务）编码
	 * @param funcCode   功能代码，必填项目，不允许为空
	 * @param todoCode   待办项代码，可选，允许为空
	 * @param type       待办数量类型：待办数量、未读数量等
	 */
	@Override
	public void clearNubmerCached(String moduleCode, String funcCode, String todoCode, TodoNumberType type) {
		try {
			if (StringUtils.isEmpty(moduleCode) || StringUtils.isEmpty(funcCode)) {
				return;
			}

			if (type == null) {
				type = TodoNumberType.TODO;// 默认获取待办数量
			}

			StringBuffer keybuff = new StringBuffer();
			keybuff.append(getRedKey());
			keybuff.append(":");
			keybuff.append(type.getKey().toUpperCase());
			keybuff.append(":");
			keybuff.append(moduleCode.toUpperCase());
			keybuff.append(":");
			keybuff.append(funcCode.toUpperCase());
			String key = keybuff.toString();

			if (StringUtils.isNotEmpty(todoCode)) {// 清除某个待办项
				key = key + ":" + todoCode.toUpperCase();
				redis.delete(key.toUpperCase());
			} else {// 清除整个功能
				Collection<String> keys = redis.keys(key + "*");
				redis.delete(keys);
			}
		} catch (Exception e) {
			log.error("", e);
		}
	}

	/**
	 * 清除待办缓存
	 * 
	 * @param moduleCode 模块（微服务）编码
	 * @param funcCode   功能代码，必填项目，不允许为空
	 */
	@Override
	public void clearTodoCached(String moduleCode, String funcCode) {
		clearTodoCached(moduleCode, funcCode, null);
	}

	/**
	 * 从redis中获取待办配置信息
	 * 
	 * @return
	 */
	private List<ToDoVo> getTodoFromRedis() {
		String key = getRedKey() + ":SET";
		List<ToDoVo> list = null;
		try {
			if (redis.hasKey(key)) {
				List<ToDoVo> readisList = redis.getClassList(ToDoVo.class, key);
				if (StringUtils.isNotEmpty(readisList)) {
					list = new ArrayList<ToDoVo>();
					HashSet<String> idSet = new HashSet<String>();
					for(ToDoVo temp:readisList) {
						if(temp.getTodoId()!=null) {
							 if(idSet.contains(temp.getTodoId())) {//判断待办id是否重复，重复了就不添加了，避免前台待办显示重复
								 continue;
							 }else {
								 idSet.add(temp.getTodoId());
								 list.add(temp);//添加到返回列表
							 }
						}else {
							list.add(temp);//如果没有id就直接添加，避免丢失待办
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return list;
	}

	/**
	 * 待办信息存入redis
	 * 
	 * @param list
	 */
	private void setTodoToRedis(List<ToDoVo> list) {
		String key = getRedKey() + ":SET";
		try {
			redis.setList(key, list);
		} catch (Exception e) {
			log.error("", e);
		}
	}

	/**
	 * 生成todovo对象
	 * 
	 * @param module
	 * @param info
	 * @return
	 */
	private ToDoVo getTodoVo(SysTodoModule module, SysTodoInfo info) {
		ToDoVo vo = new ToDoVo();
		vo.setTodoId(info.getId());

//		vo.setClassName(module.getClassName());
//		vo.setAppURL(info.getAppURL());
//		vo.setExecContent(info.getExecContent());
//		vo.setExecType(info.getExecType());
//		vo.setFuncCode(info.getFuncCode());
//		vo.setFuncName(module.getFuncName());
//		vo.setModuleCode(info.getModuleCode());
//		vo.setTmSort(info.getTmSort());
//		vo.setTodoCode(info.getTodoCode());
//		vo.setTodoCount(null);
//		vo.setTodoName(info.getTodoName());
//		vo.setTodoType(info.getTodoType());
//		vo.setTodoUrl(info.getTodoUrl());
		ObjUtils.copyTo(module, vo);// 拷贝模块相关字段
		ObjUtils.copyTo(info, vo);// 拷贝项目表相关字段
		return vo;
	}

	/**
	 * 获取待办模块列表
	 * 
	 * @param showAll true:是否显示所有 ;false :只显示used=1
	 * @return
	 */
	@Override
	public List<SysTodoModule> getTodoModule(boolean showAll) {
		Where where = Where.create();
		if (!showAll) {
			where.eq(SysTodoModule::getUsed, 1);
		}
		Order order = Order.create();
		order.orderByAsc(SysTodoModule::getTmSort);
		return entityService.queryListDisableTenant(SysTodoModule.class, where, order);
	}

	/**
	 * 获取待办信息列表
	 * 
	 * @param showAll true:是否显示所有 ;false :只显示used=1
	 * @return
	 */
	@Override
	public List<SysTodoInfo> getTodoInfo(boolean showAll) {
		Where where = Where.create();
		if (!showAll) {
			where.eq(SysTodoInfo::getUsed, 1);
		}
		Order order = Order.create();
		order.orderByAsc(SysTodoInfo::getModuleCode);
		order.orderByAsc(SysTodoInfo::getFuncCode);
		order.orderByAsc(SysTodoInfo::getTmSort);
		return entityService.queryListDisableTenant(SysTodoInfo.class, where, order);
	}

	/**
	 * 添加待办模块
	 * 
	 * @param list
	 * @return
	 */
	private boolean insertTodoModule(List<SysTodoModule> list) {
		int i = entityService.insertBatch(list);
		boolean bln = i > 0 ? true : false;
		if (bln) {
			this.clearAllTodoCached();// 清除缓存
		}
		return bln;
	}

	/**
	 * 修改待办模块
	 * 
	 * @param list
	 * @return
	 */
	@SuppressWarnings("unused")
	private boolean updateTodoModule(List<SysTodoModule> list) {
		int i = entityService.updateByIdBatch(list);
		boolean bln = i > 0 ? true : false;
		if (bln) {
			this.clearAllTodoCached();// 清除缓存
		}
		return bln;
	}

	/**
	 * 添加待办项
	 * 
	 * @param list
	 * @return
	 */
	private boolean insertTodoInfo(List<SysTodoInfo> list) {
		int i = entityService.insertBatch(list);
		boolean bln = i > 0 ? true : false;
		if (bln) {
			this.clearAllTodoCached();// 清除缓存
		}
		return bln;
	}

	/**
	 * 修改待办项
	 * 
	 * @param list
	 * @return
	 */
	@SuppressWarnings("unused")
	private boolean updateTodoInfo(List<SysTodoInfo> list) {
		int i = entityService.updateByIdBatch(list);
		boolean bln = i > 0 ? true : false;
		if (bln) {
			this.clearAllTodoCached();// 清除缓存
		}
		return bln;
	}

	/**
	 * 系统启动时初始化待办信息
	 * 
	 * @param moduleList
	 * @param infoList
	 */
	@Override
	public void initTodo(List<SysTodoModule> moduleList, List<SysTodoInfo> infoList) {
		List<SysTodoModule> moduleInsertList = new ArrayList<SysTodoModule>();
		List<SysTodoInfo> infoInsertList = new ArrayList<SysTodoInfo>();
		if (StringUtils.isNotEmpty(moduleList)) {// 待办模块
			List<SysTodoModule> list = this.getTodoModule(true);
			int sort = list.size();
			Map<String, SysTodoModule> map = new HashMap<String, SysTodoModule>();
			if (StringUtils.isNotEmpty(list)) {
				for (SysTodoModule e : list) {
					map.put(e.getModuleCode() + ":" + e.getFuncCode(), e);
				}
			}
			for (SysTodoModule e : moduleList) {
				String key = e.getModuleCode() + ":" + e.getFuncCode();
				if (!map.containsKey(key)) {
					sort++;
					e.setTmSort(sort);
					moduleInsertList.add(e);
				}
			}
		}
		if (StringUtils.isNotEmpty(infoList)) {// 待办项
			List<SysTodoInfo> list = this.getTodoInfo(true);
			int sort = list.size();
			Map<String, SysTodoInfo> map = new HashMap<String, SysTodoInfo>();
			if (StringUtils.isNotEmpty(list)) {
				for (SysTodoInfo e : list) {
					map.put(e.getModuleCode() + ":" + e.getFuncCode() + ":" + e.getTodoCode(), e);
				}
			}
			for (SysTodoInfo e : infoList) {
				String key = e.getModuleCode() + ":" + e.getFuncCode() + ":" + e.getTodoCode();
				if (!map.containsKey(key)) {
					sort++;
					e.setTmSort(sort);
					infoInsertList.add(e);
				}
			}
		}
		if (moduleInsertList.size() > 0) {
			this.insertTodoModule(moduleInsertList);
		}
		if (infoInsertList.size() > 0) {
			this.insertTodoInfo(infoInsertList);
		}
	}


	/**
	 * 获取tm3待办列表
	 * @category 获取tm3待办列表
	 * <AUTHOR> 
	 * @param user
	 * @return
	 */
	@Override
	public List<ToDoVo> getTm3TodoList(SysUser user) {
		// TODO Auto-generated method stub
		List<ToDoVo> result = new ArrayList<ToDoVo>();
		if(user!=null) {
			HashMap<String,String> tm3TodoCfg = getTM3ToDoConfig();//获取tm3待办配置
			if("true".equals(tm3TodoCfg.get("isUsed"))) {//启用待办
				String todo_address =tm3TodoCfg.get("todo_address");//tm3地址
				if (StringUtils.isNotEmpty(todo_address)) {
					String todo_codes =tm3TodoCfg.get("todo_codes");//待办模块列表
					String body= "{\"staffNo\":\""+user.getStaffNo()+"\",\"todo_codes\":\""+todo_codes+"\"}";
					String todoStr = doRequest(body,todo_address);
					if (StringUtils.isNotEmpty(todoStr)) {// 有待办项
//						[{"candoCount":0,"execType":-1,"moduleCode":"1410","moduleName":"????","rowFlag":1,"todoCount":0,"todoId":"module","todoName":"????"},{"appExecContent":"","appURL":"","canDoExecContent":"","canDoName":"","candoCount":0,"execContent":"todoApp.getCheckWorkTodo('addUp','')","execType":1,"isFc":0,"isKeep":0,"isMemCach":0,"isOpenWin":0,"mappURL":"","mico":"","mmenuType":0,"moduleCode":"1410","moduleName":"????","museType":"win,wx","rowFlag":0,"sort":19,"todoCode":"","todoCount":2,"todoId":"checkWork_addUp","todoMonth":"","todoName":"????","todoType":1,"todoUrl":"TM3://checkWork/check_suggest.jsp"}]
						try {
							JSONArray arr = JSON.parseArray(todoStr);
							if(arr!=null) {
								for (int i = 0; i < arr.size(); i++) {
					                JSONObject row = arr.getJSONObject(i);
					                if(row.containsKey("todoCount")) {	
					                	Integer todoCount= row.getInteger("todoCount");
					                	if(todoCount!=null && todoCount.intValue()>0) {//有待办数量
					                		 ToDoVo bean = new ToDoVo();
								                bean.setTodoId(row.getString("todoId"));
								                bean.setTodoCode(bean.getTodoId());
								                bean.setTodoName(row.getString("todoName"));
								                if(todoCount==null||todoCount==0) {//无效的代办
								                	continue;
								                }
								                bean.setTodoCount(todoCount);
								                bean.setModuleCode(row.getString("moduleCode"));
								                bean.setModuleName(row.getString("moduleName"));
								                bean.setFuncCode(bean.getModuleCode());
								                bean.setFuncName(bean.getModuleName());
								                bean.setTodoUrl(row.getString("todoUrl"));//详细地址
								                bean.setAppURL(row.getString("appURL"));
								                bean.setTodoType(3);// 1:待办；0：已办 3：TM3待办
								                bean.setTodoComData("[{name:\""+row.getString("todoId")+"\",url:\""+row.getString("todoUrl")+"\",span:24}]");
								                result.add(bean);
					                	}
					                }
				                }
								
							}
						}catch(Exception e) {
							log.error("TM3待办获取出错,"+todoStr, e);	
						}
					}
				}
			}
		}
		return result;
	}
	/**
	 * 获取tm3待办配置信息
	 * @category 
	 * <AUTHOR> 
	 * @return HashMap<String,String> 配置信息map
	 */
	private HashMap<String,String> getTM3ToDoConfig(){
		HashMap<String,String> result = new HashMap<String,String>();
		String cfgJson = configService.getSysConfig("todo_tm3");
		if(StringUtils.isNotEmpty(cfgJson)) {//有配置信息
			JSONObject cfgJson_main = JSONObject.parseObject(cfgJson);
			if(cfgJson_main!=null) {
				JSONObject value =cfgJson_main.getJSONObject("value");
				if(value!=null) {
					result.put("isUsed", value.getString("isUsed"));//是否启用获取TM3待办功能
					result.put("todo_address", value.getString("todo_address"));//TM3待办获取接口地址
					result.put("todo_codes", value.getString("todo_codes"));//获取待办的模块列表，逗号分隔，空为取全部模块
				}
			}
		}
		return result;
	}
	private String doRequest(String body,String url) {
		String result = null;
		try {
			HttpHeaders headers = new HttpHeaders();
			headers.set("Content-Type", "application/json; charset=utf-8");
	        //直接将数据对象放到请求体中
			Object entity = new HttpEntity<Object>(body, headers);
			ResponseEntity<String> res = restTemplate.postForEntity(url, entity, String.class);
			result = res.getBody();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error("", e);
		}
		return result;
	}
}
