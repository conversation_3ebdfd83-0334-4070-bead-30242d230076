package com.yunhesoft.system.tools.todo.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.todo.entity.dto.ReadRecordDto;
import com.yunhesoft.system.tools.todo.entity.po.SysReadRecord;
import com.yunhesoft.system.tools.todo.service.ReadRecordService;

/**
 * 系统已读未读接口实现
 * 
 * <AUTHOR>
 * @date 2022/06/01
 */
@Service
public class ReadRecordServiceImpl implements ReadRecordService {

	@Autowired
	EntityService entityService;

	/**
	 * 设置已读数据
	 */
	@Override
	public Boolean setReadRecord(ReadRecordDto dto) {

		SysUser currentUser = SysUserHolder.getCurrentUser();

		Where where = Where.create();
		where.eq(SysReadRecord::getTmUsed, 1);
		where.eq(SysReadRecord::getModuleCode, dto.getModuleCode());
		where.eq(SysReadRecord::getReadPersonId, currentUser.getId());
		where.in(SysReadRecord::getObjId, dto.getObjid().toArray());

		List<SysReadRecord> updateList = entityService.queryData(SysReadRecord.class, where, null, null);

		Map<String, String> upDateMap = updateList.stream()
				.collect(Collectors.toMap(SysReadRecord::getObjId, SysReadRecord::getObjId, (k1, k2) -> k1));
		List<SysReadRecord> insertList = new ArrayList<>();

		int sort = entityService.findMaxId(SysReadRecord.class, SysReadRecord::getTmSort).intValue();

		for (String id : dto.getObjid()) {
			if (!upDateMap.containsKey(id)) {
				SysReadRecord sysReadRecord = new SysReadRecord();
				sysReadRecord.setId(TMUID.getUID());
				sysReadRecord.setReadCount(1);
				sysReadRecord.setModuleCode(dto.getModuleCode());
				sysReadRecord.setReadPersonId(currentUser.getId());
				sysReadRecord.setReadPersonName(currentUser.getUserName());
				sysReadRecord.setObjId(id);
				sysReadRecord.setReadTimeCreate(new Date());
				sysReadRecord.setTmIsRead(1);
				sysReadRecord.setTmSort(++sort);
				sysReadRecord.setTmUsed(1);
				insertList.add(sysReadRecord);
			}
		}

		for (SysReadRecord s : updateList) {
			s.setReadCount(s.getReadCount() + 1);
			s.setReadTimeUpdate(new Date());
			s.setTmIsRead(1);
		}
		int t1 = 0;
		int t2 = 0;

		if (updateList.size() != 0) {
			t1 = entityService.updateByIdBatch(updateList);
		}
		if (insertList.size() > 0) {
			t2 = entityService.insertBatch(insertList);
		}

		return t1 + t2 > 0 ? true : false;
	}

	/**
	 * 获取已读列表
	 */
	@Override
	public List<SysReadRecord> getReadRecord(ReadRecordDto dto) {
//		SysUser currentUser = SysUserHolder.getCurrentUser();

		Where where = Where.create();
//		where.eq(SysReadRecord::getTmUsed, 1);
		if (dto != null) {
			if (StringUtils.isNotEmpty(dto.getModuleCode())) {
				where.eq(SysReadRecord::getModuleCode, dto.getModuleCode());
			}
			if (StringUtils.isNotEmpty(dto.getReadPersonId())) {
				where.eq(SysReadRecord::getReadPersonId, dto.getReadPersonId());
			}
			if (dto.getObjid() != null && dto.getObjid().size() > 0) {
				where.in(SysReadRecord::getObjId, dto.getObjid().toArray());
			}
		}
		List<SysReadRecord> sysReadRecordList = entityService.queryData(SysReadRecord.class, where, null, null);

		return sysReadRecordList;
	}

	/**
	 * 设置数据为未读（删除已读的数据）
	 */
	@Override
	public Boolean setUnReadRecord(ReadRecordDto dto) {
		SysUser currentUser = SysUserHolder.getCurrentUser();
		Where where = Where.create();

		if (dto != null) {

			where.eq(SysReadRecord::getReadPersonId, currentUser.getId());
			if (dto.getObjid() != null && dto.getObjid().size() != 0) {

				where.in(SysReadRecord::getObjId, dto.getObjid().toArray());
			}

			if (StringUtils.isNotEmpty(dto.getModuleCode())) {
				where.eq(SysReadRecord::getModuleCode, dto.getModuleCode());
			}
		}
		return entityService.rawDeleteByWhere(SysReadRecord.class, where) > 0;
	}

}
