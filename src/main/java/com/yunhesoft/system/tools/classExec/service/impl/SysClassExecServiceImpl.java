package com.yunhesoft.system.tools.classExec.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.system.tools.classExec.entry.dto.MtmFormulaTreeDto;
import com.yunhesoft.system.tools.classExec.entry.po.MtmFormulaTree;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.classExec.entry.po.MtmLookUpColumn;
import com.yunhesoft.system.tools.classExec.entry.po.MtmLookUpReg;
import com.yunhesoft.system.tools.classExec.entry.po.SYSClassExecConfig;
import com.yunhesoft.system.tools.classExec.service.SysClassExecService;

import lombok.extern.log4j.Log4j2;

import javax.transaction.Transactional;

@Log4j2
@Service
public class SysClassExecServiceImpl implements SysClassExecService {
	@Autowired
	private EntityService entityService;
	/**
	 * 注册执行类
	 * @category 
	 * <AUTHOR> 
	 * @param execType 执行的类型 比如目标传导公式用，可以传入mtm_formula
	 * @param moduleCode 模块编码
	 * @param moduleName 模块名称
	 * @param classPath 模块名称
	 * @return boolean true成功 false失败
	 */
	@Override
	public boolean register(String execType, String moduleCode, String moduleName, String classPath) {
		// TODO Auto-generated method stub
		SYSClassExecConfig cfg = new SYSClassExecConfig(); 
		cfg.setExecType(execType);
		cfg.setModuleCode(moduleCode);
		cfg.setModuleName(moduleName);
		cfg.setClassPath(classPath);		
		return registerModule(cfg);
	}
	
	/**
	 * 注册执行类
	 * @category 
	 * <AUTHOR> 
	 * @param execType 执行的类型 比如目标传导公式用，可以传入mtm_formula
	 * @param moduleCode 模块编码
	 * @param moduleName 模块名称
	 * @param serviceName  服务名
	 * @param getFormulaTree 树形获取服务
	 * @param getFormulaValue 公式解析服务
	 * @param getJsonData 获取json服务
	 * @param saveJsonData 保存json服务
	 * @param init 初始化公式服务
	 * @return boolean true成功 false失败
	 */
	@Override
	public boolean register(SYSClassExecConfig cfg) {
		return registerModule(cfg);
	}
	/**
	 * 注册执行类
	 * @category 
	 * <AUTHOR> 
	 * @param cfg
	 */
	private boolean registerModule(SYSClassExecConfig cfg){
		boolean result = false;
		if(cfg!=null) {
			if (StringUtils.isNotEmpty(cfg.getExecType()) && StringUtils.isNotEmpty(cfg.getModuleCode()) && StringUtils.isNotEmpty(cfg.getModuleName())) {
				SYSClassExecConfig dataBaseCfg = getConfigData(cfg.getExecType(),cfg.getModuleCode()); 
				if (dataBaseCfg==null) {// 为空时需要注册
//					cfg = new SYSClassExecConfig();
					cfg.setId(TMUID.getUID());
//					cfg.setExecType(execType);
//					cfg.setModuleCode(moduleCode);
//					cfg.setModuleName(moduleName);
//					cfg.setClassPath(classPath);
					cfg.setTmSort(0);
					int saveResult = entityService.insert(cfg);
					if (saveResult > 0) {
						result = true;
					}else {
						log.error("", "类执行注册插入失败：execType:"+cfg.getExecType()+"moduleCode:"+cfg.getModuleCode()+" moduleName:"+cfg.getModuleName());
					}
				}else {
					result = true;
				}
			}else {
				log.error("", "类执行注册缺少注册信息：execType:"+cfg.getExecType()+"moduleCode:"+cfg.getModuleCode()+" moduleName:"+cfg.getModuleName());
			}
		}
		return result;
	}
	/**
	 * 获取已注册的执行类
	 * @category 
	 * <AUTHOR> 
	 * @param execType 执行的类型 比如目标传导公式用，可以传入mtm_formula
	 * @return List<SYSClassExecConfig> 已注册的列表，按注册顺序显示
	 */
	@Override
	public List<SYSClassExecConfig> getRegisterList(String execType) {
		// TODO Auto-generated method stub
		return getData(execType, null);//查全部模块
	}
	
	/**
	 * 获取注册好的执行类
	 * @category 
	 * <AUTHOR> 
	 * @param execType 执行的类型 比如目标传导公式用，可以传入mtm_formula
	 * @param moduleCode 模块编码
	 * @return
	 */
	@Override
	public SYSClassExecConfig getConfigData(String execType, String moduleCode) {
		// TODO Auto-generated method stub
		SYSClassExecConfig result = null;
		List<SYSClassExecConfig> queryList = getData(execType,moduleCode);
		if (StringUtils.isNotEmpty(queryList)) {
			result = queryList.get(0);
		}
		return result;
	}
	/**
	 * 获取注册好的执行类
	 * @category 
	 * <AUTHOR> 
	 * @param execType 执行的类型 比如目标传导公式用，可以传入mtm_formula
	 * @param moduleCode 模块编码
	 * @return
	 */
	private List<SYSClassExecConfig> getData(String execType, String moduleCode) {
		List<SYSClassExecConfig> result = new ArrayList<SYSClassExecConfig>();	
		if (StringUtils.isNotEmpty(execType)){//参数有效
			Where where = Where.create();
			where.eq(SYSClassExecConfig::getExecType, execType);
			if(StringUtils.isNotEmpty(moduleCode)) {
				where.eq(SYSClassExecConfig::getModuleCode, moduleCode);
				Order order = Order.create();		
				order.orderByAsc(SYSClassExecConfig::getTmSort);
				order.orderByAsc(SYSClassExecConfig::getId);
			}
			List<SYSClassExecConfig> queryList = entityService.queryList(SYSClassExecConfig.class, where);
			if (StringUtils.isNotEmpty(queryList)) {
				result=queryList;
			}
		}
		return result;
	}

	
	/**
	 * 数据模型类实例化
	 * @param tabelCode  (String) 查表数据表编码
	 * @return MtmLookUpReg 查表注册信息
	 */
	@Override
	public MtmLookUpReg getLookUpRegByTabelCode(String tabelCode){
		MtmLookUpReg result = null;
		if (StringUtils.isNotEmpty(tabelCode)){//参数有效
			Where where = Where.create();
			where.eq(MtmLookUpReg::getTableCode, tabelCode);
			List<MtmLookUpReg> queryList = entityService.queryList(MtmLookUpReg.class, where);
			if (StringUtils.isNotEmpty(queryList)) {
				result=queryList.get(0);
			}			
		}
		return result;
	}
	/**
	 * 获取查表注册信息
	 * @param formulaModelCode  (String) 公式模块编码
	 * @return List<MtmLookUpReg>  查表注册信息
	 */
	@Override
	public List<MtmLookUpReg> getLookUpRegListByFormulaModelCode(String formulaModelCode) {
		// TODO Auto-generated method stub
		List<MtmLookUpReg> result = new ArrayList<MtmLookUpReg>();	
		if (StringUtils.isNotEmpty(formulaModelCode)){//参数有效
			Where where = Where.create();
			where.eq(MtmLookUpReg::getFormulaModelCode, formulaModelCode);
			Order order = Order.create();		
			order.orderByAsc(MtmLookUpReg::getModuleCode);
			order.orderByAsc(MtmLookUpReg::getTmSort);
			List<MtmLookUpReg> queryList = entityService.queryList(MtmLookUpReg.class, where,order);
			if (StringUtils.isNotEmpty(queryList)) {
				result=queryList;
			}			
		}
		return result;
	}
	/**
	 * 获取查表注册信息列表
	 * @return List<MtmLookUpReg> 查表注册信息
	 */
	@Override
	public List<MtmLookUpReg> getLookUpRegList() {
		// TODO Auto-generated method stub
		Where where = Where.create();
		Order order = Order.create();		
		order.orderByAsc(MtmLookUpReg::getModuleCode);
		order.orderByAsc(MtmLookUpReg::getTmSort);
		return entityService.queryList(MtmLookUpReg.class, where,order);
	}
	/**
	 * 获取查表字段信息
	 * @param formulaModelCode  (String) 公式模块编码
	 * @return List<MtmLookUpColumn>  查表字段信息
	 */
	@Override
	public List<MtmLookUpColumn> getLookUpColumnListByFormulaModelCode(String formulaModelCode) {
		// TODO Auto-generated method stub
		Where where = Where.create();
		where.eq(MtmLookUpColumn::getFormulaModelCode, formulaModelCode);
		Order order = Order.create();		
		order.orderByAsc(MtmLookUpColumn::getTmSort);
		return entityService.queryList(MtmLookUpColumn.class, where,order);
	}
	/**
	 * 获取查表表格字段列表
	 * @return List<MtmLookUpReg> 获取查表表格字段列表
	 */
	@Override
	public List<MtmLookUpColumn> getLookUpColumnList(String tableCode) {
		// TODO Auto-generated method stub
		Where where = Where.create();
		where.eq(MtmLookUpColumn::getTableCode, tableCode);
		Order order = Order.create();		
		order.orderByAsc(MtmLookUpColumn::getTmSort);
		return entityService.queryList(MtmLookUpColumn.class, where,order);
	}
	/**
	 * 注册查表表格信息
	 * @category 
	 * <AUTHOR> 
	 * @param formulaModelCode 公式模块编码
	 * @param regList 注册表信息列表
	 * @param columnList 注册表对应字段列表
	 */
	@Override
	public void registerLookUpCfg(String formulaModelCode, List<MtmLookUpReg> regList,
			List<MtmLookUpColumn> columnList) {
		// TODO Auto-generated method stub
		if (StringUtils.isNotEmpty(formulaModelCode)){//参数有效
			List<MtmLookUpReg> insertRegList = new ArrayList<MtmLookUpReg>();//注册信息
			if (StringUtils.isNotEmpty(regList)){//参数有效
				List<MtmLookUpReg> regSaveList =  getLookUpRegListByFormulaModelCode(formulaModelCode);
				Map<String, MtmLookUpReg> regMap = null;
				if (StringUtils.isNotEmpty(regSaveList)) {
					regMap = regSaveList.stream()
							.collect(Collectors.toMap(MtmLookUpReg::getTableCode, MtmLookUpReg -> MtmLookUpReg, (key1, key2) -> key1));// 将list转换为map																									// 重复键值时，第一个key不被第二个key覆盖
				}
				if(regMap==null) {
					regMap = new HashMap<String,MtmLookUpReg>();
				}
				for(MtmLookUpReg temp:regList) {
					if(!regMap.containsKey(temp.getTableCode())) {//未注册
						temp.setId(TMUID.getUID());
						insertRegList.add(temp);
					}
				}
			}
			if(insertRegList.size()>0) {
				entityService.insertBatch(insertRegList);
			}
			List<MtmLookUpColumn> insertColumnList = new ArrayList<MtmLookUpColumn>();//注册字段信息
			if (StringUtils.isNotEmpty(columnList)){//参数有效
				List<MtmLookUpColumn> columnSaveList =  getLookUpColumnListByFormulaModelCode(formulaModelCode);
				Map<String, MtmLookUpColumn> columnMap = new HashMap<String,MtmLookUpColumn>();;
				if (StringUtils.isNotEmpty(columnSaveList)) {
					for(MtmLookUpColumn temp:columnSaveList) {
						columnMap.put(temp.getTableCode()+"_"+temp.getColumnCode(), temp);//按照表+字段做key检索
					}																					
				}
				for(MtmLookUpColumn temp:columnList) {
					if(!columnMap.containsKey(temp.getTableCode()+"_"+temp.getColumnCode())) {//未注册
						temp.setId(TMUID.getUID());
						insertColumnList.add(temp);
					}
				}
			}
			if(insertColumnList.size()>0) {
				entityService.insertBatch(insertColumnList);
			}
		}
	}

	/**
	 * 初始化公式树节点数据
	 * 数据比对，多增少补
	 * @param treeNodes
	 * @return
	 */
	@Transactional
	@Override
	public boolean initMtmFormulaTree(List<MtmFormulaTree> treeNodes) {
		if (ObjUtils.isEmpty(treeNodes)) {
			log.error("无法初始化公式树形！参数无效！");
			return false;
		}

		String moduleCode = treeNodes.get(0).getModuleCode();

		Where where = new Where();
		where.eq(MtmFormulaTree::getModuleCode, moduleCode);
		where.eq(MtmFormulaTree::getTmUsed, 1);

		//获取当前模块历史公式数据
		List<MtmFormulaTree> oldTreeNodes = entityService.queryData(MtmFormulaTree.class, where, null, null);
		oldTreeNodes = Optional.ofNullable(oldTreeNodes).orElse(new ArrayList<>());
		Map<String, MtmFormulaTree> oldTreeFormulaCodeMap = oldTreeNodes.stream().collect(Collectors.toMap(MtmFormulaTree::getFormulaCode, b -> b, (key1, key2) -> key1));
		Map<String, MtmFormulaTree> newTreeFormulaCodeMap = treeNodes.stream().collect(Collectors.toMap(MtmFormulaTree::getFormulaCode, b -> b, (key1, key2) -> key1));
		Map<String, MtmFormulaTree> oldTreeIdMap = oldTreeNodes.stream().collect(Collectors.toMap(MtmFormulaTree::getId, b -> b, (key1, key2) -> key1));
		Map<String, MtmFormulaTree> newTreeIdMap = treeNodes.stream().collect(Collectors.toMap(MtmFormulaTree::getId, b -> b, (key1, key2) -> key1));

		List<MtmFormulaTree> insertTreeNodes = new ArrayList<>();
		List<MtmFormulaTree> updateTreeNodes = new ArrayList<>();

		//使用当前树节点到历史树节点中查找，找不到则新增，找到了则什么都不做
		for (MtmFormulaTree treeNode : treeNodes) {
			String formulaCode = treeNode.getFormulaCode();
			if (!oldTreeFormulaCodeMap.containsKey(formulaCode)) { //未找到历史节点
				//不破坏当前树形关系结构，使用克隆对象填充至历史树与新增数据列表
				String pId = getMtmFormulaTreePId(treeNode, oldTreeIdMap, newTreeIdMap);
				MtmFormulaTree treeNodeClone = ObjUtils.copyTo(treeNode, MtmFormulaTree.class);
				treeNodeClone.setPId(pId);
				insertTreeNodes.add(treeNodeClone);
				oldTreeNodes.add(treeNodeClone);
				oldTreeFormulaCodeMap.put(treeNodeClone.getFormulaCode(), treeNodeClone);
				oldTreeIdMap.put(treeNodeClone.getId(), treeNodeClone);
			}
		}

		//使用历史树节点到当前树节点中查找，找不到则删除，找到了则什么都不做
		for (MtmFormulaTree oldTreeNode : oldTreeNodes) {
			String formulaCode = oldTreeNode.getFormulaCode();
			if (!newTreeFormulaCodeMap.containsKey(formulaCode)) { //未找到历史节点
				//删除此历史节点与其所有子节点
				oldTreeNode.setTmUsed(0);
				oldTreeNode.setUpdateBy("系统");
				oldTreeNode.setUpdateTime(DateTimeUtils.getNowDate());
				updateTreeNodes.add(oldTreeNode);

				List<MtmFormulaTree> oldChildrenNodes = getMtmFormulaTreeChildrenNodes(oldTreeNode, oldTreeNodes);
				if (ObjUtils.notEmpty(oldChildrenNodes)) {
					for (MtmFormulaTree oldChildrenNode : oldChildrenNodes) {
						if (oldChildrenNode.getCreateTime() == null) { //添加老数据树中的新节点不处理
							continue;
						}
						oldChildrenNode.setTmUsed(0);
						oldChildrenNode.setUpdateBy("系统");
						oldChildrenNode.setUpdateTime(DateTimeUtils.getNowDate());
						updateTreeNodes.add(oldChildrenNode);
					}
				}

			}
		}

		//插入数据
		if (insertTreeNodes.size() > 0) {
			entityService.insertBatch(insertTreeNodes);
		}
		//更新数据
		if (updateTreeNodes.size() > 0) {
			//数据去重
			updateTreeNodes = updateTreeNodes.stream().distinct().collect(Collectors.toList());
			entityService.updateBatch(updateTreeNodes);
		}

		return true;
	}

	/**
	 * 通过当前节点的父节点寻找历史的父节点ID
	 * @param treeNode 当前节点
	 * @param oldTreeIdMap 历史公式树ID Map
	 * @param newTreeIdMap 新公式树ID Map
	 * @return
	 */
	private String getMtmFormulaTreePId(MtmFormulaTree treeNode, Map<String, MtmFormulaTree> oldTreeIdMap, Map<String, MtmFormulaTree> newTreeIdMap) {
		String findPid = null;

		String pId = treeNode.getPId();
		if (pId == null) {
			return findPid;
		}
		MtmFormulaTree pNode = newTreeIdMap.get(pId);
		if (pNode == null) {
			return findPid;
		}

		String pNodeFormulaCode = pNode.getFormulaCode();
		MtmFormulaTree findPNode = oldTreeIdMap.get(pNodeFormulaCode);
		if (findPNode != null) {
			findPid = findPNode.getId();
		} else {
			findPid = pId;
		}

		return findPid;
	}

	/**
	 * 获取子节点
	 * @param pNode
	 * @param treeNodes
	 * @return
	 */
	private List<MtmFormulaTree> getMtmFormulaTreeChildrenNodes(MtmFormulaTree pNode, List<MtmFormulaTree> treeNodes) {
		List<MtmFormulaTree> childrenNodes = new ArrayList<>();

		if (pNode == null || ObjUtils.isEmpty(treeNodes)) {
			return childrenNodes;
		}

		String id = Optional.ofNullable(pNode.getId()).orElse("");
		List<MtmFormulaTree> findChildren = treeNodes.stream().filter(b -> id.equals(b.getPId())).collect(Collectors.toList());
		if (ObjUtils.notEmpty(findChildren)) {
			childrenNodes.addAll(findChildren);
			for (MtmFormulaTree findChild : findChildren) {
				List<MtmFormulaTree> findChildren2 = getMtmFormulaTreeChildrenNodes(findChild, treeNodes);
				if (ObjUtils.notEmpty(findChildren)) {
					childrenNodes.addAll(findChildren2);
				}
			}
		}

		return childrenNodes;
	}

	/**
	 * 获取公式树列表
	 * @param dto
	 * @return
	 */
	public List<MtmFormulaTree> getMtmFormulaTreeList(MtmFormulaTreeDto dto) {
		List<MtmFormulaTree> list = new ArrayList<>();

		String pId = dto.getPId();
		String moduleCode = dto.getModuleCode();

		Where where = new Where();

		if (StringUtils.isNotEmpty(pId)) {
			where.eq(MtmFormulaTree::getPId, pId);
		}
		if (StringUtils.isNotEmpty(moduleCode)) {
			where.eq(MtmFormulaTree::getModuleCode, moduleCode);
		}

		Order order = new Order();
		order.orderByAsc(MtmFormulaTree::getTmSort);

		list = entityService.queryData(MtmFormulaTree.class, where, order, null);

		return list;
	}
}
