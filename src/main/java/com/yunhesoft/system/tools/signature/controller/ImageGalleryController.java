package com.yunhesoft.system.tools.signature.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.tools.signature.entity.dto.ImageData;
import com.yunhesoft.system.tools.signature.entity.dto.ImageQueryDto;
import com.yunhesoft.system.tools.signature.entity.dto.ImageSaveData;
import com.yunhesoft.system.tools.signature.entity.dto.saveDataDto;
import com.yunhesoft.system.tools.signature.entity.dto.sigQueryDto;
import com.yunhesoft.system.tools.signature.entity.po.ImageGalleryGw;
import com.yunhesoft.system.tools.signature.service.ImageGalleryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/iamgeGalleryService")
@Api(tags = "图片库操作")
public class ImageGalleryController extends BaseRestController {

	@Autowired
	private ImageGalleryService srv;
	
	@ApiOperation(value = "获取单个图片信息")
	@RequestMapping(value = "/getImgDataOne", method = { RequestMethod.POST })
	public Res<?> getImgDataOne(@RequestBody ImageQueryDto dto) {
		return Res.OK(srv.getImgData(dto.getDm(), dto.getType()));
	}
	
	@ApiOperation(value = "获取图片信息")
	@RequestMapping(value = "/getImgData", method = { RequestMethod.POST })
	public Res<?> getImgDataList(@RequestBody ImageQueryDto dto) {
		Res<List<ImageData>> res = new Res<List<ImageData>>();
		Pagination<?> page = null;
		if (dto != null && dto.getSize() != null) {
			page = new Pagination(dto.getCurrent(), dto.getSize());
		}
		List<ImageData> list = srv.getDataList(dto.getType(), dto.getDm(), page);
		if (page != null) {
			res.setTotal(page.getTotal());// 总数量赋值
		}
		res.setResult(list);
		return res;
	}
	
	@ResponseBody
	@ApiOperation(value = "保存图片信息")
	@RequestMapping(value = "/saveImgData", method = { RequestMethod.POST })
	@ApiImplicitParam(name = "listDto", value = "人员信息列表", required = true, paramType = "body", dataType = "iamgeSaveData")
	public Res<?> saveData(@RequestBody List<ImageSaveData> list){
		return Res.OK(srv.saveData(list));
	}
	
	@ApiOperation(value = "获取岗位设置数据")
	@RequestMapping(value = "/getGwSetData", method = { RequestMethod.POST })
	public Res<?> getDesignPlanChangeData(@RequestBody ImageQueryDto param) {
		Res<List<ImageGalleryGw>> res = new Res<List<ImageGalleryGw>>();
		Pagination<?> page = null;
		if (param != null && param.getSize() != null) {
			page = new Pagination(param.getCurrent(), param.getSize());
		}
		List<ImageGalleryGw> list = srv.getGwSetList(param.getDm(),page);
		if (page != null) {
			res.setTotal(page.getTotal());// 总数量赋值
		}
		res.setResult(list);
		return res;
	}

	@ApiOperation(value = "保存岗位设置数据")
	@RequestMapping(value = "/saveGwSetData", method = { RequestMethod.POST })
	public Res<?> saveDesignPlanChangeData(@RequestBody saveDataDto param) {
		String res = srv.saveGwSetData(param);
		return Res.OK(res);
	}
	
	@ApiOperation(value = "获取盖章数据（批量）")
	@RequestMapping(value = "/getSinDataList", method = { RequestMethod.POST })
	public Res<?> getSinDataList(@RequestBody sigQueryDto dto) {
		return Res.OK(srv.getSinDataList(dto.getDatas()));
	}
	
	@ApiOperation(value = "获取盖章数据（单个）")
	@RequestMapping(value = "/getSinDataOne", method = { RequestMethod.POST })
	public Res<?> getSinDataOne(@RequestBody sigQueryDto dto) {
		return Res.OK(srv.getSinDataOne(dto.getDataId(), dto.getComId()));
	}
	
	@ApiOperation(value = "获取当前登录人签章信息")
	@RequestMapping(value = "/getSinImgsOne", method = { RequestMethod.POST })
	public Res<?> getImgsOne(@RequestBody sigQueryDto dto) {
		return Res.OK(srv.getSinImgsOne(dto.getType(),dto.getImgType()));
	}
	
	@ApiOperation(value = "保存盖章数据")
	@RequestMapping(value = "/saveSigDataData", method = { RequestMethod.POST })
	public Res<?> saveSigDataData(@RequestBody saveDataDto param) {
		String res = srv.saveSigDataData(param);
		return Res.OK(res);
	}
}