package com.yunhesoft.system.tools.signature.service;

import java.util.List;

import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.tools.signature.entity.dto.ImageData;
import com.yunhesoft.system.tools.signature.entity.dto.ImageSaveData;
import com.yunhesoft.system.tools.signature.entity.dto.saveDataDto;
import com.yunhesoft.system.tools.signature.entity.po.ImageGallery;
import com.yunhesoft.system.tools.signature.entity.po.ImageGalleryGw;
import com.yunhesoft.system.tools.signature.entity.po.SigData;

public interface ImageGalleryService {

	String saveData(List<ImageSaveData> list);

	List<ImageGallery> getImgData(String dm, String type);

	List<ImageData> getDataList(String type, String orgdm, Pagination<?> page);

	String saveGwSetData(saveDataDto param);

	List<ImageGalleryGw> getGwSetList(String orgdm, Pagination<?> page);

	String saveSigDataData(saveDataDto param);

	List<SigData> getSinDataList(String datas);

	List<SigData> getSinDataOne(String dataId, String comId);

	List<ImageGallery> getSinImgsOne(String type, String imgType);

}
