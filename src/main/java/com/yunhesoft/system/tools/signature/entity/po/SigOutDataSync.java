package com.yunhesoft.system.tools.signature.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Entity
@Setter
@Getter
@ToString
@Table(name = "SIG_OUT_DATA_SYNC")
public class SigOutDataSync extends BaseEntity {
	/** */
	private static final long serialVersionUID = 1L;

	/** 数据id */
	@Column(name = "DATA_ID", length = 100)
	private String dataId;
	
	/** 签章对象 1：机构 2：人员 */
	@Column(name = "SIG_OBJECT")
	private int sigObject;
	
	/** 签章类型 1：公章  2：签名  3：人脸*/
	@Column(name = "SIG_TYPE")
	private int sigType;
	
	/** 外部代码 */
	@Column(name = "OUT_CODE", length = 100)
	private String outCode;
	
	/** 外部代码 */
	@Column(name = "OUT_NAME", length = 200)
	private String outName;
	
	/** 对照代码 */
	@Column(name = "DATA_CODE", length = 100)
	private String dataCode;
	
	/** 对照代码 */
	@Column(name = "DATA_NAME", length = 200)
	private String dataName;

	/** 第三方图片路径 */
	@Column(name = "PHOTO_URL" , length = 4000)
	private String photoUrl;
}
