package com.yunhesoft.system.tools.signature.entity.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ImageSaveData {
	
	/** 0：修改  1：添加  -1：删除 */
	@ApiModelProperty(value = "flag")
	private Integer flag;
	
	/** id */
	@ApiModelProperty(value = "id")
	private String id;
	
	/** 机构代码 */
	@ApiModelProperty(value = "orgdm")
	private String orgdm;
	
	/** 机构名称 */
	@ApiModelProperty(value = "orgmc")
	private String orgmc;
	
	/** 组员id */
	@ApiModelProperty(value = "zyid")
	private String zyid;
	
	/** 组员名称 */
	@ApiModelProperty(value = "zymc")
	private String zymc;

	/** 图片类别 1：机构  2：人员*/
	@ApiModelProperty(value = "imgClass")
	private Integer imgClass;

	/** 图片id */
	@ApiModelProperty(value = "imgId")
	private String imgId;

	/** 图片名称 */
	@ApiModelProperty(value = "imgName")
	private String imgName;

	/** 图片类型  1：公章  2：签名  3：人脸*/
	@ApiModelProperty(value = "imgType")
	private Integer imgType;
	
	/** 图片是否是重点 */
	@ApiModelProperty(value = "imgIsImportant")
	private Integer imgIsImportant;
	
	/** 图片编码 */
	@ApiModelProperty(value = "imgBase64")
	private String imgBase64;
	
	/** 图片地址 */
	@ApiModelProperty(value = "imgUrl")
	private String imgUrl;

	/** 外部ID 百度 */
	@ApiModelProperty(value = "outsideIdBaidu")
	private String outsideIdBaidu;
	
	/** 外部ID 阿里 */
	@ApiModelProperty(value = "outsideIdAli")
	private String outsideIdAli;
	
	/** 预留字段 */
	@ApiModelProperty(value = "param1")
	private String param1;

	/** 预留字段 */
	@ApiModelProperty(value = "param2")
	private String param2;

	/** 预留字段 */
	@ApiModelProperty(value = "param3")
	private String param3;

	/** 预留字段 */
	@ApiModelProperty(value = "param4")
	private String param4;
	
	/** 预留字段 */
	@ApiModelProperty(value = "param5")
	private String param5;

	/** 排序 */
	@ApiModelProperty(value = "sort")
	private Integer sort;

	/** 是否启用 1：使用 0：禁用*/
	@ApiModelProperty(value = "used")
	private Integer used;
}
