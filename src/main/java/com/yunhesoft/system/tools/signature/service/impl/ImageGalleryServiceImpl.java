package com.yunhesoft.system.tools.signature.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrg;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.impl.EntityServiceImpl;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.dto.SysOrgSelect;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.entity.vo.SysOrgTreeData;
import com.yunhesoft.system.org.service.impl.SysOrgImpl;
import com.yunhesoft.system.tools.components.entity.vo.ComponentVo;
import com.yunhesoft.system.tools.components.service.impl.SysComponentServiceImpl;
import com.yunhesoft.system.tools.signature.entity.dto.ImageData;
import com.yunhesoft.system.tools.signature.entity.dto.ImageSaveData;
import com.yunhesoft.system.tools.signature.entity.dto.saveDataDto;
import com.yunhesoft.system.tools.signature.entity.po.ImageGallery;
import com.yunhesoft.system.tools.signature.entity.po.ImageGalleryGw;
import com.yunhesoft.system.tools.signature.entity.po.SigData;
import com.yunhesoft.system.tools.signature.service.ImageGalleryService;

@Service
public class ImageGalleryServiceImpl implements ImageGalleryService {

	@Autowired
	private SysComponentServiceImpl userImpl;
	@Autowired
	private SysOrgImpl orgImpl;
	@Autowired
	private EntityServiceImpl dao;

	/**
	 * 获取当前登录人可用的章
	 * 
	 * @param type
	 * @return
	 */
	@Override
	public List<ImageGallery> getSinImgsOne(String type, String imgType) {
		SysUser user = SysUserHolder.getCurrentUser();

		List<ImageGallery> result = new ArrayList<ImageGallery>();

		Where where = Where.create();
		where.eq(ImageGallery::getUsed, 1);
		where.eq(ImageGallery::getImgType, imgType);
		where.eq(ImageGallery::getImgClass, type);
		if ("1".equals(type)) {
			where.eq(ImageGallery::getOrgdm, user.getOrgId());
		} else {
			where.eq(ImageGallery::getZyid, user.getId());
		}

		// 排序 倒序
		Order order = Order.create();
		order.orderByDesc(ImageGallery::getSort);

		result = dao.queryData(ImageGallery.class, where, order, null);

		return result;
	}

	/**
	 * 获取图片信息接口
	 * 
	 * @param type    1、机构 2、人员
	 * @param dm      type为1是机构代码 为2是人员id
	 * @param imgType 1：公章 2：签名 3：人脸
	 * @param isAall  true是全部，fasle是设置过重点，若没有设置则取倒序第一个
	 * @return
	 */
	public List<ImageGallery> getImgsOne(String type, String dm, String imgType, boolean isAall) {
		List<ImageGallery> result = new ArrayList<ImageGallery>();

		Where where = Where.create();
		where.eq(ImageGallery::getUsed, 1);
		where.eq(ImageGallery::getImgClass, type);
		where.eq(ImageGallery::getImgType, imgType);
		if ("1".equals(type)) {
			where.eq(ImageGallery::getOrgdm, dm);
		} else {
			where.eq(ImageGallery::getZyid, dm);
		}

		// 排序 倒序
		Order order = Order.create();
		order.orderByDesc(ImageGallery::getSort);

		List<ImageGallery> list = dao.queryData(ImageGallery.class, where, order, null);

		if (list != null && list.size() > 0) {
			if (isAall) {
				result = list;
			} else {
				for (int i = 0; i < list.size(); i++) {
					if (list.get(i).getImgIsImportant() == 1) {
						// 只取重点图片
						result.add(list.get(i));
						break;
					}
				}
				if (result.size() == 0) {
					// 若没有设置则取倒序第一个
					result.add(list.get(0));
				}
			}
		}

		return result;
	}

	/**
	 * 查询单个数据
	 * 
	 * @param dm
	 * @param type
	 * @return
	 */
	@Override
	public List<ImageGallery> getImgData(String dm, String type) {
		Where where = Where.create();
		where.eq(ImageGallery::getUsed, 1);
		where.eq(ImageGallery::getImgClass, type);
		if ("1".equals(type)) {
			where.eq(ImageGallery::getOrgdm, dm);
		} else {
			where.eq(ImageGallery::getZyid, dm);
		}

		// 排序
		Order order = Order.create();
		order.orderByAsc(ImageGallery::getSort);

		List<ImageGallery> List = dao.queryData(ImageGallery.class, where, order, null);

		return List;
	}

	/**
	 * 数据查询
	 * 
	 * @param orgdm
	 * @return
	 */
	@Override
	public List<ImageData> getDataList(String type, String orgdm, Pagination<?> page) {
		List<ImageData> result = new ArrayList<ImageData>(); // 新增

		if ("1".equals(type)) {
			result = this.getOrgDataList(orgdm, page);
		} else if ("2".equals(type)) {
			result = this.getUserDataList(orgdm, page);
		}

		return result;
	}

	/**
	 * 保存方法
	 * 
	 * @param list
	 * @return
	 */
	@Override
	public String saveData(List<ImageSaveData> list) {
		String result = "0";

		if (list != null && list.size() > 0) {
			SysUser user = SysUserHolder.getCurrentUser();

			List<ImageGallery> addList = new ArrayList<ImageGallery>(); // 新增
			List<ImageGallery> updList = new ArrayList<ImageGallery>(); // 修改
			// List<iamgeGallery> delList = new ArrayList<iamgeGallery>(); //删除
			for (int i = 0; i < list.size(); i++) {
				ImageSaveData info = list.get(i);
				ImageGallery bean = ObjUtils.copyTo(info, ImageGallery.class);
				int flag = info.getFlag();
				if(user!=null) {
					bean.setUpdateBy(user.getRealName());
				}else {
					bean.setUpdateBy("系统");
				}
				bean.setUpdateTime(DateTimeUtils.getNowDate());
				if (flag == 1) {
					bean.setId(TMUID.getUID());
					if(user!=null) {
						bean.setCreateBy(user.getRealName());
					}else {
						bean.setCreateBy("系统");
					}
					bean.setCreateTime(DateTimeUtils.getNowDate());
					addList.add(bean);
				} else {
					updList.add(bean);
				}
			}

			if (addList.size() > 0) {
				dao.insertBatch(addList);
			}

			if (updList.size() > 0) {
				dao.updateByIdBatch(updList);
			}
			result = "1";
		}

		return result;
	}

	/**
	 * 机构信息
	 * 
	 * @param orgdm
	 * @return
	 */
	private List<ImageData> getOrgDataList(String orgdm, Pagination<?> page) {
		SysOrgSelect orgParam = new SysOrgSelect();
		orgParam.setPorgcode(orgdm);

		List<SysOrgTreeData> orglist = orgImpl.listDatas(orgParam); // 机构信息

		List<ImageData> orgDataList = new ArrayList<ImageData>(); // 机构数据

		List<String> orgIds = new ArrayList<String>();
		if (orglist != null && orglist.size() > 0) {
			for (int i = 0; i < orglist.size(); i++) {
				orgIds.add(orglist.get(i).getOrgcode());
				ImageData info = new ImageData();
				info.setOrgdm(orglist.get(i).getOrgcode());
				info.setOrgmc(orglist.get(i).getOrgname());
				info.setType(1);// 机构

				// 岗位信息查询
				Where whereGw = Where.create();
				whereGw.eq(ImageGalleryGw::getTmused, 1);
				whereGw.eq(ImageGalleryGw::getOrgdm, orglist.get(i).getOrgcode());
				List<ImageGalleryGw> gwRes = dao.queryData(ImageGalleryGw.class, whereGw, null, null);
				info.setGwList(gwRes);

				orgDataList.add(info);
			}

			Where where = Where.create();
			where.eq(ImageGallery::getUsed, 1);
			where.eq(ImageGallery::getImgClass, 1);// 机构
			where.in(ImageGallery::getOrgdm, orgIds.toArray());

			// 排序
			Order order = Order.create();
			order.orderByAsc(ImageGallery::getSort);

			List<ImageGallery> orgImgList = dao.queryData(ImageGallery.class, where, order, null);// 机构签章信息

			for (int i = 0; i < orgDataList.size(); i++) {
				List<ImageGallery> officialSeals = new ArrayList<ImageGallery>(); // 公章
				List<ImageGallery> signature = new ArrayList<ImageGallery>(); // 签名
				List<ImageGallery> faces = new ArrayList<ImageGallery>(); // 人脸
				if (orgImgList != null && orgImgList.size() > 0) {
					for (int l = 0; l < orgImgList.size(); l++) {
						ImageGallery bean = orgImgList.get(l);
						if (orgDataList.get(i).getOrgdm().equals(bean.getOrgdm())) {
							if (bean.getImgType() == 1) {
								officialSeals.add(bean);
							} else if (bean.getImgType() == 2) {
								signature.add(bean);
							} else if (bean.getImgType() == 3) {
								faces.add(bean);
							}
						}
					}
				}
				orgDataList.get(i).setOfficialSeals(officialSeals);
				orgDataList.get(i).setSignature(signature);
				orgDataList.get(i).setFaces(faces);
			}
		}

		return orgDataList;
	}

	/**
	 * 人员信息
	 * 
	 * @param orgdm
	 * @return
	 */
	private List<ImageData> getUserDataList(String orgdm, Pagination<?> page) {
		EmpParamDto userParam = new EmpParamDto();
		userParam.setOrgcode(orgdm);

		List<ComponentVo> userList = userImpl.getEmployee(userParam, page);// 人员信息

		List<ImageData> userDataList = new ArrayList<ImageData>();

		List<String> userIds = new ArrayList<>();
		if (userList != null && userList.size() > 0) {
			List<String> orgcodes = new ArrayList<>();
			for (int i = 0; i < userList.size(); i++) {
				for (int l = 0; l < userList.get(i).getChildren().size(); l++) {
					userIds.add(userList.get(i).getChildren().get(l).getCode());
					ImageData info = new ImageData();
					info.setZyid(userList.get(i).getChildren().get(l).getCode());
					info.setZymc(userList.get(i).getChildren().get(l).getLable());
					info.setType(2);// 人员
					userDataList.add(info);
				}
			}

			Where where1 = Where.create();
			where1.eq(SysEmployeeOrg::getUsed, 1);
			where1.in(SysEmployeeOrg::getEmpid, userIds.toArray());
			List<SysEmployeeOrg> userOrgList = dao.queryData(SysEmployeeOrg.class, where1, null, null);// 人员机构信息

			Map<String, String> userMap = new HashMap<String, String>();
			Map<String, String> orgMap = new HashMap<String, String>();
			if (userOrgList != null && userOrgList.size() > 0) {
				for (int i = 0; i < userOrgList.size(); i++) {
					orgcodes.add(userOrgList.get(i).getOrgcode());
					userMap.put(userOrgList.get(i).getEmpid(), userOrgList.get(i).getOrgcode());
				}
				Where where2 = Where.create();
				where2.eq(SysOrg::getUsed, 1);
				where2.in(SysOrg::getOrgcode, orgcodes.toArray());
				List<SysOrg> userOrgmcList = dao.queryData(SysOrg.class, where2, null, null);// 人员机构信息
				if (userOrgmcList != null && userOrgmcList.size() > 0) {
					for (int i = 0; i < userOrgmcList.size(); i++) {
						orgMap.put(userOrgmcList.get(i).getOrgcode(), userOrgmcList.get(i).getOrgname());
					}
				}
			}

			Where where = Where.create();
			where.eq(ImageGallery::getUsed, 1);
			where.eq(ImageGallery::getImgClass, 2);// 人员
			where.in(ImageGallery::getZyid, userIds.toArray());

			// 排序
			Order order = Order.create();
			order.orderByAsc(ImageGallery::getSort);

			List<ImageGallery> userImgList = dao.queryData(ImageGallery.class, where, order, null);// 人员签章信息

			for (int i = 0; i < userDataList.size(); i++) {
				userDataList.get(i).setOrgdm(userMap.get(userDataList.get(i).getZyid()));
				userDataList.get(i).setOrgmc(orgMap.get(userDataList.get(i).getOrgdm()));
				List<ImageGallery> officialSeals = new ArrayList<ImageGallery>(); // 公章
				List<ImageGallery> signature = new ArrayList<ImageGallery>(); // 签名
				List<ImageGallery> faces = new ArrayList<ImageGallery>(); // 人脸
				if (userImgList != null && userImgList.size() > 0) {
					for (int l = 0; l < userImgList.size(); l++) {
						ImageGallery bean = userImgList.get(l);
						if (userDataList.get(i).getZyid().equals(bean.getZyid())) {
							if (bean.getImgType() == 1) {
								officialSeals.add(bean);
							} else if (bean.getImgType() == 2) {
								signature.add(bean);
							} else if (bean.getImgType() == 3) {
								faces.add(bean);
							}
						}
					}
				}
				userDataList.get(i).setOfficialSeals(officialSeals);
				userDataList.get(i).setSignature(signature);
				userDataList.get(i).setFaces(faces);
			}
		}
		return userDataList;
	}

	/**
	 * 查询数据源按钮数据
	 * 
	 * @param tdsalias  数据源别名
	 * @param paraalias 数据列名
	 * @return
	 */
	@Override
	public List<ImageGalleryGw> getGwSetList(String orgdm, Pagination<?> page) {
		List<ImageGalleryGw> result = new ArrayList<ImageGalleryGw>();

		Where where = Where.create();
		where.eq(ImageGalleryGw::getTmused, 1);
		where.eq(ImageGalleryGw::getOrgdm, orgdm);

		// 排序 倒序
		Order order = Order.create();
		order.orderByAsc(ImageGalleryGw::getTmsort);

		result = dao.queryData(ImageGalleryGw.class, where, order, page);

		return result;
	}

	/**
	 * 保存
	 * 
	 * @param param
	 * @return
	 */
	@Override
	public String saveGwSetData(saveDataDto param) {
		String result = "0";

		if (param != null && StringUtils.isNotEmpty(param.getData())) {
			SysUser user = SysUserHolder.getCurrentUser();

			JSONArray datas = JSONArray.parseArray(param.getData());

			List<ImageGalleryGw> addList = new ArrayList<ImageGalleryGw>(); // 新增
			List<ImageGalleryGw> updList = new ArrayList<ImageGalleryGw>(); // 修改
			if (datas != null) {
				for (int i = 0; i < datas.size(); i++) {
					JSONObject row = datas.getJSONObject(i);
					Integer flag = row.getInteger("TDSROW_rowFlag");
					ImageGalleryGw bean = row.toJavaObject(ImageGalleryGw.class);
					bean.setUpdateBy(user.getRealName());
					bean.setUpdateTime(DateTimeUtils.getNowDate());
					if (flag == null || flag == 0) {
						bean.setId(TMUID.getUID());
						bean.setCreateBy(user.getRealName());
						bean.setCreateTime(DateTimeUtils.getNowDate());
						addList.add(bean);
					} else {
						updList.add(bean);
					}
				}
			}

			if (addList.size() > 0) {
				dao.insertBatch(addList);
			}

			if (updList.size() > 0) {
				dao.updateByIdBatch(updList);
			}
			result = "1";
		}

		return result;
	}

	/**
	 * 获取当前登录人可用的章
	 * 
	 * @param type
	 * @return
	 */
	@Override
	public List<SigData> getSinDataList(String datas) {
		List<SigData> result = new ArrayList<SigData>();

		if (datas != null && datas.length() > 0) {
			List<String> data_ids = Arrays.asList(datas.split(","));
			Where where = Where.create();
			where.eq(SigData::getTmused, 1);
			where.in(SigData::getDataId, data_ids.toArray());

			// 排序 倒序
			Order order = Order.create();
			order.orderByDesc(SigData::getTmsort);

			result = dao.queryData(SigData.class, where, order, null);
		}

		return result;
	}

	/**
	 * 获取当前登录人可用的章
	 * 
	 * @param type
	 * @return
	 */
	@Override
	public List<SigData> getSinDataOne(String dataId, String comId) {
		List<SigData> result = new ArrayList<SigData>();
		boolean isRes = true;
		Where where = Where.create();
		where.eq(SigData::getTmused, 1);
		if (dataId != null && dataId.length() > 0) {
			where.eq(SigData::getDataId, dataId);
		} else {
			isRes = false;
		}

		if (comId != null && comId.length() > 0) {
			where.eq(SigData::getComId, comId);
		} else {
			isRes = false;
		}

		if (isRes) {
			result = dao.queryData(SigData.class, where, null, null);
		}

		return result;
	}

	/**
	 * 保存
	 * 
	 * @param param
	 * @return
	 */
	@Override
	public String saveSigDataData(saveDataDto param) {
		String result = "0";

		if (param != null && StringUtils.isNotEmpty(param.getData())) {
			SysUser user = SysUserHolder.getCurrentUser();

			JSONArray datas = JSONArray.parseArray(param.getData());

			List<SigData> addList = new ArrayList<SigData>(); // 新增
			List<SigData> updList = new ArrayList<SigData>(); // 修改
			if (datas != null) {
				for (int i = 0; i < datas.size(); i++) {
					JSONObject row = datas.getJSONObject(i);
					// String id = row.getString("id");
					SigData bean = row.toJavaObject(SigData.class);

					bean.setSigDate(DateTimeUtils.getNowDate());
					bean.setPersonId(user.getId());
					bean.setPersonName(user.getUserName());
					bean.setPersonMyOrgId(user.getOrgId());
					bean.setPersonMyOrgName(user.getOrgName());
					bean.setTmsort(0);

					bean.setUpdateBy(user.getRealName());
					bean.setUpdateTime(DateTimeUtils.getNowDate());
					if (bean.getId() == null || "".equals(bean.getId())) {
						bean.setId(TMUID.getUID());
						bean.setCreateBy(user.getRealName());
						bean.setCreateTime(DateTimeUtils.getNowDate());
						addList.add(bean);
					} else {
						updList.add(bean);
					}
				}
			}

			if (addList.size() > 0) {
				dao.insertBatch(addList);
			}

			if (updList.size() > 0) {
				dao.updateByIdBatch(updList);
			}
			result = "1";
		}

		return result;
	}

	// 拼接字段
	private String columnJudge(String column) {
		if (column != null && !"".equals(column)) {
			return "," + column;
		} else {
			return "";
		}
	}

}
