package com.yunhesoft.system.tools.signature.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Entity
@Setter
@Getter
@ToString
@Table(name = "IMAGE_GALLERY")
public class ImageGallery extends BaseEntity {
	/** */
	private static final long serialVersionUID = 1L;

	/** 机构代码 */
	@Column(name = "ORGDM", length = 50)
	private String orgdm;
	
	/** 机构名称 */
	@Column(name = "ORGMC", length = 50)
	private String orgmc;
	
	/** 组员id */
	@Column(name = "ZYID", length = 50)
	private String zyid;
	
	/** 组员名称 */
	@Column(name = "ZYMC", length = 50)
	private String zymc;

	/** 图片类别 1：机构  2：人员*/
	@Column(name = "IMGCLASS")
	private Integer imgClass;

	/** 图片id */
	@Column(name = "IMGID", length = 200)
	private String imgId;

	/** 图片名称 */
	@Column(name = "IMGNAME", length = 200)
	private String imgName;

	/** 图片类型  1：公章  2：签名  3：人脸*/
	@Column(name = "IMGTYPE")
	private Integer imgType;
	
	/** 图片是否是重点 */
	@Column(name = "IMGISIMPORTANT")
	private Integer imgIsImportant;
	
	/** 图片编码 */
	@Column(name = "IMGBASE64", length = 200)
	private String imgBase64;
	
	/** 图片地址 */
	@Column(name = "IMGURL", length = 200)
	private String imgUrl;

	/** 外部ID 百度 */
	@Column(name = "OUTSIDEIDBAIDU", length = 200)
	private String outsideIdBaidu;
	
	/** 外部ID 阿里 */
	@Column(name = "OUTSIDEIDALI", length = 200)
	private String outsideIdAli;
	
	/** 预留字段 */
	@Column(name = "PARAM1", length = 200)
	private String param1;

	/** 预留字段 */
	@Column(name = "PARAM2", length = 200)
	private String param2;

	/** 预留字段 */
	@Column(name = "PARAM3", length = 200)
	private String param3;

	/** 预留字段 */
	@Column(name = "PARAM4", length = 200)
	private String param4;
	
	/** 预留字段 */
	@Column(name = "PARAM5", length = 200)
	private String param5;

	/** 排序 */
	@Column(name = "SORT")
	private Integer sort;

	/** 是否启用 1：使用 0：禁用*/
	@Column(name = "USED")
	private Integer used;
}
