package com.yunhesoft.system.tools.signature.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class sigQueryDto {

	/**
	 * 数据id
	 */
	@ApiModelProperty(value = "dataId")
	private String dataId;

	/**
	 * 组件ID
	 */
	@ApiModelProperty(value = "comId")
	private String comId;
	
	/**
	 * 数据ID 逗号分隔
	 */
	@ApiModelProperty(value = "datas")
	private String datas;
	
	/**
	 * 签章类型  1：机构  2：人员
	 */
	@ApiModelProperty(value = "type")
	private String type;
	
	/**
	 * 签章类型  1：公章  2：签名  3：人脸
	 */
	@ApiModelProperty(value = "imgType")
	private String imgType;
}
