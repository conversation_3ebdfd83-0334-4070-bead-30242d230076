package com.yunhesoft.system.tools.signature.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Entity
@Setter
@Getter
@ToString
@Table(name = "IMAGE_GALLERY_GW")
public class ImageGalleryGw extends BaseEntity {
	/** */
	private static final long serialVersionUID = 1L;

	/** 机构代码 */
	@Column(name = "ORGDM", length = 50)
	private String orgdm;
	
	/** 机构名称 */
	@Column(name = "ORGMC", length = 50)
	private String orgmc;
	
	/** 组员id */
	@Column(name = "GWID", length = 50)
	private String gwid;
	
	/** 组员名称 */
	@Column(name = "GWMC", length = 50)
	private String gwmc;

	/** 是否删除 */
    @Column(name="TMUSED")
    private int tmused;
    
    /** 排序 */
    @Column(name="TMSORT")
    private int tmsort;
}
