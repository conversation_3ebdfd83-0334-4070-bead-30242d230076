package com.yunhesoft.system.tools.signature.entity.dto;

import java.util.List;

import com.yunhesoft.system.tools.signature.entity.po.ImageGallery;
import com.yunhesoft.system.tools.signature.entity.po.ImageGalleryGw;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ImageData {
	
	/**
	 * 机构代码
	 */
	@ApiModelProperty(value = "orgdm")
	private String orgdm;
	
	/**
	 * 机构名称
	 */
	@ApiModelProperty(value = "orgmc")
	private String orgmc;

	/**
	 * 组员ID
	 */
	@ApiModelProperty(value = "zyid")
	private String zyid;
	
	/**
	 * 组员名称
	 */
	@ApiModelProperty(value = "zymc")
	private String zymc;
	
	/**
	 * 类型
	 */
	@ApiModelProperty(value = "type")
	private int type;
	
	/**
	 * 公章
	 */
	@ApiModelProperty(value = "officialSeals")
	private List<ImageGallery> officialSeals;
	
	/**
	 * 签名
	 */
	@ApiModelProperty(value = "signature")
	private List<ImageGallery> signature;
	
	/**
	 * 人脸
	 */
	@ApiModelProperty(value = "faces")
	private List<ImageGallery> faces;
	
	/**
	 * 岗位
	 */
	@ApiModelProperty(value = "gwList")
	private List<ImageGalleryGw> gwList;
}
