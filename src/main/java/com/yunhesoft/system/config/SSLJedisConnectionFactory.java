package com.yunhesoft.system.config;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.security.KeyPair;
import java.security.KeyStore;
import java.security.Security;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMDecryptorProvider;
import org.bouncycastle.openssl.PEMEncryptedKeyPair;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.bouncycastle.openssl.jcajce.JcePEMDecryptorProviderBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;

import com.yunhesoft.core.common.utils.PassUtils;
import com.yunhesoft.ssl.SslSysKeys;
import com.yunhesoft.tmtools.FileUtils;

import lombok.extern.log4j.Log4j2;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.util.Pool;

/**
 * redis 连接工厂JedisConnectionFactory for https
 * 
 * <AUTHOR>
 *
 */
@Log4j2
@ConditionalOnProperty(value = "spring.redis.ssl", havingValue = "true", matchIfMissing = false)
//@DependsOn({ "SSLInitService" })
@Configuration
@Order(1)
public class SSLJedisConnectionFactory extends JedisConnectionFactory {

	@Value("${spring.redis.host:127.0.0.1}")
	private String host;

	@Value("${spring.redis.port:6379}")
	private Integer port;

	@Value("${spring.redis.password:123456}")
	private String password;

	/**
	 * 重写createRedisPool方法，让其使用SslSocketFactory创建连接池
	 */
	protected Pool<Jedis> createRedisPool() {
		SSLSocketFactory socketFactory = null;
		try {
			// redis服务器上开启TLS后的证书和密码（txt后缀）
			socketFactory = getSocketFactory(getCa_crt(), getRedis_crt(), getRedis_key(), getSslPassword());
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		password = PassUtils.ConfigFileDecrypt(password);
		return new JedisPool(super.getPoolConfig(), host, port, 1000, password, true, socketFactory, null, null);
	}

	/**
	 * 授权ca（服务端 CA 证书）
	 * 
	 * @return
	 */
	private static String getCa_crt() {
		return System.getProperty(SslSysKeys.REDIS_SSL_CA, "classpath:redis-ca.crt");
	}

	/**
	 * 公钥（客户端 CRT 文件）
	 * 
	 * @return
	 */
	private static String getRedis_crt() {
		return System.getProperty(SslSysKeys.REDIS_CLIENT_CERT_CRT, "classpath:redis-redis.crt");
	}

	/**
	 * 私钥（客户端 key 文件
	 * 
	 * @return
	 */
	private static String getRedis_key() {
		return System.getProperty(SslSysKeys.REDIS_CLIENT_CERT_KEY, "classpath:redis-redis.key");
	}

	/**
	 * 密码（redis-ca.txt 文件）
	 * 
	 * @return
	 */
	private static String getSslPassword() {
		String file = System.getProperty(SslSysKeys.REDIS_CA_TXT, "classpath:redis-ca.txt");
		InputStream inputStream = FileUtils.getFileInputStream(file);
		String pass = "";
		try {
			pass = readFromInputStream(inputStream);
		} catch (IOException e) {
			log.error("", e);
		}
		return pass;
	}

	/**
	 * 读文件
	 * 
	 * @param inputStream
	 * @return
	 * @throws IOException
	 */
	private static String readFromInputStream(InputStream inputStream) throws IOException {
		return FileUtils.readFromInputStream(inputStream);
	}

	/**
	 * 创建 SSLSocketFactory 工厂
	 *
	 * @param caCrtFile 服务端 CA 证书
	 * @param crtFile   客户端 CRT 文件
	 * @param keyFile   客户端 Key 文件
	 * @param password  SSL 密码，随机
	 * @return {@link SSLSocketFactory}
	 * @throws Exception 异常
	 */
	public static SSLSocketFactory getSocketFactory(final String caCrtFile, final String crtFile, final String keyFile,
			final String password) throws Exception {
		InputStream caInputStream = null;
		InputStream crtInputStream = null;
		InputStream keyInputStream = null;
		try {
			Security.addProvider(new BouncyCastleProvider());
			CertificateFactory cf = CertificateFactory.getInstance("X.509");
			// load CA certificate
			caInputStream = FileUtils.getFileInputStream(caCrtFile);// new
																	// ClassPathResource(caCrtFile).getInputStream();
			X509Certificate caCert = null;
			while (caInputStream.available() > 0) {
				caCert = (X509Certificate) cf.generateCertificate(caInputStream);
			}
			// load client certificate
			crtInputStream = FileUtils.getFileInputStream(crtFile);// new ClassPathResource(crtFile).getInputStream();
			X509Certificate cert = null;
			while (crtInputStream.available() > 0) {
				cert = (X509Certificate) cf.generateCertificate(crtInputStream);
			}
			// load client private key
			keyInputStream = FileUtils.getFileInputStream(keyFile);// new ClassPathResource(keyFile).getInputStream();
			PEMParser pemParser = new PEMParser(new InputStreamReader(keyInputStream));
			Object object = pemParser.readObject();
			PEMDecryptorProvider decProv = new JcePEMDecryptorProviderBuilder().build(password.toCharArray());
			JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider("BC");
			KeyPair key;
			if (object instanceof PEMEncryptedKeyPair) {
				// System.out.println("Encrypted key - we will use provided password");
				key = converter.getKeyPair(((PEMEncryptedKeyPair) object).decryptKeyPair(decProv));
			} else {
				// System.out.println("Unencrypted key - no password needed");
				key = converter.getKeyPair((PEMKeyPair) object);
			}
			pemParser.close();

			// CA certificate is used to authenticate server
			KeyStore caKs = KeyStore.getInstance(KeyStore.getDefaultType());
			caKs.load(null, null);
			caKs.setCertificateEntry("ca-certificate", caCert);
			TrustManagerFactory tmf = TrustManagerFactory.getInstance("X509");
			tmf.init(caKs);

			// client key and certificates are sent to server so it can authenticate
			// us
			KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());
			ks.load(null, null);
			ks.setCertificateEntry("certificate", cert);
			ks.setKeyEntry("private-key", key.getPrivate(), password.toCharArray(),
					new java.security.cert.Certificate[] { cert });
			KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
			kmf.init(ks, password.toCharArray());

			// finally, create SSL socket factory
			SSLContext context = SSLContext.getInstance("TLSv1.2");
			context.init(kmf.getKeyManagers(), tmf.getTrustManagers(), null);
			return context.getSocketFactory();
		} finally {
			if (null != caInputStream) {
				caInputStream.close();
			}
			if (null != crtInputStream) {
				crtInputStream.close();
			}
			if (null != keyInputStream) {
				keyInputStream.close();
			}
		}
	}
}