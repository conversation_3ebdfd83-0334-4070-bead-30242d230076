package com.yunhesoft.system.kernel.service.impl;

import java.lang.reflect.Field;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.EntityUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.model.Func;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

/**
 * @category 树形实体类持久层操作
 * 
 *           <pre>
 * 			注:
 * 			1.树形实体必须继承自TreeEntity类
 * 			2.实体类必须有一个子节点的字段，名称可自定(建议用children)
 * 			3.如果树形表格数据量特别大时，请将model_id设置为索引
 *           </pre>
 * 
 * <AUTHOR>
 *
 */
@Service
public class TreeServiceImpl {
	/** 默认子节点集合的字段名 */
	public final static String DEF_CHILDREN_FIELD = "children";
	/** 默认的模型ID编号 */
	public final static String DEF_MODEL_ID = "DEFAULT";

	public final static String DEF_ROOT_PID = "root";

	/** 默认父id字段 */
	public final static String DEF_PID = "pid";

	/** 持久层服务 */
	@Autowired
	private EntityServiceImpl dao;

	public <T> T getRoot(Class<T> entityClass) {
		return getRoot(entityClass, DEF_MODEL_ID);
	}

	/**
	 * @category 读取根节点(单点)
	 * @param <T>
	 * @param entityClass 实体类
	 * @param modelId     模型ID
	 * @return
	 */
	public <T> T getRoot(Class<T> entityClass, String modelId) {
		return getRoot(null, entityClass, modelId);
	}

	/**
	 * @category 读取根节点(单点)
	 * @param <T>
	 * @param entityClass 实体类
	 * @param modelId     模型ID
	 * @return
	 */
	public <T> T getRoot(String tenant_id, Class<T> entityClass, String modelId) {
		T rootNode = null;
		// 查找根节点
		Where where = Where.create();
		where.eq("PID", DEF_ROOT_PID);
		where.eq("MODEL_ID", modelId);

		if (StringUtils.isNotEmpty(tenant_id)) {
			rootNode = dao.rawQueryObjectByWhereWithTenant(tenant_id, entityClass, where);
			// Where.create("PID=? and MODEL_ID=?", DEF_ROOT_PID, modelId)
		} else {
			rootNode = dao.rawQueryObjectByWhere(entityClass, where);
			// Where.create("PID=? and MODEL_ID=?", DEF_ROOT_PID, modelId)
		}
		return rootNode;
		/*
		 * if (rootNode == null) { throw new RuntimeException("树形根未找到"); } else { return
		 * rootNode; }
		 */
	}

	public <T> T getRootTree(Class<T> entityClass) {
		return getRootTree(entityClass, DEF_MODEL_ID);
	}

	public <T> T getRootTree(Class<T> entityClass, String modelId) {
		return getRootTree(entityClass, DEF_CHILDREN_FIELD, modelId);
	}

	public <T> T getRootTree(Class<T> entityClass, String modelId, List<T> rows) {
		return getRootTree(null, entityClass, modelId, rows);
	}

	public <T> T getRootTree(String tenant_id, Class<T> entityClass, String modelId, List<T> rows) {
		try {
			Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
			T root = getRoot(tenant_id, entityClass, modelId);
			return wrapTree(fields, DEF_CHILDREN_FIELD, root, rows);
		} catch (IllegalArgumentException | IllegalAccessException e) {
			throw new RuntimeException(e);
		}
	}

	public <T> T getRootTree(Class<T> entityClass, T root, List<T> rows) {
		try {
			if (root == null) {
				return null;
			}
			Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
			return wrapTree(fields, DEF_CHILDREN_FIELD, root, rows);
		} catch (IllegalArgumentException | IllegalAccessException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * @category 读取根树形
	 * @param <T>
	 * @param entityClass
	 * @param childrenField
	 * @param modelId
	 * @return
	 */
	public <T> T getRootTree(Class<T> entityClass, String childrenField, String modelId) {
		return getRootTree(null, entityClass, childrenField, modelId);
	}

	/**
	 * @category 读取根树形
	 * @param <T>
	 * @param entityClass
	 * @param childrenField
	 * @param modelId
	 * @return
	 */
	public <T> T getRootTree(String tenant_id, Class<T> entityClass, String childrenField, String modelId) {
		try {
			Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
			T root = getRoot(tenant_id, entityClass, modelId);
			Where where = Where.create();
			where.ne("PID", DEF_ROOT_PID);
			where.eq("MODEL_ID", modelId);
			where.eq("DEL_FLAG", 0);// 不显示删除的项目
			List<T> rows = null;
			if (StringUtils.isNotEmpty(tenant_id)) {
				rows = dao.rawQueryListByWhereWithTenant(tenant_id, entityClass, where, Order.create("LFT", "asc"));
			} else {
				rows = dao.queryList(entityClass, where, Order.create("LFT", "asc"));
			}
			return wrapTree(fields, childrenField, root, rows);
		} catch (IllegalArgumentException | IllegalAccessException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * @category 读取节点(单节点)
	 * @param <T>
	 * @param entityClass 实体类
	 * @param modelId     模型ID
	 * @param id          节点ID
	 * @return
	 */
	public <T> T getNode(Class<T> entityClass, String id) {
		return getNode(entityClass, DEF_MODEL_ID, id);
	}

	public <T> T getNode(Class<T> entityClass, String modelId, String id) {
		return getNode(null, entityClass, modelId, id);
	}

	public <T> T getNode(String tenant_id, Class<T> entityClass, String modelId, String id) {
		T node = null;
		Where where = Where.create();
		where.eq("DEL_FLAG", 0);
		where.eq("MODEL_ID", modelId);
		where.eq("ID", id);

//		Where where = Where.create("MODEL_ID=? and DEL_FLAG = 0 and ID=?", modelId, id);
		if (StringUtils.isNotEmpty(tenant_id)) {
			node = dao.rawQueryObjectByWhereWithTenant(tenant_id, entityClass, where);
		} else {
			node = dao.rawQueryObjectByWhere(entityClass, where);
		}
		if (node == null) {
			throw new RuntimeException("节点未找到,请联系系统管理员");
		} else {
			return node;
		}
	}

	/**
	 * @category 读取节点树形
	 * @param <T>
	 * @param entityClass   实体类
	 * @param childrenField 子节点列表
	 * @param modelId       模型id
	 * @param id            节点id
	 * @return
	 */
	public <T> T getNodeTree(Class<T> entityClass, String id) {
		return getNodeTree(entityClass, DEF_MODEL_ID, id);
	}

	public <T> T getNodeTree(Class<T> entityClass, String modelId, String id) {
		return getNodeTree(entityClass, DEF_CHILDREN_FIELD, modelId, id);
	}

	public <T> T getNodeTree(Class<T> entityClass, String childrenField, String modelId, String id) {
		return getNodeTree(null, entityClass, childrenField, modelId, id);
	}

	/**
	 * @category 读取节点树形
	 * @param <T>
	 * @param entityClass   实体类
	 * @param childrenField 子节点列表
	 * @param modelId       模型id
	 * @param id            节点id
	 * @return
	 */
	public <T> T getNodeTree(String tenant_id, Class<T> entityClass, String childrenField, String modelId, String id) {
		try {
			Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
			T node = getNode(tenant_id, entityClass, modelId, id);

			Where where = Where.create().ne("PID", DEF_ROOT_PID).eq("DEL_FLAG", 0).eq("MODEL_ID", modelId);
			where.between("LFT", getLft(fields, node), getRgt(fields, node));
			List<T> rows;
			if (StringUtils.isNotEmpty(tenant_id)) {
				rows = dao.rawQueryListByWhereWithTenant(tenant_id, entityClass, where, null);
			} else {
				rows = dao.queryList(entityClass, where);
			}
			return wrapTree(fields, childrenField, node, rows);
		} catch (IllegalArgumentException | IllegalAccessException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 根据父id获取其子节点
	 * 
	 * @param <T>
	 * @param entityClass 实体类对象
	 * @param modelId     模块编码
	 * @param pid         父id
	 * @return
	 */
	public <T> List<T> getChildNodes(Class<T> entityClass, String modelId, String pid) {
		return getChildNodes(null, entityClass, modelId, pid);
	}

	public <T> List<T> getChildNodes(String tenant_id, Class<T> entityClass, String modelId, String pid) {
		List<T> rows = null;
		try {
			Where where = Where.create();
			where.eq("MODEL_ID", modelId);
			where.eq("PID", pid);
			where.eq("DEL_FLAG", 0);
			Order order = new Order();
			order.order("LFT", "ASC");
			if (StringUtils.isNotEmpty(tenant_id)) {
				rows = dao.rawQueryListByWhereWithTenant(tenant_id, entityClass, where, order);
			} else {
				rows = dao.queryList(entityClass, where, order);
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return rows;
	}

	/**
	 * @category 保存根树形节点(默认状态)
	 * @param <T>
	 * @param rootNode
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	public <T> T saveRoot(T rootNode) {
		return saveRoot(rootNode, DEF_MODEL_ID);
	}

	public <T> T saveRoot(T rootNode, String modelId) {
		try {
			return saveRoot(DEF_CHILDREN_FIELD, modelId, rootNode);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * @category 保存根树形节点(root)
	 * 
	 *           <pre>
	 * 		注：只有根树形才能用些保存
	 *         	节点的操作请用插入，追加等操作
	 *           </pre>
	 * 
	 * @param <T>
	 * @param childrenField 子节点字段名称
	 * @param modelId       模型Id
	 * @param rootNode      树形根节点
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	@Transactional
	public <T> T saveRoot(String childrenField, String modelId, T rootNode) {
		try {
			@SuppressWarnings("unchecked")
			Class<T> entityClass = (Class<T>) rootNode.getClass();
			Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
			if (ObjUtils.isEmpty(modelId)) {
				throw new RuntimeException("modelId不允许为空");
			}
			// 设置节点及子节点的左右索引值
			resetNodeIndex(fields, childrenField, modelId, rootNode, 0);
			// 生成节点的二级列表
			List<T> list = tree2list(fields, childrenField, modelId, rootNode);
			dao.rawSaveBatch(list);
			return rootNode;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public <T> T saveNode(Class<T> entityClass, String modelId, T node) {
		return saveNode(null, entityClass, modelId, node);
	}

	public <T> T saveNode(String tenant_id, Class<T> entityClass, String modelId, T node) {
		return updateNode(tenant_id, entityClass, DEF_CHILDREN_FIELD, modelId, node);
	}

	/**
	 * @category 更新单一节点
	 * @param <T>
	 * @param childrenField
	 * @param modelId
	 * @param node
	 * @return
	 */
	public <T> T updateNode(Class<T> entityClass, T node) {
		return updateNode(entityClass, DEF_MODEL_ID, node);
	}

	public <T> T updateNode(Class<T> entityClass, String modelId, T node) {
		return updateNode(entityClass, DEF_CHILDREN_FIELD, modelId, node);
	}

	public <T> T updateNode(Class<T> entityClass, String childrenField, String modelId, T node) {
		return updateNode(null, entityClass, childrenField, modelId, node);
	}

	@Transactional
	public <T> T updateNode(String tenant_id, Class<T> entityClass, String childrenField, String modelId, T node) {
		Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
		try {
			String nodeId = getId(fields, node);
			T oldNode = getNode(tenant_id, entityClass, modelId, nodeId);
			// 判断是否是移动节点
			// 比对原节点与新节点的父对象是否有变化，如果有变化，要采用移动操作,否则更新操作
			String oldPID = getPid(fields, oldNode, DEF_PID);
			String newPID = getPid(fields, node, DEF_PID);
			// String newID = getId(fields, node);
			if (!oldPID.equalsIgnoreCase(newPID)) {// 父节点移动
				T t = this.moveIn(tenant_id, entityClass, fields, modelId, node);
				int i = 0;
				if (StringUtils.isNotEmpty(tenant_id)) {
					i = dao.rawUpdateByIdWithTenant(tenant_id, t, false);
				} else {
					i = dao.rawUpdateById(t);
				}
				if (i > 0) {
					// moveIn(entityClass, modelId, oldPID, newID);
					this.correctTree(tenant_id, entityClass, childrenField, modelId);// 重新整理左右值
				}
			} else {// 正常修改属性
				if (StringUtils.isNotEmpty(tenant_id)) {
					dao.rawUpdateByIdWithTenant(tenant_id, node, false);
				} else {
					dao.rawUpdateById(node);
				}
			}
			return node;
		} catch (IllegalArgumentException | IllegalAccessException | InstantiationException | ClassNotFoundException
				| SQLException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 移动节点
	 *
	 * @param <T>
	 * @param entityClass
	 * @param fields
	 * @param modelId
	 * @param node
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
//	private <T> T moveIn(Class<T> entityClass, Map<String, Field> fields, String modelId, T node)
//			throws IllegalArgumentException, IllegalAccessException {
//		return moveIn(null, entityClass, fields, modelId, node);
//	}

	/**
	 * 移动节点
	 * 
	 * @param <T>
	 * @param entityClass
	 * @param fields
	 * @param modelId
	 * @param node
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> T moveIn(String tenant_id, Class<T> entityClass, Map<String, Field> fields, String modelId, T node)
			throws IllegalArgumentException, IllegalAccessException {
		List<T> list = this.getChildNodes(tenant_id, entityClass, modelId, this.getPid(fields, node, DEF_PID));
		long newLft = 1;
		if (StringUtils.isNotEmpty(list)) {
			T lastNode = list.get(list.size() - 1);
			newLft = this.getLft(fields, lastNode);
			newLft = newLft + 1;
		}
		this.setLft(fields, node, newLft);
		return node;
	}

	public <T> T moveIn(Class<T> entityClass, String posNodeId, String desNodeId) {
		return moveIn(entityClass, DEF_MODEL_ID, posNodeId, desNodeId);
	}

	public <T> T moveIn(Class<T> entityClass, String modelId, String posNodeId, String desNodeId) {
		return moveIn(entityClass, DEF_CHILDREN_FIELD, modelId, posNodeId, desNodeId);
	}

	/**
	 * @category 移到节点到指定节点下面
	 * @param <T>
	 * @param entityClass   实体类
	 * @param childrenField 子节点字段名称
	 * @param modelId       模型ID
	 * @param posNodeId     位置节点
	 * @param desNodeId     目标节点
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 */
	@Transactional
	public <T> T moveIn(Class<T> entityClass, String childrenField, String modelId, String posNodeId,
			String desNodeId) {
		try {
			/*
			 * String tableName = EntityUtils.tableName(entityClass); Map<String, Field>
			 * fields = ObjUtils.reflectFieldMix(entityClass); // 读取要移动的节点 T posNode =
			 * getNode(entityClass, modelId, posNodeId); T desNode =
			 * getNodeTree(entityClass, childrenField, modelId, desNodeId); //
			 * 将目标节点的父ID设置为位置节点的同级 setPid(fields, desNode, getId(fields, posNode)); List<T>
			 * desNodes = tree2list(fields, childrenField, modelId, desNode); //
			 * 重置涉及到的左右值-删除操作 long idx = getLft(fields, desNode); String lftSql = "update "
			 * + tableName +
			 * " set lft = lft - ? where model_id=? and lft > ? and del_flag=0"; String
			 * rgtSql = "update " + tableName +
			 * " set rgt = rgt - ? where model_id=? and rgt > ? and del_flag=0";
			 * dao.rawUpdate(lftSql, desNodes.size() * 2, modelId, idx);
			 * dao.rawUpdate(rgtSql, desNodes.size() * 2, modelId, idx); // 重置涉及到的左右值-追加操作
			 * idx = dao.rawQueryObjectByWhere(Long.class, entityClass,
			 * Select.create("rgt"), Where.create("model_id=? and id=?", modelId,
			 * posNodeId)); lftSql = "update " + tableName +
			 * " set lft = lft + ? where model_id=? and lft >= ? and del_flag=0"; rgtSql =
			 * "update " + tableName +
			 * " set rgt = rgt + ? where model_id=? and rgt >= ? and del_flag=0";
			 * dao.rawUpdate(lftSql, desNodes.size() * 2, modelId, idx);
			 * dao.rawUpdate(rgtSql, desNodes.size() * 2, modelId, idx); // 更新移到的目标节点的左右值
			 * resetNodeIndex(fields, childrenField, modelId, desNode, idx - 1);
			 * dao.rawSaveBatch(desNodes); return desNode;
			 */
			return null;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public <T> T move(Class<T> entityClass, String posNodeId, String desNodeId, boolean before) {
		return move(entityClass, DEF_MODEL_ID, posNodeId, desNodeId, before);
	}

	/*
	 * public <T> T move(Class<T> entityClass, String modelId, String posNodeId,
	 * String desNodeId, boolean before) { return move(entityClass,
	 * DEF_CHILDREN_FIELD, modelId, posNodeId, desNodeId, before); }
	 */

	/**
	 * @category 移到节点到指定节点左或右
	 * @param <T>
	 * @param entityClass   实体类
	 * @param childrenField 子节点字段名称
	 * @param modelId       模型ID
	 * @param posNodeId     位置节点
	 * @param desNodeId     目标节点
	 * @param before        true 之前 false 之后
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 */
	@Transactional
	public <T> T move(Class<T> entityClass, String modelId, String posNodeId, String desNodeId, boolean before) {
		return move(null, entityClass, modelId, posNodeId, desNodeId, before, null);
	}

	public <T> T move(String tenant_id, Class<T> entityClass, String modelId, String posNodeId, String desNodeId,
			boolean before) {
		return this.move(tenant_id, entityClass, modelId, posNodeId, desNodeId, before, null);
	}

	/**
	 * @category 移到节点到指定节点左或右
	 * @param <T>
	 * @param entityClass   实体类
	 * @param childrenField 子节点字段名称
	 * @param modelId       模型ID
	 * @param posNodeId     位置节点
	 * @param desNodeId     目标节点
	 * @param before        true 之前 false 之后
	 * @param orderField    排序字
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 */
	@Transactional
	public <T> T move(String tenant_id, Class<T> entityClass, String modelId, String posNodeId, String desNodeId,
			boolean before, String orderField) {
		try {
			Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
			T posNode = getNode(tenant_id, entityClass, modelId, posNodeId);// 读取要移动的节点
			T desNode = null;
			List<T> list = new ArrayList<T>();
			boolean correct = false; // 是否重新整理序号
			if (StringUtils.isNotEmpty(orderField)) {// 计算排序值
				String pid = this.getPid(fields, posNode, DEF_PID);
				List<T> nodes = this.getChildNodes(tenant_id, entityClass, modelId, pid);
				if (StringUtils.isNotEmpty(nodes)) {
					int i = 0;
					Long lft = 0L;
					Long blft = 0L;
					int pos_order = 0;
					int des_order = 0;
					for (T node : nodes) {
						i++;
						String nodeid = this.getId(fields, node);
						this.setOrderNum(fields, node, orderField, i);
						if (nodeid.equals(posNodeId)) {
							posNode = node;
							lft = this.getLft(fields, node);
							pos_order = i;
							continue;
						}
						if (nodeid.equals(desNodeId)) {
							desNode = node;
							blft = this.getLft(fields, node);
							des_order = i;
							continue;
						}
						list.add(node);
					}

					if (before) {
						if (posNode != null) {
							this.setLft(fields, posNode, lft < blft ? lft : blft);// 互换left值
						}
						if (desNode != null) {
							this.setLft(fields, desNode, lft > blft ? lft : blft);
						}
					} else {
						if (posNode != null) {
							this.setLft(fields, posNode, lft > blft ? lft : blft);// 互换left值
						}
						if (desNode != null) {
							this.setLft(fields, desNode, lft < blft ? lft : blft);
						}
					}
					// 排序值互换
					if (posNode != null) {
						this.setOrderNum(fields, posNode, orderField, des_order);
						list.add(posNode);
					}
					if (desNode != null) {
						this.setOrderNum(fields, desNode, orderField, pos_order);
						list.add(desNode);
					}
				}
			} else {
				correct = true;
				desNode = getNode(tenant_id, entityClass, modelId, desNodeId);
				long lft = this.getLft(fields, posNode);
				long blft = this.getLft(fields, desNode);
				if (before) {
					this.setLft(fields, posNode, lft < blft ? lft : blft);// 互换left值
					this.setLft(fields, desNode, lft > blft ? lft : blft);
				} else {
					this.setLft(fields, posNode, lft > blft ? lft : blft);// 互换left值
					this.setLft(fields, desNode, lft < blft ? lft : blft);
				}

				this.setLft(fields, posNode, blft);// 互换left值
				this.setLft(fields, desNode, lft);
				list.add(posNode);
				list.add(desNode);
			}

			if (StringUtils.isNotEmpty(tenant_id)) {
//				dao.updateByIdBatchWithTenant(tenant_id, list, 500);
				dao.updateBatchIncludeNullWithTenant(tenant_id, list, 500);
				if (correct) {
					this.correctTree(tenant_id, entityClass, DEF_CHILDREN_FIELD, modelId);// 重新整理序号
				}

			} else {
				dao.updateByIdBatch(list);
				if (correct) {
					this.correctTree(entityClass, modelId);// 重新整理序号
				}
			}
			return desNode;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 设置排序值
	 * 
	 * @param <T>
	 * @param fields
	 * @param node
	 * @param orderField 排序字段
	 * @param orderNum   排序值
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> void setOrderNum(Map<String, Field> fields, T node, String orderField, int orderNum)
			throws IllegalArgumentException, IllegalAccessException {
		if (StringUtils.isNotEmpty(orderField)) {
			this.setValue(fields, node, orderField, orderNum);
		}
	}

	/*
	 * public <T> T insert1(Class<T> entityClass, T posNode, T desNode, boolean
	 * before) { return insert(entityClass, DEF_MODEL_ID, posNode, desNode, before);
	 * }
	 */

	/*
	 * public <T> T insert(Class<T> entityClass, String modelId, T posNode, T
	 * desNode, boolean before) { return insert(entityClass, DEF_CHILDREN_FIELD,
	 * modelId, posNode, desNode, before); }
	 */
	/**
	 * @category 插入节点
	 * @param <T>
	 * @param entityClass   实体类
	 * @param childrenField 子节点字段名称
	 * @param modelId       模型ID
	 * @param posNode       位置节点
	 * @param desNode       目标节点
	 * @param before        true 之前 false 之后
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	/*
	 * @Transactional public <T> T insert(Class<T> entityClass, String
	 * childrenField, String modelId, T posNode, T desNode, boolean before) { try {
	 * Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass); String
	 * tableName = EntityUtils.tableName(entityClass); // 更新表中的左右值 String lftSql =
	 * "update " + tableName + " set lft = lft + ? where model_id=? and del_flag=0";
	 * String rgtSql = "update " + tableName +
	 * " set rgt = rgt + ? where model_id=? and del_flag=0"; // 得到位置节点的左，右值 long idx
	 * = 0; if (before) { idx = getLft(fields, posNode); lftSql += " and lft >= ? ";
	 * rgtSql += " and rgt >= ? "; // 根据node的左右索引值，设置子节点的左右索引值
	 * resetNodeIndex(fields, childrenField, modelId, desNode, idx - 1); } else {
	 * idx = getRgt(fields, posNode); lftSql += " and lft > ? "; rgtSql +=
	 * " and rgt > ? "; // 根据node的左右索引值，设置子节点的左右索引值 resetNodeIndex(fields,
	 * childrenField, modelId, desNode, idx); } // 插入节点记录 List<T> desNodes =
	 * tree2list(fields, childrenField, modelId, desNode); int nodeCount =
	 * desNodes.size(); // 更新未涉及到的其它子节点的rgt值 dao.rawUpdate(lftSql, nodeCount * 2,
	 * modelId, idx); dao.rawUpdate(rgtSql, nodeCount * 2, modelId, idx);
	 * dao.rawInsertBatch(desNodes); return desNode; } catch (Exception e) { throw
	 * new RuntimeException(e); } }
	 */

	/**
	 * @category 向节点追加子节点
	 * 
	 * @param <T>
	 * @param entityClass 实体类
	 * @param nodeId      节点id
	 * @param childNode   子节点
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 */
	public <T> T append(Class<T> entityClass, T node, T childNode) {
		return append(entityClass, DEF_CHILDREN_FIELD, DEF_MODEL_ID, node, childNode);
	}

	public <T> T append(Class<T> entityClass, String nodeId, T childNode) {
		return append(entityClass, DEF_MODEL_ID, nodeId, childNode);
	}

	public <T> T append(Class<T> entityClass, String modelId, String nodeId, T childNode) {
		return append(entityClass, DEF_CHILDREN_FIELD, modelId, getNode(entityClass, modelId, nodeId), childNode);
	}

	public <T> T append(String tenant_id, Class<T> entityClass, String modelId, String nodeId, T childNode) {
		return append(tenant_id, entityClass, DEF_CHILDREN_FIELD, modelId,
				getNode(tenant_id, entityClass, modelId, nodeId), childNode);
	}

	/**
	 * 设置节点属性
	 * 
	 * @param fields
	 * @param node
	 * @param modelId
	 * @param lft
	 * @param rgt
	 */
	private <T> void setNode(Map<String, Field> fields, T node, String modelId, long lft, long rgt)
			throws IllegalArgumentException, IllegalAccessException {
		this.setLft(fields, node, lft);
		this.setRgt(fields, node, rgt);
		this.setModelId(fields, node, modelId);
		/*
		 * if (createTime) { this.setCreateTime(fields, node); } if (updateTime) {
		 * this.setUpdateTime(fields, node); }
		 */
	}

	public <T> T append(Class<T> entityClass, String childrenField, String modelId, T node, T childNode) {
		return append(null, entityClass, childrenField, modelId, node, childNode);
	}

	/**
	 * 添加子节点
	 * 
	 * @param <T>
	 * @param entityClass   实体对象
	 * @param childrenField
	 * @param modelId       模块编码
	 * @param node          父节点对象
	 * @param childNode     子节点对象
	 * @return
	 */
	@Transactional
	public <T> T append(String tenant_id, Class<T> entityClass, String childrenField, String modelId, T node,
			T childNode) {
		try {
			Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
			String tableName = EntityUtils.tableName(entityClass);
			// 得到位置节点的左，右值
			long myRight = this.getRgt(fields, node);
			String sql = "";
			// 新增的节点就是左右值就是父节点的右值和右值+1，而影响到的节点值都是大于等于父节点的右值，都是原先的值加2。
			long newRight = myRight;
			long newLeft = myRight + 1;
			if (StringUtils.isNotEmpty(tenant_id)) {
				// 指定租户id
				sql = MultiTenantUtils.NoTenantTag + "UPDATE " + tableName
						+ " SET RGT = RGT + 2 WHERE RGT >= ? and DEL_FLAG=0 and MODEL_ID=? and TENANT_ID=?";
				dao.rawUpdate(sql, myRight, modelId, tenant_id);// update  tree  set  rgt = rgt + 2   where  rgt >= @rgt
				sql = MultiTenantUtils.NoTenantTag + "UPDATE " + tableName
						+ " SET LFT = LFT + 2 WHERE LFT >= ? and DEL_FLAG=0 and MODEL_ID=? and TENANT_ID=?";
				dao.rawUpdate(sql, myRight, modelId, tenant_id);// update  tree  set  lft = lft + 2   where  lft >= @rgt
			} else {
				// 按当前租户
				sql = "UPDATE " + tableName + " SET RGT = RGT + 2 WHERE RGT >= ? and DEL_FLAG=0 and MODEL_ID=?";
				dao.rawUpdate(sql, myRight, modelId);// update  tree  set  rgt = rgt + 2   where  rgt >= @rgt
				sql = "UPDATE " + tableName + " SET LFT = LFT + 2 WHERE LFT >= ? and DEL_FLAG=0 and MODEL_ID=?";
				dao.rawUpdate(sql, myRight, modelId);// update  tree  set  lft = lft + 2   where  lft >= @rgt
			}
			newLeft = myRight;
			newRight = myRight + 1;
			this.setNode(fields, childNode, modelId, newLeft, newRight);
			if (StringUtils.isNotEmpty(tenant_id)) {
				// 指定租户id
				dao.rawInsertWithTenant(tenant_id, childNode);
			} else {
				// 按当前租户
				dao.insert(childNode);
			}
			// 根据node的左右索引值，设置子节点的左右索引值
			// resetNodeIndex(fields, childrenField, modelId, childNode, idx - 1); // 插入节点记录
			// List<T> desNodes = tree2list(fields, childrenField, modelId, childNode); //
			// 更新未涉及到的其它子节点的rgt值
			// String lftSql = "update " + tableName + " set lft = lft + ? where model_id=?
			// and lft >= ? and del_flag=0";
			// String rgtSql = "update " + tableName + " set rgt = rgt + ? where model_id=?
			// and rgt >= ? and del_flag=0";
			// dao.rawUpdate(lftSql, desNodes.size() * 2, modelId, idx);
			// dao.rawUpdate(rgtSql, desNodes.size() * 2, modelId, idx); // 保存子节点
			// dao.rawSaveBatch(desNodes);
			return childNode;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public <T> int[] delete(Class<T> entityClass, T node) {
		return delete(entityClass, DEF_CHILDREN_FIELD, DEF_MODEL_ID, node);
	}

	public <T> int[] delete(Class<T> entityClass, boolean logicDelete, String modelId, String id) {
		return delete(entityClass, DEF_CHILDREN_FIELD, modelId,
				getNodeTree(entityClass, DEF_CHILDREN_FIELD, modelId, id), logicDelete);
	}

	public <T> int[] delete(String tenant_id, Class<T> entityClass, boolean logicDelete, String modelId, String id) {
		return delete(tenant_id, entityClass, DEF_CHILDREN_FIELD, modelId,
				getNodeTree(tenant_id, entityClass, DEF_CHILDREN_FIELD, modelId, id), logicDelete);
	}

	public <T> int[] delete(Class<T> entityClass, String modelId, String id) {
		return delete(entityClass, modelId, getNodeTree(entityClass, DEF_CHILDREN_FIELD, modelId, id));
	}

	public <T> int[] delete(Class<T> entityClass, String modelId, T node) {
		return delete(entityClass, DEF_CHILDREN_FIELD, modelId, node);
	}

	public <T> int[] delete(Class<T> entityClass, String childrenField, String modelId, T node, boolean logicDelete) {
		return delete(null, entityClass, childrenField, modelId, node, logicDelete);
	}

	/**
	 * @category 删除节点
	 * @param <T>
	 * @param entityClass   实体类
	 * @param childrenField 子节点字段名称
	 * @param modelId       模型ID
	 * @param node          节点
	 * @param logicDelete   是否为逻辑删除
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	@Transactional
	public <T> int[] delete(String tenant_id, Class<T> entityClass, String childrenField, String modelId, T node,
			boolean logicDelete) {
		try {
			int[] rtn = new int[3];
			Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
			String tableName = EntityUtils.tableName(entityClass);
			// 得到位置节点的左，右值
			long lft = getLft(fields, node);
			long rgt = getRgt(fields, node);
			if (logicDelete) {// 逻辑删除，更新删除标识
				String sql = MultiTenantUtils.NoTenantTag + "update " + tableName + " set DEL_FLAG=1 where ID=?";
				rtn[0] = dao.rawUpdate(sql, getId(fields, node));
			} else {// 真删除
				String sql = MultiTenantUtils.NoTenantTag + "delete from " + tableName + "  where ID=?";
				rtn[0] = dao.rawDelete(sql, getId(fields, node));
			}
			if (StringUtils.isNotEmpty(tenant_id)) {
				// 指定租户id
				String delSql = MultiTenantUtils.NoTenantTag + "update " + tableName
						+ " set DEL_FLAG=1 where LFT > ? and RGT <? and MODEL_ID=? and TENANT_ID=?";
				rtn[1] = 1;// rtn[1] = dao.rawUpdate(rgtSql, m, rgt, modelId);
				rtn[2] = dao.rawUpdate(delSql, lft, rgt, modelId, tenant_id);
			} else {
				// 当前租户
				String delSql = "update " + tableName + " set DEL_FLAG=1 where LFT > ? and RGT <? and MODEL_ID=?";
				rtn[1] = 1;// rtn[1] = dao.rawUpdate(rgtSql, m, rgt, modelId);
				rtn[2] = dao.rawUpdate(delSql, lft, rgt, modelId);
			}
			this.correctTree(tenant_id, entityClass, childrenField, modelId);// 重新整理左右值
			return rtn;// dao.rawDeleteByIdBatch(nodes);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public <T> int[] delete(Class<T> entityClass, String childrenField, String modelId, T node) {
		return this.delete(entityClass, childrenField, modelId, node, false);
	}

	public <T> int[] deleteLogic(Class<T> entityClass, T node) throws IllegalArgumentException, IllegalAccessException {
		return deleteLogic(entityClass, DEF_CHILDREN_FIELD, DEF_MODEL_ID, node);
	}

	/**
	 * @category 逻辑删除
	 * @param <T>
	 * @param entityClass
	 * @param childrenField
	 * @param modelId
	 * @param node
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	@Transactional
	public <T> int[] deleteLogic(Class<T> entityClass, String childrenField, String modelId, T node)
			throws IllegalArgumentException, IllegalAccessException {
		return this.delete(entityClass, childrenField, modelId, node, true);
	}

	/**
	 * @category 查询树形
	 * @param <T>
	 * @param entityClass   实体类
	 * @param childrenField 子节点的字段名称
	 * @param modelId       模型IDID
	 * @param nodeId        读取的树形ID
	 * @param layers        子节点的层数
	 * @return
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 */
//	public <T> T query(Class<T> entityClass, String childrenField, String modelId, String nodeId, int layers) {
//		try {
//			return queryLogic(entityClass, childrenField, modelId, nodeId, 0);
//		} catch (Exception e) {
//			throw new RuntimeException(e);
//		}
//	}

	/**
	 * @category 查询树形
	 * @param <T>
	 * @param entityClass   实体类
	 * @param childrenField 子节点的字段名称
	 * @param modelId       模型IDID
	 * @param nodeId        读取的树形ID
	 * @param layers        子节点的层数
	 * @param delFlag       逻辑删除标识
	 * @return
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 */
//	public <T> T queryLogic(Class<T> entityClass, String childrenField, String modelId, String nodeId, int delFlag)
//			throws IllegalArgumentException, IllegalAccessException, InstantiationException, ClassNotFoundException,
//			SQLException {
//		Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
//		if (ObjUtils.isEmpty(modelId)) {
//			throw new RuntimeException("模型ID不允许为空");
//		}
//		T node = dao.rawQueryObjectByColumn(entityClass, "id", nodeId);
//		if (node == null) {
//			throw new RuntimeException("树形节点未找到");
//		}
//		// 根据父节点，左值进行排序
//		Where where = Where.create("del_flag=? and model_id=? and lft between ? and ?").order("pid").order("lft");
//		List<T> nodeList = dao.rawQueryListByWhere(entityClass, where, delFlag, modelId, getLft(fields, node),
//				getRgt(fields, node));
//		wrapTree(fields, childrenField, node, nodeList);
//		return node;
//	}

	/**
	 * @category 读取单节点_无子节点
	 * @param <T>
	 * @param entityClass 实体类
	 * @param modelId     模型id
	 * @param nodeId      节点ID
	 * @return
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 */
//	public <T> T queryAlone(Class<T> entityClass, String modelId, String nodeId)
//			throws InstantiationException, IllegalAccessException, ClassNotFoundException, SQLException {
//		return dao.rawQueryObjectByWhere(entityClass, Where.create("model_id=? and id=? and del_flag=0"), modelId,
//				nodeId);
//	}

	/**
	 * @category 修正根树形rootTree的左右值
	 * @param <T>
	 * @param entityClass 实体类
	 * @param modelId     模型Id
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 */
	public <T> int correctTree(Class<T> entityClass, String modelId) throws IllegalArgumentException,
			IllegalAccessException, InstantiationException, ClassNotFoundException, SQLException {
		return correctTree(entityClass, DEF_CHILDREN_FIELD, modelId);
	}

	/**
	 * @category 修正根树形rootTree的左右值
	 * @param <T>
	 * @param entityClass
	 * @param childrenField
	 * @param modelId
	 * @param nodeId
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws InstantiationException
	 */
	public <T> int correctTree(Class<T> entityClass, String childrenField, String modelId)
			throws IllegalArgumentException, IllegalAccessException, InstantiationException, ClassNotFoundException,
			SQLException {
		return correctTree(null, entityClass, childrenField, modelId);
	}

	@Transactional
	public <T> int correctTree(String tenant_id, Class<T> entityClass, String childrenField, String modelId)
			throws IllegalArgumentException, IllegalAccessException, InstantiationException, ClassNotFoundException,
			SQLException {
		Map<String, Field> fields = ObjUtils.reflectFieldMix(entityClass);
		if (ObjUtils.isEmpty(modelId)) {
			throw new RuntimeException("模型ID不允许为空");
		}
		// 查找根节点
		T node = getRoot(tenant_id, entityClass, modelId);
		// 根据父节点，左值进行排序
		Where where = Where.create();
		where.eq("DEL_FLAG", 0);
		where.ne("PID", "root");
		where.eq("MODEL_ID", modelId);

//		Where where = Where.create("del_flag=0 and pid <> 'root' and model_id=?", modelId);
		Order order = Order.create("PID").order("LFT");
		List<T> nodeList = null;
		if (StringUtils.isNotEmpty(tenant_id)) {
			nodeList = dao.rawQueryListByWhereWithTenant(tenant_id, entityClass, where, order);
		} else {
			nodeList = dao.rawQueryListByWhere(entityClass, where, order);
		}
		wrapTree(fields, childrenField, node, nodeList);
		resetNodeIndex(fields, childrenField, modelId, node, 0);
		setLft(fields, node, 1);
		setRgt(fields, node, (nodeList.size() + 1) * 2);
		if (StringUtils.isNotEmpty(tenant_id)) {
			dao.rawUpdateByIdWithTenant(tenant_id, node, false);
//			dao.updateByIdBatchWithTenant(tenant_id, nodeList, 500);
			dao.updateBatchIncludeNullWithTenant(tenant_id, nodeList, 500);
		} else {
			dao.rawUpdateById(node);
			dao.updateByIdBatch(nodeList);
		}
		return nodeList.size();
	}

	/**
	 * @category 重置节点及子节点的左、右值
	 * @param <T>
	 * @param fields
	 * @param childrenField 子节点集合的字段名称
	 * @param modelId       模型ID
	 * @param node          节点
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> long resetNodeIndex(Map<String, Field> fields, String childrenField, String modelId, T node, long idx)
			throws IllegalArgumentException, IllegalAccessException {
		setModelId(fields, node, modelId);
		idx++;
		List<T> children = getChildren(fields, childrenField, node);
		setLft(fields, node, idx);
		idx = resetNodeIndex(fields, childrenField, modelId, children, idx);
		idx++;
		setRgt(fields, node, idx);
		return idx;
	}

	/**
	 * @category 重置节点及子节点的左、右值
	 * @param <T>
	 * @param fields        字段映射
	 * @param childrenField 子节点字段名称
	 * @param children      子节点列表
	 * @param idx           上一个左值
	 * @return 返回最后一个索引
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> long resetNodeIndex(Map<String, Field> fields, String childrenField, String modelId, List<T> children,
			long idx) throws IllegalArgumentException, IllegalAccessException {
		if (children != null) {
			for (T child : children) {
				idx++;
				EntityUtils.setValue(fields.get("lft"), child, idx);
				idx = resetNodeIndex(fields, childrenField, modelId, getChildren(fields, childrenField, child), idx);
				idx++;
				EntityUtils.setValue(fields.get("rgt"), child, idx);
			}
		}
		return idx;
	}

	/**
	 * @category 树形转换成List<T>对象，用于保存
	 * @param <T>
	 * @param fields
	 * @param childrenField 子节点字段名称
	 * @param modelId       模型ID
	 * @param node          删除的子点
	 * @param delFlag       删除标识
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> List<T> tree2list(Map<String, Field> fields, String childrenField, String modelId, T node, int delFlag)
			throws IllegalArgumentException, IllegalAccessException {
		setModelId(fields, node, modelId);
		setDelFlag(fields, node, delFlag);
		List<T> list = new ArrayList<T>();
		list.add(node);
		List<T> children = getChildren(fields, childrenField, node);
		if (children == null) {
			return list;
		}
		for (T child : children) {
			list.addAll(tree2list(fields, childrenField, modelId, child, delFlag));
		}
		return list;
	}

	/**
	 * @category 树形转换成List<T>对象，用于保存
	 * @param <T>
	 * @param fields
	 * @param childrenField 子节点字段名称
	 * @param modelId       模型ID
	 * @param node          删除的子点
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> List<T> tree2list(Map<String, Field> fields, String childrenField, String modelId, T node)
			throws IllegalArgumentException, IllegalAccessException {
		return tree2list(fields, childrenField, modelId, node, 0);
	}

	/**
	 * @category 记录集合包装成树形
	 * @param <T>
	 * @param fields        字段映射
	 * @param childrenField 子节点字段名称
	 * @param pnode         父节点
	 * @param nodeList      节点集合
	 * @param layers        读取层数
	 * @param layer         当前层号
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> T wrapTree(Map<String, Field> fields, String childrenField, T pnode, List<T> nodeList)
			throws IllegalArgumentException, IllegalAccessException {
		String id = getId(fields, pnode);
		for (int i = 0; i < nodeList.size(); i++) {
			T node = nodeList.get(i);
			String pid = getPid(fields, node, DEF_PID);
			if (id.equalsIgnoreCase(pid)) {
				addChildren(fields, childrenField, pnode, node);
				wrapTree(fields, childrenField, node, nodeList);
			}
		}
		return pnode;
	}

	/**
	 * @category 读取节点ID
	 * @param <T>
	 * @param fields
	 * @param node
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> String getId(Map<String, Field> fields, T node)
			throws IllegalArgumentException, IllegalAccessException {
		String id = null;
		if (node != null) {
			Object value = this.getValue(fields, node, "id");
			if (value != null) {
				id = String.valueOf(value);
			}
		}
		return id;
	}

	/**
	 * @category 读取父ID
	 * @param <T>
	 * @param fields
	 * @param node
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> String getPid(Map<String, Field> fields, T node, String filedName)
			throws IllegalArgumentException, IllegalAccessException {
		String pid = null;
		if (StringUtils.isEmpty(filedName)) {
			filedName = DEF_PID;
		}
		Object value = this.getValue(fields, node, filedName);
		if (value != null) {
			pid = String.valueOf(value);
		}
		return pid;
	}

	@SuppressWarnings("unused")
	private <T> void setPid(Map<String, Field> fields, T node, String pid)
			throws IllegalArgumentException, IllegalAccessException {
		this.setValue(fields, node, "pid", pid);
	}

	/**
	 * @category 读取左值
	 * @param <T>
	 * @param fields
	 * @param node
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> long getLft(Map<String, Field> fields, T node) throws IllegalArgumentException, IllegalAccessException {
		long lft = 1;
		Object value = this.getValue(fields, node, "lft");
		if (value != null) {
			lft = (long) value;
		}
		return lft;
	}

	/*
	 * private <T> void setCreateTime(Map<String, Field> fields, T node) throws
	 * IllegalArgumentException, IllegalAccessException { String dt =
	 * DateTimeUtils.formatDate(new Date()); this.setValue(fields, node,
	 * "CREATE_TIME", dt); }
	 * 
	 * private <T> void setUpdateTime(Map<String, Field> fields, T node) throws
	 * IllegalArgumentException, IllegalAccessException { this.setValue(fields,
	 * node, "UPDATE_TIME", new Date()); }
	 */

	/**
	 * @category 设置删除标识
	 * @param <T>
	 * @param fields
	 * @param node
	 * @param delFlag
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> void setDelFlag(Map<String, Field> fields, T node, int delFlag)
			throws IllegalArgumentException, IllegalAccessException {
		this.setValue(fields, node, "del_flag", delFlag);
	}

	private <T> void setLft(Map<String, Field> fields, T node, long lft)
			throws IllegalArgumentException, IllegalAccessException {
		this.setValue(fields, node, "lft", lft);
	}

	/**
	 * @category 读取模型ID
	 * @param <T>
	 * @param fields
	 * @param node
	 * @param modelId
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> void setModelId(Map<String, Field> fields, T node, String modelId)
			throws IllegalArgumentException, IllegalAccessException {
		this.setValue(fields, node, "model_id", modelId);
	}

	/**
	 * @category 读取右值
	 * @param <T>
	 * @param fields
	 * @param node
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> long getRgt(Map<String, Field> fields, T node) throws IllegalArgumentException, IllegalAccessException {
		long rgt = 1;
		Object value = this.getValue(fields, node, "rgt");
		if (value != null) {
			rgt = (long) value;
		}
		return rgt;
	}

	private <T> void setRgt(Map<String, Field> fields, T node, long rgt)
			throws IllegalArgumentException, IllegalAccessException {
		Field field = fields.get("rgt");
		field.setAccessible(true);
		field.set(node, rgt);
		field.setAccessible(false);
	}

	/**
	 * @category 读取子节点集合
	 * @param <T>
	 * @param fields
	 * @param childrenField
	 * @param node
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	@SuppressWarnings("unchecked")
	private <T> List<T> getChildren(Map<String, Field> fields, String childrenField, T node)
			throws IllegalArgumentException, IllegalAccessException {
		Field field = fields.get(childrenField);
		field.setAccessible(true);
		Object value = field.get(node);
		field.setAccessible(false);
		if (value instanceof List) {
			return (List<T>) value;
		}
		return null;
	}

	/**
	 * @category 设置子节点列表
	 * @param <T>
	 * @param fields
	 * @param childrenField
	 * @param node
	 * @param children
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> boolean setChildren(Map<String, Field> fields, String childrenField, T node, List<T> children)
			throws IllegalArgumentException, IllegalAccessException {
		Field field = fields.get(childrenField);
		field.setAccessible(true);
		field.set(node, children);
		field.setAccessible(false);
		return true;
	}

	/**
	 * @category 增加子节点
	 * @param <T>
	 * @param fields
	 * @param childrenField
	 * @param pnode
	 * @param cnode
	 * @return
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	private <T> List<T> addChildren(Map<String, Field> fields, String childrenField, T pnode, T cnode) {
		try {
			List<T> children = getChildren(fields, childrenField, pnode);
			if (children == null) {
				children = new ArrayList<T>();
				setChildren(fields, childrenField, pnode, children);
			}
			children.add(cnode);
			return children;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	private <T> Object getValue(Map<String, Field> fields, T node, String fieldName)
			throws IllegalArgumentException, IllegalAccessException {
		Field field = fields.get(fieldName);
		field.setAccessible(true);
		Object value = field.get(node);
		field.setAccessible(false);
		return value;
	}

	private <T> void setValue(Map<String, Field> fields, T node, String filedName, Object value)
			throws IllegalArgumentException, IllegalAccessException {
		if (node != null) {
			Field field = fields.get(filedName);
			field.setAccessible(true);
			field.set(node, value);
			field.setAccessible(false);
		}
	}

	/**
	 * 树节点转换成list
	 * 
	 * @param <T>
	 * @param returnEntityClass 转换后的实体对象
	 * @param list              转换后的数据
	 * @param sourceEntityClass 需要转换的实体对象
	 * @param treeNode          需要转换树节点对象
	 * @param map               id，pid 的map
	 */
	private <T, R> void treeNode2list(Class<T> returnEntityClass, List<T> list, Class<R> sourceEntityClass, R treeNode,
			Map<String, String> map, String pidFiledName) {
		if (treeNode != null) {
			try {
				T bean = ObjUtils.copyTo(treeNode, returnEntityClass);
				list.add(bean);
				Map<String, Field> rtnFields = ObjUtils.reflectFieldMix(returnEntityClass);
				map.put(getId(rtnFields, bean), getPid(rtnFields, bean, pidFiledName));
				Map<String, Field> srcFields = ObjUtils.reflectFieldMix(sourceEntityClass);
				List<R> listChildren = getChildren(srcFields, DEF_CHILDREN_FIELD, treeNode);
				if (StringUtils.isNotEmpty(listChildren)) {
					for (R node : listChildren) {
						treeNode2list(returnEntityClass, list, sourceEntityClass, node, map, pidFiledName);
					}
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}

		}
	}

	/**
	 * 
	 * @param <T>
	 * @param returnEntityClass 转换后的实体对象
	 * @param list              转换后的数据
	 * @param sourceEntityClass 需要转换的实体对象
	 * @param listTree          需要转换数据
	 * @param map               id，pid 的map
	 */
	private <T, R> void treeList2list(Class<T> returnEntityClass, List<T> list, Class<R> sourceEntityClass,
			List<R> listTree, Map<String, String> map, String pidFieldName) {
		if (StringUtils.isNotEmpty(listTree)) {
			for (R node : listTree) {
				treeNode2list(returnEntityClass, list, sourceEntityClass, node, map, pidFieldName);
			}
		}
	}

	/**
	 * 获取树形节点的层级
	 * 
	 * @param map key：id，value：pid
	 * @param id  节点id
	 * @return 节点层级
	 */
	private int getTreeLevel(Map<String, String> map, String id) {
		int level = -1;
		String tempid = id;
		while (map.get(tempid) != null) {
			tempid = map.get(tempid);
			level++;
		}
		return level;
	}

	/**
	 * 树形数据转换成grid数据（支持名称缩进模式）
	 * 
	 * @param <T>
	 * @param returnEntityClass 返回值实体类对象
	 * @param listData          需要转换的树形数据list（children模式）
	 * @param sourceEntityClass 需要转换的数据实体对象
	 * @param pidFiledName      父节点字段名
	 * @param padFieldName      需要缩进的字段名
	 * @param padChar           缩进字符
	 * @return 树形grid数据
	 */
	public <T, R> List<T> getTreeGridList(Class<T> returnEntityClass, List<R> listData, Class<R> sourceEntityClass,
			String pidFiledName, String padFieldName, String padChar) {

		Map<String, String> map = new HashMap<String, String>();
		List<T> listRtn = new ArrayList<T>(); // 返回值
		try {
			if (StringUtils.isNotEmpty(listData)) {
				treeList2list(returnEntityClass, listRtn, sourceEntityClass, listData, map, pidFiledName);
			}
			// 整理数据，形成树形的缩进模式
			if (StringUtils.isNotEmpty(padFieldName) && StringUtils.isNotEmpty(listRtn)) {
				Map<String, Field> rtnFields = ObjUtils.reflectFieldMix(returnEntityClass);
				for (T node : listRtn) {
					String nodeId = getId(rtnFields, node);
					int level = getTreeLevel(map, nodeId);
					if (level > 0) {
						Object obj = this.getValue(rtnFields, node, padFieldName);
						if (obj != null) {// 缩进，用空格填充
							String value = obj.toString();
							if (" ".equals(padChar)) {
								value = StringUtils.leftPadStr(value, level * 5, " ");
							} else {
								value = StringUtils.leftPadStr(value, level, padChar);
							}
							setValue(rtnFields, node, padFieldName, value);
						}
					}
				}
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return listRtn;

	}

	/**
	 * 树形数据转换成grid数据（支持名称缩进模式）
	 * 
	 * @param <T>
	 * @param returnEntityClass 返回值实体类对象
	 * @param listData          需要转换的树形数据list（children模式）
	 * @param sourceEntityClass 需要转换的数据实体对象
	 * @param pidFiledName      父节点字段名
	 * @param padFunc           需要缩进的字段对象
	 * @param padChar           缩进字符
	 * @return 树形grid数据
	 */
	public <T, R> List<T> getTreeGridList(Class<T> returnEntityClass, List<R> listData, Class<R> sourceEntityClass,
			Func<T, ?> pidFunc, Func<T, ?> padFunc, String padChar) {
		String pid = "pid";
		if (pidFunc != null) {
			pid = resolveColumnName(pidFunc);
		}
		return this.getTreeGridList(returnEntityClass, listData, sourceEntityClass, pid, resolveColumnName(padFunc),
				padChar);

	}

	/**
	 * 树形数据转换成grid数据（支持名称缩进模式）
	 * 
	 * @param <T>
	 * @param returnEntityClass 返回值实体类对象
	 * @param listData          需要转换的树形数据list（children模式）
	 * @param sourceEntityClass 需要转换的数据实体对象
	 * @param padFieldName      需要缩进的字段名
	 * @return 树形grid数据
	 */
	public <T, R> List<T> getTreeGridList(Class<T> returnEntityClass, List<R> listData, Class<R> sourceEntityClass,
			String padFieldName) {
		return getTreeGridList(returnEntityClass, listData, sourceEntityClass, "pid", padFieldName, " ");
	}

	public <T, R> List<T> getTreeGridList(Class<T> returnEntityClass, List<R> listData, Class<R> sourceEntityClass,
			Func<T, ?> padFunc) {
		return getTreeGridList(returnEntityClass, listData, sourceEntityClass, null, padFunc, " ");
	}

	private <T> String resolveColumnName(Func<T, ?> func) {
		Where where = Where.create();
		return where.getClassColumnName(func);
	}

}
