package com.yunhesoft.system.kernel.service;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 主键如果是字符串，全部转为小写，在读取时不区分大小写
 * 
 * 注:在遍历主键集时，会保持原有的输入的key值
 * 
 * <AUTHOR>
 * @since 2024-10-26 17:16
 * @param <Object>
 */
public class EntityMap<V> extends LinkedHashMap<String, V> implements Map<String, V> {

	private static final long serialVersionUID = 1L;
	private final Map<String, String> originalKeys = new HashMap<>();

	@Override
	public V put(String key, V value) {
		String normalizedKey = normalizeKey(key);
		originalKeys.put(normalizedKey, key);
		return super.put(normalizedKey, value);
	}

	@Override
	public V get(Object key) {
		if (key instanceof String) {
			return super.get(normalizeKey((String) key));
		}
		return null;
	}

	@Override
	public boolean containsKey(Object key) {
		if (key instanceof String) {
			return super.containsKey(normalizeKey((String) key));
		}
		return false;
	}

	@Override
	public V remove(Object key) {
		if (key instanceof String) {
			String normalizedKey = normalizeKey((String) key);
			originalKeys.remove(normalizedKey);
			return super.remove(normalizedKey);
		}
		return null;
	}

	@Override
	public void putAll(Map<? extends String, ? extends V> m) {
		for (Map.Entry<? extends String, ? extends V> entry : m.entrySet()) {
			put(entry.getKey(), entry.getValue());
		}
	}

	@Override
	public Set<String> keySet() {
		return new LinkedHashSet<>(originalKeys.values());
	}

	private String normalizeKey(String key) {
		return key.toLowerCase();
	}

	@Override
	public Set<Map.Entry<String, V>> entrySet() {
		Set<Map.Entry<String, V>> entries = new LinkedHashSet<>();
		for (Map.Entry<String, V> entry : super.entrySet()) {
			entries.add(new AbstractMap.SimpleEntry<>(originalKeys.get(entry.getKey()), entry.getValue()));
		}
		return entries;
	}

	@Override
	public boolean equals(Object o) {
		if (o == this) {
			return true;
		}
		if (!(o instanceof Map)) {
			return false;
		}
		Map<?, ?> m = (Map<?, ?>) o;
		if (m.size() != size()) {
			return false;
		}
		try {
			for (Map.Entry<String, V> e : entrySet()) {
				String key = e.getKey();
				V value = e.getValue();
				if (value == null) {
					if (!(m.get(key) == null && m.containsKey(key))) {
						return false;
					}
				} else {
					if (!value.equals(m.get(key))) {
						return false;
					}
				}
			}
		} catch (ClassCastException unused) {
			return false;
		} catch (NullPointerException unused) {
			return false;
		}
		return true;
	}

	@Override
	public int hashCode() {
		int h = 0;
		for (Map.Entry<String, V> entry : entrySet()) {
			h += entry.hashCode();
		}
		return h;
	}

	public static LinkedHashMap<String, Object> fromMap(Map<String, Object> map) {
		LinkedHashMap<String, Object> entityMap = new EntityMap<Object>();
		for (Map.Entry<String, Object> entry : map.entrySet()) {
			entityMap.put(entry.getKey(), entry.getValue());
		}
		return entityMap;
	}

	public static List<LinkedHashMap<String, Object>> fromMaps2LinkedHashMap(List<Map<String, Object>> result) {
		List<LinkedHashMap<String, Object>> entityMap = new ArrayList<LinkedHashMap<String, Object>>();
		for (Map<String, Object> map : result) {
			entityMap.add(fromMap(map));
		}
		return entityMap;
	}

	public static List<Map<String, Object>> fromMaps2Map(List<Map<String, Object>> result) {
		List<Map<String, Object>> entityMap = new ArrayList<Map<String, Object>>();
		for (Map<String, Object> map : result) {
			entityMap.add(fromMap(map));
		}
		return entityMap;
	}

	public static void main(String[] args) {
		EntityMap<Object> map0 = new EntityMap<Object>();
		Map<String, Object> map1 = new LinkedHashMap<String, Object>();
		map1.put("Id", 134);
		Map<String, Object> map2 = EntityMap.fromMap(map1);
		System.out.println(map2.get("ID"));
		for (String key : map2.keySet()) {
			System.out.println(key);
		}

	}
}
