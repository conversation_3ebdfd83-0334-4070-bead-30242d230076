package com.yunhesoft.system.kernel.service;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.management.RuntimeErrorException;
import javax.sql.DataSource;
import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.jdbc.object.BatchSqlUpdate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLExpr;
import com.alibaba.druid.sql.ast.SQLLimit;
import com.alibaba.druid.sql.ast.SQLOrderBy;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.SQLBinaryOpExpr;
import com.alibaba.druid.sql.ast.expr.SQLBinaryOperator;
import com.alibaba.druid.sql.ast.expr.SQLIdentifierExpr;
import com.alibaba.druid.sql.ast.expr.SQLIntegerExpr;
import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
import com.alibaba.druid.sql.ast.statement.SQLSelectOrderByItem;
import com.alibaba.druid.sql.ast.statement.SQLSelectQueryBlock;
import com.alibaba.druid.sql.ast.statement.SQLSelectStatement;
import com.alibaba.druid.sql.dialect.sqlserver.ast.SQLServerSelectQueryBlock;
import com.alibaba.druid.sql.dialect.sqlserver.ast.SQLServerTop;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.EntityUtils;
import com.yunhesoft.core.common.utils.MD5Utils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.impl.TransactionServiceImpl;
import com.yunhesoft.system.kernel.service.model.BlobColumnUtils;
import com.yunhesoft.system.kernel.service.model.ColumnUtils;
import com.yunhesoft.system.kernel.service.model.DBTypeUtils;
import com.yunhesoft.system.kernel.service.model.EntityLambdaUtils;
import com.yunhesoft.system.kernel.service.model.ExecParam;
import com.yunhesoft.system.kernel.service.model.Func;
import com.yunhesoft.system.kernel.service.model.Group;
import com.yunhesoft.system.kernel.service.model.ISQLObject;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.SQLModel;
import com.yunhesoft.system.kernel.service.model.Select;
import com.yunhesoft.system.kernel.service.model.Table;
import com.yunhesoft.system.kernel.service.model.TableColumnInfo;
import com.yunhesoft.system.kernel.service.model.TableInfo;
import com.yunhesoft.system.kernel.service.model.Update;
import com.yunhesoft.system.kernel.service.model.Where;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 * @category 数据库持久化操作服务
 */
@Log4j2
public class AEntityService extends TransactionServiceImpl implements EntityService {

    @Autowired
    private JdbcTemplate jdbc;

    private final static String CREATE_TIME = "CREATE_TIME"; // 创建时间字段
    private final static String CREATE_BY = "CREATE_BY"; // 创建人字段
    private final static String UPDATE_TIME = "UPDATE_TIME"; // 更新时间字段
    private final static String UPDATE_BY = "UPDATE_BY"; // 更新人字段
    private static String DBNAME = null; // 数据库名
    private static EntityService.DBTYPE DBTYPE = null; // 数据库类型
    private static Double DB_VERSION = null; // 数据库版本号

    private final static String CREATE_BY_ORG = "CREATE_BY_ORG"; // 创建机构编码
    //	private final static String CREATE_BY_ORGNAME = "CREATE_BY_ORGNAME"; // 创建机构
    private final static String CREATE_BY_POST = "CREATE_BY_POST"; // 创建岗位编码
//	private final static String CREATE_BY_POSTNAME = "CREATE_BY_POSTNAME"; // 创建岗位

    private final static int BATCH_EXECUTE_SQL_SIZE = 5000; // 批量执行 SQL 语句上限值

    /**
     * 获取事务管理器
     *
     * @param ds
     * @return
     */
    private PlatformTransactionManager getTransactionManager(DataSource ds) {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(ds);// 注入dataSource
        return transactionManager;
    }

    /**
     * 获取事务处理对象
     *
     * @param ds
     * @return
     */
    @Override
    public TransactionTemplate getTransactionTemplate(DataSource ds) {
        TransactionTemplate transactionTemplate = new TransactionTemplate();
        transactionTemplate.setTransactionManager(getTransactionManager(ds));// 注入事务管理器
        return transactionTemplate;
    }

    /**
     * 支持事务语句执行
     *
     * @param sql
     * @param args
     */
    private int jdbcUpdate(String sql, Object... args) {
        return jdbc.update(sql, args);
    }

    /**
     * @param <T>
     * @param clazz      对象类型
     * @param sqlObjects 查询对象类，如果提供Table对象，并正确赋值，则clazz可为非实体类
     * @return
     * @category 查询单个对象
     */
    @Override
    public <T> T queryObject(Class<T> clazz, ISQLObject... sqlObjects) {
        return rawQueryObject(clazz, sqlObjects);
    }

    @Override
    public <T> T queryObjectDisableTenant(Class<T> clazz, ISQLObject... sqlObjects) {
        return rawQueryObjectDiableTenant(clazz, sqlObjects);
    }

    /**
     * @param sqlObjects 至少包含 Table对象，可用对象:pagination,where
     * @return
     * @category 查询数据数量
     */
    @Override
    public Long queryCount(ISQLObject... sqlObjects) {
        SQLModel model = countSQL(sqlObjects);
        return jdbc.queryForObject(model.sql(), Long.class, model.args());
    }

    public Long queryCount(String sql, Object... args) {
        return jdbc.queryForObject(sql, Long.class, args);
    }

    /**
     * @param <T>
     * @param entityClass 查询的实体类
     * @param sqlObjects  查询用的实例对象数组
     * @return
     * @category 根据where条件查询总数量
     */
    @Override
    public <T> Long queryCount(Class<T> entityClass, Where where) {
        SQLModel model = countSQL(EntityUtils.tableName(entityClass), where);
        return jdbc.queryForObject(model.sql(), Long.class, model.args());
    }

    @Override
    public <T> Long queryCountDisableTenant(Class<T> entityClass, Where where) {
        SQLModel model = countSQL(EntityUtils.tableName(entityClass), where);
        return jdbc.queryForObject(disableTenant(model.sql()), Long.class, model.args());
    }

    @Override
    public <T> Long queryCountWithTenant(String tenantId, Class<T> entityClass, Where where) {
        SQLModel model = countSQL(EntityUtils.tableName(entityClass), where);
        return jdbc.queryForObject(withTenant(model.sql(), tenantId), Long.class, model.args());
    }

    /**
     * @param <T>
     * @param clazz      查询的实体类
     * @param sqlObjects 查询用的实例对象数组
     * @return
     * @category 查询对象列表
     */
    @Override
    public <T> List<T> queryList(Class<T> clazz, ISQLObject... sqlObjects) {
        return rawQueryList(clazz, sqlObjects);
    }

    @Override
    public <T> List<T> queryListDisableTenant(Class<T> clazz, ISQLObject... sqlObjects) {
        return rawQueryListDisableTenant(clazz, sqlObjects);
    }

    /**
     * @param sql  查询语句？形式
     * @param args 查询参数
     * @return
     * @category 查询结果为map
     */
    @Override
    public List<Map<String, Object>> queryListMap(String sql, Object... args) {
        List<Map<String, Object>> list = jdbc.queryForList(sql, args);
        List<Map<String, Object>> result = EntityMap.fromMaps2Map(list);
        list = null;
        return result;
    }

    /**
     * @param table  表格名称
     * @param column 列名称
     * @return
     * @category 查找某列的最大值
     *
     * <pre>
     *           注:列必须是数值型
     *           </pre>
     */
    @Override
    public Long findMaxId(String table, String column) {
        String sql = "select max(" + getColumn(column) + ") from " + table;
        Long id = jdbc.queryForObject(sql, Long.class);
        return id == null ? 0 : id;
    }

    /**
     * 获得字段的前缀用于适配多数据语法关键字
     *
     * @return
     */
    private String getColumn(String column) {
        if (this.isOracle()) {
            return ColumnUtils.getOracleColumn(column);
        } else {
            return column;
        }
    }

    /**
     * @param <T>
     * @param entityClass 实体类class
     * @param func        实体类字段
     * @param returnClass 返回值类型
     * @param where       where条件
     * @return
     * @category 查找字段中的最大值
     */
    @Override
    public <T, E> T findMaxValue(Class<?> entityClass, Func<E, ?> field, Class<T> returnClass, Where where) {
        String table = EntityUtils.tableName(entityClass);
        String column = getColumn(resolveColumnName(field));
        String sql = "select max(" + column + ") from " + table;
        if (where != null) {
            sql += " " + where.sql(getDbType());
        }
        T id = jdbc.queryForObject(sql, where.args(getDbType()).toArray(), returnClass);
        return id;
    }

    /**
     * 从表1查询数据插入表2
     *
     * @param selectEntityClass 查询表实体
     * @param selectFields      查询字段（支持 @TMUID变量，动态生成ID）
     * @param where             检索条件
     * @param insertEntityClass 插入表实体
     * @param insertFields      插入字段
     * @return
     */
    @Override
    public <T, V> int insertSelectTable(Class<T> selectEntityClass, List<String> selectFields, Where where, Class<V> insertEntityClass, List<String> insertFields) {
        String selectTable = EntityUtils.tableName(selectEntityClass);// 查询表
        String insertTable = EntityUtils.tableName(insertEntityClass);// 插入表
        String whereSql = where.sql(getDbType());
        StringBuffer sb = new StringBuffer();

        if (isOracle()) {
            selectFields.add("SYSDATE");
        } else {
            selectFields.add("'" + DateTimeUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS") + "'");
        }
        selectFields.add("'" + getCurrentUserId() + "'");
        insertFields.add(CREATE_TIME);
        insertFields.add(CREATE_BY);
// 		Insert into Table2(field1,field2,…) select value1,value2,… from Table1 where 。。。
        sb.append("insert into ");
        sb.append(insertTable);
        sb.append("(");
        sb.append(getInsertSelectCol(insertFields));
        sb.append(") select ");
        sb.append(getInsertSelectCol(selectFields));
        sb.append(" from ");
        sb.append(selectTable);
        sb.append(" ");
        sb.append(whereSql);
        // return jdbc.update(sb.toString(), where.getArgs().toArray());
        return jdbcUpdate(sb.toString(), where.getArgs().toArray());
    }

    /**
     * list转换为字段
     *
     * @param list
     * @return
     */
    private String getInsertSelectCol(List<String> list) {
        String s = "";
        if (StringUtils.isNotEmpty(list)) {
            for (String column : list) {
                if ("@TMUID".equalsIgnoreCase(column)) {
                    String dbtype = this.getDbType().toString();
                    if ("mysql".equalsIgnoreCase(dbtype)) {
                        s += "UPPER(replace(uuid(),'-','')),";
                    } else if ("sqlserver".equalsIgnoreCase(dbtype)) {
                        s += "UPPER(replace(NEWID(),'-','')),";
                    } else if ("oracle".equalsIgnoreCase(dbtype)) {
                        s += "RAWTOHEX(sys_guid()),";
                    } else if ("kingbase".equalsIgnoreCase(dbtype)) {
                        s += "sys_guid(),";
                    } else if ("dm".equalsIgnoreCase(dbtype)) {
                        s += "RAWTOHEX(sys_guid()),";
                    }
                } else {
                    s += this.getColumn(column) + ",";
                }
            }
            s = s.substring(0, s.length() - 1);
        }
        return s;
    }

    /**
     * @param <T>
     * @param entityClass 实体类class
     * @param field       实体类的字段
     * @param returnClass 返回类型
     * @param where       wher条件
     * @return
     * @category 查找字段中的最小值
     */
    @Override
    public <T, E> T findMinValue(Class<?> entityClass, Func<E, ?> field, Class<T> returnClass, Where where) {
        String table = EntityUtils.tableName(entityClass);
        String column = this.getColumn(resolveColumnName(field));
        String sql = "select min(" + column + ") from " + table;
        if (where != null) {
            sql += " " + where.sql(getDbType());
        }
        T id = jdbc.queryForObject(sql, where.args(getDbType()).toArray(), returnClass);
        return id;
    }

    /**
     * @param entityClass 实体类
     * @param func        字段
     * @return
     * @category 查找某列的最大值
     *
     * <pre>
     * 			注:列必须是数值型
     *           </pre>
     */
    @Override
    public <T> Long findMaxId(Class<T> entityClass, Func<T, ?> func) {
        String table = EntityUtils.tableName(entityClass);
        String column = resolveColumnName(func);
        return findMaxId(table, column);
    }

    /**
     * @param <T>
     * @param object 不为空的字段作为检索条件
     * @return
     * @category 根据对象查询
     */
    @Override
    public List<?> queryListByEntity(Object entity) {
        if (entity == null) {
            throw new RuntimeException("输入参数为空，不能创建根据对象生成检索条件对象");
        }
        return rawQueryList(entity.getClass(), Where.create(entity));
    }

    /**
     * @param sqlObjects 更新对象数组
     * @return
     * @category 根据更新对象进行更新
     */
    @Override
    public int update(ISQLObject... sqlObjects) {
        SQLModel model = updateSQL(sqlObjects);
        // return jdbc.update(model.sql(), model.getArgs().toArray());
        // BlobColumnUtils.saveBlob("update", model, model);// 大数据字段处理
        return jdbcUpdate(model.sql(), model.getArgs().toArray());
    }

    /**
     * @param entity
     * @return
     * @category 根据主键更新(不更新空值字段) 不建议使用
     *
     * <pre>
     *           如果对象主键已存在，更新，否则创建
     *           </pre>
     */
    @Deprecated
    @Override
    public int save(Object entity) {
        return rawSave(entity);
    }

    /**
     * @param entity
     * @return
     * @category 插入实体类
     */
    @Override
    public int insert(Object entity) {
        return rawInsert(entity);
    }

    /**
     * @param entity
     * @return
     * @category 根据主键更新实体(不包含空值)
     */
    @Override
    public int updateById(Object entity) {
        return rawUpdateById(entity);
    }

    @Override
    public int updateByIdDisableTenant(Object entity) {
        return rawUpdateByIdDisableTenant(entity);
    }

    /**
     * @param entityClass
     * @param sqlObjects
     * @return
     * @category 删除
     */
    @Override
    public int delete(Class<?> entityClass, ISQLObject... sqlObjects) {
        SQLModel model = deleteSQL(EntityUtils.tableName(entityClass), (Object[]) sqlObjects);
        return jdbcUpdate(model.sql(), model.args());
    }

    @Override
    public int deleteDisableTenant(Class<?> entityClass, ISQLObject... sqlObjects) {
        SQLModel model = deleteSQL(EntityUtils.tableName(entityClass), (Object[]) sqlObjects);
        return jdbcUpdate(disableTenant(model.sql()), model.args());
    }

    @Override
    public int deleteWithTenant(String tenantId, Class<?> entityClass, ISQLObject... sqlObjects) {
        SQLModel model = deleteSQL(EntityUtils.tableName(entityClass), (Object[]) sqlObjects);
        return jdbcUpdate(withTenant(model.sql(), tenantId), model.args());
    }

    /**
     * @param <T>
     * @param entity 实体对象
     * @return
     * @category 原生保存实体(主键存在 ， 更新 ， 否则插入) 不建议使用
     */
    @Override
    @Transactional
    @Deprecated
    public <T> int rawSave(T entity) {
        int rs = rawUpdateById(entity);
        if (rs == 0) {
            rs = rawInsert(entity);
        }
        return rs;
    }

    /**
     * @param <T>
     * @param entities
     * @return
     * @category 批量保存数据，包含空值也更新(不建议使用，效率低)
     */
    @Deprecated
    @Override
    public <T> int[] rawSaveBatchIncludeNull(List<T> entities) {
        List<Integer> rslt = new ArrayList<Integer>();
        int[] rs = rawUpdateByIdBatchIncludeNull(entities);
        List<T> inserts = new ArrayList<T>();
        for (int i = 0; i < rs.length; i++) {
            if (rs[i] == 0) {
                inserts.add(entities.get(i));
            } else {
                rslt.add(rs[i]);
            }
        }
        if (inserts.size() > 0) {
            int[] rs1 = rawInsertBatch(inserts);
            for (int r : rs1) {
                rslt.add(r);
            }
        }
        int[] res = new int[rslt.size()];
        for (int i = 0; i < rslt.size(); i++) {
            if (rslt.get(i) == null) {
                res[i] = 0;
            } else {
                res[i] = rslt.get(i);
            }
        }
        return res;
    }

    /**
     * @param <T>
     * @param entities 实体对象列表
     * @return
     * @category R批量保存实体类(主键存在更新 ， 否则插入)不建议使用，效率低
     */
    @Deprecated
    @Override
    @Transactional
    public <T> int[] rawSaveBatch(List<T> entities) {
        List<Integer> rslt = new ArrayList<Integer>();
        int[] rs = rawUpdateByIdBatch(entities);
        List<T> inserts = new ArrayList<T>();
        for (int i = 0; i < rs.length; i++) {
            if (rs[i] == 0) {
                inserts.add(entities.get(i));
            } else {
                rslt.add(rs[i]);
            }
        }
        if (inserts.size() > 0) {
            int[] rs1 = rawInsertBatch(inserts);
            for (int r : rs1) {
                rslt.add(r);
            }
        }
        int[] res = new int[rslt.size()];
        for (int i = 0; i < rslt.size(); i++) {
            if (rslt.get(i) == null) {
                res[i] = 0;
            } else {
                res[i] = rslt.get(i);
            }
        }
        return res;
    }

    /**
     * 不建议使用，效率低
     */
    @Deprecated
    @Override
    public <T> int saveBatch(List<T> entities) {
        return this.isBatchSuccess(this.rawSaveBatch(entities));
    }

    /**
     * @param <T>
     * @param entities
     * @return
     * @category 批量更新，包含空值也更新(不建议使用，效率低)
     */
    @Deprecated
    @Override
    public <T> int saveBatchIncludeNull(List<T> entities) {
        return this.isBatchSuccess(this.rawSaveBatchIncludeNull(entities));
    }

    /**
     * @param <T>
     * @param sql  原生sql语句
     * @param args
     * @return
     * @category 原生语句插入
     */
    @Override
    @Transactional
    public <T> int rawInsert(String sql, Object... args) {
        return jdbcUpdate(sql, args);
    }

    /**
     * @param <T>
     * @param entity 实体对象
     * @return
     * @category R原生语句插入实体
     */
    @Override
    @Transactional
    public <T> int rawInsert(T entity) {
        SQLModel model = insertSQL(entity);
        BlobColumnUtils.saveBlob("insert", entity, model);// 大数据字段处理
        return rawInsert(model.sql(), model.args());
    }

    @Override
    @Transactional
    public <T> int rawInsertDisableTenant(T entity) {
        SQLModel model = insertSQL(entity);
        return rawInsert(disableTenant(model.sql()), model.args());
    }

    @Override
    @Transactional
    public <T> int rawInsertWithTenant(String tenantId, T entity) {
        SQLModel model = insertSQL(entity);
        return rawInsert(withTenant(model.sql(), tenantId), model.args());
    }

    /**
     * @param <T>
     * @param entities 实体对象列表
     * @return
     * @category R原生语句插入实体列表
     */
    @Override
    @Transactional
    public <T> int[] rawInsertBatch(List<T> entities) {
        if (StringUtils.isNotEmpty(entities)) {
            ExecParam param = this.getInsertExecParam(entities, null);
            BlobColumnUtils.saveBlob("insert", param);
            return rawInsertBatch(param.getSql(), param.getBatchArgs());
        } else {
            return getRtnValue(entities.size(), 0);
        }
    }

    @Override
    @Transactional
    public <T> int[] rawInsertBatchDisableTenant(List<T> entities) {
        if (StringUtils.isNotEmpty(entities)) {
            ExecParam param = this.getInsertExecParam(entities, null);
            BlobColumnUtils.saveBlob("insert", param);
            return rawInsertBatch(disableTenant(param.getSql()), param.getBatchArgs());
        } else {
            return getRtnValue(entities.size(), 0);
        }
    }

    @Override
    @Transactional
    public <T> int[] rawInsertBatchWithTenant(String tenantId, List<T> entities) {
        if (StringUtils.isNotEmpty(entities)) {
            ExecParam param = this.getInsertExecParam(entities, null);
            BlobColumnUtils.saveBlob("insert", param);
            return rawInsertBatch(withTenant(param.getSql(), tenantId), param.getBatchArgs());
        } else {
            return getRtnValue(entities.size(), 0);
        }
    }

    /**
     * 获取插入字段类型
     *
     * @param name
     * @return
     */
    private int getArgType(String name) {
        // System.out.println(name);
        if ("java.lang.String".equalsIgnoreCase(name)) {
            return Types.VARCHAR;
        } else if ("java.util.Date".equalsIgnoreCase(name) || "java.sql.Timestamp".equalsIgnoreCase(name) || "oracle.sql.timestamp".equalsIgnoreCase(name)) {
            return Types.TIMESTAMP;
        } else if ("java.lang.Integer".equalsIgnoreCase(name) || "int".equals(name)) {
            return Types.INTEGER;
        } else if ("java.lang.Double".equalsIgnoreCase(name) || "double".equals(name)) {
            return Types.FLOAT;
        } else if ("java.lang.Long".equalsIgnoreCase(name) || "long".equals(name)) {
            return Types.BIGINT;
        } else if ("java.lang.Boolean".equalsIgnoreCase(name) || "boolean".equals(name)) {
            return Types.BIT;
        }
        return Types.VARCHAR;
    }

    /**
     * 获取执行insert语句参数
     *
     * @param <T>
     * @param entities
     * @param batchSize
     * @return
     */
    private <T> ExecParam getInsertExecParam(List<T> entities, Integer batchSize) {
        if (StringUtils.isNotEmpty(entities)) {
            ExecParam param = new ExecParam();
            try {
                T entity = entities.get(0);
                boolean batch = false;
                if (batchSize != null && batchSize > 0) {
                    param.setBatchSize(batchSize);
                    batch = true;
                } else {
                    param.setBatchSize(0);
                }
                List<Integer> typeList = new ArrayList<Integer>();
                // long t1 = System.currentTimeMillis();
                SQLModel model = insertSQL(entity);
                List<Object[]> batchArgs = new ArrayList<Object[]>();
                int n = 0;
                List<Field> entityFields = model.getFields();// EntityUtils.fields(entity.getClass());
                param.setFields(entityFields);
                for (T e : entities) {
                    n++;
                    List<Object> args = new ArrayList<Object>();
                    for (Field field : entityFields) {
                        if (EntityUtils.isJavaClass(field.getType())) {
                            args.add(EntityUtils.fieldValue(field, e));
                            if (batch && n == 1) {
                                typeList.add(this.getArgType(field.getType().getName()));
                            }
                        } else {// 联合主键
                            List<Field> fields1 = EntityUtils.fields(field.getType());
                            Object subClass = EntityUtils.fieldValue(field, e);
                            for (Field field1 : fields1) {
                                args.add(EntityUtils.fieldValue(field1, subClass));
                                if (batch && n == 1) {
                                    typeList.add(this.getArgType(field1.getType().getName()));
                                }
                            }
                        }
                    }
                    batchArgs.add(args.toArray());
                }
                if (batch) {
                    int[] argType = typeList.stream().mapToInt(Integer::intValue).toArray();
                    param.setArgType(argType);
                }
                param.setSql(model.sql());
                param.setBatchArgs(batchArgs);
                param.setTableName(model.getTableName());
                // double t2 = (System.currentTimeMillis() - t1) / 1000.00;
                // System.out.println("list组装耗时：" + t2);
            } catch (IllegalArgumentException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
            return param;
        } else {
            return null;
        }
    }

    /**
     * 批量执行插入实体列表
     */
    @Override
    public <T> int insertBatch(List<T> entities) {
        return this.isBatchSuccess(this.rawInsertBatch(entities));
    }

    @Override
    public <T> int insertBatchDisableTenant(List<T> entities) {
        return this.isBatchSuccess(this.rawInsertBatchDisableTenant(entities));
    }

    /**
     * 批量执行插入
     *
     * @param entities  批量插入对象
     * @param batchSize 每次批量插入数量
     */
    @Override
    public <T> int insertBatch(List<T> entities, int batchSize) {
        int rtn = 0;
        if (StringUtils.isNotEmpty(entities)) {
            if (batchSize <= 0) {
                return insertBatch(entities);
            } else {
                ExecParam param = this.getInsertExecParam(entities, batchSize);
                BlobColumnUtils.saveBlob("insert", param);
                return this.batchSqlUpdate(param);
            }
        }
        return rtn;
    }

    /**
     * 批量执行语句
     *
     * @param param
     */
    private int batchSqlUpdate(ExecParam param) {
        List<Integer> rtnList = new ArrayList<Integer>();
        DataSource dataSource = jdbc.getDataSource();
        BatchSqlUpdate bsu = new BatchSqlUpdate(dataSource, param.getSql());
        bsu.setBatchSize(param.getBatchSize());
        bsu.setTypes(param.getArgType());
        // 支持事务
        TransactionTemplate transactionTemplate = getTransactionTemplate(dataSource);
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus transactionStatus) {
                try {
                    for (Object[] objs : param.getBatchArgs()) {
                        bsu.update(objs);
                    }
                    bsu.flush();
                    rtnList.add(1);
                } catch (Exception e) {
                    transactionStatus.setRollbackOnly();// 回滚
                    rtnList.add(0);
                    throw e;
                }
            }
        });
        if (rtnList.size() > 0) {
            return rtnList.get(0);
        } else {
            return 0;
        }
    }

    /**
     * @param <T>
     * @param sql       原生sql语句
     * @param batchArgs 批量数据
     * @return
     * @category 原生语句批量插入
     */
    @Override
    @Transactional
    public <T> int[] rawInsertBatch(String sql, List<Object[]> batchArgs) {
        return rawUpdateBatch(sql, batchArgs);
    }

    /**
     * @param sql       原生sql文
     * @param batchArgs 插入的数据
     * @category 原生批量插入
     */
    @Override
    public <T> int insertBatch(String sql, List<Object[]> batchArgs) {
        return this.isBatchSuccess(rawInsertBatch(sql, batchArgs));
    }

    /**
     * @param sql  完整原生 sql 文
     * @param args 参数
     * @return 返回SqlRowSet结果集
     * @category _原生语句集合查询
     *
     * <pre>
     *           示例: sql = "select * from table where a=? and b=?";
     *           rawQuery(sql, 1, 2);
     *           </pre>
     */
    @Override
    public SqlRowSet rawQuery(String sql, Object... args) {
        try {
            sql = refactorySQLSelect(sql);
//			log.info(sql);
            return jdbc.queryForRowSet(sql, args);
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }

    @Override
    public SqlRowSet rawQueryDisableTenant(String sql, Object... args) {
        sql = refactorySQLSelect(sql);
        return jdbc.queryForRowSet(disableTenant(sql), args);
    }

    @Override
    public SqlRowSet rawQueryWithTenant(String tenantId, String sql, Object... args) {
        sql = refactorySQLSelect(sql);
        return jdbc.queryForRowSet(withTenant(sql, tenantId), args);
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param objects     查询条件对象
     * @return
     * @category 查询
     */
    @Override
    public <T> SqlRowSet rawQuery(Class<T> entityClass, ISQLObject... objects) {
        List<ISQLObject> objs = new ArrayList<ISQLObject>();
        objs.add(Table.create(entityClass));
        objs.addAll(Arrays.asList(objects));
        SQLModel model = selectSQL((ISQLObject[]) objs.toArray());
        return jdbc.queryForRowSet(model.sql(), model.args());
    }

    /**
     * @param <T>
     * @param entity
     * @return
     * @category 根据实例主键查询
     */
    @Override
    public <T> T rawQueryById(T entity) {
        if (entity == null) {
            return null;
        }
        @SuppressWarnings("unchecked") Class<T> entityClass = (Class<T>) entity.getClass();
        List<Field> idFields = EntityUtils.idFields(entityClass);
        String where = "";
        List<Object> args = new ArrayList<Object>();
        String cond = " ";
        try {
            for (Field field : idFields) {
                if (EntityUtils.isJavaClass(field.getType())) {
                    where += cond + EntityUtils.columnName(field) + "=?";
                    args.add(EntityUtils.fieldValue(field, entity));
                    cond = " and ";
                } else {
                    List<Field> fields = EntityUtils.fields(entityClass);
                    Object value = EntityUtils.fieldValue(field, entity);
                    for (Field field1 : fields) {
                        where += cond + EntityUtils.columnName(field) + "=?";
                        args.add(EntityUtils.fieldValue(field1, value));
                        cond = " and ";
                    }
                }
            }
        } catch (IllegalArgumentException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        return rawQueryObjectByWhere(entityClass, where, args.toArray());
    }

    /**
     * @param sql
     * @param page
     * @param size
     * @param args
     * @return
     * @category _原生语句集合分页查询
     */
    @Override
    public Pagination<SqlRowSet> rawQueryPage(String sql, int page, int size, Object... args) {
        long total = queryCount(sql, args);
        return Pagination.create(page, size, total, rawQuery(sql, args));
    }

    /**
     * @param <R>
     * @param resultClass 结果类
     * @param sql         完整原生 sql 文
     * @param args
     * @return 返回转换成结果类的列表
     * @category _原生语句结果类列表查询
     *
     * <pre>
     * 		示例: sql = "select * from table where a=? and b=?";
     *           rawQuery(clazz,sql, 1, 2);
     * 		注:clazz可以是非实体类，系统自动赋值
     *           </pre>
     */
    @Override
    public <R> List<R> rawQueryList(Class<R> resultClass, String sql, Object... args) {
        try {
            // log.info(sql);
            SqlRowSet rs = this.rawQuery(sql, args);
            List<R> list = EntityUtils.convertTo(rs, resultClass);
            BlobColumnUtils.setBlobData(resultClass, list);// 针对大数据blob类型进行处理
            return list;
        } catch (Exception e) {
            log.error("", e);
            return null;
            // throw new RuntimeException(e);
        }
    }

    /**
     * @param entityClass 实体类
     * @param sqlObjects  查询实例数组
     * @return
     * @category 根据实体类及查询实例对象进行查询
     */
    @Override
    public <T> List<T> rawQueryList(Class<T> entityClass, ISQLObject... sqlObjects) {
        SQLModel model = selectSQL(entityClass, sqlObjects);
        return rawQueryList(entityClass, model.sql(), model.args());
    }

    @Override
    public <T> List<T> rawQueryListDisableTenant(Class<T> entityClass, ISQLObject... sqlObjects) {
        SQLModel model = selectSQL(entityClass, sqlObjects);
        return rawQueryList(entityClass, disableTenant(model.sql()), model.args());
    }

    @Override
    public <T> List<T> rawQueryListWithTenant(String tenantId, Class<T> entityClass, ISQLObject... sqlObjects) {
        SQLModel model = selectSQL(entityClass, sqlObjects);
        return rawQueryList(entityClass, withTenant(model.sql(), tenantId), model.args());
    }

    /**
     * @param <R>
     * @param resultClass
     * @param sql
     * @param page
     * @param size
     * @param args
     * @return
     * @category _原生语句结果类分页查询
     */
    @Override
    public <R> Pagination<List<R>> rawQueryListPage(Class<R> resultClass, String sql, int page, int size, Object... args) {
        try {
            long total = queryCount(sql, args);
            SqlRowSet rs = this.rawQuery(sql, args);
            return Pagination.create(page, size, total, EntityUtils.convertTo(rs, resultClass));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param SqlWhere    where查询对象
     * @param args
     * @return
     * @category 根据where条件查询
     */
    @Override
    public <T> List<T> rawQueryListByWhere(Class<T> entityClass, Where SqlWhere) {
        SQLModel model = selectSQL(Table.create(entityClass), Select.create("*"), SqlWhere);
        return rawQueryList(entityClass, model.sql(), model.args());
    }

    @Override
    public <T> List<T> rawQueryListByWhereDisableTenant(Class<T> entityClass, Where SqlWhere) {
        SQLModel model = selectSQL(Table.create(entityClass), Select.create("*"), SqlWhere);
        return rawQueryList(entityClass, disableTenant(model.sql()), model.args());
    }

    @Override
    public <T> List<T> rawQueryListByWhereWithTenant(String tenantId, Class<T> entityClass, Where SqlWhere) {
        SQLModel model = selectSQL(Table.create(entityClass), Select.create("*"), SqlWhere);
        return rawQueryList(entityClass, withTenant(model.sql(), tenantId), model.args());
    }

    /**
     * @param <T>
     * @param entityClass
     * @param column
     * @param arg
     * @return
     * @category 根据列检查唯一性
     */
    @Override
    public <T> boolean checkUniqueByColumn(Class<T> entityClass, String column, Object arg) {
        Where where = Where.create();
        where.eq(column, arg);
        SQLModel model = countSQL(EntityUtils.tableName(entityClass), where);
        Long count = jdbc.queryForObject(model.sql(), Long.class, model.args());
        return count > 0 ? true : false;
    }

    /**
     * @param <T>
     * @param entityClass
     * @param column
     * @param arg
     * @return
     * @category 根据列检查唯一性
     */
    @Override
    public <T> boolean checkUniqueByColumn(Class<T> entityClass, Func<T, ?> func, Object arg) {
        return checkUniqueByColumn(entityClass, resolveColumnName(func), arg);
    }

    /**
     * @param <T>
     * @param entityClass
     * @return
     * @category 查询所有
     */
    @Override
    public <T> List<T> rawQueryListAll(Class<T> entityClass) {
        SQLModel model = selectSQL(Table.create(entityClass));
        return rawQueryList(entityClass, model.sql(), model.args());
    }

    /**
     * @param <T>
     * @param entityClass
     * @param where
     * @param order
     * @return
     * @category 根据where条件查询并排序
     */
    @Override
    public <T> List<T> rawQueryListByWhere(Class<T> entityClass, Where where, Order order) {
        SQLModel model = selectSQL(Table.create(entityClass), where, order);
        return rawQueryList(entityClass, model.sql(), model.args());
    }

    @Override
    public <T> List<T> rawQueryListByWhereDisableTenant(Class<T> entityClass, Where where, Order order) {
        SQLModel model = selectSQL(Table.create(entityClass), where, order);
        return rawQueryList(entityClass, disableTenant(model.sql()), model.args());
    }

    @Override
    public <T> List<T> rawQueryListByWhereWithTenant(String tenantId, Class<T> entityClass, Where where, Order order) {
        SQLModel model = selectSQL(Table.create(entityClass), where, order);
        return rawQueryList(entityClass, withTenant(model.sql(), tenantId), model.args());
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param where       查询条件
     * @param args
     * @return
     * @category 根据where条件查询
     */
    @Override
    public <T> List<T> rawQueryListByWhere(Class<T> entityClass, String where, Object... args) {
        SQLModel model = selectSQL(Table.create(entityClass), Where.create(where, args));
        return rawQueryList(entityClass, model.sql(), model.args());
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param column      列
     * @param arg         参数
     * @return
     * @category 根据列查询单一对象(多个会报错)
     */
    @Override
    public <T> T rawQueryObjectByColumn(Class<T> entityClass, String column, Object arg) {
        Where where = Where.create();
        where.eq(column, arg);
        SQLModel model = selectSQL(Table.create(entityClass), where);
        List<T> list = rawQueryList(entityClass, model.sql(), model.args());
        return getFirstObject(list);
    }

    @Override
    public <T> T rawQueryObjectByColumnDisableTenant(Class<T> entityClass, String column, Object arg) {
        Where where = Where.create();
        where.eq(column, arg);
        SQLModel model = selectSQL(Table.create(entityClass), where);
        List<T> list = rawQueryList(entityClass, disableTenant(model.sql()), model.args());
        return getFirstObject(list);
    }

    @Override
    public <T> T rawQueryObjectByColumnWithTenant(String tenantId, Class<T> entityClass, String column, Object arg) {
        Where where = Where.create();
        where.eq(column, arg);
        SQLModel model = selectSQL(Table.create(entityClass), where);
        List<T> list = rawQueryList(entityClass, withTenant(model.sql(), tenantId), model.args());
        return getFirstObject(list);
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param func        列
     * @param arg         参数
     * @return
     * @category 根据列查询单一对象(多个会报错)
     */
    @Override
    public <T> T rawQueryObjectByColumn(Class<T> entityClass, Func<T, ?> func, Object arg) {
        Where where = Where.create();
        where.eq(func, arg);
        SQLModel model = selectSQL(Table.create(entityClass), where);
        List<T> list = rawQueryList(entityClass, model.sql(), model.args());
        return getFirstObject(list);
    }

    @Override
    public <T> T rawQueryObjectByColumnDisableTenant(Class<T> entityClass, Func<T, ?> func, Object arg) {
        Where where = Where.create();
        where.eq(func, arg);
        SQLModel model = selectSQL(Table.create(entityClass), where);
        String sql = disableTenant(model.sql());
        List<T> list = rawQueryList(entityClass, sql, model.args());
        return getFirstObject(list);
    }

    @Override
    public <T> T rawQueryObjectByColumnWithTenant(String tenantId, Class<T> entityClass, Func<T, ?> func, Object arg) {
        Where where = Where.create();
        where.eq(func, arg);
        SQLModel model = selectSQL(Table.create(entityClass), where);
        String sql = withTenant(model.sql(), tenantId);
        List<T> list = rawQueryList(entityClass, sql, model.args());
        return getFirstObject(list);
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param where       where条件
     * @param arg
     * @return
     * @category 根据where条件查询单一对象(多个会报错)
     */
    @Override
    public <T> T rawQueryObjectByWhere(Class<T> entityClass, String where, Object... args) {
        SQLModel model = selectSQL(Table.create(entityClass), Where.create(where, args));
        return rawQueryObject(entityClass, model.sql(), model.args());
    }

    /**
     * @param <T>
     * @param clazz
     * @param sql
     * @param args
     * @return
     * @category 根据sql文查询并生成单个实例
     */
    @Override
    public <T> T rawQueryObject(Class<T> clazz, String sql, Object... args) {
        SqlRowSet rs = jdbc.queryForRowSet(sql, args);
        T bean = getFirstObject(EntityUtils.convertTo(rs, clazz));
        BlobColumnUtils.setBlobData(clazz, bean);
        return bean;
    }

    /**
     * @param <R>
     * @param entityClass
     * @param sqlObjects
     * @return
     * @category 查询单个对象
     */
    @Override
    public <R> R rawQueryObject(Class<R> entityClass, ISQLObject... sqlObjects) {
        SQLModel model = selectSQL(entityClass, sqlObjects);
        return rawQueryObject(entityClass, model.sql(), model.args());
    }

    @Override
    public <R> R rawQueryObjectDiableTenant(Class<R> entityClass, ISQLObject... sqlObjects) {
        SQLModel model = selectSQL(entityClass, sqlObjects);
        return rawQueryObject(entityClass, disableTenant(model.sql()), model.args());
    }

    @Override
    public <R> R rawQueryObjectWithTenant(String tenantId, Class<R> entityClass, ISQLObject... sqlObjects) {
        SQLModel model = selectSQL(entityClass, sqlObjects);
        return rawQueryObject(entityClass, withTenant(model.sql(), tenantId), model.args());
    }

    /**
     * @param entityClass 实体类
     * @param SqlWhere    where查询对象
     * @param arg
     * @return
     * @category根据where条件查询单一对象(多个会报错) @param <T>
     */
    @Override
    public <T> T rawQueryObjectByWhere(Class<T> entityClass, Where where) {
        SQLModel model = selectSQL(Table.create(entityClass), where);
        List<T> list = rawQueryList(entityClass, model.sql(), model.args());
        return getFirstObject(list);
    }

    @Override
    public <T> T rawQueryObjectByWhereDisableTenant(Class<T> entityClass, Where where) {
        SQLModel model = selectSQL(Table.create(entityClass), where);
        List<T> list = rawQueryList(entityClass, disableTenant(model.sql()), model.args());
        return getFirstObject(list);
    }

    @Override
    public <T> T rawQueryObjectByWhereWithTenant(String tenantId, Class<T> entityClass, Where where) {
        SQLModel model = selectSQL(Table.create(entityClass), where);
        List<T> list = rawQueryList(entityClass, withTenant(model.sql(), tenantId), model.args());
        return getFirstObject(list);
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param SqlSelect   选择对象
     * @param SqlWhere    条件对象
     * @param arg
     * @return
     * @category 根据where条件查询单一对象(多个会报错)
     */
    @Override
    public <T> T rawQueryObjectByWhere(Class<T> entityClass, Select select, Where SqlWhere) {
        SQLModel model = selectSQL(Table.create(entityClass), select, SqlWhere);
        List<T> list = rawQueryList(entityClass, model.sql(), model.args());
        return getFirstObject(list);
    }

    /**
     * @param <R>
     * @param <T>
     * @param clazz       结果类
     * @param entityClass 实体类
     * @param SqlSelect   选择对象
     * @param SqlWhere    查询对象
     * @param arg
     * @return
     * @category 根据where条件查询单一对象(多个会报错)
     */
    @Override
    public <R, T> R rawQueryObjectByWhere(Class<R> clazz, Class<T> entityClass, Select select, Where where) {
        SQLModel model = selectSQL(Table.create(entityClass), select, where);
        return rawQueryObject(clazz, model.sql(), model.args());
    }

    /**
     * @param <T>
     * @param objectClass 普通类
     * @param tableName   表名
     * @param select      选择列对象
     * @param column      查询列名
     * @param args
     * @return
     * @category 根据列 in 查询
     */
    @Override
    public <T> List<T> rawQueryListIn(Class<T> objectClass, String tableName, Select select, String column, Object... args) {
        SQLModel model = selectSQL(Table.create(tableName), select, Where.create().in(column, args));
        SqlRowSet rs = jdbc.queryForRowSet(model.sql(), model.args());
        return EntityUtils.convertTo(rs, objectClass);
    }

    /**
     * @param <T>
     * @param objectClass
     * @param column
     * @param args
     * @return
     * @category 根据in查询查询
     */
    @Override
    public <T> List<T> rawQueryListIn(Class<T> objectClass, String column, Object... args) {
        SQLModel model = selectSQL(Table.create(objectClass), Where.create().in(column, args));
        SqlRowSet rs = jdbc.queryForRowSet(model.sql(), model.args());
        return EntityUtils.convertTo(rs, objectClass);
    }

    /**
     * @param sql
     * @category 原生执行sql文
     */
    @Override
    @Transactional
    public void rawExcute(String sql) {
        jdbc.execute(sql);
    }

    /**
     * @param sql  原生sql文
     * @param args
     * @return
     * @category 原生执行update语句(delete insert update)
     */
    @Override
    public int rawUpdate(String sql, Object... args) {
        return jdbcUpdate(sql, args);
    }

    /**
     * @param <T>
     * @param entityClass
     * @param update
     * @param where
     * @return
     * @category 根据where条件更新给定的列
     */
    @Override
    public <T> int rawUpdate(String tableName, Update update, Where where) {
        SQLModel model = updateSQL(Table.create(tableName), update, where);
        return jdbcUpdate(model.sql(), model.args());
    }

    /**
     * @param <T>
     * @param entityClass
     * @param update
     * @param where
     * @return
     * @category 根据where条件更新给定的列
     */
    @Override
    public <T> int rawUpdate(Class<T> entityClass, Update update, Where where) {
        SQLModel model = updateSQL(Table.create(entityClass), update, where);
        BlobColumnUtils.saveBlob("update", entityClass, model);
        return jdbcUpdate(model.sql(), model.args());
    }

    /**
     * @param <T>
     * @param entity 实体类
     * @return
     * @category 根据实体的主键进行更新(不包含空值)
     */
    @Override
    public <T> int rawUpdateById(T entity) {
        return rawUpdateById(entity, false);
    }

    @Override
    public <T> int rawUpdateByIdDisableTenant(T entity) {
        return rawUpdateByIdDisableTenant(entity, false);
    }

    /**
     * @param <T>
     * @param entity
     * @return
     * @category 根据实体的主键进行更新(包含空值)
     */
    @Override
    public <T> int rawUpdateByIdIncludeNull(T entity) {
        SQLModel model = updateSQL(entity, true, null);
        BlobColumnUtils.saveBlob("update", entity, model);
        return jdbcUpdate(model.sql(), model.args());
    }

    /**
     * 根据实体的主键进行更新
     *
     * @param <T>
     * @param entity
     * @param includeNull 更新是否包含空值 true=包含
     * @return
     */
    @Override
    public <T> int rawUpdateById(T entity, boolean includeNull) {
        SQLModel model = updateSQL(entity, includeNull, null);
        BlobColumnUtils.saveBlob("update", entity, model);
        return jdbcUpdate(model.sql(), model.args());
    }

    @Override
    public <T> int rawUpdateByIdDisableTenant(T entity, boolean includeNull) {
        SQLModel model = updateSQL(entity, includeNull, null);
        BlobColumnUtils.saveBlob("update", entity, model);
        // return jdbc.update(disableTenant(model.sql()), model.args());
        return jdbcUpdate(disableTenant(model.sql()), model.args());
    }

    @Override
    public <T> int rawUpdateByIdWithTenant(String tenantId, T entity, boolean includeNull) {
        SQLModel model = updateSQL(entity, includeNull, null);
        BlobColumnUtils.saveBlob("update", entity, model);
        // return jdbc.update(disableTenant(model.sql()), model.args());
        return jdbcUpdate(withTenant(model.sql(), tenantId), model.args());
    }

    /**
     * @param <T>
     * @param entities  更新实体列表
     * @param batchSize 每多少条提交一次(暂不支持)
     * @return 1:批量更新成功；0：批量更新失败
     * @category 根据主键批量更新_空值不更新(支持事务)
     */
    @Deprecated
    public <T> int updateByIdBatchWithTenant(String tenant_id, List<T> entities, int batchSize) {
        if (StringUtils.isEmpty(entities)) {
            return 0;
        }
        if (batchSize <= 0) {
            batchSize = 500;
        }
        // 1.把相同的语句分组
        Map<String, ExecParam> map = new HashMap<String, ExecParam>();
        Date updateDt = new Date();
        for (int i = 0; i < entities.size(); i++) {
            T entity = entities.get(i);
            SQLModel model = updateSQL(entity, false, updateDt);
            String sql = model.sql();
            String key = MD5Utils.md5(sql);
            if (map.containsKey(key)) {
                map.get(key).getBatchArgs().add(model.args());
            } else {
                ExecParam param = new ExecParam();
                param.setBatchSize(batchSize);
                param.setSql(sql);
                param.getBatchArgs().add(model.args());
                param.setArgType(model.argType());
                map.put(key, param);
            }
        }
        Connection conn = this.getConnection();
        PreparedStatement ps = null;
        try {
            conn.setAutoCommit(false);// 开启事务
            for (String key : map.keySet()) {// 同一个语句，参数不 同按照语句分别提交
                ExecParam param = map.get(key);
                this.executeBatch(conn, ps, withTenant(param.getSql(), tenant_id), param.getBatchArgs());
            }
            conn.commit();// 提交事务
        } catch (Exception e) {
            try {
                conn.rollback();// 回滚事务
            } catch (SQLException e1) {
            }
            log.error("updateByIdBatch错误", e);
            throw new RuntimeException(e);
        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    log.error(e);
                }
            }
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                } catch (SQLException e) {
                }
                closeConnection(conn);
            }
        }
        return 1;
    }

    /**
     * @param <T>
     * @param entity   实体类
     * @param SqlWhere where条件对象
     * @param args
     * @return
     * @category 根据where条件更新
     */
    @Override
    public <T> int rawUpdateByWhere(T entity, Where where) {
        SQLModel model = updateSQL(Table.create(entity.getClass()), where);
        BlobColumnUtils.saveBlob("update", entity, model);
        return jdbcUpdate(model.sql(), model.args());
    }

    /**
     * @param sql
     * @return
     * @category 原生批量执行update语句
     */
    @Override
    public int[] rawUpdateBatch(String sql) {
        return this.jdbcBatchUpdate(sql);
    }

    /**
     * 批量执行语句
     *
     * @param sql
     * @return
     */
    private int[] jdbcBatchUpdate(String sql) {
        List<Integer> rtnList = new ArrayList<Integer>();
        TransactionTemplate transactionTemplate = getTransactionTemplate(jdbc.getDataSource());
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus transactionStatus) {
                try {
                    int[] retval = jdbc.batchUpdate(sql);
                    if (retval != null && retval.length > 0) {
                        List<Integer> intList = Arrays.stream(retval).boxed().collect(Collectors.toList());
                        rtnList.addAll(intList);
                    }
                } catch (Exception e) {
                    transactionStatus.setRollbackOnly();// 回滚
                    throw new RuntimeException(e);
                }
            }
        });
        if (rtnList.size() == 0) {
            return this.getRtnValue(1, 0);
        } else {
            return this.intList2ary(rtnList);
        }
    }

    /**
     * @param sql
     * @return
     * @category 原生批量执行update语句
     */
    @Override
    public int updateBatch(String sql) {
        return this.isBatchSuccess(this.rawUpdateBatch(sql));
    }

    /**
     * @param sql
     * @param batchArgs
     * @return
     * @category 原生批量执行update语句
     */
    @Override
    public int[] rawUpdateBatch(String sql, List<Object[]> batchArgs) {
        return this.jdbcUpdateBatch(sql, batchArgs);
    }

    /**
     * 批量更新语句（支持事务）
     *
     * @param sql
     * @param batchArgs
     * @return
     */
    private int[] jdbcUpdateBatch(String sql, List<Object[]> batchArgs) {
        List<Integer> rtnList = new ArrayList<Integer>();
        TransactionTemplate transactionTemplate = getTransactionTemplate(jdbc.getDataSource());
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus transactionStatus) {
                try {
                    int[] retval = jdbc.batchUpdate(sql, batchArgs);
                    if (retval != null && retval.length > 0) {
                        List<Integer> intList = Arrays.stream(retval).boxed().collect(Collectors.toList());
                        rtnList.addAll(intList);
                    }
                } catch (Exception e) {
                    transactionStatus.setRollbackOnly();// 回滚
                    throw new RuntimeException(e);
                }
            }
        });
        if (rtnList.size() == 0) {
            return this.getRtnValue(batchArgs.size(), 0);
        } else {
            return this.intList2ary(rtnList);
        }
    }

    /**
     * @param sql
     * @param batchArgs
     * @return
     * @category 原生批量执行update语句
     */
    @Override
    public int updateBatch(String sql, List<Object[]> batchArgs) {
        return this.isBatchSuccess(this.rawUpdateBatch(sql, batchArgs));
    }

    /**
     * 批量更新
     *
     * @param <T>
     * @param sql
     * @param batchArgs 更新值
     * @param batchSize 每次批量更新数量
     * @return
     */
    @Override
    public int batchSqlUpdate(String sql, List<Object[]> batchArgs, int[] argType, int batchSize) {
        ExecParam param = new ExecParam();
        param.setArgType(argType);
        param.setBatchArgs(batchArgs);
        param.setSql(sql);
        param.setBatchSize(batchSize);
        return this.batchSqlUpdate(param);
    }

    /**
     * 获得返回值
     *
     * @param size
     * @param val
     * @return
     */
    private int[] getRtnValue(int size, int val) {
        int[] i = new int[size];
        for (int n : i) {
            i[n] = val;
        }
        return i;
    }

    /**
     * @param <T>
     * @param entities
     * @param includeNull
     * @return
     * @category 根据ID进行批量更新_空值不更新
     */
    @Override
    @Transactional
    public <T> int[] rawUpdateByIdBatch(List<T> entities) {
        // this.updateByIdBatch(entities, 0);
        // return getRtnValue(entities.size(),1);
        int[] res = new int[entities.size()];
        Date updateDt = new Date();
        for (int i = 0; i < entities.size(); i++) {
            T entity = entities.get(i);
            SQLModel model = updateSQL(entity, false, updateDt);
            BlobColumnUtils.saveBlob("update", entity, model);
            res[i] = jdbcUpdate(model.sql(), model.args());

        }
        return res;
    }

    /**
     * 获得批量更新执行参数
     *
     * @param <T>
     * @param entities
     * @param batchSize
     * @return
     */
    private <T> ExecParam getUpdateExecParam(List<T> entities, Integer batchSize) {
        if (StringUtils.isNotEmpty(entities)) {
            SQLModel model = updateSQL(entities.get(0), true, null);
            List<Object[]> batchArgs = new ArrayList<Object[]>();
            try {
                for (T entity : entities) {
                    Object[] args = new Object[model.getFields().size()];
                    for (int i = 0; i < model.getFields().size(); i++) {
                        args[i] = EntityUtils.fieldValue(model.getFields().get(i), entity);
                    }
                    batchArgs.add(args);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            ExecParam param = new ExecParam();
            param.setBatchSize(batchSize);
            param.setSql(model.sql());
            param.setBatchArgs(batchArgs);
            param.setArgType(model.argType());
            param.setTableName(model.getTableName());
            param.setFields(model.getFields());
            return param;
        } else {
            return null;
        }
    }

    /**
     * @param <T>
     * @param entities 更新实体列表
     * @return
     * @category 根据ID主键进行更新_空值也更新
     */
    @Override
    public <T> int[] rawUpdateByIdBatchIncludeNull(List<T> entities) {
        ExecParam param = getUpdateExecParam(entities, 0);
        if (param != null) {
            BlobColumnUtils.saveBlob("update", param);
            return rawUpdateBatch(param.getSql(), param.getBatchArgs());
        } else {
            return null;
        }
    }

    /**
     * 批量更新，空值也更新
     *
     * @param <T>
     * @param entities
     * @return
     */
    @Override
    public <T> int updateByIdBatchIncludeNull(List<T> entities, int batchSize) {
        ExecParam param = getUpdateExecParam(entities, batchSize);
        if (param != null) {
            BlobColumnUtils.saveBlob("update", param);
            if (batchSize > 0) {

                return this.batchSqlUpdate(param.getSql(), param.getBatchArgs(), param.getArgType(), param.getBatchSize());
            } else {
                return isBatchSuccess(rawUpdateBatch(param.getSql(), param.getBatchArgs()));
            }
        } else {
            return 0;
        }
    }

    /**
     * 判断批量执行是否成功
     *
     * @param rtns
     * @return
     */
    private int isBatchSuccess(int[] rtns) {
        if (rtns == null) {
            return 0;
        }
        boolean bln = Arrays.stream(rtns).anyMatch(i -> i == 0);// Arrays.asList(rtns).contains(0);
        return bln ? 0 : 1;
    }

    /**
     * @param <T>
     * @param entities 更新实体列表
     * @return 1:批量更新成功；0：批量更新失败
     * @category 根据主键批量更新_空值不更新(支持事务)
     */
    @Override
    public <T> int updateByIdBatch(List<T> entities) {
        return updateByIdBatch(entities, 0);
        // return this.isBatchSuccess(this.rawUpdateByIdBatch(entities));
    }

    /**
     * /**
     *
     * @param <T>
     * @param entities  更新实体列表
     * @param batchSize 每多少条提交一次(暂不支持)
     * @return 1:批量更新成功；0：批量更新失败
     * @category 根据主键批量更新_空值不更新(支持事务)
     */
    @Override
    public <T> int updateByIdBatch(List<T> entities, int batchSize) {
        if (StringUtils.isEmpty(entities)) {
            return 0;
        }
        if (batchSize <= 0) {
            batchSize = 500;
        }
        // 1.把相同的语句分组
        Map<String, ExecParam> map = new HashMap<String, ExecParam>();
        Date updateDt = new Date();
        for (int i = 0; i < entities.size(); i++) {
            T entity = entities.get(i);
            SQLModel model = updateSQL(entity, false, updateDt);
            String sql = model.sql();
            String key = MD5Utils.md5(sql);
            if (map.containsKey(key)) {
                map.get(key).getBatchArgs().add(model.args());
            } else {
                ExecParam param = new ExecParam();
                param.setBatchSize(batchSize);
                param.setSql(sql);
                param.getBatchArgs().add(model.args());
                param.setArgType(model.argType());
                param.setFields(model.getFields());
                param.setTableName(model.getTableName());
                map.put(key, param);
            }
        }
//		for (String key : map.keySet()) {
//			try {
//				this.batchSqlUpdate(map.get(key));// 存在事务问题，有可能更新部分数据
//			} catch (Exception e) {
//				log.error("批量更新出错", e);
//				throw e;
//			}
//		}
        Connection conn = this.getConnection();
        PreparedStatement ps = null;
        try {
            conn.setAutoCommit(false);// 开启事务
            for (String key : map.keySet()) {// 同一个语句，参数不 同按照语句分别提交
                ExecParam param = map.get(key);
                BlobColumnUtils.saveBlob("update", param);
                this.executeBatch(conn, ps, param.getSql(), param.getBatchArgs());
            }
            conn.commit();// 提交事务
        } catch (Exception e) {
            try {
                conn.rollback();// 回滚事务
            } catch (SQLException e1) {
            }
            log.error("updateByIdBatch错误", e);
            throw new RuntimeException(e);
        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    log.error(e);
                }
            }
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                } catch (SQLException e) {
                }
                closeConnection(conn);
            }
        }
        return 1;
    }

    /**
     * @param <T>
     * @param entity        实体对象
     * @param updateColumns 更新指定列
     * @return
     * @category 根据主键更新指定的列
     *
     * <pre>
     * 		例:	根据主键更新名称字段
     * 			Entity t=new Entity();
     * 			t.setId("1111");
     * 			t.setName("name")
     * 			rawUpdateById(t,new String[]{"name"});
     * 		sql:
     * 			update t set name=? where id=?;
     *           </pre>
     */
    @Override
    public <T> int rawUpdateById(T entity, String[] updateColumns) {
        SQLModel model = updateSQL(entity, updateColumns);
        BlobColumnUtils.saveBlob("update", entity, model);
        return jdbcUpdate(model.sql(), model.args());
    }

    /**
     * @param <T>
     * @param entity
     * @param updateColumns
     * @param whereColumns
     * @return
     * @category 根据where条件更新指定的列
     *
     * <pre>
     * 		例:	根据主键更新名称字段
     * 			Entity t=new Entity();
     * 			t.setId("1111");
     * 			t.setName("name")
     * 			t.setAge(5);
     * 			rawUpdateByWhere(t,new String[]{"name"},String[]{"id","age"});
     * 		sql:
     * 			update t set name=? where id=?;
     *           </pre>
     */
    @Override
    public <T> int rawUpdateByWhere(T entity, String[] updateColumns, String[] whereColumns) {
        SQLModel model = updateSQL(entity, updateColumns, whereColumns);
        BlobColumnUtils.saveBlob("update", entity, model);
        return jdbcUpdate(model.sql(), model.args());
    }

    /**
     * @param <T>
     * @param entities      实体对象列表
     * @param updateColumns 更新指定的列
     * @return
     * @category 根据ID批量更新指定的列
     *
     * <pre>
     * 		例:	根据主键更新名称字段
     * 			Entity t=new Entity();
     * 			t.setId("1111");
     * 			t.setName("name")
     * 			t.setAge(5);
     * 			List<Entity>ts=new ArrayList<Entity>();
     * 			ts.add(t);
     * 			...
     * 			rawUpdateByIdBatch(ts,new String[]{"name"});
     * 		sql:
     * 			update t set name=? where id=?;
     *           </pre>
     */
    @Override
    public <T> int[] rawUpdateByIdBatch(List<T> entities, String[] updateColumns) {
        int i = this.updateByIdBatch(entities, updateColumns, 500);
        if (i > 0) {
            return this.getRtnValue(entities.size(), 1);
        } else {
            return null;
        }
//		SQLModel model = updateSQL(entities.get(0), updateColumns);
//		List<Object[]> argBatch = new ArrayList<Object[]>();
//		try {
//			for (int i = 0; i < entities.size(); i++) {
//				Object[] values = new Object[model.getFields().size()];
//				for (int j = 0; j < model.getFields().size(); j++) {
//					values[j] = EntityUtils.fieldValue(model.getFields().get(j), entities.get(i));
//				}
//				argBatch.add(values);
//			}
//		} catch (Exception e) {
//			throw new RuntimeException(e);
//		}
//		return jdbc.batchUpdate(model.sql(), argBatch);
    }

    /**
     * 批量更新
     *
     * @param <T>
     * @param entities
     * @param batchSize     每次批量插入数量
     * @param updateColumns 更新字段
     * @return
     */
    @Override
    public <T> int updateByIdBatch(List<T> entities, String[] updateColumns, int batchSize) {
        SQLModel model = updateSQL(entities.get(0), updateColumns);
        List<Object[]> argBatch = new ArrayList<Object[]>();
        try {
            for (int i = 0; i < entities.size(); i++) {
                Object[] values = new Object[model.getFields().size()];
                for (int j = 0; j < model.getFields().size(); j++) {
                    values[j] = EntityUtils.fieldValue(model.getFields().get(j), entities.get(i));
                }
                argBatch.add(values);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        BlobColumnUtils.saveBlob("update", model.getTableName(), model.getFields(), argBatch);
        if (batchSize > 0) {// 按照批次，批量更新
            return this.batchSqlUpdate(model.sql(), argBatch, model.argType(), batchSize);
        } else {
            return this.isBatchSuccess(rawUpdateBatch(model.sql(), argBatch));
        }
    }

    /**
     * @param <T>
     * @param entities      实体对象列表
     * @param updateColumns 更新指定的列
     * @return
     * @category 根据ID批量更新指定的列
     *
     * <pre>
     * 		例:	根据主键更新名称字段
     * 			Entity t=new Entity();
     * 			t.setId("1111");
     * 			t.setName("name")
     * 			t.setAge(5);
     * 			List<Entity>ts=new ArrayList<Entity>();
     * 			ts.add(t);
     * 			...
     * 			rawUpdateByIdBatch(ts,new String[]{"name"});
     * 		sql:
     * 			update t set name=? where id=?;
     *           </pre>
     */
    @Override
    public <T> int updateByIdBatch(List<T> entities, String... updateColumns) {
        return this.updateByIdBatch(entities, updateColumns, 500);
    }

    /**
     * @param <T>
     * @param entities      实体对象列表
     * @param updateColumns 更新指定的列
     * @return
     * @category 根据where字段批量更新指定的列
     *
     * <pre>
     * 		例:	根据主键更新名称字段
     * 			Entity t=new Entity();
     * 			t.setId("1111");
     * 			t.setName("name")
     * 			t.setAge(5);
     * 			List<Entity>ts=new ArrayList<Entity>();
     * 			ts.add(t);
     * 			...
     * 			rawUpdateByWhereBatch(ts,new String[]{"name"},String[]{"id","age"});
     * 		sql:
     * 			update t set name=? where id=?;
     *           </pre>
     */
    @Override
    public <T> int[] rawUpdateByWhereBatch(List<T> entities, String[] updateColumns, String[] whereColumns) {
        SQLModel model = updateSQL(entities.get(0), updateColumns, whereColumns);
        List<Object[]> argBatch = new ArrayList<Object[]>();
        try {
            for (int i = 0; i < entities.size(); i++) {
                Object[] args = new Object[model.getFields().size()];
                for (int j = 0; j < model.getFields().size(); j++) {
                    Field field = model.getFields().get(j);
                    args[j] = EntityUtils.fieldValue(field, entities.get(i));
                }
                argBatch.add(args);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        BlobColumnUtils.saveBlob("update", model.getTableName(), model.getFields(), argBatch);
        int i = this.batchSqlUpdate(model.sql(), argBatch, model.argType(), 500);
        if (i == 1) {
            return this.getRtnValue(entities.size(), 1);
        } else {
            return null;

        }
    }

    /**
     * @param <T>
     * @param entities      实体对象列表
     * @param updateColumns 更新指定的列
     * @return
     * @category 根据where字段批量更新指定的列
     *
     * <pre>
     * 		例:	根据主键更新名称字段
     * 			Entity t=new Entity();
     * 			t.setId("1111");
     * 			t.setName("name")
     * 			t.setAge(5);
     * 			List<Entity>ts=new ArrayList<Entity>();
     * 			ts.add(t);
     * 			...
     * 			rawUpdateByWhereBatch(ts,new String[]{"name"},String[]{"id","age"});
     * 		sql:
     * 			update t set name=? where id=?;
     *           </pre>
     */
    @Override
    public <T> int updateByWhereBatch(List<T> entities, String[] updateColumns, String[] whereColumns) {
        return this.isBatchSuccess(this.rawUpdateByWhereBatch(entities, updateColumns, whereColumns));
    }

    /**
     * @param sql
     * @param args
     * @return
     * @category 原生执行删除语句
     */
    @Override
    public int rawDelete(String sql, Object... args) {
        return jdbcUpdate(sql, args);
    }

    /**
     * @param <T>
     * @param entity 实体类
     * @return
     * @category 根据主键删除
     */
    @Override
    public <T> int rawDeleteById(T entity) {
        SQLModel model = deleteSQL(entity);
        BlobColumnUtils.saveBlob("delete", entity, model);
        return jdbcUpdate(model.sql(), model.args());
    }

    @Override
    public <T> int rawDeleteByIdDisableTenant(T entity) {
        SQLModel model = deleteSQL(entity);
        BlobColumnUtils.saveBlob("delete", entity, model);
        return jdbcUpdate(disableTenant(model.sql()), model.args());
    }

    @Override
    public <T> int rawDeleteByIdWithTenant(String tenantId, T entity) {
        SQLModel model = deleteSQL(entity);
        BlobColumnUtils.saveBlob("delete", entity, model);
        return jdbcUpdate(withTenant(model.sql(), tenantId), model.args());
    }

    /**
     * @param <T>
     * @param entities 实体对象列表
     * @return
     * @category 根据实体主键批量删除
     */
    @Override
    public <T> int[] rawDeleteByIdBatch(List<T> entities) {
        if (StringUtils.isEmpty(entities)) {
            return null;
        }
        ExecParam param = getDeleteExecParam(entities, 0);
        BlobColumnUtils.saveBlob("delete", param);
        return rawUpdateBatch(param.getSql(), param.getBatchArgs());
    }

    /**
     * @param <T>
     * @param entities 实体对象列表
     * @return
     * @category 根据实体主键批量删除
     */
    @Override
    public <T> int deleteByIdBatch(List<T> entities) {
        return this.isBatchSuccess(this.rawDeleteByIdBatch(entities));
    }

    /**
     * 获得批量删除执行参数
     *
     * @param <T>
     * @param entities
     * @param batchSize
     * @return
     */
    private <T> ExecParam getDeleteExecParam(List<T> entities, Integer batchSize) {
        if (StringUtils.isNotEmpty(entities)) {
            SQLModel model = deleteSQL(entities.get(0));
            List<Object[]> batchArgs = new ArrayList<Object[]>();
            List<Field> fieldIds = model.getFields();// EntityUtils.idFields(entities.get(0).getClass());
            try {
                for (T entity : entities) {
                    Object[] args = new Object[fieldIds.size()];
                    for (int i = 0; i < fieldIds.size(); i++) {
                        args[i] = EntityUtils.fieldValue(fieldIds.get(i), entity);
                    }
                    batchArgs.add(args);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            ExecParam param = new ExecParam();
            param.setBatchSize(batchSize);
            param.setSql(model.sql());
            param.setBatchArgs(batchArgs);
            param.setArgType(model.argType());
            param.setTableName(model.getTableName());
            param.setFields(fieldIds);

            return param;
        } else {
            return null;
        }
    }

    /**
     * 批量删除
     */
    @Override
    public <T> int deleteByIdBatch(List<T> entities, int batchSize) {
        if (StringUtils.isEmpty(entities)) {
            return 0;
        }
        if (batchSize > 0) {
            ExecParam param = getDeleteExecParam(entities, batchSize);
            BlobColumnUtils.saveBlob("delete", param);
            return this.batchSqlUpdate(param.getSql(), param.getBatchArgs(), param.getArgType(), batchSize);
        } else {
            return this.deleteByIdBatch(entities);
        }
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param column      where条件列名
     * @param arg
     * @return
     * @category 根据列字段删除
     */
    @Override
    public <T> int rawDeleteByColumn(Class<?> entityClass, String column, Object arg) {
        return rawDeleteByColumn(entityClass, new String[]{column}, arg);
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param columns     where条件列名集合
     * @param args
     * @return
     * @category 根据列字段删除
     */
    @Override
    public <T> int rawDeleteByColumn(Class<?> entityClass, String[] columns, Object... args) {
        SQLModel model = deleteSQL(EntityUtils.tableName(entityClass), columns, args);
        return jdbcUpdate(model.sql(), model.args());
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param column      查询 in 列名
     * @param args
     * @return
     * @category 删除字段包含的内容
     */
    @Override
    public <T> int[] rawDeleteIn(Class<?> entityClass, String column, Object... args) {
        SQLModel model = deleteSQL(EntityUtils.tableName(entityClass), column, args);
        List<Object[]> batchArgs = new ArrayList<Object[]>();
        for (int i = 0; i < args.length; i++) {
            batchArgs.add(new Object[]{args[i]});
        }
        return rawUpdateBatch(model.sql(), batchArgs);
    }

    @Override
    public <T> int[] rawDeleteInDisableTenant(Class<?> entityClass, String column, Object... args) {
        SQLModel model = deleteSQL(EntityUtils.tableName(entityClass), column, args);
        List<Object[]> batchArgs = new ArrayList<Object[]>();
        for (int i = 0; i < args.length; i++) {
            batchArgs.add(new Object[]{args[i]});
        }
        return rawUpdateBatch(disableTenant(model.sql()), batchArgs);
    }

    @Override
    public <T> int[] rawDeleteInWithTenant(String tenantId, Class<?> entityClass, String column, Object... args) {
        SQLModel model = deleteSQL(EntityUtils.tableName(entityClass), column, args);
        List<Object[]> batchArgs = new ArrayList<Object[]>();
        for (int i = 0; i < args.length; i++) {
            batchArgs.add(new Object[]{args[i]});
        }
        return rawUpdateBatch(withTenant(model.sql(), tenantId), batchArgs);
    }

    /**
     * @param <T>
     * @param entityClass 实体类
     * @param column      查询 in 列名
     * @param args
     * @return
     * @category 删除字段包含的内容
     */
    @Override
    public <T> int deleteIn(Class<?> entityClass, String column, Object... args) {
        return isBatchSuccess(this.rawDeleteIn(entityClass, column, args));
    }

    /**
     * @param sql       完整原生 sql 文
     * @param batchArgs sql参数
     * @return
     * @category 原生执行批量删除语句
     */
    @Override
    @Transactional
    public int[] rawDeleteBatch(String sql, List<Object[]> batchArgs) {
        return rawUpdateBatch(sql, batchArgs);
    }

    /**
     * @param sql       完整原生 sql 文
     * @param batchArgs sql参数
     * @return
     * @category 原生执行批量删除语句
     */
    @Override
    public int deleteBatch(String sql, List<Object[]> batchArgs) {
        return this.isBatchSuccess(this.rawDeleteBatch(sql, batchArgs));
    }

    /**
     * @param <T>
     * @param entityClass
     * @param where
     * @param args
     * @return
     * @category 原生根据where条件执行删除语句
     */
    @Override
    public <T> int rawDeleteByWhere(Class<T> entityClass, Where where) {
        SQLModel model = deleteSQL(EntityUtils.tableName(entityClass), where);
        return jdbcUpdate(model.sql(), model.args());
    }

    /**
     * @param <R>
     * @param <T>
     * @param clazz
     * @param entityClass
     * @param where
     * @param args
     * @return
     * @category 根据where条件统计
     */
    @Override
    public <T> Long rawCountByWhere(Class<T> entityClass, String where, Object... args) {
        return rawQueryObjectByWhere(Long.class, entityClass, Select.create("").countColumn("1", "count"), Where.create(where, args));
    }

    /**
     * @param <R>
     * @param <T>
     * @param clazz
     * @param entityClass
     * @param where
     * @category 根据where条件统计
     */
    @Override
    public <T> Long countByWhere(Class<T> entityClass, Where where) {
        return rawQueryObjectByWhere(Long.class, entityClass, Select.create("").countColumn("1", "count"), where);
    }

    /**
     * @param sql  原生任意select count(*)语句
     * @param args 参数数组
     * @return
     * @category 原生sql文统计
     * @since 2021.9.1
     */
    @Deprecated
    @Override
    public long rawCount(String sql, Object... args) {
        return jdbc.queryForObject(sql, Long.class, args);
    }

    /**
     * @param <T>
     * @param list
     * @return
     * @category 从一个列表得到唯一对象
     */
    private <T> T getFirstObject(List<T> list) {
        if (list == null || list.size() == 0) {
            return null;
        } else if (list.size() > 0) {
            return list.get(0);
        } else {
            throw new RuntimeErrorException(null, "记录不唯一");
        }
    }

    /**
     * @param <T>
     * @param entity 实体对象
     * @return
     * @category 生成实体的插入语句
     */
    private <T> SQLModel insertSQL(T entity) {
        StringBuffer sql = new StringBuffer();
        @SuppressWarnings("unchecked") Class<T> clazz = (Class<T>) entity.getClass();
        List<Field> fields = EntityUtils.fields(clazz);
        List<Field> fieldList = new ArrayList<Field>();
        String tableName = EntityUtils.tableName(clazz);
        sql.append("insert into ").append(tableName).append("(");
        String tmp = "";
        List<Object> args = new ArrayList<Object>();
        Map<String, Object> maps = new HashMap<String, Object>();
        try {
            for (Field field : fields) {
                if (EntityUtils.isJavaClass(field.getType())) {// java基本类型
                    String columnName = EntityUtils.columnName(field, true);
                    if (!maps.containsKey(columnName)) {
                        Object value = EntityUtils.fieldValue(field, entity);
                        if (isCreateByField(field) || isUpdateByField(field) || isCreateTimeField(field) || isUpdateTimeField(field)) {
                        } else {
                            fieldList.add(field);
                            sql.append(tmp).append(getColumn(columnName));
                            args.add(value);
                            maps.put(columnName, value);
                        }
                    }
                } else {// 联合主键
                    List<Field> fields1 = EntityUtils.fields(field.getType());
                    Object subClass = EntityUtils.fieldValue(field, entity);
                    for (Field field1 : fields1) {
                        String columnName = EntityUtils.columnName(field1, true);
                        if (!maps.containsKey(columnName)) {
                            if (isCreateByField(field) || isUpdateByField(field) || isCreateTimeField(field) || isUpdateTimeField(field)) {
                            } else {
                                Object value = EntityUtils.fieldValue(field1, subClass);
                                fieldList.add(field1);
                                sql.append(tmp).append(getColumn(columnName));
                                args.add(value);
                                tmp = ",";
                                maps.put(columnName, value);
                            }
                        }
                    }
                }
                tmp = ",";
            }

            sql.append(tmp).append(CREATE_BY_ORG);// .append(",").append(CREATE_BY_ORGNAME);
            sql.append(",").append(CREATE_BY_POST);// .append(",").append(CREATE_BY_POSTNAME);
            // 创建人，创建时间字段
            sql.append(",").append(CREATE_TIME).append(",").append(CREATE_BY);

            sql.append(") values (").append(String.join(",", Collections.nCopies(args.size(), "?")));

            SysUser user = this.getUser();
            // 机构
            sql.append(",");
            sql.append(this.getUserProp(user, CREATE_BY_ORG));
            // sql.append(",");
            // sql.append(this.getUserProp(user, CREATE_BY_ORGNAME));
            // 岗位
            sql.append(",");
            sql.append(this.getUserProp(user, CREATE_BY_POST));
            // sql.append(",");
            // sql.append(this.getUserProp(user, CREATE_BY_POSTNAME));

            // 创建时间
            sql.append(",");
            if (this.isOracle()) {
                sql.append("SYSDATE");
            } else {
                sql.append("'");
                sql.append(DateTimeUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
                sql.append("'");
            }
            // 创建人
            sql.append(",");
            sql.append(this.getUserProp(user, CREATE_BY));
            sql.append(")");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        SQLModel model = new SQLModel();
        model.setFields(fieldList);
        model.setSql(sql.toString());
        model.setArgs(args);
        model.setTableName(tableName);
        return model;
    }

    /**
     * 是否为Oracle类型数据库（Oracle 和达梦）
     *
     * @return
     */
    @Override
    public boolean isOracle() {
        return DBTypeUtils.isOracle(getDbType());
    }

    @Override
    public boolean isMySql() {
        return DBTypeUtils.isMySql(getDbType());
    }

    @Override
    public boolean isSqlServer() {
        return DBTypeUtils.isSqlServer(getDbType());
    }

    @Override
    public boolean isKingBase() {
        return DBTypeUtils.isKingBase(getDbType());
    }

    /**
     * 是否为PG类型数据库（pg 和 高斯）
     *
     * @return
     */
    @Override
    public boolean isPG() {
        return DBTypeUtils.isPg(getDbType());// return ColumnUtils.isPG(getDbType());
    }

    /**
     * @param tableName 查询表格名称
     * @param where     where查询对象
     * @return
     * @category 生成根据条件查询数量语句
     */
    private SQLModel countSQL(String tableName, Where where) {
        SQLModel model = new SQLModel();
        StringBuffer sql = new StringBuffer();
        sql.append("select count(*) as count from ").append(tableName);
        if (where != null) {
            sql.append(where.sql(getDbType()));
            model.setArgs(where.args(getDbType()));
        }
        model.setSql(sql.toString());
        log.trace(sql.toString(), model.args());
        return model;
    }

    /**
     * @param sqlObjects 查询对象(table,where,page有效)
     * @return
     * @category 生成统计count语法
     */
    private SQLModel countSQL(ISQLObject... sqlObjects) {
        Map<String, Object> objs = SqlObjectMap((Object[]) sqlObjects);
        StringBuilder sql = new StringBuilder();
        List<Object> args = new ArrayList<Object>();

        sql.append("select count(1)");

        if (objs.get("table") == null) {
            throw new RuntimeException("查询语句必须提供表格名称");
        } else {
            sql.append(" from ").append(((Table) objs.get("table")).sql());
        }
        if (objs.get("where") != null) {
            Where where = (Where) objs.get("where");
            sql.append(" ").append(where.sql(getDbType()));
            args.addAll(where.args(getDbType()));
        }
        if (objs.get("page") != null) {
            Pagination<?> page = (Pagination<?>) objs.get("page");
            sql.append(" ").append(page.sql(getDbType()));
        }
        log.debug("countSQL:" + sql.toString() + "  args:" + args);
        return SQLModel.create(sql.toString(), args);
    }

    /**
     * @param tableName 表格名称
     * @param objects   更新对象(table,where,update对象有效)
     * @return
     * @category 根据条件实例生成更新语句
     */
    private SQLModel updateSQL(ISQLObject... objects) {
        Map<String, Object> objs = SqlObjectMap((Object[]) objects);
        Update update = null;
        String tableName = null;
        EntityService.DBTYPE dbtype = getDbType();
        if (objs.get("table") != null) {
            tableName = ((Table) objs.get("table")).getTableName();
        } else {
            throw new RuntimeException("没有提供更新用的表格");
        }
        if (objs.get("update") != null) {
            update = (Update) objs.get("update");
        } else {
            throw new RuntimeException("更新语句缺少更新字段");
        }
        Where where = null;
        if (objs.get("where") != null) {
            where = (Where) objs.get("where");
        } else {
            log.warn("[" + tableName + "]进行无条件更新:set = " + update.sql(dbtype) + " args = " + update.args().toArray());
        }
        StringBuffer sql = new StringBuffer();
        List<Object> args = new ArrayList<Object>();
        sql.append("update ").append(tableName).append(" set ");
        sql.append(update.sql(dbtype));
        args.addAll(update.args());
        sql.append(where.sql(dbtype));
        args.addAll(where.args(dbtype));
        log.debug(sql.toString() + " " + args);
        return SQLModel.create(sql.toString(), args);
    }

    /**
     * @param <T>
     * @param entity        实体类
     * @param updateColumns 更新列
     * @return
     * @category 生成根据ID更新语法
     */
    private <T> SQLModel updateSQL(T entity, String[] updateColumns) {
        List<String> idNames = EntityUtils.idColumnNames(entity.getClass());
        return updateSQL(entity, updateColumns, idNames.toArray(new String[0]));
    }

    /**
     * @param <T>
     * @param entity        实体类
     * @param updateColumns 更新列数组
     * @param whereColumns  更新参数数组
     * @return
     * @category 生成更新语法
     */
    private <T> SQLModel updateSQL(T entity, String[] updateColumns, String[] whereColumns) {
        SQLModel model = new SQLModel();
        @SuppressWarnings("unchecked") Class<T> clazz = (Class<T>) entity.getClass();
        Map<String, Field> fieldmap = EntityUtils.reflectFieldMix(clazz);
        List<Field> fields = new ArrayList<Field>();
        for (String column : updateColumns) {
            fields.add(fieldmap.get(column.toLowerCase()));
        }
        StringBuffer sql = new StringBuffer();
        List<Object> args = new ArrayList<Object>();
        List<Integer> typeList = new ArrayList<Integer>();
        try {
            sql.append("update ").append(EntityUtils.tableName(clazz)).append(" set ");
            String tmp = "";
            for (Field field : fields) {
                if (!EntityUtils.isIdField(field)) {
                    String col = EntityUtils.columnName(field);
                    Object value = EntityUtils.fieldValue(field, entity);
                    if (isUpdateTimeField(field) || isCreateTimeField(field) || isUpdateByField(field) || isCreateByField(field)) {
                    } else {
                        sql.append(tmp).append(getColumn(col)).append("=?");
                        args.add(value);
                        typeList.add(this.getArgType(field.getType().getName()));
                        tmp = ",";
                    }
                }
            }

            // 更新时间
            sql.append(tmp).append(UPDATE_TIME + "= ");
            if (isOracle()) {
                sql.append("SYSDATE");
                // args.add(new Timestamp(System.currentTimeMillis()));
                // typeList.add(Types.TIMESTAMP);
            } else {
                sql.append("'" + DateTimeUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS") + "'");
                // args.add(new Date());
                // typeList.add(Types.TIMESTAMP);
            }

            sql.append(tmp).append(UPDATE_BY + "='").append(getCurrentUserId()).append("'");
            // args.add(getCurrentUserId());// 更新人
            // typeList.add(Types.VARCHAR);

            sql.append(" where ");
            tmp = "";
            for (String column : whereColumns) {
                sql.append(tmp).append(getColumn(column)).append("=?");
                Field field = fieldmap.get(column.toLowerCase());
                args.add(EntityUtils.fieldValue(field, entity));
                typeList.add(this.getArgType(field.getType().getName()));
                fields.add(field);
                tmp = " and ";
            }

        } catch (IllegalArgumentException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        model.setSql(sql.toString());
        model.setArgs(args);
        model.setFields(fields);
        model.setArgTypeList(typeList);
        return model;
    }

    /**
     * @param <T>
     * @param entity   实体类
     * @param nullable true 空值字段给更新 false 空值字段不更新
     * @return
     * @category 根据实体生成更新语句和参数
     */
    private <T> SQLModel updateSQL(T entity, boolean nullable, Date updateTime) {
        @SuppressWarnings("unchecked") Class<T> clazz = (Class<T>) entity.getClass();
        List<Field> fields = EntityUtils.fields(clazz);
        List<Field> updateFields = new ArrayList<Field>();
        StringBuffer sql = new StringBuffer();
        List<Object> args = new ArrayList<Object>();
        List<Integer> typeList = new ArrayList<Integer>();
        String tableName = EntityUtils.tableName(clazz);
        try {
            sql.append("update ").append(tableName).append(" set ");
            String tmp = "";
            for (Field field : fields) {
                if (!EntityUtils.isIdField(field)) {
                    String col = EntityUtils.columnName(field);
                    Object value = EntityUtils.fieldValue(field, entity);
                    if (isUpdateTimeField(field) || isUpdateByField(field) || isCreateTimeField(field) || isCreateByField(field)) {
                    } else {
                        if (nullable || value != null) {
                            sql.append(tmp).append(this.getColumn(col)).append("=?");
                            args.add(value);
                            tmp = ",";
                            updateFields.add(field);
                            typeList.add(this.getArgType(field.getType().getName()));
                        }
                    }
                }
            }
            // 更新时间
            sql.append(tmp).append(UPDATE_TIME + "= ");
            if (updateTime == null) {
                updateTime = new Date();
            }
            if (isOracle()) {
                sql.append("SYSDATE");
                // Timestamp timestamp = new Timestamp(updateTime.getTime());
                // args.add(timestamp);
                // typeList.add(Types.TIMESTAMP);
            } else {
                sql.append("'" + DateTimeUtils.format(updateTime, "yyyy-MM-dd HH:mm:ss.SSS") + "'");
                // args.add(updateTime);
                // typeList.add(Types.TIMESTAMP);
            }
            // 更新人
            //sql.append(tmp).append(UPDATE_BY + "='" + getCurrentUserId() + "'");//这个写法有问题，当全部字段为空时，会出错（tmp为空，导致2个字段更新时中间没逗号）
            sql.append("," + UPDATE_BY + "='" + getCurrentUserId() + "'");//这里必然有前缀的逗号，因为前面增加了updatetime
            // args.add(getCurrentUserId());
            // typeList.add(Types.VARCHAR);

            List<Field> idFields = EntityUtils.idFields(clazz);
            sql.append(" where ");
            tmp = "";
            for (Field field : idFields) {
                String col = EntityUtils.columnName(field);
                Object value = EntityUtils.fieldValue(field, entity);
                if (value == null) {
                    throw new RuntimeException("更新实体主键不能为空值");
                } else {
                    sql.append(tmp).append(col).append("=?");
                    tmp = " and ";
                    args.add(value);
                    updateFields.add(field);
                    typeList.add(this.getArgType(field.getType().getName()));
                }
            }

        } catch (IllegalArgumentException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        SQLModel model = SQLModel.create(sql.toString(), args);
        model.setFields(updateFields);
        model.setArgTypeList(typeList);
        model.setTableName(tableName);
        return model;
    }

    /**
     * @param <T>
     * @param entity   实体类
     * @param nullable true 空值字段给更新 false 空值字段不更新
     * @return
     * @category 根据实体生成更新语句和参数
     */
//	private <T> SQLModel updateSQL(List<T> entitys, boolean nullable, String updateTime) {
//		T entity = entitys.get(0);
//		@SuppressWarnings("unchecked")
//		Class<T> clazz = (Class<T>) entity.getClass();
//		List<Field> fields = EntityUtils.fields(clazz);
//		List<Field> updateFields = new ArrayList<Field>();
//		StringBuffer sql = new StringBuffer();
//		List<Object> args = new ArrayList<Object>();
//		List<Integer> typeList = new ArrayList<Integer>();
//
//		try {
//			sql.append("update ").append(EntityUtils.tableName(clazz)).append(" set ");
//			String tmp = "";
//			for (Field field : fields) {
//				if (!EntityUtils.isIdField(field)) {
//					String col = EntityUtils.columnName(field);
//					Object value = EntityUtils.fieldValue(field, entity);
//					if (isUpdateTimeField(field) || isUpdateByField(field) || isCreateTimeField(field)
//							|| isCreateByField(field)) {
//					} else {
//						if (nullable || value != null) {
//							sql.append(tmp).append(col).append("=?");
//							args.add(value);
//							tmp = ",";
//							updateFields.add(field);
//							typeList.add(this.getArgType(field.getType().getName()));
//						}
//					}
//				}
//			}
//			if (StringUtils.isEmpty(updateTime)) {
//				updateTime = DateTimeUtils.formatLocalDateTime(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss");
//			}
//			sql.append(tmp).append(UPDATE_TIME + "='").append(updateTime).append("'");
//			sql.append(tmp).append(UPDATE_BY + "='").append(getCurrentUserId()).append("'");
//
//			List<Field> idFields = EntityUtils.idFields(clazz);
//			sql.append(" where ");
//			tmp = "";
//			for (Field field : idFields) {
//				String col = EntityUtils.columnName(field);
//				Object value = EntityUtils.fieldValue(field, entity);
//				if (value == null) {
//					throw new RuntimeException("更新实体主键不能为空值");
//				} else {
//					sql.append(tmp).append(col).append("=?");
//					tmp = " and ";
//					args.add(value);
//					updateFields.add(field);
//					typeList.add(this.getArgType(field.getType().getName()));
//				}
//			}
//		} catch (IllegalArgumentException | IllegalAccessException e) {
//			throw new RuntimeException(e);
//		}
//		SQLModel model = SQLModel.create(sql.toString(), args);
//		model.setFields(updateFields);
//		model.setArgTypeList(typeList);
//		return model;
//	}
    private int[] intList2ary(List<Integer> list) {
        if (StringUtils.isNotEmpty(list)) {
            return list.stream().mapToInt(Integer::intValue).toArray();
        }
        return null;
    }

    /**
     * @param tableName  表格名称
     * @param sqlObjects 删除对象(Where对象有效)
     * @return
     * @category 生成删除语句与参数
     */
    private SQLModel deleteSQL(String tableName, Object... sqlObjects) {
        Map<String, Object> objs = SqlObjectMap(sqlObjects);
        StringBuffer sql = new StringBuffer();
        sql.append("delete from ").append(tableName);
        SQLModel model = new SQLModel();
        if (objs.get("where") != null) {
            Where where = (Where) objs.get("where");
            sql.append(where.sql(getDbType()));
            model.setArgs(where.args(getDbType()));
            // model.setArgTypeList(where.g)
        }
        model.setSql(sql.toString());
        model.setTableName(tableName);
        return model;
    }

    /**
     * @param tableName    表格名称
     * @param deleteColumn 删除列
     * @param args         删除参数 如果是单个参数，则直接等于，如果是多个参数，则用in
     * @return
     * @category 生成删除语法
     */
    private SQLModel deleteSQL(String tableName, String deleteColumn, Object... args) {
        StringBuffer sql = new StringBuffer();
        sql.append("delete from ").append(tableName).append(" where ").append(getColumn(deleteColumn)).append("=?");
        SQLModel model = SQLModel.create(sql.toString(), args);
        model.setTableName(tableName);
        return model;
    }

    /**
     * @param tableName     表格名称
     * @param deleteColumns 删除列数组
     * @param args          参数数组
     * @return
     * @category 生成删除语法
     */
    private SQLModel deleteSQL(String tableName, String[] deleteColumns, Object... args) {
        StringBuffer sql = new StringBuffer();
        sql.append("delete from ").append(tableName).append(" where ");
        String tmp = "";
        for (String column : deleteColumns) {
            sql.append(tmp).append(getColumn(column)).append("=?");
            tmp = " and ";
        }
        SQLModel model = SQLModel.create(sql.toString(), args);
        model.setTableName(tableName);
        return model;
    }

    /**
     * @param <T>
     * @param entity
     * @return
     * @category 生成删除语法
     */
    private <T> SQLModel deleteSQL(T entity) {
        @SuppressWarnings("unchecked") Class<T> clazz = (Class<T>) entity.getClass();
        List<Field> fields = new ArrayList<Field>();
        StringBuffer sql = new StringBuffer();
        List<Object> args = new ArrayList<Object>();
        String tableName = EntityUtils.tableName(clazz);
        sql.append("delete from ").append(tableName).append(" where ");
        List<Field> fieldIds = EntityUtils.idFields(clazz);
        List<Integer> typeList = new ArrayList<Integer>();
        try {
            String tmp = "";
            for (Field field : fieldIds) {
                if (EntityUtils.isJavaClass(field.getClass())) {
                    String col = getColumn(EntityUtils.columnName(field));
                    sql.append(tmp).append(col).append("=?");
                    args.add(EntityUtils.fieldValue(field, entity));
                    fields.add(field);
                    typeList.add(this.getArgType(field.getType().getName()));
                    tmp = " and ";
                } else {
                    List<Field> fields1 = EntityUtils.fields(field.getClass());
                    Object subClass = EntityUtils.fieldValue(field, entity);
                    for (Field field1 : fields1) {
                        String col = getColumn(EntityUtils.columnName(field1));
                        sql.append(tmp).append(col).append("=?");
                        args.add(EntityUtils.fieldValue(field1, subClass));
                        fields.add(field1);
                        typeList.add(this.getArgType(field1.getType().getName()));
                        tmp = " and ";
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        SQLModel model = new SQLModel();
        model.setSql(sql.toString());
        model.setArgs(args);
        model.setFields(fields);
        model.setArgTypeList(typeList);
        model.setTableName(tableName);
        return model;
    }

    /**
     * @param sql
     * @param values
     * @throws SQLException
     * @category 执行无返回值的Sql文
     */
    @Override
    public boolean execute(String sql, Object... values) {
        Connection conn = getConnection();
        PreparedStatement st = null;
        try {
            st = conn.prepareStatement(sql);
            for (int i = 0; i < values.length; i++) {
                st.setObject(i + 1, values[i]);
            }
            return st.execute();
        } catch (SQLException e) {
            log.error("关闭数据库连接错误", e);
            return false;
        } finally {
            if (st != null) {
                try {
                    st.close();
                } catch (SQLException e) {
                }
            }
            closeConnection(conn);
        }
    }

    @Override
    public boolean executeDisableTenlant(String sql, Object... values) {
        sql = disableTenant(sql);
        return execute(sql, values);
    }

    @Override
    public boolean executeWithTenlant(String tenantId, String sql, Object... values) {
        sql = withTenant(sql, tenantId);
        return execute(sql, values);
    }

    /**
     * @param objects 查询对象数组
     * @return
     * @category 生成选择对象map
     */
    private Map<String, Object> SqlObjectMap(Object... objects) {
        Map<String, Object> objs = new HashMap<String, Object>();
        for (Object value : objects) {
            if (value instanceof Select) {
                objs.put("select", value);
            } else if (value instanceof Where) {
                objs.put("where", value);
            } else if (value instanceof Pagination) {
                objs.put("page", value);
            } else if (value instanceof Group) {
                objs.put("group", value);
            } else if (value instanceof Order) {
                objs.put("order", value);
            } else if (value instanceof Update) {
                objs.put("update", value);
            } else if (value instanceof Table) {
                if (objs.get("table") == null) {
                    // 最后一个table可能是实体类传进来的
                    // 如果在之前已经通过ISQLObjects传进一个table，则以之前的为准
                    objs.put("table", value);
                }
            }
        }
        return objs;
    }

    /**
     * @param tableName
     * @param sqlObjects
     * @return
     * @category 查询语句生成器
     */
    private SQLModel selectSQL(Class<?> entityClass, ISQLObject... sqlObjects) {
        return selectSQL(push(sqlObjects, Table.create(entityClass)));
    }

    /**
     * @param sqlObjects 查询对象数组
     * @return
     * @category 根据查询对象生成查询语句
     */
    private SQLModel selectSQL(ISQLObject... sqlObjects) {
        Map<String, Object> objs = SqlObjectMap((Object[]) sqlObjects);
        StringBuilder sql = new StringBuilder();
        List<Object> args = new ArrayList<Object>();
        EntityService.DBTYPE dbtype = this.getDbType();
        sql.append("select ");
        if (objs.get("select") != null) {
            Select select = (Select) objs.get("select");
            sql.append(select.sql(dbtype));
            args.addAll(select.args());
        } else {
            sql.append(" * ");
        }
        if (objs.get("table") == null) {
            throw new RuntimeException("查询语句必须提供表格名称");
        } else {
            sql.append(" from ").append(((Table) objs.get("table")).sql());
        }
        if (objs.get("where") != null) {
            Where where = (Where) objs.get("where");
            sql.append(" ").append(where.sql(dbtype));
            args.addAll(where.args(getDbType()));
        }
        if (objs.get("order") != null) {
            Order order = (Order) objs.get("order");
            sql.append(" ").append(order.sql(dbtype));
        }
        if (objs.get("group") != null) {
            Group group = (Group) objs.get("group");
            sql.append(" ").append(group.sql(dbtype));
        }
        if (objs.get("page") != null) {
            Pagination<?> page = (Pagination<?>) objs.get("page");
            sql.append(" ").append(page.sql(dbtype));
        }
        log.debug("selectSQL:" + sql.toString() + "  args:" + args);
        // log.info("selectSQL:" + sql.toString() + " args:" + args);
        return SQLModel.create(refactorySQLSelect(sql.toString()), args);
    }

    /**
     * @param src
     * @param objs
     * @return
     * @category 合并查询对象
     */
    private ISQLObject[] push(ISQLObject[] src, ISQLObject... objs) {
        int len1 = src.length;
        int len2 = objs.length;
        ISQLObject[] res = new ISQLObject[len1 + len2];
        int i = 0;
        for (; i < src.length; i++) {
            res[i] = src[i];
        }
        for (; i < res.length; i++) {
            res[i] = objs[i - len1];
        }
        return res;
    }

    /**
     * @return
     * @category 当前数据库表格列表
     */
    @Override
    @Transactional
    public List<TableInfo> getTableList(String tableName) {
        String database = getDatabaseName();
        String sql = "";
        String dbtype = DBTypeUtils.getVirtualDBType(this.getDbType());// getDatabaseType();
        if (isOracle() || isKingBase()) {
            database = database.toUpperCase();
// if ("oracle".equalsIgnoreCase(dbtype) || "dm".equalsIgnoreCase(dbtype) ||"kingbase".equalsIgnoreCase(dbtype)) {
            sql = "SELECT OWNER as TABLE_SCHEMA,TABLE_NAME,null as  TABLE_COMMENT FROM all_tables WHERE upper(OWNER) = '" + database + "'";
            if (!ObjUtils.isEmpty(tableName)) {
                sql += " and TABLE_NAME='" + tableName + "'";
            }
            sql += " order  by TABLE_NAME ";
        } else if ("mysql".equalsIgnoreCase(dbtype)) {
            sql = "SELECT TABLE_SCHEMA,TABLE_NAME,TABLE_COMMENT FROM `information_schema`.tables WHERE table_schema='" + database + "'";
            if (StringUtils.isNotEmpty(tableName)) {
                sql += " and TABLE_NAME='" + tableName + "'";
            }
            sql += " order  by TABLE_NAME ";
// } else if ("pg".equalsIgnoreCase(dbtype) || "gauss".equalsIgnoreCase(dbtype)) {
        } else if (isPG()) {
            sql = "select tableowner as TABLE_SCHEMA,tablename as TABLE_NAME ,null as TABLE_COMMENT from pg_tables where Schemaname= 'public' ";
            if (StringUtils.isNotEmpty(tableName)) {
                sql += " and tablename='" + tableName + "'";
            }
            sql += " order  by tablename ";
        } else if ("sqlserver".equalsIgnoreCase(dbtype)) {
            sql += " 	SELECT DISTINCT";
            sql += "		'" + database + "' AS TABLE_SCHEMA,";
            sql += " 		d.name AS TABLE_NAME,";
            sql += " 		cast(f.value as varchar) AS TABLE_COMMENT ";
            sql += " 	FROM";
            sql += "		syscolumns a";
            sql += "		LEFT JOIN systypes b ON a.xusertype= b.xusertype";
            sql += " 		INNER JOIN sysobjects d ON a.id= d.id AND d.xtype= 'U'AND d.name<> 'dtproperties'";
            sql += " 		LEFT JOIN syscomments e ON a.cdefault= e.id";
            sql += " 		LEFT JOIN sys.extended_properties g ON a.id= G.major_id AND a.colid= g.minor_id";
            sql += " 		LEFT JOIN sys.extended_properties f ON d.id= f.major_id AND f.minor_id= 0 ";
            if (!ObjUtils.isEmpty(tableName)) {
                sql += " where d.name='" + tableName + "'";
            }
            sql += " 	ORDER BY d.name";
        } else {
            throw new RuntimeException("[" + database + "]类型数据库暂不支持");
        }

        SqlRowSet rs = jdbc.queryForRowSet(disableTenant(sql));
        List<TableInfo> list = EntityUtils.convertTo(rs, TableInfo.class);
        return list;
    }

    /**
     * 获取无租户id过滤的语句
     */
    private String disableTenant(String sql) {
        if (StringUtils.isNotEmpty(sql) && !sql.startsWith(MultiTenantUtils.NoTenantTag)) {
            sql = MultiTenantUtils.NoTenantTag + sql;// "/**NoTenantId*/" + sql;
        }
        return sql;
    }

    private StringBuilder disableTenant(StringBuilder sql) {
        if (StringUtils.isNotEmpty(sql) && !sql.toString().startsWith(MultiTenantUtils.NoTenantTag)) {
            sql.insert(0, MultiTenantUtils.NoTenantTag);
        }
        return sql;
    }

    private String withTenant(String sql, String tenantId) {
        return MultiTenantUtils.addTenantId(tenantId) + sql;
    }

    private StringBuilder withTenant(StringBuilder sql, String tenantId) {
        return sql.insert(0, MultiTenantUtils.addTenantId(tenantId));
    }

    /**
     * @return
     * @category 当前数据库表格列表
     */
    @Override
    public TableInfo getTable(String tableName) {
        List<TableInfo> list = this.tableList(tableName);
        if (StringUtils.isNotEmpty(list)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    /**
     * @return
     * @category 获得数据库连接
     * <p>
     * 强烈要求，用后必须关闭
     */
    @Override
    public Connection getConnection() {
        Connection conn = null;
        try {
            conn = jdbc.getDataSource().getConnection();
        } catch (Exception e) {
            closeConnection(conn);
            log.error("", e);

        }
        return conn;
    }

    /**
     * @category 关闭数据库连接
     */
    @Override
    public void closeConnection(Connection conn) {
        try {
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            log.error("", e);
        }
    }

    /**
     * @param tableName 表格名称
     * @return
     * @category 表格列列表
     *
     * <pre>
     *   注:目前支持mysql
     * </pre>
     */

    @Override
    public List<TableColumnInfo> getTableColumnList(String tableName) {
        String dbtype = getDatabaseType();
        String sql = "";
        if (isOracle()||isKingBase() || dbtype.equalsIgnoreCase("dm")) {// if (dbtype.equalsIgnoreCase("oracle") || dbtype.equalsIgnoreCase("dm")) {
//            sql += " select col.table_name,col.column_name,col.data_type,col.data_type as column_type,(case col.nullable when 'Y' then 1 else 0 end) as is_nullable,";
//            sql += " (case con.constraint_type when 'P' then 1 else 0 end) as primary_key,col.data_length,col.data_length as CHAR_MAXLENGTH,com.comments as column_comment ";
//            sql += " from user_tab_columns col ";
//            sql += " join user_col_comments com  on com.table_name=col.table_name and com.column_name=col.column_name ";
//            sql += " left join  user_cons_columns cols on col.table_name=cols.table_name and col.column_name=cols.column_name ";
//            sql += " left join user_constraints   con  on con.table_name=col.table_name and con.constraint_name = cols.constraint_name ";
//            sql += " where col.table_name='" + tableName.toUpperCase() + "' ";
//            sql += " order by col.COLUMN_ID ";
            sql += "select col.table_name,col.column_name,col.data_type,col.data_type as column_type,(case col.nullable when 'Y' then 1 else 0 end) as is_nullable,";
            sql +="(case col.nullable when 'Y' then 0 else 1 end) as primary_key,";
            sql += "col.data_length,col.data_length as CHAR_MAXLENGTH,null as column_comment ";
            sql += " from user_tab_columns col";
            sql += " where col.table_name='" + tableName.toUpperCase() + "' ";
            sql += " order by col.COLUMN_ID ";
        } else if (isMySql()) {
            sql += " SELECT COLUMN_NAME,DATA_TYPE,CHARACTER_MAXIMUM_LENGTH, CHARACTER_MAXIMUM_LENGTH as CHAR_MAXLENGTH,NUMERIC_PRECISION,NUMERIC_SCALE,COLUMN_COMMENT,IS_NULLABLE,COLUMN_KEY,COLUMN_TYPE,ORDINAL_POSITION ";
            sql += " FROM Information_schema.COLUMNS ";
            sql += " WHERE table_Name ='" + tableName + "'AND table_schema = DATABASE ( ) ";
            sql += " ORDER BY ordinal_position";
        } else if (isSqlServer()) {
            sql += " SELECT obj.NAME as table_name,";
            sql += " col.NAME AS column_name,";
            sql += " col.max_length AS DataLength,";
            sql += " isnull(syscol.prec,col.max_length) AS CHAR_MAXLENGTH,";
            sql += " col.is_nullable AS IsNullable,";
            sql += " t.NAME AS column_type,";
            sql += " t.NAME AS data_type,";
            sql += " cast (ep.VALUE as varchar) AS column_comment,";
            sql += " (SELECT TOP 1 case ind.is_primary_key when 1 then 1 else 0 end FROM sys.index_columns ic	LEFT JOIN sys.indexes ind ON ic.object_id = ind.object_id AND ic.index_id = ind.index_id AND ind.NAME LIKE 'PK_%' WHERE ic.object_id = obj.object_id AND ic.column_id = col.column_id ) AS primary_key ";
            sql += " FROM sys.objects obj";
            sql += " INNER JOIN sys.COLUMNS col ON obj.object_id = col.object_id";
            sql += " LEFT JOIN sys.syscolumns syscol ON col.object_id = syscol.id AND col.NAME=syscol.NAME ";
            sql += " LEFT JOIN sys.types t ON t.user_type_id = col.user_type_id";
            sql += " LEFT JOIN sys.extended_properties ep ON ep.major_id = obj.object_id ";
            sql += " AND ep.minor_id = col.column_id ";
            sql += " AND ep.NAME = 'MS_Description' ";
            sql += " WHERE obj.NAME = '" + tableName + "'";
            sql += " ORDER BY col.column_id";
        } else {
            String database = getDatabaseName();
            throw new RuntimeException("[" + database + "]类型数据库暂不支持");
        }
        SqlRowSet rs = jdbc.queryForRowSet(disableTenant(sql));
        List<TableColumnInfo> list = EntityUtils.convertTo(rs, TableColumnInfo.class);
        return list;
    }

    /**
     * @param entityClass 实体类类型
     * @param arg         参数
     * @category 根据ID获取实体对象
     */
    @Override
    public <T> T queryObjectById(Class<T> entityClass, Object arg) {
        return rawQueryObjectByColumn(entityClass, "ID", arg);
    }

    @Override
    public <T> T queryObjectByIdDisableTenant(Class<T> entityClass, Object arg) {
        return rawQueryObjectByColumnDisableTenant(entityClass, "ID", arg);
    }

    /**
     * @param func lamda字段
     * @param arg  参数
     * @category 根据lamda表达式进行删除
     */
    @Override
    public <T> int deleteByColumn(Class<?> entityClass, Func<T, ?> func, Object arg) {
        return rawDeleteByColumn(entityClass, resolveColumnName(func), arg);
    }

    /**
     * @param <T>
     * @param func
     * @return
     * @category 根据lamda表达式获取字段名称
     */
    private <T> String resolveColumnName(Func<T, ?> func) {
        Where where = Where.create();
        return where.getClassColumnName(func);
    }

    /**
     * @param sql    查询语句
     * @param values 参数值
     * @return 数据list
     * @category 原生sql语句查询
     */
    @Override
    public List<LinkedHashMap<String, Object>> query(String sql, List<Object> values) {
        List<LinkedHashMap<String, Object>> list = null;
        SqlRowSet rs = null;
        if (StringUtils.isEmpty(values)) {
            rs = rawQuery(sql);
        } else {
            rs = rawQuery(sql, values.toArray());
        }
        if (rs != null) {
            list = new ArrayList<LinkedHashMap<String, Object>>();
            while (rs.next()) {
                EntityMap<Object> map = new EntityMap<Object>();
                int fieldCount = rs.getMetaData().getColumnCount();
                for (int i = 1; i <= fieldCount; i++) {
                    String fieldName = rs.getMetaData().getColumnLabel(i);
                    if (StringUtils.isEmpty(fieldName)) {
                        fieldName = rs.getMetaData().getColumnName(i);
                    }
                    map.put(fieldName, rs.getObject(i));
                }
                list.add(map);
            }
        }
        return list;

    }

    /**
     * @param tableName 表格名称
     * @category 列举数据库表格信息
     */
    @Override
    public List<TableInfo> tableList(String tableName) {
        return getTableList(tableName);
    }

    /**
     * @param tableName 表格名称
     * @category 表格的字段详细信息
     */
    @Override
    public List<TableColumnInfo> tableColumnList(String tableName) {
        return getTableColumnList(tableName);
    }

    /**
     * @param entityClass 实体类型
     * @param func        字段表达式
     * @param args        in的参数数组
     * @category 根据某个字段使用in方式删除
     */
    @Override
    public <T> int deleteIn(Class<?> entityClass, Func<T, ?> func, Object... args) {
        return isBatchSuccess(this.rawDeleteIn(entityClass, resolveColumnName(func), args));
    }

    @Override
    public <T> int deleteInDisableTenant(Class<?> entityClass, Func<T, ?> func, Object... args) {
        return isBatchSuccess(this.rawDeleteInDisableTenant(entityClass, resolveColumnName(func), args));
    }

    /**
     * 获得数据库类型
     */
    @Override
    public EntityService.DBTYPE getDbType() {
        if (DBTYPE == null) {
            DruidDataSource ds = ((DruidDataSource) jdbc.getDataSource());
            DBTYPE = DBTypeUtils.getDbType(ds);
        }
        return DBTYPE;
    }

    /**
     * 获得数据库类型
     */
    @Override
    public String getDatabaseType() {
        return this.getDbType().name().toLowerCase();// ((DruidDataSource) jdbc.getDataSource()).getDbType();
    }

    /**
     * 获得数据库版本号，目前只支持Oracle,sqlserver
     *
     * @return
     */
    @Override
    public double getDatabaseVersion() {
        if (DB_VERSION != null) {
            return DB_VERSION;
        } else {
            try {
                String sql = "";
                if (this.getDbType().equals(EntityService.DBTYPE.ORACLE)) {
                    sql = "SELECT VALUE FROM v$system_parameter WHERE name = 'compatible'";
                } else if (this.getDbType().equals(EntityService.DBTYPE.SQLSERVER)) {
                    //11.0.2100.60 SQL Server 2012
                    sql = "SELECT CAST(SERVERPROPERTY('productversion') AS VARCHAR(255)) ";
                }
                if (sql.length() > 0) {
                    SqlRowSet rs = jdbc.queryForRowSet(disableTenant(sql));
                    if (rs.next()) {
                        String v = rs.getString(1);
                        if (v != null) {
                            String[] vs = v.split("\\.");
                            String ns = "";
                            for (int n = 0; n < vs.length; n++) {
                                if (n == 2) {
                                    break;
                                }
                                ns += vs[n] + ".";
                            }
                            DB_VERSION = Double.parseDouble(ns.substring(0, ns.length() - 1));
                            //DB_VERSION = 10.0;//test
                        }
                    }
                } else {
                    DB_VERSION = 0.0;
                }
            } catch (Exception e) {
                DB_VERSION = 0.0;
                log.error(e);
            }
            return DB_VERSION;
        }
    }

    /**
     * 获得数据库名
     */
    @Override
    public String getDatabaseName() {
        if (StringUtils.isNotEmpty(DBNAME)) {
            return DBNAME;
        } else {
            // String dbtype = this.getDatabaseType();
            String database = null;
            Connection conn = null;
            try {
                conn = this.getConnection();
                if (this.isOracle()) {
//				if ("oracle".equalsIgnoreCase(dbtype) || "dm".equalsIgnoreCase(dbtype)|| "kingbase".equalsIgnoreCase(dbtype)) {
                    database = conn.getSchema();
                } else {
                    database = conn.getCatalog();
                }
                DBNAME = database;
            } catch (SQLException e) {
                log.error(e);
            } finally {
                closeConnection(conn);
            }
            return database;
        }
    }

    /**
     * @param sql sql文
     * @return
     * @category 重构sql以适应当前连接的数据库类型
     */
    private String refactorySQLSelect(String sql) {
        try {
            StringBuilder sb = new StringBuilder();
            if (this.isPG()) {// pg数据库
                sb.append(sql);
            } else {
                // 查询语句转换为分页查询语句
                String dbtype = "";// getDatabaseType()
                if (DBTypeUtils.isKingBase(this.getDbType()) || DBTypeUtils.isPg(this.getDbType())) {
                    dbtype = "postgresql";
                } else {
                    dbtype = DBTypeUtils.getVirtualDBType(this.getDbType());// getDatabaseType()
                }
                List<SQLStatement> statementList = SQLUtils.parseStatements(sql, dbtype);
                for (SQLStatement statement : statementList) {
                    SQLSelectStatement sqlSelectStatement = (SQLSelectStatement) statement;
                    SQLSelectQueryBlock selectQuery = (SQLSelectQueryBlock) sqlSelectStatement.getSelect().getQuery();
                    // 重构sql的语句分页信息
                    refactorySelectPage(dbtype, selectQuery, null);
                    // 生成sql文件
                    sb.append(statement.toString());
                    if (this.isOracle()) {

                    } else {
                        if (!statement.toString().endsWith(";")) {
                            sb.append(";");
                        }
                    }
                }
            }
            if (MultiTenantUtils.hasDisabledTenantId(sql)) {
                disableTenant(sb);
            } else if (MultiTenantUtils.hasWithTenantTag(sql) && !MultiTenantUtils.hasWithTenantTag(sb.toString())) {
                withTenant(sb, MultiTenantUtils.parseWithTenantId(sql));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * @param dbtype      数据库类型
     * @param selectQuery 查询对象(durid对象)
     * @param page        分页信息(优先)
     * @category 重构语句的limit，top或分页信息
     */
    private void refactorySelectPage(String dbtype, SQLSelectQueryBlock selectQuery, Pagination<?> page) {
        if (page == null) {
            // mysql 先判断是否语句中包含limit
            SQLLimit limit = selectQuery.getLimit();
            if (limit != null) {
                SQLExpr rowcountExpr = limit.getRowCount();
                if (rowcountExpr != null) {
                    if (page == null) {
                        page = new Pagination<>();
                    }
                    page.setSize((int) ((SQLIntegerExpr) rowcountExpr).getNumber());
                }
                SQLExpr offsetExpr = limit.getOffset();
                if (offsetExpr != null) {
                    if (page == null) {
                        page = new Pagination<>();
                    }
                    if (page.getSize() == 0) {
                        page.setSize(1);
                    }
                    page.setPage(((Integer) ((SQLIntegerExpr) offsetExpr).getNumber()) / page.getSize());
                }
                selectQuery.setLimit(null);
            }
            // 再判断是否sqlserver语句包含top
            if (selectQuery instanceof SQLServerSelectQueryBlock) {
                SQLServerSelectQueryBlock sqlserverSelectQuery = (SQLServerSelectQueryBlock) selectQuery;
                SQLServerTop top = sqlserverSelectQuery.getTop();
                if (top != null) {
                    if (page == null) {
                        page = new Pagination<>();
                    }
                    SQLExpr topExpr = top.getExpr();
                    page.setSize((int) ((SQLIntegerExpr) topExpr).getNumber());
                }
                sqlserverSelectQuery.setTop(null);
            }
        }
        // 如果分页信息不为空，则根据数据库类型重新处理分页语句
        if (page != null) {
            SQLOrderBy order = selectQuery.getOrderBy();
            if (order == null) {
                throw new RuntimeException("分页查询语句必须包含排序字段");
            }
            if (dbtype.equalsIgnoreCase("sqlserver")) {
                SQLServerSelectQueryBlock sqlserverSelectQuery = (SQLServerSelectQueryBlock) selectQuery;
                sqlserverSelectQuery.setTop(page.getSize());
                if (page.getPage() != null) {
                    StringBuffer sb = new StringBuffer();
                    sb.append("(");
                    sb.append("SELECT ROW_NUMBER () ");
                    sb.append("OVER(ORDER BY ");
                    String flag = "";
                    for (SQLSelectOrderByItem item : order.getItems()) {
                        sb.append(flag).append(item.getExpr().toString());
                        if (item.getType() != null) {
                            sb.append(" ").append(item.getType().toString());
                        }
                        flag = ",";
                    }
                    sb.append(")");
                    sb.append(" AS __RowNumber,* FROM ").append(selectQuery.getFrom());
                    // where 条件
                    if (selectQuery.getWhere() != null) {
                        sb.append(" where ").append(selectQuery.getWhere().toString());
                    }
                    sb.append(")");
                    SQLExprTableSource from = (SQLExprTableSource) selectQuery.getFrom();
                    from.setExpr(sb.toString());
                    from.setAlias("__TN");
                    // 查询分页的条数条件
                    SQLBinaryOpExpr where = new SQLBinaryOpExpr(new SQLIdentifierExpr("__TN.__RowNumber"), SQLBinaryOperator.GreaterThan, new SQLIntegerExpr(page.getPage() * page.getSize()));
                    sqlserverSelectQuery.setWhere(where);
                }
            } else if (dbtype.equalsIgnoreCase("dm") || (dbtype.equalsIgnoreCase("oracle") && getDatabaseVersion() < 12.0)) { // Oracle(11g版本) or 达梦数据库
                StringBuffer sb = new StringBuffer();
                sb.append("(");
                sb.append("SELECT ROWNUM AS TM_ROWNUM, t.* FROM ");
//(SELECT * FROM MTM_INDEXINFO WHERE  TMUSED = 1 and   versionnumber='2024-07' and pmatrixid='ZQRTXD7LW05CVZKK5Q0130ZQRTXQD2005CVZKB2P0179' and matrixid='ZQRTXD7LW05CVZKK5Q0130ZQRTXRC1R05CVZK86F0167'ORDER BY CLASSSORT ASC,TMSORT ASC)  t
                sb.append("(");
                sb.append(selectQuery.toString());
                sb.append(") t ");
                sb.append("WHERE ROWNUM <= ");
                sb.append((page.getPage() + 1) * page.getSize());
                sb.append(")  ");

                SQLExprTableSource from = (SQLExprTableSource) selectQuery.getFrom();
                from.setExpr(sb.toString());
                from.setAlias("TABLE_ALIAS");
                // 查询分页的条数条件
                SQLBinaryOpExpr where = new SQLBinaryOpExpr(new SQLIdentifierExpr("TABLE_ALIAS.TM_ROWNUM"), SQLBinaryOperator.GreaterThan, new SQLIntegerExpr(page.getPage() * page.getSize()));
                selectQuery.setWhere(where);
            } else {
                SQLLimit limit1 = new SQLLimit();
                if (page.getPage() != null) {
                    limit1.setOffset(page.getPage() * page.getSize());
                }
                limit1.setRowCount(page.getSize());
                selectQuery.setLimit(limit1);
            }
        }
    }

    /**
     * @param <T>
     * @param clazz 实体类对象
     * @param where where对象
     * @param order order对象
     * @param page  分页对象
     * @return
     * @category 查询
     */
    @Override
    public <T> List<T> queryData(Class<T> clazz, Where where, Order order, Pagination<?> page) {
//        List<T> list = null;
//        if (page != null && page.getSize() > 0) { // 分页查询
//            list = this.queryList(clazz, where, order, page);
//            if (StringUtils.isNotEmpty(list)) {
//                Long total = this.queryCount(clazz, where); // 查询总数
//                page.setTotal(total.intValue());
//            }
//        } else {
//            list = this.queryList(clazz, where, order);
//        }
        return this.queryData(clazz, null, where, order, page);
    }
    /**
     * @param <T>
     * @param clazz 实体类对象
     * @param select select对象
     * @param where where对象
     * @param order order对象
     * @param page  分页对象
     * @return
     * @category 查询
     */
	@Override
	public <T> List<T> queryData(Class<T> clazz, Select select, Where where, Order order, Pagination<?> page) {
		// TODO Auto-generated method stub
		List<T> list = null;
        if (page != null && page.getSize() > 0) { // 分页查询
        	if(select!=null) {
        		list = this.queryList(clazz ,select ,where, order, page);
        	}else {
        		list = this.queryList(clazz ,where, order, page);
        	}
            if (StringUtils.isNotEmpty(list)) {
                Long total = this.queryCount(clazz, where); // 查询总数
                page.setTotal(total.intValue());
            }
        } else {
        	if(select!=null) {
        		list = this.queryList(clazz ,select, where, order);
        	}else {
        		list = this.queryList(clazz, where, order);
        	}
        }
        return list;
	}
    @Override
    public <T> List<T> queryDataDisableTenant(Class<T> clazz, Where where, Order order, Pagination<?> page) {
        List<T> list = null;
        if (page != null && page.getSize() > 0) { // 分页查询
            list = this.queryListDisableTenant(clazz, where, order, page);
            if (StringUtils.isNotEmpty(list)) {
                Long total = this.queryCount(clazz, where); // 查询总数
                page.setTotal(total.intValue());
            }
        } else {
            list = this.queryListDisableTenant(clazz, where, order);
        }
        return list;
    }

    /**
     * @param field
     * @return
     * @category 判断是否是createTime字段
     */
    private boolean isCreateTimeField(Field field) {
        return "createTime".equalsIgnoreCase(field.getName());
    }

    /**
     * @param field
     * @return
     * @category 判断是否是updateTime字段
     */
    private boolean isUpdateTimeField(Field field) {
        return "updateTime".equalsIgnoreCase(field.getName());
    }

    /**
     * @param field
     * @return
     * @category 判断是否是crateBy字段
     */
    private boolean isCreateByField(Field field) {
        return "createBy".equalsIgnoreCase(field.getName()) || "CREATEBYORG".equalsIgnoreCase(field.getName()) || "CREATEBYPOST".equalsIgnoreCase(field.getName());
//			|| "CREATEBYORGNAME".equalsIgnoreCase(field.getName())|| "CREATEBYPOSTNAME".equalsIgnoreCase(field.getName());
    }

    /**
     * @param field
     * @return
     * @category 判断是否是updateBy字段
     */
    private boolean isUpdateByField(Field field) {
        return "updateBy".equalsIgnoreCase(field.getName());
    }

    /**
     * 获得当前登录人id
     *
     * @return
     */
    private String getCurrentUserId() {
        SysUser user = getUser();
        if (user == null) {
            return "system";
        } else {
            return user.getId();
        }
    }

    /**
     * 获得当前登录人相关属性
     *
     * @param user
     * @param code
     * @return
     */
    private String getUserProp(SysUser user, String code) {
        String value = "";
        if (user == null) {
            if (CREATE_BY.equals(code)) {
                value = "system";
            } else {
                value = null;
            }
        } else {
            if (CREATE_BY.equals(code)) {
                value = user.getId();
            } else if (CREATE_BY_ORG.equals(code)) {
                value = user.getOrgId();
                // } else if (CREATE_BY_ORGNAME.equals(code)) {
                // value = user.getOrgName();
            } else if (CREATE_BY_POST.equals(code)) {
                value = user.getPostId();
                // } else if (CREATE_BY_POSTNAME.equals(code)) {
                // value = user.getPostName();
            }
        }
        if (value == null) {
            return "NULL";
        } else {
            return "'" + value + "'";
        }
    }

    /**
     * 获得当前用户
     *
     * @return
     */
    private SysUser getUser() {
        return SysUserHolder.getCurrentUser();
    }

    /**
     * 批量更新（空值更新）(支持事务)
     */
    @Override
    public <T> int updateBatch(List<T> entities) {
        return isBatchSuccess(this.rawUpdateByIdBatchIncludeNull(entities));
    }

    /**
     * 批量更新（空值更新）(支持事务)
     */
    @Override
    public <T> int update(T entity) {
        return this.rawUpdateById(entity, true);
    }

    /**
     * 批量更新，空值也更新(支持事务)
     *
     * @param <T>
     * @param entities
     * @param batchSize 每次提交数量
     * @return
     */
    @Override
    public <T> int updateBatch(List<T> entities, int batchSize) {
        return this.updateByIdBatchIncludeNull(entities, batchSize);
    }

    /**
     * @param <T>
     * @param entity 实体类
     * @return
     * @category 根据主键删除
     */
    @Override
    public <T> int deleteById(T entity) {
        return this.rawDeleteById(entity);
    }

    /**
     * 使用jdbc批量执行语句
     *
     * @param conn
     * @param ps
     * @param sql
     * @param argsList
     * @throws SQLException
     */
    private void executeBatch(Connection conn, PreparedStatement ps, String sql, List<Object[]> argsList) throws SQLException {
        ps = conn.prepareStatement(sql);
        for (Object[] arg : argsList) {
            for (int n = 0; n < arg.length; n++) {
                ps.setObject(n + 1, arg[n]);// 参数赋值
            }
            ps.addBatch();
        }
        ps.executeBatch();// 执行语句
        try {
            ps.close();
        } catch (SQLException e1) {
            log.error(e1);
        }
    }

    /**
     * 删除并添加数据(支持事务)
     *
     * @param <T>
     * @param deleteEntityClass 删除类
     * @param deleteWhere       删除where条件
     * @param insertList        插入数据
     * @return
     */
    @Override
    public <T> int deleteAndInsert(Class<?> deleteEntityClass, ISQLObject deleteWhere, List<T> insertList) {
        int batchSize = 500;
        // 添加语句
        ExecParam insertParam = this.getInsertExecParam(insertList, batchSize);
        if (insertParam != null) {
            // 删除语句
            SQLModel deleteModel = deleteSQL(EntityUtils.tableName(deleteEntityClass), deleteWhere);
            DataSource dataSource = jdbc.getDataSource();
            BatchSqlUpdate bsu = new BatchSqlUpdate(dataSource, insertParam.getSql());
            bsu.setBatchSize(insertParam.getBatchSize());
            bsu.setTypes(insertParam.getArgType());
            // 支持事务
            TransactionTemplate transactionTemplate = getTransactionTemplate(dataSource);
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus transactionStatus) {
                    try {
                        // 执行删除语句
                        if (deleteModel.args() != null && deleteModel.args().length > 0) {
                            jdbc.update(deleteModel.sql(), deleteModel.args());
                        } else {
                            jdbc.update(deleteModel.sql());
                        }
                        // 执行添加语句
                        for (Object[] objs : insertParam.getBatchArgs()) {
                            bsu.update(objs);
                        }
                        bsu.flush();
                    } catch (Exception e) {
                        transactionStatus.setRollbackOnly();// 回滚
                        throw e;
                    }
                }
            });
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 删除并添加数据(支持事务)
     *
     * @param <K>
     * @param <V>
     * @param deleteList 删除数据
     * @param insertList 添加数据
     * @return
     */
    @Override
    public <K, V> int deleteAndInsert(List<K> deleteList, List<V> insertList) {
        int batchSize = 500;
        // 删除语句
        ExecParam deleteParam = this.getDeleteExecParam(deleteList, batchSize);
        // 添加语句
        ExecParam insertParam = this.getInsertExecParam(insertList, batchSize);
        if (deleteParam != null && insertParam != null) {
            DataSource dataSource = jdbc.getDataSource();
            // 添加
            BatchSqlUpdate bsu = new BatchSqlUpdate(dataSource, insertParam.getSql());
            bsu.setBatchSize(insertParam.getBatchSize());
            bsu.setTypes(insertParam.getArgType());
            // 删除
            BatchSqlUpdate bsuDel = new BatchSqlUpdate(dataSource, deleteParam.getSql());
            bsuDel.setBatchSize(deleteParam.getBatchSize());
            bsuDel.setTypes(deleteParam.getArgType());
            // 支持事务
            TransactionTemplate transactionTemplate = getTransactionTemplate(dataSource);
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus transactionStatus) {
                    try {
                        // 执行删除语句
                        for (Object[] objs : deleteParam.getBatchArgs()) {
                            bsuDel.update(objs);
                        }
                        bsuDel.flush();
                        // 执行添加语句
                        for (Object[] objs : insertParam.getBatchArgs()) {
                            bsu.update(objs);
                        }
                        bsu.flush();
                    } catch (Exception e) {
                        transactionStatus.setRollbackOnly();// 回滚
                        throw e;
                    }
                }
            });
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 指定租户批量更新
     *
     * @param tenantId
     * @param entities
     * @param batchSize
     * @param <T>
     * @return
     */
    public <T> int updateBatchIncludeNullWithTenant(String tenantId, List<T> entities, int batchSize) {
        ExecParam param = getUpdateExecParam(entities, batchSize);
        if (param != null) {
            param.setSql(withTenant(param.getSql(), tenantId));
            BlobColumnUtils.saveBlob("update", param);
            if (batchSize > 0) {
                return this.batchSqlUpdate(param.getSql(), param.getBatchArgs(), param.getArgType(), param.getBatchSize());
            } else {
                return isBatchSuccess(rawUpdateBatch(param.getSql(), param.getBatchArgs()));
            }
        } else {
            return 0;
        }
    }

    /**
     * 忽略租户批量更新
     *
     * @param entities
     * @param batchSize
     * @param <T>
     * @return
     */
    public <T> int updateBatchIncludeNullDisableTenant(List<T> entities, int batchSize) {
        ExecParam param = getUpdateExecParam(entities, batchSize);
        if (param != null) {
            param.setSql(disableTenant(param.getSql()));
            BlobColumnUtils.saveBlob("update", param);
            if (batchSize > 0) {
                return this.batchSqlUpdate(param.getSql(), param.getBatchArgs(), param.getArgType(), param.getBatchSize());
            } else {
                return isBatchSuccess(rawUpdateBatch(param.getSql(), param.getBatchArgs()));
            }
        } else {
            return 0;
        }
    }


    @Override
    public void batchExecuteSqls(List<String> sqls, Integer size) {
        if (ObjUtils.isEmpty(sqls)) {
            return;
        }

        if (size == null) {
            size = BATCH_EXECUTE_SQL_SIZE;
        }

        List<String> batchSqls = new ArrayList<>();
        int i = 1;
        boolean isExecute;
        for (String sql : sqls) {
            isExecute = false;
            batchSqls.add(sql);

            if (size != -1 && i % size == 0) { //到批量限制次数
                isExecute = true;
            } else if (i == sqls.size()) { //到末尾记录
                isExecute = true;
            } else {
                //...
            }

            if (isExecute) {
                rawExcute(String.join(";", batchSqls));
                //LOG
                //System.out.println("批量执行了 " + batchSqls.size() + " 条语句：" + JSONArray.toJSONString(batchSqls));
                batchSqls.clear();
            }

            i++;
        }

        //LOG
        //System.out.println("总共执行了 " + sqls.size() + " 条语句：" + JSONArray.toJSONString(sqls));
    }

    /**
     * 分页查询
     * 注意：sqlserver 数据库 2008以下版本分页有问题，需执行处理
     *
     * @param querySql 查询语句
     * @param countSql 统计语句
     * @param page     分页对象
     * @return
     */
    @Override
    public SqlRowSet rawQueryPage(String querySql, String countSql, List<Object> params, Pagination page) {
        SqlRowSet rs = null;
        if (page != null && page.getSize() > 0) {
            if (StringUtils.isEmpty(params)) {
                rs = this.rawQuery(countSql);
            } else {
                rs = this.rawQuery(countSql, params.toArray());
            }
            if (rs != null && rs.next()) {
                int count = rs.getInt(1);
                page.setTotal(count);
                if (count > 0) {
                    if (DBTypeUtils.isSqlServer(this.getDbType())) {//sqlserver
                        if (this.getDatabaseVersion() >= 11.0) {//sqlserver 2012 以上版本支持
                            // OFFSET 0 ROWS FETCH NEXT 50 ROWS ONLY
                            querySql += " OFFSET " + (page.getPage() - 1) * page.getSize() + " ROWS FETCH NEXT " + page.getSize() + " ROWS ONLY";
                        } else {
                            //sqlserver 2008====================================================================
//                    public SqlRowSet getPagedData(int pageNumber, int pageSize) {
//                        int startRow = (pageNumber - 1) * pageSize + 1;
//                        int endRow = pageNumber * pageSize;
//                        String sql = "WITH PagedData AS (SELECT *, ROW_NUMBER() OVER (ORDER BY some_column) AS RowNum FROM your_table) " +
//                                "SELECT * FROM PagedData WHERE RowNum BETWEEN ? AND ?";
//                        return jdbcTemplate.queryForRowSet(sql, startRow, endRow);
//                    }
                        }
                    } else if (DBTypeUtils.isKingBase(this.getDbType()) || DBTypeUtils.isPg(this.getDbType())) {//人大金仓、PostgreSQL
                        //SELECT * FROM user ORDER BY id LIMIT page_size OFFSET (page - 1) * page_size;
                        querySql += " LIMIT " + page.getSize() + " OFFSET " + (page.getPage() - 1) * page.getSize();
                        //querySql = this.refactorySQLSelect(querySql, page);
                    } else if (DBTypeUtils.isMySql(this.getDbType())) {//mysql
                        //SELECT * FROM users LIMIT offset_value, number_of_records;
                        querySql += " LIMIT " + (page.getPage() - 1) * page.getSize() + "," + page.getSize();
                    } else if (DBTypeUtils.isOracle(this.getDbType())) {
                        if (this.getDatabaseVersion() >= 12.0) {//oracle 12c 以后版本
                            querySql += " OFFSET " + (page.getPage() - 1) * page.getSize() + " ROWS FETCH NEXT " + page.getSize() + " ROWS ONLY ";
                        } else {
                            log.error("不支持分页查询，请升级数据库到oracle 12c以上版本");
                        }
                    }
                    if (StringUtils.isEmpty(params)) {
                        return jdbc.queryForRowSet(querySql);
                    } else {
                        return jdbc.queryForRowSet(querySql, params.toArray());
                    }
                }
            }
        }
        return null;
    }


    /**
     * 生成 语句
     *
     * @param sqltype     select or delete
     * @param objectClass
     * @param column
     * @param values
     * @param where
     * @param order
     * @param <T>
     * @return
     */
    private <T> SQLModel getInSql(String sqltype, Class<T> objectClass, String column, List<Object> values, Where where, Order order) {
        if (StringUtils.isNotEmpty(column) && StringUtils.isNotEmpty(values)) {
            List<String> newValues = new ArrayList<>();
            //id去重复
            for (Object value : values) {
                if (value != null && !newValues.contains(value)) {
                    newValues.add(value.toString());
                }
            }
            SQLModel sqlModel = new SQLModel();
            List<Object> args = new ArrayList<>();
            String table = EntityUtils.tableName(objectClass);
            StringBuffer buffer = new StringBuffer();
            StringBuffer pre_buf = new StringBuffer();
            if ("delete".equals(sqltype)) {
                buffer.append("DELETE  FROM ");
            } else {
                buffer.append("SELECT * FROM ");
            }
            buffer.append(table);
            buffer.append(" WHERE exists (");
            //String dbtype = this.getDbType().toString();
            //           if (newValues.size() > 100) {//系统不支持【传入的请求具有过多的参数。该服务器支持最多 2100 个参数】
            String argStr = String.join(", ", newValues).replaceAll(" ", "");
            args.add(argStr);
            buffer.append("SELECT 1 FROM (");
            if (isKingBase() || isPG()) {
                buffer.append("SELECT unnest(string_to_array(?, ',')) AS IDS");
            } else if (isSqlServer()) {
                log.info("改查询方式需求sqlserver自定义函数[F_SplitString] 支持!");
                buffer.append("select id as IDS from dbo.F_SplitString(?,',')"); //采用自定义函数
            } else if (isMySql() || isOracle()) {
                //SELECT jt.item as id FROM JSON_TABLE(CONCAT('["', REPLACE('苹果,香蕉,橙子', ',', '","'), '"]'),'$[*]' COLUMNS (item VARCHAR(200) PATH '$')) AS jt
                String varchar = "VARCHAR";
                if (isOracle()) {
                    varchar = "VARCHAR2";
                }
                buffer.append("SELECT jt.item as IDS ");
                buffer.append("FROM JSON_TABLE(CONCAT('[\"', REPLACE(?, ',', '\",\"'), '\"]'),'$[*]' COLUMNS (item " + varchar + "(200) PATH '$')) AS jt");
            } else {
                //log.error("查询语句最多 2000 个参数，请减少参数的数目！");
                return null;
            }
            buffer.append(") as TM_IDS WHERE " + table + "." + column + " = TM_IDS.IDS");
//            } else {
//                args.addAll(newValues);
//                if (isMySql() || isOracle()) {
//                    pre_buf.append("WITH TM_IDS(id) AS (");
//                    for (int i = 0; i < newValues.size(); i++) {
//                        pre_buf.append("SELECT ? ");
//                        if (isOracle()) {
//                            pre_buf.append("FROM DUAL ");
//                        }
//                        if (i != newValues.size() - 1) {
//                            pre_buf.append("UNION ALL ");
//                        }
//                    }
//                    pre_buf.append(") ");
//                    buffer.append("SELECT 1 FROM TM_IDS WHERE " + table + "." + column + "=TM_IDS.ID" );
//                } else {
//                    buffer.append("SELECT 1 FROM (values");
//                    for (int i = 0; i < newValues.size(); i++) {
//                        buffer.append("(?),");
//                    }
//                    buffer.deleteCharAt(buffer.length() - 1);
//                    buffer.append(") as TM_IDS(");
//                    buffer.append(column);
//                    buffer.append(") WHERE " + table + ".");
//                    buffer.append(column);
//                    buffer.append("=TM_IDS.");
//                    buffer.append(column);
//                }
//            }
            buffer.append(") ");
            if (where != null) {
                String whereSql = where.sql(this.getDbType()).trim();
                if (StringUtils.isNotEmpty(whereSql)) {
                    if (whereSql.startsWith("where")) {
                        whereSql = whereSql.substring(5);
                    }
                    buffer.append(" and ");
                    buffer.append(whereSql);
                    args.addAll(where.args(this.getDbType()));
                }
            }
            if (order != null) {
                buffer.append(" ");
                buffer.append(order.sql(this.getDbType()));
            }
            String sql = pre_buf.toString() + buffer.toString();
            sqlModel.setSql(sql);
            sqlModel.setArgs(args);
            sqlModel.setTableName(table);
            return sqlModel;
        } else {
            return null;
        }
    }

    /**
     * 根据条件id in 方式查询
     *
     * @param objectClass 实体类
     * @param column      检索字段
     * @param values      检索值
     * @param where       where条件
     * @param order       排序
     */
    @Override
    public <T> List<T> queryListIn(Class<T> objectClass, String column, List<Object> values, Where where, Order order) {
        SQLModel sqlModel = this.getInSql("select", objectClass, column, values, where, order);
        if (sqlModel != null) {
            return this.rawQueryList(objectClass, sqlModel.sql(), sqlModel.getArgs().toArray());
        }
        return Collections.emptyList();
    }

    /**
     * 根据条件id in 方式查询
     *
     * @param objectClass 实体类
     * @param func        检索字段
     * @param values      检索值
     * @param where       where条件
     * @param order       排序
     *                    List<Object> values = new ArrayList<Object>();
     *                    values.add("ZQEVBRI4F091U1F9RO1961");
     *                    values.add("ZQEVBVVGD091U1FF7H1911");
     *                    Where where = Where.create();
     *                    where.eq(SysOrg::getUsed, 1);
     *                    Order order = Order.create();
     *                    order.orderByAsc(SysOrg::getTmSort);
     *                    list = entityService.queryListIn(SysOrg.class, SysOrg::getId, values, where, order);
     * @param <T>
     * @return
     */
    @Override
    public <T> List<T> queryListIn(Class<T> objectClass, Func<T, ?> func, List<Object> values, Where where, Order order) {
        EntityLambdaUtils utils = new EntityLambdaUtils(func);
        return this.queryListIn(objectClass, utils.columnName(), values, where, order);
    }

    /**
     * 通过in的方式删除数据
     *
     * @param objectClass
     * @param column
     * @param values
     * @param where
     * @param <T>
     * @return
     */
    @Override
    public <T> boolean deleteListByIn(Class<T> objectClass, String column, List<Object> values, Where where) {
        SQLModel sqlModel = this.getInSql("delete", objectClass, column, values, where, null);
        if (sqlModel != null) {
            return this.execute(sqlModel.sql(), sqlModel.getArgs().toArray());
        }
        return false;
    }

    /**
     * 通过in的方式删除数据
     *
     * @param objectClass
     * @param func
     * @param values
     * @param where
     * @param <T>
     * @return
     */
    @Override
    public <T> boolean deleteListByIn(Class<T> objectClass, Func<T, ?> func, List<Object> values, Where where) {
        EntityLambdaUtils utils = new EntityLambdaUtils(func);
        return this.deleteListByIn(objectClass, utils.columnName(), values, where);
    }

    /**
     * 判断数据库中是否存在函数
     *
     * @param type FUNCTION
     * @param name 函数名称
     * @return
     */
    @Override
    public boolean isExists(String type, String name) {
        String sql = "";
        if (this.isSqlServer()) {
            sql = "SELECT count(*) as c FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_TYPE = ? AND ROUTINE_NAME = ? ";
        }
        if (sql.length() > 0) {
            SqlRowSet rs = this.rawQuery(sql, type, name);
            if (rs != null && rs.next()) {
                if (rs.getInt("c") > 0) {
                    return true;
                }
            }
        }
        return false;
    }

}