package com.yunhesoft.system.kernel.config;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * 系统级 controller 拦截器
 * 
 * <AUTHOR>
 *
 */
@Component
public class SysRequestFilter extends OncePerRequestFilter {

	/**
	 * [OncePerRequestFilter] 顾名思义，它能够确保在一次请求中只通过一次filter，而不会重复执行是由Spring提供的抽象类
	 */
	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hain filterChain)
			throws ServletException, IOException {

		// 项目报"xxx响应头缺失“漏洞处理方案
		response.addHeader("X-XSS-Protection", "1; mode=block");
		response.addHeader("Content-Security-Policy", "object-src 'self'");
		response.addHeader("Referrer-Policy", "origin");
		response.addHeader("X-Permitted-Cross-Domain-Policies", "master-only");
		response.addHeader("X-Frame-Options", "SAMEORIGIN");
		response.addHeader("X-Content-Type-Options", "nosniff");
		response.addHeader("X-Download-Options", "noopen");
		response.addHeader("Strict-Transport-Security", "max-age=16070400");

//        //处理cookie问题
//        Cookie[] cookies = request.getCookies();
//        if (cookies != null) {
//            for (Cookie cookie : cookies) {
//                String value = cookie.getValue();
//                StringBuilder builder = new StringBuilder();
//                builder.append(cookie.getName()+"="+value+";");
//                builder.append("Secure;");//Cookie设置Secure标识
//                builder.append("HttpOnly;");//Cookie设置HttpOnly
//                response.addHeader("Set-Cookie", builder.toString());
//            }
// 
//        }

		filterChain.doFilter(request, response);
	}

	/**
	 * 将原过滤器中操作封装
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	public void filterOperator(HttpServletRequest request, HttpServletResponse response){
		//项目报"xxx响应头缺失“漏洞处理方案
		response.addHeader("X-XSS-Protection","1; mode=block");
		response.addHeader("Content-Security-Policy","object-src 'self'");
		response.addHeader("Referrer-Policy","origin");
		response.addHeader("X-Permitted-Cross-Domain-Policies","master-only");
		response.addHeader("X-Frame-Options","SAMEORIGIN");
		response.addHeader("X-Content-Type-Options","nosniff");
		response.addHeader("X-Download-Options","noopen");
		response.addHeader("Strict-Transport-Security","max-age=16070400");

		//处理cookie问题
		Cookie[] cookies = request.getCookies();
		if (cookies != null) {
			for (Cookie cookie : cookies) {
				String value = cookie.getValue();
				StringBuilder builder = new StringBuilder();
				builder.append(cookie.getName()+"="+value+";");
				builder.append("Secure;");//Cookie设置Secure标识
				builder.append("HttpOnly;");//Cookie设置HttpOnly
				response.addHeader("Set-Cookie", builder.toString());
			}

		}
	}

}
