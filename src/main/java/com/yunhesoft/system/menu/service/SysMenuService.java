package com.yunhesoft.system.menu.service;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.service.ISysRoleLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.impl.TreeServiceImpl;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
//import com.yunhesoft.system.menu.entity.po.MtmIndexInfo;
import com.yunhesoft.system.menu.entity.po.SysMenu;
import com.yunhesoft.system.menu.entity.po.SysMenuLib;
import com.yunhesoft.system.menu.entity.po.SysMenuLibClass;
import com.yunhesoft.system.menu.entity.vo.MetaVo;
import com.yunhesoft.system.menu.entity.vo.RouterVo;
import com.yunhesoft.system.role.service.ISysRolePermService;
import com.yunhesoft.system.role.service.ISysUserPermService;
import com.yunhesoft.system.role.utils.IPermUtils;
import com.yunhesoft.system.third.service.ThirdAuthService;
import com.yunhesoft.system.tools.sysConfig.service.impl.SysConfigServiceImpl;
import com.yunhesoft.tmtools.JwtUser;
import com.yunhesoft.tmtools.TokenUtils;

import lombok.extern.log4j.Log4j2;

/**
 * 系统菜单
 *
 * <AUTHOR>
 */
@Log4j2
@Service
public class SysMenuService {
    public final static String MODEL_ID = "SYS_MENU";
    public final static String ROOT_ID = "root";
    // private final static long MENU_TIMEOUT = 24 * 60 * 60; // redis菜单失效时间

    @Autowired
    private TreeServiceImpl treeService;

    @Autowired
    private IPermUtils perUtils; // 权限资源

    @Autowired
    private ISysMenuLibService menuLibserv;// 菜单库资源

    @Autowired
    private EntityService dao;// 数据库服务

    @Autowired
    private RedisUtil redis;

    @Autowired
    private SysConfigServiceImpl configService;// 系统配置信息

    @Autowired
    private ISysRolePermService iSysRolePermServ; // 角色权限

    @Autowired
    private ISysUserPermService iSysUserPermServ; // 个人权限
    @Autowired
    private Environment env;// 获取配置信息
    @Autowired
    private ISysRoleLevelService roleLevelService;

    /**
     * 菜单redis-key值
     *
     * @param userId
     */
    private String getMenuKey() {
        return "SYSTEM:MENU";
    }

    private String getMenuInfoKey(String tenant_id) {
        String key = "SYSTEM:MENU_INFO";
        if (MultiTenantUtils.enalbe()) {
            String tid = null;
            if (StringUtils.isNotEmpty(tenant_id)) {
                tid = tenant_id;
            } else {
                tid = MultiTenantUtils.getTenantId();
            }
            if (tid != null) {
                key += ":" + tid;
            }
        }
        return key;
    }

    /**
     * 加载菜单总入口
     *
     * @param userId 用户ID
     * @return
     */
    public List<RouterVo> routerList(String mode, String userId) {
        List<RouterVo> routers = thirdRouterList(userId);
        if (routers != null) {
            return routers;
        }
        Map<String, Object> map = perUtils.getPerms(mode, userId, "M"); // 获取权限有权限的对象;
        return routerList(map);
    }

    public List<RouterVo> routerList(String userId) {
        return this.routerList("1", userId);
    }

    /**
     * 获取菜单
     *
     * @param mode 1：正常模式；2：协作模式
     * @return
     */
    public Res<?> menuRouters(String mode) {
        String userId = SysUserHolder.getCurrentUser().getId();
        JSONArray jarry = null;
        String key = userId;
        if (StringUtils.isNotEmpty(mode)) {
            key += ":" + mode;
        }
        try {
            jarry = this.getMenuFromRedis(key);
        } catch (Exception e) {
            log.error("菜单初次解析出现错误：{}", userId);
            List<RouterVo> list = this.routerList(mode, userId);
            this.setMenuToRedis(key, list);
            return Res.OK(list);
        }
        if (jarry == null || jarry.size() == 0) {
            List<RouterVo> list = this.routerList(mode, userId);
            this.setMenuToRedis(key, list);
            return Res.OK(list);
        } else {
            return Res.OK(jarry);
        }
    }


    /**
     * 获得系统菜单
     *
     * @return
     */
    public Res<?> menuRouters() {
        return this.menuRouters("1");
    }

    /**
     * 获得快捷导航菜单
     *
     * @param userId
     * @return
     */
    public Res<?> getQitMenuRouters(String userId) {
        // String userId = SysUserHolder.getCurrentUser().getId();
        JSONArray jarry = this.getMenuFromRedis(userId);
        if (jarry == null || jarry.size() == 0) {
            List<RouterVo> list = this.routerList(userId);
            this.setMenuToRedis(userId, list);
            List<RouterVo> rootlist = new ArrayList<RouterVo>();
            RouterVo root = new RouterVo();
            root.setChildren(list);
            root.setMenuName("根菜单");
            root.setMenuType("R");
            rootlist.add(root);
            return Res.OK(rootlist);
        } else {
            JSONArray rootAry = new JSONArray();
            JSONObject root = new JSONObject();
            root.put("menuName", "根菜单");
            root.put("menuType", "R");
            root.put("children", jarry);
            rootAry.add(root);
            return Res.OK(rootAry);
        }
    }

    /**
     * @param userId
     * @return
     * @category 从第三方平台读取路由信息
     */
    private List<RouterVo> thirdRouterList(String userId) {
        JwtUser juser = SysUserHolder.getCurrentJwtUser();
        if (ObjUtils.isEmpty(juser.getSource())) {
            return null;
        }
        String serviceKey = juser.getSource() + ".service";
        String beanName = configService.getSysConfig(serviceKey);
        ThirdAuthService thirdAuthService = SpringUtils.getBean(beanName);
        SysUser user = thirdAuthService.getUserInfo(TokenUtils.createToken(juser));
        return this.getMenuByPermstr(user.getPermissions());
    }

    /**
     * 加载菜单
     *
     * @return
     */
    @SuppressWarnings("unchecked")
    private List<RouterVo> routerList(Map<String, Object> map) {
        // log.info("数据库获取菜单：" + map.get("userId"));
        List<RouterVo> routers = null;
        boolean isAdmin = false;// isAdmin(userId); // 是否为超级管理员
        boolean isTenantAdmin = false;// 是否为租户管理员
        Object obj = map.get("isAdmin");
        if (obj != null && "true".equals(obj.toString())) {// 超级管理员
            isAdmin = true;
        }
        Object objisTenantAdmin = map.get("isTenantAdmin");
        if (objisTenantAdmin != null && "true".equals(objisTenantAdmin.toString())) {// 租户管理员
            isTenantAdmin = true;
        }
        if (isAdmin || isTenantAdmin) {// 超级管理员 or 租户管理员
            List<SysMenu> rootMenus = this.menuList();
            if (rootMenus.size() > 0) {
                routers = this.buildMenus(rootMenus.get(0).getChildren());
                if (StringUtils.isNotEmpty(routers)) {
                    List<SysMenuLib> subPageList = menuLibserv.getSubPageList(); // 子页面
                    if (StringUtils.isNotEmpty(subPageList)) {
                        RouterVo comDir = getComDir();
                        comDir.setChildren(getSubMenuList(subPageList));
                        routers.add(comDir);
                    }
                }
                return routers;
            } else {
                return null;
            }
        } else {// 普通用户
            List<String> listPerms = new ArrayList<String>();
            Object objMenus = null;
            if (map != null) {
                objMenus = map.get("menus");
            }
            if (objMenus != null) {
                listPerms = (List<String>) objMenus;
            } else {
                return null;
            }
            routers = this.buildUserMenus(listPerms);
            // List<String> temp = new ArrayList<String>();
            // temp.add("system:employee:index:query");
            // temp.add("system:org:index:query");
            // temp.add("proj:enterprise:audit:query");
            // routers = this.getMenuByPermstr(temp);
            return routers;
        }
    }

    /**
     * 菜单存入redis
     *
     * @param userId
     * @param timeout
     */
    public void setMenuToRedis(String userId) {
        this.setMenuToRedis(userId, routerList(userId));
    }

    /**
     * 菜单存入redis
     *
     * @param userId
     * @param timeout
     */
    public void setMenuToRedis(String userId, Map<String, Object> map) {
        this.setMenuToRedis(userId, routerList(map));
    }

    /**
     * 菜单存入redis
     *
     * @param userId
     * @param timeout
     */
    public void setMenuToRedis(String userId, List<RouterVo> routers) {
        String json = JSONArray.toJSONString(routers);
        redis.setMapValue(getMenuKey(), userId, json);
        // redis.setJSONString(getMenuKey(userId), routers, MENU_TIMEOUT);
    }

    /**
     * 从redis获取菜单
     *
     * @param userId
     * @return
     */
    public JSONArray getMenuFromRedis(String userId) {
        String json = redis.getMapValue(getMenuKey(), userId);
        if (json != null) {
            return JSONArray.parseArray(json);
        } else {
            return null;// redis.getJSONArray(getMenuKey(userId));
        }
        // log.info("内存中获取菜单:"+userId);

    }

    /**
     * 清除菜单缓存
     *
     * @param userId
     */
    public void removeUserFromRedis(String userId) {
        redis.hDelete(getMenuKey(), userId);
        // redis.delete(getMenuKey(userId));
    }

    public void removeAllUserFromRedis() {
        redis.delete(getMenuKey());
    }

    /**
     * 清除当前用户菜单缓存
     */
    public void removeUserFromRedis() {
        SysUser user = SysUserHolder.getCurrentUser();
        if (user != null) {
            removeUserFromRedis(user.getId());
        }
    }

    /**
     * 获取根节点
     *
     * @return
     */
    private SysMenu getRootNode() {
//		SysMenu root = null;
//		Where where = Where.create();
//		where.eq(SysMenu::getModelId, MODEL_ID);
//		where.eq(SysMenu::getPid, ROOT_ID);
//		List<SysMenu> list = dao.rawQueryListByWhere(SysMenu.class, where);
//		if (StringUtils.isNotEmpty(list)) {
//			root = list.get(0);
//		}
        return getRootNode(null);
    }

    private SysMenu getRootNode(String tenant_id) {
        SysMenu root = null;
        Where where = Where.create();
        where.eq(SysMenu::getModelId, MODEL_ID);
        where.eq(SysMenu::getPid, ROOT_ID);
        List<SysMenu> list;
        if (StringUtils.isNotEmpty(tenant_id)) {
            list = dao.rawQueryListByWhereWithTenant(tenant_id, SysMenu.class, where);// ====多租户====
        } else {
            list = dao.rawQueryListByWhere(SysMenu.class, where);
        }
        if (StringUtils.isNotEmpty(list)) {
            root = list.get(0);
        }
        return root;
    }

//	/**
//	 * 获取根节点
//	 *
//	 * @return
//	 */
//	private SysMenu getRootNodeTenant(String tenantId) {
//		SysMenu root = null;
//		Where where = Where.create();
//		where.eq(SysMenu::getModelId, MODEL_ID);
//		where.eq(SysMenu::getPid, ROOT_ID);
////		where.eq(SysMenu::getTenant_id, tenantId);
//		List<SysMenu> list = dao.rawQueryListByWhereWithTenant(tenantId, SysMenu.class, where);// ====多租户====
//		if (StringUtils.isNotEmpty(list)) {
//			root = list.get(0);
//		}
//		return root;
//	}

    /**
     * 批量添加菜单数据
     *
     * @param list
     * @return
     */
    public int insertMenus(List<SysMenu> list) {
        return insertMenus(list, null);
    }

    /**
     * 批量添加菜单数据
     *
     * @param list
     * @return
     */
    public int insertMenus(List<SysMenu> list, String tenant_id) {
        int result = 0;
        if (StringUtils.isEmpty(tenant_id)) {
            result = dao.insertBatch(list);
        } else {
            int[] i = dao.rawInsertBatchWithTenant(tenant_id, list);
            if (i != null && i.length > 0) {
                result = 1;
            }
        }
        if (result > 0) {
            removeUserFromRedis();// 清除缓存
            this.initMenuData(tenant_id);// 加缓存
        }
        return result;
    }

    /**
     * 批量添加菜单数据
     *
     * @param list
     * @return
     */
    public int insertMenusDisableTenant(List<SysMenu> list) {
        int i = dao.insertBatchDisableTenant(list);
        if (i > 0) {
            removeUserFromRedis();// 清除缓存
            this.initMenuData(null);// 加缓存
        }
        return i;
    }

    /**
     * 根据按钮权限字符串list获取菜单
     *
     * @param list system:employee:add
     * @return
     */
    public List<RouterVo> getMenuByPermstr(List<String> list) {
        List<String> menuIdList = getMenuId(list);
        return buildUserMenus(menuIdList);
    }

    /**
     * 根据按钮权限字符串list 获取菜单id
     *
     * @param list system:employee:add
     * @return
     */
    private List<String> getMenuId(List<String> list) {
        List<String> menuIdList = new ArrayList<String>();
        if (StringUtils.isNotEmpty(list)) {
            List<SysMenu> listMenu = getMenuList(false, false, false, false);
            if (StringUtils.isNotEmpty(listMenu)) {
                Map<String, SysMenu> menuMap = listMenu.stream().collect(Collectors.toMap(SysMenu::getComponent, a -> a, (k1, k2) -> k1));
                for (String permStr : list) {
                    int index = permStr.lastIndexOf(":");
                    if (index >= 0) {
                        String key = permStr.substring(0, index);
                        key = key.replaceAll(":", "/");
                        SysMenu menu = menuMap.get(key);
                        if (menu != null) {
                            if (!menuIdList.contains(menu.getId())) {
                                menuIdList.add(menu.getId());
                            }
                        }
                    }
                }
            }
        }
        return menuIdList;
    }

    // 生成用户菜单======================================================================================================================================

    /**
     * 获得用户菜单
     *
     * @param listPerms
     * @return
     */
    public List<RouterVo> buildUserMenus(List<String> listPerms) {
        List<RouterVo> routers = new ArrayList<RouterVo>();
        List<SysMenu> listMenus = this.getMenuList();
        List<RouterVo> newMenus = new ArrayList<RouterVo>();
        List<RouterVo> subMenus = new ArrayList<RouterVo>(); // 子菜单
        if (StringUtils.isNotEmpty(listMenus)) {
            Map<String, List<SysMenuLib>> subPageMap = menuLibserv.getSubPage(); // 子页面
            Hashtable<String, List<RouterVo>> dicNode = new Hashtable<>();
            Map<String, SysMenu> map = new HashMap<String, SysMenu>();
            RouterVo rootNode = null;
            List<String> perms = new ArrayList<String>();
            //获取当前用户的最大级别
            Integer roleLevel = null;
            SysRole currentUserRoleLevel = roleLevelService.getCurrentUserRoleLevel();
            if (currentUserRoleLevel != null) {
                roleLevel = currentUserRoleLevel.getRoleLevel();
            }
            // 开始整理数据
            for (SysMenu sysMenu : listMenus) {
                //当前用户级别不为空
                if ((!SysUserUtil.isAdmin()) && sysMenu.getRoleLevel() != null) {
                    //菜单已经制定角色级别
                    if (roleLevel == null || roleLevel > sysMenu.getRoleLevel()) {
                        continue;
                    }
                }
                String menuid = sysMenu.getId();
                map.put(menuid, sysMenu);
                if (SysMenu.TYPE_ROOT.equals(sysMenu.getMenuType())) {// 根节点
                    rootNode = this.buildMenu(sysMenu, false);
                }
                if (!SysMenu.TYPE_BUTTON.equals(sysMenu.getMenuType())) {// 非按钮
                    routers.add(this.buildMenu(sysMenu, false));
                }

                if (listPerms.contains(menuid)) {
                    String s = "";
                    SysMenu tempMenu = null;
                    if (SysMenu.TYPE_MENU.equals(sysMenu.getMenuType())) {// 菜单项
                        s = menuid;
                        tempMenu = sysMenu;
                        List<SysMenuLib> libList = subPageMap.get(sysMenu.getPath());
                        if (StringUtils.isNotEmpty(libList)) {
                            subMenus.addAll(getSubMenuList(libList));
                        }
                    } else if (SysMenu.TYPE_BUTTON.equals(sysMenu.getMenuType())) {// 按钮项
                        tempMenu = map.get(sysMenu.getPid()); // 获取其菜单项信息
                        if (tempMenu != null) {
                            s = tempMenu.getId();
                        }
                    }
                    if (StringUtils.isNotEmpty(s) && !perms.contains(s)) {
                        perms.add(s);
                        List<RouterVo> childList = dicNode.get(tempMenu.getPid());
                        if (null == childList) {
                            childList = new ArrayList<RouterVo>();
                            dicNode.put(tempMenu.getPid(), childList);
                        }
                        childList.add(this.buildMenu(tempMenu, false));
                    }
                }
            }
            // 调用生成树方法
            newMenus = this.getBranchTree(routers, rootNode, dicNode); // 根据叶子节点生成整棵树
            if (subMenus.size() > 0) {
                RouterVo comDir = getComDir();
                comDir.setChildren(subMenus);
                newMenus.add(comDir);
            }
        }
        return newMenus;
    }

    public boolean getMenuByRoleLevel(Integer sysMenuRoleLevel, SysRole sysRole) {
        //获取当前用户的最大级别
        Integer roleLevel = null;
        if (sysRole != null) {
            roleLevel = sysRole.getRoleLevel();
        }
        //当前用户级别不为空
        if ((!SysUserUtil.isAdmin()) && sysMenuRoleLevel != null) {
            //菜单已经制定角色级别
            if (roleLevel == null || roleLevel > sysMenuRoleLevel) {
                return false;
            }
        }
        return true;
    }

    public boolean getMenuByRoleLevel(String permId, SysRole sysRole, Map<String, Map<String, Object>> menuData) {
//        SysMenu sysMenu = menuData.get(permId);
//        if (sysMenu == null) {
//            return false;
//        }
        if (menuData == null || !menuData.containsKey(permId)) {
            return false;
        }
        Map<String, Object> menu = menuData.get(permId);
        Object roleLevelObj = menu.get("roleLevel");
        Integer roleLevel = null;
        if (roleLevelObj != null) {
            roleLevel = Integer.parseInt(roleLevelObj.toString());
        }
        return this.getMenuByRoleLevel(roleLevel, sysRole);
    }

    /**
     * 生成组件目录
     *
     * @return
     */
    private RouterVo getComDir() {
        RouterVo v = new RouterVo();
        v.setHidden(true);
        v.setComponent("Layout");
        v.setId(TMUID.getUID());
        v.setIsFrame("1");
        v.setShowType("0");
        v.setApplyRange("1");
        v.setName("/" + v.getId());
        v.setPath("/" + v.getId());
        v.setPid("0");
        v.setSort(100000L);
        MetaVo metaVo = new MetaVo("-", "link", false);
        v.setMeta(metaVo);
        return v;
    }

    /**
     * 子菜单库转换为显示菜单
     *
     * @param libList
     * @return
     */
    private List<RouterVo> getSubMenuList(List<SysMenuLib> libList) {
        List<RouterVo> list = new ArrayList<RouterVo>();
        if (StringUtils.isNotEmpty(libList)) {
            for (SysMenuLib lib : libList) {
                lib.setPath(lib.getComponent());
                SysMenu sysMenu = lib2menu(null, lib);
                list.add(buildMenu(sysMenu, false));
            }
        }
        return list;

    }

    /**
     * 根据叶子节点生成只包含叶子节点分支的树，可以是深林，最终汇成一棵树
     *
     * @param allNodeList 整棵树或者整个深林的所有的节点
     * @param rootNode    根节点
     * @param dicNode     需要展示的叶子节点
     * @return 分支树
     */
    private List<RouterVo> getBranchTree(List<RouterVo> allNodeList, RouterVo rootNode, Hashtable<String, List<RouterVo>> dicNode) {
        if (StringUtils.isEmpty(allNodeList) || StringUtils.isEmpty(dicNode)) {
            return Collections.emptyList();
        }
        if (rootNode == null) {// 根节点
            rootNode = new RouterVo();
            rootNode.setId("0");
            rootNode.setPid(ROOT_ID);
        }
        Map<String, Boolean> mapNodes = new HashMap<String, Boolean>(); // 判断某个节点在树中是否存在
        while (!(dicNode.containsKey(rootNode.getPid()) && dicNode.size() <= 1)) { // 递归出口
            conbineDirectParant(allNodeList, mapNodes, rootNode, dicNode);
            if (StringUtils.isEmpty(dicNode)) {
                return Collections.emptyList();
            }
        }
        List<RouterVo> rtnlist = dicNode.get(rootNode.getPid());
        if (StringUtils.isNotEmpty(rtnlist)) {
            List<RouterVo> childList = rtnlist.get(0).getChildren();
            // 按照排序字段重新排序
            // childList.stream().sorted(Comparator.comparing(RouterVo::getSort).reversed()).collect(Collectors.toList());//逆序
            return childList.stream().sorted(Comparator.comparing(RouterVo::getSort)).collect(Collectors.toList());// 正序
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 自底向上，每次每个叶子节点上升一层，查找并合并一层的父子关系，通过dicNode里面的父子关系把children并到父节点数据结构的childList中
     *
     * @param allNodeList 所有节点，已经找到祖宗孩子关系的节点就remove，不重复添加
     * @param dicNode     每次只存一层关系合并完就删除，最后只剩下根节点和第一层节点的父子关系
     */
    private void conbineDirectParant(List<RouterVo> allNodeList, Map<String, Boolean> mapNodes, RouterVo rootNode, Hashtable<String, List<RouterVo>> dicNode) {
        if (StringUtils.isNotEmpty(dicNode)) {
            List<String> removeKeys = new ArrayList<String>();
            // 判断 dicNode 是否在 allNodeList，如果没有的话删除 dicNode节点，防止死循环
            for (String key : dicNode.keySet()) {
                if (key.equals(rootNode.getId()) || key.equals(rootNode.getPid())) { // 根节点不用判断
                    continue;
                }
                if (mapNodes.get(key) != null && mapNodes.get(key)) { // map中查找节点是否存在
                    continue;
                }
                boolean bln = allNodeList.stream().anyMatch(e -> e.getId().equals(key)); // 在allNodeList查找节点是否存在
                mapNodes.put(key, bln);
                if (!bln) {// 删除未找到父节点的节点
                    removeKeys.add(key);// dicNode.remove(key);
                }
            }
            if (StringUtils.isNotEmpty(removeKeys)) {// 删除无效节点
                for (String key : removeKeys) {
                    dicNode.remove(key);// log.info("remove:" + key);
                }
            }
            Iterator<RouterVo> iterator = (Iterator<RouterVo>) allNodeList.iterator();
            while (iterator.hasNext()) {
                RouterVo parentNode = iterator.next();
                if (dicNode.containsKey(parentNode.getId())) { // 爸爸
                    List<RouterVo> childList = dicNode.get(parentNode.getId());
                    // 把父子关系保存在node结构体，dicNode的父子关系就可以remove了
                    // ***此处需要去重********************************************
                    // parentNode.getChildren().addAll(childList);
                    if (StringUtils.isNotEmpty(childList)) {
                        for (RouterVo cnode : childList) {
                            if (!parentNode.getChildren().contains(cnode)) {
                                parentNode.getChildren().add(cnode);
                            }
                        }
                    }
                    addOneNode(parentNode, dicNode);
                    // 删除已经用完了的下一级，构造dicNode.size==1的递归出口，保证不同树叶子节点都可以递归到深林的根
                    dicNode.remove(parentNode.getId());
                    // if (!parentNode.getId().equals(rootNode.getId())) {// 根节点不能删除，否则死循环了
                    // iterator.remove();
                    // }
                }
                /*
                 * if(dicNode.size()==1 && dicNode.containsKey(rootNode.getPid())) { break; }
                 */
                // log.info(parentNode.getMeta().getTitle());
            }
        }

    }

    /**
     * 更新dicNode的父子关系，把当前节点合并到上一层的父子关系或者升级到上一层的父子关系
     *
     * @param node    dicNode中的Key对应的节点，也就是value的父节点
     * @param dicNode
     */
    private void addOneNode(RouterVo node, Hashtable<String, List<RouterVo>> dicNode) {
        List<RouterVo> childList = dicNode.get(node.getPid());
        if (childList == null) {
            childList = new ArrayList<RouterVo>();
            dicNode.put(node.getPid(), childList);
        }
        if (childList.size() == 0) {
            childList.add(node);
        } else {
            // 防止添加相同节点
            boolean bln = childList.stream().anyMatch(e -> e.getId().equals(node.getId()));
            if (!bln) {
                childList.add(node);
            }
        }
    }
    // ==============================================================================================================================================

    /**
     * 修正菜单左右值
     *
     * @param modelId
     * @return
     */
    public int correctTree(String modelId) {
        try {
            if (modelId == null) {
                modelId = MODEL_ID;
            }
            int i = treeService.correctTree(SysMenu.class, modelId);
            if (i > 0) {
                removeUserFromRedis();
            }
            return i;
        } catch (IllegalArgumentException | IllegalAccessException | InstantiationException | ClassNotFoundException |
                 SQLException e) {
            throw new RuntimeException(e);
        }
    }

    public int correctTree(String modelId, String tenant_id) {
        try {
            if (modelId == null) {
                modelId = MODEL_ID;
            }
            int i = treeService.correctTree(tenant_id, SysMenu.class, TreeServiceImpl.DEF_CHILDREN_FIELD, modelId);
//			int i = treeService.correctTree(SysMenu.class, modelId);
            if (i > 0) {
                removeUserFromRedis();
            }
            return i;
        } catch (IllegalArgumentException | IllegalAccessException | InstantiationException | ClassNotFoundException |
                 SQLException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 修正菜单左右值
     *
     * @param modelId
     * @return
     */
    public int correctTree() {
        return this.correctTree(null);
    }

    public List<SysMenu> menuList() {
        return menuList(null);
    }

    public List<SysMenu> menuList(String tenant_id) {
        SysMenu menuRoot = treeService.getRootTree(tenant_id, SysMenu.class, MODEL_ID, this.getMenuList(tenant_id, true, true, true, true));
        List<SysMenu> menus = new ArrayList<SysMenu>();
        menus.add(menuRoot);
        return menus;
    }

    /**
     * 根据菜单ID查询一个菜单
     *
     * @param id 菜单id
     * @return
     */
    public SysMenu getMenu(String id) {
//		return dao.queryObjectById(SysMenu.class, id);
        return dao.queryObjectByIdDisableTenant(SysMenu.class, id);
    }

    public List<RouterVo> buildMenus(List<SysMenu> menus) {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (SysMenu menu : menus) {
            RouterVo router = buildMenu(menu, true);
            routers.add(router);
        }
        return routers;
    }

    /**
     * 菜单对象转换
     *
     * @param menu
     * @param setChild
     * @return
     */
    private RouterVo buildMenu(SysMenu menu, boolean setChild) {
        RouterVo router = new RouterVo();
        router.setId(menu.getId());
        router.setPid(menu.getPid());
        router.setHidden("0".equals(menu.getVisible()));
        router.setName(getRouteName(menu));
        router.setComponent(getComponent(menu));
        router.setQuery(getQuery(menu));
        router.setPath(getRouterPath(menu));
        router.setIsFrame(menu.getIsFrame());
        router.setApplyRange(menu.getApplyRange());
        router.setShowType(menu.getShowType());
        router.setMeta(getMeta(menu));
        router.setMenuName(menu.getMenuName());
        router.setMenuType(menu.getMenuType());
        router.setIcon(menu.getIcon());
        router.setVisible(menu.getVisible());

        // router.setSort(menu.getLft());
        if (menu.getOrderNum() != null) {
            router.setSort(Long.parseLong(menu.getOrderNum().toString()));
        } else {
            router.setSort(menu.getLft());
        }
        List<SysMenu> cMenus = menu.getChildren();
        if (!cMenus.isEmpty() && cMenus.size() > 0 && SysMenu.TYPE_DIR.equals(menu.getMenuType())) {
            router.setAlwaysShow(true);
            router.setRedirect("noRedirect");
            if (setChild) {
                router.setChildren(buildMenus(cMenus));
            }
        } else if (isMeunFrame(menu)) {
            if (setChild) {
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(getComponent(menu));
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(getMeta(menu));
                childrenList.add(children);
                router.setChildren(childrenList);
            } else {// 此处有问题
                // TODO:此处有问题 =====================
                // 一级目录建立菜单有问题，一级目录必须是文件夹，目前设置上已经控制了
                router.setPath(menu.getPath());
                router.setComponent(getComponent(menu));
                router.setName(StringUtils.capitalize(menu.getPath()));
                router.setMeta(getMeta(menu));
            }
        }
        return router;
    }

    private MetaVo getMeta(SysMenu menu) {
        return new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()));
    }

    private String getRouteName(SysMenu menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMeunFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获得地址
     *
     * @param menu
     * @return
     */
    private String getRouterPath(SysMenu menu) {
        String routerPath = menu.getPath();
        if (routerPath == null) {
            routerPath = "";
        }
        if (SysMenu.TYPE_DIR.equals(menu.getMenuType())) {
            if (StringUtils.isEmpty(routerPath)) {
                routerPath = "/" + TMUID.getUID();
            }
        } else {
            if ("1".equals(menu.getIsFrame())) {// 非iframe模式
                int index = routerPath.indexOf("?");
                if (index >= 0) {// 去掉参数
                    routerPath = routerPath.substring(0, index);
                }
            }
        }
        /*
         * // 非外链并且是一级目录（类型为目录） if ("0".equals(menu.getPid()) &&
         * SysMenu.TYPE_DIR.equals(menu.getMenuType()) &&
         * SysMenu.NO_FRAME.equals(menu.getIsFrame())) { // routerPath = "/" +
         * menu.getPath();
         *
         * // 去掉外链路径问号参数（关哥） // routerPath = "/"; // } else if (isMeunFrame(menu)) {//
         * 非外链并且是一级目录（类型为菜单） // routerPath = "/"; } else { if
         * ("1".equals(menu.getIsFrame())) {// 非iframe模式 int index =
         * routerPath.indexOf("?"); if (index >= 0) {// 去掉参数 routerPath =
         * routerPath.substring(0, index); } } }
         */
        return routerPath;
    }

    /**
     * 获取url参数
     *
     * @param menu
     * @return
     */
    private String getQuery(SysMenu menu) {
        String query = menu.getQuery();// StringUtils.isNotEmpty(query) &&
        if (StringUtils.isNotEmpty(menu.getPath())) {
            boolean isTds = false; // 是否为数据源
            String tdsAlias = "";
            int index = menu.getPath().indexOf("?");
            if (menu.getPath().startsWith("/tds/query/") || menu.getPath().startsWith("/tds/edit/")) {// 数据源菜单
                isTds = true;
                String url = menu.getPath();
                if (index >= 0) {
                    url = url.substring(0, index);
                }
                String[] ary = url.split("\\/");
                tdsAlias = ary[ary.length - 1];
            }
            JSONObject queryObj = null;
            if (StringUtils.isNotEmpty(query)) {
                try {
                    queryObj = JSON.parseObject(query);
                } catch (Exception e) {
                    // return null;
                    queryObj = new JSONObject();
                    log.info("菜单参数错误：" + menu.getPath() + "," + menu.getQuery());
                    log.error("", e);
                }
            } else {
                queryObj = new JSONObject();
            }
            if (isTds && StringUtils.isNotEmpty(tdsAlias)) {
                queryObj.put("TDSALIAS", tdsAlias);
            }

            if (menu.getPath().startsWith("/report/view/")) {// 自定义报表菜单
                String url = menu.getPath();
                if (index >= 0) {
                    url = url.substring(0, index);
                }
                String[] ary = url.split("\\/");
                queryObj.put("id", ary[ary.length - 1]);
            }

            if (menu.getPath().startsWith("/tmsf/data-manager/")) {// 表单数据管理
                String url = menu.getPath();
                if (index >= 0) {
                    url = url.substring(0, index);
                }
                String[] ary = url.split("\\/");
                queryObj.put("id", ary[ary.length - 1]);
            }

            if (index >= 0) {// 把地址栏参数变成对象
                String param = menu.getPath().substring(index + 1);
                String[] p = param.split("&");
                for (String s : p) {
                    String[] v = s.split("=");
                    String key = v[0];
                    String value = "";
                    if (v.length > 1) {
                        value = v[1];
                    }
                    queryObj.put(key, value);
                }

            }
            if (queryObj != null && queryObj.size() > 0) {
                query = queryObj.toJSONString();
            }
        }
        if (StringUtils.isEmpty(query)) {
            return null;
        } else {
            return query;
        }
    }

    /**
     * 获得菜单路径
     *
     * @param path
     * @return
     */
    private String getPath(String path) {
        if (StringUtils.isNotEmpty(path)) {
            path = path.trim();
            if (!path.startsWith("/")) {
                path = "/" + path;
            }
        }
        return path;
    }

    /**
     * 获得菜单的组件
     *
     * @param component
     * @return
     */
    private String getComponent(String component) {
        if (StringUtils.isNotEmpty(component)) {
            if (component.startsWith("/")) {
                component = component.substring(1);
            }
            int index = component.indexOf("?");
            if (index >= 0) {
                component = component.substring(0, index);
            }
            // 数据源组件需特殊处理
            if (component.startsWith("tds/query")) {
                component = "system/tds/query";
            }
            if (component.startsWith("tds/edit")) {
                component = "system/tds/edit";
            }
            // 报表预览组件
            if (component.startsWith("report/view")) {
                component = "report/base/reportView";
            }
            // 表单数据管理
            if (component.startsWith("tmsf/data-manager")) {
                component = "tmsf/data-manager";
            }
        }
        return component;
    }

    private String getComponent(SysMenu menu) {
        String component = SysMenu.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMeunFrame(menu)) {
            component = getComponent(menu.getComponent());
        } else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu)) {
            // component = SysMenu.PARENT_VIEW;
        }
        return component;
    }

    private boolean isMeunFrame(SysMenu menu) {
        return "0".equals(menu.getPid()) && SysMenu.TYPE_MENU.equals(menu.getMenuType()) && menu.getIsFrame().equals(SysMenu.NO_FRAME);
    }

    private boolean isParentView(SysMenu menu) {
        return "0".equals(menu.getPid()) && SysMenu.TYPE_DIR.equals(menu.getMenuType());
    }

    /**
     * 修改菜单
     *
     * @param menu
     * @return
     */
    public SysMenu updateMenu(SysMenu menu) {
        removeUserFromRedis();// 清除缓存
        String type = "update";
        SysMenu node = this.getMenuNode(menu.getTenant_id(), menu.getId());
        if (node != null && !node.getPid().equals(menu.getPid())) {// 换目
            type = "insert";
        }
        if (SysMenu.TYPE_DIR.equals(menu.getMenuType())) {// 文件夹
            if (isParentView(menu)) {
                menu.setComponent(SysMenu.LAYOUT);// 根文件夹
            } else {
                menu.setComponent(SysMenu.PARENT_VIEW);// 子文件夹
            }
        }
        SysMenu rtnMenu = treeService.saveNode(menu.getTenant_id(), SysMenu.class, menu.getModelId(), this.convertMenu(menu.getTenant_id(), 2, menu));
        this.updateOrder(menu.getTenant_id(), type, rtnMenu.getPid(), rtnMenu.getId());
        redis.delete("SYSTEM:MENU");
        redis.delete("SYSTEM:MENU_INFO");
        redis.delete("SYSTEM:USERPERM_INFO");
        redis.delete("SYSTEM:USERROLE_INFO");
        redis.delete("SYSTEM:USERROLE_INFO");
        redis.delete("SYSTEM:USER_INFO");

        return rtnMenu;

    }

    /**
     * 获得根目录
     *
     * @return
     */
    public SysMenu getMenuRoot() {
        return treeService.getRootTree(SysMenu.class, MODEL_ID);
    }

    public SysMenu getMenuRoot(String tenant_id) {
        return treeService.getRootTree(tenant_id, SysMenu.class, TreeServiceImpl.DEF_CHILDREN_FIELD, MODEL_ID);
    }

    /**
     * 普通用户权限菜单
     *
     * @return
     * <AUTHOR>
     * @params
     */

    @SuppressWarnings("unchecked")
    public SysMenu getMenuByPermiss(String objId) {
        Map<String, Object> mapPage = perUtils.getPerms(SysUserUtil.getCurrentUser().getId(), "M"); // 获取权限有权限的对象;
        List<String> idList = new ArrayList<String>(); // ;
        if (mapPage.containsKey("menus")) {
            idList.addAll((List<String>) mapPage.get("menus"));
        }
        Map<String, Object> mapButton = perUtils.getPerms(SysUserUtil.getCurrentUser().getId(), "B"); // 获取权限有权限的对象;
        if (mapButton.containsKey("perms")) {
            idList.addAll((List<String>) mapButton.get("perms"));
        }

        if (StringUtils.isNotEmpty(objId)) {
            // 如果是人员、角色管理页面打开的
            // 需要查验此对象原本具有但是当前登录人没有的页面权限
            Map<String, Object> mapPage1 = perUtils.getPerms(objId, "M");
            // 权限去重
            List<String> idls = new ArrayList<String>();
            if (mapPage1.containsKey("menus")) {
                idls.addAll((List<String>) mapPage1.get("menus"));
            }
            if (StringUtils.isNotEmpty(idls)) {
                idls = idls.stream().filter(i -> !idList.contains(i)).collect(Collectors.toList());
                if (StringUtils.isNotEmpty(idls)) {
                    idList.addAll(idls);
                }
            }
            Map<String, Object> mapButton1 = perUtils.getPerms(objId, "B");
            List<String> idls1 = new ArrayList<String>();
            if (mapButton1.containsKey("perms")) {
                idls1.addAll((List<String>) mapButton1.get("perms"));
            }
            if (StringUtils.isNotEmpty(idls1)) {
                idls1 = idls1.stream().filter(i -> !idList.contains(i)).collect(Collectors.toList());
                if (StringUtils.isNotEmpty(idls1)) {
                    idList.addAll(idls1);
                }
            }
        }
        SysMenu menuTree = treeService.getRootTree(SysMenu.class, MODEL_ID);
        SysMenu res = this.rebuildMenuBypermiss(menuTree, idList);
        if (res != null) {
            // 过滤无权限菜单
            List<SysMenu> childrenMenu = res.getChildren();
            childrenMenu = childrenMenu.stream().filter(i -> StringUtils.isNotEmpty(i.getChildren())).collect(Collectors.toList());
            res.setChildren(childrenMenu);
        }
        return res;
    }

    /**
     * 按权限重新整理菜单列表
     *
     * @return
     * <AUTHOR>
     * @params
     */
    public SysMenu rebuildMenuBypermiss(SysMenu menuTree, List<String> idList) {
        // 菜单
        if (menuTree == null) {
            return null;
        }
        List<SysMenu> childrenMenu = menuTree.getChildren();
        if (StringUtils.isNotEmpty(childrenMenu)) {
            for (SysMenu menu : childrenMenu) {
                SysMenu sm = this.rebuildMenuBypermiss(menu, idList);
                if (sm == null) {
                    continue;
                }
                // 页面权限
                if ("M".equals(sm.getMenuType())) {
                    String permisString = sm.getPath().replace("/", ":");
                    String permision = permisString.substring(1);
                    if (!idList.contains(sm.getId()) && !idList.contains(permision)) {
                        childrenMenu = childrenMenu.stream().filter(i -> !sm.getId().equals(i.getId()) && "1".equals(i.getVisible())).collect(Collectors.toList());
                    }
//					if(StringUtils.isNotEmpty(menu.getComponent())) {
//						String menuPermis = menu.getComponent().replace("/", ":");
//						if (!permissList.contains(menuPermis)) {
//							childrenMenu = childrenMenu.stream().filter(i -> !menu.getComponent().equals(i.getComponent()) && "1".equals(i.getVisible()) ).collect(Collectors.toList());
//							if (StringUtils.isEmpty(childrenMenu)) {
//								break;
//							}
//						}
//					}
                }
                // 按钮权限
                if ("B".equals(sm.getMenuType())) {
                    if (StringUtils.isNotEmpty(menuTree.getComponent())) {
                        String menuPermis = menuTree.getComponent().replace("/", ":");
                        String permisString = menuTree.getPath().replace("/", ":");
                        String permision = permisString.substring(1);
                        if (StringUtils.isNotEmpty(menuPermis)) {
                            menuPermis = menuPermis + ":" + sm.getPerms();
                            permision = permision + ":" + sm.getPerms();
                            if (!idList.contains(menuPermis) && !idList.contains(permision)) {
                                childrenMenu = childrenMenu.stream().filter(i -> !sm.getPerms().equals(i.getPerms()) && "1".equals(i.getVisible())).collect(Collectors.toList());
                                if (StringUtils.isEmpty(childrenMenu)) {
                                    break;
                                }
                            }
                        }
                    }
                }
                // 分类权限
                if ("D".equals(sm.getMenuType())) {
                    if (StringUtils.isEmpty(sm.getChildren())) {
                        //分类下为空移除此分类
                        childrenMenu = childrenMenu.stream().filter(i -> !i.getId().equals(sm.getId())).collect(Collectors.toList());
                    }
                    childrenMenu = childrenMenu.stream().filter(i -> "1".equals(i.getVisible())).collect(Collectors.toList());
                    if (StringUtils.isEmpty(childrenMenu)) {
                        break;
                    }
                }
            }
            menuTree.setChildren(childrenMenu);
        }
        return menuTree;
    }

    /**
     * 获得单个节点
     *
     * @param id
     * @return
     */
    public SysMenu getMenuNode(String id) {
        return treeService.getNode(SysMenu.class, MODEL_ID, id);
    }

    public SysMenu getMenuNode(String tenant_id, String id) {
        return treeService.getNode(tenant_id, SysMenu.class, MODEL_ID, id);
    }

    /**
     * 添加菜单
     *
     * @param menu
     * @return
     */
    public SysMenu addMenu(SysMenu menu) {
        String tenant_id = menu.getTenant_id();
        SysMenu sysMenu = this.insertNode(tenant_id, menu);
        if (SysMenu.TYPE_MENU.equalsIgnoreCase(menu.getMenuType())) {
            this.addButtons(tenant_id, sysMenu);
        }
        removeUserFromRedis();// 清除缓存
        this.initMenuData(tenant_id);// 加缓存
        return sysMenu;
    }

    /**
     * 添加节点
     *
     * @param menu
     * @return
     */
    private SysMenu insertNode(SysMenu menu) {
        return insertNode(null, menu);
    }

    /**
     * 添加节点
     *
     * @param menu
     * @return
     */
    private SysMenu insertNode(String tenant_id, SysMenu menu) {
        removeUserFromRedis();// 清除缓存
        SysMenu rtnMenu = treeService.append(tenant_id, SysMenu.class, MODEL_ID, menu.getPid(), this.convertMenu(tenant_id, 1, menu));
        this.updateOrder(tenant_id, "insert", rtnMenu.getPid(), rtnMenu.getId());// 更新排序序号
        this.initMenuData(tenant_id);// 加缓存
        return rtnMenu;
    }

    /**
     * 更新排序序号
     *
     * @param tenant_id
     * @param type      添加:insert;
     * @param pid       父id
     * @param nodeId    添加节点id
     * @return
     */
    private boolean updateOrder(String tenant_id, String type, String pid, String nodeId) {
        List<SysMenu> nodes = this.getChildNodes(tenant_id, pid);
        if (StringUtils.isNotEmpty(nodes)) {
            List<SysMenu> upNodes = new ArrayList<SysMenu>();
            int i = 0;
            SysMenu node = null;
            for (SysMenu e : nodes) {
                if ("insert".equals(type)) {
                    if (nodeId.equals(e.getId())) {
                        node = e;
                        continue;
                    }
                }
                i++;
                e.setOrderNum(i);
                upNodes.add(e);
            }
            if (node != null) {
                i++;
                node.setOrderNum(i);
                upNodes.add(node);
            }
            dao.rawUpdateByIdBatch(upNodes);
            this.initMenuData(tenant_id);// 加缓存
        }
        return true;
    }

//	private boolean addButtons(SysMenu menu) {
//		return addButtons(null, menu);
//	}

    /**
     * 同步添加菜单库按钮
     *
     * @param menu
     * @return
     */
    private boolean addButtons(String tenant_id, SysMenu menu) {
        List<SysMenuLib> list = menuLibserv.getButtonLib(tenant_id, menu.getModuleCode(), menu.getPath());// 获取菜单库按钮信息
        if (StringUtils.isNotEmpty(list)) {
            for (SysMenuLib sysMenuLib : list) {
                SysMenu sysMenu = new SysMenu();
                sysMenu = this.convertMenu(tenant_id, 1, lib2button(menu.getId(), sysMenuLib));
                insertNode(sysMenu);
            }
        }
        return false;
    }

    /**
     * 菜单库与系统菜单目录转换
     *
     * @param pid
     * @param libClass
     * @return
     */
    public SysMenu lib2dir(String pid, SysMenuLibClass libClass) {
        SysMenu sysMenu = new SysMenu();
        sysMenu.setModelId(MODEL_ID);
        sysMenu.setId(TMUID.getUID());
        sysMenu.setPid(pid);
        sysMenu.setVisible("1");
        sysMenu.setStatus("0");
        sysMenu.setModuleCode(libClass.getModuleCode());
        sysMenu.setComponent("Layout");
        sysMenu.setIcon("list");
        sysMenu.setPath("/" + sysMenu.getId());
        sysMenu.setIsCache("1");// 不缓存，刷新
        sysMenu.setIsFrame("1");
        sysMenu.setApplyRange("1");
        sysMenu.setShowType("0");
        sysMenu.setMenuName(libClass.getClassName());
        sysMenu.setMenuType(SysMenu.TYPE_DIR);
        return sysMenu;
    }

    /**
     * 菜单库与系统菜单转换
     *
     * @param pid
     * @param btn
     * @return
     */
    public SysMenu lib2menu(String pid, SysMenuLib menu) {
        SysMenu sysMenu = new SysMenu();
        sysMenu.setModelId(MODEL_ID);
        sysMenu.setId(TMUID.getUID());
        sysMenu.setPid(pid);
        sysMenu.setVisible("1");
        sysMenu.setStatus("0");
        sysMenu.setModuleCode(menu.getModuleCode());
        sysMenu.setIcon(StringUtils.isEmpty(menu.getIcon()) ? "build" : menu.getIcon());
        sysMenu.setIsCache("1");
        sysMenu.setIsFrame("1");
        sysMenu.setApplyRange(StringUtils.isEmpty(menu.getApplyRange()) ? "1" : menu.getApplyRange());
        sysMenu.setShowType("0");
        sysMenu.setMenuName(menu.getMenuName());
        sysMenu.setMenuType(SysMenu.TYPE_MENU);
        sysMenu.setPerms(menu.getPerms());
        sysMenu.setQuery(menu.getQuery());
        sysMenu.setPath(this.getPath(menu.getPath()));
        sysMenu.setComponent(this.getComponent(sysMenu.getPath()));

        sysMenu.setApp_menuName(menu.getApp_menuName());
        sysMenu.setApp_path(this.getPath(menu.getApp_path()));
        sysMenu.setApp_query(menu.getQuery());
        sysMenu.setApp_icon(menu.getApp_icon());

//		sysMenu.setSynType(menu.getSynType());
        return sysMenu;
    }

    /**
     * 菜单库与系统菜单按钮转换
     *
     * @param pid
     * @param btn
     * @return
     */
    public SysMenu lib2button(String pid, SysMenuLib btn) {
        SysMenu sysMenu = new SysMenu();
        sysMenu.setModelId(MODEL_ID);
        sysMenu.setId(TMUID.getUID());
        sysMenu.setPid(pid);
        sysMenu.setVisible("1");
        sysMenu.setStatus("0");
        sysMenu.setModuleCode(btn.getModuleCode());
        sysMenu.setPerms(btn.getPerms());
        sysMenu.setMenuType(btn.getMenuType());
        sysMenu.setMenuName(btn.getMenuName());
        sysMenu.setIcon("");
        sysMenu.setIsCache("1");
        sysMenu.setIsFrame("1");
        sysMenu.setApplyRange("1");
        sysMenu.setShowType("0");
        return sysMenu;
    }

//	private SysMenu convertMenu(int type, SysMenu menu) {
//		return convertMenu(null, type, menu);
//	}

    /**
     * 菜单数据转换
     *
     * @param type 1:添加；2:修改
     * @param menu
     * @return
     */
    private SysMenu convertMenu(String tenant_id, int type, SysMenu menu) {
        if (menu == null) {
            return null;
        }
        if (SysMenu.TYPE_DIR.equalsIgnoreCase(menu.getMenuType())) {// 目录
            String pid = "";
            String rootId = "";
            if (type == 1) {// 添加
                SysMenu pmenu = null;
                if (menu.getPid() == null || "".equals(menu.getPid().trim())) {
                    pmenu = this.getMenuRoot();
                } else {
                    pmenu = treeService.getNode(tenant_id, SysMenu.class, MODEL_ID, menu.getPid());
                }
                menu.setPid(pmenu.getId());
                menu.setPath("/" + TMUID.getUID());
                pid = pmenu.getPid();
                rootId = ROOT_ID;
            } else {// 修改
                pid = menu.getPid();
                SysMenu rootMenu = this.getRootNode(tenant_id);
                rootId = rootMenu.getId();
            }
            if (rootId.equalsIgnoreCase(pid)) {
                menu.setComponent("Layout");
            } else {
                menu.setComponent("ParentView");
            }
        } else if (SysMenu.TYPE_MENU.equalsIgnoreCase(menu.getMenuType())) {// 菜单
            String path = menu.getPath();
            if (StringUtils.isNotEmpty(path)) {
                path = path.trim();
                if (path.toLowerCase().startsWith("tm3://")) {
                    menu.setIsFrame("0");
                }
            }
            if ("1".equals(menu.getIsFrame())) {// 内部页面
                menu.setPath(this.getPath(path));
                menu.setComponent(this.getComponent(menu.getPath()));
                if (type == 1) {
                    if (StringUtils.isEmpty(menu.getModuleCode())) {
                        menu.setModuleCode(this.getModuleCode(menu));
                    }
                } else {
                    String s = this.getModuleCode(menu);
                    if (StringUtils.isNotEmpty(s)) {
                        menu.setModuleCode(s);
                    }
                }
            }
        } else if (SysMenu.TYPE_BUTTON.equalsIgnoreCase(menu.getMenuType())) {// 按钮
            menu.setPath(null);
            menu.setComponent(null);
        }
        return menu;
    }

    /**
     * 获取默认模块代码
     *
     * @param menu
     * @return
     */
    private String getModuleCode(SysMenu menu) {
        String code = "";
        if (StringUtils.isNotEmpty(menu.getComponent())) {
            int index = menu.getComponent().indexOf("/");
            if (index > 0) {
                code = menu.getComponent().substring(0, index);
            }
        }
        return code;
    }

    /**
     * 删除菜单
     *
     * @param id
     * @return
     */
    public int[] deleteMenu(String id) {
        return deleteMenu(null, id);
    }

    /**
     * 删除菜单
     *
     * @param id
     * @return
     */
    public int[] deleteMenu(String tenant_id, String id) {
        removeUserFromRedis();// 清除缓存
        int[] rtn = new int[3];
        rtn = treeService.delete(tenant_id, SysMenu.class, false, MODEL_ID, id);// 真删除
        if (rtn[0] == 1) {// 删除成功
            iSysRolePermServ.deleteByPermId(tenant_id, id);// 删除角色权限
            iSysUserPermServ.deleteByPermId(tenant_id, id);// 删除个人权限
            this.initMenuData(tenant_id);// 加缓存
        }
        return rtn;
    }

    /**
     * 获取菜单list
     *
     * @return
     */
    public List<SysMenu> getMenuList() {
        return this.getMenuList(true, true, true, false);
    }

    public List<SysMenu> getMenuList(boolean hasRoot, boolean hasDir, boolean hasButton, boolean showVis) {
        return getMenuList(null, hasRoot, hasDir, hasButton, showVis);
    }

    /**
     * 获取菜单
     *
     * @param hasRoot   true:显示根节点
     * @param hasDir    true:显示目录节点
     * @param hasButton true:显示按钮节点
     * @param showVis   true:显示隐藏节点
     * @return
     */
    public List<SysMenu> getMenuList(String tenant_id, boolean hasRoot, boolean hasDir, boolean hasButton, boolean showVis) {
        Where where = Where.create();
        where.eq(SysMenu::getModelId, MODEL_ID);
        where.eq(SysMenu::getDelFlag, 0);
        if (!hasRoot) {// 不显示根节点
            where.ne(SysMenu::getMenuType, SysMenu.TYPE_ROOT);
        }
        if (!hasDir) {// 不显示目录
            where.ne(SysMenu::getMenuType, SysMenu.TYPE_DIR);
        }
        if (!hasButton) {// 不显示按钮节点
            where.ne(SysMenu::getMenuType, SysMenu.TYPE_BUTTON);
        }
        if (!showVis) {// 显示隐藏节点
            where.eq(SysMenu::getVisible, "1");
            where.eq(SysMenu::getStatus, "0");
        }
        Order order = Order.create();
        order.orderByDesc(SysMenu::getMenuType);
        order.orderByAsc(SysMenu::getOrderNum);
        order.orderByAsc(SysMenu::getLft);// 一级菜单这个排序不起作用，通过list重新排序排序
        List<SysMenu> list = null;
        if (StringUtils.isNotEmpty(tenant_id)) {
            // 按传入指定租户id查询
            list = dao.rawQueryListByWhereWithTenant(tenant_id, SysMenu.class, where, order);
        } else {
            // 正常按登录租户查询
            list = dao.rawQueryListByWhere(SysMenu.class, where, order);
        }
        if (StringUtils.isNotEmpty(list)) {
            int i = 0;
            for (SysMenu sysMenu : list) {
                i++;
                sysMenu.setOrderNum(i);
            }
        }
        return list;
    }

    /**
     * 获取菜单map
     *
     * @return
     */
    private Map<String, SysMenu> getMenuMap(List<SysMenu> list) {
        Map<String, SysMenu> map = new HashMap<String, SysMenu>();
        if (StringUtils.isNotEmpty(list)) {
            for (SysMenu sysMenu : list) {
                map.put(sysMenu.getId(), sysMenu);
            }
        }
        return map;
    }

    public Map<String, SysMenu> getMenuMap() {
        return this.getMenuMap(this.getMenuList());
    }

    /**
     * 从内存中获取菜单信息
     *
     * @return
     */
    public Map<String, Map<String, Object>> getMenuMapFromRedis(String tenant_id) {
        String key = this.getMenuInfoKey(tenant_id);
        Map<String, Map<String, Object>> map = redis.getMap(key);
        if (map == null) {
            List<SysMenu> list = this.getMenuList(tenant_id, true, true, true, false);
            if (StringUtils.isNotEmpty(list)) {
                map = new HashMap<String, Map<String, Object>>();
                for (SysMenu sysMenu : list) {
                    Map<String, Object> mp = ObjUtils.convertToMap(sysMenu);
                    mp.put("id", sysMenu.getId());
                    mp.put("pid", sysMenu.getPid());
                    mp.put("modelId", sysMenu.getModelId());
                    if (sysMenu.getRoleLevel() != null) {
                        mp.put("roleLevel", sysMenu.getRoleLevel());
                    }
                    map.put(sysMenu.getId(), mp);
                }
                redis.setMap(key, map);
            }
        }
        return map;
    }

    /**
     * 重新加载菜单
     */
    public void initMenuData(String tenant_id) {
        String key = this.getMenuInfoKey(tenant_id);
        redis.delete(key);
        this.getMenuMapFromRedis(tenant_id);
    }

    /**
     * 获得菜单树for权限资源选择
     *
     * @param menu
     * @return
     */
    public SysMenu getPermMenuTree(SysMenu menu) {
        if (menu != null) {
            this.rebuild(menu.getChildren());
        }
        return menu;
    }

    /**
     * 重新构造资源树
     *
     * @param list
     */
    private void rebuild(List<SysMenu> list) {
        if (StringUtils.isEmpty(list)) {
            return;
        }
        Iterator<SysMenu> iter = list.iterator();
        while (iter.hasNext()) {
            SysMenu sysMenu = iter.next();
            if ("0".equals(sysMenu.getVisible()) || "1".equals(sysMenu.getStatus())) {// 隐藏或停用的菜单
                iter.remove();
            } else {
                if (SysMenu.TYPE_MENU.equals(sysMenu.getMenuType())) {// 菜单
                    if (StringUtils.isNotEmpty(sysMenu.getChildren())) {
                        SysMenu menu = new SysMenu();
                        menu.setId(sysMenu.getId());
                        menu.setMenuType(SysMenu.TYPE_BUTTON);
                        //menu.setMenuName(sysMenu.getMenuName() + "-访问权限");
                        //menu.setMenuName("页面访问");
                        menu.setMenuName("显示[" + sysMenu.getMenuName() + "]菜单");
                        if (sysMenu.getChildren() == null) {
                            sysMenu.setChildren(new ArrayList<>());
                        }
                        sysMenu.getChildren().add(0, menu);
                    }
                }
                sysMenu.setId(sysMenu.getMenuType() + "_" + sysMenu.getId());
                this.rebuild(sysMenu.getChildren());
            }
        }
    }

    /**
     * 获得下拉框选择的树形菜单
     *
     * @return
     */
    public List<SysMenu> getSelectMenu() {
        return getSelectMenu(null);
    }

    /**
     * 获得下拉框选择的树形菜单
     *
     * @return
     */
    public List<SysMenu> getSelectMenu(String tenant_id) {
        SysMenu menuRoot = treeService.getRootTree(tenant_id, SysMenu.class, MODEL_ID, this.getMenuList(tenant_id, true, true, false, false));
        List<SysMenu> menus = new ArrayList<SysMenu>();
        menus.add(menuRoot);
        return menus;
    }

    /**
     * 同级节点移动
     *
     * @param posNodeId 位置节点
     * @param desNodeId 目标节点
     * @param before    true 之前 false 之后
     */
    public void move(String posNodeId, String desNodeId, boolean before) {
        move(null, posNodeId, desNodeId, before);
    }

    /**
     * 同级节点移动
     *
     * @param posNodeId 位置节点
     * @param desNodeId 目标节点
     * @param before    true 之前 false 之后
     */
    public void move(String tenant_id, String posNodeId, String desNodeId, boolean before) {
        removeUserFromRedis();// 清除缓存
        treeService.move(tenant_id, SysMenu.class, MODEL_ID, posNodeId, desNodeId, before, "orderNum");
        // 移动后所以不对，需重置
        try {
            treeService.correctTree(SysMenu.class, MODEL_ID);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        this.initMenuData(tenant_id);// 加缓存
    }

    public List<SysMenu> getMenuListByPath(List<String> paths) {
        return dao.queryList(SysMenu.class, Where.create().in(SysMenu::getPath, paths.toArray()));
    }

    /**
     * 菜单初始化
     *
     * @param iAdmin true：超级管理员
     * @return
     */
    public List<SysMenu> initMenu(boolean isAdmin) {

        List<SysMenu> list = new ArrayList<SysMenu>();
        SysMenu root = this.getRootNode();
        int lft = 0;
        if (root == null) {// 根目录
            lft++;
            root = new SysMenu();
            root.setId(TMUID.getUID());
            root.setDelFlag(0);
            root.setLft(lft);
            root.setRgt(17);
            root.setModelId(MODEL_ID);
            root.setPid(ROOT_ID);
            root.setIcon("#");
            root.setIsCache("1");
            root.setIsFrame("1");
            root.setApplyRange("1");
            root.setShowType("0");
            root.setMenuName("根菜单");
            root.setMenuType(SysMenu.TYPE_ROOT);
            root.setStatus("0");
            root.setVisible("1");
            root.setPath("/root");
            list.add(root);

            // 系统设置
            lft++;
            SysMenu menu1 = new SysMenu();
            menu1.setModuleCode("system");// 模块编码
            menu1.setId(TMUID.getUID());
            menu1.setDelFlag(0);
            menu1.setLft(lft);
            menu1.setRgt(16);
            menu1.setModelId(MODEL_ID);
            menu1.setPid(root.getId());
            menu1.setIcon("system");
            menu1.setIsCache("1");
            menu1.setIsFrame("1");
            menu1.setApplyRange("1");
            menu1.setShowType("0");
            menu1.setMenuName("系统设置");
            menu1.setMenuType(SysMenu.TYPE_DIR);
            menu1.setStatus("0");
            menu1.setVisible("1");
            menu1.setPath("/" + menu1.getId());
            list.add(menu1);

            // 系统参数
            if (isAdmin) {
                lft++;
                SysMenu menu11 = new SysMenu();
                menu11.setModuleCode("system");// 模块编码
                menu11.setId(TMUID.getUID());
                menu11.setDelFlag(0);
                menu11.setLft(lft);
                menu11.setRgt(15);
                menu11.setModelId(MODEL_ID);
                menu11.setPid(menu1.getId());

                menu11.setIcon("server");
                menu11.setComponent("system/config/index");
                menu11.setPath("/system/config/index");

                menu11.setIsCache("1");
                menu11.setIsFrame("1");
                menu11.setApplyRange("1");
                menu11.setShowType("0");
                menu11.setMenuName("系统参数");
                menu11.setMenuType(SysMenu.TYPE_MENU);
                menu11.setStatus("0");
                menu11.setVisible("1");
                list.add(menu11);
            }

            // 人员管理
            lft++;
            SysMenu menu0 = new SysMenu();
            menu0.setModuleCode("system");// 模块编码
            menu0.setId(TMUID.getUID());
            menu0.setDelFlag(0);
            menu0.setLft(lft);
            menu0.setRgt(14);
            menu0.setModelId(MODEL_ID);
            menu0.setPid(menu1.getId());

            menu0.setIcon("user");
            menu0.setComponent("system/employee/index");
            menu0.setPath("/system/employee/index");

            menu0.setIsCache("1");
            menu0.setIsFrame("1");
            menu0.setApplyRange("1");
            menu0.setShowType("0");
            menu0.setMenuName("人员管理");
            menu0.setMenuType(SysMenu.TYPE_MENU);
            menu0.setStatus("0");
            menu0.setVisible("1");
            list.add(menu0);

            // 菜单设置
            lft++;
            SysMenu menu2 = new SysMenu();
            menu2.setModuleCode("system");// 模块编码
            menu2.setId(TMUID.getUID());

            menu2.setDelFlag(0);
            menu2.setLft(lft);
            menu2.setRgt(13);
            menu2.setModelId(MODEL_ID);
            menu2.setPid(menu1.getId());

            menu2.setIcon("table");
            menu2.setComponent("system/menu/index");
            menu2.setPath("/system/menu/index");

            menu2.setIsCache("1");
            menu2.setIsFrame("1");
            menu2.setApplyRange("1");
            menu2.setShowType("0");
            menu2.setMenuName("菜单管理");
            menu2.setMenuType(SysMenu.TYPE_MENU);
            menu2.setStatus("0");
            menu2.setVisible("1");
            list.add(menu2);

            if (isAdmin) {
                // 字典
                lft++;
                SysMenu menu3 = new SysMenu();
                menu3.setId(TMUID.getUID());
                menu3.setModuleCode("system");// 模块编码
                menu3.setDelFlag(0);
                menu3.setLft(lft);
                menu3.setRgt(12);
                menu3.setModelId(MODEL_ID);
                menu3.setPid(menu1.getId());

                menu3.setIcon("dict");
                menu3.setComponent("system/dict/index");
                menu3.setPath("/system/dict/index");

                menu3.setIsCache("1");
                menu3.setIsFrame("1");
                menu3.setApplyRange("1");
                menu3.setShowType("0");
                menu3.setMenuName("系统字典");
                menu3.setMenuType(SysMenu.TYPE_MENU);
                menu3.setStatus("0");
                menu3.setVisible("1");
                list.add(menu3);
            }

            // 角色设置
            lft++;
            SysMenu menu4 = new SysMenu();
            menu4.setModuleCode("system");// 模块编码
            menu4.setId(TMUID.getUID());
            menu4.setDelFlag(0);
            menu4.setLft(lft);
            menu4.setRgt(11);
            menu4.setModelId(MODEL_ID);
            menu4.setPid(menu1.getId());

            menu4.setIcon("peoples");
            menu4.setComponent("system/role/index");
            menu4.setPath("/system/role/index");

            menu4.setIsCache("1");
            menu4.setIsFrame("1");
            menu4.setApplyRange("1");
            menu4.setShowType("0");
            menu4.setMenuName("角色设置");
            menu4.setMenuType(SysMenu.TYPE_MENU);
            menu4.setStatus("0");
            menu4.setVisible("1");
            list.add(menu4);

            // 机构设置
            lft++;
            SysMenu menu5 = new SysMenu();
            menu5.setModuleCode("system");// 模块编码
            menu5.setId(TMUID.getUID());
            menu5.setDelFlag(0);
            menu5.setLft(lft);
            menu5.setRgt(10);
            menu5.setModelId(MODEL_ID);
            menu5.setPid(menu1.getId());

            menu5.setIcon("tree");
            menu5.setComponent("system/org/index");
            menu5.setPath("/system/org/index");

            menu5.setIsCache("1");
            menu5.setIsFrame("1");
            menu5.setApplyRange("1");
            menu5.setShowType("0");
            menu5.setMenuName("机构设置");
            menu5.setMenuType(SysMenu.TYPE_MENU);
            menu5.setStatus("0");
            menu5.setVisible("1");
            list.add(menu5);

            // 岗位设置
            lft++;
            SysMenu menu6 = new SysMenu();
            menu6.setModuleCode("system");// 模块编码
            menu6.setId(TMUID.getUID());

            menu6.setDelFlag(0);
            menu6.setLft(lft);
            menu6.setRgt(9);
            menu6.setModelId(MODEL_ID);
            menu6.setPid(menu1.getId());

            menu6.setIcon("post");
            menu6.setComponent("system/post/index");
            menu6.setPath("/system/post/index");

            menu6.setIsCache("1");
            menu6.setIsFrame("1");
            menu6.setApplyRange("1");
            menu6.setShowType("0");
            menu6.setMenuName("岗位设置");
            menu6.setMenuType(SysMenu.TYPE_MENU);
            menu6.setStatus("0");
            menu6.setVisible("1");
            list.add(menu6);
            if (isAdmin) {
                // 数据源设置
                lft++;
                SysMenu menu7 = new SysMenu();
                menu7.setModuleCode("system");// 模块编码
                menu7.setId(TMUID.getUID());

                menu7.setDelFlag(0);
                menu7.setLft(lft);
                menu7.setRgt(8);
                menu7.setModelId(MODEL_ID);
                menu7.setPid(menu1.getId());

                menu7.setIcon("code");
                menu7.setComponent("system/tds/setTds");
                menu7.setPath("/system/tds/setTds");

                menu7.setIsCache("1");
                menu7.setIsFrame("1");
                menu7.setApplyRange("1");
                menu7.setShowType("0");
                menu7.setMenuName("数据源设置");
                menu7.setMenuType(SysMenu.TYPE_MENU);
                menu7.setStatus("0");
                menu7.setVisible("1");
                list.add(menu7);
            }

            if (MultiTenantUtils.enalbe()) {
                if (isAdmin) {// 租户菜单
                    lft++;
                    SysMenu menu71 = new SysMenu();
                    menu71.setModuleCode("system");// 模块编码
                    menu71.setId(TMUID.getUID());

                    menu71.setDelFlag(0);
                    menu71.setLft(lft);
                    menu71.setRgt(8);
                    menu71.setModelId(MODEL_ID);
                    menu71.setPid(menu1.getId());

                    menu71.setIcon("build");
                    menu71.setComponent("system/menu/tenantMenuConfigDefault");
                    menu71.setPath("/system/menu/tenantMenuConfigDefault");

                    menu71.setIsCache("1");
                    menu71.setIsFrame("1");
                    menu71.setApplyRange("1");
                    menu71.setShowType("0");
                    menu71.setMenuName("租户默认菜单");
                    menu71.setMenuType(SysMenu.TYPE_MENU);
                    menu71.setStatus("0");
                    menu71.setVisible("1");
                    list.add(menu71);

                    lft++;
                    SysMenu menu72 = new SysMenu();
                    menu72.setModuleCode("system");// 模块编码
                    menu72.setId(TMUID.getUID());

                    menu72.setDelFlag(0);
                    menu72.setLft(lft);
                    menu72.setRgt(8);
                    menu72.setModelId(MODEL_ID);
                    menu72.setPid(menu1.getId());

                    menu72.setIcon("build");
                    menu72.setComponent("system/menu/tenantMenuConfig");
                    menu72.setPath("/system/menu/tenantMenuConfig");

                    menu72.setIsCache("1");
                    menu72.setIsFrame("1");
                    menu72.setApplyRange("1");
                    menu72.setShowType("0");
                    menu72.setMenuName("租户菜单管理");
                    menu72.setMenuType(SysMenu.TYPE_MENU);
                    menu72.setStatus("0");
                    menu72.setVisible("1");
                    list.add(menu72);

                }

            }

            if (isAdmin) {
                // 系统管理
                SysMenu menuFl1 = new SysMenu();
                menuFl1.setModuleCode("system");// 模块编码
                menuFl1.setId(TMUID.getUID());

                menuFl1.setDelFlag(0);
                menuFl1.setLft(lft);
                menuFl1.setRgt(20);
                menuFl1.setModelId(MODEL_ID);
                menuFl1.setPid(root.getId());
                menuFl1.setIcon("monitor");
                menuFl1.setIsCache("1");
                menuFl1.setIsFrame("1");
                menuFl1.setApplyRange("1");
                menuFl1.setShowType("0");
                menuFl1.setMenuName("系统管理");
                menuFl1.setMenuType(SysMenu.TYPE_DIR);
                menuFl1.setStatus("0");
                menuFl1.setVisible("1");
                menuFl1.setPath("/" + menuFl1.getId());
                list.add(menuFl1);

                // redis管理

                lft++;
                SysMenu menu8 = new SysMenu();
                menu8.setModuleCode("system");// 模块编码
                menu8.setId(TMUID.getUID());

                menu8.setDelFlag(0);
                menu8.setLft(lft);
                menu8.setRgt(10);
                menu8.setModelId(MODEL_ID);
                menu8.setPid(menuFl1.getId());

                menu8.setIcon("redis");
                menu8.setComponent("system/tools/redisManager");
                menu8.setPath("/system/tools/redisManager");

                menu8.setIsCache("1");
                menu8.setIsFrame("1");
                menu8.setApplyRange("1");
                menu8.setShowType("0");
                menu8.setMenuName("Redis管理");
                menu8.setMenuType(SysMenu.TYPE_MENU);
                menu8.setStatus("0");
                menu8.setVisible("1");
                list.add(menu8);
            }

            int sort = 0;
            for (SysMenu sysMenu : list) {
                sysMenu.setOrderNum(sort++);
            }
            int r = this.insertMenus(list);
            if (r <= 0) { // 添加失败
                return new ArrayList<SysMenu>();
            } else {
                // this.correctTree();
                log.info("*****初始化菜单成功***********");
                return list;
            }
        } else {
            list.add(root);
            return list;
        }

    }

    /**
     * 多租户菜单初始化
     *
     * @param iAdmin true：超级管理员
     * @return
     */
    public List<SysMenu> initMenuTenant(boolean isAdmin, String tenantId) {

        List<SysMenu> list = new ArrayList<SysMenu>();
//		SysMenu root = this.getRootNodeTenant(tenantId);
        SysMenu root = this.getRootNode(tenantId);
        int lft = 0;
        if (root == null) {// 根目录
            lft++;
            root = new SysMenu();
            root.setId(TMUID.getUID());
            root.setDelFlag(0);
            root.setLft(lft);
            root.setRgt(17);
            root.setModelId(MODEL_ID);
            root.setPid(ROOT_ID);
            root.setIcon("#");
            root.setIsCache("1");
            root.setIsFrame("1");
            root.setApplyRange("1");
            root.setShowType("0");
            root.setMenuName("根菜单");
            root.setMenuType(SysMenu.TYPE_ROOT);
            root.setStatus("0");
            root.setVisible("1");
            root.setPath("/root");
//			root.setTenant_id(tenantId);
            list.add(root);
        } else {
            list.add(root);
            return list;
        }

        // 系统设置
        lft++;
        SysMenu menu1 = new SysMenu();
        menu1.setModuleCode("system");// 模块编码
        menu1.setId(TMUID.getUID());
        menu1.setDelFlag(0);
        menu1.setLft(lft);
        menu1.setRgt(16);
        menu1.setModelId(MODEL_ID);
        menu1.setPid(root.getId());
        menu1.setIcon("monitor");
        menu1.setIsCache("1");
        menu1.setIsFrame("1");
        menu1.setApplyRange("1");
        menu1.setShowType("0");
        menu1.setMenuName("系统设置");
        menu1.setMenuType(SysMenu.TYPE_DIR);
        menu1.setStatus("0");
        menu1.setVisible("1");
        menu1.setPath("/" + menu1.getId());
//		menu1.setTenant_id(tenantId);
        list.add(menu1);

        // 人员管理
        lft++;
        SysMenu menu0 = new SysMenu();
        menu0.setModuleCode("system");// 模块编码
        menu0.setId("sysEmployee_" + tenantId);
        menu0.setDelFlag(0);
        menu0.setLft(lft);
        menu0.setRgt(14);
        menu0.setModelId(MODEL_ID);
        menu0.setPid(menu1.getId());

        menu0.setIcon("user");
        menu0.setComponent("system/employee/index");
        menu0.setPath("/system/employee/index");

        menu0.setIsCache("1");
        menu0.setIsFrame("1");
        menu0.setApplyRange("1");
        menu0.setShowType("0");
        menu0.setMenuName("人员管理");
        menu0.setMenuType(SysMenu.TYPE_MENU);
        menu0.setStatus("0");
        menu0.setVisible("1");
//		menu0.setTenant_id(tenantId);
        list.add(menu0);

        // 菜单设置
        lft++;
        SysMenu menu2 = new SysMenu();
        menu2.setModuleCode("system");// 模块编码
        menu2.setId("sysMenu_" + tenantId);
        menu2.setDelFlag(0);
        menu2.setLft(lft);
        menu2.setRgt(13);
        menu2.setModelId(MODEL_ID);
        menu2.setPid(menu1.getId());

        menu2.setIcon("table");
        menu2.setComponent("system/menu/index");
        menu2.setPath("/system/menu/index");

        menu2.setIsCache("1");
        menu2.setIsFrame("1");
        menu2.setApplyRange("1");
        menu2.setShowType("0");
        menu2.setMenuName("菜单管理");
        menu2.setMenuType(SysMenu.TYPE_MENU);
        menu2.setStatus("0");
        menu2.setVisible("1");
//		menu2.setTenant_id(tenantId);
        list.add(menu2);
        // 角色设置
        lft++;
        SysMenu menu4 = new SysMenu();
        menu4.setModuleCode("system");// 模块编码
        menu4.setId("sysRole_" + tenantId);
        menu4.setDelFlag(0);
        menu4.setLft(lft);
        menu4.setRgt(11);
        menu4.setModelId(MODEL_ID);
        menu4.setPid(menu1.getId());

        menu4.setIcon("peoples");
        menu4.setComponent("system/role/index");
        menu4.setPath("/system/role/index");

        menu4.setIsCache("1");
        menu4.setIsFrame("1");
        menu4.setApplyRange("1");
        menu4.setShowType("0");
        menu4.setMenuName("角色设置");
        menu4.setMenuType(SysMenu.TYPE_MENU);
        menu4.setStatus("0");
        menu4.setVisible("1");
//		menu4.setTenant_id(tenantId);
        list.add(menu4);

        // 机构设置
        lft++;
        SysMenu menu5 = new SysMenu();
        menu5.setModuleCode("system");// 模块编码
        menu5.setId("sysOrg_" + tenantId);
        menu5.setDelFlag(0);
        menu5.setLft(lft);
        menu5.setRgt(10);
        menu5.setModelId(MODEL_ID);
        menu5.setPid(menu1.getId());

        menu5.setIcon("tree");
        menu5.setComponent("system/org/index");
        menu5.setPath("/system/org/index");

        menu5.setIsCache("1");
        menu5.setIsFrame("1");
        menu5.setApplyRange("1");
        menu5.setShowType("0");
        menu5.setMenuName("机构设置");
        menu5.setMenuType(SysMenu.TYPE_MENU);
        menu5.setStatus("0");
        menu5.setVisible("1");
//		menu5.setTenant_id(tenantId);
        list.add(menu5);

        // 岗位设置
        lft++;
        SysMenu menu6 = new SysMenu();
        menu6.setModuleCode("system");// 模块编码
        menu6.setId("sysPost_" + tenantId);

        menu6.setDelFlag(0);
        menu6.setLft(lft);
        menu6.setRgt(9);
        menu6.setModelId(MODEL_ID);
        menu6.setPid(menu1.getId());

        menu6.setIcon("post");
        menu6.setComponent("system/post/index");
        menu6.setPath("/system/post/index");

        menu6.setIsCache("1");
        menu6.setIsFrame("1");
        menu6.setApplyRange("1");
        menu6.setShowType("0");
        menu6.setMenuName("岗位设置");
        menu6.setMenuType(SysMenu.TYPE_MENU);
        menu6.setStatus("0");
        menu6.setVisible("1");
//		menu6.setTenant_id(tenantId);
        list.add(menu6);
        int r = 0;
        r = dao.rawInsertWithTenant(tenantId, list);// ====多租户====
        if (r <= 0) { // 添加失败
            return new ArrayList<SysMenu>();
        } else {
            log.info("*****初始化菜单成功***********");
            return list;
        }

    }

    /**
     * 菜单初始化
     *
     * @return
     */
    public List<SysMenu> initMenu() {
        return initMenu(true);
    }

    /**
     * 根据目录名称获取目录名
     *
     * @param name
     * @return
     */
    public SysMenu getMenuDirByName(String name) {
        return getMenuDirByName(name, null);
    }

    /**
     * 根据目录名称获取目录名
     *
     * @param name
     * @return
     */
    public SysMenu getMenuDirByName(String name, String tenant_id) {
        Where where = Where.create();
        where.eq(SysMenu::getModelId, MODEL_ID);
        where.eq(SysMenu::getMenuName, name);
        where.eq(SysMenu::getMenuType, SysMenu.TYPE_DIR);
        where.eq(SysMenu::getDelFlag, 0);
        where.eq(SysMenu::getVisible, 1);
        // select * from SYS_MENU where MODEL_ID='SYS_MENU' and MENU_NAME='目标传导系统管理' and
        // MENU_TYPE='D' and DEL_FLAG=0 and VISIBLE=1
        List<SysMenu> list = null;
        if (StringUtils.isNotEmpty(tenant_id)) {
            list = dao.rawQueryListByWhereWithTenant(tenant_id, SysMenu.class, where, null);
        } else {
            list = dao.queryList(SysMenu.class, where);
        }
        if (StringUtils.isNotEmpty(list)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    /**
     * 根据id获取子菜单
     *
     * @param pid
     * @return
     */
    public List<SysMenu> getChildNodes(String pid) {
        return getChildNodes(null, pid);
    }

    /**
     * 根据id获取子菜单
     *
     * @param pid
     * @return
     */
    public List<SysMenu> getChildNodes(String tenant_id, String pid) {

        Where where = Where.create();
        where.eq(SysMenu::getModelId, MODEL_ID);
        where.eq(SysMenu::getPid, pid);
        where.eq(SysMenu::getDelFlag, 0);
        Order order = new Order();
        order.orderByAsc(SysMenu::getOrderNum);
        order.orderByAsc(SysMenu::getLft);
        List<SysMenu> list = null;
        if (StringUtils.isNotEmpty(tenant_id)) {
            list = dao.queryDataDisableTenant(SysMenu.class, where, order, null);
        } else {
            list = dao.queryData(SysMenu.class, where, order, null);
        }
        // treeService.getChildNodes(SysMenu.class, MODEL_ID, pid);
        if (StringUtils.isNotEmpty(list)) {
            int sort = 0;
            for (SysMenu sysMenu : list) {
                sysMenu.setOrderNum(sort++);
            }
        }
        return list;

        // return treeService.getChildNodes(tenant_id, SysMenu.class, MODEL_ID, pid);
    }

    /**
     * 根据路径获取菜单信息
     *
     * @param path
     * @return
     */
    public List<SysMenu> getMenuListByPath(String path) {
        List<SysMenu> list = new ArrayList<SysMenu>();
        if (StringUtils.isNotEmpty(path)) {
            Where where = Where.create();
            where.eq(SysMenu::getPath, path);
            Order order = Order.create();
            order.orderByAsc(SysMenu::getId);
            List<SysMenu> queryList = dao.rawQueryListByWhere(SysMenu.class, where, order);
            if (StringUtils.isNotEmpty(queryList)) {
                list = queryList;
            }
        }
        return list;
    }

    /**
     * 获取非普通同步菜单的默认分类节点
     *
     * @param synIds    同步id
     * @param synType   同步类型 1位主题菜单
     * @param tenant_id 租户id
     * @return SysMenu
     * @category <AUTHOR>
     */
    public SysMenu getSynClass(String tenant_id, String synId, Integer synType) {
        SysMenu result = null;
        if (StringUtils.isNotEmpty(synId)) {
            List<String> synIdList = new ArrayList<String>();
            synIdList.add(synId);
            List<SysMenu> queryList = this.getSynClass(tenant_id, synIdList, synType);
            if (StringUtils.isNotEmpty(queryList)) {
                result = queryList.get(0);
            }
        }
        return result;
    }

    /**
     * 获取非普通同步菜单的默认分类节点
     *
     * @param synIdList 同步id列表
     * @param synType   同步类型 1位主题菜单
     * @param tenant_id 租户id
     * @return SysMenu
     * @category <AUTHOR>
     */
    public List<SysMenu> getSynClass(String tenant_id, List<String> synIdList, Integer synType) {
        List<SysMenu> result = new ArrayList<SysMenu>();
        if (StringUtils.isNotEmpty(synIdList)) {
            Where where = Where.create();
            where.eq(SysMenu::getModelId, MODEL_ID);
            where.eq(SysMenu::getSynType, synType);
            where.eq(SysMenu::getMenuType, SysMenu.TYPE_DIR);
            where.eq(SysMenu::getDelFlag, 0);
            where.eq(SysMenu::getVisible, 1);
            if (synIdList.size() == 1) {
                where.eq(SysMenu::getSynId, synIdList.get(0));
            } else {
                where.in(SysMenu::getSynId, synIdList.toArray());
            }
            List<SysMenu> list = null;
            if (StringUtils.isNotEmpty(tenant_id)) {
                list = dao.rawQueryListByWhereWithTenant(tenant_id, SysMenu.class, where, null);
            } else {
                list = dao.queryList(SysMenu.class, where);
            }
            if (StringUtils.isNotEmpty(list)) {
                result.addAll(list);
            }
        }
        return result;
    }

    /**
     * 获得最大序号
     *
     * @return
     */
    public int getMaxOrderNumber() {
        Where where = Where.create();
        where.eq(SysMenu::getDelFlag, 0);
        Integer sort = dao.findMaxValue(SysMenu.class, SysMenu::getOrderNum, Integer.class, where);
        if (sort == null) {
            return 0;
        } else {
            return sort;
        }
    }

//	/**
//	 * 更新
//	 * @category 
//	 * <AUTHOR> 
//	 * @param dataList
//	 * @return
//	 */
//	public boolean updateSynClass(List<SysMenu> dataList) {
//		boolean result = false;
//		if(StringUtils.isNotEmpty(dataList)) {
//			int saveResult =  dao.updateBatch(dataList);
//			if(saveResult>0) {
//				result = true;
//			}
//		}
//		return result;
//	}

    /**
     * 转换TM3的菜单
     *
     * @param menu
     * @return
     */
    public String getMenuConversion(String menu) {
        // menu=tm3://scp/contractorinfo.jsp?hmd=0
        String url = "";
        try {
            if (menu == null || "".equals(menu)) {
                return "";
            }
            SysUser user = SysUserHolder.getCurrentUser();
            String gh = user.getStaffNo();// 工号
            if (gh == null || "".equals(gh)) {
                return "";
            }
//			menu = PassUtils.RSAEncryptPassword(menu);//java.net.URLEncoder.encode(menu,"utf-8");
//			String username=PassUtils.RSAEncryptPassword(gh);//JumpIn.SSOEnCode("00858688");
            if (menu.toLowerCase().startsWith("/tm3://")) {
                menu = menu.substring(1);
            }
            String tm3Url = env.getProperty("app.outsys.clientSecret");
            url = tm3Url + "/config.jsp?gh=" + gh + "&path=" + menu;
            // System.out.println("url=" + url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return url;
    }

    /**
     * 根据用户权限获得用户菜单列表
     *
     * @param userId
     * @return
     */
    public List<SysMenu> getUserMenuList() {
        List<SysMenu> menus = null;
        String userId = SysUserHolder.getCurrentUser().getId();
        List<SysMenu> rtnList = new ArrayList<SysMenu>();
        String tenant_id = null;
        if (MultiTenantUtils.enalbe()) {
            tenant_id = SysUserHolder.getCurrentUser().getTenant_id();
            menus = this.getMenuList(tenant_id, true, true, true, true);
        } else {
            menus = this.getMenuList(true, true, true, true);
        }
        if (StringUtils.isNotEmpty(menus)) {
            List<RouterVo> routerList = this.routerList(userId);
            if (StringUtils.isNotEmpty(routerList)) {
                SysMenu root = new SysMenu();
                Map<String, SysMenu> menuMap = this.getMenuMap(menus);
                this.bulidMenu(menuMap, routerList, root);
                List<SysMenu> temp = new ArrayList<SysMenu>();
                temp.add(root);
                rtnList.addAll(temp);
            }
        }
        return rtnList;

    }

    private void bulidMenu(Map<String, SysMenu> menuMap, List<RouterVo> routerList, SysMenu pmenu) {
        if (routerList.size() > 0) {
            for (RouterVo vo : routerList) {
                SysMenu menu = menuMap.get(vo.getId());
                if (menu != null) {
                    pmenu.getChildren().add(menu);
                    this.bulidMenu(menuMap, vo.getChildren(), menu);
                }
            }
        }
    }

    /**
     * 根据用户权限获得用户菜单列表 用于下拉选择
     *
     * @param userId
     * @return
     */
    public List<SysMenu> getUserSelectMenu() {
        List<SysMenu> rtnList = getUserMenuList();
        if (rtnList.size() > 0) {
            String tenant_id = null;
            if (MultiTenantUtils.enalbe()) {
                tenant_id = SysUserHolder.getCurrentUser().getTenant_id();
            } else {
                tenant_id = "";
            }
            SysMenu menuRoot = null;
            if (StringUtils.isNotEmpty(tenant_id)) {
                menuRoot = treeService.getRoot(tenant_id, SysMenu.class, MODEL_ID);
            } else {
                menuRoot = treeService.getRoot(SysMenu.class, MODEL_ID);
            }
            rtnList.get(0).setMenuName(menuRoot.getMenuName());
            rtnList.get(0).setId(menuRoot.getId());
            rtnList.get(0).setMenuType(menuRoot.getMenuType());
        }
        return rtnList;
    }

    /**
     * 获取按钮权限的map
     *
     * @return
     */
    public Map<String, List<String>> getButtonMap() {
        Map<String, List<String>> rtnMap = new HashMap<String, List<String>>();
        String tenant_id = null;
        if (MultiTenantUtils.enalbe()) {
            tenant_id = SysUserHolder.getCurrentUser().getTenant_id();
        }
        Map<String, Map<String, Object>> menuMap = this.getMenuMapFromRedis(tenant_id);
        if (menuMap != null) {
            for (String k : menuMap.keySet()) {
                Map<String, Object> mp = menuMap.get(k);
                String menuType = (String) mp.get("menuType");
                if (menuType.equals(SysMenu.TYPE_BUTTON)) {
                    String menuId = (String) mp.get("id");
                    String pid = (String) mp.get("pid");
                    String visible = (String) mp.get("visible");
                    String status = (String) mp.get("status");
                    if (StringUtils.isNotEmpty(pid) && "1".equals(visible) && "0".equals(status)) {
                        if (rtnMap.containsKey(pid)) {
                            rtnMap.get(pid).add(menuId);
                        } else {
                            List<String> list = new ArrayList<String>();
                            list.add(menuId);
                            rtnMap.put(pid, list);
                        }
                    }
                }
            }
        }
        return rtnMap;
    }


    /**
     * 获取协作模式下的隐藏菜单
     *
     * @return
     */
    public List<String> getAccHideList() {
        List<String> list = new ArrayList<String>();
        Where where = Where.create();
        where.eq(SysMenu::getIsHideOnAss, "0");// 1 or null ：显示；0:隐藏
        List<SysMenu> menus = dao.queryList(SysMenu.class, where);
        if (StringUtils.isNotEmpty(menus)) {
           Map<String,List<String>> menuMap = this.getButtonMap();
            for (SysMenu menu : menus) {
                list.add(menu.getId());
                if(menuMap.containsKey(menu.getId())){
                    list.addAll(menuMap.get(menu.getId()));//按钮权限
                }
            }
        }
        return list;
    }


//	public List<MtmIndexInfo> test() {
//		
////		select classsort,tmsort,indexname,* from mtm_indexinfo where
////		（tmused=1 and themesid='ZQRTXD7LW05CVZKK5Q0130' and versionnumber='2024-07'
////				and pmatrixid='ZQRTXD7LW05CVZKK5Q0130ZQRTXQD2005CVZKB2P0179' and matrixid='ZQRTXD7LW05CVZKK5Q0130ZQRTXRC1R05CVZK86F0167' ）
////				and rownum<=5
////				order by classsort, tmsort;
//		
//		Where where = Where.create();
//		
//		where.eq(MtmIndexInfo::getTmUsed, 1);
//		where.eq(MtmIndexInfo::getThemesId, "ZQRTXD7LW05CVZKK5Q0130");
//		where.eq(MtmIndexInfo::getVersionNumber, "2024-07");
//		where.eq(MtmIndexInfo::getPMatrixId, "ZQRTXD7LW05CVZKK5Q0130ZQRTXQD2005CVZKB2P0179");
//		where.eq(MtmIndexInfo::getMatrixId, "ZQRTXD7LW05CVZKK5Q0130ZQRTXRC1R05CVZK86F0167");
//	
//		
//		Order order = Order.create();
//		
//		order.orderByAsc(MtmIndexInfo::getClassSort);
//		order.orderByAsc(MtmIndexInfo::getTmSort);
//		
//		Pagination<?> page = Pagination.create(4,3);
//		
//		
//		return dao.queryData(MtmIndexInfo.class, where, order, page);
//		
//	}

}
