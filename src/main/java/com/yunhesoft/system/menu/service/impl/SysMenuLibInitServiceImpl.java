package com.yunhesoft.system.menu.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.dataperm.entity.po.SysDatapermConfig;
import com.yunhesoft.system.dataperm.service.ISysDataPermService;
import com.yunhesoft.system.menu.entity.po.SysMenu;
import com.yunhesoft.system.menu.entity.po.SysMenuLib;
import com.yunhesoft.system.menu.entity.po.SysMenuLibClass;
import com.yunhesoft.system.menu.entity.po.SysModule;
import com.yunhesoft.system.menu.service.ISysMenuLibInitService;
import com.yunhesoft.system.menu.service.ISysMenuLibService;
import com.yunhesoft.system.menu.service.SysMenuService;
import com.yunhesoft.system.mobileSys.entity.po.MobileMsgUrl;
import com.yunhesoft.system.mobileSys.service.IMobileService;
import com.yunhesoft.system.tools.initSql.entity.po.SysInitSql;
import com.yunhesoft.system.tools.initSql.service.InitSqlService;
import com.yunhesoft.system.tools.sysConfig.entity.SysConfig;
import com.yunhesoft.system.tools.sysConfig.service.ISysConfigService;
import com.yunhesoft.system.tools.todo.entity.po.SysTodoInfo;
import com.yunhesoft.system.tools.todo.entity.po.SysTodoModule;
import com.yunhesoft.system.tools.todo.service.TodoService;

import lombok.extern.log4j.Log4j2;

/**
 * 菜单初始化实现类
 * 
 * <AUTHOR>
 *
 */
@Log4j2
@Service
public class SysMenuLibInitServiceImpl implements ISysMenuLibInitService {

	@Autowired
	private ISysMenuLibService serv; // 菜单库

	@Autowired
	private SysMenuService menuServ; // 菜单

	@Autowired
	private ISysConfigService cfgServ; // 系统参数

	@Autowired
	private InitSqlService initSqlServ; // 初始化语句

	@Autowired
	private TodoService todoServ; // 待办服务

	@Autowired
	private ISysDataPermService datapermServ; // 数据权限服务

	@Autowired
	private IMobileService movileServ; // 移动端服务

	private SysModule module = null;// 模块

	private List<SysMenuLib> listMenu = new ArrayList<SysMenuLib>();// 菜单&按钮

	private List<SysMenuLibClass> listClass = new ArrayList<SysMenuLibClass>();// 分类

	private List<SysConfig> listConfig = new ArrayList<SysConfig>();// 系统参数

	private List<SysInitSql> listInitSql = new ArrayList<SysInitSql>();// 初始化SQL脚本

	private List<SysTodoModule> listTodoModule = new ArrayList<SysTodoModule>();// 初始化待办模块

	private List<SysTodoInfo> listTodoInfo = new ArrayList<SysTodoInfo>();// 初始化待办项

	private List<SysDatapermConfig> listDataPermCfg = new ArrayList<SysDatapermConfig>();// 初始化数据权限

	private static List<String> moduleCodeList = new ArrayList<String>();// 系统注册的模块
	private static List<SysModule> moduleList = new ArrayList<SysModule>();// 系统注册的模块

	private List<MobileMsgUrl> listMobileMsgUrl = new ArrayList<MobileMsgUrl>();// 初始化移动端跳转链接

	/**
	 * 清除数据
	 */
	@Override
	public void clearData() {
		listMenu.clear();
		listClass.clear();
		listConfig.clear();
		listTodoModule.clear();
		listTodoInfo.clear();
		listInitSql.clear();
		listMobileMsgUrl.clear();
	}

	/**
	 * 初始化模块
	 * 
	 * @param name 模块名称
	 * @param code 模块代码
	 */
	@Override
	public void initModule(String name, String code) {
		this.registerModule(code, name, null);// 模块注册
	}

	/**
	 * 初始化模块
	 * 
	 * @param name           模块名称
	 * @param code           模块代码
	 * @param modulePackPath 模块包路径
	 */
	@Override
	public void initModule(String name, String code, String modulePackPath) {
		this.registerModule(code, name, modulePackPath);// 模块注册
	}

	/**
	 * 初始化菜单库分类
	 * 
	 * @param name 分类名称
	 * @param code 分类代码
	 */
	@Override
	public void initClass(String name, String code) {
		listClass.add(initMenuClass(code, name));

	}

	/**
	 * 初始化菜单
	 * 
	 * @param name 菜单名称
	 * @param path 菜单路径
	 */
	@Override
	public void initMenuLib(String name, String path) {
		this.initMenuLib(name, path, null);
	}

	/**
	 * 初始化菜单
	 * 
	 * @param name  菜单名称
	 * @param path  菜单路径
	 * @param param 菜单参数
	 */
	@Override
	public void initMenuLib(String name, String path, String param) {
		listMenu.add(initMenu(listClass, name, path, param));
	}

	/**
	 * 初始化菜单
	 * 
	 * @param name    菜单名称
	 * @param path    菜单路径
	 * @param param   菜单参数
	 * @param synType 同步模式 1主题模式（菜单同步到各个主题，需要在主题内进行同步，不能直接从菜单库选择后同步） 0或null普通模式
	 */
	@Override
	public void initMenuLib(String name, String path, String param, Integer synType) {
		listMenu.add(initMenu(listClass, name, path, param, null, null, null, null, null, null, synType));
	}

	/**
	 * 初始化菜单(移动端)
	 * 
	 * @param app_name  移动端-菜单名称
	 * @param app_path  移动端-路由地址
	 * @param app_param 移动端-菜单参数
	 * @param app_icon  移动端-菜单图标
	 */
	@Override
	public void initMenuLibApp(String app_name, String app_path, String app_param, String app_icon) {
		listMenu.add(initMenuApp(listClass, app_name, app_path, app_param, app_icon));
	}

	/**
	 * 初始化菜单
	 * 
	 * @param name       菜单名称
	 * @param path       菜单路径
	 * @param param      菜单参数
	 * @param icon       图标
	 * @param applyRange 应用范围（0跟随父菜单 1PC 2移动端 3全部）
	 * @param app_name   移动端-菜单名称
	 * @param app_path   移动端-路由地址
	 * @param app_param  移动端-菜单参数
	 * @param app_icon   移动端-菜单图标
	 */
	@Override
	public void initMenuLib(String name, String path, String param, String icon, String applyRange, String app_name,
			String app_path, String app_param, String app_icon) {
		listMenu.add(initMenu(listClass, name, path, param, icon, applyRange, app_name, app_path, app_param, app_icon,
				null));
	}

	/**
	 * 初始化系统参数
	 * 
	 * @param path  菜单路径
	 * @param name  菜单名称
	 * @param param 菜单参数
	 */
	@Override
	public void initConfig(String name, String key, String value, String remark) {
		SysConfig config = initSysConfig(name, key, "varchar", value, remark);
		listConfig.add(config);
	}

	@Override
	public void initConfig(String name, String key, String value, String remark, Integer isOpen) {
		SysConfig config = initSysConfig(name, key, "varchar", value, remark, isOpen);
		listConfig.add(config);
	}

	/**
	 * 初始化系统参数
	 * 
	 * @param path  菜单路径
	 * @param name  菜单名称
	 * @param param 菜单参数
	 */
	@Override
	public void initConfigJson(String name, String key, String value, String remark) {
		SysConfig config = initSysConfig(name, key, "json", value, remark);
		listConfig.add(config);
	}

	/**
	 * 初始化按钮权限
	 * 
	 * @param name 按钮名称
	 * @param code 按钮代码
	 */
	@Override
	public void initButton(String name, String code) {
		this.initBtn(code, name);
	}

	@Override
	public void initButtonAdd() {
		this.initButton("添加", "add");
	}

	@Override
	public void initButtonEdit() {
		this.initButton("修改", "edit");
	}

	@Override
	public void initButtonDel() {
		this.initButton("删除", "delete");
	}

	@Override
	public void initButtonSave() {
		this.initButton("保存", "save");
	}

	// 导入
	@Override
	public void initButtonImport() {
		this.initButton("导入", "import");
	}

	// 导出
	@Override
	public void initButtonExport() {
		this.initButton("导出", "export");
	}

	// 导出
	@Override
	public void initButtonRemove() {
		this.initButton("清除", "remove");
	}

	/**
	 * 判断分类是否有变化
	 * 
	 * @param _old
	 * @param _new
	 * @return
	 */
	private boolean isChange(SysMenuLibClass _old, SysMenuLibClass _new) {
		if (!_old.getClassName().equals(_new.getClassName())) {
			return true;
		}
		if (_old.getUsed() == 0) {
			return true;
		}

		if (_old.getTmsort() != _new.getTmsort()) {
			return true;
		}
		return false;
	}

	/**
	 * 判断菜单是否有变化
	 * 
	 * @param _old
	 * @param _new
	 * @return
	 */
	private boolean isChange(SysMenuLib _old, SysMenuLib _new) {
		if (!_old.getMenuName().equals(_new.getMenuName())) {
			return true;
		}
		if (_old.getUsed() == 0) {
			return true;
		}
		if (_old.getTmsort() != _new.getTmsort()) {
			return true;
		}
		String q1 = _old.getQuery() == null ? "" : _old.getQuery();
		String q2 = _new.getQuery() == null ? "" : _new.getQuery();
		if (!q1.equals(q2)) {
			return true;
		}

		// app 参数判断 ----
		q1 = _old.getApp_menuName() == null ? "" : _old.getApp_menuName();
		q2 = _new.getApp_menuName() == null ? "" : _new.getApp_menuName();
		if (!q1.equals(q2)) {
			return true;
		}

		q1 = _old.getApp_query() == null ? "" : _old.getApp_query();
		q2 = _new.getApp_query() == null ? "" : _new.getApp_query();
		if (!q1.equals(q2)) {
			return true;
		}

		return false;
	}

	/**
	 * 判断按钮是否有变化
	 * 
	 * @param _old
	 * @param _new
	 * @return
	 */
	private boolean isBtnChange(SysMenuLib _old, SysMenuLib _new) {
		if (!_old.getMenuName().equals(_new.getMenuName())) {
			return true;
		}
		if (_old.getUsed() == 0) {
			return true;
		}
		if (_old.getTmsort() != _new.getTmsort()) {
			return true;
		}
		return false;
	}

	/**
	 * 执行初始化操作
	 */
	@Override
	public void runInit() {
		if (module == null) {
			return;
		}
		// **** 运行初始化脚本功能
		initSqlServ.insertScript(listInitSql);// 初始化脚本添加到数据库
		for (String code : moduleCodeList) {
			initSqlServ.execScript(code);// 执行初始化脚本
		}
		// **** 初始化菜单
		this.runInitMenu();
		// **** 初始化系统参数
		this.initConfig();
		// **** 初始化数据权限
		datapermServ.initDataPerm(listDataPermCfg);
		// **** 开始初始化待办信息
		todoServ.initTodo(listTodoModule, listTodoInfo);
		// todoServ.clearAllTodoCached(); // 清除待办缓存

		// **** 开始初始化移动端跳转链接
		movileServ.initMobile(listMobileMsgUrl);
	}

	/**
	 * 处理初始化菜单
	 */
	private void runInitMenu() {
		// log.info("正在初始化菜单，模块:[" + module.getModuleCode() + "," +
		// module.getModuleName() + "]");
		// **** 更新分类
		if (StringUtils.isNotEmpty(listClass)) {

			Map<String, Map<String, SysMenuLibClass>> mapModuleClass = new HashMap<String, Map<String, SysMenuLibClass>>();

			// Map<String, SysMenuLibClass> mapClass =
			// this.getMenuClassMap(module.getModuleCode());
			List<SysMenuLibClass> listClassAdd = new ArrayList<SysMenuLibClass>();
			List<SysMenuLibClass> listClassEdit = new ArrayList<SysMenuLibClass>();
			int n = 0;
			for (SysMenuLibClass e : listClass) {
				n++;
				String modulecode = e.getModuleCode();
				Map<String, SysMenuLibClass> mapClass = mapModuleClass.get(modulecode);
				if (mapClass == null) {
					mapClass = this.getMenuClassMap(modulecode);
					mapModuleClass.put(modulecode, mapClass);
				}
				SysMenuLibClass libClass = mapClass.get(e.getClassCode());
				e.setTmsort(n);
				if (libClass == null) {// 新增
					e.setId(TMUID.getUID());
					e.setUsed(1);
					listClassAdd.add(e);
				} else {
					if (isChange(libClass, e)) {// 判断是否有变化，有变化则修改
						libClass.setClassName(e.getClassName());
						libClass.setUsed(1);
						listClassEdit.add(libClass);
					}
				}
			}
			if (StringUtils.isNotEmpty(listClassAdd)) {
				serv.addClass(listClassAdd);
			}
			if (StringUtils.isNotEmpty(listClassEdit)) {
				serv.updateClass(listClassEdit);
			}
		}
		// **** 更新菜单和按钮
		if (StringUtils.isNotEmpty(listMenu)) {
			List<SysMenuLib> listAdd = new ArrayList<SysMenuLib>();
			List<SysMenuLib> listEdit = new ArrayList<SysMenuLib>();
			Map<String, Map<String, SysMenuLib>> mapModuleMenuLib = new HashMap<String, Map<String, SysMenuLib>>();
			// Map<String, SysMenuLib> mapMenuLib =
			// this.getMenuLibMap(module.getModuleCode());// 菜单库
			Map<String, List<SysMenu>> mapMenu = this.getMenuMap();// 系统菜单
			List<SysMenu> listMenuAdd = new ArrayList<SysMenu>();
			int n = 0;
			for (SysMenuLib e : listMenu) {
				n++;
				String modulecode = e.getModuleCode();
				String key = e.getMenuType() + ":" + e.getPath();
				if ("C".equalsIgnoreCase(e.getMenuType())) {// 子菜单
					key += ":" + e.getComponent();
				}

				Map<String, SysMenuLib> mapMenuLib = mapModuleMenuLib.get(modulecode);
				if (mapMenuLib == null) {
					mapMenuLib = this.getMenuLibMap(modulecode);// 菜单库
					mapModuleMenuLib.put(modulecode, mapMenuLib);
				}
				SysMenuLib menu = mapMenuLib.get(key);
				e.setTmsort(n);
				if (menu == null) {// 新增
					e.setId(TMUID.getUID());
					e.setUsed(1);
					listAdd.add(e);
					List<SysMenuLib> btns = e.getChildren();
					if (StringUtils.isNotEmpty(btns)) {// 新增按钮
						int p = 0;
						for (SysMenuLib btn : btns) {
							p++;
							btn.setId(TMUID.getUID());
							btn.setTmsort(p);
							btn.setUsed(1);
							listAdd.add(btn);
						}
					}
				} else {
					// 判断是否有变化，有变化则修改
					if (isChange(menu, e)) {
						menu.setMenuName(e.getMenuName());
						menu.setUsed(1);
						menu.setQuery(e.getQuery());
						listEdit.add(menu);
					}
					List<SysMenuLib> btns = e.getChildren();
					if (StringUtils.isNotEmpty(btns)) {// 按钮
						int p = 0;
						for (SysMenuLib eb : btns) {
							p++;
							// 判断按钮是否有变化
							String key1 = eb.getMenuType() + ":" + eb.getPath() + ":" + eb.getPerms();
							SysMenuLib btn = mapMenuLib.get(key1);
							SysMenuLib libBtn = null;
							eb.setTmsort(p);
							if (btn == null) {
								eb.setId(TMUID.getUID());
								eb.setUsed(1);
								libBtn = eb;
								listAdd.add(eb);
							} else {
								if (isBtnChange(btn, eb)) {
									btn.setMenuName(eb.getMenuName());
									btn.setUsed(1);
									btn.setTmsort(p);
									listEdit.add(btn);
								}
								libBtn = btn;
							}
							// 开始判断系统菜单是否需要添加按钮权限
							List<SysMenu> sysMenuBtn = mapMenu.get(key1);
							if (sysMenuBtn == null) {// 没有需要添加
								String key2 = SysMenu.TYPE_MENU + ":" + eb.getPath();
								List<SysMenu> pmList = mapMenu.get(key2);
								if (pmList != null) {
									for (SysMenu pm : pmList) {
										listMenuAdd.add(menuServ.lib2button(pm.getId(), libBtn));
									}
								}
							}
						}
					}
				}
			}
			if (StringUtils.isNotEmpty(listAdd)) {
				serv.addMenuLib(listAdd);
			}
			if (StringUtils.isNotEmpty(listEdit)) {
				serv.updateMenuLib(listEdit);
			}
			if (StringUtils.isNotEmpty(listMenuAdd)) {// 系统菜单添加按钮
				menuServ.insertMenus(listMenuAdd);
				menuServ.correctTree(null);// 重新计算左右值
			}
		}
		// log.info("初始化菜单完毕，模块:[" + module.getModuleCode() + "," +
		// module.getModuleName() + "]");
	}

	/**
	 * 初始化按钮
	 * 
	 * @param code 编码
	 * @param name 名称
	 */
	private void initBtn(String code, String name) {
		if (StringUtils.isNotEmpty(listMenu)) {
			SysMenuLib pmenu = listMenu.get(listMenu.size() - 1);
//			log.info(code + "," + name);
//			long l1 = System.currentTimeMillis();
			SysMenuLib bean = this.copyBtn(pmenu);
//			long l2 = System.currentTimeMillis() - l1;
//			System.out.println("===============" + l2 + "," + pmenu.getChildren().size());

			bean.setMenuName(name);
			bean.setPerms(code);
			List<SysMenuLib> list = pmenu.getChildren();
			if (list == null) {
				list = new ArrayList<SysMenuLib>();
			}
			list.add(bean);
			pmenu.setChildren(list);
		}
	}

	/**
	 * 按钮拷贝父菜单属性
	 * 
	 * @param pmenu
	 * @return
	 */
	private SysMenuLib copyBtn(SysMenuLib menu) {
		// 哪些字段不复制
		List<String> excludeList = new ArrayList<String>();
		excludeList.add("ID");
		excludeList.add("menuType");
		excludeList.add("children");
		// 开始复制
		SysMenuLib bean = ObjUtils.copyTo(menu, SysMenuLib.class, excludeList);
		bean.setId(null);
		bean.setMenuType(SysMenu.TYPE_BUTTON);
		return bean;
	}

	/**
	 * 初始化菜单
	 * 
	 * @param list
	 * @param menuName 菜单名称
	 * @param path     菜单路径
	 * @param param    菜单参数
	 * @return
	 */
	private SysMenuLib initMenu(List<SysMenuLibClass> list, String menuName, String path, String param) {
		return this.initMenu(list, menuName, path, param, null, null, null, null, null, null, null);
	}

	/**
	 * 初始化菜单（移动端）
	 * 
	 * @param list
	 * @param app_name  移动端-菜单名称
	 * @param app_path  移动端-路由地址
	 * @param app_param 移动端-菜单参数
	 * @param app_icon  移动端-菜单图标
	 * @return
	 */
	private SysMenuLib initMenuApp(List<SysMenuLibClass> list, String app_name, String app_path, String app_param,
			String app_icon) {
		return this.initMenu(list, app_name, null, null, null, "2", app_name, app_path, app_param, app_icon, null);
	}

	/**
	 * 初始化菜单
	 * 
	 * @param list
	 * 
	 * @param menuName   菜单名称
	 * @param path       菜单路径
	 * @param param      菜单参数
	 * @param icon       图标
	 * @param applyRange 应用范围（0跟随父菜单 1PC 2移动端 3全部）
	 * @param app_name   移动端-菜单名称
	 * @param app_path   移动端-路由地址
	 * @param app_param  移动端-菜单参数
	 * @param app_icon   移动端-菜单图标
	 * @param synType    同步模式 1主题模式（菜单同步到各个主题，需要在主题内进行同步，不能直接从菜单库选择后同步） 0或null普通模式
	 * @return
	 */
	private SysMenuLib initMenu(List<SysMenuLibClass> list, String menuName, String path, String param, String icon,
			String applyRange, String app_name, String app_path, String app_param, String app_icon, Integer synType) {
		SysMenuLib bean = new SysMenuLib();
		if (StringUtils.isNotEmpty(list)) {
			if (module != null) {
				bean.setModuleCode(module.getModuleCode());
			}
			bean.setClassCode(list.get(list.size() - 1).getClassCode());
			bean.setMenuName(menuName);
			bean.setPath(path);
			bean.setMenuType(SysMenu.TYPE_MENU);
			if (StringUtils.isNotEmpty(param)) {
				JSONObject queryObj = new JSONObject();
				String[] p = param.split("&");
				for (String s : p) {
					String[] v = s.split("=");
					String key = v[0];
					String value = "";
					if (v.length > 1) {
						value = v[1];
					}
					queryObj.put(key, value);
				}
				bean.setQuery(queryObj.toJSONString());
			}
			bean.setIcon(icon);
			bean.setApplyRange(applyRange);

			// app 参数 -----------------
			bean.setApp_menuName(app_name);
			bean.setApp_path(app_path);
			bean.setApp_icon(app_icon);
			if (StringUtils.isNotEmpty(app_param)) {
				JSONObject queryObj = new JSONObject();
				String[] p = app_param.split("&");
				for (String s : p) {
					String[] v = s.split("=");
					String key = v[0];
					String value = "";
					if (v.length > 1) {
						value = v[1];
					}
					queryObj.put(key, value);
				}
				bean.setApp_query(queryObj.toJSONString());
			}
			bean.setSynType(synType);
		}
		return bean;
	}

	/**
	 * 初始化子菜单
	 * 
	 * @param menuName      子菜单名称
	 * @param componentPath 子菜单路径
	 */
	@Override
	public void initSubMenu(String menuName, String componentPath) {
		if (StringUtils.isNotEmpty(listMenu)) {
			SysMenuLib pmenu = listMenu.get(listMenu.size() - 1);
			SysMenuLib subMenu = initSubMenu(pmenu, menuName, componentPath);
			listMenu.add(subMenu);
		}
	}

	/**
	 * 初始化子菜单组件
	 * 
	 * @param parantMenu    父菜单对象
	 * @param menuName      子菜单名称
	 * @param componentPath 子菜单路径
	 * @return
	 */
	private SysMenuLib initSubMenu(SysMenuLib parantMenu, String menuName, String componentPath) {
		SysMenuLib bean = new SysMenuLib();
		if (parantMenu != null) {
			bean.setModuleCode(parantMenu.getModuleCode());
			bean.setClassCode(parantMenu.getClassCode());
			bean.setMenuName(menuName);
			bean.setComponent(componentPath);
			bean.setPath(parantMenu.getPath());
			bean.setMenuType("C");
			bean.setUsed(1);
		}
		return bean;
	}

	/**
	 * 初始化系统参数
	 * 
	 * @param configName
	 * @param configKey
	 * @param configValue
	 * @param remark
	 * @return
	 */
//	private SysConfig initSysConfig(String configName, String configKey, String configValue, String remark) {
//		return this.initSysConfig(configName, configKey, "varchar", configValue, remark);
//	}

	/**
	 * 初始化系统参数
	 * 
	 * @param configName
	 * @param configKey
	 * @param configType
	 * @param configValue
	 * @param remark
	 * @return
	 */
	private SysConfig initSysConfig(String configName, String configKey, String configType, String configValue,
			String remark) {
		return initSysConfig(configName, configKey, configType, configValue, remark, null);
	}

	private SysConfig initSysConfig(String configName, String configKey, String configType, String configValue,
									String remark, Integer isOpen) {
		SysConfig bean = new SysConfig();
		if (module != null) {
			bean.setModuleCode(module.getModuleCode());
		}
		bean.setConfigKey(configKey);
		bean.setConfigName(configName);
		bean.setConfigType(configType);
		bean.setConfigValue(configValue);
		bean.setRemark(remark);
		bean.setIsOpen(isOpen);

		return bean;
	}

	/**
	 * 分类初始化
	 * 
	 * @param classCode
	 * @param className
	 * @return
	 */
	private SysMenuLibClass initMenuClass(String classCode, String className) {
		SysMenuLibClass bean = new SysMenuLibClass();
		if (module != null) {
			bean.setModuleCode(module.getModuleCode());
		}
		bean.setClassCode(classCode);
		bean.setClassName(className);
		return bean;

	}

	/**
	 * 初始化模块
	 * 
	 * @param moduleCode     模块编码
	 * @param moduleName     模块名称
	 * @param modulePackPath 模块包路径
	 * @return
	 */
	private SysModule initSysModule(String moduleCode, String moduleName, String modulePackPath) {
		List<SysModule> listModule = serv.getSysModuleByCode(true);
		SysModule module = serv.getSysModuleByCode(listModule, moduleCode);
		int type = -1;
		if (module == null) {// 新增
			type = 0;
			module = new SysModule();
			module.setModuleCode(moduleCode);
			module.setModuleName(moduleName);
			module.setUsed(1);
			if ("system".equalsIgnoreCase(moduleCode)) {// 系统模块
				module.setTmsort(1);
			} else {
				if (StringUtils.isEmpty(listModule)) {
					module.setTmsort(1001);
				} else {
					module.setTmsort(listModule.get(listModule.size() - 1).getTmsort() + 1);
				}
			}
		} else {// 更新
			if (moduleName != null && !moduleName.equalsIgnoreCase(module.getModuleName())) {
				type = 1;
				module.setModuleName(moduleName);
			}
			// if (module.getUsed() == null || module.getUsed() == 0) {
			if (module.getUsed() == null) {// 数据库里used=0 的记录不要更新为1 -- by x.zhong 2023.10.31
				type = 1;
				module.setUsed(1);
			}
			if (modulePackPath != null && !modulePackPath.equalsIgnoreCase(module.getModulePackPath())) {
				type = 1;
				module.setModulePackPath(modulePackPath);
			} else if (modulePackPath == null && module.getModulePackPath() != null) {// 置空
				type = 1;
				module.setModulePackPath("");
			}
		}
		if (type >= 0) {
			if (type == 1) {// 更新
				serv.updateModule(module);
			} else {// 添加
				serv.addModule(module);
			}
			// 更新模块
		}
		return module;
	}

	/**
	 * 获取分类
	 * 
	 * @param moduleCode
	 * @return
	 */
	private Map<String, SysMenuLibClass> getMenuClassMap(String moduleCode) {
		Map<String, SysMenuLibClass> map = new HashMap<String, SysMenuLibClass>();
		List<SysMenuLibClass> list = serv.getSysMenuLibClass(moduleCode);
		if (StringUtils.isNotEmpty(list)) {
			for (SysMenuLibClass sysMenuLibClass : list) {
				map.put(sysMenuLibClass.getClassCode(), sysMenuLibClass);
			}
		}
		return map;
	}

	/**
	 * 获取菜单库map
	 * 
	 * @param moduleCode
	 * @return
	 */
	private Map<String, SysMenuLib> getMenuLibMap(String moduleCode) {
		return serv.getMenuLibMap(moduleCode);
	}

	/**
	 * 获得系统菜单map
	 * 
	 * @return
	 */
	private Map<String, List<SysMenu>> getMenuMap() {
		Map<String, List<SysMenu>> map = new HashMap<String, List<SysMenu>>();
		List<SysMenu> listMenu = menuServ.getMenuList(false, false, true, true);
		if (StringUtils.isNotEmpty(listMenu)) {
			Map<String, SysMenu> mapMenu = new HashMap<String, SysMenu>();
			for (SysMenu sysMenu : listMenu) {
				if ("1".equals(sysMenu.getIsFrame())) {
					if (SysMenu.TYPE_MENU.equalsIgnoreCase(sysMenu.getMenuType())) {// 菜单
						String key = sysMenu.getMenuType() + ":" + sysMenu.getPath();
						if (map.containsKey(key)) {
							map.get(key).add(sysMenu);
						} else {
							List<SysMenu> list = new ArrayList<SysMenu>();
							list.add(sysMenu);
							map.put(key, list);
						}
						mapMenu.put(sysMenu.getId(), sysMenu);
					}
				}
			}
			for (SysMenu sysMenu : listMenu) {
				if ("1".equals(sysMenu.getIsFrame())) {
					if (SysMenu.TYPE_BUTTON.equalsIgnoreCase(sysMenu.getMenuType())) {// 按钮
						SysMenu m = mapMenu.get(sysMenu.getPid());
						if (m != null) {
							String key = sysMenu.getMenuType() + ":" + m.getPath() + ":" + sysMenu.getPerms();
							List<SysMenu> list = new ArrayList<SysMenu>();
							list.add(sysMenu);
							map.put(key, list);
							// map.put(key, sysMenu);
						}
					}
				}
			}
		}
		return map;
	}

	/**
	 * 初始化系统参数
	 */
	private void initConfig() {
		if (StringUtils.isNotEmpty(listConfig)) {
			for (SysConfig e : listConfig) {
				SysConfig config = cfgServ.getSysConfigByKey(e.getConfigKey());// 从数据库读
				if (config == null) {// 没有这个参数
					try {
						cfgServ.insertSysConfig(e);// 添加
					} catch (Exception ex) {
						log.error("", ex);
					}
				}
			}
		}
	}

	/**
	 * 初始化sql语句
	 * 
	 * @param sql         初始化语句
	 * @param description 描述
	 * @param author      作者
	 * @param inputDate   添加日期
	 */
	@Override
	public void initSqlScript(String sql, String description, String author, String inputDate) {
		SysInitSql bean = new SysInitSql();
		bean.setModuleCode(this.module.getModuleCode());
		bean.setSqlAuthor(author);
		bean.setDescription(description);
		bean.setInputDt(inputDate);
		bean.setSqlScript(sql);
		this.listInitSql.add(bean);
	}

	/**
	 * 初始化待办模块
	 *
	 * @param funcCode      功能编码
	 * @param funcName      功能名称
	 * @param execClassName 执行类全路径
	 */
	@Override
	public void initTodoModule(String funcCode, String funcName, String execClassName) {
		initTodoModule(funcCode, funcName, execClassName, null);
	}

	/**
	 * 初始化待办模块
	 *
	 * @param funcCode      功能编码
	 * @param funcName      功能名称
	 * @param execClassName 执行类全路径
	 * @param serviceName 	对应微服务工程的spring.application.name，也是服务注册与发现中心的微服务名称
	 */
	@Override
	public void initTodoModule(String funcCode, String funcName, String execClassName, String serviceName) {
		SysTodoModule bean = new SysTodoModule();
		bean.setId(TMUID.getUID());
		bean.setModuleCode(this.module.getModuleCode());
		bean.setFuncCode(funcCode);
		bean.setFuncName(funcName);
		bean.setClassName(execClassName);
		bean.setUsed(1);
		//TODO 初始化微服务项目名与待办接口路径
		bean.setServiceName(serviceName);
		this.listTodoModule.add(bean);
	}

	/**
	 * 初始化待办项
	 * 
	 * @param todoCode   待办编码
	 * @param todoName   待办名称
	 * @param execScript 执行内容
	 * @param url        点击地址
	 */
	@Override
	public void initTodo(String todoCode, String todoName, String execScript, String url) {
		initTodo(todoCode, todoName, execScript, null, url, null);
	}

	/**
	 * 初始化待办项
	 * 
	 * @param todoCode   待办编码
	 * @param todoName   待办名称
	 * @param execScript 执行内容
	 * @param url        点击地址
	 */
	@Override
	public void initTodo(String todoCode, String todoName, String execScript, String execScriptUnRead, String url) {
		initTodo(todoCode, todoName, execScript, execScriptUnRead, url, null);
	}

	/**
	 * 初始化待办项
	 * 
	 * @param todoCode   待办编码
	 * @param todoName   待办名称
	 * @param execScript 执行内容(待办数量)
	 * @param url        点击地址
	 * @param dataMap    SysTodoInfo其他扩展字段
	 */
	@Override
	public void initTodo(String todoCode, String todoName, String execScript, String url, Map<String, ?> dataMap) {
		initTodo(todoCode, todoName, execScript, null, url, dataMap);
	}

	/**
	 * 初始化待办项
	 * @param todoCode         待办编码
	 * @param todoName         待办名称
	 * @param execScript       执行内容(待办数量)
	 * @param execScriptUnRead 执行内容(未读数量)
	 * @param url              点击地址
	 * @param dataMap          SysTodoInfo其他扩展字段
	 */
	@Override
	public void initTodo(String todoCode, String todoName, String execScript, String execScriptUnRead, String url,
						 Map<String, ?> dataMap) {
		initTodo(todoCode, todoName, execScript, execScriptUnRead, url, dataMap, null, null);
	}

	/**
	 * 初始化待办项
	 * 
	 * @param todoCode         待办编码
	 * @param todoName         待办名称
	 * @param execScript       执行内容(待办数量)
	 * @param execScriptUnRead 执行内容(未读数量)
	 * @param url              点击地址
	 * @param dataMap          SysTodoInfo其他扩展字段
	 * @param requestPath	   请求路径
	 * @param execType		   执行类型（1=类反射执行方法，2=REST API接口，其它=sql语句查询）
	 */
	@Override
	public void initTodo(String todoCode, String todoName, String execScript, String execScriptUnRead, String url,
						 Map<String, ?> dataMap, String requestPath, Integer execType) {
		if (this.listTodoModule.size() > 0) {
			SysTodoModule todoModule = this.listTodoModule.get(this.listTodoModule.size() - 1);
			SysTodoInfo bean = null;
			if (StringUtils.isNotEmpty(dataMap)) {
				bean = ObjUtils.convertToObject(SysTodoInfo.class, dataMap);
			}
			if (bean == null) {
				bean = new SysTodoInfo();
			}
			bean.setId(TMUID.getUID());
			bean.setModuleCode(todoModule.getModuleCode());
			bean.setFuncCode(todoModule.getFuncCode());
			bean.setTodoCode(todoCode);
			bean.setTodoName(todoName);
			bean.setExecContent(execScript);
			bean.setExecContentUnRead(execScriptUnRead);
			bean.setTodoUrl(url);
			if (execType == null) {
				execType = 1;
			}
			bean.setExecType(execType);
			bean.setTodoType(1);
			bean.setUsed(1);
			bean.setRequestPath(requestPath);
			this.listTodoInfo.add(bean);
		}
	}

	/**
	 * 初始化数据权限（组织机构的数据权限）
	 * 
	 * @param code 数据权限编码
	 * @param name 数据权限名称
	 */
	@Override
	public void initDataPerm(String code, String name) {
		this.initDataPerm(code, name, null, null);

	}

	/**
	 * 初始化数据权限（自定义的数据权限）
	 * 
	 * @param code         数据权限编码
	 * @param name         数据权限名称
	 * @param className    备选数据类路径
	 * @param functionName 备选数据执行函数
	 */
	@Override
	public void initDataPerm(String code, String name, String className, String functionName) {
		initDataPerm(code, name, className, functionName, null);
	}

	/**
	 * 初始化数据权限（自定义的数据权限）
	 * 
	 * @param code               数据权限编码
	 * @param name               数据权限名称
	 * @param className          备选数据类路径
	 * @param getFunctionName    备选数据执行函数
	 * @param extandFunctionName 左侧数据权限节点展开执行函数
	 */
	@Override
	public void initDataPerm(String code, String name, String className, String getFunctionName,
			String extandFunctionName) {
		this.initDataPerm(code, extandFunctionName, className, getFunctionName, extandFunctionName, null, null, null);
	}

	/**
	 * 初始化数据权限（自定义的数据权限）
	 * 
	 * @param code         数据权限编码
	 * @param name         数据权限名称
	 * @param className    备选数据类路径
	 * @param getFunctionName 备选数据执行函数
	 * @param extandFunctionName 左侧数据权限节点展开执行函数
	 * @param serviceName 服务名
	 * @param treeGetDataUrl 树形获取服务
	 * @param treeExpandDataUrl 树形展开服务 
	 */
	public void initDataPerm(String code, String name, String className, String getFunctionName,
			String extandFunctionName,String serviceName,String treeGetDataUrl,String treeExpandDataUrl) {
		SysDatapermConfig bean = new SysDatapermConfig();
		int datatype = 0;// 数据类型，1：自定义；0：组织机构
		if ((StringUtils.isNotEmpty(className) && StringUtils.isNotEmpty(getFunctionName)) || StringUtils.isNotEmpty(serviceName)) {//有外部类或者服务名
			datatype = 1;
		}
		bean.setDatatype(datatype);
		bean.setCode(code);
		bean.setId(TMUID.getUID());
		bean.setModulecode(module.getModuleCode());
		bean.setName(name);
		bean.setTreeClassName(className);
		bean.setTreeGetDataFun(getFunctionName);
		bean.setTreeExpandDataFun(extandFunctionName);
		bean.setServiceName(serviceName);
		bean.setTreeGetDataUrl(treeGetDataUrl);
		bean.setTreeExpandDataUrl(treeExpandDataUrl);
		this.listDataPermCfg.add(bean);
	}
	
	/**
	 * 模块注册
	 * 
	 * @param code           模块代码
	 * @param name           模块名称
	 * @param modulePackPath 模块包路径
	 */
	private void registerModule(String code, String name, String modulePackPath) {
		if (StringUtils.isNotEmpty(code)) {
			String moduleCode = code.toLowerCase();
			if (!moduleCodeList.contains(moduleCode)) {
				moduleCodeList.add(moduleCode);

				// 初始化模块对象
				module = initSysModule(code, name, modulePackPath);
				moduleList.add(module);

			}
		}
	}

	/**
	 * 判断模块是否注册
	 * 
	 * @param code
	 */
	@Override
	public boolean isModuleRegister(String code) {
		if (StringUtils.isEmpty(code)) {
			return false;
		}

		//TODO 多微服务运行模式下，影响菜单显示、待办显示……，暂时放开此处限制
		return true;

		//if (moduleCodeList.contains(code.toLowerCase())) {
		//	return true;
		//} else {
		//	return false;
		//}
	}

	/**
	 * 获取系统内已注册的模块列表(所有加载的微服务)
	 * 
	 * @return
	 */
	@Override
	public List<SysModule> getModuleList() {
		List<SysModule> tempModuleList = null;
		if (moduleList != null) {
			tempModuleList = ObjUtils.copyToList(moduleList, SysModule.class);
		}
		return tempModuleList;
	}

	/**
	 * 创建移动端消息跳转地址对象（不操作数据库）
	 * 
	 * @param modulecode   模板编码(必填) 例如：system
	 * @param funname      功能名称(必填) 例如：系统模块_数据源查询
	 * @param funcode      功能编码(必填) 例如：tds_query
	 * @param appurl       APP页面跳转地址(必填)/pages/tds/index?TDSALIAS=pm_clxxcx&queryMode=true
	 * @param appurlParams APP页面跳转地址参数
	 * @param memo         描述
	 * @return
	 */
	@Override
	public void initMobileMsgUrl(String funname, String funcode, String appurl, String appurlParams, String memo) {
		MobileMsgUrl e = movileServ.createMobileMsgUrl(module.getModuleCode(), funname, funcode, appurl, appurlParams,
				memo);
		if (e != null) {
			listMobileMsgUrl.add(e);
		}
	}

	/**
	 * 获取当前模块编码
	 * 
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	@Override
	public String getCurrentMoudleCode() {
		return module.getModuleCode();
	}


	/**
	 * 模块注册
	 *
	 * @param code 模块编码
	 */
	private void registerModule(String code) {
		if (StringUtils.isNotEmpty(code)) {
			String moduleCode = code.toLowerCase();
			if (!moduleCodeList.contains(moduleCode)) {
				moduleCodeList.add(moduleCode);
			}
		}
	}


	/**
	 * 注册模块并初始化
	 */
	private SysModule registerAndInitSysModule(String code,String name) {
		this.registerModule(code);// 模块注册
		return this.initSysModule(code, name, null);//初始化模块
	}

		/**
	 * 初始化待办模块
	 * @param moduleCode    模块编码
	 * @param funcCode      功能编码
	 * @param funcName      功能名称
	 * @param execClassName 执行类全路径
	 */
	private void initTodoModuleWithModuleCode(String moduleCode,String funcCode, String funcName, String execClassName) {
		SysTodoModule bean = new SysTodoModule();
		bean.setId(TMUID.getUID());
		bean.setModuleCode(moduleCode);
		bean.setFuncCode(funcCode);
		bean.setFuncName(funcName);
		bean.setClassName(execClassName);
		bean.setUsed(1);
		this.listTodoModule.add(bean);
	}

	/**
	 * 初始化待办模块(同时初始化模块)
	 *
	 * @param ModuleCode    模块编码
	 * @param moduleName    模块名称
	 * @param funcCode      功能编码
	 * @param funcName      功能名称
	 * @param execClassPath 执行类全路径
	 */
	@Override
	public void initTodoModuleAndModule(String moduleCode, String moduleName, String funcCode, String funcName,
			String execClassName) {
		// TODO Auto-generated method stub
		// 模块注册
		this.registerAndInitSysModule(moduleCode, moduleName);
		// 待办注册
		this.initTodoModuleWithModuleCode(moduleCode, funcCode, funcName, execClassName);
	}

}
