package com.yunhesoft.system.menu.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.menu.entity.po.SysMenu;
import com.yunhesoft.system.menu.service.SysMenuService;
import com.yunhesoft.system.role.service.ISysRolePermService;
import com.yunhesoft.system.role.service.ISysUserPermService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@Api(tags = "系统菜单")
@RestController
@RequestMapping("/menu")
public class MenuController extends BaseRestController {

    @Autowired
    private SysMenuService menuService;

    @Autowired
    private ISysRolePermService rolePermSer;

    @Autowired
    ISysUserPermService userPermSer;

    @ApiOperation(value = "菜单列表", notes = "根据登录用户得到菜单列表")
    @RequestMapping(value = "routers", method = {RequestMethod.GET})
    public Res<?> menuRouters(@RequestParam(required = false) @ApiParam(value = "账号模式") String mode) {
        /**
         * mode : 1:正常模式；2：协作模式
         */
        return menuService.menuRouters(mode);
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "菜单列表", notes = "根据所有菜单列表")
    @RequestMapping(value = "menus", method = {RequestMethod.GET})
    public Res<List<SysMenu>> menuList(@RequestParam(required = false) @ApiParam(value = "租户id") String tenant_id) {
        List<SysMenu> menus = null;
        if (SysUserUtil.isAdmin()) {// 管理员-全部菜单
            menus = menuService.menuList(tenant_id);
        } else {// 普通用户-根据角色权限显示
            menus = menuService.getUserMenuList();
        }
        if (menus != null) {
            return Res.OK(menus);
        } else {
            return Res.FAIL("菜单读取错误，请联系系统管理员");
        }
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "修正菜单左右值")
    @RequestMapping(value = "correct", method = {RequestMethod.GET})
    public Res<Integer> correctMenuTree(@RequestParam @ApiParam(value = "菜单模型ID") String menuModelId,
                                        @RequestParam(required = false) @ApiParam(value = "租户ID") String tenant_id) {
        return Res.OK(menuService.correctTree(menuModelId, tenant_id));
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "菜单列表", notes = "根据登录用户得到菜单列表for下拉框")
    @RequestMapping(value = "selectMenus", method = {RequestMethod.GET})
    public Res<List<SysMenu>> selectMenu(@RequestParam(required = false) @ApiParam(value = "租户id") String tenant_id) {
        List<SysMenu> menus = null;//menuService.getSelectMenu(tenant_id);
        if (SysUserUtil.isAdmin()) {// 管理员-全部菜单
            menus = menuService.getSelectMenu(tenant_id);
        } else {// 普通用户-根据角色权限显示
            menus = menuService.getUserSelectMenu();
        }
        if (menus != null) {
            return Res.OK(menus);
        } else {
            return Res.FAIL("菜单读取错误，请联系系统管理员");
        }
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "单条菜单", notes = "得到单条菜单")
    @RequestMapping(value = "menu", method = {RequestMethod.GET})
    public Res<SysMenu> menuItem(@RequestParam String menuId) {
        SysMenu sysMenu = menuService.getMenu(menuId);
        if (sysMenu != null && SysMenu.TYPE_BUTTON.equalsIgnoreCase(sysMenu.getMenuType())) {
            SysMenu pmenu = menuService.getMenu(sysMenu.getPid());
            if (pmenu != null) {
                sysMenu.setParentNode(pmenu);
                sysMenu.setComponent(pmenu.getComponent());
                sysMenu.setModuleCode(pmenu.getModuleCode());
            }
        }
        return Res.OK(sysMenu);
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "删除菜单", notes = "删除菜单")
    @RequestMapping(value = "delete", method = {RequestMethod.GET})
    public Res<SysMenu> deleteMenuTree(@RequestParam String menuId, @RequestParam(required = false) String tenant_id) {
        return Res.OK(menuService.deleteMenu(tenant_id, menuId));
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "更新菜单", notes = "更新菜单")
    @RequestMapping(value = "update", method = {RequestMethod.POST})
    public Res<SysMenu> menuUpdate(@RequestBody SysMenu menu) {
        return Res.OK(menuService.updateMenu(menu));
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "新增菜单", notes = "新增菜单")
    @RequestMapping(value = "add", method = {RequestMethod.POST})
    public Res<SysMenu> menuAdd(@RequestBody SysMenu menu) {
        menu.setId(TMUID.getUID());
        return Res.OK(menuService.addMenu(menu));
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "菜单树形", notes = "菜单树形")
    @RequestMapping(value = "tree", method = {RequestMethod.GET})
    public Res<SysMenu> getMenuTree(@RequestParam String id) {
        SysMenu menus = null;
        if (id == null || "".equals(id.trim()) || "root".equalsIgnoreCase(id.trim())) {
            menus = menuService.getMenuRoot();
        } else {
            menus = menuService.getMenuNode(id.trim());
        }
        if (menus == null) {
            throw new RuntimeException("没有查找到菜单，请联系管理员");
        } else {
            return Res.OK(menus);
        }
    }

    @ApiOperation(value = "角色（个人）树形菜单设置", notes = "角色（个人）树形菜单设置")
    @RequestMapping(value = "roleMenuTree", method = {RequestMethod.GET})
    public Res<?> roleMenuTree(@RequestParam String type, @RequestParam String dataid) {
//		SysMenu menus = menuService.getMenuRoot();
        SysMenu menus = userPermSer.getUserMenuPerm();
        if (menus == null) {
            throw new RuntimeException("没有查找到菜单，请联系管理员");
        } else {
            menus.setMenuName("权限资源树");
            /* dataid 为角色id 或者 人员id */
            List<SysMenu> list = new ArrayList<SysMenu>();
            list.add(menuService.getPermMenuTree(menus));
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("menus", list);
            if ("role".equals(type)) {// 角色拥有的权限
                map.put("perms", rolePermSer.getRolePerm(dataid, true));
            } else if ("user".equals(type)) {// 个人权限
                map.put("perms", userPermSer.getUserPerm(dataid, true));
            }
            return Res.OK(map);
        }
    }

    @ApiOperation(value = "角色（个人）树形菜单设置", notes = "角色（个人）树形菜单设置")
    @RequestMapping(value = "roleMenuTreePerm", method = {RequestMethod.GET})
    public Res<?> roleMenuTree(@RequestParam String type, @RequestParam String dataid, @RequestParam String objId) {
//		SysMenu menus = menuService.getMenuRoot();
        SysMenu menus = userPermSer.getUserMenuPerm(objId);
        if (menus == null) {
            menus = new SysMenu();
            menus.setMenuName("权限资源树");
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("menus", new ArrayList<SysMenu>());
            return Res.OK(map);
            // throw new RuntimeException("没有查找到菜单，请联系管理员");
        } else {
            menus.setMenuName("权限资源树");
            /* dataid 为角色id 或者 人员id */
            List<SysMenu> list = new ArrayList<SysMenu>();
            list.add(menuService.getPermMenuTree(menus));
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("menus", list);
            if ("role".equals(type)) {// 角色拥有的权限
                map.put("perms", rolePermSer.getRolePerm(dataid, true));
            } else if ("user".equals(type)) {// 个人权限
                map.put("perms", userPermSer.getUserPerm(dataid, true));
            }
            return Res.OK(map);
        }
    }

    @ApiOperation(value = "菜单移动", notes = "同一层级菜单移动")
    @RequestMapping(value = "moveMenu", method = {RequestMethod.GET})
    public Res<?> moveMenu(@RequestParam String posNodeId, @RequestParam String desNodeId, @RequestParam String type,
                           @RequestParam(required = false) String tenant_id) {
        boolean before = true;
        if ("up".equals(type)) {// 上移
            before = true;
        } else if ("down".equals(type)) {// 下移
            before = false;
        }
        menuService.move(tenant_id, posNodeId, desNodeId, before);
        return Res.OK();
    }

    @ApiOperation(value = "菜单转换", notes = "TM3菜单转换")
    @RequestMapping(value = "conversion", method = {RequestMethod.GET})
    public Res<?> conversionMenu(@RequestParam String menu) {
        String menu_new = menuService.getMenuConversion(menu);
        return Res.OK(menu_new);
    }

}
