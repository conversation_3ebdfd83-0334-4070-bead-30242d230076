package com.yunhesoft.outInterface.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.outInterface.entity.po.PortAffairsOilDetail;
import com.yunhesoft.outInterface.entity.po.PortAffairsProductDetail;
import com.yunhesoft.outInterface.service.IPortAffairsService;
import com.yunhesoft.system.tools.classExec.entry.po.MtmLookUpColumn;
import com.yunhesoft.system.tools.classExec.entry.po.MtmLookUpReg;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaTreeVo;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaValueVo;
import com.yunhesoft.system.tools.classExec.service.SysClassExecService;
import com.yunhesoft.system.tools.classExec.utils.MtmFormulaModel;

import lombok.extern.log4j.Log4j2;
//必须带@Service，否则工厂无法实例化
@Service
@Log4j2
public class PortAffairsFormulaForMTM extends MtmFormulaModel {
	
	@Autowired
	private SysClassExecService sysClassService;
	
	@Autowired
	private IPortAffairsService ipService;
	
	@Override
	public String getModuleCode() {
		// TODO Auto-generated method stub
		return "portAffairs";//本模块的编码
	}

	@Override
	public String getModuleName() {
		// TODO Auto-generated method stub
		return "港务系统";//本模块的名称
	}
	/**
	 * 获取公式tree
	 * @category 获取公式tree
	 * <AUTHOR> 
	 * @param pId 父ID 加载根节点时会传入机构代码(为了扩展性，这里可能传入多个机构代码，用逗号分隔)
	 * @param isRootLoad 是否在加载根节点
	 * @return
	 */
	@Override
	protected List<MtmFormulaTreeVo> getFormulaTree(String pId,boolean isRootLoad) {
		// TODO Auto-generated method stub
		List<MtmFormulaTreeVo> result = new ArrayList<MtmFormulaTreeVo>();
//		MtmFormulaTreeVo fl1 = new MtmFormulaTreeVo();
//		fl1.setFormulaName("分类1");
//		fl1.setIsLeaf(0);//非公式节点
//		result.add(fl1);//添加到根节点
//		
//		MtmFormulaTreeVo fl1_1 = new MtmFormulaTreeVo();
//		fl1_1.setFormulaName("分类1_1");
//		fl1_1.setIsLeaf(0);//非公式节点
//		fl1.getChildren().add(fl1_1);//添加到分类1下
//		
//		MtmFormulaTreeVo zb1 = new MtmFormulaTreeVo();
//		zb1.setFormulaName("指标1");
//		zb1.setFormulaCode("a.zb1");
//		zb1.setFormulaDesc("这个指标1公式的说明信息");
//		zb1.setIsLeaf(1);//公式节点
//		fl1_1.getChildren().add(zb1);//添加到分类1_1下
//		
//		MtmFormulaTreeVo fl2 = new MtmFormulaTreeVo();
//		fl2.setFormulaName("分类2");
//		fl2.setIsLeaf(0);//非公式节点
//		result.add(fl2);//添加到根节点
//		
//		MtmFormulaTreeVo zb2 = new MtmFormulaTreeVo();
//		zb2.setFormulaName("指标2");
//		zb2.setFormulaCode("b.zb2");
//		zb2.setFormulaDesc("这个指标2公式的说明信息");
//		zb2.setIsLeaf(1);//公式节点
//		fl2.getChildren().add(zb2);//添加到分类2下
		return result;
	}
	/**
	 * 解析公式
	 * @param startDt 开始日期
	 * @param endDt 截止日期
	 * @param formulaTextList 公式列表
	 */
	@Override
	protected List<MtmFormulaValueVo> getFormulaValue(String startDt, String endDt,
			List<String> formulaTextList,List<MtmFormulaValueVo> formulaTextObjList) {
		// TODO Auto-generated method stub
		//传入的内容 startDt = 2024-01 endDt = 2024-03 formulaTextList = [a.zb1,b.zb2]  传进来的公式，就是之前树形上的公式编码
		
		List<MtmFormulaValueVo> reuslt = new ArrayList<MtmFormulaValueVo>();
//		//解析结果
//		MtmFormulaValueVo zb1_1 = new MtmFormulaValueVo();
//		zb1_1.setFormulaText("a.zb1");//把传入的公式写到这个里
//		zb1_1.setObjCode("JGDM111111");//这里存对象ID 可以是人员id 岗位id 机构id
//		zb1_1.setObjType(1);//这个是code的类型1：机构，2：岗位，3：人员
//		zb1_1.setFormulaValue("15.6");
//		reuslt.add(zb1_1);
//		
//		MtmFormulaValueVo zb1_2 = new MtmFormulaValueVo();
//		zb1_2.setFormulaText("a.zb1");//把传入的公式写到这个里
//		zb1_2.setObjCode("JGDM222222");//这里存对象ID 可以是人员id 岗位id 机构id
//		zb1_2.setObjType(1);//这个是code的类型1：机构，2：岗位，3：人员
//		zb1_2.setFormulaValue("-5.8");
//		reuslt.add(zb1_2);
//		//一条公式如果只能解析出一个结果，那就不需要加objCode和type了
//		//如果这个是有指向性的数据还要叫，比如取装置平稳率，公式出的是装置下5个班组的结果，如果只查到了1个班组（这时就是解析出1个结果了），那就还是需要加上班组代码（objCode和objType）
//		//如果这个就是只出一个结果的公式，比如取装置总平稳率，公式出的就只有该装置的总平稳率，那就不要加上objCode和objType了
//		MtmFormulaValueVo zb2_1 = new MtmFormulaValueVo();
//		zb2_1.setFormulaText("b.zb2");//把传入的公式写到这个里
//		//zb2_1.setObjCode("ZYID111111");//这里存对象ID 可以是人员id 岗位id 机构id
//		//zb2_1.setObjType(3);//这个是code的类型1：机构，2：岗位，3：人员
//		zb2_1.setFormulaValue("11.2");
//		reuslt.add(zb2_1);

		return reuslt;
	}
	/**
	 * 根据条件获取模块内部数据
	 * @category 根据条件获取模块内部数据 
	 * <AUTHOR> 
	 * @param startDt 开始日期
	 * @param endDt 截止日期
	 * @param queryList 查询条件列表 MtmFormulaValueVo.objType 类型（机构、岗位、人员） MtmFormulaValueVo.objCode 对应类型的代码 MtmFormulaValueVo.paramValue存放要取数据的表
	 * @return 查询结果转为 json存入MtmFormulaValueVo.paramResult中
	 */
	@Override
	public void getJsonData(String startDt, String endDt, List<MtmFormulaValueVo> queryList) {
		// TODO Auto-generated method stub
		if(StringUtils.isNotEmpty(startDt) && StringUtils.isNotEmpty(endDt) && StringUtils.isNotEmpty(queryList)) {//参数有效，必须有开始截止日期
			HashMap<String,HashSet<String>> moduleCodeMap = new HashMap<String,HashSet<String>>();
			HashMap<String,List<MtmFormulaValueVo>> dataMap = new HashMap<String,List<MtmFormulaValueVo>>();
			getTableConfig(queryList,moduleCodeMap,dataMap);					
			if(moduleCodeMap.size()>0) {//有分组数据
				for(Entry<String, HashSet<String>> temp:moduleCodeMap.entrySet()) {
					HashMap<String,String> tableDataMap =  getTableData(temp.getKey(), startDt,  endDt,temp.getValue());//获取表数据
					if(tableDataMap!=null && tableDataMap.size()>0) {
						List<MtmFormulaValueVo> voList = dataMap.get(temp.getKey());//获取查表数据列表
						if(voList!=null) {
							for(MtmFormulaValueVo tempVo:voList) {
								String jsonData = tableDataMap.get(tempVo.getParamValue()+"_"+tempVo.getObjCode());//获取查到的数据
								if(StringUtils.isNotEmpty(jsonData)) {//获取到了数据
									tempVo.setParamResult(jsonData);
								}
							}
						}
					}
				}
			}
		}
	}
	/**
	 * 保存模块数据
	 * @category 保存模块数据
	 * <AUTHOR> 
	 * @param saveList 要保存的数据列表，json格式，数据存储在MtmFormulaValueVo.formulaValue字段中，对应表存储在MtmFormulaValueVo.paramValue中
	 * @return
	 */
	@Override
	public boolean saveJsonData(List<MtmFormulaValueVo> saveList) {
		// TODO Auto-generated method stub
		boolean result = false;
		HashMap<String,HashSet<String>> moduleCodeMap = new HashMap<String,HashSet<String>>();
		HashMap<String,List<MtmFormulaValueVo>> dataMap = new HashMap<String,List<MtmFormulaValueVo>>();
		getTableConfig(saveList,moduleCodeMap,dataMap);					
		if(moduleCodeMap.size()>0) {//有分组数据
			for(Entry<String, List<MtmFormulaValueVo>> temp:dataMap.entrySet()) {
				if("PORTAFFAIRS_PRODUCT".equals(temp.getKey())) {//产品
					result = ipService.updatePortAffairsProduct(getSaveData(temp.getValue(),PortAffairsProductDetail.class));//保存数据
				}else if("PORTAFFAIRS_OIL".equals(temp.getKey())) {//原油
					result = ipService.updatePortAffairsOil(getSaveData(temp.getValue(),PortAffairsOilDetail.class));//保存数据
				}
			}
		}
		return result;
	}
	/**
	 * 公式模块数据初始化
	 * @category 
	 * <AUTHOR>
	 */
	@Override
	public void init() {
		// TODO Auto-generated method stub
		//查表表格注册
		List<MtmLookUpReg> regList = new ArrayList<MtmLookUpReg>();
		List<MtmLookUpColumn> columnList = new ArrayList<MtmLookUpColumn>();
		
//		//产品码头查表
//		PORTAFFAIRS_PRODUCT_STOP 靠泊用时
		regList.add(this.getRegObject("PORTAFFAIRS_PRODUCT", "PORTAFFAIRS_PRODUCT_STOP", "产品-靠泊用时", 1));
		columnList.addAll(getProductColumn("PORTAFFAIRS_PRODUCT_STOP"));	
//		PORTAFFAIRS_PRODUCT_PUMP 接管开泵
		regList.add(this.getRegObject("PORTAFFAIRS_PRODUCT", "PORTAFFAIRS_PRODUCT_PUMP", "产品-接管开泵", 2));
		columnList.addAll(getProductColumn("PORTAFFAIRS_PRODUCT_PUMP"));
//		PORTAFFAIRS_PRODUCT_LOADING 装卸货
		regList.add(this.getRegObject("PORTAFFAIRS_PRODUCT", "PORTAFFAIRS_PRODUCT_LOADING", "产品-装卸货", 3));
		columnList.addAll(getProductColumn("PORTAFFAIRS_PRODUCT_LOADING"));
//		PORTAFFAIRS_PRODUCT_LEAVE 拆管离泊
		regList.add(this.getRegObject("PORTAFFAIRS_PRODUCT", "PORTAFFAIRS_PRODUCT_LEAVE", "产品-拆管离泊", 4));
		columnList.addAll(getProductColumn("PORTAFFAIRS_PRODUCT_LEAVE"));
	
//		//原油码头查表
//		PORTAFFAIRS_OIL_CHECK 卸船前检查
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_CHECK", "原油-卸船前检查", 1));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_CHECK"));
//		PORTAFFAIRS_OIL_PREPARE 备缆 
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_PREPARE", "原油-备缆", 2));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_PREPARE"));
//		PORTAFFAIRS_OIL_MOORING 靠泊系缆
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_MOORING", "原油-靠泊系缆", 3));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_MOORING"));
//		PORTAFFAIRS_OIL_PIPELINE 接输油臂
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_PIPELINE", "原油-接输油臂", 4));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_PIPELINE"));
//		PORTAFFAIRS_OIL_DISCONNECT 拆臂
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_DISCONNECT", "原油-拆臂", 5));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_DISCONNECT"));
//		PORTAFFAIRS_OIL_UNLOCKING 解缆
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_UNLOCKING", "原油-解缆", 6));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_UNLOCKING"));
//		PORTAFFAIRS_OIL_MOORING_TIME 系缆时间
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_MOORING_TIME", "原油-系缆时间", 7));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_MOORING_TIME"));
//		PORTAFFAIRS_OIL_PIPELINE_TIME 接臂时间
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_PIPELINE_TIME", "原油-接臂时间", 8));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_PIPELINE_TIME"));
//		PORTAFFAIRS_OIL_DISCONNECT_TIME 吹扫拆臂时间
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_DISCONNECT_TIME", "原油-吹扫拆臂时间", 9));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_DISCONNECT_TIME"));
//		PORTAFFAIRS_OIL_METERING_TIME 联检和计量之间衔接时间
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_METERING_TIME", "原油-联检和计量之间衔接时间", 10));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_METERING_TIME"));
//		PORTAFFAIRS_OIL_PUMP_TIME 计量完成和起泵之间时间
		regList.add(this.getRegObject("PORTAFFAIRS_OIL", "PORTAFFAIRS_OIL_PUMP_TIME", "原油-计量完成和起泵之间时间", 11));
		columnList.addAll(getOilColumn("PORTAFFAIRS_OIL_PUMP_TIME"));
		
		sysClassService.registerLookUpCfg(getModuleCode(), regList, columnList);
	}
	/**
	 * 生成查表注册信息
	 * @category 
	 * <AUTHOR> 
	 * @param moduleCode 模块编码 产品/原油
	 * @param tableCode 表编码
	 * @param tableName 表名称
	 * @param sort 排序
	 * @return
	 */
	private MtmLookUpReg getRegObject(String moduleCode,String tableCode,String tableName,Integer sort) {
		MtmLookUpReg bean = new MtmLookUpReg();
		bean.setId(tableCode);//用表编码做为ID
		bean.setModuleCode(moduleCode);//接口编码
		bean.setTableCode(tableCode);//数据表编码
		bean.setTableName(tableName);//数据表名称
		bean.setFormulaModelCode(getModuleCode());//公式模块编码
		bean.setTmSort(sort);//排序
		return bean;
	}
	/**
	 * 生成查表列信息
	 * @category 生成查表列信息
	 * <AUTHOR> 
	 * @param moduleCode 模块编码 产品/原油
	 * @param tableCode 表编码
	 * @param columnCode 列编码
	 * @param columnName 列名称
	 * @param columnType 列类型 string or number
	 * @param sort 排序
	 * @return
	 */
	private MtmLookUpColumn getColumnObject(String moduleCode,String tableCode,String columnCode,String columnName,String columnType,Integer sort) {
		MtmLookUpColumn bean = new MtmLookUpColumn();
		bean.setModuleCode(moduleCode);//接口编码
		bean.setTableCode(tableCode);//数据表编码
		bean.setFormulaModelCode(getModuleCode());//公式模块编码
		bean.setColumnCode(columnCode);//列编码
		bean.setColumnName(columnName);//列名称
		bean.setColumnType(columnType);//列类型 string or number
		bean.setTmSort(sort);//排序
		return bean;
	}
	/**
	 * 获取产品查表字段
	 * @category 获取产品查表字段
	 * <AUTHOR> 
	 * @param tableCode 表编码
	 * @return
	 */
	private List<MtmLookUpColumn> getProductColumn(String tableCode){
		List<MtmLookUpColumn> result = new ArrayList<MtmLookUpColumn>();
		int i=1;
		result.add(getColumnObject("PORTAFFAIRS_PRODUCT",tableCode,"shipName","船名","string",i++));
		result.add(getColumnObject("PORTAFFAIRS_PRODUCT",tableCode,"shipType","船型","string",i++));
		result.add(getColumnObject("PORTAFFAIRS_PRODUCT",tableCode,"startTime","开始时间","string",i++));
		result.add(getColumnObject("PORTAFFAIRS_PRODUCT",tableCode,"endTime","截止时间","string",i++));
		result.add(getColumnObject("PORTAFFAIRS_PRODUCT",tableCode,"operateTypeName","操作类型","string",i++));
		result.add(getColumnObject("PORTAFFAIRS_PRODUCT",tableCode,"operateHour","用时","number",i++));
		result.add(getColumnObject("PORTAFFAIRS_PRODUCT",tableCode,"score","得分","number",i++));
		return result;
	}
	/**
	 * 获取原油查表字段
	 * @category 获取原油查表字段
	 * <AUTHOR> 
	 * @param tableCode 表编码
	 * @return
	 */
	private List<MtmLookUpColumn> getOilColumn(String tableCode){
		List<MtmLookUpColumn> result = new ArrayList<MtmLookUpColumn>();
		int i=1;
		result.add(getColumnObject("PORTAFFAIRS_OIL",tableCode,"shipName","船名","string",i++));
		result.add(getColumnObject("PORTAFFAIRS_OIL",tableCode,"shipType","船型","string",i++));
		result.add(getColumnObject("PORTAFFAIRS_OIL",tableCode,"startTime","开始时间","string",i++));
		result.add(getColumnObject("PORTAFFAIRS_OIL",tableCode,"endTime","截止时间","string",i++));
		result.add(getColumnObject("PORTAFFAIRS_OIL",tableCode,"operateTypeName","操作类型","string",i++));
		result.add(getColumnObject("PORTAFFAIRS_OIL",tableCode,"operateHour","用时","number",i++));
		result.add(getColumnObject("PORTAFFAIRS_OIL",tableCode,"score","得分","number",i++));
		return result;
	}
	/**
	 * 获取查表配置信息
	 * @category 
	 * <AUTHOR> 
	 * @param dataList 数据列表
	 * @param moduleCodeMap  模块map
	 * @param dataMap 数据map
	 */
	private void getTableConfig(List<MtmFormulaValueVo> dataList,HashMap<String,HashSet<String>> moduleCodeMap,HashMap<String,List<MtmFormulaValueVo>> dataMap){
		List<MtmLookUpReg>	regList = sysClassService.getLookUpRegListByFormulaModelCode(getModuleCode());//获取table列表	
		if (StringUtils.isNotEmpty(regList)){//获取到了列表
			HashMap<String,String> regMap = new HashMap<String,String>();
			for(MtmLookUpReg temp:regList) {
				regMap.put(temp.getTableCode(), temp.getModuleCode());
			}
			for(MtmFormulaValueVo temp:dataList) {
				String moduleCode = regMap.get(temp.getParamValue());//通过tableCode获取moduleCode
				if(StringUtils.isNotEmpty(moduleCode)) {
					HashSet<String> tableList = moduleCodeMap.get(moduleCode);
					if(tableList==null) {
						tableList = new HashSet<String>();
						moduleCodeMap.put(moduleCode, tableList);
					}
					tableList.add(temp.getParamValue());//对表进行分组，后续用户查找真实表
					
					List<MtmFormulaValueVo> voList = dataMap.get(moduleCode);
					if(voList==null) {
						voList = new ArrayList<MtmFormulaValueVo>();
						dataMap.put(moduleCode, voList);
					}
					voList.add(temp);//对表进行分组，后续用户查找真实表
				}
			}
		}
	}
	
	
	
	
	/**
	 * 获取接口表数据
	 * @category 
	 * <AUTHOR> 
	 * @param moduleCode 模块编码
	 * @param startDt 开始日期
	 * @param endDt 截止日期
	 * @param tableList tableCode列表
	 * @return HashMap<String,String> key tableCode value jsonArr数据例： [{a:1,b:2},{a:2,b:3}]
	 */
	private HashMap<String,String> getTableData(String moduleCode,String startDt, String endDt, HashSet<String> tableCodeList){
		HashMap<String,String> result = new HashMap<String,String>();
		List<String> queryList = new ArrayList<String>();
		if(tableCodeList!=null) {
			queryList.addAll(tableCodeList);
		}
		if("PORTAFFAIRS_PRODUCT".equals(moduleCode)) {//产品
			result = getQueryData(this.getPortAffairsProduct(startDt, endDt, queryList),PortAffairsProductDetail.class);
		}else if("PORTAFFAIRS_OIL".equals(moduleCode)) {//原油
			result = getQueryData(this.getPortAffairsOil(startDt, endDt, queryList),PortAffairsOilDetail.class);
		}
		return result;
	}
	/**
	 * 获取产品码头进港数据
	 * @category 获取产品码头进港数据
	 * <AUTHOR> 
	 * @param startDt
	 * @param endDt
	 * @param tableCodeList
	 * @return
	 */
	private HashMap<String,List<PortAffairsProductDetail>> getPortAffairsProduct(String startDt, String endDt, List<String> tableCodeList){
		HashMap<String,List<PortAffairsProductDetail>> result = new HashMap<String,List<PortAffairsProductDetail>>();
		List<PortAffairsProductDetail> queryList = ipService.getPortAffairsProduct(startDt, endDt, tableCodeList);
		if (StringUtils.isNotEmpty(queryList)){//获取到了列表
			for(PortAffairsProductDetail temp:queryList) {
				String key = temp.getOperateType()+"_"+temp.getOrgCode();
				List<PortAffairsProductDetail> dataList = result.get(key);
				if(dataList==null) {
					dataList = new ArrayList<PortAffairsProductDetail>();
					result.put(key, dataList);
				}
				dataList.add(temp);
			}
		}
		return result;
	}
	/**
	 * 获取原油码头进港数据
	 * @category 获取产品码头进港数据
	 * <AUTHOR> 
	 * @param startDt
	 * @param endDt
	 * @param tableCodeList
	 * @return
	 */
	private HashMap<String,List<PortAffairsOilDetail>> getPortAffairsOil(String startDt, String endDt, List<String> tableCodeList){
		HashMap<String,List<PortAffairsOilDetail>> result = new HashMap<String,List<PortAffairsOilDetail>>();
		List<PortAffairsOilDetail> queryList = ipService.getPortAffairsOil(startDt, endDt, tableCodeList);
		if (StringUtils.isNotEmpty(queryList)){//获取到了列表
			for(PortAffairsOilDetail temp:queryList) {
				String key = temp.getOperateType()+"_"+temp.getOrgCode();
				List<PortAffairsOilDetail> dataList = result.get(key);
				if(dataList==null) {
					dataList = new ArrayList<PortAffairsOilDetail>();
					result.put(key, dataList);
				}
				dataList.add(temp);
			}
		}
		return result;
	}
	
	/**
	 * 转换数据为json
	 * @category 
	 * <AUTHOR> 
	 * @param dataMap 数据map
	 * @param <T>
	 * @return HashMap<String,String>  key tableCode value jsonArr数据例： [{a:1,b:2},{a:2,b:3}]
	 */
	private <T> HashMap<String,String> getQueryData(HashMap<String, List<T>> dataMap,Class<T> cls){
		HashMap<String,String> result = new HashMap<String,String>();
		if(dataMap!=null && dataMap.size()>0) {
			for(Entry<String, List<T>> temp:dataMap.entrySet()) {
				result.put(temp.getKey(), JSON.toJSONString(temp.getValue()));//转换成json
			}
		}
		return result;
	}
	/**
	 * 转换为数据bean
	 * @category 
	 * <AUTHOR> 
	 * @param <T>
	 * @param dataList 
	 * @param cls
	 * @return List<T>
	 */
	private <T> List<T> getSaveData(List<MtmFormulaValueVo> dataList,Class<T> cls){
		List<T> result = new ArrayList<T>();
		if (StringUtils.isNotEmpty(dataList)){//有数据
			for(MtmFormulaValueVo temp:dataList) {
				if (StringUtils.isNotEmpty(temp.getParamResult())){//有要保存的数据
					try {
						JSONArray arr = JSON.parseArray(temp.getParamResult());
						for (int i = 0,j=arr.size();i < j; i++) {
							String row = arr.getString(i);
							T obj =JSON.parseObject(row, cls);
							if(obj!=null) {
								result.add(obj);
							}
						}
					}catch(Exception e) {
						log.error("", e);
					}
				}
			}
		}
		return result;
	}

}
