package com.yunhesoft.outInterface.util;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.ObjUtilsReflection;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.outInterface.entity.po.PortAffairsOilDetail;
import com.yunhesoft.outInterface.entity.po.PortAffairsOilInfo;
import com.yunhesoft.outInterface.entity.po.PortAffairsProductDetail;
import com.yunhesoft.outInterface.entity.po.PortAffairsProductInfo;
import com.yunhesoft.outInterface.entity.po.PortAffairsShip;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.synchronous.utils.SynModel;

import lombok.extern.log4j.Log4j2;
@Log4j2
@Service
public class SynPortAffairs extends SynModel {
	private static final String wharf_cpmt="CPMT";//产品码头
	private static final String wharf_yymt="YYMT";//原油码头
	@Autowired
	private EntityService entityService;
	
	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList,HashMap<String,String> synParam) {
		// TODO Auto-generated method stub
		//System.out.println(JSON.toJSONString(dataList));
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>();//失败返回的数据		
//		this.rootId = synParam.get("rootId");//外部系统机构根节点ID
//		this.synDelFlagValue = synParam.get("synDelFlagValue");//外部数据同步删除标识值
//		if(StringUtils.isEmpty(this.synDelFlagValue)) {
//			this.synDelFlagValue="1";//默认标识1为删除
//		}
//		String rootSynStr = synParam.get("rootSyn");//是否要同步外部系统的根节点
//		if("false".equals(rootSynStr)) {
//			this.rootSyn = false;
//		}
		if(StringUtils.isNotEmpty(dataList)) {
			List<PortAffairsProductInfo> productList = new ArrayList<PortAffairsProductInfo>();//产品码头
			List<PortAffairsOilInfo> oilList = new ArrayList<PortAffairsOilInfo>();//原油码头
			for(HashMap<String, Object> temp:dataList) {
				Object wharf = temp.get("wharf");//码头 "wharf":"berth_id": "CPMT_008",  "wharf": "berth_id": "YYMT_001",
				if(wharf!=null) {
					String wharfStr = wharf.toString();
					if(wharfStr.startsWith(wharf_cpmt)) {//产品码头 "wharf":"berth_id": "CPMT_008"
						PortAffairsProductInfo bean = ObjUtils.convertToObject(PortAffairsProductInfo.class, temp);//由map生成机构类
						if(bean!=null) {
							bean.setWharf(wharf_cpmt);
							productList.add(bean);
						}
					}else if(wharfStr.startsWith(wharf_yymt)) {//原油码头 "wharf": "berth_id": "YYMT_001",
						PortAffairsOilInfo bean = ObjUtils.convertToObject(PortAffairsOilInfo.class, temp);//由map生成机构类
						if(bean!=null) {
							bean.setWharf(wharf_yymt);
							oilList.add(bean);
						}
					}
				}
			}
			synProduct(productList);//同步产品码头数据
			synOil(oilList);//同步原油码头数据
			
		}
//		if(StringUtils.isNotEmpty(rootId)) {}else {
//			this.errorInfo="未指定外部数据根节点ID，无法同步机构数据";
//		}
		return result;
	}
	/**
	 * 同步产品码头数据
	 * @category 
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	private boolean synProduct(List<PortAffairsProductInfo> dataList) {
		boolean result = false;
		if(StringUtils.isNotEmpty(dataList)) {
			Map<String,PortAffairsShip> shipMap = null;// 船数据map
			Where where = Where.create();
			where.notNull(PortAffairsShip::getShipName);
			List<PortAffairsShip> queryList = entityService.queryList(PortAffairsShip.class, where);
			if (StringUtils.isNotEmpty(queryList)) {// 不为空
				shipMap = queryList.stream().collect(
						Collectors.toMap(PortAffairsShip::getShipName, PortAffairsShip -> PortAffairsShip, (key1, key2) -> key1));// 将list转换为map
			}
			if(shipMap==null) {
				shipMap= new HashMap<String, PortAffairsShip>();
			}
			List<PortAffairsProductInfo> addInfoList = new ArrayList<PortAffairsProductInfo>();
			List<PortAffairsProductDetail> addDetailList = new ArrayList<PortAffairsProductDetail>();
			ObjUtilsReflection dataReflection = new ObjUtilsReflection(PortAffairsProductInfo.class);// 生成反射用结构体
			List<String> idList = new ArrayList<String>();			
			for(PortAffairsProductInfo temp:dataList) {
				if(StringUtils.isNotEmpty(temp.getId())) {//有数据ID
					if(StringUtils.isEmpty(temp.getShipType())) {
						PortAffairsShip ship = shipMap.get(temp.getShipName());//根据船名获取船型
						if(ship!=null) {
							temp.setShipType(ship.getShipType());//根据配置设置船型
						}
					}
					if(temp.getArrivalDate()!=null && temp.getArrivalDate().length()>=10) {
						temp.setArrivalDate(temp.getArrivalDate().substring(0,10));//时间正确，截取前10位
					}else {
						if(temp.getActualBerthTime()!=null && temp.getActualBerthTime().length()>=10) {
							temp.setArrivalDate(temp.getArrivalDate().substring(0,10));//取实际靠泊时间为进港日期
						}
					}
					if(StringUtils.isNotEmpty(temp.getArrivalDate())) {//获取到了进港日期
						idList.add(temp.getId());
						addInfoList.add(temp);
	//					靠泊用时：通知进港时间 - 系缆完毕时间（目前没有）
	//					接管开泵：储运开泵时间-船方见油时间
	//					装卸货：装卸货开泵时间-装卸货停泵时间
	//					拆管离泊：装卸货停泵时间 - 解缆完毕时间（目前没有）
						this.createProductDetail("notifiEntryTime", "actualBerthTime", "PORTAFFAIRS_PRODUCT_STOP","靠泊用时", dataReflection, temp, shipMap, addDetailList);//靠泊用时
						this.createProductDetail("pumpingTime", "theshipOilTime", "PORTAFFAIRS_PRODUCT_PUMP","接管开泵", dataReflection, temp, shipMap, addDetailList);//接管开泵
						this.createProductDetail("unloadpumpingstrTime", "unloadpumpingstopTime", "PORTAFFAIRS_PRODUCT_LOADING","装卸货", dataReflection, temp, shipMap, addDetailList);//装卸货
						this.createProductDetail("unloadpumpingstopTime", "actualUnberthingTime", "PORTAFFAIRS_PRODUCT_LEAVE","拆管离泊", dataReflection, temp, shipMap, addDetailList);//拆管离泊
					}else {
						log.error( "产品码头接口未获取到正确的进港时间：ID:"+temp.getId()+" ship:"+temp.getShipName()+" time:"+temp.getArrivalDate()+"--"+temp.getActualBerthTime());
					}
				}else {
					log.error( "产品码头接口未获取到正确的数据ID：ID:"+temp.getId()+" ship:"+temp.getShipName()+" time:"+temp.getArrivalDate()+"--"+temp.getActualBerthTime());
				}
			}
			if(idList.size()>0) {
				List<PortAffairsProductInfo> oldInfoList= getProductInfo(idList);
				if(StringUtils.isNotEmpty(oldInfoList)) {//获取到了数据
					entityService.deleteByIdBatch(oldInfoList, 1000);//删除数据
				}
				List<PortAffairsProductDetail> oldDetailList= getProductInfoDetail(idList);				
				if(StringUtils.isNotEmpty(oldDetailList)) {//获取到了数据
					entityService.deleteByIdBatch(oldDetailList, 1000);//删除数据
				}
				if(StringUtils.isNotEmpty(addInfoList)) {//获取到了数据
					entityService.insertBatch(addInfoList, 1000);//插入数据
				}
				if(StringUtils.isNotEmpty(addDetailList)) {//获取到了数据
					entityService.insertBatch(addDetailList, 1000);//插入数据
				}
				result = true;
			}
		}
		return result;
	}
	/**
	 * 获取已采集的记录（产品码头）
	 * @category 
	 * <AUTHOR> 
	 * @param idList 
	 * @return
	 */
	private List<PortAffairsProductInfo> getProductInfo(List<String> idList){
		List<PortAffairsProductInfo> result = new ArrayList<PortAffairsProductInfo>();
		if(StringUtils.isNotEmpty(idList)) {//有数据ID
			Where where = Where.create();
			where.in(PortAffairsProductInfo::getId, idList.toArray());
			List<PortAffairsProductInfo> queryList = entityService.queryList(PortAffairsProductInfo.class, where);
			if(StringUtils.isNotEmpty(queryList)) {//获取到了数据
				result=queryList;
			}
		}
		return result;
	}
	/**
	 * 获取已采集的记录（产品码头）
	 * @category 
	 * <AUTHOR> 
	 * @param idList 
	 * @return
	 */
	private List<PortAffairsProductDetail> getProductInfoDetail(List<String> idList){
		List<PortAffairsProductDetail> result = new ArrayList<PortAffairsProductDetail>();
		if(StringUtils.isNotEmpty(idList)) {//有数据ID
			Where where = Where.create();
			where.in(PortAffairsProductDetail::getPId, idList.toArray());
			List<PortAffairsProductDetail> queryList = entityService.queryList(PortAffairsProductDetail.class, where);
			if(StringUtils.isNotEmpty(queryList)) {//获取到了数据
				result=queryList;
			}
		}
		return result;
	}
	/**
	 * 同步原油码头数据
	 * @category 
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	private boolean synOil(List<PortAffairsOilInfo> dataList) {
		boolean result = false;
		if(StringUtils.isNotEmpty(dataList)) {
			List<PortAffairsOilInfo> addInfoList = new ArrayList<PortAffairsOilInfo>();
			List<PortAffairsOilDetail> addDetailList = new ArrayList<PortAffairsOilDetail>();
			ObjUtilsReflection dataReflection = new ObjUtilsReflection(PortAffairsOilInfo.class);// 生成反射用结构体
			List<String> idList = new ArrayList<String>();			
			for(PortAffairsOilInfo temp:dataList) {
				if(StringUtils.isNotEmpty(temp.getId())) {//有数据ID
					idList.add(temp.getId());
					if(temp.getArrivalDate()!=null && temp.getArrivalDate().length()>=10) {
						temp.setArrivalDate(temp.getArrivalDate().substring(0,10));//时间正确，截取前10位
					}else {
						if(temp.getActualBerthTime()!=null && temp.getActualBerthTime().length()>=10) {
							temp.setArrivalDate(temp.getArrivalDate().substring(0,10));//取实际靠泊时间为进港日期
						}
					}
					if(StringUtils.isNotEmpty(temp.getArrivalDate())) {//获取到了进港日期
						idList.add(temp.getId());
						addInfoList.add(temp);
						//时间段考核
	//					系缆时间：系缆开始时间 - 系缆完毕时间（目前没有）
	//					接臂时间：接臂开始时间 - 接臂完毕时间（目前没有）
	//					吹扫拆臂时间：吹扫拆臂开始时间（目前没有） - 吹扫拆臂时间截止时间（目前没有）
	//					联检和计量之间衔接时间：联检完成时间（目前没有） -计量开始时间（目前只有计量时间，但不能确定是开始时间还是结束时间）
	//					计量完成和起泵之间时间：-计量完成时间（目前只有计量时间，但不能确定是开始时间还是结束时间）- 起泵时间（目前没有）
						this.createOilDetail("mooringlineTime", "actualUnberthingTime", "PORTAFFAIRS_OIL_MOORING_TIME","系缆时间",2, dataReflection, temp, addDetailList);//系缆时间
						this.createOilDetail("extenStrarmTime", "extenEndTime", "PORTAFFAIRS_OIL_PIPELINE_TIME","接臂时间",2, dataReflection, temp, addDetailList);//接臂时间
						this.createOilDetail("detachStrarmTime", "detachEndTime", "PORTAFFAIRS_OIL_DISCONNECT_TIME","吹扫拆臂时间",2, dataReflection, temp, addDetailList);//吹扫拆臂时间
						this.createOilDetail("unionCheckTime", "measurementStartTime", "PORTAFFAIRS_OIL_METERING_TIME","联检和计量之间衔接时间",2, dataReflection, temp, addDetailList);//联检和计量之间衔接时间
						this.createOilDetail("measurementEndTime", "risePumpingTime", "PORTAFFAIRS_OIL_PUMP_TIME","计量完成和起泵之间时间",2, dataReflection, temp, addDetailList);//计量完成和起泵之间时间
						//工作次数考核
	//					卸船前检查：有卸船前检查完成时间，则班组工作次数+1
	//					备缆：有备缆时间，则班组工作次数+1
	//					靠泊系缆：有系缆开始时间 ，则班组工作次数+1
	//					接输油臂：有接臂开始时间 ，则班组工作次数+1
	//					拆臂：有吹扫拆臂开始时间 ，则班组工作次数+1
	//					解缆：有解缆开始时间 ，则班组工作次数+1
						this.createOilDetail("unloadCheckTime", "unloadCheckTime", "PORTAFFAIRS_OIL_CHECK","卸船前检查",1, dataReflection, temp, addDetailList);//卸船前检查
						this.createOilDetail("prepareRopeTime", "prepareRopeTime", "PORTAFFAIRS_OIL_PREPARE","备缆",1, dataReflection, temp, addDetailList);//备缆
						this.createOilDetail("mooringlineTime", "mooringlineTime", "PORTAFFAIRS_OIL_MOORING","靠泊系缆",1, dataReflection, temp, addDetailList);//靠泊系缆
						this.createOilDetail("extenStrarmTime", "extenStrarmTime", "PORTAFFAIRS_OIL_PIPELINE","接输油臂",1, dataReflection, temp, addDetailList);//接输油臂
						this.createOilDetail("detachStrarmTime", "detachStrarmTime", "PORTAFFAIRS_OIL_DISCONNECT","拆臂",1, dataReflection, temp, addDetailList);//拆臂
						this.createOilDetail("unwindingTime", "unwindingTime", "PORTAFFAIRS_OIL_UNLOCKING","解缆",1, dataReflection, temp, addDetailList);//解缆
					}else {
						log.error( "原油码头接口未获取到正确的进港时间：ID:"+temp.getId()+" ship:"+temp.getShipName()+" time:"+temp.getArrivalDate()+"--"+temp.getActualBerthTime());
					}
				}else {
					log.error( "原油码头接口未获取到正确的数据ID：ID:"+temp.getId()+" ship:"+temp.getShipName()+" time:"+temp.getArrivalDate()+"--"+temp.getActualBerthTime());
				}
			}
			if(idList.size()>0) {
				List<PortAffairsOilInfo> oldInfoList= getOilInfo(idList);
				if(StringUtils.isNotEmpty(oldInfoList)) {//获取到了数据
					entityService.deleteByIdBatch(oldInfoList, 1000);//删除数据
				}
				List<PortAffairsOilDetail> oldDetailList= getOilDetail(idList);				
				if(StringUtils.isNotEmpty(oldDetailList)) {//获取到了数据
					entityService.deleteByIdBatch(oldDetailList, 1000);//删除数据
				}
				if(StringUtils.isNotEmpty(addInfoList)) {//获取到了数据
					entityService.insertBatch(addInfoList, 1000);//插入数据
				}
				if(StringUtils.isNotEmpty(addDetailList)) {//获取到了数据
					entityService.insertBatch(addDetailList, 1000);//插入数据
				}
				result = true;
			}
		}
		return result;
	}
	/**
	 * 获取已采集的记录（原油码头）
	 * @category 
	 * <AUTHOR> 
	 * @param idList 
	 * @return
	 */
	private List<PortAffairsOilInfo> getOilInfo(List<String> idList){
		List<PortAffairsOilInfo> result = new ArrayList<PortAffairsOilInfo>();
		if(StringUtils.isNotEmpty(idList)) {//有数据ID
			Where where = Where.create();
			where.in(PortAffairsOilInfo::getId, idList.toArray());
			List<PortAffairsOilInfo> queryList = entityService.queryList(PortAffairsOilInfo.class, where);
			if(StringUtils.isNotEmpty(queryList)) {//获取到了数据
				result=queryList;
			}
		}
		return result;
	}
	/**
	 * 获取已采集的记录（原油码头）
	 * @category 
	 * <AUTHOR> 
	 * @param idList 
	 * @return
	 */
	private List<PortAffairsOilDetail> getOilDetail(List<String> idList){
		List<PortAffairsOilDetail> result = new ArrayList<PortAffairsOilDetail>();
		if(StringUtils.isNotEmpty(idList)) {//有数据ID
			Where where = Where.create();
			where.in(PortAffairsOilDetail::getPId, idList.toArray());
			List<PortAffairsOilDetail> queryList = entityService.queryList(PortAffairsOilDetail.class, where);
			if(StringUtils.isNotEmpty(queryList)) {//获取到了数据
				result=queryList;
			}
		}
		return result;
	}
	
	/**
	 *  生成产品码头数据
	 * @category 
	 * <AUTHOR> 
	 * @param startDtColumn
	 * @param endDtColumn
	 * @param operateType
	 * @param dataReflection
	 * @param bean
	 * @param shipMap
	 * @param addList
	 */
	private void createProductDetail(String startDtColumn,String endDtColumn,String operateType,String operateTypeName,ObjUtilsReflection dataReflection,PortAffairsProductInfo bean,Map<String,PortAffairsShip> shipMap,List<PortAffairsProductDetail> addList) {
		Object startDt =  getReflectData(bean, startDtColumn, dataReflection);//开始时间
		Object endDt =  getReflectData(bean, endDtColumn, dataReflection);//截止时间
		if(startDt!=null && endDt!=null) {
			String startDtStr = startDt.toString();
			String endDtStr = endDt.toString();
			if(startDtStr!=null && startDtStr.length()>=10 && endDtStr!=null && endDtStr.length()>=10) {//时间格式正确
				Date startDate = DateTimeUtils.parseDate(startDtStr);
				Date endDate = DateTimeUtils.parseDate(endDtStr);
				double diffHour = Math.abs(DateTimeUtils.diffHour(endDate, startDate));//计算时间差（小时）
				PortAffairsProductDetail detailBean = new PortAffairsProductDetail();
				detailBean.setId(TMUID.getUID());
				detailBean.setPId(bean.getId());
				detailBean.setShipName(bean.getShipName());//船名
				detailBean.setShipType(bean.getShipType());//船型
				detailBean.setOrgCode(bean.getOrgCode());//班组代码
				detailBean.setOrgName(bean.getOrgName());//班组名称
				detailBean.setStartTime(DateTimeUtils.formatDateTime(startDate));//开始时间
				detailBean.setEndTime(DateTimeUtils.formatDateTime(endDate));//截止时间
				detailBean.setOperateType(operateType);//操作类型编码
				detailBean.setOperateTypeName(operateTypeName);//操作类型名称
				detailBean.setOperateDate(bean.getArrivalDate());//操作日期
				detailBean.setOperateHour(diffHour);//操作小时数
				addList.add(detailBean);
			}else {
				log.error( "产品码头接口时间格式不正确：type:"+operateTypeName+" "+startDtColumn+":"+startDtStr+" "+endDtColumn+":"+endDtStr);
			}
		}
	}
	/**
	 * 生成原油码头数据
	 * @category 
	 * <AUTHOR> 
	 * @param startDtColumn
	 * @param endDtColumn
	 * @param operateType
	 * @param operateTypeName
	 * @param beanType 1统计次数 2统计时间间隔
	 * @param dataReflection
	 * @param bean
	 * @param addList
	 */
	private void createOilDetail(String startDtColumn,String endDtColumn,String operateType,String operateTypeName,int beanType,ObjUtilsReflection dataReflection,PortAffairsOilInfo bean,List<PortAffairsOilDetail> addList) {
		Object startDt =  getReflectData(bean, startDtColumn, dataReflection);//开始时间
		Object endDt =  getReflectData(bean, endDtColumn, dataReflection);//截止时间
		if(startDt!=null && endDt!=null) {
			String startDtStr = startDt.toString();
			String endDtStr = endDt.toString();
			if(startDtStr!=null && startDtStr.length()>=10 && endDtStr!=null && endDtStr.length()>=10) {//时间格式正确
				Date startDate = DateTimeUtils.parseDate(startDtStr);
				Date endDate = DateTimeUtils.parseDate(endDtStr);
				double diffHour = 0d;//时间差（分钟） 或次数（次数时为1） 
				if(beanType==2) {//时间差
					diffHour = (double)Math.abs(DateTimeUtils.diffSecond(endDate, startDate)/60);//计算时间差（分钟）
				}else {//次数
					diffHour = 1d;
				}
				PortAffairsOilDetail detailBean = new PortAffairsOilDetail();
				detailBean.setId(TMUID.getUID());
				detailBean.setPId(bean.getId());
				detailBean.setShipName(bean.getShipName());//船名
				detailBean.setShipType(bean.getShipType());//船型
				detailBean.setOrgCode(bean.getOrgCode());//班组代码
				detailBean.setOrgName(bean.getOrgName());//班组名称
				detailBean.setStartTime(DateTimeUtils.formatDateTime(startDate));//开始时间
				detailBean.setEndTime(DateTimeUtils.formatDateTime(endDate));//截止时间
				detailBean.setOperateType(operateType);//操作类型编码
				detailBean.setOperateTypeName(operateTypeName);//操作类型名称
				detailBean.setOperateDate(bean.getArrivalDate());//操作日期
				detailBean.setOperateHour(diffHour);//操作小时数
				addList.add(detailBean);
			}else {
				log.error("原油码头接口时间格式不正确：type:"+operateTypeName+" "+startDtColumn+":"+startDtStr+" "+endDtColumn+":"+endDtStr);
			}
		}
	}
	/**
	 * 通过反射获取数据
	 *
	 * @category 通过反射获取数据
	 * <AUTHOR>
	 * @param bean           数据bean
	 * @param columnCode     字段
	 * @param dataReflection 反射对象
	 * @return Object
	 */
	private Object getReflectData(Object dataBean, String columnCode, ObjUtilsReflection dataReflection) {
		Object result = null;
		Method getMethod = dataReflection.getGetMethod(columnCode);
		if (ObjUtils.notEmpty(getMethod)) {
			try {
				result = getMethod.invoke(dataBean);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				log.error("",e);
			}
		}
		return result;
	}
	
	@Override
	protected List<Object> getPullData(String whereSql,HashMap<String,String> synParam) {
		// TODO Auto-generated method stub
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

}
