package com.yunhesoft.outInterface.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;

@RestController
@RequestMapping("/outInterface")
@Api(tags = "外部数据接口")
public class OutInterfaceController extends BaseRestController {

//	@Autowired
//	private IMtmClassService mtmClassServ;
//	
//	@Autowired
//	private ImtmIndexToolsService indexTools;
//	
//	/**
//	 *	查询类别维度树形信息
//	 * @param queryParam
//	 * @return
//	 */
//	@RequestMapping(value = "/getClassTreeData", method = RequestMethod.POST)
//	@ApiOperation("查询类别维度树形信息")
//	public Res<?> getClassTreeData(@RequestBody MtmClassDto queryParam) {
//		return Res.OK(mtmClassServ.getClassTreeData(queryParam,true,true,true,1,null,true));
//	}
//	
//	/**
//	 *	查询类别维度树形数据
//	 * @param queryParam
//	 * @return
//	 */
//	@RequestMapping(value = "/getClassTreeList", method = RequestMethod.POST)
//	@ApiOperation("查询类别维度树形信息")
//	public Res<?> getClassTreeList(@RequestBody MtmClassDto queryParam) {
//		return Res.OK(mtmClassServ.getClassTreeList(queryParam));
//	}
//	
//	/**
//	 * 	保存类别维度数据
//	 * @param param
//	 * @return
//	 */
//    @RequestMapping(value = "/saveClassData", method = RequestMethod.POST)
//    @ApiOperation("保存类别维度数据")
//    public Res<?> saveClassData(@RequestBody MtmClassDto param) {
//        return Res.OK(mtmClassServ.saveClassData(param));
//    }
//    
//    /**
//	 * 	是否有继承数据
//	 * @param param
//	 * @return
//	 */
//    @RequestMapping(value = "/hasExtendData", method = RequestMethod.POST)
//    @ApiOperation("是否有继承数据")
//    public Res<?> hasExtendData(@RequestBody MtmClassDto param) {
//        return Res.OK(mtmClassServ.hasExtendData(param));
//    }
//	
//    /**
//	 * 	继承类别维度数据
//	 * @param param
//	 * @return
//	 */
//    @RequestMapping(value = "/extendData", method = RequestMethod.POST)
//    @ApiOperation("继承类别维度数据")
//    public Res<?> extendData(@RequestBody MtmClassDto param) {
//        return Res.OK(mtmClassServ.extendData(param));
//    }
//    
//    /**
//	 * 	移动排序数据
//	 * @param param
//	 * @return
//	 */
//    @RequestMapping(value = "/moveData", method = RequestMethod.POST)
//    @ApiOperation("移动排序数据")
//    public Res<?> moveData(@RequestBody MtmClassDto param) {
//        return Res.OK(mtmClassServ.moveData(param));
//    }
//    
//    /**
//	 * 	校验保存的数据
//	 * @param param
//	 * @return
//	 */
//    @RequestMapping(value = "/checkSaveData", method = RequestMethod.POST)
//    @ApiOperation("校验保存的数据")
//    public Res<?> checkSaveData(@RequestBody MtmClassDto param) {
//        return Res.OK(mtmClassServ.checkSaveData(param));
//    }
//    
//    /**
//   	 * 	清除当前版本的数据
//   	 * @param param
//   	 * @return
//   	 */
//    @RequestMapping(value = "/clearData", method = RequestMethod.POST)
//    @ApiOperation("清除当前版本的数据")
//    public Res<?> clearData(@RequestBody MtmClassDto param) {
//    	return Res.OK(mtmClassServ.clearData(param));
//    }
//
//    /**
//   	 * 	获取类别维度默认月份
//   	 * @param themesId
//   	 * @return
//   	 */
//    @RequestMapping(value = "/getClassDefaultMonth", method = RequestMethod.POST)
//    @ApiOperation("获取类别维度默认月份")
//    public Res<?> getClassDefaultMonth(@RequestBody MtmClassDto param) {
//    	return Res.OK(mtmClassServ.getClassDefaultMonth(param));
//    }
//    
//    /**
//	 *	查询类别维度树形信息
//	 * @param queryParam
//	 * @return
//	 */
//	@RequestMapping(value = "/getClassIndexTreeDataFun", method = RequestMethod.POST)
//	@ApiOperation("查询类别维度树形信息")
//	public Res<?> getClassIndexTreeData(@RequestBody MtmClassDto queryParam) {
//		return Res.OK(mtmClassServ.getClassIndexTreeData(queryParam));
//	}
//	
//	@ApiOperation(value = "获取通用类别维度的指标节点", notes = "获取通用类别维度的指标节点")
//	@RequestMapping(value = "/getClassIsLeaf", method = { RequestMethod.POST })
//	public Res<List<MtmClassVo>> getClassIsLeaf(@RequestBody MtmClassDto param) {
//		Res<List<MtmClassVo>> res = new Res<List<MtmClassVo>>();
//		res.setResult(mtmClassServ.getClassIsLeaf(param.getThemesId(), param.getVersionNumber()));
//		return res;
//	}
//	
//	@ApiOperation(value = "判断类别维度是否被使用", notes = "判断类别维度是否被使用")
//	@RequestMapping(value = "/classIsUse", method = { RequestMethod.POST })
//	public Res<Boolean> classIsUse(@RequestParam @ApiParam(value = "战略主题ID") String themesId,@RequestParam @ApiParam(value = "类别维度别名") String classAlias, @RequestParam @ApiParam(value = "版本号") String versionNumber) {
//		Res<Boolean> res = new Res<Boolean>();
//		res.setResult(indexTools.classIsUse(themesId, classAlias, versionNumber));
//		return res;
//	}
	
}
