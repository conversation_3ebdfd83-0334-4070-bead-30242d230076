package com.yunhesoft.outInterface.service;

import java.util.List;

import com.yunhesoft.outInterface.entity.po.PortAffairsOilDetail;
import com.yunhesoft.outInterface.entity.po.PortAffairsProductDetail;

/**
 * 港务接口数据库服务类
 * 
 * @Description:
 * <AUTHOR>
 * @date 2024年03月23日
 */
public interface IPortAffairsDBService {
	/**
	 * 获取产品码头进港数据
	 * @category 获取产品码头进港数据
	 * <AUTHOR> 
	 * @param startDt
	 * @param endDt
	 * @param tableCodeList
	 * @return
	 */
	public List<PortAffairsProductDetail> getPortAffairsProduct(String startDt, String endDt, List<String> tableCodeList);
	/**
	 * 更新产品码头进港数据
	 * @category  更新产品码头进港数据
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	public boolean updatePortAffairsProduct(List<PortAffairsProductDetail> dataList);
	
	/**
	 * 获取原油码头进港数据
	 * @category 获取原油码头进港数据
	 * <AUTHOR> 
	 * @param startDt
	 * @param endDt
	 * @param tableCodeList
	 * @return
	 */
	public List<PortAffairsOilDetail> getPortAffairsOil(String startDt, String endDt, List<String> tableCodeList);

	/**
	 * 更新原油码头进港数据
	 * @category  更新原油码头进港数据
	 * <AUTHOR> 
	 * @param dataList
	 * @return
	 */
	public boolean updatePortAffairsOil(List<PortAffairsOilDetail> dataList);
}
