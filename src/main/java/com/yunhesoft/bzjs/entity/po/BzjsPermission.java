package com.yunhesoft.bzjs.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Data
@ApiModel("班组记事权限设置表")
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "BZJS_PERMISSION")
public class BzjsPermission extends BaseEntity {
    @ApiModelProperty(value = "班组记事模版ID", example = "BZJS_TPL 表 ID 字段")
    @Column(name = "TPL_ID", length = 50)
    private String tplId;

    @ApiModelProperty(value = "班组记事模版栏目ID", example = "BZJS_TPL_DETAIL 表 ID 字段")
    @Column(name = "TPL_DETAIL_ID", length = 50)
    private String tplDetailId;

    @ApiModelProperty(value = "权限类型", example = "role=角色，post=岗位，staff=员工")
    @Column(name = "PERM_TYPE", length = 50)
    private String permType;

    @ApiModelProperty(value = "权限ID", example = "一个ID为一条记录，存储角色、岗位、员工的ID")
    @Column(name = "PERM_ID", length = 50)
    private String permId;

    @ApiModelProperty(value = "权限名称", example = "")
    @Column(name = "PERM_NAME", length = 200)
    private String permName;

    @ApiModelProperty(value = "记录标识", example = "1=可用，0=已删除")
    private Integer tmused;
}
