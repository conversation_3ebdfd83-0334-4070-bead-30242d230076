package com.yunhesoft.bzjs.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Data
@ApiModel("班组记事模版设置表")
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "BZJS_TPL")
public class BzjsTpl extends BaseEntity {
    @ApiModelProperty(value = "模版名称", example = "")
    @Column(name = "TPL_NAME", length = 200)
    private String tplName;

    @ApiModelProperty(value = "备注", example = "")
    @Column(name = "REMARK", length = 4000)
    private String remark;

    @ApiModelProperty(value = "使用状态", example = "1=使用，0=停用")
    @Column(name = "ENABLED")
    private Integer enabled;

    @ApiModelProperty(value = "记录标识", example = "1=可用，0=已删除")
    @Column(name = "TMUSED")
    private Integer tmused;
}
