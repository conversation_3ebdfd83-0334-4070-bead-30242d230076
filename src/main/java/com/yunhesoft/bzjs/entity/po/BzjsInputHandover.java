package com.yunhesoft.bzjs.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Data
@ApiModel("班组记事录入交班信息")
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "BZJS_INPUT_HANDOVER")
public class BzjsInputHandover extends BaseEntity {
    @ApiModelProperty(value = "行云流表单数据ID", example = "行云流表单数据ID1")
    @Column(name = "FORM_DATA_ID", length = 50)
    private String formDataId;

    @ApiModelProperty(value = "录入主数据ID", example = "录入主数据ID1")
    @Column(name = "IPT_DATA_ID", length = 50)
    private String iptDataId;

    @ApiModelProperty(value = "录入明细数据ID", example = "录入明细数据ID1")
    @Column(name = "IPT_DATAMX_ID", length = 50)
    private String iptDatamxId;

    @ApiModelProperty(value = "班次名称", example = "班次名称1")
    @Column(name = "BCMC", length = 200)
    private String bcmc;

    @ApiModelProperty(value = "班次代码", example = "班次代码1")
    @Column(name = "BCDM", length = 50)
    private String bcdm;

    @ApiModelProperty(value = "班组名称", example = "班组名称1")
    @Column(name = "BZMC", length = 200)
    private String bzmc;

    @ApiModelProperty(value = "班组代码", example = "班组代码1")
    @Column(name = "BZDM", length = 50)
    private String bzdm;

    @ApiModelProperty(value = "人员ID", example = "人员ID1")
    @Column(name = "PERSON_ID", length = 50)
    private String personId;

    @ApiModelProperty(value = "人员姓名", example = "人员姓名1")
    @Column(name = "PERSON_NAME", length = 50)
    private String personName;

    @ApiModelProperty(value = "操作时间", example = "2023-11-13 10:33:48")
    @Column(name = "OPER_TIME")
    private Date operTime;

    @ApiModelProperty(value="交班时间", example="2023-12-21 13:19:21")
    @Column(name="HANDOVER_TIME")
    private Date handoverTime;

    @ApiModelProperty(value = "记录标识", example = "123")
    @Column(name = "TMUSED")
    private int tmused;
}
