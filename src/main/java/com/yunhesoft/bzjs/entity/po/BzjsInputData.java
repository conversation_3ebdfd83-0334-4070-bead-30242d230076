package com.yunhesoft.bzjs.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Data
@ApiModel("班组记事录入数据表")
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "BZJS_INPUT_DATA")
public class BzjsInputData extends BaseEntity {
    @ApiModelProperty(value = "行云流表单模版ID", example = "")
    @Column(name = "FORM_ID", length = 50)
    private String formId;

    @ApiModelProperty(value = "行云流表单数据ID", example = "表单每填写一次都会生成新的ID")
    @Column(name = "FORM_DATA_ID", length = 50)
    private String formDataId;

    @ApiModelProperty(value = "班组记事模版ID", example = "BZJS_TPL 表 ID 字段")
    @Column(name = "TPL_ID", length = 50)
    private String tplId;

    @ApiModelProperty(value = "模版名称", example = "")
    @Column(name = "TPL_NAME", length = 200)
    private String tplName;

    @ApiModelProperty(value = "录入时间", example = "2023-11-06 11:34:24")
    @Column(name = "INPUT_TIME")
    private Date inputTime;

    @ApiModelProperty(value = "多个核算对象ID", example = "逗号分隔，例如：xxx,xxx,xxx")
    @Column(name = "ACCNT_OBJ_IDS", length = 4000)
    private String accntObjIds;

    @ApiModelProperty(value = "多个核算对象名称", example = "逗号分隔，例如：xxx,xxx,xxx")
    @Column(name = "ACCNT_OBJ_NAMES", length = 4000)
    private String accntObjNames;

    @ApiModelProperty(value = "台账ID", example = "台账ID1")
    @Column(name = "ACCOUNT_ID", length = 50)
    private String accountId;

    @ApiModelProperty(value = "班次代码", example = "班次代码1")
    @Column(name = "BCDM", length = 50)
    private String bcdm;

    @ApiModelProperty(value = "班次名称", example = "班次名称1")
    @Column(name = "bcmc", length = 200)
    private String bcmc;

    @ApiModelProperty(value = "上班时间", example = "2023-11-08 11:34:24")
    @Column(name = "SBSJ")
    private Date sbsj;

    @ApiModelProperty(value = "下班时间", example = "2023-11-09 11:34:24")
    @Column(name = "XBSJ")
    private Date xbsj;

    @ApiModelProperty(value = "岗位名称", example = "岗位名称1")
    @Column(name = "GWMC", length = 200)
    private String gwmc;

    @ApiModelProperty(value = "岗位代码", example = "岗位代码1")
    @Column(name = "GWDM", length = 50)
    private String gwdm;

    @ApiModelProperty(value="班组ID", example="班组ID1")
    @Column(name="TEAM_ID", length=50)
    private String teamId;

    @ApiModelProperty(value="班组名称", example="班组名称1")
    @Column(name="TEAM_NAME", length=200)
    private String teamName;

    @ApiModelProperty(value = "记录标识", example = "1=可用，0=已删除")
    @Column(name = "TMUSED")
    private Integer tmused;
}
