package com.yunhesoft.bzjs.entity.dto;

import com.yunhesoft.bzjs.entity.vo.BzjsInputVo;
import com.yunhesoft.bzjs.entity.vo.BzjsTplMainDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("班组记事配置数据请求参数")
@Data
public class BzjsConfDto {
    @ApiModelProperty("模版ID")
    private String tplId;
    @ApiModelProperty("模版栏目ID")
    private String tplDetailId;
    @ApiModelProperty("是否查询已启用的模版数据")
    private Boolean isTplEnabled;
    @ApiModelProperty("待保存的录入数据")
    private BzjsTplMainDetailVo saveData;
}
