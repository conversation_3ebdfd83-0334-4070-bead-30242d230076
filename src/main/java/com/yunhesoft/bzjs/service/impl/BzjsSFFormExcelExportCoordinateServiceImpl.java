package com.yunhesoft.bzjs.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.bzjs.constant.BzjsConstant;
import com.yunhesoft.bzjs.entity.dto.BzjsInputDto;
import com.yunhesoft.bzjs.entity.po.*;
import com.yunhesoft.bzjs.entity.vo.BzjsInputVo;
import com.yunhesoft.bzjs.entity.vo.BzjsInputmxVo;
import com.yunhesoft.bzjs.service.IBzjsInputService;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.tmsf.form.entity.SFFormComType;
import com.yunhesoft.tmsf.form.entity.dto.SFFormExcelExportCellDto;
import com.yunhesoft.tmsf.form.entity.dto.SFFormExcelExportParamDto;
import com.yunhesoft.tmsf.form.entity.dto.SFFormExcelExportRowDto;
import com.yunhesoft.tmsf.form.service.ISFFormExcelExportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 行云流通用Excel导出实现类
 * （坐标定位实现法）
 */
@SFFormComType("bzjsShow")
@Service
public class BzjsSFFormExcelExportCoordinateServiceImpl implements ISFFormExcelExportService {
    @Autowired
    private IBzjsInputService bzjsInputService;

    @Override
    public List<SFFormExcelExportRowDto> getRowList(SFFormExcelExportParamDto excelParam) {
        List<SFFormExcelExportRowDto> list = new ArrayList<>();

        String titleBackgroundColor = "#e0e0e0";

        JSONObject options = excelParam.getOptions();
        //班组记事模版ID
        String tplId = options.containsKey("bzjsTemplateSelect") ? options.getString("bzjsTemplateSelect") : "";
        //行云流表单数据ID
        String dataId = excelParam.getDataId();
        if (StringUtils.isEmpty(tplId) || StringUtils.isEmpty(dataId)) {
            return list;
        }

        BzjsInputDto param = new BzjsInputDto();
        param.setTplId(tplId);
        param.setFormDataId(dataId);

        BzjsInputVo historyInputData = bzjsInputService.getHistoryInputData(param);
        if (historyInputData == null || ObjUtils.isEmpty(historyInputData.getData())) {
            return list;
        }

        List<BzjsInputmxVo> inputMxList = historyInputData.getData();
        int rowIdx = 0;
        for (BzjsInputmxVo inputMx : inputMxList) {
            SFFormExcelExportRowDto rowDto = new SFFormExcelExportRowDto();
            List<SFFormExcelExportCellDto> cellList = rowDto.getCellList();

            //标题
            SFFormExcelExportCellDto titleCell = new SFFormExcelExportCellDto(rowIdx, 0);
            titleCell.setValue(inputMx.getTitle());
            titleCell.setAroundBorder(false);
            cellList.add(titleCell);

            String dataSource = inputMx.getDataSource();
            if (BzjsConstant.DATA_SOURCE_HANDOVERINFO.equals(dataSource)) {
                Object dataArray = inputMx.getData();
                if (dataArray instanceof ArrayList && ((List) dataArray).size() > 0) {
                    BzjsInputHandover data = ((List<BzjsInputHandover>) dataArray).get(0);
                    //班次
                    SFFormExcelExportCellDto titleBC = new SFFormExcelExportCellDto(rowIdx, 1);
                    titleBC.setValue("班次：");
                    titleBC.setBackgroundColor(titleBackgroundColor);
                    titleBC.setAroundBorder(true);
                    cellList.add(titleBC);
                    SFFormExcelExportCellDto valueBC = new SFFormExcelExportCellDto(rowIdx, 2);
                    valueBC.setValue(data.getBcmc());
                    valueBC.setAroundBorder(true);
                    cellList.add(valueBC);
                    //班组
                    SFFormExcelExportCellDto titleBZ = new SFFormExcelExportCellDto(rowIdx, 3);
                    titleBZ.setValue("班组：");
                    titleBZ.setBackgroundColor(titleBackgroundColor);
                    titleBZ.setAroundBorder(true);
                    cellList.add(titleBZ);
                    SFFormExcelExportCellDto valueBZ = new SFFormExcelExportCellDto(rowIdx, 4);
                    valueBZ.setValue(data.getBzmc());
                    valueBZ.setAroundBorder(true);
                    cellList.add(valueBZ);
                    //人员
                    SFFormExcelExportCellDto titleRY = new SFFormExcelExportCellDto(rowIdx, 5);
                    titleRY.setValue("人员：");
                    titleRY.setBackgroundColor(titleBackgroundColor);
                    titleRY.setAroundBorder(true);
                    cellList.add(titleRY);
                    SFFormExcelExportCellDto valueRY = new SFFormExcelExportCellDto(rowIdx, 6);
                    valueRY.setValue(data.getPersonName());
                    valueRY.setAroundBorder(true);
                    cellList.add(valueRY);
                    //下班时间
                    SFFormExcelExportCellDto titleXBSJ = new SFFormExcelExportCellDto(rowIdx, 7);
                    titleXBSJ.setValue("下班时间：");
                    titleXBSJ.setBackgroundColor(titleBackgroundColor);
                    titleXBSJ.setAroundBorder(true);
                    cellList.add(titleXBSJ);
                    SFFormExcelExportCellDto valueXBSJ = new SFFormExcelExportCellDto(rowIdx, 8);
                    valueXBSJ.setValue(DateTimeUtils.formatDateTime(data.getHandoverTime()));
                    valueXBSJ.setAroundBorder(true);
                    cellList.add(valueXBSJ);
                    //操作时间
                    SFFormExcelExportCellDto titleCZSJ = new SFFormExcelExportCellDto(rowIdx, 9);
                    titleCZSJ.setValue("操作时间：");
                    titleCZSJ.setBackgroundColor(titleBackgroundColor);
                    titleCZSJ.setAroundBorder(true);
                    cellList.add(titleCZSJ);
                    SFFormExcelExportCellDto valueCZSJ = new SFFormExcelExportCellDto(rowIdx, 10);
                    valueCZSJ.setValue(DateTimeUtils.formatDateTime(data.getOperTime()));
                    valueCZSJ.setAroundBorder(true);
                    cellList.add(valueCZSJ);
                }
            } else if (BzjsConstant.DATA_SOURCE_TAKEOVERINFO.equals(dataSource)) {
                Object dataArray = inputMx.getData();
                if (dataArray instanceof ArrayList && ((List) dataArray).size() > 0) {
                    BzjsInputTakeover data = ((List<BzjsInputTakeover>) dataArray).get(0);
                    //班次
                    SFFormExcelExportCellDto titleBC = new SFFormExcelExportCellDto(rowIdx, 1);
                    titleBC.setValue("班次：");
                    titleBC.setBackgroundColor(titleBackgroundColor);
                    titleBC.setAroundBorder(true);
                    cellList.add(titleBC);
                    SFFormExcelExportCellDto valueBC = new SFFormExcelExportCellDto(rowIdx, 2);
                    valueBC.setValue(data.getBcmc());
                    valueBC.setAroundBorder(true);
                    cellList.add(valueBC);
                    //班组
                    SFFormExcelExportCellDto titleBZ = new SFFormExcelExportCellDto(rowIdx, 3);
                    titleBZ.setValue("班组：");
                    titleBZ.setBackgroundColor(titleBackgroundColor);
                    titleBZ.setAroundBorder(true);
                    cellList.add(titleBZ);
                    SFFormExcelExportCellDto valueBZ = new SFFormExcelExportCellDto(rowIdx, 4);
                    valueBZ.setValue(data.getBzmc());
                    valueBZ.setAroundBorder(true);
                    cellList.add(valueBZ);
                    //人员
                    SFFormExcelExportCellDto titleRY = new SFFormExcelExportCellDto(rowIdx, 5);
                    titleRY.setValue("人员：");
                    titleRY.setBackgroundColor(titleBackgroundColor);
                    titleRY.setAroundBorder(true);
                    cellList.add(titleRY);
                    SFFormExcelExportCellDto valueRY = new SFFormExcelExportCellDto(rowIdx, 6);
                    valueRY.setValue(data.getPersonName());
                    valueRY.setAroundBorder(true);
                    cellList.add(valueRY);
                    //上班时间
                    SFFormExcelExportCellDto titleSBSJ = new SFFormExcelExportCellDto(rowIdx, 7);
                    titleSBSJ.setValue("上班时间：");
                    titleSBSJ.setBackgroundColor(titleBackgroundColor);
                    titleSBSJ.setAroundBorder(true);
                    cellList.add(titleSBSJ);
                    SFFormExcelExportCellDto valueSBSJ = new SFFormExcelExportCellDto(rowIdx, 8);
                    valueSBSJ.setValue(DateTimeUtils.formatDateTime(data.getTakeoverTime()));
                    valueSBSJ.setAroundBorder(true);
                    cellList.add(valueSBSJ);
                }
            } else if (BzjsConstant.DATA_SOURCE_HSZB.equals(dataSource)) {
                //键值对表格最大列数
                int maxColNum = inputMx.getMaxColNum() == null ? BzjsConstant.MAX_COL_NUM : inputMx.getMaxColNum().intValue();
                // 核算指标：循环处理，判断换行
                Object dataArray = inputMx.getData();
                if (dataArray instanceof ArrayList && ((List) dataArray).size() > 0) {
                    int colIdx = 1;
                    for (int i = 0; i < ((List) dataArray).size(); i++) {
                        BzjsInputHszb data = ((List<BzjsInputHszb>) dataArray).get(i);

                        SFFormExcelExportCellDto title = new SFFormExcelExportCellDto(rowIdx, colIdx);
                        title.setValue(data.getZbmc());
                        title.setBackgroundColor(titleBackgroundColor);
                        title.setAroundBorder(true);
                        cellList.add(title);
                        colIdx++;
                        SFFormExcelExportCellDto value = new SFFormExcelExportCellDto(rowIdx, colIdx);
                        String valueStr = data.getValue() == null ? "" : data.getValue();
                        String itemUnitStr = data.getItemUnit() == null ? "" : data.getItemUnit();
                        value.setValue(valueStr + " " + itemUnitStr);
                        value.setAroundBorder(true);
                        cellList.add(value);
                        colIdx++;

                        //新起一行
                        if ((i + 1) % maxColNum == 0 && (i + 1) < ((List) dataArray).size()) {
                            //保存当前行
                            list.add(rowDto);
                            rowIdx++;
                            //创建新行
                            rowDto = new SFFormExcelExportRowDto();
                            cellList = rowDto.getCellList();
                            //列坐标归位（1）
                            colIdx = 1;
                        }
                    }
                }
            } else if (BzjsConstant.DATA_SOURCE_SGLR.equals(dataSource)) {
                Object dataArray = inputMx.getData();
                if (dataArray instanceof ArrayList && ((List) dataArray).size() > 0) {
                    BzjsInputText data = ((List<BzjsInputText>) dataArray).get(0);
                    //手工录入
                    SFFormExcelExportCellDto value = new SFFormExcelExportCellDto(rowIdx, 1);
                    value.setValue(data.getInputContent());
                    value.setAroundBorder(true);
                    value.setRowSpan(3);
                    value.setColSpan(10);
                    cellList.add(value);
                    rowIdx += 2;
                }
            } else if (BzjsConstant.DATA_SOURCE_GJSB.equals(dataSource)) {
                //键值对表格最大列数
                int maxColNum = inputMx.getMaxColNum() == null ? BzjsConstant.MAX_COL_NUM : inputMx.getMaxColNum().intValue();
                // 关键设备：循环处理，判断换行
                Object dataArray = inputMx.getData();
                if (dataArray instanceof ArrayList && ((List) dataArray).size() > 0) {
                    int colIdx = 1;
                    for (int i = 0; i < ((List) dataArray).size(); i++) {
                        BzjsInputGjsb data = ((List<BzjsInputGjsb>) dataArray).get(i);

                        SFFormExcelExportCellDto title = new SFFormExcelExportCellDto(rowIdx, colIdx);
                        title.setValue(data.getSbmc());
                        title.setBackgroundColor(titleBackgroundColor);
                        title.setAroundBorder(true);
                        cellList.add(title);
                        colIdx++;
                        SFFormExcelExportCellDto value = new SFFormExcelExportCellDto(rowIdx, colIdx);
                        value.setValue(data.getSbzt());
                        value.setAroundBorder(true);
                        cellList.add(value);
                        colIdx++;

                        //新起一行
                        if ((i + 1) % maxColNum == 0 && (i + 1) < ((List) dataArray).size()) {
                            //保存当前行
                            list.add(rowDto);
                            rowIdx++;
                            //创建新行
                            rowDto = new SFFormExcelExportRowDto();
                            cellList = rowDto.getCellList();
                            //列坐标归位（1）
                            colIdx = 1;
                        }
                    }
                }
            } else if (BzjsConstant.DATA_SOURCE_QRQZ.equals(dataSource)) {
                Object dataArray = inputMx.getData();
                if (dataArray instanceof ArrayList && ((List) dataArray).size() > 0) {
                    BzjsInputConfirm data = ((List<BzjsInputConfirm>) dataArray).get(0);
                    //岗位
                    SFFormExcelExportCellDto titleGW = new SFFormExcelExportCellDto(rowIdx, 1);
                    titleGW.setValue("岗位：");
                    titleGW.setBackgroundColor(titleBackgroundColor);
                    titleGW.setAroundBorder(true);
                    cellList.add(titleGW);
                    SFFormExcelExportCellDto valueGW = new SFFormExcelExportCellDto(rowIdx, 2);
                    valueGW.setValue(data.getGwmc());
                    valueGW.setAroundBorder(true);
                    cellList.add(valueGW);
                    //确认人
                    SFFormExcelExportCellDto titleQRR = new SFFormExcelExportCellDto(rowIdx, 3);
                    titleQRR.setValue("确认人：");
                    titleQRR.setBackgroundColor(titleBackgroundColor);
                    titleQRR.setAroundBorder(true);
                    cellList.add(titleQRR);
                    SFFormExcelExportCellDto valueQRR = new SFFormExcelExportCellDto(rowIdx, 4);
                    valueQRR.setValue(data.getPersonName());
                    valueQRR.setAroundBorder(true);
                    cellList.add(valueQRR);
                    //操作时间
                    SFFormExcelExportCellDto titleCZSJ = new SFFormExcelExportCellDto(rowIdx, 5);
                    titleCZSJ.setValue("操作时间：");
                    titleCZSJ.setBackgroundColor(titleBackgroundColor);
                    titleCZSJ.setAroundBorder(true);
                    cellList.add(titleCZSJ);
                    SFFormExcelExportCellDto valueCZSJ = new SFFormExcelExportCellDto(rowIdx, 6);
                    valueCZSJ.setValue(DateTimeUtils.formatDateTime(data.getOperTime()));
                    valueCZSJ.setAroundBorder(true);
                    cellList.add(valueCZSJ);
                }
            }

            list.add(rowDto);
            //rowIdx++;
            rowIdx += 2;
        }

        return list;
    }
}
