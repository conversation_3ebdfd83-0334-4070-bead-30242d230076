package com.yunhesoft.bzjs.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.accountTools.service.IAccountToolsService;
import com.yunhesoft.bzjs.constant.BzjsConstant;
import com.yunhesoft.bzjs.entity.dto.*;
import com.yunhesoft.bzjs.entity.po.*;
import com.yunhesoft.bzjs.entity.vo.*;
import com.yunhesoft.bzjs.exception.BzjsWarningException;
import com.yunhesoft.bzjs.service.IBzjsConfService;
import com.yunhesoft.bzjs.service.IBzjsInputService;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.costReport.entity.dto.TeamRecordDto;
import com.yunhesoft.leanCosting.costReport.entity.vo.TeamRecordDataVo;
import com.yunhesoft.leanCosting.costReport.service.IGetTeamRecordService;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.ICostitemService;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.service.IRtdbService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class BzjsInputServiceImpl implements IBzjsInputService {
    @Autowired
    private EntityService entityService;
    @Autowired
    private IBzjsConfService bzjsConfService;
    @Autowired
    private IShiftService shiftService;
    @Autowired
    private IGetTeamRecordService getTeamRecordService;
    @Autowired
    private ICostService iCostService;
    @Autowired
    private IAccountToolsService iAccountToolsService;
    @Autowired
    private ICostitemService iCostitemService;
    @Autowired
    private IRtdbService rtdbSrv;

    /**
     * 获取班组记事表单数据
     *
     * @param param
     */
    @Override
    public BzjsInputVo getFormData(BzjsInputDto param) {
        BzjsInputVo inputData = null;

        if (param.getUseHisTpl() != null && param.getUseHisTpl() == true) {
            //通过历史模版配置获取初始化数据
            BzjsInputVo initData = getInitInputData(param);
            inputData = initData;
        } else {
            //获取历史数据
            BzjsInputVo hisData = getHistoryInputData(param);
            if (ObjUtils.isEmpty(hisData)) { //无历史数据
                //通过最新模版配置获取初始化数据
                BzjsInputVo initData = getInitInputData(param);
                inputData = initData;
            } else {
                inputData = hisData;
            }
        }

        return inputData;
    }

    /**
     * 获取班组记事已录入的历史主数据
     *
     * @param param
     * @return
     */
    public List<BzjsInputData> getBzjsInputData(BzjsInputDto param) {
        List<BzjsInputData> list = null;
        String tplId = param.getTplId();
        String formDataId = param.getFormDataId();
        //String iptDataId = param.getIptDataId();

        Where where = new Where();
        where.eq(BzjsInputData::getTmused, 1);
        where.eq(BzjsInputData::getTplId, tplId);
        where.eq(BzjsInputData::getFormDataId, formDataId);
        //where.eq(BzjsInputData::getId, iptDataId);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputData.class, where);
        } else { //普通模式
            list = entityService.queryList(BzjsInputData.class, where);
        }

        return list;
    }

    /**
     * 获取班组记事已录入的历史主数据
     *
     * @param param
     * @return
     */
    public List<BzjsInputDatamx> getBzjsInputDatamx(BzjsInputDto param) {
        List<BzjsInputDatamx> list = null;
        String tplId = param.getTplId();
        String formDataId = param.getFormDataId();
        String iptDataId = param.getIptDataId();

        Where where = new Where();
        where.eq(BzjsInputDatamx::getTmused, 1);
        where.eq(BzjsInputDatamx::getTplId, tplId);
        where.eq(BzjsInputDatamx::getFormDataId, formDataId);
        where.eq(BzjsInputDatamx::getIptDataId, iptDataId);

        Order order = new Order();
        order.orderByAsc(BzjsInputDatamx::getSn);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputDatamx.class, where, order);
        } else { //普通模式
            list = entityService.queryList(BzjsInputDatamx.class, where, order);
        }

        return list;
    }

    /**
     * 获取已录入的班组记事历史数据
     *
     * @param param
     * @return
     */
    public BzjsInputVo getHistoryInputData(BzjsInputDto param) {
        BzjsInputVo hisData = null;

        //主数据
        List<BzjsInputData> bzjsInputDataList = getBzjsInputData(param);
        if (ObjUtils.isEmpty(bzjsInputDataList)) {
            return hisData;
        }

        BzjsInputData bzjsInputData = bzjsInputDataList.get(0);
        String iptDataId = bzjsInputData.getId();
        param.setIptDataId(iptDataId);
        hisData = ObjUtils.copyTo(bzjsInputData, BzjsInputVo.class);

        //详细数据（栏目）
        List<BzjsInputDatamx> bzjsInputDatamxList = getBzjsInputDatamx(param);
        if (ObjUtils.isEmpty(bzjsInputDatamxList)) {
            return hisData;
        }
        hisData.setData(ObjUtils.convertToList(BzjsInputmxVo.class, bzjsInputDatamxList));

        for (BzjsInputmxVo bzjsInputmxVo : hisData.getData()) {
            String dataSource = bzjsInputmxVo.getDataSource();
            param.setIptDatamxId(bzjsInputmxVo.getId());
            if (BzjsConstant.DATA_SOURCE_HANDOVERINFO.equals(dataSource)) { //交班信息
                List<BzjsInputHandover> bzjsInputHandoverData = getBzjsInputHandoverData(param);
                bzjsInputmxVo.setData(bzjsInputHandoverData);
            } else if (BzjsConstant.DATA_SOURCE_TAKEOVERINFO.equals(dataSource)) { //接班信息
                List<BzjsInputTakeover> bzjsInputTakeoverData = getBzjsInputTakeoverData(param);
                bzjsInputmxVo.setData(bzjsInputTakeoverData);
            } else if (BzjsConstant.DATA_SOURCE_HSZB.equals(dataSource)) { //核算指标
                List<BzjsInputHszb> bzjsInputHszbData = getBzjsInputHszbData(param);
                bzjsInputmxVo.setData(bzjsInputHszbData);
            } else if (BzjsConstant.DATA_SOURCE_SGLR.equals(dataSource)) { //手工录入
                List<BzjsInputText> bzjsInputTextData = getBzjsInputTextData(param);
                bzjsInputmxVo.setData(bzjsInputTextData);
            } else if (BzjsConstant.DATA_SOURCE_GJSB.equals(dataSource)) { //关键设备
                List<BzjsInputGjsb> bzjsInputGjsbData = getBzjsInputGjsbData(param);
                bzjsInputmxVo.setData(bzjsInputGjsbData);
            } else if (BzjsConstant.DATA_SOURCE_QRQZ.equals(dataSource)) { //确认签字
                List<BzjsInputConfirm> bzjsInputConfirmData = getBzjsInputConfirmData(param);
                bzjsInputmxVo.setData(bzjsInputConfirmData);

                // 获取栏目绑定的权限数据
                List<BzjsInputPermission> bzjsInputPermissionList = getBzjsInputPermission(param);
                bzjsInputmxVo.setPermData(bzjsInputPermissionList);
            } else if (BzjsConstant.DATA_SOURCE_COLLECTPOINT.equals(dataSource)) { //采集点
                // 采集点历史数据组装
                List<BzjsInputCollectPoint> bzjsInputCollectpointDatas = getBzjsInputCollectpointData(param);
                List<BzjsInputCollectPointFlVo> collectPointDataFlList = new ArrayList<>();
                Map<String, BzjsInputCollectPointFlVo> flMap = new LinkedHashMap<>();
                if(ObjUtils.notEmpty(bzjsInputCollectpointDatas)){
                    for (BzjsInputCollectPoint collectpointData : bzjsInputCollectpointDatas) {
                        String collectPointFlId = collectpointData.getCollectPointFlId();
                        String collectPointFlName = collectpointData.getCollectPointFlName();
                        Integer collectPointFlSn = collectpointData.getCollectPointFlSn();

                        //组装采集点分类
                        BzjsInputCollectPointFlVo bzjsInputCollectPointFlVo = flMap.get(collectPointFlId);
                        if (bzjsInputCollectPointFlVo == null) {
                            bzjsInputCollectPointFlVo = new BzjsInputCollectPointFlVo();
                            bzjsInputCollectPointFlVo.setCollectPointFlId(collectPointFlId);
                            bzjsInputCollectPointFlVo.setCollectPointFlName(collectPointFlName);
                            bzjsInputCollectPointFlVo.setCollectPointFlSn(collectPointFlSn);
                            bzjsInputCollectPointFlVo.setCollectPointList(new ArrayList<>());
                            flMap.put(collectPointFlId, bzjsInputCollectPointFlVo);
                            collectPointDataFlList.add(bzjsInputCollectPointFlVo);
                        }

                        //采集点分类中添加采集点数据
                        bzjsInputCollectPointFlVo.getCollectPointList().add(collectpointData);
                    }
                }

                bzjsInputmxVo.setData(collectPointDataFlList);
            }
        }

        return hisData;
    }

    /**
     * 获取班组记事初始化数据
     *
     * @param param
     * @return
     */
    public BzjsInputVo getInitInputData(BzjsInputDto param) {
        BzjsInputVo initData = null;

        log.info("--------- 班组记事初始化数据 ---------");

        SysUser user = SysUserHolder.getCurrentUser();

        String formId = param.getFormId();
        String formDataId = param.getFormDataId();
        String tplId = param.getTplId();

        BzjsConfDto confParam = new BzjsConfDto();
        confParam.setTplId(param.getTplId());

        Boolean useHisTpl = param.getUseHisTpl();
        if (useHisTpl == null) {
            useHisTpl = false;
        }

        if (useHisTpl == true) { //使用历史模版配置
            //主数据
            List<BzjsInputData> bzjsInputDataList = getBzjsInputData(param);
            if (ObjUtils.notEmpty(bzjsInputDataList)) {
                BzjsInputData bzjsInputData = bzjsInputDataList.get(0);
                String iptDataId = bzjsInputData.getId();
                param.setIptDataId(iptDataId);
                initData = ObjUtils.copyTo(bzjsInputData, BzjsInputVo.class);

                log.info("------ 班组记事历史配置模版 - {} ------", initData.getTplName());

                //详细数据（栏目）
                List<BzjsInputDatamx> bzjsInputDatamxList = getBzjsInputDatamx(param);
                if (ObjUtils.notEmpty(bzjsInputDatamxList)) {
                    initData.setData(ObjUtils.convertToList(BzjsInputmxVo.class, bzjsInputDatamxList));
                } else {
                    initData = null;
                }
            }
        }

        if (initData == null) { //使用最新模版配置
            //获取班组记事模版主配置
            List<BzjsTplVo> dataList = bzjsConfService.getBzjsTplData(confParam);
            if (ObjUtils.isEmpty(dataList)) {
                log.info("班组记事 - 初始化数据：未初始化，无模版数据（模版ID：{}）", tplId);
                return initData;
            }
            BzjsTplVo bzjsTplVo = dataList.get(0);
            if (Optional.ofNullable(bzjsTplVo.getEnabled()).orElse(0) != 1) {
                log.info("班组记事 - 初始化数据：未初始化，模版未启用（模版ID：{}，模版名称：{}）", tplId, bzjsTplVo.getTplName());
                return initData;
            }

            log.info("------ 班组记事最新配置模版 - {} ------", bzjsTplVo.getTplName());

            //获取班组记事模版栏目配置
            List<BzjsTplDetailVo> detailDataList = bzjsConfService.getBzjsTplDetailData(confParam);
            if (ObjUtils.notEmpty(detailDataList)) {
                initData = new BzjsInputVo();
                initData.setTmused(1);
                initData.setFormId(formId);
                initData.setFormDataId(formDataId);
                initData.setTplId(tplId);
                initData.setTplName(bzjsTplVo.getTplName());
                initData.setData(new ArrayList<>());
                initData.setGwdm(user.getPostId());
                initData.setGwmc(user.getPostName());
                initData.setBcdm(param.getShiftId());
                initData.setBcmc(param.getShiftName());
                initData.setAccntObjIds(param.getAccntObjIds());
                initData.setAccntObjNames(param.getAccntObjNames());
                initData.setAccountId(param.getAccountId());
                initData.setTeamId(param.getTeamId());
                initData.setTeamName(param.getTeamName());
                try {
                    initData.setSbsj(DateTimeUtils.parseDate(param.getShiftBeginTime()));
                } catch (Exception e) {
                    log.error("班组记事 - 获取初始化数据 - 上班时间解析失败：{}", param.getShiftBeginTime(), e);
                }
                try {
                    initData.setXbsj(DateTimeUtils.parseDate(param.getShiftEndTime()));
                } catch (Exception e) {
                    log.error("班组记事 - 获取初始化数据 - 下班时间解析失败：{}", param.getShiftEndTime(), e);
                }
                try {
                    initData.setInputTime(DateTimeUtils.parseDate(param.getSummaryDay()));
                } catch (Exception e) {
                    log.error("班组记事 - 获取初始化数据 - 统计时间解析失败：{}", param.getSummaryDay(), e);
                }

                for (BzjsTplDetailVo detailData : detailDataList) { //栏目
                    if (Optional.ofNullable(detailData.getEnabled()).orElse(0) != 1) {
                        log.info("班组记事 - 初始化数据：此栏目未启用（栏目ID：{}，栏目标题：{}）", detailData.getId(), detailData.getTitle());
                        continue;
                    }

                    BzjsInputmxVo bzjsInputmxVo = new BzjsInputmxVo();
                    bzjsInputmxVo.setTmused(1);
                    bzjsInputmxVo.setFormDataId(formDataId);
                    bzjsInputmxVo.setTplId(tplId);
                    bzjsInputmxVo.setTplDetailId(detailData.getId());
                    bzjsInputmxVo.setTitle(detailData.getTitle());
                    bzjsInputmxVo.setDataSource(detailData.getDataSource());
                    bzjsInputmxVo.setDataType(detailData.getDataType());
                    bzjsInputmxVo.setDataRange(detailData.getDataRange());
                    bzjsInputmxVo.setEditable(detailData.getEditable());
                    bzjsInputmxVo.setEnabled(detailData.getEnabled());
                    bzjsInputmxVo.setSn(detailData.getSn());
                    bzjsInputmxVo.setMaxColNum(detailData.getMaxColNum());
                    initData.getData().add(bzjsInputmxVo);
                }
            }
        }

        if (initData != null) {
            for (BzjsInputmxVo bzjsInputmxVo : initData.getData()) { //栏目
                param.setTplDetailId(bzjsInputmxVo.getTplDetailId());
                String dataType = bzjsInputmxVo.getDataType();
                String dataSource = bzjsInputmxVo.getDataSource();
                log.info("--- 班组记事栏目 - {} ---", bzjsInputmxVo.getTitle());

                if (BzjsConstant.DATA_SOURCE_HANDOVERINFO.equals(dataSource)) { //交班信息
                    // 取当班信息
                    List<BzjsInputHandover> bzjsInputHandoverData = new ArrayList<>();
                    if (StringUtils.isNotEmpty(param.getShiftId())) { //班组工作台参数传入
                        BzjsInputHandover bzjsInputHandover = new BzjsInputHandover();
                        bzjsInputHandover.setFormDataId(formDataId);
                        bzjsInputHandover.setBcmc(param.getShiftName());
                        bzjsInputHandover.setBcdm(param.getShiftId());
                        bzjsInputHandover.setBzdm(param.getTeamId());
                        bzjsInputHandover.setBzmc(param.getTeamName());
                        bzjsInputHandover.setPersonId(user.getId());
                        bzjsInputHandover.setPersonName(user.getRealName());
                        Date handoverTime = StringUtils.isNotEmpty(param.getShiftEndTime()) ? DateTimeUtils.parseDateTime(param.getShiftEndTime()) : null;
                        bzjsInputHandover.setHandoverTime(handoverTime);
                        bzjsInputHandover.setOperTime(DateTimeUtils.getNowDate());
                        bzjsInputHandoverData.add(bzjsInputHandover);
                        log.info("表单传入参数 - 本班数据：{}（用户：{}，用户所属机构：{}）", JSONObject.toJSONString(bzjsInputHandover), user.getRealName(), user.getOrgName());
                    } else { //推倒班
                        ShiftForeignVo shiftInfo = getShiftInfo(BzjsConstant.DATA_TYPE_CURRENT_SHIFT);
                        if (shiftInfo != null) {
                            BzjsInputHandover bzjsInputHandover = new BzjsInputHandover();
                            bzjsInputHandover.setFormDataId(formDataId);
                            bzjsInputHandover.setBcmc(shiftInfo.getShiftClassName());
                            bzjsInputHandover.setBcdm(shiftInfo.getShiftClassCode());
                            bzjsInputHandover.setBzdm(shiftInfo.getOrgCode());
                            bzjsInputHandover.setBzmc(shiftInfo.getOrgName());
                            bzjsInputHandover.setPersonId(user.getId());
                            bzjsInputHandover.setPersonName(user.getRealName());
                            Date handoverTime = StringUtils.isNotEmpty(shiftInfo.getXbsj()) ? DateTimeUtils.parseDateTime(shiftInfo.getXbsj()) : null;
                            bzjsInputHandover.setHandoverTime(handoverTime);
                            bzjsInputHandover.setOperTime(DateTimeUtils.getNowDate());
                            bzjsInputHandoverData.add(bzjsInputHandover);
                        }
                    }
                    bzjsInputmxVo.setData(bzjsInputHandoverData);
                } else if (BzjsConstant.DATA_SOURCE_TAKEOVERINFO.equals(dataSource)) { //接班信息
                    //接班信息 取当班信息 / 上班信息
                    List<BzjsInputTakeover> bzjsInputTakeoverData = new ArrayList<>();
                    if (StringUtils.isNotEmpty(param.getShiftId())) { //班组工作台参数传入
                        if (BzjsConstant.DATA_TYPE_CURRENT_SHIFT.equals(dataType)) {
                            BzjsInputTakeover bzjsInputTakeover = new BzjsInputTakeover();
                            bzjsInputTakeover.setFormDataId(formDataId);
                            bzjsInputTakeover.setBcmc(param.getShiftName());
                            bzjsInputTakeover.setBcdm(param.getShiftId());
                            bzjsInputTakeover.setBzdm(param.getTeamId());
                            bzjsInputTakeover.setBzmc(param.getTeamName());
                            bzjsInputTakeover.setPersonId(user.getId());
                            bzjsInputTakeover.setPersonName(user.getRealName());
                            //当班信息的上班时间
                            Date takeoverTime = StringUtils.isNotEmpty(param.getShiftBeginTime()) ? DateTimeUtils.parseDateTime(param.getShiftBeginTime()) : null;
                            bzjsInputTakeover.setTakeoverTime(takeoverTime);
                            bzjsInputTakeover.setOperTime(DateTimeUtils.getNowDate());
                            bzjsInputTakeoverData.add(bzjsInputTakeover);
                        } else if (BzjsConstant.DATA_TYPE_LAST_SHIFT.equals(dataType)) {
                            BzjsInputTakeover lastShiftHandoverData = getLastShiftHandoverData(param);
                            if (lastShiftHandoverData != null) {
                                lastShiftHandoverData.setFormDataId(formDataId);
                                bzjsInputTakeoverData.add(lastShiftHandoverData);
                            }
                        }
                    } else { //推倒班
                        ShiftForeignVo shiftInfo = getShiftInfo(BzjsConstant.DATA_TYPE_CURRENT_SHIFT);
                        if (shiftInfo != null) {
                            if (BzjsConstant.DATA_TYPE_CURRENT_SHIFT.equals(dataType)) { //提取本班
                                BzjsInputTakeover bzjsInputTakeover = new BzjsInputTakeover();
                                bzjsInputTakeover.setFormDataId(formDataId);
                                bzjsInputTakeover.setBcmc(shiftInfo.getShiftClassName());
                                bzjsInputTakeover.setBcdm(shiftInfo.getShiftClassCode());
                                bzjsInputTakeover.setBzdm(shiftInfo.getOrgCode());
                                bzjsInputTakeover.setBzmc(shiftInfo.getOrgName());
                                bzjsInputTakeover.setPersonId(user.getId());
                                bzjsInputTakeover.setPersonName(user.getRealName());
                                //当班信息的上班时间
                                Date takeoverTime = StringUtils.isNotEmpty(shiftInfo.getSbsj()) ? DateTimeUtils.parseDateTime(shiftInfo.getSbsj()) : null;
                                bzjsInputTakeover.setTakeoverTime(takeoverTime);
                                bzjsInputTakeover.setOperTime(DateTimeUtils.getNowDate());
                                bzjsInputTakeoverData.add(bzjsInputTakeover);
                            } else if (BzjsConstant.DATA_TYPE_LAST_SHIFT.equals(dataType)) { //提取上个班次
                                param.setTeamId(shiftInfo.getOrgCode());
                                param.setTeamName(shiftInfo.getOrgName());
                                param.setShiftBeginTime(shiftInfo.getSbsj());
                                param.setShiftEndTime(shiftInfo.getXbsj());
                                BzjsInputTakeover lastShiftHandoverData = getLastShiftHandoverData(param);
                                if (lastShiftHandoverData != null) {
                                    lastShiftHandoverData.setFormDataId(formDataId);
                                    bzjsInputTakeoverData.add(lastShiftHandoverData);
                                }
                            }
                        }
                    }
                    bzjsInputmxVo.setData(bzjsInputTakeoverData);
                } else if (BzjsConstant.DATA_SOURCE_HSZB.equals(dataSource)) { //核算指标
                    List<BzjsInputHszb> bzjsInputHszbData = getHszbData(param, dataType);
                    bzjsInputmxVo.setData(bzjsInputHszbData);
                } else if (BzjsConstant.DATA_SOURCE_SGLR.equals(dataSource)) { //手工录入
                    List<BzjsInputText> bzjsInputTextData = new ArrayList<>();
                    BzjsInputText bzjsInputText = new BzjsInputText();
                    bzjsInputText.setTmused(1);
                    bzjsInputText.setInputContent("");
                    bzjsInputText.setFormDataId(formDataId);
                    bzjsInputTextData.add(bzjsInputText);
                    bzjsInputmxVo.setData(bzjsInputTextData);
                } else if (BzjsConstant.DATA_SOURCE_GJSB.equals(dataSource)) { //关键设备
                    List<BzjsInputGjsb> bzjsInputGjsbData = getGjsbData(param, dataType);
                    bzjsInputmxVo.setData(bzjsInputGjsbData);
                } else if (BzjsConstant.DATA_SOURCE_QRQZ.equals(dataSource)) { //确认签字
                    List<BzjsInputConfirm> bzjsInputConfirmData = new ArrayList<>();
                    BzjsInputConfirm bzjsInputConfirm = new BzjsInputConfirm();
                    bzjsInputConfirm.setTmused(1);
                    bzjsInputConfirm.setFormDataId(formDataId);
                    bzjsInputConfirmData.add(bzjsInputConfirm);
                    bzjsInputmxVo.setData(bzjsInputConfirmData);

                    // 获取栏目绑定的权限数据
                    String iptDataId = bzjsInputmxVo.getIptDataId(); //已保存主记录ID
                    if (StringUtils.isEmpty(iptDataId)) { //无历史数据，取最新权限配置
                        BzjsConfDto permParams = new BzjsConfDto();
                        permParams.setTplId(bzjsInputmxVo.getTplId());
                        permParams.setTplDetailId(bzjsInputmxVo.getTplDetailId());
                        List<BzjsPermissionVo> bzjsPermissionData = bzjsConfService.getBzjsPermissionData(permParams);
                        for (BzjsPermissionVo bzjsPermissionDatum : bzjsPermissionData) {
                            bzjsPermissionDatum.setId(null);
                            bzjsPermissionDatum.setFormDataId(formDataId);
                        }
                        bzjsInputmxVo.setPermData(bzjsPermissionData);
                    } else { //有历史保存数据，取历史权限配置
                        BzjsInputDto permParams = new BzjsInputDto();
                        permParams.setIptDataId(iptDataId);
                        permParams.setIptDatamxId(bzjsInputmxVo.getId());
                        List<BzjsInputPermission> bzjsInputPermissionList = getBzjsInputPermission(permParams);
                        bzjsInputmxVo.setPermData(bzjsInputPermissionList);
                    }
                } else if (BzjsConstant.DATA_SOURCE_COLLECTPOINT.equals(dataSource)) { //采集点数据
                    // 获取采集点数据
                    List<BzjsInputCollectPointFlVo> collectPointFlData = getCollectPointData(param, bzjsInputmxVo);
                    bzjsInputmxVo.setData(collectPointFlData);
                }
            }
        }

        return initData;
    }

    /**
     * 获取班组记事录入交班数据
     *
     * @param param
     * @return
     */
    public List<BzjsInputHandover> getBzjsInputHandoverData(BzjsInputDto param) {
        List<BzjsInputHandover> list = null;
        String iptDataId = param.getIptDataId();
        String iptDatamxId = param.getIptDatamxId();

        Where where = new Where();
        where.eq(BzjsInputHandover::getTmused, 1);
        where.eq(BzjsInputHandover::getIptDataId, iptDataId);
        where.eq(BzjsInputHandover::getIptDatamxId, iptDatamxId);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputHandover.class, where);
        } else { //普通模式
            list = entityService.queryList(BzjsInputHandover.class, where);
        }

        return list;
    }

    /**
     * 获取班组记事录入接班数据
     *
     * @param param
     * @return
     */
    public List<BzjsInputTakeover> getBzjsInputTakeoverData(BzjsInputDto param) {
        List<BzjsInputTakeover> list = null;
        String iptDataId = param.getIptDataId();
        String iptDatamxId = param.getIptDatamxId();

        Where where = new Where();
        where.eq(BzjsInputTakeover::getTmused, 1);
        where.eq(BzjsInputTakeover::getIptDataId, iptDataId);
        where.eq(BzjsInputTakeover::getIptDatamxId, iptDatamxId);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputTakeover.class, where);
        } else { //普通模式
            list = entityService.queryList(BzjsInputTakeover.class, where);
        }

        return list;
    }

    /**
     * 获取班组记事录入确认签字数据
     *
     * @param param
     * @return
     */
    public List<BzjsInputConfirm> getBzjsInputConfirmData(BzjsInputDto param) {
        List<BzjsInputConfirm> list = null;
        String iptDataId = param.getIptDataId();
        String iptDatamxId = param.getIptDatamxId();

        Where where = new Where();
        where.eq(BzjsInputConfirm::getTmused, 1);
        where.eq(BzjsInputConfirm::getIptDataId, iptDataId);
        where.eq(BzjsInputConfirm::getIptDatamxId, iptDatamxId);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputConfirm.class, where);
        } else { //普通模式
            list = entityService.queryList(BzjsInputConfirm.class, where);
        }

        return list;
    }

    /**
     * 获取班组记事录入栏目绑定的权限数据
     *
     * @param param
     * @return
     */
    public List<BzjsInputPermission> getBzjsInputPermission(BzjsInputDto param) {
        List<BzjsInputPermission> list = null;
        String iptDataId = param.getIptDataId();
        String iptDatamxId = param.getIptDatamxId();

        Where where = new Where();
        where.eq(BzjsInputPermission::getTmused, 1);
        where.eq(BzjsInputPermission::getIptDataId, iptDataId);
        where.eq(BzjsInputPermission::getIptDatamxId, iptDatamxId);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputPermission.class, where);
        } else { //普通模式
            list = entityService.queryList(BzjsInputPermission.class, where);
        }

        return list;
    }

    /**
     * 获取班组记事录入采集点数据
     * @param param
     * @return
     */
    public List<BzjsInputCollectPoint> getBzjsInputCollectpointData(BzjsInputDto param) {
        List<BzjsInputCollectPoint> list = null;
        String iptDataId = param.getIptDataId();
        String iptDatamxId = param.getIptDatamxId();

        Where where = new Where();
        where.eq(BzjsInputCollectPoint::getTmused, 1);
        where.eq(BzjsInputCollectPoint::getIptDataId, iptDataId);
        where.eq(BzjsInputCollectPoint::getIptDatamxId, iptDatamxId);

        Order order = new Order();
        order.orderByAsc(BzjsInputCollectPoint::getCollectPointFlSn);
        order.orderByAsc(BzjsInputCollectPoint::getSn);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputCollectPoint.class, where, order);
        } else { //普通模式
            list = entityService.queryList(BzjsInputCollectPoint.class, where, order);
        }

        return list;
    }

    /**
     * 获取班组记事录入手工录入数据
     *
     * @param param
     * @return
     */
    public List<BzjsInputText> getBzjsInputTextData(BzjsInputDto param) {
        List<BzjsInputText> list = null;
        String iptDataId = param.getIptDataId();
        String iptDatamxId = param.getIptDatamxId();

        Where where = new Where();
        where.eq(BzjsInputText::getTmused, 1);
        where.eq(BzjsInputText::getIptDataId, iptDataId);
        where.eq(BzjsInputText::getIptDatamxId, iptDatamxId);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputText.class, where);
        } else { //普通模式
            list = entityService.queryList(BzjsInputText.class, where);
        }

        return list;
    }

    /**
     * 获取班组记事录入核算指标数据
     *
     * @param param
     * @return
     */
    public List<BzjsInputHszb> getBzjsInputHszbData(BzjsInputDto param) {
        List<BzjsInputHszb> list = null;
        String iptDataId = param.getIptDataId();
        String iptDatamxId = param.getIptDatamxId();

        Where where = new Where();
        where.eq(BzjsInputHszb::getTmused, 1);
        where.eq(BzjsInputHszb::getIptDataId, iptDataId);
        where.eq(BzjsInputHszb::getIptDatamxId, iptDatamxId);

        Order order = new Order();
        order.orderByAsc(BzjsInputHszb::getSn);
        order.orderByAsc(BzjsInputHszb::getZbmc);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputHszb.class, where, order);
        } else { //普通模式
            list = entityService.queryList(BzjsInputHszb.class, where, order);
        }

        return list;
    }

    /**
     * 获取班组记事录入关键设备数据
     *
     * @param param
     * @return
     */
    public List<BzjsInputGjsb> getBzjsInputGjsbData(BzjsInputDto param) {
        List<BzjsInputGjsb> list = null;
        String iptDataId = param.getIptDataId();
        String iptDatamxId = param.getIptDatamxId();

        Where where = new Where();
        where.eq(BzjsInputGjsb::getTmused, 1);
        where.eq(BzjsInputGjsb::getIptDataId, iptDataId);
        where.eq(BzjsInputGjsb::getIptDatamxId, iptDatamxId);

        Order order = new Order();
        order.orderByAsc(BzjsInputGjsb::getSn);
        order.orderByAsc(BzjsInputGjsb::getSbmc);

        Boolean isTenant = MultiTenantUtils.enalbe();
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputGjsb.class, where, order);
        } else { //普通模式
            list = entityService.queryList(BzjsInputGjsb.class, where, order);
        }

        return list;
    }

    /**
     * 保存表单数据
     *
     * @param param
     * @return
     */
    @Transactional
    @Override
    public BzjsInputVo saveFormData(BzjsInputDto param) throws Exception {
        BzjsInputVo resData = null;
        SysUser user = SysUserHolder.getCurrentUser();
        boolean ifRefreshTodoData = false;

        BzjsInputVo saveData = param.getSaveData();
        if (ObjUtils.isEmpty(saveData)) {
            throw new Exception("待保存数据无效");
        }

        log.info("--------- 班组记事保存数据 ---------");

        List<BzjsInputData> bzjsInputDataInsertList = new ArrayList<>();
        List<BzjsInputData> bzjsInputDataUpdateList = new ArrayList<>();
        List<BzjsInputDatamx> bzjsInputDatamxInsertList = new ArrayList<>();
        List<BzjsInputDatamx> bzjsInputDatamxUpdateList = new ArrayList<>();
        List<BzjsInputHandover> bzjsInputHandoverInsertList = new ArrayList<>();
        List<BzjsInputHandover> bzjsInputHandoverUpdateList = new ArrayList<>();
        List<BzjsInputTakeover> bzjsInputTakeoverInsertList = new ArrayList<>();
        List<BzjsInputTakeover> bzjsInputTakeoverUpdateList = new ArrayList<>();
        List<BzjsInputHszb> bzjsInputHszbInsertList = new ArrayList<>();
        List<BzjsInputHszb> bzjsInputHszbUpdateList = new ArrayList<>();
        List<BzjsInputText> bzjsInputTextInsertList = new ArrayList<>();
        List<BzjsInputText> bzjsInputTextUpdateList = new ArrayList<>();
        List<BzjsInputGjsb> bzjsInputGjsbInsertList = new ArrayList<>();
        List<BzjsInputGjsb> bzjsInputGjsbUpdateList = new ArrayList<>();
        List<BzjsInputConfirm> bzjsInputConfirmInsertList = new ArrayList<>();
        List<BzjsInputConfirm> bzjsInputConfirmUpdateList = new ArrayList<>();
        List<BzjsInputPermission> bzjsInputPermissionInsertList = new ArrayList<>();
        List<BzjsInputPermission> bzjsInputPermissionUpdateList = new ArrayList<>();
        List<BzjsInputCollectPoint> bzjsInputCollectPointInsertList = new ArrayList<>();
        List<BzjsInputCollectPoint> bzjsInputCollectPointUpdateList = new ArrayList<>();

        //班组记事主数据（录入时间、核算对象、班次、班组等信息）
        BzjsInputData bzjsInputData = ObjUtils.copyTo(saveData, BzjsInputData.class);

        //校验是否存在相同条件的历史数据，防止多人同时保存数据导致数据重复
        List<BzjsInputData> hisInputData = getBzjsInputData(param);
        String prompt = "班组记事数据已更新，请点击刷新按钮后再修改并保存";

        if (StringUtils.isEmpty(saveData.getId())) {
            if (ObjUtils.notEmpty(hisInputData)) {
                //当前用户保存数据之前，已有其他人进行了保存（防止数据覆盖，抛异常前台提示）
                throw new BzjsWarningException(prompt);
            }
            bzjsInputData.setId(TMUID.getUID());
            bzjsInputData.setTmused(1);
            bzjsInputDataInsertList.add(bzjsInputData);
            saveData.setId(bzjsInputData.getId());
            saveData.setTmused(1);
            ifRefreshTodoData = true;
        } else {
            if (ObjUtils.notEmpty(hisInputData)) {
                BzjsInputData dbRecord = hisInputData.get(0);
                long saveDataUpdateTime = 0;
                if (bzjsInputData.getUpdateTime() != null) {
                    saveDataUpdateTime = bzjsInputData.getUpdateTime().getTime();
                }
                long dbDataUpdateTime = 0;
                if (dbRecord.getUpdateTime() != null) {
                    dbDataUpdateTime = dbRecord.getUpdateTime().getTime();
                }
                if (dbDataUpdateTime > saveDataUpdateTime) {
                    //当前用户保存数据之前，已有其他人进行了保存（防止数据覆盖，抛异常前台提示）
                    throw new BzjsWarningException(prompt);
                }
            }

            bzjsInputDataUpdateList.add(bzjsInputData);
        }

        //班组记事栏目数据
        List<BzjsInputmxVo> data = saveData.getData();
        if (ObjUtils.notEmpty(data)) {
            for (BzjsInputmxVo bzjsInputmxVo : data) {
                param.setTplDetailId(bzjsInputmxVo.getTplDetailId());
                //BzjsInputDatamx bzjsInputDatamx = ObjUtils.convertTo(bzjsInputmxVo, BzjsInputDatamx.class);
                BzjsInputDatamx bzjsInputDatamx = ObjUtils.copyTo(bzjsInputmxVo, BzjsInputDatamx.class);
                if (StringUtils.isEmpty(bzjsInputDatamx.getId())) {
                    bzjsInputDatamx.setId(TMUID.getUID());
                    bzjsInputDatamx.setIptDataId(saveData.getId());
                    bzjsInputDatamx.setTmused(1);
                    bzjsInputDatamxInsertList.add(bzjsInputDatamx);
                    bzjsInputmxVo.setId(bzjsInputDatamx.getId());
                    bzjsInputmxVo.setIptDataId(bzjsInputDatamx.getIptDataId());
                    bzjsInputmxVo.setTmused(1);
                } else {
                    bzjsInputDatamxUpdateList.add(bzjsInputDatamx);
                }
                Object mxData = bzjsInputmxVo.getData();
                if (ObjUtils.isEmpty(mxData)) {
                    continue;
                }
                Object permData = bzjsInputmxVo.getPermData();
                String dataSource = bzjsInputmxVo.getDataSource();

                if (BzjsConstant.DATA_SOURCE_HANDOVERINFO.equals(dataSource)) { //交班信息
                    //List<BzjsInputHandover> bzjsInputHandoverDataList = (List<BzjsInputHandover>) mxData;
                    //List<BzjsInputHandover> bzjsInputHandoverDataList = copyList((JSONArray) mxData, BzjsInputHandover.class);
                    List<BzjsInputHandover> bzjsInputHandoverDataList = ((JSONArray) mxData).toJavaList(BzjsInputHandover.class);
                    bzjsInputmxVo.setData(bzjsInputHandoverDataList);
                    for (BzjsInputHandover bzjsInputHandoverVo : bzjsInputHandoverDataList) {
                        BzjsInputHandover bzjsInputHandover = ObjUtils.copyTo(bzjsInputHandoverVo, BzjsInputHandover.class);
                        if (StringUtils.isEmpty(bzjsInputHandover.getId())) {
                            bzjsInputHandover.setId(TMUID.getUID());
                            bzjsInputHandover.setIptDataId(saveData.getId());
                            bzjsInputHandover.setIptDatamxId(bzjsInputDatamx.getId());
                            bzjsInputHandover.setTmused(1);
                            bzjsInputHandover.setOperTime(DateTimeUtils.getNowDate());
                            bzjsInputHandoverInsertList.add(bzjsInputHandover);
                            bzjsInputHandoverVo.setId(bzjsInputHandover.getId());
                            bzjsInputHandoverVo.setIptDataId(bzjsInputHandover.getIptDataId());
                            bzjsInputHandoverVo.setIptDatamxId(bzjsInputHandover.getIptDatamxId());
                            bzjsInputHandoverVo.setTmused(1);
                            bzjsInputHandoverVo.setOperTime(DateTimeUtils.getNowDate());
                        } else {
                            bzjsInputHandover.setOperTime(DateTimeUtils.getNowDate());
                            bzjsInputHandoverUpdateList.add(bzjsInputHandover);
                        }
                        //更新交班人员
                        String personId = bzjsInputHandover.getPersonId();
                        if (StringUtils.isEmpty(personId)) {
                            bzjsInputHandover.setPersonId(user.getId());
                            bzjsInputHandover.setPersonName(user.getRealName());
                            bzjsInputHandoverVo.setPersonId(user.getId());
                            bzjsInputHandoverVo.setPersonName(user.getRealName());
                        } else {
                            String accntObjId = "";
                            String accntObjIds = param.getAccntObjIds();
                            if (StringUtils.isNotEmpty(accntObjIds)) {
                                String[] accntObjIdArr = accntObjIds.split(",");
                                accntObjId = accntObjIdArr[0];
                            }
                            long startTime = System.currentTimeMillis();
                            String status = iCostService.userOrgIsManageOrg(accntObjId, user.getOrgId(), user.getId(), user.getPostId());
                            long endTime = System.currentTimeMillis();
                            log.info("操作班组人员接口 - 人员状态：{}（核算对象ID：{}，机构ID：{}）（耗时：{}ms）", status, accntObjId, user.getOrgId(), (endTime - startTime));
                            if ("operate".equals(status)) { //操作班组人员
                                bzjsInputHandover.setPersonId(user.getId());
                                bzjsInputHandover.setPersonName(user.getRealName());
                                bzjsInputHandoverVo.setPersonId(user.getId());
                                bzjsInputHandoverVo.setPersonName(user.getRealName());
                            }
                        }
                    }
                } else if (BzjsConstant.DATA_SOURCE_TAKEOVERINFO.equals(dataSource)) { //接班信息
                    //List<BzjsInputTakeover> bzjsInputTakeoverDataList = (List<BzjsInputTakeover>) mxData;
                    //List<BzjsInputTakeover> bzjsInputTakeoverDataList = copyList((JSONArray) mxData, BzjsInputTakeover.class);
                    List<BzjsInputTakeover> bzjsInputTakeoverDataList = ((JSONArray) mxData).toJavaList(BzjsInputTakeover.class);
                    bzjsInputmxVo.setData(bzjsInputTakeoverDataList);
                    for (BzjsInputTakeover bzjsInputTakeoverVo : bzjsInputTakeoverDataList) {
                        BzjsInputTakeover bzjsInputTakeover = ObjUtils.copyTo(bzjsInputTakeoverVo, BzjsInputTakeover.class);
                        if (StringUtils.isEmpty(bzjsInputTakeover.getId())) {
                            bzjsInputTakeover.setId(TMUID.getUID());
                            bzjsInputTakeover.setIptDataId(saveData.getId());
                            bzjsInputTakeover.setIptDatamxId(bzjsInputDatamx.getId());
                            bzjsInputTakeover.setTmused(1);
                            bzjsInputTakeoverInsertList.add(bzjsInputTakeover);
                            bzjsInputTakeoverVo.setId(bzjsInputTakeover.getId());
                            bzjsInputTakeoverVo.setIptDataId(bzjsInputTakeover.getIptDataId());
                            bzjsInputTakeoverVo.setIptDatamxId(bzjsInputTakeover.getIptDatamxId());
                            bzjsInputTakeoverVo.setTmused(1);
                        } else {
                            bzjsInputTakeoverUpdateList.add(bzjsInputTakeover);
                        }
                    }
                } else if (BzjsConstant.DATA_SOURCE_HSZB.equals(dataSource)) { //核算指标
                    //List<BzjsInputHszb> bzjsInputHszbDataList = (List<BzjsInputHszb>) mxData;
                    //List<BzjsInputHszb> bzjsInputHszbDataList = copyList((JSONArray) mxData, BzjsInputHszb.class);
                    List<BzjsInputHszb> bzjsInputHszbDataList = ((JSONArray) mxData).toJavaList(BzjsInputHszb.class);
                    bzjsInputmxVo.setData(bzjsInputHszbDataList);
                    if (ObjUtils.notEmpty(bzjsInputHszbDataList)) {
                        //获取当班/上个班次的核算指标数据
                        Map<String, BzjsInputHszb> hszbMap = null;
                        List<BzjsInputHszb> hszbData = getHszbData(param, bzjsInputmxVo.getDataType());
                        if (ObjUtils.notEmpty(hszbData)) {
                            hszbMap = hszbData.stream().collect(Collectors.toMap(BzjsInputHszb::getZbbm, b -> b));
                        }
                        if (ObjUtils.isEmpty(hszbMap)) {
                            hszbMap = new HashMap<>();
                        }

                        for (BzjsInputHszb bzjsInputHszbVo : bzjsInputHszbDataList) {
                            BzjsInputHszb bzjsInputHszb = ObjUtils.copyTo(bzjsInputHszbVo, BzjsInputHszb.class);
                            //如果前台核算指标值为空值，或者前台核算指标值非空且为用户无法修改的自动提取项，则自动填入当班/上个班次的的核算指标值
                            if (StringUtils.isEmpty(bzjsInputHszb.getValue()) || StringUtils.isEmpty(bzjsInputHszb.getCanModified()) || "f".equals(bzjsInputHszb.getCanModified().toLowerCase())) {
                                BzjsInputHszb findHszb = hszbMap.get(bzjsInputHszb.getZbbm());
                                if (findHszb != null) {
                                    bzjsInputHszb.setValue(findHszb.getValue());
                                    bzjsInputHszbVo.setValue(findHszb.getValue());
                                }
                            }

                            if (StringUtils.isEmpty(bzjsInputHszb.getId())) { //添加
                                bzjsInputHszb.setId(TMUID.getUID());
                                bzjsInputHszb.setIptDataId(saveData.getId());
                                bzjsInputHszb.setIptDatamxId(bzjsInputDatamx.getId());
                                bzjsInputHszb.setTmused(1);
                                bzjsInputHszbInsertList.add(bzjsInputHszb);
                                bzjsInputHszbVo.setId(bzjsInputHszb.getId());
                                bzjsInputHszbVo.setIptDataId(bzjsInputHszb.getIptDataId());
                                bzjsInputHszbVo.setIptDatamxId(bzjsInputHszb.getIptDatamxId());
                                bzjsInputHszbVo.setTmused(1);
                            } else { //修改
                                bzjsInputHszbUpdateList.add(bzjsInputHszb);
                            }
                        }
                    }

                } else if (BzjsConstant.DATA_SOURCE_SGLR.equals(dataSource)) { //手工录入
                    //List<BzjsInputText> bzjsInputTextDataList = (List<BzjsInputText>) mxData;
                    //List<BzjsInputText> bzjsInputTextDataList = copyList((JSONArray) mxData, BzjsInputText.class);
                    List<BzjsInputText> bzjsInputTextDataList = ((JSONArray) mxData).toJavaList(BzjsInputText.class);
                    bzjsInputmxVo.setData(bzjsInputTextDataList);
                    for (BzjsInputText bzjsInputTextVo : bzjsInputTextDataList) {
                        BzjsInputText bzjsInputText = ObjUtils.copyTo(bzjsInputTextVo, BzjsInputText.class);
                        if (StringUtils.isEmpty(bzjsInputText.getId())) {
                            bzjsInputText.setId(TMUID.getUID());
                            bzjsInputText.setIptDataId(saveData.getId());
                            bzjsInputText.setIptDatamxId(bzjsInputDatamx.getId());
                            bzjsInputText.setTmused(1);
                            bzjsInputTextInsertList.add(bzjsInputText);
                            bzjsInputTextVo.setId(bzjsInputText.getId());
                            bzjsInputTextVo.setIptDataId(bzjsInputText.getIptDataId());
                            bzjsInputTextVo.setIptDatamxId(bzjsInputText.getIptDatamxId());
                            bzjsInputTextVo.setTmused(1);
                        } else {
                            bzjsInputTextUpdateList.add(bzjsInputText);
                        }
                    }
                } else if (BzjsConstant.DATA_SOURCE_GJSB.equals(dataSource)) { //关键设备
                    //List<BzjsInputGjsb> bzjsInputGjsbDataList = (List<BzjsInputGjsb>) mxData;
                    //List<BzjsInputGjsb> bzjsInputGjsbDataList = copyList((JSONArray) mxData, BzjsInputGjsb.class);
                    List<BzjsInputGjsb> bzjsInputGjsbDataList = ((JSONArray) mxData).toJavaList(BzjsInputGjsb.class);
                    bzjsInputmxVo.setData(bzjsInputGjsbDataList);
                    if (ObjUtils.notEmpty(bzjsInputGjsbDataList)) {
                        //获取当班/上个班次的关键设备数据
                        Map<String, BzjsInputGjsb> gjsbMap = null;
                        List<BzjsInputGjsb> gjsbData = getGjsbData(param, bzjsInputmxVo.getDataType());
                        if (ObjUtils.notEmpty(gjsbData)) {
                            gjsbMap = gjsbData.stream().collect(Collectors.toMap(BzjsInputGjsb::getSbbm, b -> b));
                        }
                        if (ObjUtils.isEmpty(gjsbMap)) {
                            gjsbMap = new HashMap<>();
                        }

                        for (BzjsInputGjsb bzjsInputGjsbVo : bzjsInputGjsbDataList) {
                            BzjsInputGjsb bzjsInputGjsb = ObjUtils.copyTo(bzjsInputGjsbVo, BzjsInputGjsb.class);
                            //如果前台关键设备值为空值，则自动填入当班/上个班次的的关键设备值
                            //if (StringUtils.isEmpty(bzjsInputGjsb.getSbzt())) {
                            //if (bzjsInputGjsb.getSbzt() == null) { //空字符串判定为人为修改，不使用初始化数据覆盖，保存空字符串
                            if (!(bzjsInputGjsb.getAltered() != null && bzjsInputGjsb.getAltered() == 1)) { //非人为手动修改的，使用初始化数据覆盖
                                BzjsInputGjsb findGjsb = gjsbMap.get(bzjsInputGjsb.getSbbm());
                                if (findGjsb != null) {
                                    bzjsInputGjsb.setSbzt(findGjsb.getSbzt());
                                    bzjsInputGjsbVo.setSbzt(findGjsb.getSbzt());
                                }
                            }

                            if (StringUtils.isEmpty(bzjsInputGjsb.getId())) { //添加
                                bzjsInputGjsb.setId(TMUID.getUID());
                                bzjsInputGjsb.setIptDataId(saveData.getId());
                                bzjsInputGjsb.setIptDatamxId(bzjsInputDatamx.getId());
                                bzjsInputGjsb.setTmused(1);
                                bzjsInputGjsbInsertList.add(bzjsInputGjsb);
                                bzjsInputGjsbVo.setId(bzjsInputGjsb.getId());
                                bzjsInputGjsbVo.setIptDataId(bzjsInputGjsb.getIptDataId());
                                bzjsInputGjsbVo.setIptDatamxId(bzjsInputGjsb.getIptDatamxId());
                                bzjsInputGjsbVo.setTmused(1);
                            } else { //修改
                                bzjsInputGjsbUpdateList.add(bzjsInputGjsb);
                            }
                        }
                    }
                } else if (BzjsConstant.DATA_SOURCE_QRQZ.equals(dataSource)) { //确认签字
                    //List<BzjsInputConfirm> bzjsInputConfirmDataList = (List<BzjsInputConfirm>) mxData;
                    //List<BzjsInputConfirm> bzjsInputConfirmDataList = copyList((JSONArray) mxData, BzjsInputConfirm.class);
                    List<BzjsInputConfirm> bzjsInputConfirmDataList = ((JSONArray) mxData).toJavaList(BzjsInputConfirm.class);
                    bzjsInputmxVo.setData(bzjsInputConfirmDataList);
                    for (BzjsInputConfirm bzjsInputConfirmVo : bzjsInputConfirmDataList) {
                        BzjsInputConfirm bzjsInputConfirm = ObjUtils.copyTo(bzjsInputConfirmVo, BzjsInputConfirm.class);
                        
                        if (StringUtils.isEmpty(bzjsInputConfirm.getId())) {
                            bzjsInputConfirm.setId(TMUID.getUID());
                            bzjsInputConfirm.setIptDataId(saveData.getId());
                            bzjsInputConfirm.setIptDatamxId(bzjsInputDatamx.getId());
                            bzjsInputConfirm.setTmused(1);
                            bzjsInputConfirmInsertList.add(bzjsInputConfirm);
                            bzjsInputConfirmVo.setId(bzjsInputConfirm.getId());
                            bzjsInputConfirmVo.setIptDataId(bzjsInputConfirm.getIptDataId());
                            bzjsInputConfirmVo.setIptDatamxId(bzjsInputConfirm.getIptDatamxId());
                            bzjsInputConfirmVo.setTmused(1);

                            Integer confirmed = bzjsInputConfirm.getConfirmed();
                            if (confirmed != null && confirmed == 1) {
                                ifRefreshTodoData = true;
                            }
                        } else {
                            bzjsInputConfirmUpdateList.add(bzjsInputConfirm);
                        }
                        
                        Integer confirmed = bzjsInputConfirm.getConfirmed();
                        if (confirmed != null && confirmed == 1) {
                            ifRefreshTodoData = true;
                        }
                    }

                    if (ObjUtils.notEmpty(permData)) {
                        List<BzjsInputPermission> bzjsPermissionList = ((JSONArray) permData).toJavaList(BzjsInputPermission.class);
                        for (BzjsInputPermission bzjsPermission : bzjsPermissionList) {
                            if (StringUtils.isEmpty(bzjsPermission.getId())) {
                                bzjsPermission.setId(TMUID.getUID());
                                bzjsPermission.setIptDataId(saveData.getId());
                                bzjsPermission.setIptDatamxId(bzjsInputDatamx.getId());
                                bzjsPermission.setTmused(1);
                                bzjsInputPermissionInsertList.add(bzjsPermission);
                                //bzjsInputConfirmVo.setId(bzjsInputConfirm.getId());
                                //bzjsInputConfirmVo.setIptDataId(bzjsInputConfirm.getIptDataId());
                                //bzjsInputConfirmVo.setIptDatamxId(bzjsInputConfirm.getIptDatamxId());
                                //bzjsInputConfirmVo.setTmused(1);
                            } else {
                                bzjsInputPermissionUpdateList.add(bzjsPermission);
                            }
                        }
                    }
                } else if (BzjsConstant.DATA_SOURCE_COLLECTPOINT.equals(dataSource)) { //采集点
                    List<BzjsInputCollectPointFlVo> collectPointFlVos = ((JSONArray) mxData).toJavaList(BzjsInputCollectPointFlVo.class);
                    bzjsInputmxVo.setData(collectPointFlVos);
                    for (BzjsInputCollectPointFlVo collectPointFlVo : collectPointFlVos) { //采集点分类
                        List<BzjsInputCollectPoint> collectPointList = collectPointFlVo.getCollectPointList();
                        if(ObjUtils.isEmpty(collectPointList)){
                            continue;
                        }
                        for (BzjsInputCollectPoint collectPoint : collectPointList) { //采集点
                            if (StringUtils.isEmpty(collectPoint.getId())) { //添加
                                collectPoint.setId(TMUID.getUID());
                                collectPoint.setIptDataId(saveData.getId());
                                collectPoint.setIptDatamxId(bzjsInputDatamx.getId());
                                collectPoint.setTmused(1);
                                //填充分类信息
                                collectPoint.setCollectPointFlId(collectPointFlVo.getCollectPointFlId());
                                collectPoint.setCollectPointFlName(collectPointFlVo.getCollectPointFlName());
                                collectPoint.setCollectPointFlSn(collectPointFlVo.getCollectPointFlSn());
                                //TODO 完善其他字段
                                bzjsInputCollectPointInsertList.add(collectPoint);
                            } else { //修改
                                bzjsInputCollectPointUpdateList.add(collectPoint);
                            }
                        }
                    }
                }
            }
        }

        if (ObjUtils.notEmpty(bzjsInputDataInsertList)) {
            entityService.rawInsertBatch(bzjsInputDataInsertList);
        }
        if (ObjUtils.notEmpty(bzjsInputDataUpdateList)) {
            entityService.updateBatch(bzjsInputDataUpdateList);
        }
        if (ObjUtils.notEmpty(bzjsInputDatamxInsertList)) {
            entityService.rawInsertBatch(bzjsInputDatamxInsertList);
        }
        if (ObjUtils.notEmpty(bzjsInputDatamxUpdateList)) {
            entityService.updateBatch(bzjsInputDatamxUpdateList);
        }
        if (ObjUtils.notEmpty(bzjsInputHandoverInsertList)) {
            entityService.rawInsertBatch(bzjsInputHandoverInsertList);
        }
        if (ObjUtils.notEmpty(bzjsInputHandoverUpdateList)) {
            entityService.updateBatch(bzjsInputHandoverUpdateList);
        }
        if (ObjUtils.notEmpty(bzjsInputTakeoverInsertList)) {
            entityService.rawInsertBatch(bzjsInputTakeoverInsertList);
        }
        if (ObjUtils.notEmpty(bzjsInputTakeoverUpdateList)) {
            entityService.updateBatch(bzjsInputTakeoverUpdateList);
        }
        if (ObjUtils.notEmpty(bzjsInputHszbInsertList)) {
            entityService.rawInsertBatch(bzjsInputHszbInsertList);
        }
        if (ObjUtils.notEmpty(bzjsInputHszbUpdateList)) {
            entityService.updateBatch(bzjsInputHszbUpdateList);
        }
        if (ObjUtils.notEmpty(bzjsInputTextInsertList)) {
            entityService.rawInsertBatch(bzjsInputTextInsertList);
        }
        if (ObjUtils.notEmpty(bzjsInputTextUpdateList)) {
            entityService.updateBatch(bzjsInputTextUpdateList);
        }
        if (ObjUtils.notEmpty(bzjsInputGjsbInsertList)) {
            entityService.rawInsertBatch(bzjsInputGjsbInsertList);
        }
        if (ObjUtils.notEmpty(bzjsInputGjsbUpdateList)) {
            entityService.updateBatch(bzjsInputGjsbUpdateList);
        }
        if (ObjUtils.notEmpty(bzjsInputConfirmInsertList)) {
            entityService.rawInsertBatch(bzjsInputConfirmInsertList);
        }
        if (ObjUtils.notEmpty(bzjsInputConfirmUpdateList)) {
            entityService.updateBatch(bzjsInputConfirmUpdateList);
        }
        if (ObjUtils.notEmpty(bzjsInputPermissionInsertList)) {
            entityService.rawInsertBatch(bzjsInputPermissionInsertList);
        }
        if (ObjUtils.notEmpty(bzjsInputPermissionUpdateList)) {
            entityService.updateBatch(bzjsInputPermissionUpdateList);
        }
        if (ObjUtils.notEmpty(bzjsInputCollectPointInsertList)) {
            entityService.rawInsertBatch(bzjsInputCollectPointInsertList);
        }
        if (ObjUtils.notEmpty(bzjsInputCollectPointUpdateList)) {
            entityService.updateBatch(bzjsInputCollectPointUpdateList);
        }

        //TODO 如后续有需要返回更新id后的数据，就把下行代码注释去掉
        //resData = saveData;

        if (ifRefreshTodoData) {
            iAccountToolsService.refreshAccountConfirmTodo(null);
            log.info("班组记事保存，刷新待办数据");
        }

        return resData;
    }

    /**
     * 将JSON数组转换为实体类对象列表
     * @param jsonArray JSON数组
     * @param targetCls 实体类
     * @return
     * @param <T>
     */
    private <T> List<T> copyList(JSONArray jsonArray, Class<T> targetCls) {
        List<T> list = new ArrayList<>();

        if (jsonArray != null && jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                try {
                    T t = targetCls.newInstance();
                    t = copy(t, jsonObject);
                    list.add(t);
                } catch (Exception e) {
                    log.error("班组记事 - JSON数组转换失败", e);
                }
            }
        }

        return list;
    }

    /**
     * 将JSON对象转换为实体类对象
     * @param target
     * @param jsonObj
     * @return
     * @param <T>
     */
    private <T> T copy(T target, JSONObject jsonObj) {
        if (jsonObj == null || target == null) {
            return target;
        }
        final BeanWrapper trg = new BeanWrapperImpl(target);
        for (final Field property : target.getClass().getDeclaredFields()) {
            if (property.getName().equalsIgnoreCase("serialVersionUID")) {
                continue;
            }
            Type genericType = property.getGenericType();
            String fieldName = property.getName();
            if (jsonObj.containsKey(fieldName)) {
                Object value = jsonObj.get(fieldName);
                if (value != null) {
                    if ("java.util.Date".equals(genericType.getTypeName())) {
                        String valStr = value.toString();
                        trg.setPropertyValue(fieldName, DateTimeUtils.parseDate(valStr));
                    } else {
                        trg.setPropertyValue(fieldName, value);
                    }
                }
            }
        }
        return target;
    }

    /**
     * 获取上班/本班信息（倒班信息）
     * @param dataType
     * @return
     */
    private ShiftForeignVo getShiftInfo(String dataType) {
        ShiftForeignVo res = null;
        SysUser user = SysUserHolder.getCurrentUser();

        if (BzjsConstant.DATA_TYPE_CURRENT_SHIFT.equals(dataType)) {
            long startTime = System.currentTimeMillis();
            ShiftForeignVo shiftByDateTime = shiftService.getShiftByDateTime(null, null);
            if (shiftByDateTime != null && StringUtils.isNotEmpty(shiftByDateTime.getSbsj())) {
                res = shiftByDateTime;
                long endTime = System.currentTimeMillis();
                log.info("倒班数据接口 - 本班数据：{}（用户：{}，机构：{}）（耗时：{}ms）", JSONObject.toJSONString(res), user.getRealName(), user.getOrgName(), (endTime - startTime));
            } else {
                long endTime = System.currentTimeMillis();
                log.info("倒班数据接口 - 本班数据：暂无倒班数据（用户：{}，机构：{}）（耗时：{}ms）", user.getRealName(), user.getOrgName(), (endTime - startTime));
            }
        } else if (BzjsConstant.DATA_TYPE_LAST_SHIFT.equals(dataType)) {
            long startTime = System.currentTimeMillis();
            ShiftForeignVo shiftByDateTime = shiftService.getShiftByDateTime(null, null);
            if (shiftByDateTime != null && StringUtils.isNotEmpty(shiftByDateTime.getSbsj()) && StringUtils.isNotEmpty(shiftByDateTime.getOrgCode())) {
                ShiftForeignVo pShiftByDateTime = shiftService.getPShiftByDateTime(shiftByDateTime.getSbsj(), shiftByDateTime.getOrgCode());
                if (pShiftByDateTime != null && StringUtils.isNotEmpty(pShiftByDateTime.getSbsj())) {
                    res = pShiftByDateTime;
                    long endTime = System.currentTimeMillis();
                    log.info("倒班数据接口 - 上班数据：{}；本班数据：{}（用户：{}，机构：{}）（耗时：{}ms）", JSONObject.toJSONString(res), JSONObject.toJSONString(shiftByDateTime), user.getRealName(), user.getOrgName(), (endTime - startTime));
                } else {
                    long endTime = System.currentTimeMillis();
                    log.info("倒班数据接口 - 上班数据：暂无倒班数据；本班数据：{}（用户：{}，机构：{}）（耗时：{}ms）", JSONObject.toJSONString(shiftByDateTime), user.getRealName(), user.getOrgName(), (endTime - startTime));
                }
            } else {
                long endTime = System.currentTimeMillis();
                log.info("倒班数据接口 - 上班数据：暂无倒班数据；本班数据：暂无倒班数据（用户：{}，机构：{}）（耗时：{}ms）", user.getRealName(), user.getOrgName(), (endTime - startTime));
            }
        }

        return res;
    }

    /**
     * 获取确认人数据
     *
     * @param param
     * @return
     */
    public BzjsInputConfirmVo getConfirmPersonInfo(BzjsInputDto param) {
        SysUser user = SysUserHolder.getCurrentUser();
        BzjsInputConfirmVo confirmVo = new BzjsInputConfirmVo();
        confirmVo.setPersonId(user.getId());
        confirmVo.setPersonName(user.getRealName());
        confirmVo.setTmused(1);
        confirmVo.setGwmc(user.getPostName());
        confirmVo.setGwdm(user.getPostId());
        confirmVo.setOperTime(DateTimeUtils.getNowDate());
        return confirmVo;
    }

    /**
     * 获取关键设备状态配置数据
     * @return
     */
    @Override
    public List<BzjsGjsbStatusVo> getBzjsGjsbStatus() {
        List<BzjsGjsbStatusVo> voList = new ArrayList<>();

        Where where = new Where();
        where.eq(BzjsGjsbStatus::getTmused, 1);

        Order order = new Order();
        order.orderByAsc(BzjsGjsbStatus::getSn);

        Boolean isTenant = MultiTenantUtils.enalbe();
        List<BzjsGjsbStatus> list = null;
        if (isTenant != null && isTenant == true) { //多租户模式
            list = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsGjsbStatus.class, where);
        } else { //普通模式
            list = entityService.queryList(BzjsGjsbStatus.class, where);
        }
        if (ObjUtils.notEmpty(list)) {
            voList = ObjUtils.convertToList(BzjsGjsbStatusVo.class, list);
        }

        return voList;
    }

    /**
     * 获取核算指标数据
     * @param param
     * @param dataType
     * @return
     */
    private List<BzjsInputHszb> getHszbData(BzjsInputDto param, String dataType) {
        List<BzjsInputHszb> bzjsInputHszbData = new ArrayList<>();

        TeamRecordDto tRDto = new TeamRecordDto();
        tRDto.setUnitIdList(param.getAccntObjIds());
        tRDto.setWriteDay(param.getWriteDay());
        tRDto.setTeamId(param.getTeamId());
        tRDto.setShiftId(param.getShiftId());
        tRDto.setBeginTime(param.getShiftBeginTime());
        tRDto.setEndTime(param.getShiftEndTime());
        tRDto.setAccountId(param.getAccountId());
        long startTime = System.currentTimeMillis();


        try {
            List<TeamRecordDataVo> shiftDataList = getTeamRecordService.getShiftData(tRDto);
            if (ObjUtils.notEmpty(shiftDataList)) {
                long endTime = System.currentTimeMillis();
                log.info("核算数据接口 - 核算指标数据：{}（接口参数：{}）（耗时：{}ms）", JSONArray.toJSONString(shiftDataList), JSONObject.toJSONString(tRDto), (endTime - startTime));

                int sn = 1;
                for (TeamRecordDataVo teamRecordDataVo : shiftDataList) {
                    BzjsInputHszb hszb = new BzjsInputHszb();
                    hszb.setFormDataId(param.getFormDataId());
                    hszb.setTmused(1);
                    hszb.setZbbm(teamRecordDataVo.getItemId());
                    hszb.setZbmc(teamRecordDataVo.getItemName());
                    hszb.setSn(sn);
                    hszb.setCanModified(teamRecordDataVo.getCanModified());
                    try {
                        hszb.setValue(teamRecordDataVo.getFetchValue());
                    } catch (Exception e) {
                        hszb.setValue(null);
                    }
                    hszb.setItemUnit(teamRecordDataVo.getItemUnit());
                    bzjsInputHszbData.add(hszb);
                    sn++;
                }

                //带入上班数据
                if (BzjsConstant.DATA_TYPE_LAST_SHIFT.equals(dataType)) {
                    List<BzjsInputHszb> lastShiftHszbData = getLastShiftHszbData(param);
                    if (ObjUtils.notEmpty(lastShiftHszbData)) {
                        Map<String, BzjsInputHszb> lastShiftDataMap = lastShiftHszbData.stream().collect(Collectors.toMap(BzjsInputHszb::getZbbm, b -> b));
                        for (BzjsInputHszb hszb : bzjsInputHszbData) {
                            BzjsInputHszb lastShiftData = lastShiftDataMap.get(hszb.getZbbm());
                            if (lastShiftData != null) {
                                hszb.setValue(lastShiftData.getValue());
                            }
                        }
                    }
                }
            } else {
                long endTime = System.currentTimeMillis();
                log.info("核算数据接口 - 核算指标数据：暂无数据（接口参数：{}）（耗时：{}ms）", JSONObject.toJSONString(tRDto), (endTime - startTime));
            }
        } catch (Exception e) {
            log.error("核算数据接口 - 核算指标数据：获取核算对象的核算数据失败（接口参数：{}）", JSONObject.toJSONString(tRDto), e);
        }

        return bzjsInputHszbData;
    }

    /**
     * 获取上个班次的核算指标数据
     * @param param
     * @return
     */
    private List<BzjsInputHszb> getLastShiftHszbData(BzjsInputDto param) {
        List<BzjsInputHszb> list = new ArrayList<>();

        String shiftBeginTime = param.getShiftBeginTime();
        String teamId = param.getTeamId();
        String tplId = param.getTplId();
        String accountId = param.getAccountId();

        log.info("班组记事 - 获取上个班次的核算指标数据：");
        ShiftForeignVo pShiftByDateTime = shiftService.getPShiftByDateTime(shiftBeginTime, teamId);
        if (pShiftByDateTime != null && StringUtils.isNotEmpty(pShiftByDateTime.getSbsj())) {
            log.info("倒班数据接口 - 上班数据：{}（当前上班时间：{}，班组：{}）", JSONObject.toJSONString(pShiftByDateTime), shiftBeginTime, param.getTeamName());

            //查主数据
            String sbsj = pShiftByDateTime.getSbsj();
            Date sbsjDt = null;
            try {
                sbsjDt = DateTimeUtils.parseDate(sbsj);
            } catch (Exception e) {
                log.error("上班时间解析失败（上班时间：{}）", sbsj, e);
                return list;
            }
            //String xbsj = pShiftByDateTime.getXbsj();
            String accntObjIds = param.getAccntObjIds();

            Where where = new Where();
            where.eq(BzjsInputData::getTmused, 1);
            where.eq(BzjsInputData::getTplId, tplId);
            where.eq(BzjsInputData::getAccntObjIds, accntObjIds);
            where.eq(BzjsInputData::getSbsj, sbsjDt);
            if (StringUtils.isNotEmpty(accountId)) {
                where.eq(BzjsInputData::getAccountId, accountId);
            }

            Boolean isTenant = MultiTenantUtils.enalbe();
            List<BzjsInputData> bzjsInputDataList = null;
            if (isTenant != null && isTenant == true) { //多租户模式
                bzjsInputDataList = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputData.class, where);
            } else { //普通模式
                bzjsInputDataList = entityService.queryList(BzjsInputData.class, where);
            }
            if (ObjUtils.isEmpty(bzjsInputDataList)) {
                log.info("无主数据（上班时间：{},核算对象ID：{}）", sbsj, accntObjIds);
                return list;
            }
            BzjsInputData bzjsInputData = bzjsInputDataList.get(0);

            //查栏目数据
            where = new Where();
            where.eq(BzjsInputDatamx::getTmused, 1);
            where.eq(BzjsInputDatamx::getIptDataId, bzjsInputData.getId());
            where.eq(BzjsInputDatamx::getTplDetailId, param.getTplDetailId());
            List<BzjsInputDatamx> bzjsInputDatamxList = null;
            if (isTenant != null && isTenant == true) { //多租户模式
                bzjsInputDatamxList = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputDatamx.class, where);
            } else { //普通模式
                bzjsInputDatamxList = entityService.queryList(BzjsInputDatamx.class, where);
            }
            if (ObjUtils.isEmpty(bzjsInputDatamxList)) {
                log.info("无栏目数据（上班时间：{},核算对象ID：{},主数据ID：{}，模版栏目ID：{}）", sbsj, accntObjIds, bzjsInputData.getId(), param.getTplDetailId());
                return list;
            }
            BzjsInputDatamx bzjsInputDatamx = bzjsInputDatamxList.get(0);

            //查核算指标数据
            where = new Where();
            where.eq(BzjsInputHszb::getTmused, 1);
            where.eq(BzjsInputHszb::getIptDataId, bzjsInputData.getId());
            where.eq(BzjsInputHszb::getIptDatamxId, bzjsInputDatamx.getId());
            List<BzjsInputHszb> bzjsInputHszbList = null;
            if (isTenant != null && isTenant == true) { //多租户模式
                bzjsInputHszbList = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputHszb.class, where);
            } else { //普通模式
                bzjsInputHszbList = entityService.queryList(BzjsInputHszb.class, where);
            }
            if (ObjUtils.isEmpty(bzjsInputHszbList)) {
                log.info("无核算指标数据（上班时间：{},核算对象ID：{},主数据ID：{}，模版栏目ID：{}）", sbsj, accntObjIds, bzjsInputData.getId(), param.getTplDetailId());
                return list;
            }
            list = bzjsInputHszbList;
            log.info("班组记事 - 上个班次的核算指标数据：{}", JSONArray.toJSONString(list));
        } else {
            log.info("倒班数据接口 - 上班数据：暂无倒班数据（当前上班时间：{}，班组：{}）", shiftBeginTime, param.getTeamName());
        }

        return list;
    }

    /**
     * 获取关键设备数据
     * @param param
     * @param dataType
     * @return
     */
    private List<BzjsInputGjsb> getGjsbData(BzjsInputDto param, String dataType) {
        List<BzjsInputGjsb> bzjsInputGjsbData = new ArrayList<>();

        TeamRecordDto tRDto = new TeamRecordDto();
        tRDto.setUnitIdList(param.getAccntObjIds());
        tRDto.setWriteDay(param.getWriteDay());
        tRDto.setTeamId(param.getTeamId());
        tRDto.setShiftId(param.getShiftId());
        tRDto.setBeginTime(param.getShiftBeginTime());
        tRDto.setEndTime(param.getShiftEndTime());
        tRDto.setAccountId(param.getAccountId());
        long startTime = System.currentTimeMillis();
        List<TeamRecordDataVo> deviceDataList = getTeamRecordService.getDeviceData(tRDto);
        if (ObjUtils.notEmpty(deviceDataList)) {
            long endTime = System.currentTimeMillis();
            log.info("核算数据接口 - 关键设备数据：{}（接口参数：{}）（耗时：{}ms）", JSONArray.toJSONString(deviceDataList), JSONObject.toJSONString(tRDto), (endTime - startTime));

            int sn = 1;
            for (TeamRecordDataVo teamRecordDataVo : deviceDataList) {
                BzjsInputGjsb gjsb = new BzjsInputGjsb();
                gjsb.setFormDataId(param.getFormDataId());
                gjsb.setTmused(1);
                gjsb.setSbbm(teamRecordDataVo.getItemId());
                gjsb.setSbmc(teamRecordDataVo.getItemName());
                gjsb.setSbzt(teamRecordDataVo.getFetchValue());
                List<String> optionValue = teamRecordDataVo.getOptionValue();
                if (ObjUtils.notEmpty(optionValue)) {
                    JSONArray optionValueJsArr = new JSONArray();
                    for (String s : optionValue) {
                        JSONObject optionValueJsObj = new JSONObject();
                        optionValueJsObj.put("value", s);
                        optionValueJsArr.add(optionValueJsObj);
                    }
                    gjsb.setSbOptions(optionValueJsArr.toJSONString());
                }
                gjsb.setItemUnit(teamRecordDataVo.getItemUnit());
                gjsb.setSn(sn);
                bzjsInputGjsbData.add(gjsb);
                sn++;
            }

            //带入上班数据
            if (BzjsConstant.DATA_TYPE_LAST_SHIFT.equals(dataType)) {
                List<BzjsInputGjsb> lastShiftGjsbData = getLastShiftGjsbData(param);
                if (ObjUtils.notEmpty(lastShiftGjsbData)) {
                    Map<String, BzjsInputGjsb> lastShiftDataMap = lastShiftGjsbData.stream().collect(Collectors.toMap(BzjsInputGjsb::getSbbm, b -> b));
                    for (BzjsInputGjsb gjsb : bzjsInputGjsbData) {
                        BzjsInputGjsb lastShiftData = lastShiftDataMap.get(gjsb.getSbbm());
                        if (lastShiftData != null) {
                            gjsb.setSbzt(lastShiftData.getSbzt());
                        }
                    }
                }
            }
        } else {
            long endTime = System.currentTimeMillis();
            log.info("核算数据接口 - 关键设备数据：暂无数据（接口参数：{}）（耗时：{}ms）", JSONObject.toJSONString(tRDto), (endTime - startTime));
        }

        return bzjsInputGjsbData;
    }

    /**
     * 获取上个班次的关键设备数据
     * @param param
     * @return
     */
    private List<BzjsInputGjsb> getLastShiftGjsbData(BzjsInputDto param) {
        List<BzjsInputGjsb> list = new ArrayList<>();

        String shiftBeginTime = param.getShiftBeginTime();
        String teamId = param.getTeamId();
        String tplId = param.getTplId();
        String accountId = param.getAccountId();

        log.info("班组记事 - 获取上个班次的关键设备数据：");
        ShiftForeignVo pShiftByDateTime = shiftService.getPShiftByDateTime(shiftBeginTime, teamId);
        if (pShiftByDateTime != null && StringUtils.isNotEmpty(pShiftByDateTime.getSbsj())) {
            log.info("倒班数据接口 - 上班数据：{}（当前上班时间：{}，班组：{}）", JSONObject.toJSONString(pShiftByDateTime), shiftBeginTime, param.getTeamName());

            //查主数据
            String sbsj = pShiftByDateTime.getSbsj();
            Date sbsjDt = null;
            try {
                sbsjDt = DateTimeUtils.parseDate(sbsj);
            } catch (Exception e) {
                log.error("上班时间解析失败（上班时间：{}）", sbsj, e);
                return list;
            }
            //String xbsj = pShiftByDateTime.getXbsj();
            String accntObjIds = param.getAccntObjIds();

            Where where = new Where();
            where.eq(BzjsInputData::getTmused, 1);
            where.eq(BzjsInputData::getTplId, tplId);
            where.eq(BzjsInputData::getSbsj, sbsjDt);
            where.eq(BzjsInputData::getAccntObjIds, accntObjIds);
            if (StringUtils.isNotEmpty(accountId)) {
                where.eq(BzjsInputData::getAccountId, accountId);
            }

            Boolean isTenant = MultiTenantUtils.enalbe();
            List<BzjsInputData> bzjsInputDataList = null;
            if (isTenant != null && isTenant == true) { //多租户模式
                bzjsInputDataList = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputData.class, where);
            } else { //普通模式
                bzjsInputDataList = entityService.queryList(BzjsInputData.class, where);
            }
            if (ObjUtils.isEmpty(bzjsInputDataList)) {
                log.info("无主数据（上班时间：{},核算对象ID：{}）", sbsj, accntObjIds);
                return list;
            }
            BzjsInputData bzjsInputData = bzjsInputDataList.get(0);

            //查栏目数据
            where = new Where();
            where.eq(BzjsInputDatamx::getTmused, 1);
            where.eq(BzjsInputDatamx::getIptDataId, bzjsInputData.getId());
            where.eq(BzjsInputDatamx::getTplDetailId, param.getTplDetailId());
            List<BzjsInputDatamx> bzjsInputDatamxList = null;
            if (isTenant != null && isTenant == true) { //多租户模式
                bzjsInputDatamxList = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputDatamx.class, where);
            } else { //普通模式
                bzjsInputDatamxList = entityService.queryList(BzjsInputDatamx.class, where);
            }
            if (ObjUtils.isEmpty(bzjsInputDatamxList)) {
                log.info("无栏目数据（上班时间：{},核算对象ID：{},主数据ID：{}，模版栏目ID：{}）", sbsj, accntObjIds, bzjsInputData.getId(), param.getTplDetailId());
                return list;
            }
            BzjsInputDatamx bzjsInputDatamx = bzjsInputDatamxList.get(0);

            //查关键设备数据
            where = new Where();
            where.eq(BzjsInputGjsb::getTmused, 1);
            where.eq(BzjsInputGjsb::getIptDataId, bzjsInputData.getId());
            where.eq(BzjsInputGjsb::getIptDatamxId, bzjsInputDatamx.getId());
            List<BzjsInputGjsb> bzjsInputGjsbList = null;
            if (isTenant != null && isTenant == true) { //多租户模式
                bzjsInputGjsbList = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputGjsb.class, where);
            } else { //普通模式
                bzjsInputGjsbList = entityService.queryList(BzjsInputGjsb.class, where);
            }
            if (ObjUtils.isEmpty(bzjsInputGjsbList)) {
                log.info("无关键设备数据（上班时间：{},核算对象ID：{},主数据ID：{}，模版栏目ID：{}）", sbsj, accntObjIds, bzjsInputData.getId(), param.getTplDetailId());
                return list;
            }
            list = bzjsInputGjsbList;
            log.info("班组记事 - 上个班次的关键设备数据：{}", JSONArray.toJSONString(list));
        } else {
            log.info("倒班数据接口 - 上班数据：暂无倒班数据（当前上班时间：{}，班组：{}）", shiftBeginTime, param.getTeamName());
        }

        return list;
    }

    /**
     * 获取上个班次的交班信息
     * @return
     */
    private BzjsInputTakeover getLastShiftHandoverData(BzjsInputDto param) {
        BzjsInputTakeover lastShiftHandoverData = null;

        String shiftBeginTime = param.getShiftBeginTime();
        String teamId = param.getTeamId();
        String tplId = param.getTplId();
        String accountId = param.getAccountId();

        log.info("班组记事 - 获取上个班次的交班数据：");
        ShiftForeignVo pShiftByDateTime = shiftService.getPShiftByDateTime(shiftBeginTime, teamId);
        if (pShiftByDateTime != null && StringUtils.isNotEmpty(pShiftByDateTime.getSbsj())) {
            log.info("倒班数据接口 - 上班数据：{}（当前上班时间：{}，班组：{}）", JSONObject.toJSONString(pShiftByDateTime), shiftBeginTime, param.getTeamName());

            //查主数据
            String sbsj = pShiftByDateTime.getSbsj();
            Date sbsjDt = null;
            try {
                sbsjDt = DateTimeUtils.parseDate(sbsj);
            } catch (Exception e) {
                log.error("上班时间解析失败（上班时间：{}）", sbsj, e);
                return lastShiftHandoverData;
            }
            //String xbsj = pShiftByDateTime.getXbsj();
            String accntObjIds = param.getAccntObjIds();

            Where where = new Where();
            where.eq(BzjsInputData::getTmused, 1);
            where.eq(BzjsInputData::getTplId, tplId);
            where.eq(BzjsInputData::getAccntObjIds, accntObjIds);
            where.eq(BzjsInputData::getSbsj, sbsjDt);
            if (StringUtils.isNotEmpty(accountId)) {
                where.eq(BzjsInputData::getAccountId, accountId);
            }

            Boolean isTenant = MultiTenantUtils.enalbe();
            List<BzjsInputData> bzjsInputDataList = null;
            if (isTenant != null && isTenant == true) { //多租户模式
                bzjsInputDataList = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputData.class, where);
            } else { //普通模式
                bzjsInputDataList = entityService.queryList(BzjsInputData.class, where);
            }
            if (ObjUtils.isEmpty(bzjsInputDataList)) {
                log.info("无主数据（上班时间：{},核算对象ID：{}）", sbsj, accntObjIds);
                return lastShiftHandoverData;
            }
            BzjsInputData bzjsInputData = bzjsInputDataList.get(0);

            //查交班信息栏目数据
            where = new Where();
            where.eq(BzjsInputDatamx::getTmused, 1);
            where.eq(BzjsInputDatamx::getIptDataId, bzjsInputData.getId());
            //where.eq(BzjsInputDatamx::getTplDetailId, param.getTplDetailId());
            where.eq(BzjsInputDatamx::getDataSource, BzjsConstant.DATA_SOURCE_HANDOVERINFO);
            Order order = new Order();
            order.orderByAsc(BzjsInputDatamx::getSn);
            List<BzjsInputDatamx> bzjsInputDatamxList = null;
            if (isTenant != null && isTenant == true) { //多租户模式
                bzjsInputDatamxList = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputDatamx.class, where, order);
            } else { //普通模式
                bzjsInputDatamxList = entityService.queryList(BzjsInputDatamx.class, where, order);
            }
            if (ObjUtils.isEmpty(bzjsInputDatamxList)) {
                log.info("无栏目数据（上班时间：{},核算对象ID：{},主数据ID：{}，模版栏目ID：{}）", sbsj, accntObjIds, bzjsInputData.getId(), param.getTplDetailId());
                return lastShiftHandoverData;
            }

            //查交班数据
            for (BzjsInputDatamx bzjsInputDatamx : bzjsInputDatamxList) {
                //如果配置多个交班信息栏目，则取第一个有数据的那条
                where = new Where();
                where.eq(BzjsInputHandover::getTmused, 1);
                where.eq(BzjsInputHandover::getIptDataId, bzjsInputData.getId());
                where.eq(BzjsInputHandover::getIptDatamxId, bzjsInputDatamx.getId());
                List<BzjsInputHandover> bzjsInputHandoverList = null;
                if (isTenant != null && isTenant == true) { //多租户模式
                    bzjsInputHandoverList = entityService.rawQueryListWithTenant(MultiTenantUtils.getTenantId(), BzjsInputHandover.class, where);
                } else { //普通模式
                    bzjsInputHandoverList = entityService.queryList(BzjsInputHandover.class, where);
                }
                if (ObjUtils.isEmpty(bzjsInputHandoverList)) {
                    log.info("无交班数据（上班时间：{},核算对象ID：{},主数据ID：{}，模版栏目ID：{}）", sbsj, accntObjIds, bzjsInputData.getId(), param.getTplDetailId());
                    return lastShiftHandoverData;
                }
                BzjsInputHandover bzjsInputHandover = bzjsInputHandoverList.get(0);
                lastShiftHandoverData = ObjUtils.copyTo(bzjsInputHandover, BzjsInputTakeover.class);
                //上个班次交班信息的下班时间 -> 当前班次接班信息的上班时间
                lastShiftHandoverData.setTakeoverTime(bzjsInputHandover.getHandoverTime());
                lastShiftHandoverData.setId(null);
                lastShiftHandoverData.setFormDataId(param.getFormDataId());
                break;
            }
        } else {
            log.info("倒班数据接口 - 上班数据：暂无倒班数据（当前上班时间：{}，班组：{}）", shiftBeginTime, param.getTeamName());
        }

        return lastShiftHandoverData;
    }

    /**
     * 获取用户信息
     * @return
     */
    @Override
    public BzjsUserInfoVo getUserInfo() {
        BzjsUserInfoVo bzjsUserInfoVo = new BzjsUserInfoVo();
        SysUser user = SysUserHolder.getCurrentUser();
        String id = user.getId();
        String postId = user.getPostId();
        List<String> roles = user.getRoles();

        bzjsUserInfoVo.setUserId(id);
        bzjsUserInfoVo.setPostId(postId);
        bzjsUserInfoVo.setRoleIdList(roles);

        return bzjsUserInfoVo;
    }

    /**
     * 获取确认状态信息
     * @param dto 参数对象
     * @return
     */
    @Override
    public List<BzjsConfirmStatusVo> getConfirmStatusInfo(BzjsConfirmStatusDto dto) {
        List<BzjsConfirmStatusVo> list = new ArrayList<>();

        String roleIds = Optional.ofNullable(dto.getRoleIds()).orElse("");
        String roleIdsInStr = "'" + roleIds.replace(",", "','") + "'";

        String sqlStr = "select a.FORM_DATA_ID, a.INPUT_TIME, a.ACCNT_OBJ_IDS, a.BCDM, a.XBSJ from bzjs_input_data a left join bzjs_input_confirm b on b.tmused=1 and b.ipt_data_id=a.id where a.tmused=1 and b.tmused=1 " +
                " and a.xbsj >= ? and a.xbsj <= ? and (b.person_id is null or b.person_id = '')" +
                " and case when ( select count(*) from BZJS_INPUT_PERMISSION where tmused=1 and ipt_data_id=a.id )=0 then 1 when exists( select * from BZJS_INPUT_PERMISSION where tmused=1 and ipt_data_id=a.id and ((PERM_TYPE='staff' and PERM_ID=?) or (PERM_TYPE='post' and PERM_ID=?) or (PERM_TYPE='role' and PERM_ID in (" + roleIdsInStr + "))) ) then 1 else 0 end=1";

        List<Object> values = new ArrayList<>();
        values.add(dto.getStartDate());
        values.add(dto.getEndDate());
        values.add(Optional.ofNullable(dto.getPersonId()).orElse(""));
        values.add(Optional.ofNullable(dto.getPostId()).orElse(""));
        //values.add(Arrays.asList(Optional.ofNullable(dto.getRoleIds()).orElse("").split(",")));

        List<LinkedHashMap<String, Object>> result = entityService.query(sqlStr, values);
        if (ObjUtils.isEmpty(result)) {
            return list;
        }
        for (LinkedHashMap<String, Object> record : result) {
            BzjsConfirmStatusVo vo = new BzjsConfirmStatusVo();

            vo.setConfirmed(false);
            vo.setFormDataId((String) record.get("FORM_DATA_ID"));
            vo.setAccntObjIds((String) record.get("ACCNT_OBJ_IDS"));
            vo.setBcdm((String) record.get("BCDM"));

            Object xbsjObj = record.get("XBSJ");
            if (xbsjObj instanceof oracle.sql.TIMESTAMP) {
                String xbsjStr = xbsjObj.toString();
                try {
                    vo.setXbsj(xbsjStr.substring(0, 19));
                } catch (Exception e) {
                    log.error("班组记事获取确认状态信息接口，下班时间串截取失败（{}）", xbsjStr);
                }
            }

            Object inputTimeObj = record.get("INPUT_TIME");
            if (inputTimeObj instanceof oracle.sql.TIMESTAMP) {
                String inputTimeStr = inputTimeObj.toString();
                try {
                    vo.setInputDate(inputTimeStr.substring(0, 10));
                } catch (Exception e) {
                    log.error("班组记事获取确认状态信息接口，录入日期串截取失败（{}）", inputTimeStr);
                }
            }

            list.add(vo);
        }

        return list;
    }

    /**
     * 获取采集点数据
     * @param param
     * @param bzjsInputmxVo
     * @return
     */
    private List<BzjsInputCollectPointFlVo> getCollectPointData(BzjsInputDto param, BzjsInputmxVo bzjsInputmxVo) {
        List<BzjsInputCollectPointFlVo> collectPointDataFlList = new ArrayList<>();
        Map<String, List<BzjsInputCollectPoint>> flMap = new LinkedHashMap<>(); //采集点分类缓存Map
        Map<String, List<BzjsInputCollectPoint>> collectPointDataMap = new LinkedHashMap<>(); //提取实时数据的采集点缓存Map

        //公共参数
        String accntObjId = param.getAccntObjIds(); //核算对象ID（最早设计班组记事可能会有多个核算ID，但后续不用了，只使用一个）
        String dataType = bzjsInputmxVo.getDataType(); //数据类型
        String teamLogColumnId = bzjsInputmxVo.getTplDetailId(); //栏目ID

        String realTimeStr = "";
        if (dataType != null && BzjsConstant.DATA_TYPE_LAST_SHIFT.equals(dataType)) { //上班数据
            realTimeStr = param.getShiftBeginTime(); //上个班次的交班时间（本班的上班时间）
        } else { //本班数据
            realTimeStr = param.getShiftEndTime(); //本班交班时间
        }
        Date realTimeDate = DateTimeUtils.parseDateTime(realTimeStr);

        long startTime = System.currentTimeMillis();

        // 通过核算对象ID、班组记事栏目ID获取所有绑定的采集点分类及其子节点数据
        SqlRowSet dotInfoSet = iCostitemService.getDotInfoSet(accntObjId, teamLogColumnId);
        if (dotInfoSet == null) {
            log.warn("通过核算对象ID（{}）、班组记事栏目ID（{}）获取所有绑定的采集点分类及其子节点数据 - 无采集点数据", accntObjId, teamLogColumnId);
            return collectPointDataFlList;
        }
        while (dotInfoSet.next()) {
            String flid = dotInfoSet.getString("flid");
            String flmc = dotInfoSet.getString("flmc");
            //String teamLogColumnId = dotInfoSet.getString("team_log_column_id");
            int flsn = dotInfoSet.getInt("flsn");
            String pointid = dotInfoSet.getString("pointid");
            String pointname = dotInfoSet.getString("pointname");
            int pointsn = dotInfoSet.getInt("pointsn");
            String combinitkey = dotInfoSet.getString("combinitkey");
            String combinitval = dotInfoSet.getString("combinitval");
            String controltype = dotInfoSet.getString("controltype");
            String datasource = dotInfoSet.getString("datasource");
            int defaultval = dotInfoSet.getInt("defaultval");
            String devicedefaultval = dotInfoSet.getString("devicedefaultval");
            String sdunit = dotInfoSet.getString("sdunit");
            int pointcountledger = dotInfoSet.getInt("pointcountledger");

            List<BzjsInputCollectPoint> collectPointDataList = flMap.get(flid);
            if (collectPointDataList == null) {
                collectPointDataList = new ArrayList<>();
                flMap.put(flid, collectPointDataList);
                //组装采集点分类节点
                BzjsInputCollectPointFlVo collectPointDataFl = new BzjsInputCollectPointFlVo();
                collectPointDataFl.setCollectPointFlId(flid);
                collectPointDataFl.setCollectPointFlName(flmc);
                collectPointDataFl.setCollectPointFlSn(flsn);
                collectPointDataFl.setCollectPointList(collectPointDataList);
                collectPointDataFlList.add(collectPointDataFl);
            }

            //组装采集点节点
            BzjsInputCollectPoint bzjsInputCollectPoint = new BzjsInputCollectPoint();
            bzjsInputCollectPoint.setCollectPointFlId(flid);
            bzjsInputCollectPoint.setCollectPointFlName(flmc);
            bzjsInputCollectPoint.setCollectPointFlSn(flsn);
            bzjsInputCollectPoint.setCollectPointId(pointid);
            bzjsInputCollectPoint.setCollectPoint(pointname);
            bzjsInputCollectPoint.setSn(pointsn);
            bzjsInputCollectPoint.setInputCompType(controltype);
            bzjsInputCollectPoint.setDefaultVal(defaultval);
            bzjsInputCollectPoint.setDevicedefaultval(devicedefaultval);
            bzjsInputCollectPoint.setInputTime(realTimeDate);
            bzjsInputCollectPoint.setSdunit(sdunit);
            bzjsInputCollectPoint.setPointcountledger(pointcountledger);

            //实时位号
            bzjsInputCollectPoint.setDatasource(datasource);
            //对提取实时数据的仪表进行缓存
            if (StringUtils.isNotEmpty(datasource)) {
                //缓存实时位号 → 采集点列表（考虑到配置相同位号的多个采集点）
                List<BzjsInputCollectPoint> rtdbPointList = collectPointDataMap.get(datasource);
                if (rtdbPointList == null) {
                    rtdbPointList = new ArrayList<>();
                    collectPointDataMap.put(datasource, rtdbPointList);
                }
                rtdbPointList.add(bzjsInputCollectPoint);
            }

            // 下拉框
            JSONArray comboOption = getComboOptionStr(combinitkey, combinitval);
            String comboOptionStr = comboOption.toString();
            bzjsInputCollectPoint.setInputOptions(comboOptionStr);
            bzjsInputCollectPoint.setOptions(comboOption);

            //默认值
            if (defaultval == 1) { //当前登录人
                SysUser user = SysUserHolder.getCurrentUser();
                if ("8".equals(controltype) || "9".equals(controltype)) {
                    bzjsInputCollectPoint.setValue(user.getId());
                    bzjsInputCollectPoint.setValueText(user.getNickName());
                } else if ("0".equals(controltype)) {
                    bzjsInputCollectPoint.setValue(user.getNickName());
                }
            } else if (defaultval == 2) { //当前日期时间
                bzjsInputCollectPoint.setValue(DateTimeUtils.getNowDateTimeStr());
            } else if (defaultval == 3) { //自定义默认值（手动输入）
                bzjsInputCollectPoint.setValue(devicedefaultval);

                //使用key（隐藏值）查询value（名称）
                if ("4".equals(controltype)) { //下拉框
                    for (int i = 0; i < comboOption.size(); i++) {
                        JSONObject comboOptionObj = comboOption.getJSONObject(i);
                        if (comboOptionObj.getString("key").equals(devicedefaultval)) {
                            bzjsInputCollectPoint.setValueText(comboOptionObj.getString("value"));
                            break;
                        }
                    }
                }
            } else if (defaultval == 4) { //日期
                bzjsInputCollectPoint.setValue(DateTimeUtils .getNowDateStr());
            } else if (defaultval == 5) { //时间
                bzjsInputCollectPoint.setValue(DateTimeUtils.getNowDateTimeStr().substring(11));
            }

            collectPointDataList.add(bzjsInputCollectPoint);
        }

        //实时仪表位号的采集点，需要提取实时数据，当班的使用上班时间，交班的使用下班时间提取
        //查询rtdb数据 接口这个间隔timeInterval是秒
        if (collectPointDataMap.size() > 0) {
            List<String> tagcodes = new ArrayList<>();

            for (String tagNumber : collectPointDataMap.keySet()) {
                tagcodes.add(tagNumber);
            }

            String timeStr = "";
            if (BzjsConstant.DATA_TYPE_LAST_SHIFT.equals(dataType)) { //上班数据（使用上班时间获取实时数据）
                timeStr = param.getShiftBeginTime();
            } else { //本班数据（使用交班时间获取实时数据）
                timeStr = param.getShiftEndTime();
            }

            List<Tag> dataResult = null;
            try {
                //dataResult = rtdbSrv.queryRtdbTagData(tagcodes, timeStr, timeStr, 60 /*0*/);
                dataResult = rtdbSrv.queryLastTagDatas(tagcodes, timeStr);
            } catch (Exception e) {
                log.error("查询实时数据接口异常：{}", e);
            }

            if (ObjUtils.notEmpty(dataResult)) {
                for (Tag tag : dataResult) {
                    String tagCode = tag.getTagCode();
                    List<BzjsInputCollectPoint> bzjsInputCollectPoints = collectPointDataMap.get(tagCode);
                    List<TagData> datas = tag.getDatas();

                    if (ObjUtils.notEmpty(datas)) {
                        TagData data = datas.get(datas.size() - 1);
                        Object value = Optional.ofNullable(data.getValue()).orElse("");
                        for (BzjsInputCollectPoint bzjsInputCollectPoint : bzjsInputCollectPoints) {
                            //赋值
                            bzjsInputCollectPoint.setValue(value.toString());
                        }
                    }

                    //for (TagData data : tag.getDatas()) {
                    //    Object value = Optional.ofNullable(data.getValue()).orElse( "");
                    //    for (BzjsInputCollectPoint bzjsInputCollectPoint : bzjsInputCollectPoints) {
                    //        //赋值
                    //        bzjsInputCollectPoint.setValue(value.toString());
                    //    }
                    //
                    //    break; //只取这块仪表的第一条数据
                    //}
                }
            }
        }

        long endTime = System.currentTimeMillis();
        log.info("采集点数据接口执行完成（耗时：{} ms）", (endTime - startTime));

        return collectPointDataFlList;
    }

    /**
     * 组装下拉框备选数据
     * @param combinitkey
     * @param combinitval
     * @return
     */
    private JSONArray getComboOptionStr(String combinitkey, String combinitval) {
        JSONArray options = new JSONArray();

        if (StringUtils.isNotEmpty(combinitkey) && StringUtils.isNotEmpty(combinitval)) {
            String[] combinitkeyArray = combinitkey.split(",");
            String[] combinitvalArray = combinitval.split(",");
            if (combinitkeyArray.length != combinitvalArray.length) {
                log.error("获取采集点下拉框备选数据解析失败，键值个数不匹配（键：{}，值：{}）", combinitkey, combinitval);
                return options;
            }
            for (int i = 0; i < combinitkeyArray.length; i++) {
                JSONObject option = new JSONObject();
                //TODO
                option.put("key", combinitkeyArray[i]);
                option.put("value", combinitvalArray[i]);
                options.add(option);
            }
        }

        return options;
    }

    /**
     * 重新提取实时仪表数据
     * @param param
     */
    @Override
    public List<BzjsRtdbDetailVo> reExtractRtdbData(BzjsRtdbDto param) {
        List<BzjsRtdbDetailVo> rst = new ArrayList<>();

        if (ObjUtils.isEmpty(param)) {
            log.error("重新提取实时仪表数据失败，参数为空");
            return rst;
        }
        List<BzjsRtdbDetail> details = param.getDetails();
        if (ObjUtils.isEmpty(details)) {
            log.error("重新提取实时仪表数据失败，栏目数据为空");
            return rst;
        }

        for (BzjsRtdbDetail detail : details) {
            String time = detail.getTime(); //采集时间
            List<String> tagcodes = detail.getTagcodes(); //仪表列表
            //采集实时数据
            List<Tag> tags = null;
            try {
                //tags = rtdbSrv.queryRtdbTagData(tagcodes, time, time, 60 /*0*/);
                tags = rtdbSrv.queryLastTagDatas(tagcodes, time);
            } catch (Exception e) {
                log.error("查询实时数据接口异常：{}", e);
            }
            if (ObjUtils.isEmpty(tags)) {
                //log.error("重新提取实时仪表数据失败，查询实时数据失败。开始时间：{}，截止时间：{}，仪表：{}", time, time, tagcodes.toString());
                continue;
            }

            //返回结果，栏目
            BzjsRtdbDetailVo bzjsRtdbDetailVo = new BzjsRtdbDetailVo();
            bzjsRtdbDetailVo.setTags(new ArrayList<>());
            rst.add(bzjsRtdbDetailVo);

            //遍历仪表结果
            for (Tag tag : tags) {
                List<TagData> datas = tag.getDatas();
                if (ObjUtils.notEmpty(datas)) {
                    //取最后一条数据
                    TagData data = datas.get(datas.size() - 1);
                    Object value = Optional.ofNullable(data.getValue()).orElse("");

                    //返回结果，仪表值
                    BzjsRtdbValueVo bzjsRtdbValueVo = new BzjsRtdbValueVo();
                    bzjsRtdbValueVo.setTagcode(tag.getTagCode());
                    bzjsRtdbValueVo.setValue(value.toString());

                    bzjsRtdbDetailVo.getTags().add(bzjsRtdbValueVo);
                }

                //for (TagData data : tag.getDatas()) {
                //    //返回结果，仪表值
                //    BzjsRtdbValueVo bzjsRtdbValueVo = new BzjsRtdbValueVo();
                //    bzjsRtdbValueVo.setTagcode(tag.getTagCode());
                //    bzjsRtdbValueVo.setValue(data.getValue().toString());
                //
                //    bzjsRtdbDetailVo.getTags().add(bzjsRtdbValueVo);
                //
                //    break; //只取这块仪表的第一条数据
                //}
            }
        }

        return rst;
    }
}
