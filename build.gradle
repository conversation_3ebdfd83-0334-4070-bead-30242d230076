plugins {
    id 'org.springframework.boot' version '2.3.4.RELEASE'
    id 'io.spring.dependency-management' version '1.0.10.RELEASE'
    id 'java'
}
group = 'com.yunhesoft.tm4'
//第四版本号为测试用版本号，请忽略，正式发布时请使用3位版本号
version = '0.0.252-saas.421'

sourceCompatibility = '1.8'

ext {
	//set('springCloudVersion', "2020.0.0")
	set('springCloudVersion', "Hoxton.SR12")
}

dependencyManagement {
	imports {
		mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
	}
}

configurations {
	developmentOnly
	runtimeClasspath {
		extendsFrom developmentOnly
	}
	compileOnly {
		extendsFrom annotationProcessor
	}
}
tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}
//声明url地址
def baseUrl = "http://developer.yunhesoft.net:8081/repository/"
def nexusUsername = 'tm4dev'
def nexusPassword = 'YH2019@bzhs!*6'
repositories {
//	mavenLocal()
    maven{
		url baseUrl+"/tm4-group/"
		credentials {
			username nexusUsername
			password nexusPassword
		}
	}
    maven { url "https://maven.aliyun.com/repository/gradle-plugin"}
}

gradle.projectsEvaluated {
    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
    }
}
//全局排除spring boot 自带的日志
configurations {
	compile.exclude group:'org.springframework.boot',module:'spring-boot-starter-logging'
}

dependencies {

	//implementation 'org.springframework.cloud:spring-cloud-dependencies:Hoxton.SR12'
	//implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client:2.2.9.RELEASE'

	implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
	implementation 'org.springframework.cloud:spring-cloud-starter-openfeign:2.2.9.RELEASE'

	//倒班
	implementation 'com.yunhesoft.tm4:tm4-comm-shift:0.0.31'

	implementation 'com.yunhesoft.tm4:tm4-tools:0.0.20'
    //implementation 'com.yunhesoft.tm4:tm4-comm-datav:0.0.1.saas.06'
	implementation 'com.yunhesoft.tm4:tm3-proxy:0.0.2'

	//大屏功能
    //implementation 'com.yunhesoft.tm4:tm4-comm-datav:0.0.19'

	//验证码
	implementation 'com.github.penggle:kaptcha:2.3.2'

	implementation ('org.springframework.boot:spring-boot-starter-web:2.3.4.RELEASE'){
		exclude group: 'org.springframework.boot', module: 'spring-boot-starter-tomcat'
	}
	compile 'org.apache.tomcat.embed:tomcat-embed-core:9.0.102'
	compile 'org.apache.tomcat.embed:tomcat-embed-el:9.0.102'
	compile 'org.apache.tomcat.embed:tomcat-embed-websocket:9.0.102'
	implementation 'org.apache.tomcat:tomcat-annotations-api:9.0.102'

	implementation 'org.apache.httpcomponents:httpclient:4.5.13'

	implementation 'org.springframework.boot:spring-boot-starter-security:2.3.4.RELEASE'
	implementation 'org.springframework.security:spring-security-core:5.5.0'
	implementation 'org.apache.velocity:velocity:1.7'

	implementation 'org.springframework.boot:spring-boot-starter-cache:2.3.4.RELEASE'

	implementation 'org.springframework.boot:spring-boot-starter-log4j2:2.3.4.RELEASE'
	//服务注册与发现
        // implementation 'org.springframework.cloud:spring-cloud-dependencies:Hoxton.SR12'
//         implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client:2.2.9.RELEASE'
        //负载均衡
//         implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer:2.2.9.RELEASE'

//         implementation 'org.springframework.retry:spring-retry'
	implementation 'org.apache.logging.log4j:log4j-core:2.17.1'
	implementation 'org.apache.logging.log4j:log4j-api:2.17.1'

	//日志存储数据库
	//implementation 'org.apache.logging.log4j:log4j-core:2.9.1'
	//implementation 'org.apache.logging.log4j:log4j-api:2.9.1'
	//implementation 'org.apache.logging.log4j:log4j-slf4j-impl:2.9.1'
	//implementation 'org.mongodb:mongo-java-driver:3.2.2'
	//implementation 'org.apache.logging.log4j:log4j-nosql:2.9.0'
	//implementation 'com.lmax:disruptor:3.4.2

	//token认证
	implementation 'io.jsonwebtoken:jjwt:0.9.1'

	implementation 'org.springframework.boot:spring-boot-starter-aop:2.3.4.RELEASE'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa:2.3.4.RELEASE'

	//工具
	implementation 'org.apache.commons:commons-lang3:3.12.0'
	implementation 'com.belerweb:pinyin4j:2.5.1'

	implementation 'commons-io:commons-io:2.5'
//	implementation 'com.alibaba:fastjson:2.0.49'
	implementation 'com.alibaba:fastjson:2.0.53'

	implementation 'org.springframework.boot:spring-boot-starter-validation:2.3.4.RELEASE'
	//阿里云套件
	implementation 'com.quhaodian:plug_alidayu:1.2.0'
	implementation 'com.aliyun:aliyun-java-sdk-core:4.1.0'
	implementation 'com.aliyun:aliyun-java-sdk-dysmsapi:1.1.0'
	//easyPoi
	implementation 'cn.afterturn:easypoi:4.3.0'
	implementation 'cn.afterturn:easypoi-base:4.3.0'
	implementation 'cn.afterturn:easypoi-annotation:4.3.0'
	implementation 'cn.afterturn:easypoi-web:4.3.0'

	//poi word操作类
	implementation 'com.deepoove:poi-tl:1.10.4'
	//excel
	implementation 'org.apache.poi:poi:4.1.2'
	implementation 'org.apache.poi:poi-ooxml:4.1.2'
	implementation 'org.apache.poi:poi-ooxml-schemas:4.1.2'
	implementation 'org.apache.poi:poi-scratchpad:4.1.2'
	// https://mvnrepository.com/artifact/org.apache.poi/ooxml-schemas
	implementation 'org.apache.poi:ooxml-schemas:1.4'

	//swagger
    implementation 'com.github.xiaoymin:knife4j-spring-boot-starter:2.0.9'

	//数据库驱动
	implementation 'com.alibaba:druid-spring-boot-starter:1.1.23'
	implementation 'com.github.jsqlparser:jsqlparser:3.1'
	implementation 'com.microsoft.sqlserver:mssql-jdbc:6.1.0.jre8'
	implementation 'mysql:mysql-connector-java:8.0.16'
	//oracle
	implementation 'com.oracle:ojdbc7:12.1.0.1'
	//implementation 'com.oracle:ojdbc8:19.3.0.0'
	//implementation 'cn.easyproject:orai18n:12.1.0.2.0'

	//dm
	implementation 'dm:DmJdbcDriver:18'
	implementation 'dm:DmHibernateSpatial:1.1'
	implementation 'dm:DmDictionary:1.0'
	implementation 'dm:DmDialect-for-hibernate:5.3'

	//人大金仓
	implementation 'cn.com.kingbase:kingbase8:8.6.0'
	implementation 'cn.com.kingbase:kingbase-hibernate-dialect:4'

	//redis
	implementation 'org.apache.commons:commons-pool2:2.6.2'
	implementation 'org.springframework.boot:spring-boot-starter-data-redis:2.3.3.RELEASE'
   // implementation 'org.springframework.session:spring-session-data-redis:2.3.3.RELEASE'

    implementation 'redis.clients:jedis'

	implementation 'net.sf.ehcache:ehcache:2.10.9.2'

	implementation 'javax.interceptor:javax.interceptor-api:1.2'
	//google 解析器
	implementation 'com.googlecode.aviator:aviator:5.3.1'

	//Spring Boot项目application.properties文件数据库配置密码加密
	implementation 'com.github.ulisesbocchio:jasypt-spring-boot-starter:3.0.4'

	//xxl-job 调度执行器
	implementation 'com.xuxueli:xxl-job-core:2.3.0'

	//mongodb
	implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'

	//在线文档处理
    implementation 'fr.opensagres.xdocreport:fr.opensagres.poi.xwpf.converter.xhtml:2.0.2'
    implementation "org.apache.xmlgraphics:fop:2.6"
    implementation 'org.apache.xmlgraphics:batik-transcoder:1.14'
    implementation 'org.apache.xmlgraphics:batik-codec:1.7'
    implementation 'com.itextpdf:html2pdf:4.0.5'
    implementation 'com.itextpdf:font-asian:8.0.0'
    implementation 'io.github.java-diff-utils:java-diff-utils:4.11'

    //nacos
	//implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery:2.2.3.RELEASE'
	//implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config:2.2.3.RELEASE'

    compileOnly 'org.projectlombok:lombok:1.18.20'
    annotationProcessor 'org.projectlombok:lombok:1.18.20'

    testImplementation('org.springframework.boot:spring-boot-starter-test:2.3.4.RELEASE') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }


	implementation 'commons-fileupload:commons-fileupload:1.5'

	//minio文件服务 minio中的okhttp有冲突，需单独引用
	implementation 'io.minio:minio:8.2.2'
//	implementation("io.minio:minio:8.5.2") {
//		exclude group: 'com.squareup.okhttp3', module: 'okhttp'
//	}
//	implementation("com.squareup.okhttp3:okhttp:4.10.0")

	//PostgreSQL  数据库驱动
	//implementation 'org.postgresql:postgresql:42.2.4'

	//rtdb实时数据客户端
	implementation 'com.yunhesoft.rtdb:tm4-rtdb-client-java:0.0.13'

	//compile fileTree(dir: 'lib', includes: ['*.jar'])

	//RESTTEMPLATE HTTP通信负载均衡
	implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer:2.2.9.RELEASE'

}

//打包并发布到nexus3服务器
apply plugin:'maven-publish'

//读取git版本号
def getGitCommit = { ->
    def stdout = new ByteArrayOutputStream()
    exec {
        commandLine 'git', 'rev-parse', 'HEAD'
        standardOutput = stdout
    }
    return stdout.toString().trim()
}
//在可执行jar包中的mainifest.mf中增加git版本号
bootJar {
	manifest {
        attributes 	'Manifest-Version': project.version,"Git-Commit" : getGitCommit()
    }
    baseName = project.name + "-app"
}
//声明非启动模式的jar包
task clientJar(type:Jar) {
	//在jar包的mainifest.mf中增加git版本号
	manifest {
        attributes 	'Manifest-Version': project.version,"Git-Commit" : getGitCommit()
    }
	//打包成jar时，必须设置为true
	enabled = true
	//基础名称为项目的名称
	baseName = project.name
    //打包的class文件位置
    from('build/classes/java/main/').include('**/*.class')
    //需要加入的资源那文件
    from('src/main/resources/').include('documents/**/*.*')
	//打包时不包含的class文件
	exclude('com/yunhesoft/system/Tm4SystemApplication*.class')
	//打包class文件放到jar包中的哪个目录下
	into('')
}
task clientSourceJar(type: Jar) {
    from sourceSets.main.allJava
    classifier = 'sources'
}
publishing {
    publications {
    	//发布一个jar包
        //mavenBootJar(MavenPublication) {
        //    //指定group/artifact/version信息，可以不填。默认使用项目group/name/version作为groupId/artifactId/version
        //    groupId project.group
        //    artifactId project.name
        //    version project.version
        //    //artifact标识，bootJar是一个生成jar的任务
        //    artifact bootJar
       	//}
        mavenClientJar(MavenPublication) {
            //指定group/artifact/version信息，可以不填。默认使用项目group/name/version作为groupId/artifactId/version
            groupId project.group
            //artifactId project.name+'-client'
            //tm4-core是一个纯的供其它项目使用的依赖包，因此，需要打成不包含其它内容的包
            artifactId project.name
            version project.version
            artifact clientJar
            //包含源文件
            artifact clientSourceJar
            pom.withXml {
             	//查找正则:(')([\w\d\.\-]+)(:)([\w\d\.\-]+)(:)([\w\d\.\-]+)(')
				//替换正则:def dependencyNode1 = dependenciesNode.appendNode\('dependency'\)\r\ndependencyNode1.appendNode\('groupId', '\2'\)\r\ndependencyNode1.appendNode\('artifactId', '\4'\)\r\ndependencyNode1.appendNode\('version', '\6'\)

		    	def dependenciesNode = asNode().appendNode('dependencies')

            	//验证码
				def dependencyNode1 = dependenciesNode.appendNode('dependency')
				dependencyNode1.appendNode('groupId', 'com.github.penggle')
				dependencyNode1.appendNode('artifactId', 'kaptcha')
				dependencyNode1.appendNode('version', '2.3.2')

				def dependencyNode2 = dependenciesNode.appendNode('dependency')
				dependencyNode2.appendNode('groupId', 'org.springframework.boot')
				dependencyNode2.appendNode('artifactId', 'spring-boot-starter-web')
				dependencyNode2.appendNode('version', '2.3.4.RELEASE')

				def dependencyNode3 = dependenciesNode.appendNode('dependency')
				dependencyNode3.appendNode('groupId', 'org.apache.httpcomponents')
				dependencyNode3.appendNode('artifactId', 'httpclient')
				dependencyNode3.appendNode('version', '4.5.13')

				def dependencyNode4 = dependenciesNode.appendNode('dependency')
				dependencyNode4.appendNode('groupId', 'org.springframework.boot')
				dependencyNode4.appendNode('artifactId', 'spring-boot-starter-security')
				dependencyNode4.appendNode('version', '2.3.4.RELEASE')

				def dependencyNode5 = dependenciesNode.appendNode('dependency')
				dependencyNode5.appendNode('groupId', 'org.springframework.security')
				dependencyNode5.appendNode('artifactId', 'spring-security-core')
				dependencyNode5.appendNode('version', '5.5.0')

				def dependencyNode6 = dependenciesNode.appendNode('dependency')
				dependencyNode6.appendNode('groupId', 'org.apache.velocity')
				dependencyNode6.appendNode('artifactId', 'velocity')
				dependencyNode6.appendNode('version', '1.7')

				def dependencyNode7 = dependenciesNode.appendNode('dependency')
				dependencyNode7.appendNode('groupId', 'org.springframework.boot')
				dependencyNode7.appendNode('artifactId', 'spring-boot-starter-cache')
				dependencyNode7.appendNode('version', '2.3.4.RELEASE')

				def dependencyNode8 = dependenciesNode.appendNode('dependency')
				dependencyNode8.appendNode('groupId', 'org.apache.logging.log4j')
				dependencyNode8.appendNode('artifactId', 'log4j-core')
				dependencyNode8.appendNode('version', '2.17.1')

				def dependencyNode9 = dependenciesNode.appendNode('dependency')
				dependencyNode9.appendNode('groupId', 'org.apache.logging.log4j')
				dependencyNode9.appendNode('artifactId', 'log4j-api')
				dependencyNode9.appendNode('version', '2.17.1')

				//token认证
				def dependencyNode10 = dependenciesNode.appendNode('dependency')
				dependencyNode10.appendNode('groupId', 'io.jsonwebtoken')
				dependencyNode10.appendNode('artifactId', 'jjwt')
				dependencyNode10.appendNode('version', '0.9.1')

				def dependencyNode11 = dependenciesNode.appendNode('dependency')
				dependencyNode11.appendNode('groupId', 'org.springframework.boot')
				dependencyNode11.appendNode('artifactId', 'spring-boot-starter-security')
				dependencyNode11.appendNode('version', '2.3.4.RELEASE')

				def dependencyNode12 = dependenciesNode.appendNode('dependency')
				dependencyNode12.appendNode('groupId', 'org.springframework.boot')
				dependencyNode12.appendNode('artifactId', 'spring-boot-starter-aop')
				dependencyNode12.appendNode('version', '2.3.4.RELEASE')

				def dependencyNode13 = dependenciesNode.appendNode('dependency')
				dependencyNode13.appendNode('groupId', 'org.springframework.boot')
				dependencyNode13.appendNode('artifactId', 'spring-boot-starter-data-jpa')
				dependencyNode13.appendNode('version', '2.3.4.RELEASE')

				def dependencyNode14 = dependenciesNode.appendNode('dependency')
				dependencyNode14.appendNode('groupId', 'org.springframework.boot')
				dependencyNode14.appendNode('artifactId', 'spring-boot-starter-log4j2')
				dependencyNode14.appendNode('version', '2.3.4.RELEASE')

				//工具
				def dependencyNode15 = dependenciesNode.appendNode('dependency')
				dependencyNode15.appendNode('groupId', 'org.apache.commons')
				dependencyNode15.appendNode('artifactId', 'commons-lang3')
				dependencyNode15.appendNode('version', '3.12.0')

				def dependencyNode16 = dependenciesNode.appendNode('dependency')
				dependencyNode16.appendNode('groupId', 'com.belerweb')
				dependencyNode16.appendNode('artifactId', 'pinyin4j')
				dependencyNode16.appendNode('version', '2.5.1')

				def dependencyNode17 = dependenciesNode.appendNode('dependency')
				dependencyNode17.appendNode('groupId', 'commons-io')
				dependencyNode17.appendNode('artifactId', 'commons-io')
				dependencyNode17.appendNode('version', '2.5')

				def dependencyNode18 = dependenciesNode.appendNode('dependency')
				dependencyNode18.appendNode('groupId', 'com.alibaba')
				dependencyNode18.appendNode('artifactId', 'fastjson')
//				dependencyNode18.appendNode('version', '2.0.49')
				dependencyNode18.appendNode('version', '2.0.53')

				def dependencyNode19 = dependenciesNode.appendNode('dependency')
				dependencyNode19.appendNode('groupId', 'org.springframework.boot')
				dependencyNode19.appendNode('artifactId', 'spring-boot-starter-validation')
				dependencyNode19.appendNode('version', '2.3.4.RELEASE')

				//阿里云套件
				def dependencyNode20 = dependenciesNode.appendNode('dependency')
				dependencyNode20.appendNode('groupId', 'com.quhaodian')
				dependencyNode20.appendNode('artifactId', 'plug_alidayu')
				dependencyNode20.appendNode('version', '1.2.0')

				def dependencyNode21 = dependenciesNode.appendNode('dependency')
				dependencyNode21.appendNode('groupId', 'com.aliyun')
				dependencyNode21.appendNode('artifactId', 'aliyun-java-sdk-core')
				dependencyNode21.appendNode('version', '4.1.0')

				def dependencyNode22 = dependenciesNode.appendNode('dependency')
				dependencyNode22.appendNode('groupId', 'com.aliyun')
				dependencyNode22.appendNode('artifactId', 'aliyun-java-sdk-dysmsapi')
				dependencyNode22.appendNode('version', '1.1.0')

				//easyPoi
				def dependencyNode23 = dependenciesNode.appendNode('dependency')
				dependencyNode23.appendNode('groupId', 'cn.afterturn')
				dependencyNode23.appendNode('artifactId', 'easypoi')
				dependencyNode23.appendNode('version', '4.3.0')

				def dependencyNode24 = dependenciesNode.appendNode('dependency')
				dependencyNode24.appendNode('groupId', 'cn.afterturn')
				dependencyNode24.appendNode('artifactId', 'easypoi-base')
				dependencyNode24.appendNode('version', '4.3.0')

				def dependencyNode25 = dependenciesNode.appendNode('dependency')
				dependencyNode25.appendNode('groupId', 'cn.afterturn')
				dependencyNode25.appendNode('artifactId', 'easypoi-annotation')
				dependencyNode25.appendNode('version', '4.3.0')

				def dependencyNode26 = dependenciesNode.appendNode('dependency')
				dependencyNode26.appendNode('groupId', 'cn.afterturn')
				dependencyNode26.appendNode('artifactId', 'easypoi-web')
				dependencyNode26.appendNode('version', '4.3.0')

				//poi word操作类
				def dependencyNode27 = dependenciesNode.appendNode('dependency')
				dependencyNode27.appendNode('groupId', 'com.deepoove')
				dependencyNode27.appendNode('artifactId', 'poi-tl')
				dependencyNode27.appendNode('version', '1.10.4')

				//excel
				def dependencyNode28 = dependenciesNode.appendNode('dependency')
				dependencyNode28.appendNode('groupId', 'org.apache.poi')
				dependencyNode28.appendNode('artifactId', 'poi')
				dependencyNode28.appendNode('version', '4.1.2')

				def dependencyNode29 = dependenciesNode.appendNode('dependency')
				dependencyNode29.appendNode('groupId', 'org.apache.poi')
				dependencyNode29.appendNode('artifactId', 'poi-ooxml')
				dependencyNode29.appendNode('version', '4.1.2')

				def dependencyNode30 = dependenciesNode.appendNode('dependency')
				dependencyNode30.appendNode('groupId', 'org.apache.poi')
				dependencyNode30.appendNode('artifactId', 'poi-ooxml-schemas')
				dependencyNode30.appendNode('version', '4.1.2')

				def dependencyNode31 = dependenciesNode.appendNode('dependency')
				dependencyNode31.appendNode('groupId', 'org.apache.poi')
				dependencyNode31.appendNode('artifactId', 'poi-scratchpad')
				dependencyNode31.appendNode('version', '4.1.2')

				//swagger
				def dependencyNode32 = dependenciesNode.appendNode('dependency')
				dependencyNode32.appendNode('groupId', 'com.github.xiaoymin')
				dependencyNode32.appendNode('artifactId', 'knife4j-spring-boot-starter')
				dependencyNode32.appendNode('version', '2.0.9')

				//数据库连接池
				def dependencyNode33 = dependenciesNode.appendNode('dependency')
				dependencyNode33.appendNode('groupId', 'com.alibaba')
				dependencyNode33.appendNode('artifactId', 'druid-spring-boot-starter')
				dependencyNode33.appendNode('version', '1.1.23')
				//空34


				def dependencyNode35 = dependenciesNode.appendNode('dependency')
				dependencyNode35.appendNode('groupId', 'com.github.jsqlparser')
				dependencyNode35.appendNode('artifactId', 'jsqlparser')
				dependencyNode35.appendNode('version', '3.1')

				//redis
				def dependencyNode36 = dependenciesNode.appendNode('dependency')
				dependencyNode36.appendNode('groupId', 'org.apache.commons')
				dependencyNode36.appendNode('artifactId', 'commons-pool2')
				dependencyNode36.appendNode('version', '2.6.2')

				def dependencyNode37 = dependenciesNode.appendNode('dependency')
				dependencyNode37.appendNode('groupId', 'org.springframework.boot')
				dependencyNode37.appendNode('artifactId', 'spring-boot-starter-data-redis')
				dependencyNode37.appendNode('version', '2.3.3.RELEASE')

				//def dependencyNode38 = dependenciesNode.appendNode('dependency')
				//dependencyNode38.appendNode('groupId', 'org.springframework.session')
				//dependencyNode38.appendNode('artifactId', 'spring-session-data-redis')
				//dependencyNode38.appendNode('version', '2.3.3.RELEASE')

				def dependencyNode39 = dependenciesNode.appendNode('dependency')
				dependencyNode39.appendNode('groupId', 'net.sf.ehcache')
				dependencyNode39.appendNode('artifactId', 'ehcache')
				dependencyNode39.appendNode('version', '2.10.9.2')

				def dependencyNode40 = dependenciesNode.appendNode('dependency')
				dependencyNode40.appendNode('groupId', 'javax.interceptor')
				dependencyNode40.appendNode('artifactId', 'javax.interceptor-api')
				dependencyNode40.appendNode('version', '1.2')

				//Spring Boot项目application.properties文件数据库配置密码加密
				def dependencyNode41 = dependenciesNode.appendNode('dependency')
				dependencyNode41.appendNode('groupId', 'com.github.ulisesbocchio')
				dependencyNode41.appendNode('artifactId', 'jasypt-spring-boot-starter')
				dependencyNode41.appendNode('version', '3.0.4')

				//空42

				//数据库驱动
				def dependencyNode43 = dependenciesNode.appendNode('dependency')
				dependencyNode43.appendNode('groupId', 'com.microsoft.sqlserver')
				dependencyNode43.appendNode('artifactId', 'mssql-jdbc')
				dependencyNode43.appendNode('version', '6.1.0.jre8')

				def dependencyNode44 = dependenciesNode.appendNode('dependency')
				dependencyNode44.appendNode('groupId', 'mysql')
				dependencyNode44.appendNode('artifactId', 'mysql-connector-java')
				dependencyNode44.appendNode('version', '8.0.16')

				//dm
				def dependencyNode45 = dependenciesNode.appendNode('dependency')
				dependencyNode45.appendNode('groupId', 'dm')
				dependencyNode45.appendNode('artifactId', 'DmJdbcDriver')
				dependencyNode45.appendNode('version', '18')

				def dependencyNode46 = dependenciesNode.appendNode('dependency')
				dependencyNode46.appendNode('groupId', 'dm')
				dependencyNode46.appendNode('artifactId', 'DmHibernateSpatial')
				dependencyNode46.appendNode('version', '1.1')

				def dependencyNode47 = dependenciesNode.appendNode('dependency')
				dependencyNode47.appendNode('groupId', 'dm')
				dependencyNode47.appendNode('artifactId', 'DmDictionary')
				dependencyNode47.appendNode('version', '1.0')

				def dependencyNode48 = dependenciesNode.appendNode('dependency')
				dependencyNode48.appendNode('groupId', 'dm')
				dependencyNode48.appendNode('artifactId', 'DmDialect-for-hibernate')
				dependencyNode48.appendNode('version', '5.3')

				//tm4-comm-flow使用
				def dependencyNode49 = dependenciesNode.appendNode('dependency')
				dependencyNode49.appendNode('groupId', 'org.springframework.boot')
				dependencyNode49.appendNode('artifactId', 'spring-boot-starter-security')
				dependencyNode49.appendNode('version', '2.3.4.RELEASE')

				def dependencyNode50 = dependenciesNode.appendNode('dependency')
				dependencyNode50.appendNode('groupId', 'org.springframework.security')
				dependencyNode50.appendNode('artifactId', 'spring-security-core')
				dependencyNode50.appendNode('version', '5.5.0')

				def dependencyNode51 = dependenciesNode.appendNode('dependency')
				dependencyNode51.appendNode('groupId', 'com.xuxueli')
				dependencyNode51.appendNode('artifactId', 'xxl-job-core')
				dependencyNode51.appendNode('version', '2.3.0')

				//谷歌脚本解析器implementation 'com.googlecode.aviator:aviator:5.3.1'
				def dependencyNode52 = dependenciesNode.appendNode('dependency')
				dependencyNode52.appendNode('groupId', 'com.googlecode.aviator')
				dependencyNode52.appendNode('artifactId', 'aviator')
				dependencyNode52.appendNode('version', '5.3.1')
				//mongodb
				def dependencyNode53 = dependenciesNode.appendNode('dependency')
				dependencyNode53.appendNode('groupId', 'org.springframework.boot')
				dependencyNode53.appendNode('artifactId', 'spring-boot-starter-data-mongodb')
				//dependencyNode53.appendNode('version', '3.0.4.RELEASE')


				//minio文件服务 本minio中的okhttp有冲突，需单独引用
				def dependencyNode54 = dependenciesNode.appendNode('dependency')
				dependencyNode54.appendNode('groupId', 'io.minio')
				dependencyNode54.appendNode('artifactId', 'minio')
				dependencyNode54.appendNode('version', '8.2.2')

				def dependencyNode55 = dependenciesNode.appendNode('dependency')
				dependencyNode55.appendNode('groupId', 'commons-fileupload')
				dependencyNode55.appendNode('artifactId', 'commons-fileupload')
				dependencyNode55.appendNode('version', '1.5')

				//在线文档处理
				def dependencyNode56 = dependenciesNode.appendNode('dependency')
				dependencyNode56.appendNode('groupId', 'fr.opensagres.xdocreport')
				dependencyNode56.appendNode('artifactId', 'fr.opensagres.poi.xwpf.converter.xhtml')
				dependencyNode56.appendNode('version', '2.0.2')

				def dependencyNode57 = dependenciesNode.appendNode('dependency')
				dependencyNode57.appendNode('groupId', 'org.apache.xmlgraphics')
				dependencyNode57.appendNode('artifactId', 'fop')
				dependencyNode57.appendNode('version', '2.6')

				def dependencyNode58 = dependenciesNode.appendNode('dependency')
				dependencyNode58.appendNode('groupId', 'org.apache.xmlgraphics')
				dependencyNode58.appendNode('artifactId', 'batik-transcoder')
				dependencyNode58.appendNode('version', '1.14')

				def dependencyNode59 = dependenciesNode.appendNode('dependency')
				dependencyNode59.appendNode('groupId', 'org.apache.xmlgraphics')
				dependencyNode59.appendNode('artifactId', 'batik-codec')
				dependencyNode59.appendNode('version', '1.7')

				def dependencyNode60 = dependenciesNode.appendNode('dependency')
				dependencyNode60.appendNode('groupId', 'com.itextpdf')
				dependencyNode60.appendNode('artifactId', 'html2pdf')
				dependencyNode60.appendNode('version', '4.0.5')

				def dependencyNode61 = dependenciesNode.appendNode('dependency')
				dependencyNode61.appendNode('groupId', 'com.itextpdf')
				dependencyNode61.appendNode('artifactId', 'font-asian')
				dependencyNode61.appendNode('version', '8.0.0')

				def dependencyNode62 = dependenciesNode.appendNode('dependency')
				dependencyNode62.appendNode('groupId', 'io.github.java-diff-utils')
				dependencyNode62.appendNode('artifactId', 'java-diff-utils')
				dependencyNode62.appendNode('version', '4.11')

				//oracle
				def dependencyNode63 = dependenciesNode.appendNode('dependency')
				dependencyNode63.appendNode('groupId', 'com.oracle')
				dependencyNode63.appendNode('artifactId', 'ojdbc7')
				dependencyNode63.appendNode('version', '12.1.0.1')

				//redis 客户端
				def dependencyNode64 = dependenciesNode.appendNode('dependency')
				dependencyNode64.appendNode('groupId', 'redis.clients')
				dependencyNode64.appendNode('artifactId', 'jedis')

				//运和工具包
				def dependencyNode65 = dependenciesNode.appendNode('dependency')
				dependencyNode65.appendNode('groupId', 'com.yunhesoft.tm4')
				dependencyNode65.appendNode('artifactId', 'tm4-tools')
				dependencyNode65.appendNode('version', '0.0.20')

				//rtdb实时数据客户端
				def dependencyNode66 = dependenciesNode.appendNode('dependency')
				dependencyNode66.appendNode('groupId', 'com.yunhesoft.rtdb')
				dependencyNode66.appendNode('artifactId', 'tm4-rtdb-client-java')
				dependencyNode66.appendNode('version', '0.0.13')

				//人大金昌数据库
				def dependencyNode67 = dependenciesNode.appendNode('dependency')
				dependencyNode67.appendNode('groupId', 'cn.com.kingbase')
				dependencyNode67.appendNode('artifactId', 'kingbase8')
				dependencyNode67.appendNode('version', '8.6.0')

				def dependencyNode68 = dependenciesNode.appendNode('dependency')
				dependencyNode68.appendNode('groupId', 'cn.com.kingbase')
				dependencyNode68.appendNode('artifactId', 'kingbase-hibernate-dialect')
				dependencyNode68.appendNode('version', '4')

				def dependencyNode69 = dependenciesNode.appendNode('dependency')
				dependencyNode69.appendNode('groupId', 'org.springframework.cloud')
				dependencyNode69.appendNode('artifactId', 'spring-cloud-starter-loadbalancer')
				dependencyNode69.appendNode('version', '2.2.9.RELEASE')

            }
		}
    }
    repositories {
        maven {
            //指定要上传的maven私服仓库
            url = baseUrl+"/tm4-public/"
            //认证用户和密码
            credentials {
				username nexusUsername
				password nexusPassword
            }
        }
    }
}
