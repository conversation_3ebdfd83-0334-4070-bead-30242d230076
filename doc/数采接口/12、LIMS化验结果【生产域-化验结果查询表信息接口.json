{"columnCfg": [{"insideColumn": "prodType", "outsideColumn": "prod_type", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "sampleType", "outsideColumn": "sample_type", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "<PERSON>on", "outsideColumn": "released_on", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "processunit", "outsideColumn": "process_unit", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "productName", "outsideColumn": "product", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "samplingPoint", "outsideColumn": "sampling_point", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "analysisName", "outsideColumn": "analysis", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "itemName", "outsideColumn": "name", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "sampledDate", "outsideColumn": "sampled_date", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "drawtime", "outsideColumn": "drawtime", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "changedOn", "outsideColumn": "changed_on", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "sampleInSpec", "outsideColumn": "sample_in_spec", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "resultInSpec", "outsideColumn": "result_in_spec", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "formattedEntry", "outsideColumn": "formatted_entry", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "unitsDisplay", "outsideColumn": "units_display", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "<PERSON><PERSON>", "outsideColumn": "c_brand", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "analysisStandard", "outsideColumn": "analysis_standard", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "prod<PERSON><PERSON><PERSON><PERSON>", "outsideColumn": "prod_standard", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "testType", "outsideColumn": "test_type", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "productCode", "outsideColumn": "product_name", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "workshopSection", "outsideColumn": "workshop_section", "synDataType": "", "synDesc": "", "synParam": ""}, {"insideColumn": "tmused", "outsideColumn": "remove", "synDataType": "", "synDesc": "", "synParam": ""}], "pullTemplate": "", "synCode": "com.yunhesoft.leanCosting.synLeanCost.SynLimsResultData", "synDataJson": "{{rawData}}", "synDataPath": "/rows", "synDesc": "", "synParam": "{\"synDelFlagValue\":\"T\",\"noSynDeviceType\":\"ZZDY,AHXNZZ,XNZZ\"}", "synVersionPath": "/released_on", "tableName": "", "whereSql": ""}