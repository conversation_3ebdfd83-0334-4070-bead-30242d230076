# 台账删除权限功能优化总结

## 优化概述

本次优化主要针对 `AccountConfigServiceImpl.checkDeletePermission` 方法，使其能够同时适配PC端和APP端台账删除按钮权限验证。

## 主要改进

### 1. 功能扩展
- **PC端支持**：保持原有逻辑，从DTO直接获取权限配置
- **APP端支持**：新增从台账模型表(`DigitalLedger`)获取权限配置的逻辑

### 2. 权限配置处理
- **空权限处理**：当台账模型的`permission`字段为空时，返回`true`（无权限限制）
- **JSON解析**：添加`parsePermissionFromJson`方法，将数据库中的JSON字符串解析为权限对象
- **异常处理**：JSON解析失败时记录日志并返回null

### 3. 代码重构
- **权限检查统一**：提取`checkUserPermission`方法，统一处理角色、机构、岗位、人员权限验证
- **逻辑清晰**：分离APP端和PC端的权限获取逻辑，提高代码可读性

## 核心逻辑流程

### APP端权限验证流程
1. 根据`formId`和`ledgerModuleId`查询台账模型记录
2. 遍历台账记录，检查`permission`字段：
   - 如果为空：直接返回`true`（无权限限制）
   - 如果不为空：解析JSON并进行权限验证
3. 如果所有记录都没有有效权限配置：返回`true`
4. 如果没有找到台账记录：返回`false`

### PC端权限验证流程
1. 直接从DTO获取权限配置
2. 如果权限配置为空：返回`false`
3. 执行权限验证

### 权限验证逻辑
权限验证按以下顺序进行，任一通过即返回`true`：
1. **角色权限**：检查用户角色是否在权限配置的角色列表中
2. **机构权限**：检查用户机构是否在权限配置的机构列表中
3. **岗位权限**：检查用户岗位是否在权限配置的岗位列表中
4. **人员权限**：检查用户ID是否在权限配置的人员列表中

## 修改的文件

### AccountConfigServiceImpl.java
- **新增导入**：`com.alibaba.fastjson.JSONObject`
- **优化方法**：`checkDeletePermission` - 完善APP端权限获取逻辑
- **新增方法**：
  - `parsePermissionFromJson` - JSON权限配置解析
  - `checkUserPermission` - 统一权限验证逻辑

## 技术要点

### JSON解析
使用FastJSON库将数据库中的权限配置字符串解析为Java对象：
```java
JSONObject jsonObject = JSONObject.parseObject(permissionStr);
return jsonObject.toJavaObject(CheckDeletePermissionDto.Permission.class);
```

### 权限配置结构
权限配置支持四种类型的权限验证：
- `role`：角色权限
- `org`：机构权限  
- `post`：岗位权限
- `staff`：人员权限

### 异常处理
- JSON解析异常时记录详细日志
- 数据库查询异常由框架处理
- 权限验证过程中的空值安全处理

## 使用说明

### APP端调用
```java
CheckDeletePermissionDto dto = new CheckDeletePermissionDto();
dto.setType("app");
dto.setFormId("表单ID");
dto.setLedgerModuleId("台账模块ID");
boolean hasPermission = accountConfigService.checkDeletePermission(dto);
```

### PC端调用
```java
CheckDeletePermissionDto dto = new CheckDeletePermissionDto();
dto.setType("pc");
dto.setPermission(permissionObject); // 直接设置权限配置
boolean hasPermission = accountConfigService.checkDeletePermission(dto);
```

## 注意事项

1. **数据库权限配置**：确保台账模型表中的`permission`字段存储的是有效的JSON格式
2. **性能考虑**：APP端会查询数据库获取权限配置，建议在高频调用场景下考虑缓存
3. **权限配置为空**：APP端权限配置为空时默认允许删除，PC端权限配置为空时默认禁止删除
4. **向后兼容**：保持了原有PC端的调用方式和逻辑不变

## 测试建议

1. **APP端测试**：
   - 权限配置为空的情况
   - 权限配置不为空且用户有权限的情况
   - 权限配置不为空且用户无权限的情况
   - 无台账记录的情况

2. **PC端测试**：
   - 验证原有功能不受影响
   - 各种权限类型的验证

3. **异常测试**：
   - JSON格式错误的权限配置
   - 数据库连接异常情况
